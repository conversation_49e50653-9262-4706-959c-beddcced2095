local GameModel = appdf.req(appdf.CLIENT_SRC.."system.GameModel")

local GameLayer = class("GameLayer", GameModel)

local cmd = appdf.req(appdf.GAME_SRC.."fourcards.src.room.CMD_Game")
local GameLogic = appdf.req(appdf.GAME_SRC.."fourcards.src.room.GameLogic")
local GameViewLayer = appdf.req(appdf.GAME_SRC.."fourcards.src.room.GameViewLayer")
local ExternalFun =  cs.app.client('external.ExternalFun')

function GameLayer:ctor(frameEngine, scene)
    GameLayer.super.ctor(self, frameEngine, scene)

    cs.game.IS_RECIVE_GAME_RESULT = false
    self.is_zhengdian = false
    self.is_zhuaneight = false
end

function GameLayer:CreateView()
    return GameViewLayer:create(self):addTo(self)
end

function GameLayer:OnInitGameEngine()
    GameLayer.super.OnInitGameEngine(self)
    self.cbPlayStatus = {0, 0, 0, 0}
    self.cbCardData = {}
    self.cbOutCardState = {}
    self.cbWinIndex = {}
    --self.anayseCard = {}
    self.wBankerUser = yl.INVALID_CHAIR
    self.wPeiyin = {0, 0, 0, 0}
end

function GameLayer:OnResetGameEngine()
    GameLayer.super.OnResetGameEngine(self)
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 语音播放开始
function GameLayer:onUserVoiceStart( useritem, filepath )
    local view_id = self:SwitchViewChairID(useritem.wChairID)
    self._gameView:onUserVoiceStart(view_id)
    return view_id
end

-- 语音播放结束
function GameLayer:onUserVoiceEnded( view_id, filepath )
    self._gameView:onUserVoiceEnded(view_id)
end

function GameLayer:SwitchViewChairID(chair)
    local viewid = yl.INVALID_CHAIR
    local nChairCount = self._gameFrame:GetChairCount()
    if self.MeChairID == nil or self.MeChairID == yl.INVALID_CHAIR then
        self.MeChairID = self:GetMeChairID()
        --print('self chair id ', self.MeChairID)
    end
    
    local nChairID = self.MeChairID
    --print('nChairCount is : ', nChairCount)
    if chair ~= yl.INVALID_CHAIR and chair < nChairCount and nChairID ~= yl.INVALID_CHAIR then
        viewid = math.mod(chair + math.floor(cmd.GAME_PLAYER * 3 / 2) - nChairID, cmd.GAME_PLAYER) + 1
        --print('before viewid is : ', viewid, chair, nChairID)
        if nChairCount == 2 and viewid ~= 3 then        -- 两人时对方总在1号位
            viewid = 1
        elseif nChairCount == 3 and viewid == 1 then    -- 三人时1号位总空着
            --if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false and isCheckFix3 then
                if nChairID == 0 then
                    viewid = 2
                elseif nChairID == 2 then
                    viewid = 4
                end
            --end      
        end
    end
    return viewid
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair,time,clockId)
    -- body
    --[[
    if clockId == cmd.IDI_NULLITY then
        if time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_START_GAME then
        if time == 0 then
            self._gameFrame:setEnterAntiCheatRoom(false)--退出防作弊
            self:onExitTable()
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_CALL_BANKER then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CANCEL)
        end
    elseif clockId == cmd.IDI_TIME_USER_ADD_SCORE then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CHIP + 4)
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_TIME_OPEN_CARD then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_OPENCARD)
        end
    end

    --]]
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 用户配音改变
function GameLayer:onUserPeiyinChange(user_item)
	local view_id = self:SwitchViewChairID(user_item.wChairID)
    self.wPeiyin[view_id] = user_item.wPeiyin
    --print('user_item is ', user_item.wPeiyin, view_id)
end

-- 刷新房卡数据
function GameLayer:updatePriRoom()
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end

-- 场景信息
function GameLayer:onEventGameScene(cbGameStatus, dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cbGameStatus
    --end
    
	local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
           -- print('GameLayer _gameView ', wViewChairId, userItem.szNickName)
            --dump(userItem)
            self.wPeiyin[wViewChairId] = userItem.wPeiyin
            self._gameView:OnUpdateUser(wViewChairId, userItem)
        end
    end
    self._gameView:onResetView()

	if cbGameStatus == cmd.GS_TK_FREE	then				--空闲状态
        self:onSceneFree(dataBuffer)
    elseif cbGameStatus == cmd.GS_TK_PLAYING  then            --游戏状态
        self:onScenePlaying(dataBuffer)
	end

    -- 启动 心跳
    self:startOrStopHeartBeat( true )
    --self:startOrStopReqLocation( true )
    
    self:dismissPopWait()
end

--空闲场景
function GameLayer:onSceneFree(dataBuffer)
    print("onSceneFree")
    local int64 = Integer64.new()
    local lCellScore = dataBuffer:readscore(int64):getvalue()
    --local lRoomStorageStart = dataBuffer:readscore(int64):getvalue()
    --local lRoomStorageCurrent = dataBuffer:readscore(int64):getvalue()

    local lTurnScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTurnScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    local lCollectScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lCollectScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    self._gameView:setCellScore(lCellScore)
    if not GlobalUserItem.isAntiCheat() then    --非作弊房间
        if not yl.IS_REPLAY_MODEL then
            self._gameView.btStart:setVisible(true)
        end
        self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    end
    self._gameView:showJuShu()

    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbGameRule[1][4] == 1 then
        self.is_zhengdian = true
    end

    if room_data.cbGameRule[1][5] == 1 then
        self.is_zhuaneight = true
    end

    local tableId = self._gameFrame:GetTableID()
    local userItem = self._gameFrame:getTableUserItem(tableId, self:GetMeChairID())
    if userItem and yl.US_READY == userItem.cbUserStatus then
        self._gameView.btStart:hide()
    end
end

--游戏场景
function GameLayer:onScenePlaying(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_StatusPlay, dataBuffer)
    --dump(cmd_data)

    self.wCurrentUser = cmd_data.wCurrentUser

    local recover_card_num = 0
    for j = 1, cmd.GAME_CARD_NUM do
        self.cbCardData[j] = cmd_data.cbHandCardData[1][j]
        if self.cbCardData[j] ~= 0 then
            recover_card_num = recover_card_num + 1
        end
    end

    self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)
    
    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbGameRule[1][4] == 1 then
        self.is_zhengdian = true
    end

    if room_data.cbGameRule[1][5] == 1 then
        self.is_zhuaneight = true
    end
    
    self._gameView:createSelfCard(recover_card_num)
    self._gameView:showAllcards()

    self._gameView.player_num = cmd_data.cbUserCount
    ---self._gameView:gameSendCard(self:SwitchViewChairID(self.wCurrentUser), cmd.GAME_PLAYER * cmd.GAME_CARD_NUM)

    self.jushu = cmd_data.cbJushu
    self.lCardScore = cmd_data.lCardScore
    self.cbZhaUser = cmd_data.wPreUser
    local out_cards = {}
    local card_score = {}
    local gong_score = {}
    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.cbWinIndex[1][i]
        card_score[i] = cmd_data.lCardScoreT[1][i]
        gong_score[i] = cmd_data.lScore[1][i]
    end
   
    local real_card_num = {0, 0, 0, 0}
    local card_xian = {0, 0, 0, 0}
    for i = 1, cmd.GAME_PLAYER do
        out_cards[i] = {}
        card_xian[i] = cmd_data.cbCardXian[1][i]
        for j = 1, #cmd_data.cbOutCard[i] do
            out_cards[i][j] = cmd_data.cbOutCard[i][j]
            if out_cards[i][j] > 0 then
                real_card_num[i] = real_card_num[i] + 1
            end
        end
    end
    
    self:updatePriRoom()
    self._gameView:setBankerUser(self:SwitchViewChairID(self.wBankerUser))
    self._gameView:gameScenePlaying(self.wCurrentUser, out_cards, real_card_num, card_xian, card_score, gong_score, cmd_data.lCardScore, self.cbZhaUser)

    local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:recoverUserScore(wViewChairId, userItem.nTableScore)
           -- print('recover score is ', userItem.nTableScore)
            --dump(useritem)
        end
    end

    self:SetGameClock(self.wCurrentUser, cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
end

-- 游戏消息
function GameLayer:onEventGameMessage(sub,dataBuffer)
	if sub == cmd.SUB_S_GAME_START then 
		self:onSubGameStart(dataBuffer)
	elseif sub == cmd.SUB_S_SEND_CARD then 
		self:onSubSendCard(dataBuffer)
	elseif sub == cmd.SUB_S_OPERATE then 
		self:onSubOpenCard(dataBuffer)
	elseif sub == cmd.SUB_S_PLAYER_EXIT then 
		self:onSubPlayerExit(dataBuffer)
	elseif sub == cmd.SUB_S_GAME_END then 
		self:onSubGameEnd(dataBuffer)
    elseif sub == cmd.SUB_S_READY_STATE then 
		--self:onSubSendCard(dataBuffer)
        self.game_end = false
	else
		print("unknow gamemessage sub is"..sub)
	end
end

function GameLayer:getPlayNum()
    local num = 0
    for i = 1, cmd.GAME_PLAYER do
        if self.cbPlayStatus[i] == 1 then
            num = num + 1
        end
    end

    return num
end

--游戏开始
function GameLayer:onSubGameStart(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameStart, dataBuffer)
    self.wBankerUser = cmd_data.wBankerUser
    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = cmd_data.cbPlayerStatus[1][i]
    end
    self.jushu = cmd_data.cbJushu;
    self._gameView:showJuShu()
    self._gameView:startGame()
    self._gameView.player_num = cmd_data.cbUserCount
    --print('player count is ', self._gameView.player_num)
    self._gameView.bCanNextReplay = true;
end

--发牌消息
function GameLayer:onSubSendCard(dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
        if self._gameView._priView then
            self._gameView._priView:onRefreshInviteBtn()
        end
    --end
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_SendCard, dataBuffer)
    self.wCurrentUser = cmd_data.cbFirstUser
    --self.wBankerUser = self.wCurrentUser
    local wCurrentViewId = self:SwitchViewChairID(cmd_data.cbCurrentUser)
    if wCurrentViewId ~= cmd.MY_VIEWID then
        return
    end
	
    for j = 1, cmd.GAME_CARD_NUM do
        self.cbCardData[j] = cmd_data.cbHandCardData[1][j]
    end

    --dump(self.cbCardData)
    self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)
    --dump(self.anayseCard)
    
    local card_num = cmd.GAME_CARD_NUM
    if self.is_zhengdian == false then
        card_num = card_num - 1
    end
    self._gameView:createSelfCard(card_num)

    self._gameView:gameSendCard(self:SwitchViewChairID(self.wBankerUser), cmd.GAME_PLAYER * card_num)
    
end

--用户摊牌
function GameLayer:onSubOpenCard(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_Operator, dataBuffer)
    --dump(cmd_data)
    self.wCurrentUser = cmd_data.wPlayerID
    self.bCanOut = cmd_data.cbCanOut
    self.lCardScore = cmd_data.lCardScore
    self.cbTurnStart = cmd_data.cbTurnStart
    self.cbZhaUser = cmd_data.cbZhaUser
    local out_cards = {}
    local card_xian = cmd_data.cbCardXian
    local card_score = {}
    local gong_score = {}
    for i = 1, cmd.GAME_PLAYER do
        self.cbOutCardState[i] = cmd_data.cbOutCardState[1][i]
        self.cbWinIndex[i] = cmd_data.cbWinIndex[1][i]
        card_score[i] = cmd_data.lCardScoreT[1][i]
        gong_score[i] = cmd_data.lScore[1][i]
    end
   
    --
    local real_card_num = 0
    for i = 1, #cmd_data.cbOutCards[1] do
        out_cards[i] = cmd_data.cbOutCards[1][i]
        if out_cards[i] > 0 then
            real_card_num = real_card_num + 1
        end
    end
    
    self._gameView:showWinUser()

    local curOutChairId = self:SwitchViewChairID(self.wCurrentUser)

    if curOutChairId == nil or curOutChairId == yl.INVALID_CHAIR then
        return
    end
    self._gameView.flag_ready[curOutChairId]:hide()
    self._gameView:clearOneOutCard(self.wCurrentUser)

    
    if self.wCurrentUser == self:GetMeChairID() then
        self._gameView.bCanMoveCard = true
        self._gameView:setBtnState(true, self.bCanOut)
        
        self._gameView:clearOneOutCard(self.wCurrentUser)

    end
    
    local peiyin = GlobalUserItem.wPeiyin
    --[[
    if peiyin > 1 or peiyin < 0 then
        peiyin = GlobalUserItem.wPeiyin
    end
    --]]
   -- print('peiyin is ', peiyin)
    if cmd_data.wOp == GameLogic.USER_OPERATE_PASS or cmd_data.wOp == GameLogic.USER_OPERATE_HINT then
            local wViewChairId = self:SwitchViewChairID(cmd_data.cbZhaUser)
           -- print('show guo user is ', wViewChairId)
            if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
                return
            end
            self._gameView.flag_ready[wViewChairId]:show()
            self._gameView.flag_ready[wViewChairId]:texture('word/guo.png')
            if self.cbTurnStart == 1 then
                self._gameView:clearOutCard()
                self._gameView:showCardScore(self.lCardScore, card_score)
                for i = 1, cmd.GAME_PLAYER do
                    self._gameView.flag_ready[i]:hide()
                end
            end
            if cmd_data.cbZhaUser == self:GetMeChairID() then
                self._gameView:setBtnState(false)
            end
            self._gameView:clearOneOutCard(cmd_data.cbZhaUser)
            if GlobalUserItem.bVoiceAble then
                helper.music.playPeiyin( self.wPeiyin[wViewChairId], 'skip' )
                --AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/" .. tostring(peiyin) .. "/" .. tostring(peiyin) ..'_skip.mp3')
            end
            --[[
            if self.wCurrentUser == self:GetMeChairID() then
                self._gameView:clearOutCard()
            end 
            --]]
            --self._gameView:clearOutCard()
            self:SetGameClock(self.wCurrentUser, cmd.IDI_TIME_OUT_CARD, cmd.TIME_USER_OPEN_CARD)
            return
    end

    if cmd_data.cbIsRePush == 0 then
        self._gameView:showOutCard(cmd_data.cbZhaUser, out_cards, real_card_num, cmd_data.cbIsZha, card_xian, peiyin)
        
        if cmd_data.cbZhaUser == self:GetMeChairID() then
            self._gameView.bCanMoveCard = false
            self:removeCard(out_cards)
            self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)
            self._gameView:createSelfCard(#self.cbCardData, true)
            self._gameView:setBtnState(false)
            --dump(out_cards)
        end
        
        self._gameView:showGongScore(gong_score)
        self._gameView:showCardScore(self.lCardScore, card_score)
    end
    if real_card_num > 0 then
        local filename = nil
        local card_data = out_cards[1]
        local card_v = GameLogic:getCardValue(card_data)
        local is_king = false
        if card_data == 0x41 then
            card_v = 14
            is_king = true
        elseif card_data == 0x42 then
            card_v = 15
            is_king = true
        elseif card_data == 0x44 then
            card_v = 16
            is_king = true
        end

        if real_card_num < 4 then
            if (card_v == 15 or card_v == 16) and card_xian == 3 then
                filename = string.format("%02d_%02d", card_xian, 14)
            else
                filename = string.format("%02d_%02d", card_xian, card_v)
            end
            
        else
            if is_king then
                filename = string.format("%02d_%02d", 4, 14)
            else
                if card_xian < 9 then
                    filename = string.format("%02d_%02d", card_xian, 0)
                else
                    filename = string.format("%02d_%02d", 9, 0)
                end
                
            end
        end
        --print('play effect file is ', filename, self.wPeiyin[wViewChairId], wViewChairId)
        if GlobalUserItem.bVoiceAble then
            --AudioEngine.playEffect(filename)
            local wViewChairId = self:SwitchViewChairID(cmd_data.cbZhaUser)
            helper.music.playPeiyin(self.wPeiyin[wViewChairId], filename)
        end
    end
    
   
    
    self:SetGameClock(self.wCurrentUser, cmd.IDI_TIME_OUT_CARD, cmd.TIME_USER_OPEN_CARD)
    --AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/OPEN_CARD.wav")
end

function GameLayer:removeCard(out_cards)
    for i = 1, #out_cards do
        if out_cards[i] == 0 then
            break
        end
        for j = 1, #self.cbCardData do
            if self.cbCardData[j] == out_cards[i] then
                self.cbCardData[j] = 0
                break
            end
        end
    end
    

    local len = #self.cbCardData
    do 
        local i = 1
        while i <= len do
            if self.cbCardData[i] == 0 then
                table.remove(self.cbCardData, i)
                len = len - 1
            else
                i = i + 1
            end
        end
    end
   
    --print('after reomve card , len is ',  #self.cbCardData)
end

--用户强退
function GameLayer:onSubPlayerExit(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    self.cbPlayStatus[wPlayerID + 1] = 0
    self._gameView.nodePlayer[wViewChairId]:setVisible(false)
    self._gameView.bCanMoveCard = false
    self._gameView.btOpenCard:setVisible(false)
    --self._gameView.btPrompt:setVisible(false)
    self._gameView.spritePrompt:setVisible(false)
    self._gameView.spriteCardBG:setVisible(false)
    self._gameView:setOpenCardVisible(wViewChairId, false)
end

function GameLayer:sendOp(op, index)
    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_OxCard)
    cmd_data:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OPEN_CARD)
    cmd_data:pushword(op)
    local len = 0
    if op == GameLogic.USER_OPERATE_OUTCARD then
        len = #self._gameView.cbPreOutCard
        for i = 1, len do
            cmd_data:pushbyte(self._gameView.cbPreOutCard[i].data)
        end
    end
    for i = len + 1, cmd.GAME_MAX_OUT_CARD do
        --print('push data is ', i)
        cmd_data:pushbyte(0)
    end
    cmd_data:pushbyte(index)
    cmd_data:pushbyte(GlobalUserItem.wPeiyin)
    --self._gameView.bCanMoveCard = false
   -- print('send op ...........', GlobalUserItem.wPeiyin)
    --self._gameFrame:sendSocketData(cmd_data)
	return self:SendData(cmd.SUB_C_OPEN_CARD, cmd_data)
end

function GameLayer:openGameResultLayer()
    
end

--游戏结束
function GameLayer:onSubGameEnd(dataBuffer)
    self.game_end = true
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameEnd, dataBuffer)
    --dump(cmd_data)
    --self.game_result = cmd_data
    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.cbWinIndex[1][i]
    end

    self._gameView.animateCard:stopAllActions()
    self._gameView:gameEnd(cmd_data.lGameScore)

    --self:perform( handler(self, self.openGameResultLayer), 2, 1)
    local path = cs.game.SRC .. 'room.GameResultLayer'
    helper.pop.popLayer(path, nil, {self._gameView, cmd_data, self.wBankerUser, self._gameView.player_num})

    local cards = {}
    local card_num = 0
    for i = 1, #cmd_data.cbLeftCard[1] do
        table.insert(cards, cmd_data.cbLeftCard[1][i])
        if cmd_data.cbLeftCard[1][i] > 0 then
            card_num = card_num + 1
        end
    end

    self._gameView:gameEndShowLeftCard(cmd_data.cbLastUser, cards, card_num)

   


    --local index = self:GetMeChairID() + 1
    

    --self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    --AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_END.WAV")

    --print('self.m_userRecord is , ', self._gameView.player_num)
    self.m_userRecord = {}
	for i = 1, self._gameView.player_num do
		self.m_userRecord[i] = {}
		self.m_userRecord[i].cbHuCount =  1
		--self.m_userRecord[i].cbMingGang =  cmd_data.lGameScore[i]
		--self.m_userRecord[i].cbAnGang =  cmd_data.lGameScore[i]
		--self.m_userRecord[i].cbMaCount =  cmd_data.lGameScore[i]
		self.m_userRecord[i].lDetailScore = {}
	end
end

function GameLayer:getCardData(index)
    local index = self:GetMeChairID() + 1
    local data = self.cbCardData[index][i]
    return data
end

function GameLayer:getDetailScore()
	return self.m_userRecord
end

function GameLayer:getUserInfoByChairID(chairId)
	local viewId = self:SwitchViewChairID(chairId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getUserInfoByViewID(viewId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:onExitRoom()
    --self:startOrStopReqLocation( false )
    self:startOrStopHeartBeat( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end 
    end
    self:removeFromParent()
end

-- 刷新界面
function GameLayer:updateView()
end

--开始游戏
function GameLayer:onStartGame()
    -- body
    --self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
    self:KillGameClock()
    self._gameView:onResetView()
    self._gameView:startGame()
    self._gameFrame:SendUserReady()
end

--将视图id转换为普通id
function GameLayer:isPlayerPlaying(viewId)
    if viewId < 1 or viewId > cmd.GAME_PLAYER then
        print("view chair id error!")
        return false
    end

    for i = 1, cmd.GAME_PLAYER do
        if self:SwitchViewChairID(i - 1) == viewId then
            if self.cbPlayStatus[i] == 1 then
                return true
            end
        end
    end

    return false
end

function GameLayer:sendCardFinish()
    self:SetGameClock(self:GetMeChairID(), cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
end

function GameLayer:getMeCardLogicValue(num)
    local index = self:GetMeChairID() + 1

    --此段为测试错误
    if nil == index then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == num then
        showToast(self, "nil == index", 1)
        return false
    end
   
end

function GameLayer:getOxCard(cbCardData)
    return GameLogic:getOxCard(cbCardData)
end

--********************   发送消息     *********************--
function GameLayer:onBanker(cbBanker)
    local dataBuffer = CCmd_Data:create(1)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME,cmd.SUB_C_CALL_BANKER)
    dataBuffer:pushbyte(cbBanker)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onAddScore(lScore)
	print("下注金币", lScore)
    local dataBuffer = CCmd_Data:create(8)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_ADD_SCORE)
    dataBuffer:pushscore(lScore)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onOpenCard(is_special_type)
    local index = self:GetMeChairID() + 1
    local bOx = GameLogic:getOxCard(self.cbCardData[index])
    if is_special_type == nil then
        is_special_type = 0
    end
    
    local dataBuffer = CCmd_Data:create(15)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OPEN_CARD)
    dataBuffer:pushbyte(1)
    for i = 1, cmd.GAME_CARD_NUM do
        if is_special_type == 0 then
            dataBuffer:pushbyte(self._gameView.cbOpenCardData[i]) 
        else
            dataBuffer:pushbyte(self.cbCardData[index]) 
        end
    end
    
    dataBuffer:pushbyte(is_special_type)

    return self._gameFrame:sendSocketData(dataBuffer)
end


-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
       self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
      -- print('stop.............replay')
       self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
    if self._gameView.bCanNextReplay then
        if not PassRoom:getInstance():getNetFrame():doNextReplay() then
            self:startOrStopReplay( false )
        end
    end
     
end


-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:stop( yl.ActionTag.HEART )
            
            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )
            
        end
    else
       --- print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end

-- 检测心跳
function GameLayer:checkHeartBeat()
   -- print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end

-- 重连
function GameLayer:doReConnect()
   -- print('网络重连。。。')
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()
    PassRoom:getInstance():getNetFrame():onSearchRoom( PassRoom:getInstance().m_tabPriData.szServerID )
end


-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
        --print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
       -- print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    --print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置 
function GameLayer:onCheckSocketIsOK()
   self:startCheckIsSocketOK( false )
   self.check_is_socket_ok_times = 0
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('subGameResultLayer')
    if view then
        view:removeFromParent()
    end
end


return GameLayer
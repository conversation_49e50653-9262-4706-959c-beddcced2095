local GameViewLayer = class("GameViewLayer",function(scene)
		local gameViewLayer =  display.newLayer()
    return gameViewLayer
end)

cs.app.client('system.GameViewLayerEx').assign(GameViewLayer)

--require("client/src/frame/yl")
local cmd = cs.app.game("room.CMD_Game")
local PopupHead = appdf.req("client.src.system.PopupHead")
--local GameChatLayer = appdf.req(appdf.CLIENT_SRC.."plaza.views.layer.game.GameChatLayer")
local ExternalFun = require(appdf.EXTERNAL_SRC .. "ExternalFun")
local GameLogic = cs.app.game("room.GameLogic")

GameViewLayer.BT_PROMPT 			= 2
GameViewLayer.BT_OPENCARD 			= 3
GameViewLayer.BT_START 				= 4
GameViewLayer.BT_OK 				= 5
GameViewLayer.BT_CANCEL 			= 6
GameViewLayer.BT_CHIP 				= 7
GameViewLayer.BT_CHIP1 				= 8
GameViewLayer.BT_CHIP2 				= 9
GameViewLayer.BT_CHIP3 				= 10
GameViewLayer.BT_CHIP4 				= 11

GameViewLayer.BT_SWITCH 			= 12
GameViewLayer.BT_EXIT 				= 13
GameViewLayer.BT_CHAT 				= 29
GameViewLayer.BT_SETTING			= 14
--GameViewLayer.BT_TAKEBACK 			= 16
GameViewLayer.BT_DUIZI				= 16
GameViewLayer.BT_LIANGDUI			= 17
GameViewLayer.BT_SANTIAO			= 18
GameViewLayer.BT_SHUNZI				= 19
GameViewLayer.BT_TONGHUA			= 20
GameViewLayer.BT_HULU				= 21
GameViewLayer.BT_TIEZHI				= 22
GameViewLayer.BT_TONGHUASHUN		= 23
GameViewLayer.BT_YQHY 				= 24
GameViewLayer.BT_VOL_START			= 25
GameViewLayer.BT_VOL_END			= 26
GameViewLayer.BT_VOL				= 27
GameViewLayer.BT_VOL_CANCEL			= 28
GameViewLayer.BT_GOTOJIESUAN		= 30
GameViewLayer.BT_SPECAIL			= 31
GameViewLayer.BT_GIVEUP				= 32


GameViewLayer.FRAME 				= 1
GameViewLayer.NICKNAME 				= 2
GameViewLayer.SCORE 				= 3
GameViewLayer.FACE 					= 7

GameViewLayer.TIMENUM   			= 1
GameViewLayer.CHIPNUM 				= 1

--牌间距
GameViewLayer.CARDSPACING 			= 40

GameViewLayer.VIEWID_CENTER 		= 5

GameViewLayer.CARD_SCALE			= 0.9

GameViewLayer.MOVEMINDISTANCE		= 30

GameViewLayer.CARD_SHOW_WIDTH		= 40

GameViewLayer.RES_PATH 				= "game/thirteen/res/"

local ZORDER_HEAD_INFO = 10
local XIAZHU_NUM = 4
local ZORDER_CHAT_LAYER = 10

local pointPlayer = {cc.p(170, 115), cc.p(897, 625)}
local pointCard = {cc.p(display.cx, 100), cc.p(display.cx, 617)}
local pointClock = {cc.p(157, 275), cc.p(1037, 640)}
local pointOpenCard = {cc.p(display.size.width - 350, 115), cc.p(display.cx - 200, display.cy + 250)}
local pointTableScore = {cc.p(display.cx, 342), cc.p(display.cx, 505)}
local pointBankerFlag = {cc.p(243, 208), cc.p(965, 715)}
local pointChat = {cc.p(230, 250), cc.p(767, 690)}
local ptWinLoseAnimate = {cc.p(320, 60), cc.p(1065, 500)}
local pointUserInfo = {cc.p(205, 170), cc.p(445, 240)}
local anchorPoint = {cc.p(0, 0), cc.p(1, 1)}

local AnimationRes = 
{
	--{name = "banker", file = GameViewLayer.RES_PATH.."animation_banker/banker_", nCount = 11, fInterval = 0.2, nLoops = 1},
	{name = "faceFlash", file = GameViewLayer.RES_PATH.."animation_faceFlash/faceFlash_", nCount = 2, fInterval = 0.6, nLoops = -1},
	{name = "lose", file = GameViewLayer.RES_PATH.."animation_lose/lose_", nCount = 17, fInterval = 0.1, nLoops = 1},
	{name = "start", file = GameViewLayer.RES_PATH.."animation_start/start_", nCount = 11, fInterval = 0.15, nLoops = 1},
	{name = "victory", file = GameViewLayer.RES_PATH.."animation_victory/victory_", nCount = 17, fInterval = 0.13, nLoops = 1},
	{name = "yellow", file = GameViewLayer.RES_PATH.."animation_yellow/yellow_", nCount = 5, fInterval = 0.2, nLoops = 1},
	{name = "blue", file = GameViewLayer.RES_PATH.."animation_blue/blue_", nCount = 5, fInterval = 0.2, nLoops = 1}
}

function GameViewLayer:onInitData()
	self.bCardOut = {false, false, false, false, false, false, false, false, false }
	self.bMidCardOut = {false, false, false, false, false, false, false, false, false}
	self.lUserMaxScore = {1, 2, 3, 5, 10}
	self.chatDetails = {}
	self.gamePlayStatus = {}
	self.m_bNormalState = {}
	self.bCanMoveCard = false
	self.typeMapPng = {
		'word/t_sanpai.png',
		'word/t_duizi.png',
		'word/t_liangdui.png',
		'word/t_santiao.png',
		'word/t_shunzi.png',
		'word/t_tonghua.png',
		'word/t_hulu.png',
		'word/t_tiezhi.png',
		'word/t_tonghuashun.png',
	}
	self.typeMapSound = {
		'sanpai'
		,'duizi'
		,'liangdui'
		,'santiao'
		,'shunzi'
		,'tonghua'
		,'hulu'
		,'tiezhi'
		,'tonghuashun'
	}
	self.player_num = 0
	self.bCanNextReplay = true
end

function GameViewLayer:onExit()
	print("GameViewLayer onExit")
	yl.IS_REPLAY_MODEL = false
	self:onExitEx()
	--[[
	cc.Director:getInstance():getTextureCache():removeTextureForKey(GameViewLayer.RES_PATH.."card.png")
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile(GameViewLayer.RES_PATH.."game_oxnew_res.plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey(GameViewLayer.RES_PATH.."game_oxnew_res.png")
	--]]
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
    cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
end

local this
function GameViewLayer:ctor(scene)
	this = self
	self._scene = scene

	local main_node = helper.app.loadCSB('GameLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

	--房卡需要
	self.m_sparrowUserItem = {}
	self:onInitDataEx()

	self.cbOpenCardData = {}
	self.cbMidOpenCardData = {{0,0,0}, {0,0,0}, {0,0,0}}

	self:onInitData()
	self:preloadUI()
	--self.is_not_check = false

	--节点事件
	local function onNodeEvent(event)
		if event == "exit" then
			self:onExit()
		end
	end
	self:registerScriptHandler(onNodeEvent)

--[[
	display.newSprite(GameViewLayer.RES_PATH.."background.png")
		:move(display.center)
		:addTo(self)
--]]
	local bAble = GlobalUserItem.bSoundAble or GlobalUserItem.bVoiceAble					--声音
	if GlobalUserItem.bVoiceAble then
		AudioEngine.playMusic(GameViewLayer.RES_PATH.."sound/backMusic.mp3", true)
	end
	
	local  btcallback = function(ref, type)
		helper.app.tintClickEffect(ref, eventType)
        if type == ccui.TouchEventType.ended then
			if GlobalUserItem.bVoiceAble then
				AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/clickcard.mp3")
			end
         	this:onButtonClickedEvent(ref:getTag(),ref)
		elseif type == ccui.TouchEventType.began and ref:getTag() == GameViewLayer.BT_VOL then
			self:onButtonClickedEvent(GameViewLayer.BT_VOL_START, ref)
		elseif type == ccui.TouchEventType.canceled and ref:getTag() == GameViewLayer.BT_VOL then
			self:onButtonClickedEvent(GameViewLayer.BT_VOL_CANCEL, ref)
		end
    end

	self.game_is_start = false
	self.daqiang_ani_play = false

    --特殊按钮
    local pointBtSwitch = cc.p(display.size.width - 60, display.size.height - 60)


	
	--[[
	self.btSound = ccui.CheckBox:create("bt_sound_0.png",
										"bt_sound_1.png",
										"bt_soundOff_0.png",
										"bt_soundOff_1.png", 
										"bt_soundOff_1.png", ccui.TextureResType.plistType)
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_SOUND)
		:setTouchEnabled(false)
		:setSelected(not bAble)
		:addTo(self)
	self.btSound:addTouchEventListener(btcallback)
	--]]
	

	self.btChat = self.main_node:child('img_chat')
	self.btChat:setTag(GameViewLayer.BT_CHAT)
	self.btChat:addTouchEventListener(btcallback)

	self.btVol = self.main_node:child('img_vol')
	self.btVol:setTag(GameViewLayer.BT_VOL)
	self.btVol:addTouchEventListener(btcallback)

	self.btExit = ccui.Button:create("room/fanhui.png", "room/fanhui.png", "")
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_EXIT)
		:setTouchEnabled(false)
		:addTo(self)
	self.btExit:addTouchEventListener(btcallback)

	self.btExit = ccui.Button:create("room/set.png", "room/set.png", "")
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_SETTING)
		:setTouchEnabled(false)
		:addTo(self)
	self.btExit:addTouchEventListener(btcallback)


	self.btSwitch = ccui.Button:create("room/shez.png", "room/shez.png", "")
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_SWITCH)
		:addTo(self)
	self.btSwitch:addTouchEventListener(btcallback)

	self.btStart = self.main_node:child('btn_start')
	self.btStart:setTag(GameViewLayer.BT_START)
	self.btStart:zorder(100)
	--self.btStart:setVisible(true)
	self.btStart:addTouchEventListener(btcallback)

	self.btJieSuan = self.main_node:child('btn_gotoJieSuan')
	self.btJieSuan:setTouchEnabled(true)
	self.btJieSuan:setTag(GameViewLayer.BT_GOTOJIESUAN)
	self.btJieSuan:setVisible(false)
	self.btJieSuan:addTouchEventListener(btcallback)

	--[[
	self.btYqhy = self.main_node:child('btn_yqhy')
	self.btYqhy:setTag(GameViewLayer.BT_YQHY)
	--self.btYqhy:setVisible(true)
	self.btYqhy:addTouchEventListener(btcallback)
	--]]
	

	--普通按钮
	-- self.btPrompt = ccui.Button:create("bt_prompt_0.png", "bt_prompt_1.png", "", ccui.TextureResType.plistType)
	-- 	:move(yl.WIDTH - 163, 60)
	-- 	:setTag(GameViewLayer.BT_PROMPT)
	-- 	:setVisible(false)
	-- 	:addTo(self)
	-- self.btPrompt:addTouchEventListener(btcallback)
--[[
	self.btOpenCard = self.main_node:child('btn_opencard')
	self.btOpenCard:setTag(GameViewLayer.BT_OPENCARD)
	self.btOpenCard:addTouchEventListener(btcallback)

	--]]

	self.chatBubble = {}
	for i = 1 , cmd.GAME_PLAYER do
		local bubble = self.main_node:child('chat_bubble_' .. i):hide()
        bubble:child('place'):hide()
		self.chatBubble[i] = bubble
		self.chatBubble[i]:zorder(ZORDER_CHAT_LAYER)
	end

	self.btOk = self.main_node:child('btn_ok')
	self.btOk:setTag(GameViewLayer.BT_OK)
	self.btOk:addTouchEventListener(btcallback)

	self.btCancel = self.main_node:child('btn_cancel')
	self.btCancel:setTag(GameViewLayer.BT_CANCEL)
	self.btCancel:addTouchEventListener(btcallback)

	
	--print(LANG.DESKTOP_NUM)
	--dump(cs.game)
	self.txt_CellScore = cc.Label:createWithTTF("底注：0","common/round_body.ttf",24)
		:move(1040, display.size.height - 20)
		:setVisible(false)
		:addTo(self)
	self.txt_TableID = cc.Label:createWithTTF(LANG.DESKTOP_NUM,"common/round_body.ttf",24)
		:move(100, display.size.height - 20)
		:addTo(self)
	
	self.txt_TableID:hide()

	--摆牌背景
	self.spritePrompt = self.main_node:child('dun_bg')
	self.spritePrompt:setLocalZOrder(6)
	self.spriteKuang = {}
	local kuang1 = self.spritePrompt:child('cart_light_1')
	local kuang2 = self.spritePrompt:child('cart_light_2')
	self.spriteKuang[1] = kuang1
	self.spriteKuang[2] = kuang2


	--时钟
	--self.spriteClock = self.main_node:child('daojishibg')
	--self.spriteClock:setLocalZOrder(10)

	--local labAtTime = self.spriteClock:child('num')
	--labAtTime:setTag(GameViewLayer.TIMENUM)

	--用于发牌动作的那张牌
	--self.animateCard = self.main_node:child('animateCard')

	--四个玩家
	self.nodePlayer = {}
	for i = 1 ,cmd.GAME_PLAYER do
		--玩家结点
		self.nodePlayer[i] = self.main_node:child('face_bg_' .. i)
		print('....', self.nodePlayer[i])
		self.nodePlayer[i]:hide()

		--昵称
		--self.nicknameConfig = string.getConfig("public/round_body.ttf", 18)
		local name = self.nodePlayer[i]:child('name')
		name:setTag(GameViewLayer.NICKNAME)

		--score:hide()
	end
	
	self.m_node_player = {}
	for i=1, cmd.GAME_PLAYER do
		self.m_node_player[i] = self.nodePlayer[i]
	end


	--牌节点
	self.nodeCard = {}
	self.nodeCardPos = {}
	--牌的类型
	self.cardType = {}
	--下注的分数
	self.tableScore = {}

	self.runNode = {}
	self.qiangyan = {}

		
	--准备标志
	self.flag_ready = {}

	--特殊牌型标志
	self.special_flag = {}
	--摊牌标志
	self.flag_openCard = {}
	self.compare_bg = {}
	self.score_bg = {}
	self:createCards()
	for i = 1, cmd.GAME_PLAYER do
		
		--牌型
		--self.cardType[i] = self.main_node:child('cardType_' .. i)

		--桌面金币
		local score = self.nodePlayer[i]:child('txt_difen')
		self.tableScore[i] = score -- self.main_node:child('dizhu_' .. i)

		--准备
		self.flag_ready[i] = self.main_node:child('img_state_' .. i)
		self.flag_ready[i]:ignoreContentAdaptWithSize(true)
		self.flag_ready[i]:setLocalZOrder(5)

		self.special_flag[i] = self.main_node:child('img_special_' .. i)
		self.special_flag[i]:ignoreContentAdaptWithSize(true)

		--摊牌
		--[[
		self.flag_openCard[i] = display.newSprite("#sprite_openCard.png")
			:move(pointOpenCard[i])
			:setVisible(false)
			:addTo(self)
			--]]

		self.compare_bg[i] = self.main_node:child('compare_bg_' .. i)
		self.score_bg[i] = self.main_node:child('score_bg_' .. i)
	end

	self.nodeLeaveCard = cc.Node:create():addTo(self)

	self.spriteBankerFlag = display.newSprite()
		:setVisible(false)
		:setLocalZOrder(2)
		:addTo(self)

	self:resetTablecloth()

	

	self.panel_btn = self.main_node:child('bg'):child('panel_btn')
	local btn = self.panel_btn:child('btn_duizi')
	btn:setTag(GameViewLayer.BT_DUIZI)
	btn:addTouchEventListener(btcallback)
	--btn_special
	--[[
	btn = self.panel_btn:child('btn_liangdui')
	btn:setTag(GameViewLayer.BT_LIANGDUI)
	btn:addTouchEventListener(btcallback)
	--]]

	btn = self.panel_btn:child('btn_special')
	btn:setTag(GameViewLayer.BT_SPECAIL)
	btn:addTouchEventListener(btcallback)
	self.img_specail = btn

	btn = self.panel_btn:child('btn_giveup')
	btn:setTag(GameViewLayer.BT_GIVEUP)
	btn:addTouchEventListener(btcallback)
	self.btn_giveup = btn

	btn = self.panel_btn:child('btn_santiao')
	btn:setTag(GameViewLayer.BT_SANTIAO)
	btn:addTouchEventListener(btcallback)

	btn = self.panel_btn:child('btn_shunzi')
	btn:setTag(GameViewLayer.BT_SHUNZI)
	btn:addTouchEventListener(btcallback)

	btn = self.panel_btn:child('btn_tonghua')
	btn:setTag(GameViewLayer.BT_TONGHUA)
	btn:addTouchEventListener(btcallback)


	btn = self.panel_btn:child('btn_tonghuashun')
	btn:setTag(GameViewLayer.BT_TONGHUASHUN)
	btn:addTouchEventListener(btcallback)

	if yl.IS_REPLAY_MODEL then
		self.panel_btn:hide()
		self.btStart:hide()
		--self.btYqhy:hide()
	else
		self.btStart:hide()
		--self.btYqhy:show()
	end

--[[
	began = 0,
    moved = 1,
	--]]
	--点击事件
	self:setTouchEnabled(true)
	self:registerScriptTouchHandler(function(eventType, x, y)
		if eventType == "ended" then
			self:onEventTouchCallback(x, y)
		elseif eventType == 'moved' then
			self:onEventTouchMvCallback(x, y)
		elseif eventType == 'began' then
			self:onEventTouchBeginCallback(x, y)
		end
		return true
	end)

	self.main_node:perform(handler(self, self.checkJieSuan), 1, -1)
--[[
	local daqiang_user = {}
	table.insert(daqiang_user, {1, 2})
	table.insert(daqiang_user, {1, 3})
	table.insert(daqiang_user, {2, 3})
	self:showDaQiang(daqiang_user)
	--]]
	--self:showQuanDa(4)
end

function GameViewLayer:createCards()
	local bg = self.main_node:child('bg')
	local card101 = bg:child('card_1')
	local card201 = bg:child('card_2')
	local card301 = bg:child('card_3')
	local card401 = bg:child('card_4')
	local card501 = bg:child('card_5')
	for i = 1, cmd.GAME_PLAYER do
		--牌
		self.nodeCard[i] = {}
		self.nodeCardPos[i] = {}
		for j = 1, cmd.GAME_CARD_NUM do
			local tag = string.format('card_%d%02d', i, j)
			local x, y
			if i == 1 then
				self.nodeCard[i][j] = card101:clone()
				x, y = card101:pos()
				x = (j - 1) * 17 + x
				self.nodeCard[i][j]:pos(x, y)
				
			elseif i == 2 then
				self.nodeCard[i][j] = card201:clone()
				x, y = card201:pos()
				y = y - (j - 1) * 17
				self.nodeCard[i][j]:pos(x, y)
				self.nodeCard[i][j]:zorder(cmd.GAME_CARD_NUM + 10 - j)
			elseif i == 3 then
				self.nodeCard[i][j] = card301:clone()
				x, y = card301:pos()
				x = (j - 1) * 47 + x
				self.nodeCard[i][j]:pos(x, y)
				--self.nodeCard[i][j]:zorder(cmd.GAME_CARD_NUM - j)
			elseif i == 4 then
				self.nodeCard[i][j] = card401:clone()
				x, y = card401:pos()
				y = y - (j - 1) * 17
				self.nodeCard[i][j]:pos(x, y)
				self.nodeCard[i][j]:zorder(cmd.GAME_CARD_NUM + 10 - j)
			elseif i == 5 then
				self.nodeCard[i][j] = card501:clone()
				x, y = card501:pos()
				x = x - (j - 1) * 17
				self.nodeCard[i][j]:pos(x, y)
				self.nodeCard[i][j]:zorder(cmd.GAME_CARD_NUM + 10 - j)
			end
			self.nodeCardPos[i][j] = cc.p(x, y)
			self.nodeCard[i][j]:setName(tag)
			self.nodeCard[i][j]:hide()
			self.nodeCard[i][j]:addTo(bg)
		end
	end
end

function GameViewLayer:checkJieSuan()
--[[
	local tableId = self._scene._gameFrame:GetTableID()
	for i = 1, cmd.GAME_PLAYER do
		local userItem = self._scene._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self._scene:SwitchViewChairID(i - 1)
			self:OnUpdateUser(wViewChairId, userItem)
		end
	end
--]]
	if self.is_not_check then
		return
	end
	if cs.game.IS_RECIVE_GAME_RESULT == true then
		print('show jiesuan btn')
		--self:stopAllActions()
		self.spritePrompt:hide()
		self.panel_btn:hide()
		self.btStart:hide()
		--self.btJieSuan:show()
		if self._scene.is_jiesan == 1 then
			local layer = helper.app.getFromScene('GameResultLayer')
			if layer then
				layer:show()
			end
		end
		
		self.is_not_check = true
		--cs.game.IS_RECIVE_GAME_RESULT = false
	end
end


-------------------------------------------------------------------------------
-- 玩家聊天
-------------------------------------------------------------------------------
function GameViewLayer:userChat(wViewChairId, chatString)
	if not chatString or #chatString == 0 then return end

    local bubble = self.chatBubble[wViewChairId]
	local label = self.chatDetails[wViewChairId]

    -- 取消上次	
    if label then
		label:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	-- 创建label
	local limWidth = 24*12
	local labCountLength = cc.Label:createWithTTF(chatString,"common/round_body.ttf", 24)  
	if labCountLength:getContentSize().width > limWidth then
		label = cc.Label:createWithTTF(chatString,"common/round_body.ttf", 24, cc.size(limWidth, 0))
	else
		label = cc.Label:createWithTTF(chatString,"common/round_body.ttf", 24)
	end
    self.chatDetails[wViewChairId] = label
	label:setColor(cc.c3b(0, 0, 0))
	label:addTo(bubble)

	self:showBubble(wViewChairId, cc.size(label:size().width + 58, label:size().height + 40))
end


-------------------------------------------------------------------------------
-- 显示气泡
-------------------------------------------------------------------------------
function GameViewLayer:showBubble(wViewChairId, size, no_remove)
    local node = self.chatDetails[wViewChairId]
	print('showBubble 11111111111111111111', node)
    if not node then return end

	print('showBubble 11111111111111111111')
    local bubble = self.chatBubble[wViewChairId]
    local place = bubble:child('place')
    if not place._offset_right then
        place._offset_right = bubble:size().width - place:px()
    end
	bubble:size(size):stop():show():scale(0):runAction( cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) ) )

    -- 锚点在右侧的框size改变后子节点需要调整坐标，place在编辑器中设了右侧绑定，但无效，暂时先在这里修正
    if bubble:anchor().x >= 1  then
        place:px( size.width - place._offset_right )
    end

    local anchor = place:anchor()
    if node:getRotation() ~= 0 then -- 水平翻转问题修正
        anchor.x = 1 - anchor.x
    end
    node:anchor(anchor):pos( place:px(), size.height/2 )

    if not no_remove then
        node:runAction( cc.Sequence:create(
	        cc.DelayTime:create(3),
	        cc.CallFunc:create(function(ref)
                bubble:stop():runAction( cc.Sequence:create(
                    cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
                    cc.Hide:create(),
                    cc.CallFunc:create(function(sender)
                        if self.chatDetails[wViewChairId] then
    	    	            self.chatDetails[wViewChairId]:removeFromParent()
	    		            self.chatDetails[wViewChairId] = nil
                        end
                    end)
                ) )
	        end)
        ))
    end
end



-------------------------------------------------------------------------------
-- 玩家语音
-------------------------------------------------------------------------------
function GameViewLayer:onUserVoiceStart(wViewChairId)
	print('onUserVoiceStartonUserVoiceStartonUserVoiceStart')
    local bubble = self.chatBubble[wViewChairId]
	local sprite = self.chatDetails[wViewChairId]

	-- 取消上次
	if sprite then
		sprite:stop():removeFromParent()
		sprite = nil
	end

    -- 语音动画
    local sprite = helper.app.createAnimation('room/icon_voice_play', 4, 0.4, true, nil, ccui.TextureResType.localType)
	sprite:addTo(bubble)

	if wViewChairId == 4 then
		sprite:setRotation(180)
	end

    self.chatDetails[wViewChairId] = sprite
	self:showBubble(wViewChairId, cc.size(130, 90), true)
end


function GameViewLayer:onUserVoiceEnded(viewId)
	if self.chatDetails[viewId] then
	    self.chatDetails[viewId]:removeFromParent()
	    self.chatDetails[viewId] = nil
	    self.chatBubble[viewId]:hide()
	end
end

function GameViewLayer:onResetView()
	self.nodeLeaveCard:removeAllChildren()
	self.spriteBankerFlag:setVisible(false)
	--dump(self.nodeCardPos)
	dump(self.nodeCard)
	for i = 1, cmd.GAME_PLAYER do
		
		for j = 1, cmd.GAME_CARD_NUM do
			print('cur i and j is ', i, j)
			self.nodeCard[i][j]:pos(self.nodeCardPos[i][j].x, self.nodeCardPos[i][j].y)
			self.nodeCard[i][j]:setVisible(false)
			self.nodeCard[i][j]:setLocalZOrder(0)
			self.nodeCard[i][j]:texture('card/card403.png')

			local card = self.compare_bg[i]:child('card_' .. j)
			card:texture('card/card403.png')
			
		end
		self.compare_bg[i]:hide()
		self.score_bg[i]:hide()
		self.special_flag[i]:hide()
		for j = 1, 4 do
			local img = self.compare_bg[i]:child('card_type_' .. j)
			if img then
				img:hide()
			end
			
			local txt = self.compare_bg[i]:child('txt_score_' .. j)
			txt:hide()
		end
		--self.tableScore[i]:setVisible(false)
		--self.cardType[i]:setVisible(false)
		--self.nodePlayer[i]:child('img_zhuang'):setVisible(false)
	end
	
	self.bCardOut = {false, false, false, false, false, false, false, false, false}
	self.bMidCardOut = {false, false, false, false, false, false, false, false, false}
	self.cbOpenCardData = {}
	self.cbMidOpenCardData = {{0,0,0}, {0,0,0}, {0,0,0}}
	self.gamePlayStatus = {}
	self.is_show_special_type = false
	self.spriteKuang[1]:hide()
	self.spriteKuang[2]:hide()
	self.prev_tag = nil
	self.daqiang_ani_play = false

	self.btOk:hide()
	self.btCancel:hide()
	

	
	for i = 1, cmd.GAME_CARD_NUM do
		local card = self.spritePrompt:child('card_' .. i)
		card:texture('card/card403.png')
		card:removeChildByName('ma_card_sign')
		self.cbOpenCardData[i] = 0
	end

	if self.runNode then
		for i = 1, #self.runNode do
			self.runNode[i]:removeFromParent()
		end
	end
	
	self.runNode = {}

	if self.qiangyan then
		for i = 1, #self.qiangyan do
			self.qiangyan[i]:removeFromParent()
		end
	end
	self.qiangyan = {}

	if yl.IS_REPLAY_MODEL == true then
		self.panel_btn:hide()
		self.btStart:hide()
		--self.btYqhy:hide()
	end
end

function GameViewLayer:showJuShu()
	local room_data = PassRoom:getInstance().m_tabPriData
    -- 局数
    --local count = room_data.dwPlayCount + 1
	local limit = room_data.nPlayCount
	local count = self._scene.jushu or 1
	print('count, limit, ', self._scene.jushu, limit)
	self.main_node:child('title'):setString(LANG{'ROOM_JUSHU_TITLE', count=count, limit=limit})
end

--更新用户显示
function GameViewLayer:OnUpdateUser(viewId, userItem)
	if not viewId or viewId == yl.INVALID_CHAIR then
		print("OnUpdateUser viewId is nil")
		return
	end

--[[
	if userItem then
		print('GameViewLayer user stauts ', userItem.cbUserStatus)
	end
--]]
	print('更新用户显示', viewId)
	if self._scene.game_end then
		return
	end
	
	
	self.m_sparrowUserItem[viewId] = userItem
	
	--头像
	local head = self.nodePlayer[viewId]:child('sp_head')
	if not userItem then
		
		self.nodePlayer[viewId]:setVisible(false)
		if self.flag_ready[viewId] then
			self.flag_ready[viewId]:setVisible(false)
		end
		
		if head then
			head:setVisible(false)
		end
		
	else
		self.nodePlayer[viewId]:setVisible(true)

		self:recoverUserScore(viewId, userItem.nTableScore)

		local touxiang = self.nodePlayer[viewId]:child('head')

		--头像
		if not head then
			head = PopupHead:create(self, userItem, 90, ZORDER_HEAD_INFO)
			head:pos(touxiang:pos())			--初始位置
			head:setName('sp_head')
			head:addTo( self.nodePlayer[viewId] )
		else
			head:updateHead(userItem)
			--掉线头像变灰
			if userItem.cbUserStatus == yl.US_OFFLINE then
                head:runAction(cc.Sequence:create(
				        cc.DelayTime:create(5),
				        cc.CallFunc:create(function()     
							head:removeChildByName('offline')       
                            local icon = display.newSprite('word/font_offline.png'):setName('offline')
                            helper.layout.addCenter(head, icon)
				        end)
		        ))
			else
                head:removeChildByName('offline')
			end
		end
		head:show()

--[[
		if GlobalUserItem.cbGender == 0 then   --- 0 : man
			touxiang:texture('room/nan.png')
		else
			touxiang:texture('room/nv.png')
		end


		local fen = self.nodePlayer[viewId]:child('fen')
		local font_fen = self.nodePlayer[viewId]:child('font_fen')
--]]
		self:setNickname(viewId, userItem.szNickName)
		--self:setScore(viewId, userItem.lScore)
		if self.flag_ready[viewId] then
			self.flag_ready[viewId]:setVisible(yl.US_READY == userItem.cbUserStatus)
			self.flag_ready[viewId]:texture('word/yizhunbei.png')
		end

		local is_fangzhu = userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID
		self.nodePlayer[viewId]:child('fangzhu'):setVisible(is_fangzhu)
		self.nodePlayer[viewId]:child('fangzhu'):setLocalZOrder(10)

		if is_fangzhu == true and viewId == cmd.MY_VIEWID then
			self.game_is_start = true
		end
		
		
	end
	
end

--****************************      计时器        *****************************--
function GameViewLayer:OnUpdataClockView(viewId, time)

	if not viewId or viewId == yl.INVALID_CHAIR or not time then
		self.main_node:child('clock'):setVisible(false)
	else
		if viewId == cmd.MY_VIEWID then
			self.main_node:child('clock'):setVisible(true)
			self.main_node:child('clock'):child('txt'):setString(time);
		end
		if time <= 0 then
			self.main_node:child('clock'):setVisible(false)
		end
	end

end

function GameViewLayer:setClockPosition(viewId)
	--[[
	if viewId and pointClock[viewId] ~= nil then
		self.spriteClock:move(pointClock[viewId])
	else
		self.spriteClock:move(display.cx, display.cy + 50)
	end
	--]]
    --self.spriteClock:setVisible(true)
end

function GameViewLayer:onEventTouchBeginCallback(x, y)
	self.mv_status = self.mv_status or {}
	for i = 1, cmd.GAME_CARD_NUM do
		self.mv_status[i] = false
	end
	self.is_select = false
	self.start_pt = cc.p(x, y)
end

function GameViewLayer:onEventTouchMvCallback(x, y)
	--牌可点击
	if self.bCanMoveCard == true then
		--local size1 = self.nodeCard[cmd.MY_VIEWID]:getContentSize()
		--local x1, y1 = self.nodeCard[cmd.MY_VIEWID]:getPosition()
		
		for i = cmd.GAME_CARD_NUM, 1, -1 do
			local card = self.nodeCard[cmd.MY_VIEWID][i]
			local x2, y2 = card:getPosition()
			local size2 = card:getContentSize()
			local rect = card:getBoundingBox()
			--rect.x = rect.x * 0.75
			--rect.y = rect.y * 0.75
			rect.width = rect.width * GameViewLayer.CARD_SCALE
			rect.height = rect.height * GameViewLayer.CARD_SCALE
			--print('rect is ', rect.x, rect.y, rect.width, rect.height, x, y)
			--rect.x = x1 - size1.width/2 + x2 - size2.width/2
			--rect.y = y1 - size1.height/2 + y2 - size2.height/2
			if cc.rectContainsPoint(rect, cc.p(x, y)) and not self.mv_status[i] 
				and not self.bMidCardOut[i]  then
				self.mv_status[i] = true
				print('cur status is ', i)
				self.is_select = true
			end
		end
	end
end

--**************************      点击事件        ****************************--
--用于触发手牌的点击事件
function GameViewLayer:onEventTouchCallback(x, y)
	--按钮滚回
	if self.bBtnInOutside then
		self:onButtonSwitchAnimate(true)
	end

	-- --聊天框
	-- if self._chatLayer:isVisible() then
	-- 	self._chatLayer:showGameChat(false)
	-- end

	

	--牌可点击
	if self.bCanMoveCard == true then
		--local size1 = self.nodeCard[cmd.MY_VIEWID]:getContentSize()
		--local x1, y1 = self.nodeCard[cmd.MY_VIEWID]:getPosition()
		--local is_select = false
		local dis_x = math.abs(x - self.start_pt.x)
		if dis_x < GameViewLayer.MOVEMINDISTANCE then
			for i = 1, cmd.GAME_CARD_NUM do
				self.mv_status[i] = false
			end
			self.is_select = false
		end
		for i = 1, cmd.GAME_CARD_NUM do
			if self.mv_status[i] then
				local card = self.nodeCard[cmd.MY_VIEWID][i]
				local x2, y2 = card:getPosition()
				local size2 = card:getContentSize()
				local rect = card:getBoundingBox()
				rect.width = rect.width * GameViewLayer.CARD_SCALE - GameViewLayer.CARD_SHOW_WIDTH
				rect.height = rect.height * GameViewLayer.CARD_SCALE
				local hit_pt = cc.p(x, y)
				if x > self.start_pt.x then
					hit_pt = self.start_pt
				end
				if not cc.rectContainsPoint(rect, hit_pt) then
					self.mv_status[i] = false
				end
				break
			end
		end
		
		for i = cmd.GAME_CARD_NUM, 1, -1 do
			local card = self.nodeCard[cmd.MY_VIEWID][i]
			local x2, y2 = card:getPosition()
			local size2 = card:getContentSize()
			local rect = card:getBoundingBox()
			--rect.x = rect.x * 0.75
			--rect.y = rect.y * 0.75
			rect.width = rect.width * GameViewLayer.CARD_SCALE
			rect.height = rect.height * GameViewLayer.CARD_SCALE
			--print('rect is ', rect.x, rect.y, rect.width, rect.height, x, y)
			--rect.x = x1 - size1.width/2 + x2 - size2.width/2
			--rect.y = y1 - size1.height/2 + y2 - size2.height/2
			if cc.rectContainsPoint(rect, cc.p(x, y)) and self.bMidCardOut[i] == false then
				if self.mv_status[i] then
					self.mv_status[i] = false
				end
				if false == self.bCardOut[i] then
					card:move(x2, y2 + 20)
				elseif true == self.bCardOut[i] then
					card:move(x2, y2 - 20)
				end
				self.bCardOut[i] = not self.bCardOut[i]
				--self:updateCardPrompt()
				self.is_select = true
				break
			end
		end

		

		for i = cmd.GAME_CARD_NUM, 1, -1 do
			if self.mv_status[i] then
				local card = self.nodeCard[cmd.MY_VIEWID][i]
				local x2, y2 = card:getPosition()
				if false == self.bCardOut[i] then
					card:move(x2, y2 + 20)
				elseif true == self.bCardOut[i] then
					card:move(x2, y2 - 20)
				end
				self.bCardOut[i] = not self.bCardOut[i]
				is_select = true
				self.mv_status[i] = not self.mv_status[i]
			end
		end

		

		local count = 0
		
		for i = 1, cmd.GAME_CARD_NUM do
			if self.bCardOut[i] == true then
				count = count + 1
			end
		end

		print('count is  ... ', count, self.is_select)

		if count == 5 then
			if self.cbMidOpenCardData[2][1] == 0 then
				self.spriteKuang[1]:show()
			end
			if self.cbMidOpenCardData[3][1] == 0 then
				self.spriteKuang[2]:show()
			end
		end

		

		--self.spritePrompt
		if count == 3  then
			for i = 1, cmd.GAME_CARD_NUM do
				local card = self.spritePrompt:child('card_' .. i)
				local rect = card:getBoundingBox()
				local pos= self.spritePrompt:convertToWorldSpace(cc.p(rect.x, rect.y))
				rect.x = pos.x
				rect.y = pos.y
				rect.width = rect.width * GameViewLayer.CARD_SCALE
				rect.height = rect.height * GameViewLayer.CARD_SCALE
				--print('rect is ', rect.x, rect.y, rect.width, rect.height, x, y)
				print('cur card point prev ... ', i, self.bMidCardOut[i])
				if cc.rectContainsPoint(rect, cc.p(x, y)) and self.cbOpenCardData[i] == 0 then
					print('cur card point ... ', i, self.bMidCardOut[i])
					self:updateShowCard(count, i)
					return
				end
			end
		else
			for i = 1, cmd.GAME_CARD_NUM do
				local card = self.spritePrompt:child('card_' .. i)
				local rect = card:getBoundingBox()
				local pos= self.spritePrompt:convertToWorldSpace(cc.p(rect.x, rect.y))
				rect.x = pos.x
				rect.y = pos.y
				rect.width = rect.width * 0.6
				rect.height = rect.height * 0.6
				--card:removeChildByName('ma_card_sign')
				--print('rect is ', rect.x, rect.y, rect.width, rect.height, x, y)
				if cc.rectContainsPoint(rect, cc.p(x, y)) and self.cbOpenCardData[i] > 0 then
					self:resetView()
					self.btOk:hide()
					self.btCancel:hide()
					--self:updateCardPos()
					return
				end
			end

		end

		if not self.is_select then
			self:updateCardPos()
			for i = 1, cmd.GAME_CARD_NUM do
				self.bCardOut[i] = false
			end
			self.spriteKuang[1]:hide()
			self.spriteKuang[2]:hide()
		end
		
	end

end

function GameViewLayer:updateCardPos()
	local count = 0
	for i = 1, cmd.GAME_CARD_NUM do
		if self.bMidCardOut[i] == true then
			count = count + 1
		end
	end
	local start_pos_index = math.floor(count / 2)
	for i = 1, cmd.GAME_CARD_NUM do
		if self.bMidCardOut[i] == false then
			local mv_pos = self.nodeCardPos[cmd.MY_VIEWID][start_pos_index + 1]
			self.nodeCard[cmd.MY_VIEWID][i]:pos(mv_pos.x, mv_pos.y)
			start_pos_index = start_pos_index + 1
		end
	end
end

function GameViewLayer:isCorrectCards()
	for i = 1, 2 do
		for j = i + 1, 3 do
			if GameLogic:compare(self.cbMidOpenCardData[i], self.cbMidOpenCardData[j]) then
				helper.pop.message(LANG.DAO_DAXIAO_ERROR)
				return false
			end
		end
	end

	return true
end

function GameViewLayer:updateShowCard(count, index)
	local is_return = true
	local start_index = 1
	local cur_mid_index = 0
	if index >= 7 and count == 3 then  -- 后墩
		start_index = 7
		is_return = false
		cur_mid_index = 3
	elseif index <= 3 and count == 3 then -- 前墩
		is_return = false
		cur_mid_index = 1
	elseif index >= 4 and index < 7 and count == 3  then
		start_index = 4
		is_return = false
		cur_mid_index = 2		
	end
	
	if is_return then
		return
	end

	if GlobalUserItem.bVoiceAble then
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/fangpai.mp3")
	end
	local mid_index = 1
	for i = 1, cmd.GAME_CARD_NUM do
		if self.bCardOut[i] == true then
			local index = self._scene:GetMeChairID() + 1
			local data = self._scene.cbCardData[index][i]
			local value = GameLogic:getCardValue(data)
        	local color = GameLogic:getCardColor(data)
			local card = self.spritePrompt:child('card_' .. start_index)
			self.cbOpenCardData[start_index] = data
			self.cbMidOpenCardData[cur_mid_index][mid_index] = data
			mid_index = mid_index + 1
			local png = string.format('card/card%d%02d.png', color, value)
			card:texture(png)
			
			--card:removeChildByName('ma_card_sign')
			start_index = start_index + 1

			self.nodeCard[cmd.MY_VIEWID][i]:hide()
			self.bCardOut[i] = false
			self.bMidCardOut[i] = true
		end
	end
	self.start_index = 0
	self.spriteKuang[1]:hide()
	self.spriteKuang[2]:hide()

	local count = 0
	for i = 1, cmd.GAME_CARD_NUM do
		if self.bMidCardOut[i] == true then
			count = count + 1
		end
	end


	if count == 6 then
		if self.cbOpenCardData[1] == 0 then
			start_index = 1
			cur_mid_index = 1
		elseif self.cbOpenCardData[4] == 0 then
			start_index = 4
			cur_mid_index = 2
		else
			start_index = 7
			cur_mid_index = 3
		end
		mid_index = 1
		for i = 1, cmd.GAME_CARD_NUM do
			if self.bMidCardOut[i] == false then
				local index = self._scene:GetMeChairID() + 1
				local data = self._scene.cbCardData[index][i]
				print(' index, i is ', index, i)
				local value = GameLogic:getCardValue(data)
        		local color = GameLogic:getCardColor(data)
				local card = self.spritePrompt:child('card_' .. start_index)
				self.cbOpenCardData[start_index] = data
				self.cbMidOpenCardData[cur_mid_index][mid_index] = data
				mid_index = mid_index + 1
				local png = string.format('card/card%d%02d.png', color, value)
				card:texture(png)
				self:addMaCardSign(card, value, color)
				--card:removeChildByName('ma_card_sign')
				start_index = start_index + 1

				--local tag = string.format('card3%02d.png', value)
				self.nodeCard[cmd.MY_VIEWID][i]:hide()
				self.bCardOut[i] = false
				self.bMidCardOut[i] = true
			end
			self.panel_btn:hide()
		end
		self.btOk:show()
		self.btCancel:show()
	else

		self._scene:getTypeWithShow()
		self:updateCardPos()
	end
	
	
end

--按钮点击事件
function GameViewLayer:onButtonClickedEvent(tag,ref)
	if tag == GameViewLayer.BT_EXIT then
		--self._scene:onQueryExitGame()
		if not yl.IS_REPLAY_MODEL then
			self._scene:onQueryExitGame()
		else
			self._scene:onExitRoom()
		end
		
		--[[
		if yl.IS_REPLAY_MODEL or self.game_is_start == false then
			self._scene:onExitRoom()
		else
			PassRoom:getInstance():queryDismissRoom()
		end
		--]]
	elseif tag == GameViewLayer.BT_OPENCARD then
		if not self:isCorrectCards() then
			return 
		end

		self.bCanMoveCard = false
		self.btOpenCard:setVisible(false)
		--self.btPrompt:setVisible(false)
		self.spritePrompt:setVisible(false)
		--self.spriteCardBG:setVisible(false)

		self._scene:onOpenCard()
	elseif tag == GameViewLayer.BT_PROMPT then
		self:promptOx()
	elseif tag == GameViewLayer.BT_START then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self.btStart:setVisible(false)
		--self.btYqhy:setVisible(false)
		self._scene:onStartGame()
	elseif tag == GameViewLayer.BT_GOTOJIESUAN then
		self:openJieSuan()
	elseif tag == GameViewLayer.BT_OK then
		if #self.cbOpenCardData ~= cmd.GAME_CARD_NUM then
			return
		end 
		if not self:isCorrectCards() then
			return 
		else
			self.bCanMoveCard = false
			self.spritePrompt:hide()
			self._scene:onOpenCard()
			--self:resetView()
		end
		--nicknameConfigend
		
		self.btOk:setVisible(false)
		self.btCancel:setVisible(false)
		
	elseif tag == GameViewLayer.BT_CANCEL then
		self.btOk:setVisible(false)
		self.btCancel:setVisible(false)
		self:resetView()
	elseif tag - GameViewLayer.BT_CHIP == 1 or
			tag - GameViewLayer.BT_CHIP == 2 or
			tag - GameViewLayer.BT_CHIP == 3 or
			tag - GameViewLayer.BT_CHIP == 4 then
			self.chip_bg:setVisible(false)
		for i = 1, XIAZHU_NUM do
			self.btChip[i]:setVisible(false)
		end
		local index = tag - GameViewLayer.BT_CHIP
		self._scene:onAddScore(self.lUserMaxScore[index])
	elseif tag == GameViewLayer.BT_CHAT then
		--self._chatLayer:showGameChat(true)
		self:showChatLayer()
	elseif tag == GameViewLayer.BT_VOL then
		print("录音结束！")
		helper.voice.stopRecord()
	elseif tag == GameViewLayer.BT_VOL_START then
		print("录音开始！")
        helper.voice.startRecord( self._scene._gameFrame )
		--self:onUserVoiceStart(cmd.MY_VIEWID)
		--self:showBubble(cmd.MY_VIEWID, cc.size(130, 90), true)
	elseif tag == GameViewLayer.BT_VOL_CANCEL then
		print("录音取消！")
        helper.voice.cancelRecord()
	elseif tag == GameViewLayer.BT_SWITCH then
		print("BT_SWITCH")
		self:onButtonSwitchAnimate()
	elseif tag == GameViewLayer.BT_DUIZI 	
	or tag == GameViewLayer.BT_LIANGDUI 	
	or tag == GameViewLayer.BT_SANTIAO 	
	or tag == GameViewLayer.BT_SHUNZI 		
	or tag == GameViewLayer.BT_TONGHUA 	
	or tag == GameViewLayer.BT_HULU 		
	or tag == GameViewLayer.BT_TIEZHI 		
	or tag == GameViewLayer.BT_TONGHUASHUN then
		if self.bCanMoveCard then
			self:onBtnCardType(tag - GameViewLayer.BT_DUIZI)
		end
	elseif tag == GameViewLayer.BT_SPECAIL then
		if self.bCanMoveCard then
			local index = self._scene:GetMeChairID() + 1
			self:showSpecialDialog(index)
		end
	elseif tag == GameViewLayer.BT_GIVEUP then
		local csb_node = helper.app.loadCSB('GiveupLayer.csb')
		self.giveupnode = csb_node
		self:addChild(csb_node)

		local panel = csb_node:child('panel')
		local bg = panel:child('bg')
		local img_cancel = bg:child('img_cancel')
		img_cancel:addTouchEventListener(handler(self, self.CancelGiveup))

		local img_cancel = bg:child('img_ok')
		img_cancel:addTouchEventListener(handler(self, self.OkGiveup))

		--[[
		self.bCanMoveCard = false
		self.spritePrompt:hide()
		self._scene:onOpenCard(0, 1)
		--]]
	elseif tag == GameViewLayer.BT_SETTING then
		helper.link.toSetting( cs.game.SRC .. 'RoomSetting')
	else
		showToast(self, LANG.FUNC_NOT_OPEN, 1)
	end
end

function GameViewLayer:CancelGiveup(  )
	-- body
	self.giveupnode:removeFromParent()
	self.giveupnode = nil
end

function GameViewLayer:OkGiveup(  )
	-- body
	self.bCanMoveCard = false
	self.spritePrompt:hide()
	self._scene:onOpenCard(0, 1)
	self:CancelGiveup()
end

function GameViewLayer:openJieSuan()
	print('open jiesuan ui')
	local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
    if room_result_layer then
		print('open jiesuan ui', room_result_layer)
        room_result_layer:show()
    end
end

function GameViewLayer:InviteHY()
	local data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[GlobalUserItem.nCurGameKind]

    local name = config.NAME
    local brand = cs.game.BRAND
    local room_id = data.szServerID
    local jushu = data.nPlayCount
    local cost = data.nFee
    local pay_type = data.cbPayType
    local zhifu = LANG{'CREATE_ZHIFU_'..pay_type, fangka = cost}
    if jushu < 0 then
        jushu = LANG{'ROOM_KUN', kun=-jushu}
    end
    local renshu = PassRoom:getInstance():getChairCount()
    local difen = data.lCellScore

	local url = yl.INVITE_URL .. '?roomId=' .. room_id
    local title = LANG{'ROOM_INVITE_TITLE', brand = brand, name = name, room = room_id}
	local desc = LANG{'ROOM_INVITE_CONTENT', jushu = jushu, renshu = renshu, zhifu = zhifu}

    helper.pop.shareLink(url, title, desc, 'word/font_title_invite.png')
end

function GameViewLayer:showChatLayer()
	print('main.ChatLayermain.ChatLayermain.ChatLayer')
	if not self._chatLayer then
		local max_history = 30
        local path = cs.app.CLIENT_SRC .. 'main.ChatLayer'
        self._chatLayer = helper.pop.popLayer(path, self, {self._scene._gameFrame, max_history}, 100, true)
        self._chatLayer:zorder(ZORDER_CHAT_LAYER)
		self._chatLayer:hide()
		
		if self._chat_histories then
            local index = 1
            if #self._chat_histories > max_history then
                index = #self._chat_histories - max_history + 1
            end
            for i=index, #self._chat_histories do
                local v = self._chat_histories[i]
                self._chatLayer:addHistory(v[1], v[2])
            end
		end
        
	end

	self._chatLayer:effectShow()
end

-------------------------------------------------------------------------------
-- 增加聊天历史记录
-------------------------------------------------------------------------------
function GameViewLayer:addChatHistory(wViewChairId, chatString)
    local useritem = self.m_sparrowUserItem[wViewChairId]
    if not useritem then return end

    if self._chatLayer and not tolua.isnull(self._chatLayer) then
        self._chatLayer:addHistory(useritem.szNickName, chatString)
    else
        if not self._chat_histories then
            self._chat_histories = {}
        end
        table.insert(self._chat_histories, {useritem.szNickName, chatString})
    end
end

-------------------------------------------------------------------------------
-- 玩家聊天
-------------------------------------------------------------------------------
function GameViewLayer:userChat(wViewChairId, chatString)
	if not chatString or #chatString == 0 then return end

    self:addChatHistory(wViewChairId, chatString)

    local bubble = self.chatBubble[wViewChairId]
	local label = self.chatDetails[wViewChairId]

    -- 取消上次	
    if label then
		label:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	-- 创建label
	local limWidth = 24*12
	local label = cc.Label:createWithSystemFont(chatString, cs.game.FONT_NAME, 24)
	if label:size().width > limWidth then
		label = cc.Label:createWithSystemFont(chatString, cs.game.FONT_NAME, 24, cc.size(limWidth, 0))
	end
    self.chatDetails[wViewChairId] = label
	label:setColor(cc.c3b(0, 0, 0))
	label:addTo(bubble)

	self:showBubble(wViewChairId, cc.size(label:size().width + 58, label:size().height + 40))
end


-------------------------------------------------------------------------------
-- 玩家表情
-------------------------------------------------------------------------------
function GameViewLayer:userExpression(wViewChairId, wItemIndex)
	if not wItemIndex or wItemIndex < 1 then return end

    self:addChatHistory(wViewChairId, wItemIndex)

    local bubble = self.chatBubble[wViewChairId]
	local icon = self.chatDetails[wViewChairId]

	-- 取消上次
	if icon then
		icon:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	local strName = string.format("room/face_%02d.png", wItemIndex)
	icon = display.newSprite(strName)
	icon:addTo(bubble)
    self.chatDetails[wViewChairId] = icon

	self:showBubble(wViewChairId, cc.size(130, 90))
end


-------------------------------------------------------------------------------
-- 生成玩法字符串
-------------------------------------------------------------------------------
function GameViewLayer:makeRuleStr()
	local room_data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[ GlobalUserItem.nCurGameKind ]
    local t = {}
	self.m_rule_arr = self.m_rule_arr or {}
	self.m_rule_arr = room_data.cbGameRule[1]
    for i, v in ipairs(self.m_rule_arr) do
        if i > 2 then   -- 1是标志位
            local rule_key = 'RULE' .. (i - 3) .. (v == 1 and '' or '_NONE')
            local str = config[rule_key]
            if str then 
                t[#t + 1] = str
            end
        end
    end
    return table.concat(t, '/')
end

function GameViewLayer:resetView()
	self.spritePrompt:show()
	self.panel_btn:show()
	for i = 1, cmd.GAME_CARD_NUM do
		local card = self.spritePrompt:child('card_' .. i)
		card:removeChildByName('ma_card_sign')
		card:texture('card/card403.png')
	end
	for i = 1, cmd.GAME_CARD_NUM do
		self.nodeCard[cmd.MY_VIEWID][i]:show()
		local pos = self.nodeCardPos[cmd.MY_VIEWID][i]
		self.nodeCard[cmd.MY_VIEWID][i]:pos(pos.x, pos.y)
		self.bCardOut[i] = false
		self.bMidCardOut[i] = false
		self.cbOpenCardData[i] = 0
	end
	self.cbMidOpenCardData = {{0,0,0}, {0,0,0}, {0,0,0,}}
	self._scene:getTypeWithShow()
	self.spriteKuang[1]:hide()
	self.spriteKuang[2]:hide()
	self.start_index = 0
	self.prev_tag = nil
end

function GameViewLayer:onBtnCardType(tag)
	if not self.prev_tag then
		self.prev_tag = -1
		self.start_index = 0
	end
	if self.prev_tag ~= tag then
		self.start_index = 0
		self.second_index = 0
	end
	self._scene:getPos(tag, self.start_index)
	self.prev_tag = tag
end

function GameViewLayer:MvCard(pos_index, type)
	for i = 1, cmd.GAME_CARD_NUM do
		self.nodeCard[cmd.MY_VIEWID][i]:pos(self.nodeCardPos[cmd.MY_VIEWID][i].x, self.nodeCardPos[cmd.MY_VIEWID][i].y)
		self.bCardOut[i] = false
	end

	self:updateCardPos()


	if #pos_index < 1 then
		return
	end

	local len = #pos_index
	self.start_index = self.start_index + 1
	
	self.start_index = self.start_index - math.floor(self.start_index / len) * len

	if self.start_index == 0 then
		self.start_index = #pos_index
	end

	
	local mv_pos_index = pos_index[self.start_index]

	for i = 1, #mv_pos_index do
		local mv_by = cc.MoveBy:create(0.1, cc.p(0, 20))
		print('mv_pos_index ', mv_pos_index[i], self.start_index)
		self.nodeCard[cmd.MY_VIEWID][mv_pos_index[i]]:runAction(mv_by)
		self.bCardOut[mv_pos_index[i]] = true
	end
	
	local count = 0
	for i = 1, cmd.GAME_CARD_NUM do
		if self.bCardOut[i] == true then
			count = count + 1
		end
	end

	print('count is  ... ', count, self.is_select)

	if count == 5 then
		if self.cbMidOpenCardData[2][1] == 0 then
			self.spriteKuang[1]:show()
		end
		if self.cbMidOpenCardData[3][1] == 0 then
			self.spriteKuang[2]:show()
		end
	end	
	
end

-- 初始化玩家
function GameViewLayer:initPlayers()
end


function GameViewLayer:onButtonSwitchAnimate(bTakeBack)
	local fInterval = 0.15
	local spacing = 80
	local originX, originY = self.btSwitch:getPosition()
	for i = GameViewLayer.BT_EXIT, GameViewLayer.BT_SETTING do
		local nCount = i - GameViewLayer.BT_EXIT + 1
		local button = self:getChildByTag(i)
		if not button  then break end
		button:setTouchEnabled(false)
		--算时间和距离
		local time = fInterval*nCount
		local pointTarget = cc.p(0, spacing*nCount)

		local fRotate = 720
		if not bTakeBack then 			--按钮滚出(否则滚回)
			fRotate = -fRotate
			pointTarget = cc.p(-pointTarget.x, -pointTarget.y)
		end

		button:runAction(cc.Sequence:create(
			cc.Spawn:create(cc.MoveBy:create(time, pointTarget), cc.RotateBy:create(time, fRotate)),
			cc.CallFunc:create(function()
				if not bTakeBack then
					button:setTouchEnabled(true)
					self.bBtnInOutside = true
				else
					self.bBtnInOutside = false
				end
			end)))
	end
	if not bTakeBack then
		self.btSwitch:setTouchEnabled(false)
	else
		self.btSwitch:setTouchEnabled(true)
	end
end

function GameViewLayer:gameCallBanker(callBankerViewId, bFirstTimes)
	if callBankerViewId == cmd.MY_VIEWID then
        self.btCallBanker:setVisible(true)
        self.btCancel:setVisible(true)
    end

    if bFirstTimes then
		display.newSprite()
			:move(display.center)
			:addTo(self)
			:runAction(self:getAnimate("start", true))
    	AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_START.WAV")
    end
end

function GameViewLayer:gameStart(bankerViewId)
    if bankerViewId ~= cmd.MY_VIEWID then
        for i = 1, XIAZHU_NUM do
			self.chip_bg:setVisible(true)
            self.btChip[i]:setVisible(true)
        end
    end
end

function GameViewLayer:gameAddScore(viewId, score)

end

function GameViewLayer:gameSendCard(firstViewId, totalCount)
	--开始发牌
	--self.spriteCardBG:setVisible(true)
	self.bCanNextReplay = false
	self:runSendCardAnimate(firstViewId, totalCount)
	self.game_is_start = true
end

--开牌
function GameViewLayer:gameOpenCard()

	self.spritePrompt:hide()
	self.btOk:setVisible(false)
	self.btStart:hide()
	--self.btYqhy:hide()
	self.btCancel:setVisible(false)
	self.panel_btn:hide()
	

	-- 先把翻开的牌再翻转
	for i = 1, cmd.GAME_CARD_NUM do
		local card = self.compare_bg[cmd.MY_VIEWID]:child('card_' .. i)
        local png = string.format('card/card403.png')
		card:texture(png)
	end

	for i = 1, cmd.GAME_PLAYER do
		--local ViewID = self._scene:SwitchViewChairID(i - 1)
		for j = 1, cmd.GAME_CARD_NUM do
			self.nodeCard[i][j]:hide()
		end
		self.flag_ready[i]:hide()
		if self._scene.cbUseSpecialType[i] == 1 then
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			if ViewID ~= yl.INVALID_CHAIR then
				self.special_flag[ViewID]:hide()
			end
			
		end
	end

	for i = 1, cmd.GAME_PLAYER do
		for j = 1, 4 do
			local img_type = self.compare_bg[i]:child('card_type_' .. j)
			local txt = self.compare_bg[i]:child('txt_score_' .. j)
			if img_type then
				img_type:hide()
			end
			if txt then
				txt:hide()
			end
		end
	end


	for i = 1, cmd.GAME_PLAYER do repeat
		if self.gamePlayStatus[i] == 0 then
			break
		end
		for j = 1, cmd.GAME_CARD_NUM do repeat 
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			--if self._scene.cbUseSpecialType[i] == 0 then
				--self.nodeCard[ViewID][j]:hide()
			--else
			if ViewID == yl.INVALID_CHAIR then
				break
			end
				self.nodeCard[ViewID][j]:hide()
				
				local card = self.nodeCard[ViewID][j]

				local data = self._scene.cbCardData[i][j]
				local value = GameLogic:getCardValue(data)
        		local color = GameLogic:getCardColor(data)
				local png = string.format('card/card%d%02d.png', color, value)
				card:texture(png)
				--card:removeChildByName('ma_card_sign')
				--self:addMaCardSign(card, value, color)
				--print('png is ', png)
			--end
		until true
		end
		if self._scene.cbUseSpecialType[i] == 1 then
			local type = self._scene.cbSpeicalType[i] + GameLogic.OX_VALUEO
			
			local path = self:getSpeicalTypePngPath(type)
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			if ViewID ~= yl.INVALID_CHAIR then
				self.special_flag[ViewID]:show()
				self.special_flag[ViewID]:texture(path)
			end
			
		end
		until true
	end
	for i = 1, cmd.GAME_PLAYER do repeat
		if self.gamePlayStatus[i] == 0 then
			break
		end

		print('... cur player status is ', self.gamePlayStatus[i])
		--if self._scene.cbPlayStatus[i] == 1 then
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			print('... cur player status is ', ViewID)
			--if self._scene.cbUseSpecialType[i] == 1 then
			--	self.compare_bg[ViewID]:hide()
				
			--else
			if ViewID ~= yl.INVALID_CHAIR then
				self.compare_bg[ViewID]:show()
				--end
				self.score_bg[ViewID]:hide()
			end
			
			
		--end
	until true
	end
	self.compare_start_index = 1
	self.is_play_music = false
	self.compare_dao_index = 1
	self.comapre_user_index = 1
	self.quanda_user = {}
	self:setTxtScore4()
	helper.music.playPeiyin( self._scene.wPeiyin[cmd.MY_VIEWID], 'kaishibipai')
	self.is_running_action = true
	--self:runAction(cc.Sequence:create(cc.DelayTime:create(0.5), cc.CallFunc:create( handler(self, self.GameCompareCard))))
	self:runAction(cc.Sequence:create(cc.DelayTime:create(1.0),
		cc.CallFunc:create( handler(self, self.showSpecialTypeCard)),
		cc.CallFunc:create( handler(self, self.CompareCard))))
end

function GameViewLayer:showSpecialTypeCard()
	for i = 1, self.player_num do repeat
		local ViewID = self._scene:SwitchViewChairID(i - 1)
		if ViewID == yl.INVALID_CHAIR then
			break
		end
		if self._scene.cbSpeicalType[i] > 0 then
			for j = 1, cmd.GAME_CARD_NUM do 
				local card = self.compare_bg[ViewID]:child('card_' .. j)
				local data = self._scene.cbCardData[i][j]
				local value = GameLogic:getCardValue(data)
				local color = GameLogic:getCardColor(data)
				local png = string.format('card/card%d%02d.png', color, value)
				card:texture(png)	
				self:addMaCardSign(card, value, color)			
			end
		end
	until true
    end
end


function GameViewLayer:getSpeicalTypePngPath(type)
	local path = ''
	if type == GameLogic.OX_SANQING then
		path = 'word/t_sanqing.png'
	elseif type == GameLogic.OX_QUANHEI then
		path = 'word/t_quanhei.png'
	elseif type == GameLogic.OX_QUANHONG then
		path = 'word/t_quanhong.png'
	elseif type == GameLogic.OX_QUANSHUNZI then
		path = 'word/t_quanshun.png'
	elseif type == GameLogic.OX_SHUANGSHUNQING then
		path = 'word/t_shuangshunqing.png'
	elseif type == GameLogic.OX_SHUANGSANTIAO then
		path = 'word/t_shuangsantiao.png'
	elseif type == GameLogic.OX_SITIAO then
		path = 'word/t_sitiao.png'
	elseif type == GameLogic.OX_QUANSHUNQING then
		path = 'word/t_quanshunqing.png'
	elseif type == GameLogic.OX_SHUANGSITIAO then
		path = 'word/t_shuangsitiao.png'
	elseif type == GameLogic.OX_QUANSANTIAO then
		path = 'word/t_quansantiao.png'
	end
	return path
end

-------------------------------------------------------------------------------
-- 重新设置桌布
-------------------------------------------------------------------------------
function GameViewLayer:resetTablecloth()
    if GlobalUserItem.nTablecloth >= 0 and GlobalUserItem.nTablecloth <= 1 then
        self.main_node:child('bg'):texture('room/bg_room' .. GlobalUserItem.nTablecloth .. '.jpg')
    end
end

function GameViewLayer:onSettingChange()
	self:resetTablecloth()
--[[
	local scale = GlobalUserItem.nCardFontScale / 100
    local reset = function(container)
        local children = container:getChildren()
        for i, card in ipairs(children) do
            local font = card:getChildByTag(1)
            if font then
                font:scale(scale)
            end
        end
    end
	for i = 1, cmd.GAME_PLAYER do
	    reset( self.nodeDiscard[i] )
	    reset( self.nodeBpBgCard[i] )
	    reset( self.nodeHandCard[i] )
    end
	--]]

end

---設置第四道的分數
function GameViewLayer:setTxtScore4()

	for i = 1, self.player_num do repeat
		local ViewID = self._scene:SwitchViewChairID(i - 1) 
		if ViewID == yl.INVALID_CHAIR then
			break
		end

		local txt_show = self.compare_bg[ViewID]:child('txt_score_4')
		local score_total = 0
		for j = 1, 3 do
			score_total = score_total + self._scene.sandaoScore[i][j]
		end
		local tmp_score = score_total
		score_total = self._scene.lGameScore[i] - score_total
		print('4 score is ', tmp_score, score_total)
		if score_total > 0 then
			txt_show:setProperty('/' .. score_total, 'room/n1.png', 21, 31, '/')
		else
			score_total = -score_total
			txt_show:setProperty('/' .. score_total, 'room/n2.png', 21, 31, '/')
		end
	until true
	end
	
end

function GameViewLayer:createAction()
	for i = 1, self.player_num do repeat
		local ViewID = self._scene:SwitchViewChairID(i - 1) 
		if ViewID == yl.INVALID_CHAIR then
			break
		end
		local result_score = 0 
		for j = 1, 3 do
			result_score =  result_score + self._scene.sandaoScore[i][1]
		end
		local is_has_daqiang = false
		local is_quanda = true
		local is_shu = true
		local daqiang_user = {}
		
		if self.player_num <= 2 then
			is_quanda = false
		end
		local voice_file = nil
		print('is_quanda ', is_quanda, is_has_daqiang)
		
	until true
	end

	local func_daqiang = function ()
		if #self.quanda_user >= 1 then
			print('quan da .....')
			self:showQuanDa(self.quanda_user[1])
		else
			print('dan da .....')
			self:callDaQiang()
		end
	end
	self:runAction(cc.CallFunc:create(func_daqiang))
end

function GameViewLayer:CompareCard()
	if self.compare_dao_index == 4 then
		self:createAction()
		return
	end
	
	local cur_user_index = self._scene.sort_head[self.comapre_user_index][1]
	if self.compare_dao_index == 2 then
		cur_user_index = self._scene.sort_mid[self.comapre_user_index][1]
	elseif self.compare_dao_index == 3 then
		cur_user_index = self._scene.sort_foot[self.comapre_user_index][1]
	end
	local ViewID = self._scene:SwitchViewChairID(cur_user_index - 1) 
	print('cur compare user is ', self.comapre_user_index, cur_user_index, ViewID)
	if ViewID == yl.INVALID_CHAIR then
		return
	end
	--[[
	if GlobalUserItem.bVoiceAble then
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/bipai.mp3")
	end
	--]]
	local action_time = 0.1
	local start_index = 1
	local end_index = 3
	if self.compare_dao_index == 2 then
		start_index = 4
		end_index = start_index + 2
	elseif self.compare_dao_index == 3 then
		start_index = 7
		end_index = start_index + 2
	end
	if self._scene.cbUseSpecialType[cur_user_index] == 1 then
		self:calcNextComapreIndex()
		self:CompareCard()
		return
	end
	for j = start_index, end_index do 
		local card = self.compare_bg[ViewID]:child('card_' .. j)
		local data = self._scene.cbCardData[cur_user_index][j]
		local value = GameLogic:getCardValue(data)
		local color = GameLogic:getCardColor(data)
		local png = string.format('card/card%d%02d.png', color, value)
		if self._scene.cbGiveup[cur_user_index] == 1 then
			png = 'card/card403.png'
		end
		--card:removeChildByName('ma_card_sign')
		card:runAction(cc.Sequence:create(cc.DelayTime:create((j - start_index) * action_time),
			cc.CallFunc:create(function() 
				card:texture(png)
				--self:addMaCardSign(card, value, color)
			end)
		))
		
	end

	local img_type = self.compare_bg[ViewID]:child('card_type_' .. self.compare_dao_index)
	local txt = self.compare_bg[ViewID]:child('txt_score_' .. self.compare_dao_index)
	local card_type = self._scene.sandaoType[cur_user_index][self.compare_dao_index]
	if self.compare_dao_index > 1 then
		local pre_img_type = self.compare_bg[ViewID]:child('card_type_' .. (self.compare_dao_index - 1))
		local pre_txt = self.compare_bg[ViewID]:child('txt_score_' .. (self.compare_dao_index - 1))
		pre_img_type:hide()
		--pre_txt:hide()
	end
	print('self._scene.cbUseSpecialType[cur_user_index] is ', cur_user_index, self._scene.cbUseSpecialType[cur_user_index])
	if self._scene.cbUseSpecialType[cur_user_index] == 1 then
		if img_type then
			img_type:hide()
		end
		txt:hide()
		
	else
		if self._scene.cbGiveup[cur_user_index] == 1 then
			img_type:texture('word/t_qipai.png')
		else
			self:showType(img_type, card_type)
			print('sound peiyin is ', self._scene.wPeiyin[ViewID], self.typeMapSound[card_type + 1], cur_user_index)
			helper.music.playPeiyin( self._scene.wPeiyin[ViewID], self.typeMapSound[card_type + 1])
		end
		

		local score = self._scene.sandaoScore[cur_user_index][self.compare_dao_index]
		if score > 0 then
			txt:setProperty('/' .. score, 'room/n1.png', 21, 31, '/')
		else
			score = -score
			txt:setProperty('/' .. score, 'room/n2.png', 21, 31, '/')
		end
		
	end
	
	self:runAction(cc.Sequence:create(
		cc.DelayTime:create(action_time * (end_index - start_index + 1)),
		cc.CallFunc:create(function () 
			if self._scene.cbUseSpecialType[cur_user_index] ~= 1 then
				if img_type then
					img_type:show()
				end
				txt:show()
			end
		end),
		cc.DelayTime:create(1.0),
		cc.CallFunc:create(handler(self, self.CompareCard))
	))

	self:calcNextComapreIndex()
	
end

function GameViewLayer:calcNextComapreIndex()
	if self.comapre_user_index < self.player_num then
		self.comapre_user_index = self.comapre_user_index + 1
	else
		self.compare_dao_index = self.compare_dao_index + 1
		self.comapre_user_index = 1
	end
end

function GameViewLayer:GameCompareCard()
	local end_index = 3
	local show_index = 1
	
	if self.compare_start_index ~= 1 then
		end_index = self.compare_start_index + 4
	end
	show_index = math.floor(end_index / 5) + 1
	if show_index == 4 then
		return
	else
		for i = 1, self.player_num do
			local ViewID = self._scene:SwitchViewChairID(i - 1) 
			if ViewID ~= yl.INVALID_CHAIR then
				self.score_bg[ViewID]:hide()
			end
		end
	end

	local action_time = 0.1
	
	for i = 1, self.player_num do repeat
	--[[
		if self._scene.cbUseSpecialType[i] == 1 then
			break
		end
		--]]
		local ViewID = self._scene:SwitchViewChairID(i - 1) 
		if ViewID == yl.INVALID_CHAIR then
			break
		end
		if GlobalUserItem.bVoiceAble then
			AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/bipai.mp3")
		end
		for j = self.compare_start_index, end_index do 
			print('compare bg ', ViewID, j, self.compare_bg[ViewID], self.player_num)
			local card = self.compare_bg[ViewID]:child('card_' .. j)
			card:runAction(cc.Sequence:create(
				cc.DelayTime:create((j - self.compare_start_index) * action_time),
				cc.CallFunc:create(function (ref) 
					local data = self._scene.cbCardData[i][j]
					local value = GameLogic:getCardValue(data)
        			local color = GameLogic:getCardColor(data)
					local png = string.format('card/card%d%02d.png', color, value)
					card:texture(png)
					self:addMaCardSign(card, value, color)
				end)
			))
		end

		if self._scene.cbUseSpecialType[i] == 1 then
			
			for j = 1, 4 do
				local img_type = self.compare_bg[ViewID]:child('card_type_' .. j)
				if img_type then
					img_type:hide()
				end
				local txt = self.compare_bg[ViewID]:child('txt_score_' .. j)
				txt:hide()
			end
			break
		end

		for j = 1, 3 do
			local img_type = self.compare_bg[ViewID]:child('card_type_' .. j)
			if show_index == j then
				img_type:show()
				self:showType(img_type, self._scene.sandaoType[i][show_index])
			else
				img_type:hide()
			end
		end
		local txt = self.compare_bg[ViewID]:child('txt_score_' .. show_index)
		local score = self._scene.sandaoScore[i][show_index]
		if score > 0 then
			txt:setProperty('/' .. score, 'room/n1.png', 21, 31, '/')
		else
			score = -score
			txt:setProperty('/' .. score, 'room/n2.png', 21, 31, '/')
		end
		if show_index == 3  then
			

			--if self._scene:GetMeChairID() + 1 == i then
			--self:getDaQiang()
			local result_score = 0 
			for j = 1, 3 do
				result_score =  result_score + self._scene.sandaoScore[i][1]
			end
			local is_has_daqiang = false
			local is_quanda = true
			local is_shu = true
			local daqiang_user = {}
			for j = 1, self.player_num do repeat
				if j == i then
					break
				end
				if self._scene.cbDaQiang[i][j] == 1 then
					is_has_daqiang = true 
					--break
					--table.insert(daqiang_user, {i, j})
				else
					is_quanda = false
					print('is_quanda ', is_quanda, i, j)
				end
			until true
			end
			if self.player_num <= 2 then
				is_quanda = false
			end
			local voice_file = nil
			print('is_quanda ', is_quanda, is_has_daqiang)
			if is_quanda then
				
				table.insert(self.quanda_user, i)
				
			elseif is_has_daqiang then
				--voice_file = GameViewLayer.RES_PATH.."sound/jiqiang.mp3"
				--[[
				if i == self.player_num then
					self.daqiang_ani_play = true
					local daqiang_user = self:getDaQiang()
					self:showDaQiang(daqiang_user)
				end
				--]]
				
			elseif result_score > 0 then
				voice_file = GameViewLayer.RES_PATH.."sound/danwin.mp3"
			elseif self._scene.all_shu[i] == 1 then
				voice_file = GameViewLayer.RES_PATH.."sound/shusanjia.mp3"
			end
			if voice_file and GlobalUserItem.bVoiceAble then
				--for i = 1, 3 do
				AudioEngine.playEffect(voice_file)
				--end
				--self.is_play_music = true
			end
			--end
		end
		
		txt:runAction(cc.Sequence:create(
			cc.DelayTime:create((end_index - self.compare_start_index + 1) * action_time),
			cc.CallFunc:create(function(ref)
				local x, y = ref:pos()
				ref:pos(x, y + 50)
			 end
			),
			cc.CallFunc:create(function(ref)
				ref:show()
			 end
			),
			cc.MoveBy:create(0.5, cc.p(0, -50))
		))
		
	until true	
	end
	
	if show_index == 3 then
		local func_daqiang = function ()
			if #self.quanda_user >= 1 then
				print('quan da .....')
				self:showQuanDa(self.quanda_user[1])
			else
				print('dan da .....')
				self:callDaQiang()
			end
		end
		self:runAction(cc.Sequence:create(cc.DelayTime:create((end_index - self.compare_start_index + 1) * action_time + 0.5),
			cc.CallFunc:create(func_daqiang)
		))
	else
		self:runAction(cc.Sequence:create(cc.DelayTime:create((end_index - self.compare_start_index + 1) * action_time + 0.5),
			cc.CallFunc:create(function() self.compare_start_index = end_index + 1 end),
			cc.DelayTime:create(0.5),
			cc.CallFunc:create( handler(self, self.GameCompareCard))))
	
	end
	
end

function GameViewLayer:addMaCardSign( card, value, color )
	card:removeChildByName('ma_card_sign')
		
	if self._scene.maCardValue ~= -1 and self._scene.maCardValue == value and 
	color == 3  then
    	local image = ccui.ImageView:create()
    	image:texture('room/ma_card_sign.png')
		image:setName('ma_card_sign')
		local size = card:size()
		image:pos(20, size.height - 71)
    	card:addChild(image)
    end
end

function GameViewLayer:stopAllAction()
	if self.is_running_action then
		self:stop()
		local layer = helper.app.getFromScene('GameResultLayer')
		if layer then
			layer:show()
		end
		self.is_running_action = false
	end
end

function GameViewLayer:recoverUserScore(wViewChairId, lScore)
	self.tableScore[wViewChairId]:setString(lScore)
end


function GameViewLayer:showComparedEnd()
	print('self.player_num is ', self.player_num)
	for i = 1, self.player_num do
		local ViewID = self._scene:SwitchViewChairID(i - 1) 
		if ViewID ~= yl.INVALID_CHAIR then
			self.score_bg[ViewID]:show()
			local compare_bg = self.compare_bg[ViewID]
			local txt = compare_bg:child('txt_score_4')
			local x, y = txt:pos()
			txt:pos(x, y + 50)
			txt:show()
			txt:runAction(cc.MoveBy:create(0.5, cc.p(0, -50)))
		end
		
	end
	if not yl.IS_REPLAY_MODEL then
		local room_data = PassRoom:getInstance().m_tabPriData
		local count = self._scene.jushu
    	local limit = room_data.nPlayCount
		if count >= limit then
			self.btJieSuan:show()
			self.btStart:hide()
		else
			self.btStart:show()
		end
	end
	local layer = helper.app.getFromScene('GameResultLayer')
	if layer then
		layer:show()
	end
	self.is_running_action = false
	
	--[[
	if GlobalUserItem.bVoiceAble then
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/bipai.mp3")
	end
	--]]
	--self.btYqhy:show()
	return
end

function GameViewLayer:getDaQiang()
	local daqiang_user = {}
	return daqiang_user	
end

function GameViewLayer:getAniDirection(a, b)
	local direction = 0
	if a == 1 and b == 2 then
		direction = 3
	elseif a == 1 and b == 3 then
		direction = 2
	elseif a == 1 and b == 4 then
		direction = 1
	elseif a == 2 and b == 1 then
		direction = 0
	elseif a == 2 and b == 3 then
		direction = 2
	elseif a == 2 and b == 4 then
		direction = 1
	elseif a == 3 and b == 1 then
		direction = 0
	elseif a == 3 and b == 2 then
		direction = 3
	elseif a == 3 and b == 4 then
		direction = 1
	elseif a == 4 and b == 1 then
		direction = 0
	elseif a == 4 and b == 2 then
		direction = 3
	elseif a == 4 and b == 3 then
		direction = 2
	end

	return direction
end

function GameViewLayer:showQuanDa(user)
	local file_name = 'room/anim/allShoot/shoot'
	local a_v_id = self._scene:SwitchViewChairID(user - 1)
	if a_v_id == yl.INVALID_CHAIR then
		return
	end
	local size = self.compare_bg[a_v_id]:size()
	local x = size.width / 2
	local y = size.height / 2
	local callback = cc.RemoveSelf:create(true)
	local sprite = helper.app.createAnimation(file_name, 12, 1.2, true, callback, ccui.TextureResType.localType)
	x, y = self.compare_bg[a_v_id]:pos()
	sprite:pos(x + size.width / 2, y + size.height / 2 + 20)
	sprite:addTo(self.main_node):zorder(10)

	local x_dif = 20
	local y_dif = 20
	for i = 1, self.player_num do repeat
		if i == user then
			break
		end
		
		local b_v_id = self._scene:SwitchViewChairID(i - 1)
		if b_v_id == yl.INVALID_CHAIR then
			break
		end
		self.compare_bg[b_v_id]:show()
		for i = 1, 3 do
			local sprite = display.newSprite('room/qiangyan_1.png')
			local sprite_size = sprite:size()
			x = size.width / 2 + math.random(-x_dif, x_dif)
			y = size.height / 2 + math.random(-y_dif, y_dif)
			--x = math.random(sprite_size.width / 2, size.width - sprite_size.width / 2)
			--y = math.random(sprite_size.height / 2, size.width - sprite_size.height / 2)
			self.compare_bg[b_v_id]:addChild(sprite)
			sprite:pos(x, y)
			table.insert(self.qiangyan, sprite)
		end
	until true
	end
	local voice_file = GameViewLayer.RES_PATH.."sound/jiqiang.mp3"
	if voice_file and GlobalUserItem.bVoiceAble then
		AudioEngine.playEffect(voice_file)
	end

	local func = function()
		self:callDaQiang()
	end

	self:runAction(cc.Sequence:create(
		cc.DelayTime:create(1.2),
		cc.CallFunc:create(func)
	))

end

function GameViewLayer:callDaQiang()
	local daqiang_user = self:getDaQiang()
	if #daqiang_user >= 1 then
		self:showDaQiang(daqiang_user)
		return false
	end
	self:showComparedEnd()
	return true
end


function GameViewLayer:showDaQiang(daqiang_user)
	local frame_num = 12
	local frame_time = 0.1
	local action_time = frame_num * frame_time
	for i = 1, #daqiang_user do
		local func = function() 
			local single = daqiang_user[i]
			local a_u, b_u = single[1], single[2]
			--local a_u, b_u = 1, 2
			local a_v_id = self._scene:SwitchViewChairID(a_u - 1)
			local b_v_id = self._scene:SwitchViewChairID(b_u - 1)
			if a_v_id == nil or b_v_id == nil then
				return
			end

			local file_name = 'room/anim/Shoot' .. self:getAniDirection(a_v_id, b_v_id) .. '/shoot'
			print(file_name, a_v_id, b_v_id, a_u, b_u)
			local size = self.compare_bg[b_v_id]:size()
			local x = size.width / 2
			local y = size.height / 2
			local callback = cc.RemoveSelf:create(true)
			local sprite = helper.app.createAnimation(file_name, frame_num, action_time, true, callback, 	ccui.TextureResType.localType)
			x, y = self.compare_bg[a_v_id]:pos()
			sprite:pos(x + size.width / 2, y + size.height / 2)
			sprite:addTo(self.main_node):zorder(10)

			local x_dif = 20
			local y_dif = 20
			for i = 1, 3 do
				local sprite = display.newSprite('room/qiangyan_1.png')
				local sprite_size = sprite:size()
				x = size.width / 2 + math.random(-x_dif, x_dif)
				y = size.height / 2 + math.random(-y_dif, y_dif)
				--x = math.random(sprite_size.width / 2, size.width - sprite_size.width / 2)
				--y = math.random(sprite_size.height / 2, size.width - sprite_size.height / 2)
				self.compare_bg[b_v_id]:addChild(sprite)
				sprite:pos(x, y)
				table.insert(self.qiangyan, sprite)
			end
		end

		local play_func = function()
			local voice_file = GameViewLayer.RES_PATH.."sound/jiqiang.mp3"
			if voice_file and GlobalUserItem.bVoiceAble then
				--for i = 1, 3 do
				AudioEngine.playEffect(voice_file)
				--end
				--self.is_play_music = true
			end
			func()
		end
		
		local delay = 0
		if i > 1 then
			delay = 0.5
		end
		if i == #daqiang_user then
			self:runAction(cc.Sequence:create(
				cc.DelayTime:create(action_time * (i - 1)  + delay ),
				cc.CallFunc:create(play_func),
				cc.DelayTime:create(action_time),
				--cc.CallFunc:create(func),
				cc.CallFunc:create(function() self:showComparedEnd() end)
			))
		else
			self:runAction(cc.Sequence:create(
				cc.DelayTime:create(action_time * (i - 1) + delay),
				cc.CallFunc:create(play_func)))
		end
		
		
	end
end

function GameViewLayer:showType(node, type)
	--node:show()
	print('type is ', type + 1)
	node:texture(self.typeMapPng[type + 1])
end

function GameViewLayer:gameEnd(bMeWin)
	local name
	if bMeWin then
		name = "victory"
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WIN.WAV")
	else
		name = "lose"
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_LOST.WAV")
	end

    self.btStart:setVisible(true)
	--self.btYqhy:show()

	display.newSprite()
		:move(display.center)
		:addTo(self)
		:runAction(self:getAnimate(name, true))
end

function GameViewLayer:gameScenePlaying()
	local index = self._scene:GetMeChairID() + 1
	local status = self._scene.recoverStatus[index]
	self.btStart:hide()
	--self.btYqhy:hide()
	if status == 0 or status == 3 then
		self:onResetView()
		if not yl.IS_REPLAY_MODEL then
			self.btStart:show()
			--self.btYqhy:show()
		end
	elseif status == 1 then
		self.btOk:hide()
		self.btCancel:hide()
		self:runSendCardAnimate()
		
	elseif status == 2 then
		self.btOk:setVisible(false)
		self.btCancel:setVisible(false)
		self.spritePrompt:hide()
		self._scene:openCard(index - 1)
		self:showUserOperateStatus()
	end
end

function GameViewLayer:showUserOperateStatus()

	for i = 1, cmd.GAME_PLAYER do
		if self._scene.cbPlayStatus[i] == 1 then
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			local userItem = self._scene._gameFrame:getTableUserItem(self.table_id, i - 1)

			if self.flag_ready[ViewID] and userItem then
				if ViewID ~= cmd.MY_VIEWID then
					self.flag_ready[ViewID]:setVisible(true)
					if yl.US_READY == userItem.cbUserStatus then
						self.flag_ready[ViewID]:texture('word/yizhunbei.png')	
					else
						if ViewID == 2 or ViewID == 4 then
							self.flag_ready[ViewID]:texture('word/peipaiz02.png')
						else
							self.flag_ready[ViewID]:texture('word/peipaiz.png')
						end
						
						for j = 1, cmd.GAME_CARD_NUM do
							if self.nodeCard[ViewID][j] then
								self.nodeCard[ViewID][j]:show()
							end
						end
					end
				end
			end
		end
	end
end
--[[
function GameViewLayer:setCellScore(cellscore)
	if not cellscore then
		self.txt_CellScore:setString("底注：")
	else
		self.txt_CellScore:setString("底注："..cellscore)
	end
end
--]]

function GameViewLayer:setTableID(id)
	self.table_id = id
	if not id or id == yl.INVALID_TABLE then
		self.txt_TableID:setString(LANG.DESKTOP_NUM)
	else
		self.txt_TableID:setString(LANG{'DESKTOP_NUM_1', num = (id + 1) })
	end
end

function GameViewLayer:setCardTextureRect(viewId, tag, cardValue, cardColor)
	if viewId < 1 or viewId > 5 then
		print("card texture rect error!")
		return
	end
	
	local card = self.nodeCard[viewId][tag]
	local png = string.format('card/card%d%02d.png', cardColor, cardValue)
	
	card:texture(png)
	--card:removeChildByName('ma_card_sign')
	--self:addMaCardSign(card, cardValue, cardColor)
	--local rectCard = card:getTextureRect()
	--rectCard.x = rectCard.width*(cardValue - 1)
	--rectCard.y = rectCard.height*cardColor
	--card:setTextureRect(rectCard)
end

function GameViewLayer:setNickname(viewId, strName)
	local name = string.EllipsisByConfig(strName, 133, self.nicknameConfig)
	local labelNickname = self.nodePlayer[viewId]:getChildByTag(GameViewLayer.NICKNAME)
	labelNickname:setString(name)

	-- local labelWidth = labelNickname:getContentSize().width
	-- if labelWidth > 113 then
	-- 	labelNickname:setScaleX(113/labelWidth)
	-- elseif labelNickname:getScaleX() ~= 1 then
	-- 	labelNickname:setScaleX(1)
	-- end
end

function GameViewLayer:setScore(viewId, lScore)
	local labelScore = self.nodePlayer[viewId]:child('txt_difen')
	labelScore:setString(lScore)

	local labelWidth = labelScore:getContentSize().width
	if labelWidth > 98 then
		labelScore:setScaleX(98/labelWidth)
	elseif labelScore:getScaleX() ~= 1 then
		labelScore:setScaleX(1)
	end
end

function GameViewLayer:setCellScore(cellscore)
	-- body
	local txt_difen = self.main_node:child('txt_difen')
	txt_difen:setString(LANG{'DIFEN', lcellScore = cellscore})
end

function GameViewLayer:setUserScore(wViewChairId, lScore)
	self.nodePlayer[wViewChairId]:child('txt_difen'):setString(lScore)
end

function GameViewLayer:setReadyVisible(wViewChairId, isVisible)
	if self.flag_ready[wViewChairId] then
		self.flag_ready[wViewChairId]:setVisible(isVisible)
	end
end

function GameViewLayer:setSpecialTypeVisible(wViewChairId, type)
	if self.flag_ready[wViewChairId] then
		self.flag_ready[wViewChairId]:setVisible(isVisible)
	end
end

function GameViewLayer:setOpenCardGiveup( wViewChairId, isVisible )
	-- body

	self.flag_ready[wViewChairId]:setVisible(isVisible)
	if wViewChairId == 2 or wViewChairId == 4 then
		self.flag_ready[wViewChairId]:texture('word/qipai2.png')
	else
		self.flag_ready[wViewChairId]:texture('word/qipai1.png')
	end
end

function GameViewLayer:setOpenCardVisible(wViewChairId, isVisible)
	if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
		return
	end
	self.flag_ready[wViewChairId]:setVisible(isVisible)
	if wViewChairId == 2 or wViewChairId == 4 then
		self.flag_ready[wViewChairId]:texture('word/yiwanc02.png')
	else
		self.flag_ready[wViewChairId]:texture('word/complete.png')
	end
	
	

		--if not self.m_bNormalState[wViewChairId] then
	local head = self.nodePlayer[wViewChairId]:child('sp_head')
    head:removeChildByName('offline')
	self.m_bNormalState[wViewChairId] = true
		--end
				
end

function GameViewLayer:setTurnMaxScore(lTurnMaxScore)
	for i = 1, XIAZHU_NUM do
		--self.lUserMaxScore[i] = math.max(lTurnMaxScore, 1)
		self.btChip[i]:getChildByTag(GameViewLayer.CHIPNUM):setString(self.lUserMaxScore[i])
		lTurnMaxScore = math.floor(lTurnMaxScore/2)
	end
end

function GameViewLayer:setBankerUser(wViewChairId)
	if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
		return
	end
	
	local fangzhu = self.nodePlayer[wViewChairId]:child('fangzhu')
	--[[
	local mv_pos = fangzhu:convertToWorldSpace(cc.p(fangzhu:pos()))
	self.spriteBankerFlag:move(mv_pos.x, mv_pos.y)
	self.spriteBankerFlag:setVisible(true)
	self.spriteBankerFlag:runAction(self:getAnimate("banker"))
--]]
	local img = self.nodePlayer[wViewChairId]:child('img_zhuang')
	print('bank user is ', wViewChairId, self.nodePlayer[wViewChairId], img)
	if img then
		img:setVisible(true)
		img:setLocalZOrder(10)
	end
	
	--print('viewID , self.bank_user', wViewChairId, self.bank_user, is_zhuang)

	----闪烁动画
	-- display.newSprite()
	-- 	:move(pointPlayer[wViewChairId].x + 2, pointPlayer[wViewChairId].y - 12)
	-- 	:addTo(self)
	-- 	:runAction(self:getAnimate("faceFlash", true))
end

function GameViewLayer:setUserTableScore(wViewChairId, lScore)
	if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
		return
	end
	local cur_fen = tonumber(self.tableScore[wViewChairId]:getString())
	self.tableScore[wViewChairId]:setString(cur_fen + lScore)
	local txt_score = self.score_bg[wViewChairId]:child('txt_score')
	txt_score:setString(lScore)
	self.score_bg[wViewChairId]:hide()
	--self.tableScore[wViewChairId]:setVisible(true)
end

function GameViewLayer:showSpecialDialog(index)
	if self.specail_node then
		self.specail_node:removeFromParent()
		self.specail_node = nil
	end
	local lang_code = 'SPECIAL_CARD_TYPE_' .. self._scene.cbSpecailType[index]
	local csb_node = helper.app.loadCSB('SpecialTypeCard.csb')
	self.specail_node = csb_node
	self:addChild(csb_node)

	local panel = csb_node:child('panel')
	local txt_card_type = panel:child('txt_card_type')
	print('lang_code is ', lang_code, LANG[lang_code], LANG{'SPECIAL_WIN_DAO', win_dao=self._scene.cbSpecailWinDao[index]})
	txt_card_type:setString(LANG[lang_code])

	local bg = panel:child('bg')
	local img_cancel = bg:child('img_cancel')
	img_cancel:addTouchEventListener(handler(self, self.CancelSpecialType))

	local img_cancel = bg:child('img_ok')
	img_cancel:addTouchEventListener(handler(self, self.OkSpecialType))
end

--发牌动作
function GameViewLayer:runSendCardAnimate(wViewChairId, nCount)
	local run_action_time = 0.1
	for i = 1, cmd.GAME_PLAYER do repeat
		if self._scene.cbPlayStatus[i] == 1 then
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			if ViewID == yl.INVALID_CHAIR then
				break
			end
			for j = 1, cmd.GAME_CARD_NUM do
				self.nodeCard[ViewID][j]:runAction(
					cc.Sequence:create(
						cc.DelayTime:create(j * run_action_time),
						cc.CallFunc:create( function(ref)
							ref:show()
						end
						)
					)
				)
			end
		end
		until true
	end

	if not yl.IS_REPLAY_MODEL then
		self:runAction(cc.Sequence:create(
			cc.DelayTime:create( (cmd.GAME_CARD_NUM + 1)* run_action_time),
			cc.CallFunc:create( function() 
				local index = self._scene:GetMeChairID() + 1
				self.bCanMoveCard = true
				for i = 1, cmd.GAME_PLAYER do
					if self._scene.cbPlayStatus[i] == 1 then
						local ViewID = self._scene:SwitchViewChairID(i - 1)
						if ViewID ~= cmd.MY_VIEWID and ViewID ~= yl.INVALID_CHAIR then
							self.flag_ready[ViewID]:setVisible(true)
							if ViewID == 2 or ViewID == 4 then
								self.flag_ready[ViewID]:texture('word/peipaiz02.png')
							else
								self.flag_ready[ViewID]:texture('word/peipaiz.png')
							end
						end
						
					end
				end

				self._scene:SetGameClock(self._scene:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
				

				if self._scene.cbSpecailType[index] and self._scene.cbSpecailType[index] > 0 then
					self:showSpecialDialog(index)
				end
			end)
		))
	else
		self:runAction(cc.Sequence:create(
			cc.DelayTime:create( (cmd.GAME_CARD_NUM + 1)* run_action_time),
			cc.CallFunc:create( function() 
				self.bCanNextReplay = true
			end)))
	end

	

	if GlobalUserItem.bVoiceAble then
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/fapai.mp3")
	end
	if not yl.IS_REPLAY_MODEL then
		self.panel_btn:show()
		--self.btOk:show()
		--self.btCancel:show()
		self.spritePrompt:show()
	end
end

function GameViewLayer:CancelSpecialType(ref, type)
	 if type == ccui.TouchEventType.ended then
	 	if self.specail_node then
		 	self.specail_node:removeFromParent()
			self.specail_node = nil	
		end
		
    end
end

function GameViewLayer:OkSpecialType(ref, type)
	 if type == ccui.TouchEventType.ended then
		self.specail_node:removeFromParent()
		self.specail_node = nil
		self.is_show_special_type = true
		self.bCanMoveCard = false
		self.spritePrompt:hide()
		self.panel_btn:hide()
		for i = 1, cmd.GAME_CARD_NUM do
			self.nodeCard[cmd.MY_VIEWID][i]:hide()
		end
		self._scene:onOpenCard(1)
    end
end

function GameViewLayer:showBtn(type, is_show)
	print('btn tag is ... ', type, is_show)
	local btn = self.panel_btn:getChildByTag(type)
	--btn:setBright(is_show)
	if not btn then
		return 
	end
	if is_show then
		btn:texture('room/paixingann.png')
	else
		btn:texture('room/paixingann02.png')
	end
end

--检查牌类型
function GameViewLayer:updateCardPrompt()
	--弹出牌显示，统计和
	local nSumTotal = 0
	local nSumOut = 0
	local nCount = 1
	for i = 1, 5 do
		local nCardValue = self._scene:getMeCardLogicValue(i)
		nSumTotal = nSumTotal + nCardValue
		if self.bCardOut[i] then
	 		if nCount <= 3 then
	 			self.labAtCardPrompt[nCount]:setString(nCardValue)
	 		end
	 		nCount = nCount + 1
			nSumOut = nSumOut + nCardValue
		end
	end
end

function GameViewLayer:preloadUI()
	--display.loadSpriteFrames(GameViewLayer.RES_PATH.."game_oxex_res.plist",
	--						GameViewLayer.RES_PATH.."game_oxex_res.png")

	--[[
	for i = 1, #AnimationRes do
		local animation = cc.Animation:create()
		animation:setDelayPerUnit(AnimationRes[i].fInterval)
		animation:setLoops(AnimationRes[i].nLoops)

		for j = 1, AnimationRes[i].nCount do
			local strFile = AnimationRes[i].file..string.format("%d.png", j)
			animation:addSpriteFrameWithFile(strFile)
		end

		cc.AnimationCache:getInstance():addAnimation(animation, AnimationRes[i].name)
	end
	--]]
end

function GameViewLayer:getAnimate(name, bEndRemove)
	local animation = cc.AnimationCache:getInstance():getAnimation(name)
	local animate = cc.Animate:create(animation)

	if bEndRemove then
		animate = cc.Sequence:create(animate, cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end))
	end

	return animate
end

function GameViewLayer:promptOx()
	--首先将牌复位
	for i = 1, cmd.GAME_CARD_NUM do
		if self.bCardOut[i] == true then
			local card = self.nodeCard[cmd.MY_VIEWID][i]
			local x, y = card:getPosition()
			y = y - 30
			card:move(x, y)
			self.bCardOut[i] = false
		end
	end
	--将牛牌弹出
	local index = self._scene:GetMeChairID() + 1
	local cbDataTemp = self:copyTab(self._scene.cbCardData[index])
	if self._scene:getOxCard(cbDataTemp) then
		for i = 1, 5 do
			for j = 1, 3 do
				if self._scene.cbCardData[index][i] == cbDataTemp[j] then
					local card = self.nodeCard[cmd.MY_VIEWID][i]
					local x, y = card:getPosition()
					y = y + 30
					card:move(x, y)
					self.bCardOut[i] = true
				end
			end
		end
	end
	--self:updateCardPrompt()
end

--拷贝表
function GameViewLayer:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
 end

--取模
function GameViewLayer:mod(a,b)
    return a - math.floor(a/b)*b
end

--运行输赢动画
function GameViewLayer:runWinLoseAnimate(viewid, score)
	local strAnimate
	local strSymbol
	local strNum
	--[[
	if score > 0 then
		
		strSymbol = GameViewLayer.RES_PATH.."symbol_add.png"
		strNum = GameViewLayer.RES_PATH.."num_add.png"
	else
		score = -score
		
		strSymbol = GameViewLayer.RES_PATH.."symbol_reduce.png"
		strNum = GameViewLayer.RES_PATH.."num_reduce.png"
	end

	--加减
	local node = cc.Node:create()
		:move(self.nodeCardPos[viewid])
		:setAnchorPoint(cc.p(0.5, 0.5))
		:setOpacity(0)
		:setCascadeOpacityEnabled(true)
		:addTo(self, 4)

	local spriteSymbol = display.newSprite(strSymbol)		--符号
		:addTo(node)
	local sizeSymbol = spriteSymbol:getContentSize()
	spriteSymbol:move(sizeSymbol.width/2, sizeSymbol.height/2)
--]]
	local x, y = self.nodePlayer[viewid]:pos()
	local size = self.nodePlayer[viewid]:size()
	y = y - size.height / 2
	local word_pos = self.nodePlayer[viewid]:getParent():convertToWorldSpace(cc.p(x, y))
	local node = cc.Node:create()
		:move(word_pos)
		:setAnchorPoint(cc.p(0.5, 0.5))
		:setOpacity(0)
		:setCascadeOpacityEnabled(true)
		:addTo(self, 4)

	local is_win = false
	local png_path = 'room/n1.png'
	local start_char = '/'
	if score > 0 then
		score = '/' .. tostring(score)
		strAnimate = "yellow"
		is_win = true
	else
		if score == 0 then
			score = tostring(score)
		else
			score = '/' .. tostring(-score)
		end
		
		strAnimate = "blue"
		png_path = 'room/n2.png'
		start_char = '/'
	end

--[[
	for i = 1, self._scene:getPlayNum() do
		if i == self._scene:GetMeChairID() then
			self.cur_score[i] = self.cur_score[i] + score
		else
			self.cur_score[i] = self.cur_score[i] - score
		end
	end
	--]]
	local labAtNum = cc.LabelAtlas:_create(score, png_path, 21, 31, string.byte(start_char))		--数字
		:setAnchorPoint(cc.p(0.5, 0.5))
		:addTo(node)
	local sizeNum = labAtNum:getContentSize()
	
	--local num_pos = node:convertToNodeSpace(word_pos)
	--labAtNum:move(num_pos)
	--labAtNum:move(self.nodeCardPos[viewid])

	--node:setContentSize(sizeNum.width, sizeNum.height)

	--底部动画
	local nTime = 1.5
	local spriteAnimate = display.newSprite()
		:move(self.nodeCardPos[viewid])
		:addTo(self, 3)
	spriteAnimate:runAction(cc.Sequence:create(
		cc.Spawn:create(
			cc.MoveBy:create(nTime, cc.p(0, 100)),
			self:getAnimate(strAnimate)
		),
		cc.DelayTime:create(2),
		cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end)
	))

	node:runAction(cc.Sequence:create(
		cc.Spawn:create(
			cc.MoveBy:create(nTime, cc.p(0, 100)), 
			cc.FadeIn:create(nTime)
		),
		cc.DelayTime:create(2))
	)
	table.insert(self.runNode, node)
end


return GameViewLayer
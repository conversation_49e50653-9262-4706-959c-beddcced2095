if nil == cc.XMLHttpRequest then
    return
end

_G.kWebSocketScriptHandlerOpen  = cc.WEBSOCKET_OPEN
_G.kWebSocketScriptHandlerMessage = cc.WEBSOCKET_MESSAGE
_G.kWebSocketScriptHandlerClose   = cc.WEBSOCKET_CLOSE
_G.kWebSocketScriptHandlerError   = cc.WEBSOCKET_ERROR

_G.kStateConnecting               = cc.WEBSOCKET_STATE_CONNECTING 
_G.kStateOpen                     = cc.WEBSOCKET_STATE_OPEN 
_G.kStateClosing                  = cc.WEBSOCKET_STATE_CLOSING
_G.kStateClosed                   = cc.WEBSOCKET_STATE_CLOSED

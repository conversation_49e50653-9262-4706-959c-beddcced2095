@echo off
echo ===== Apache 端口冲突解决脚本 =====
echo.

echo 1. 查找占用端口 443 的进程...
netstat -ano | findstr :443
echo.

echo 2. 查找占用端口 80 的进程...
netstat -ano | findstr :80
echo.

echo 3. 显示进程详细信息...
echo 请查看上面的 PID，然后手动执行以下命令之一：
echo.
echo 查看进程详情：
echo tasklist | findstr "PID号"
echo.
echo 强制结束进程（请谨慎使用）：
echo taskkill /PID PID号 /F
echo.

echo 4. 检查常见的端口占用程序...
echo 检查 IIS...
sc query W3SVC
echo.
echo 检查 Skype...
tasklist | findstr skype
echo.
echo 检查其他 Web 服务器...
tasklist | findstr nginx
tasklist | findstr httpd
echo.

echo 5. 建议的解决步骤：
echo a) 停止占用端口的服务
echo b) 重启 Apache
echo c) 检查防火墙设置
echo d) 确认 Apache 配置文件正确
echo.

pause

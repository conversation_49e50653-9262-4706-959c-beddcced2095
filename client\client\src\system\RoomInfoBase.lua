-------------------------------------------------------------------------------
--  创世版1.0
--  私人房游戏顶层
--  @date 2017-06-15
--  @auth woodoo
-------------------------------------------------------------------------------
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")


local ACTION_TAG_BTN_DISMISS = 2341


local RoomInfoBase = class('RoomInfoBase', cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomInfoBase:ctor( game_layer )
    print('RoomInfoBase:ctor...')
    self:enableNodeEvents()
    self.m_game_layer = game_layer
    self.m_rule_arr = {}
    self.m_cbShengPaiNum = 0

    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomInfoLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    self.m_label_time = main_node:child('label_time')

    main_node:child('label_version'):setString( appdf.app:getVersion() )
    if main_node:child('btn_dismiss') then
        if GlobalUserItem.bPrivateRoom or GlobalUserItem.bCommonGold then  -- 私人房或普通金币场
            main_node:child('btn_dismiss'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnDismiss) )
        else
            main_node:child('btn_dismiss'):removeFromParent()
        end
    end
    if main_node:child('btn_invite') then
        self.m_panel_shares = main_node:child('panel_shares'):hide()
        local share_bg = self.m_panel_shares:child('bg')
        self.m_panel_shares:addTouchEventListener( handler(self, function() self.m_panel_shares:hide() end) )

        main_node:child('btn_invite'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnInvite) )
        share_bg:child('btn_wechat'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShareTypes) )
        share_bg:child('btn_copy'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShareTypes) )
        share_bg:child('btn_alipay'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShareTypes) )

        -- 暂时屏蔽支付宝
        share_bg:child('btn_alipay'):hide()
        share_bg:child('btn_copy'):pos(share_bg:child('btn_alipay'):pos())

        share_bg:child('btn_wechat').kind = 'wechat'
        share_bg:child('btn_copy').kind = 'copy'
        share_bg:child('btn_alipay').kind = 'alipay'
    end
    if main_node:child('img_continue') then
        main_node:child('img_continue'):hide()
    end

    -- 时间定时器
    self:perform( handler(self, self.udpateTime), 1, -1 )

    self:setRemainCardNum(0)
    self:onRefreshInfo()
    self:setShengPaiVisible(false)

    self:goldRoomProcess()
    if main_node:child('btn_back_exit') then
        main_node:child('btn_back_exit'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBackExit) )
        main_node:child('btn_back_exit'):hide()
    end
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function RoomInfoBase:onEnter()
    print('RoomInfoBase:onEnter...')
    helper.app.setBatteryCallback( handler(self, self.onBatteryNotify) )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RoomInfoBase:onExit()
    print('RoomInfoBase:onExit...')
    helper.app.setBatteryCallback(nil)
end


-------------------------------------------------------------------------------
-- 电池变化通知
-------------------------------------------------------------------------------
function RoomInfoBase:onBatteryNotify(percent)
    local bar = self.main_node:child('loading_battery')
    bar:setPercent(percent * 100)
    local rate = 255 * percent
	bar:setColor( cc.c3b(255 - rate, rate, 0) )
end


-------------------------------------------------------------------------------
-- 解散房间按钮点击
-------------------------------------------------------------------------------
function RoomInfoBase:onBtnDismiss(sender)
    self.m_game_layer:onQueryExitGame()
end


-------------------------------------------------------------------------------
-- 邀请好友按钮点击
-------------------------------------------------------------------------------
function RoomInfoBase:onBtnShareTypes(sender)
    local kind = sender.kind
    local url, title, desc = self:makeShareInfo()
    if kind == 'copy' then
        desc = title .. ' ' .. desc
    else
        desc = desc .. ' ' .. LANG.CLICK_JOIN
    end

    if kind == 'wechat' then
        helper.pop.shareLink(url, title, desc, 'word/font_title_invite.png'):showButtons('hy,mowang,xianliao,copy')
    elseif kind == 'alipay' then
        local callback = function(isok)
            if isok == 'true' then
                self:perform(function()
                    helper.pop.message( LANG.SHARE_SUCCESS )
                end, 0.5)
            end
        end
        MultiPlatform:getInstance():shareToTarget(yl.ThirdParty.ALIPAY, callback, title, desc, url, "")
    elseif kind == 'copy' then
        MultiPlatform:getInstance():copyToClipboard(desc)
        helper.pop.message( LANG.SHARE_COPIED )
    end
    self.m_panel_shares:hide()
end


-------------------------------------------------------------------------------
-- 邀请好友按钮点击
-------------------------------------------------------------------------------
function RoomInfoBase:onBtnInvite(sender)
    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.dwClubID and room_data.dwClubID > 0 then
        helper.link.toClubInvite(room_data.dwClubID, room_data.dwClubRuleID, room_data.dwClubOwnerID, self.m_game_layer, 999)

    --[[
    elseif yl.IS_ALIPAY_SHARE then
        self.m_panel_shares:show()
        self.m_panel_shares:child('bg'):scale(0):runAction( cc.Sequence:create(
            cc.EaseBackOut:create(
                cc.ScaleTo:create(0.2, 1)
            )
        ) )
    --]]
    else
        self:onBtnShareTypes(self.m_panel_shares:child('bg/btn_wechat'))
    end
end


-------------------------------------------------------------------------------
-- 生成邀请信息
-------------------------------------------------------------------------------
function RoomInfoBase:makeShareInfo()
    return '', '', ''   -- 子类中实现
end


-------------------------------------------------------------------------------
-- 生成玩法字符串
-------------------------------------------------------------------------------
function RoomInfoBase:makeRuleStr()
    local config = cs.game[ GlobalUserItem.nCurGameKind ]
    local t = {}
    for i, v in ipairs(self.m_rule_arr) do
        if i > 2 then   -- 1是标志位
            local rule_key = 'RULE' .. (i - 3) .. (v == 1 and '' or '_NONE')
            local str = config[rule_key]
            if str then 
                t[#t + 1] = str
            end
        end
    end
    return table.concat(t, '/')
end


-------------------------------------------------------------------------------
-- 刷新界面
-------------------------------------------------------------------------------
function RoomInfoBase:onRefreshInfo()
    local room_data = PassRoom:getInstance().m_tabPriData
    local main_node = self.main_node

    -- 连开标记
    if main_node:child('img_continue') then
        main_node:child('img_continue'):hide()
    end

    -- 房间ID
    main_node:child('label_room'):setString( room_data.szServerID or '000000' )
    local config = cs.game[GlobalUserItem.nCurGameKind]

    -- 局数
    if main_node:child('label_jushu') then
        local count = room_data.dwPlayCount
        local limit = room_data.nPlayCount
        local str = ''
        if not limit then   -- 可能信息晚到，此时为nil
    
        elseif limit > 0 then
            str = LANG{'ROOM_JUSHU', count=count and count > 0 and ((count - 1) % limit + 1) or 0, limit=limit}
            if main_node:child('img_continue') and room_data.m_nContinue then
                if room_data.m_nContinue > 0 and (count - 1) % limit == (limit - 1) then -- 有连开标记，最后一局
                    if count + 1 < (room_data.m_nContinue + 1) * limit then
                        main_node:child('img_continue'):show()
                    end
                end
            end
        else
            if limit > -10000 then
                str = LANG{'ROOM_JUSHU_KUN', count = count}
                if config.DAKUN_DESC then
                   local s, flag = string.gsub( config.DAKUN_DESC, "$kun", -limit )   -- abpqefg 1
                   str = str .. s
                else
                   str = str .. LANG{'ROOM_KUN', kun = -limit}
                end
            else
                str = LANG{'ROOM_JUSHU_QUAN', count=count}
            end
        end
        local  quan_index = PassRoom:getInstance().m_tabPriData.cbCurPlayRoundIndex;
        if config.QUAN_FENG and quan_index then
            str = str .. '  '.. LANG['ROOM_QUAN_FENG_POS'][quan_index + 1] 
        end
        main_node:child('label_jushu'):setString( str )
    end

    -- 底分
    if main_node:child('label_difen') then
        local difen = room_data.lCellScore
        main_node:child('label_difen'):setString( LANG{'ROOM_DIFEN', value=difen} )
    end

    -- 规则
    local label_rule = main_node:child('label_rule')
    label_rule:setString('')
    local desc = ''
    local name = config.NAME
    desc = desc .. name .. ': '
    if room_data.cbGameRule then
        self.m_rule_arr = room_data.cbGameRule[1]
        desc = desc .. self:makeRuleStr()
    end
    local jushu = room_data.nPlayCount
    if jushu then
        if jushu < 0 then
            if jushu > -10000 then
                if config.DAKUN_DESC then
                   local s, flag = string.gsub( config.DAKUN_DESC, "$kun", -jushu )   -- abpqefg 1
                   jushu = s
                else
                   jushu = LANG{'ROOM_KUN', kun = -jushu}
                end
            else
                jushu = LANG{'ROOM_QUAN', kun=-(jushu + 10000)}
            end
            desc = desc .. ' ' .. jushu
        end
    end

    if GlobalUserItem.bPrivateRoom then
        local cost = room_data.nFee
        local pay_type = room_data.cbPayType
        if cost and pay_type then
            local zhifu = LANG{'CREATE_ZHIFU_'..pay_type, fangka = cost}
            desc = desc .. ' ' .. zhifu
        end
        --dump(room_data, 'room_data', 9)
        --print('pay_type........', room_data, pay_type, cost)
    end

    if self.makeRenShu then
        desc = desc .. self:makeRenShu()
    end

    label_rule:setString(desc)

    -- 规则文字和背景适配
    local bg_rule = main_node:child('bg_rule')
    local bg_size = bg_rule:size()
    local label_size = label_rule:size()
    local max_width = 670
    if label_size.width > max_width then
        label_rule:scale(max_width / label_size.width)
        label_size.width = max_width
    end
    bg_rule:size(math.max(560, label_size.width + 20), bg_size.height)

    self:onRefreshInviteBtn()
end


-------------------------------------------------------------------------------
-- 刷新邀请按钮状态
-------------------------------------------------------------------------------
function RoomInfoBase:onRefreshInviteBtn()
    local label_remain, label_shengpai = self.main_node:child('label_remain, label_shengpai')
    local btn_invite = self.main_node:child('btn_invite')
    local btn_dismiss = self.main_node:child('btn_dismiss')
    if self.m_game_layer.m_cbGameStatus and self.m_game_layer.m_cbGameStatus > 0 then -- 不是空闲场景
        if btn_invite then
            btn_invite:hide()
        end
        if btn_dismiss then
            btn_dismiss:stop(ACTION_TAG_BTN_DISMISS)
            if self.m_game_layer.m_cbGameStatus == cmd.GAME_SCENE_WAITING and GlobalUserItem.bCommonGold then -- 普遍金币场
                btn_dismiss:perform(function()
                    btn_dismiss:show()  -- 金币场在等待时需要显示退出按钮
                end, 2, nil, ACTION_TAG_BTN_DISMISS)
            else
                btn_dismiss:hide()
            end
        end
        label_remain:show()
        return
    end
    label_shengpai:hide()
    label_remain:hide()

    if btn_invite then
        btn_invite:setVisible(PassRoom:getInstance().m_bIsMyRoomOwner) -- 只有房主可以邀请
    end

    if btn_dismiss then
        if PassRoom:getInstance().m_bIsMyRoomOwner then
            btn_dismiss:child('font_dismiss'):show()    -- 房主用“解散包厢”
            btn_dismiss:child('font_leave'):hide()
        else
            btn_dismiss:child('font_dismiss'):hide()    -- 非房主用“退出房间”
            btn_dismiss:child('font_leave'):show()
        end
        btn_dismiss:show()
    end

    self:goldRoomProcess()
end


-------------------------------------------------------------------------------
-- 金币场处理
-------------------------------------------------------------------------------
function RoomInfoBase:goldRoomProcess(num)
    if not GlobalUserItem.bPrivateRoom then
        self.main_node:child('font_room_no_pre'):hide()
        self.main_node:child('bg_room_no'):hide()
        self.main_node:child('label_room'):hide()
        if self.main_node:child('btn_invite') then
            self.main_node:child('btn_invite'):hide()
        end
        if self.main_node:child('label_jushu') and GlobalUserItem.bCommonGold then
            self.main_node:child('label_jushu'):hide()
        end
        if self.main_node:child('label_difen') then
            local label_difen = self.main_node:child('label_difen')
            label_difen:setTextColor(display.COLOR_WHITE)
            label_difen:pos(self.main_node:child('label_room'):pos())
            label_difen:setFontSize(self.main_node:child('label_room'):getFontSize())
        end
    end
end


-------------------------------------------------------------------------------
-- 设置剩余牌，外部调用
-------------------------------------------------------------------------------
function RoomInfoBase:setRemainCardNum( num )
	local label_remain = self.main_node:child('label_remain')
	label_remain:setString( num > 0 and LANG{'REMAIN_CARD_NUM', num=num} or '')
end

function RoomInfoBase:setShengPaiVisible( visible )
    --print('self.设置剩余牌...............', self.m_cbShengPaiNum)
    local label_shengpai = self.main_node:child('label_shengpai')
    if visible then
        local game_config = cs.game[GlobalUserItem.nCurGameKind]
        if game_config.SHENGPAI_DESC then
            label_shengpai:setString(game_config.SHENGPAI_DESC)
        end
        label_shengpai:show()
    else
        label_shengpai:hide()
    end
end
-------------------------------------------------------------------------------
-- 设置改变
-------------------------------------------------------------------------------
function RoomInfoBase:onSettingChange()
    -- 子类各自实现
end


-------------------------------------------------------------------------------
-- 更新时间
-------------------------------------------------------------------------------
function RoomInfoBase:udpateTime()
    self.m_label_time:setString( helper.time.format(nil, '%H:%M') )
end


-------------------------------------------------------------------------------
-- 私人房游戏结束，显示大结算
-------------------------------------------------------------------------------
function RoomInfoBase:onPriGameEnd( cmd_table )
    --print('私人房游戏结束，显示大结算')
    if cs.game.IS_RECIVE_GAME_RESULT ~= nil then
        cs.game.IS_RECIVE_GAME_RESULT = true
    end

    local layer = helper.app.getFromScene('subRoomResultLayer')
    if layer then
        layer:removeFromParent()
    end

    layer = helper.pop.popLayer(cs.game.SRC .. 'RoomResultLayer', nil, {self.m_game_layer, cmd_table})
    local game_result_layer = helper.app.getFromScene('subGameResultLayer')
    if cs.game.HAS_GAME_RESULT and (cmd_table.cbGameStart == 1 or game_result_layer) then -- hide是等待小结算界面显示后开启，已开始一局或还有小结算界面
        layer:hide()
        --print('cs.game.IS_RECIVE_GAME_RESULT ', cs.game.IS_RECIVE_GAME_RESULT)
    end
    if self.main_node:child('btn_back_exit') then
        self.main_node:child('btn_back_exit'):show()
    end
end

-------------------------------------------------------------------------------
--备用 退出按钮
-------------------------------------------------------------------------------
function RoomInfoBase:onBtnBackExit(sender)
    GlobalUserItem.bWaitQuit = false
    self.m_game_layer:onExitRoom()
    if buglyReportLuaException then
        --mike
        --buglyReportLuaException('mike this a special error', debug.traceback())
    end
end

return RoomInfoBase
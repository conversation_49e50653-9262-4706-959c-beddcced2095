--[[---------------------------------------------------------------------------
									EditBox控件
--]]---------------------------------------------------------------------------


local EditBox = class('EditBox', function(params)
    local editor = ccui.EditBox:create(params.size, params.bg or '', params.bg_type or ccui.TextureResType.plistType)
	return editor
end)


--------------------------------------------------------------------------------
-- 构造
--------------------------------------------------------------------------------
function EditBox:ctor(params)
	if params.password then
		self:setInputFlag(0)
	end

	self:setFont(cs.app.FONT_NAME, params.font_size or 20)
	self:setFontColor(params.font_color or cc.c3b(255, 255, 255))

	if params.center then
		self:setHorizontalAlignment(cc.TEXT_ALIGNMENT_CENTER)
	elseif params.align then
		self:setHorizontalAlignment(params.align)
	end

	self:setPlaceholderFontName(cs.app.FONT_NAME)
	if params.place_holder then
		local t = params.place_holder
		if type(t) == 'table' then
			self:setPlaceHolder(t[1])
			if t[2] then
				self:setPlaceholderFontSize(t[2])
			end
			if t[3] then
				self:setPlaceholderFontColor(t[3])
			end
		else
			self:setPlaceHolder(t)
		end
	end
end

--------------------------------------------------------------------------------
-- 设置文本
--------------------------------------------------------------------------------
function EditBox:setString(s)
	self:setText(s)
end


--------------------------------------------------------------------------------
-- 获取输入
--------------------------------------------------------------------------------
function EditBox:getString()
	return self:getText()
end


--------------------------------------------------------------------------------
-- 从TextField创建
--	注意：该方法静态
--	bg: nil or ''表示不需要背景，否则表示背景图片
--------------------------------------------------------------------------------
function EditBox.convertTextField(ui, bg, bg_type, align)
	local render = ui:getVirtualRenderer()
	local params = {
		size			= ui:size(),
		bg				= bg or '',
        bg_type         = bg_type,
		align			= align,
		password		= ui:isPasswordEnabled(),
		font_color		= ui:getColor(),--render:getTextColor(),
		font_size		= ui:getFontSize(),
		place_holder	= {
			ui:getPlaceHolder(),
			ui:getFontSize(),
			ui:getPlaceHolderColor()
		}
	}
	local editor = EditBox.new(params)
	editor:setInputMode(cc.EDITBOX_INPUT_MODE_SINGLELINE)
	editor:setName(ui:getName())
	editor:setTag(ui:getTag())
	editor:anchor(ui:anchor()):pos(ui:pos()):zorder(ui:getLocalZOrder()):addTo(ui:getParent())

	ui:removeFromParent()

	return editor
end


return EditBox
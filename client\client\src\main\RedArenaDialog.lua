-------------------------------------------------------------------------------
--  创世版1.0
--  万元红包赛对话框
--  @date 2018-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')


local MAX_WIN_TIMES = 7     -- 最大连胜次数


local RedArenaDialog = class("RedArenaDialog", cc.Layer)


-------------------------------------------------------------------------------
-- 静态方法：显示对话框（name：win, lose, succ, task）
-------------------------------------------------------------------------------
function RedArenaDialog.showDialog(kind, param_t)
    helper.pop.popLayer( cs.app.CLIENT_SRC .. 'main.RedArenaDialog', nil, {kind, param_t or {}} )
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RedArenaDialog:ctor(kind, params)
    self:setName('red_arena_layer')
    self:enableNodeEvents()
    self.m_kind = kind
    self.m_params = params

    -- 载入主UI
    local main_node = helper.app.loadCSB('RedArenaDialog.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('panel_win'):hide()
    main_node:child('panel_lose'):hide()
    main_node:child('panel_succ'):hide()
    main_node:child('panel_task'):hide()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function RedArenaDialog:onEnter()
    print('RedArenaDialog:onEnter...')

    -- 重置socket监听（连接成功且验证通过）
    LiveFrame:getInstance():addListen(cmd_common.MDM_SOCKET_SERVICE, cmd_common.SUB_SET_SOCKET, self, self.onSetSocketResp)
    -- 信息查询
    LiveFrame:getInstance():addListen(cmd_common.MDM_WIN_STREAK_SERVICE, cmd_common.SUB_WIN_STREAK_QUERY, self, self.onInfoResp)
    -- 复活
    LiveFrame:getInstance():addListen(cmd_common.MDM_WIN_STREAK_SERVICE, cmd_common.SUB_WIN_STREAK_REBORN, self, self.onReliveResp)
    -- 任务
    LiveFrame:getInstance():addListen(cmd_common.MDM_DAILY_EVENT_SERVICE, cmd_common.SUB_LIST_DAILY_EVENT, self, self.onTasksResp)

    -- 创建大厅长连接（本类中没有对应关闭，主要考虑无法确定房间是否会退出，可能导致误关闭）
    local r = LiveFrame:getInstance():connect()
    if not r and LiveFrame:getInstance():isSocketServer() then
        self:afterConnect()
    end
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RedArenaDialog:onExit()
    print('RedArenaDialog:onExit...')

    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 长连接认证返回（监听是为了准备就绪后发送命令）
-------------------------------------------------------------------------------
function RedArenaDialog:onSetSocketResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_ID)
    if not ret then return false end
    self:afterConnect()
end


-------------------------------------------------------------------------------
-- 连接之后
-------------------------------------------------------------------------------
function RedArenaDialog:afterConnect()
    if self.m_params.is_task then
        -- 获取任务列表
        self:requestTasks()
    else
        -- 获取当前连胜次数和复活卡数量
        self:requestInfo()
    end
end


-------------------------------------------------------------------------------
-- 初始化连胜面板
-------------------------------------------------------------------------------
function RedArenaDialog:initPanelWin(win_num)
    local panel = self.main_node:child('panel_win'):show()
    panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    panel:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )
    panel:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShareWin) )
    panel:child('atlas_num'):setString(tostring(win_num))
    panel:child('label_content'):setRich( LANG{'REDARENA_WIN', num=MAX_WIN_TIMES-win_num})
end


-------------------------------------------------------------------------------
-- 初始化连胜失败面板
-------------------------------------------------------------------------------
function RedArenaDialog:initPanelLose(win_num, has_num, need_num)
    local panel = self.main_node:child('panel_lose'):show()
    panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    panel:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )
    if win_num == 0 then
        panel:child('atlas_num'):hide()
        panel:child('font_title'):px(display.cx)
        panel:child('label_content'):setRich( LANG.REDARENA_LOSE_0 )
        panel:child('btn_share'):hide()
        panel:child('btn_continue'):px(display.cx)
    else
        panel:child('atlas_num'):setString(tostring(win_num + 1))
        if need_num == 0 then   -- 可以分享复活
            panel:child('label_content'):setRich( LANG{'REDARENA_LOSE_N_TIP', has_num=has_num})
            panel:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShareLose) )
        else
            panel:child('label_content'):setRich( LANG{'REDARENA_LOSE_N', need_num=need_num, has_num=has_num})
            if has_num >= need_num then
                panel:child('btn_share/label'):setString( LANG.REDARENA_RELIVE )
                panel:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRelive) )
            else
                panel:child('btn_share'):removeFromParent()
                panel:child('btn_continue'):px(display.cx)
            end
        end
    end

    -- 输的一方修改小结算的“继续游戏”功能，直接退出房间
    local game_result_layer = helper.app.getFromScene('subGameResultLayer')
    if game_result_layer then        
        game_result_layer:setContinueAsExit(function()
            helper.pop.popLayer( cs.app.CLIENT_SRC .. 'main.RedArenaWaiting' )
        end)
    end
end


-------------------------------------------------------------------------------
-- 初始化连胜7次面板
-------------------------------------------------------------------------------
function RedArenaDialog:initPanelSucc(succ_times, tickets)
    local panel = self.main_node:child('panel_succ'):show()
    panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnExitRoom) )
    panel:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShareSucc) )
    panel:child('btn_share').tickets = tickets
    panel:child('label_content'):setRich( LANG{'REDARENA_SUCCESS', date=os.date("%Y-%m-%d", os.time()), times=succ_times} )
    panel:child('label_num'):setString('x' .. tickets)
end


-------------------------------------------------------------------------------
-- 初始化任务面板
-------------------------------------------------------------------------------
function RedArenaDialog:initPanelTask(tasks)
    local panel = self.main_node:child('panel_task'):show()
    if not self.m_task_inited then
        panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    end

    local listview, template = panel:child('listview,template')
    listview:removeAllItems()
    for i, task in ipairs(tasks) do
        local has_done = task.cbStatus > 0
        local item = template:clone():show()
        item.task = task
        item:child('label_content'):setRich( task.szName )
        item:child('label_num'):setRich( string.format('%s%d/%d', (has_done and '<g>' or ''), task.nDoneNum, task.nGoalNum) )
        local btn = item:child('btn_go')
        if has_done then
            btn:removeAllChildren()
            btn:ignoreContentAdaptWithSize(true)
            btn:texture('word/font_redarena_has_done.png')
        else
            btn:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnTaskGo) )
        end
        listview:pushBackCustomItem(item)
    end
    template:hide()
    self.m_task_inited = true
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnClose(sender)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 继续按钮点击
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnContinue(sender)
    local result_layer = helper.app.getFromScene('subGameResultLayer')
    if result_layer then
       result_layer:onBtnContinue() 
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 离开房间按钮点击
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnExitRoom(sender)
    local result_layer = helper.app.getFromScene('subGameResultLayer')
    if result_layer then
       result_layer:onBtnExit() 
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 分享按钮点击--胜利炫耀
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnShareWin(sender)
    helper.pop.shareImage():showButtons('hy,pyq') 
end


-------------------------------------------------------------------------------
-- 分享按钮点击--失败请求好友帮助
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnShareLose(sender)
    helper.pop.alert( LANG.REDARENA_LOSE_HELP, function()
        local layer = helper.pop.shareImage('common/share_image_redarena.jpg', nil, nil, 'redarena_lose', function()
            if tolua.isnull(self) then return end
            helper.pop.message( LANG.REDARENA_RELIVE_SUCC )
            self:requestInfo()  -- 会导致红包赛主UI刷新
            self:removeFromParent()
        end)
        layer:showButtons('hy')
        layer:setParam(self.m_kind)
    end, true )
end


-------------------------------------------------------------------------------
-- 分享按钮点击--7连胜
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnShareSucc(sender)
    local image_path = 'myredarenashare.jpg'

    local label_name = ccui.Text:create( GlobalUserItem.szNickName, cs.app.FONT_NAME, 30)
    label_name:setTextColor(cc.c3b(255, 0, 0))
    local label_date = ccui.Text:create( os.date("%Y-%m-%d", os.time()), cs.app.FONT_NAME, 30)
    label_date:setTextColor(cc.c3b(255, 0, 0))
    local money = string.format('%.2f', sender.tickets / 100.0)
    local arr = money:split('.')
    local label_money1 = ccui.TextAtlas:create(arr[1], 'common/number_rank_red_big.png', 117, 163, '0')
    local label_money2 = display.newSprite('common/number_rank_red_big_dian.png')
    local label_money3 = ccui.TextAtlas:create(arr[2], 'common/number_rank_red_big.png', 117, 163, '0')
    local bg = display.newSprite('common/share_image_redarena_finish.jpg')

    local size = bg:size()
    local w, h = size.width, size.height
    local render = cc.RenderTexture:create(w, h)
    render:begin()
        
    bg:pos(w * 0.5, h * 0.5):visit()
    label_name:pos(491, 460):visit()
    label_date:pos(751, 460):visit()
    label_money3:pos(615, 225):visit()
    label_money2:pos(615 - label_money3:size().width/2 - label_money2:size().width/2, 225):visit()
    label_money1:pos(615 - label_money3:size().width/2 - label_money2:size().width - label_money1:size().width/2, 225):visit()

    render:endToLua()
    render:saveToFile(image_path, cc.IMAGE_FORMAT_JPEG)

    local layer = helper.pop.shareImage(image_path, nil, nil, 'redarena_succ', function()
        if tolua.isnull(self) then return end
        helper.pop.message( LANG.REDARENA_SUCC_SHARE )
        self:requestInfo()  -- 会导致红包赛主UI刷新，主要是为了刷新复活卡数量
    end)
    layer:showButtons('hy,pyq')
    layer:setParam(self.m_kind)
end


-------------------------------------------------------------------------------
-- 任务执行按钮点击
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnTaskGo(sender)
    local task = sender:getParent().task
    if task.szGoalType == 'SHARE_FRIEND' or task.szGoalType == 'SHARE_CIRCLE' then
        local tag = task.szGoalType == 'SHARE_FRIEND' and 'redarena_task' or 'redarena_task_circle'
        local btns = task.szGoalType == 'SHARE_FRIEND' and 'hy' or 'pyq'
        local layer = helper.pop.shareImage('common/share_image_redarena.jpg', nil, nil, tag, function()
            if tolua.isnull(self) then return end
            -- 刷新任务列表，刷新信息
            self:requestInfo()
            self:requestTasks()
        end)
        layer:showButtons(btns)
        layer:setParam(self.m_kind)
    end
end


-------------------------------------------------------------------------------
-- 复活按钮点击
-------------------------------------------------------------------------------
function RedArenaDialog:onBtnRelive(sender)
	self:requestRelive()
end


-------------------------------------------------------------------------------
-- 请求任务列表
-------------------------------------------------------------------------------
function RedArenaDialog:requestTasks()
    local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = yl.DAILY_TASK_REDARENA} )
	cmd_data:setcmdinfo(cmd_common.MDM_DAILY_EVENT_SERVICE, cmd_common.SUB_LIST_DAILY_EVENT)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 任务列表返回
-------------------------------------------------------------------------------
function RedArenaDialog:onTasksResp(data)
    local tasks = LiveFrame:getInstance():resp(data, cmd_common.tagDailyEvent, true)
    if not tasks then return false end

    self:initPanelTask(tasks)
end


-------------------------------------------------------------------------------
-- 请求初始信息
-------------------------------------------------------------------------------
function RedArenaDialog:requestInfo()
    local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = self.m_kind} )
	cmd_data:setcmdinfo(cmd_common.MDM_WIN_STREAK_SERVICE, cmd_common.SUB_WIN_STREAK_QUERY)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 初始信息返回
-------------------------------------------------------------------------------
function RedArenaDialog:onInfoResp(data)
    if self.m_params.is_task then return end

    local ret = LiveFrame:getInstance():resp(data, cmd_common.WinStreakInfo)
    if not ret then return false end
    if ret.isLastLose then
        self:initPanelLose(ret.nWin, ret.nRebornCard, ret.nNeedRebornCard)
    elseif ret.nWin == MAX_WIN_TIMES then
        self:initPanelSucc(ret.nFinishRound, ret.nTicket)
    else
        self:initPanelWin(ret.nWin)
    end
end


-------------------------------------------------------------------------------
-- 请求复活
-------------------------------------------------------------------------------
function RedArenaDialog:requestRelive()
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = self.m_kind} )
	cmd_data:setcmdinfo(cmd_common.MDM_WIN_STREAK_SERVICE, cmd_common.SUB_WIN_STREAK_REBORN)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 复活返回
-------------------------------------------------------------------------------
function RedArenaDialog:onReliveResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDValue)
    if not ret then return false end

    if ret.dwID > 0 then    
        helper.pop.message(ret.szMsg)
    else
        helper.pop.message( LANG.REDARENA_RELIVE_SUCC )
        self:requestInfo()  -- 会导致红包赛主UI刷新
        self:removeFromParent()
    end
end


return RedArenaDialog

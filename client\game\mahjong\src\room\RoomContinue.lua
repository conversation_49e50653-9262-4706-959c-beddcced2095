-------------------------------------------------------------------------------
--  创世版1.0
--  再开一局面板
--  @date 2019-06-12
--  @auth woodoo
-------------------------------------------------------------------------------
local PopupHead = cs.app.client('system.PopupHead')


local RoomContinue = class('RoomContinue', cc.Node)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomContinue:ctor( view_layer, user_items )
    print('RoomContinue:ctor...')
    self:enableNodeEvents()
    self.m_view_layer = view_layer
    self.m_user_items = user_items

    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomContinue.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('template'):hide()

    main_node:child('mask'):addTouchEventListener( handler(self, self.onBtnClose) )
    main_node:child('bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    main_node:child('bg/btn_agree'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAgree) )
    main_node:child('bg/btn_refuse'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRefuse) )

    self:initUsers()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RoomContinue:onExit()
    print('RoomContinue:onExit...')
end


-------------------------------------------------------------------------------
-- 初始化玩家信息
-------------------------------------------------------------------------------
function RoomContinue:initUsers()
    local room_data = PassRoom:getInstance().m_tabPriData
    local replies = room_data.m_nContinueReply[1]

    self.users = {}
    local container = self.main_node:child('bg/panel_list')
    local template = self.main_node:child('template')
    local size = template:size()
    local box = container:size()
    local gap = 10

    local user_count = 0
    for k, user_item in pairs(self.m_user_items) do
        user_count = user_count + 1
    end
    local x = box.width / 2 - (user_count - 1) * (size.width / 2 + gap)

    for view_id, user_item in pairs(self.m_user_items) do
        local item = template:clone()
        local head = PopupHead:create(self.m_view_layer, user_item, 68, 100)
        head:pos(size.width/2, size.height/2):addTo(item:child('head'))
        item:child('word_waiting'):show()
        item:child('word_agree'):hide()
        item:child('word_refuse'):hide()
        item:pos(x, 0)
        item:show():addTo(container)
        self.users[user_item.dwUserID] = item
        x = x + size.width + gap

        local reply = replies[user_item.wChairID + 1] or 0
        if reply ~= 0 then
            self:updateUser(user_item, reply == 1)
        end
    end
end


-------------------------------------------------------------------------------
-- 更新玩家信息
-------------------------------------------------------------------------------
function RoomContinue:updateUser(user_item, is_agree)
    local item = self.users[user_item.dwUserID]
    if not item then return end
    item:child('word_refuse'):setVisible(not is_agree)
    item:child('word_agree'):setVisible(is_agree)
    item:child('word_waiting'):hide()
    if user_item.dwUserID == GlobalUserItem.dwUserID then
        self.main_node:child('bg/btn_agree'):hide()
        self.main_node:child('bg/btn_refuse'):hide()
    end
end


-------------------------------------------------------------------------------
-- 重置玩家信息
-------------------------------------------------------------------------------
function RoomContinue:resetUsers()
    if not self.m_user_items then return end
    for k, user_item in pairs(self.m_user_items) do
        local item = self.users[user_item.dwUserID]
        if not item then return end
        item:child('word_waiting'):show()
        item:child('word_agree'):hide()
        item:child('word_refuse'):hide()
    end
    self.main_node:child('bg/btn_refuse'):show()
    self.main_node:child('bg/btn_agree'):show()
end


-------------------------------------------------------------------------------
-- 效果关闭
-------------------------------------------------------------------------------
function RoomContinue:effectClose()
    if self.m_is_closing then return end
    self.m_is_closing = true

    local this = self
    self.main_node:child('bg'):stop():runMyAction( cc.Sequence:create(
        cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
        cc.CallFunc:create( function()
            this:hide()
            this.m_is_closing = false
        end )
    ) )
end


-------------------------------------------------------------------------------
-- 效果显示
-------------------------------------------------------------------------------
function RoomContinue:effectShow()
    if not self.m_is_closing and self:isVisible() then return end
    self.m_is_closing = false

    self:show()
    self.main_node:child('bg'):stop():scale(0):runMyAction( cc.Sequence:create(
        cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
    ) )
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function RoomContinue:onBtnClose()
    self:effectClose()
end


-------------------------------------------------------------------------------
-- 同意再开一局
-------------------------------------------------------------------------------
function RoomContinue:onBtnAgree()
    PassRoom:getInstance():getNetFrame():sendContinuePlay(1)
end


-------------------------------------------------------------------------------
-- 拒绝再开一局
-------------------------------------------------------------------------------
function RoomContinue:onBtnRefuse()
    PassRoom:getInstance():getNetFrame():sendContinuePlay(0)
end


return RoomContinue
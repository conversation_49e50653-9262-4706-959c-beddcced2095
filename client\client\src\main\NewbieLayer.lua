-------------------------------------------------------------------------------
--  创世版1.0
--  新手礼物
--  @date 2018-04-20
--  @auth woodoo
-------------------------------------------------------------------------------
local NewbieLayer = class("NewbieLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function NewbieLayer:ctor()
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('NewbieLayer.csb')
    self.main_node = main_node
    main_node:anchor(0.5, 0.5):pos(self:size().width/2, self:size().height/2):addTo(self)

    main_node:child('panel_newbie_gift/btn_get'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnGet) )

    main_node:scale(0.75):runAction( cc.EaseBackOut:create(cc.ScaleTo:create(0.3, 1)) )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function NewbieLayer:onEnter()
    print('NewbieLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function NewbieLayer:onExit()
    print('NewbieLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 立即领取按钮点击
-------------------------------------------------------------------------------
function NewbieLayer:onBtnGet(sender)
    self:removeFromParent()
    helper.app.guide('newbie_gift')
end


return NewbieLayer

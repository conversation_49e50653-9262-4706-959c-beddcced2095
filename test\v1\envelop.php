<?php
require(APPPATH.'/libraries/REST_Controller.php');
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Config.php');


class Envelop extends REST_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('activity_model');

    }

    public function withdraw_post()
    {
        $money = $this->input->post('money');
        $role_id = $this->input->post('uid');
        $event_id = $this->input->post('event_id');

        $this->_game = $this->server_model->get_game_by_id($this->_channel['game_id']);

        $this->player_model->set_database($this->_game['game_id']);

        $role = $this->player_model->get_role_info($role_id);

        if(!$role){
            $this->response(array('code'=>1,'msg'=>'角色信息不存在'));
        }

        $this->activity_model->set_database($this->_game['game_id']);

        $event = $this->activity_model->get_game_envelop_by_id($event_id);

        if(!$event) {
            $this->response(array('code'=>1,'msg'=>'为查询到对应活动'));
        }

        if($event['BeginTime']>time()) {
            $this->response(array('code'=>1,'msg'=>'活动未开始'));
        }

        if($event['EndTime']<time()) {
            $this->response(array('code'=>1,'msg'=>'活动已结束'));
        }

        // 查询用户当前活动奖励
        $reward = $this->activity_model->get_role_envelop_reward($role_id,$event_id);

        if($event['Withdraw']>0&&$reward['RedPacket']<$event['Withdraw']) {
            $this->response(array('code'=>1,'msg'=>'累计获得'.$event['Withdraw'].'元才可以提现哦'));
        }

        $this->load->model('mp_model');

        $mp_user = $this->mp_model->get_mp_user_by_roleid($role_id,$this->_channel['game_id']);

        if(!$mp_user) {
            $this->response(array('code'=>1,'msg'=>'请关注【'.$this->_game['mp_name'].'】微信公众号后按提示领取'));
        }

        if($this->_send_redpack($role,$mp_user['openid'],$reward['RedPacket'],$event)) {

            $this->activity_model->clean_role_envelop_reward($role_id,$event_id);

            $this->response(array('code'=>0,'msg'=>'提现成功'));
        } else {
            $this->response(array('code'=>1,'msg'=>'提现失败,请联系客服'));
        }
    }

    private function _send_redpack($role,$open_id,$money,$event)
    {
        $mp_config = $this->mp_model->get_mp_config($this->_channel['game_id']);
        WxPayConfig::setConfig($mp_config);
        $game_info = $this->mp_model->get_game_info($this->_channel['game_id']);

        $this->load->helper('string');
        $order_no = date("YmdHis") . random_string('nozero', 3);

        // 创建提现订单
        $data = array(
            'role_id' => $role['UserID'],
            'role_name'  =>$role['NickName'],
            'order_no' => $order_no,
            'total_fee' => $money,
            'create_time' => time(),
            'game_id'=>$this->_channel['game_id']
        );

        $this->db->insert('mp_exchange_cash', $data);

        $id = $this->db->insert_id();

        if ($id){
            $input = new WxPaySendredpack();
            $input->SetMch_billno($order_no);//商户订单号
            $input->SetSend_name($game_info['game_name']);//商户名称
            $input->SetRe_openid($open_id);//用户openid
            $input->SetTotal_amount($money*100);//付款金额
            $input->SetTotal_num(1);//红包发放总人数
            $input->SetWishing($event['Name']);//红包祝福语
            $input->SetAct_name($this->_game['game_name']);//活动名称
            $input->SetRemark($event['Name']);//备注信息
            if ($money >= 200){
                $input->SetScene_id('PRODUCT_3');//场景id
            }
            $order = WxPayApi::SendredpackOrder($input);

            log_message('error','发红包参数');
            log_message('error',var_export($input, TRUE));

            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS'){
                $this->db->where('id',$id);
                $this->db->update('mp_exchange_cash',array('out_trade_no'=>$order['send_listid'],'status'=>1));

                return true;
            }else{
                $this->db->where('id',$id);
                $this->db->update('mp_exchange_cash',array('status'=>-1));

                log_message('error','发红包');
                log_message('error',var_export($order, TRUE));
                return false;
            }
        }else{
            return false;
        }
    }
}
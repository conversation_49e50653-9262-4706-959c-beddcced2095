-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 主界面
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbMain = class('OrbMain', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbMain:ctor(panel)
    print('OrbMain:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('label_notice'):hide()
    self.template_friend = self.m_panel:child('template_friend')
    self.template_friend:hide()
    self.m_panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    self.m_panel:child('btn_draw'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnDraw) )
    self.m_panel:child('btn_my'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnMy) )
    self.m_panel:child('btn_rule'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRule) )
end


-------------------------------------------------------------------------------
-- 更新
-------------------------------------------------------------------------------
function OrbMain:onUpdate()
    local amount = self:getAmount()
    self.m_panel:child('label_amount'):setString(string.format('%.2f', amount))
end


-------------------------------------------------------------------------------
-- 显示事件
-------------------------------------------------------------------------------
function OrbMain:onShow()
    print('OrbMain:onShow')
    self:onUpdate()
    self:startTimedown()

    self.m_panel:child('panel_notice'):removeAllChildren()
    self.m_panel:perform(function()
        self:showNotice(self.m_panel:child('panel_notice'), self.m_panel:child('label_notice'), 'ORB_NOTICE_MAIN')
    end, 0.2)

    local users = {}
    local user_logs = {}
    local logs = self:getOpenLogs()
    for i, log in ipairs(logs) do
        local info = user_logs[log.from_id]
        if not info then
            users[#users + 1] = log.from_id
            info = {from_id = log.from_id, avatar = log.from_avatar, amount = 0, is_self = (log.from_id == log.to_id)}
            user_logs[log.from_id] = info
        end
        info.amount = info.amount + log.total_fee
        if not info.is_self then
            info.status = info.amount > 0.5 and 'orb_icon_status_has_game.png' or 'orb_icon_status_no_game.png'
        else
            info.status = 'orb_icon_status_has_open.png'
        end
    end
    local scroll = self.m_panel:child('listview')
    scroll:removeAllItems()
    for i, uid in ipairs(users) do
        local info = user_logs[uid]
        local item = self.template_friend:clone():show()
        item:child('bg_status'):zorder(1):texture('orb/' .. info.status)
        item:child('bg_amount'):zorder(1)
        item:child('label_amount'):zorder(2):setString(info.amount)

        local icon = item:child('icon')
        local head = self:createHead(info.from_id, info.avatar, icon:size().width, 'orb/orb_bg_avatar_75.png')
        head:pos(icon:pos()):addTo(item)
        icon:hide()

        scroll:pushBackCustomItem(item)
    end

    local num = math.max(1, 6 - #users)
    for i = 1, num do
        local item = self.template_friend:clone():show()
        item:child('bg_status'):hide()
        item:child('bg_amount'):hide()
        item:child('label_amount'):hide()

        local icon = item:child('icon')
        icon:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )

        scroll:pushBackCustomItem(item)
    end

    self:open('panel_summary')
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function OrbMain:onBtnClose()
    self:open('panel_leave')
end


-------------------------------------------------------------------------------
-- 提现按钮点击
-------------------------------------------------------------------------------
function OrbMain:onBtnDraw()
    --[[
    local one_status = self:getOneStatus()
    local can_draw = (one_status == 1 and self:getAmount() >= 1) or self:canDraw()

    if can_draw then
        self:open('panel_draw')
    else
        self:open('panel_mission')
    end
    --]]
    self:open('panel_draw')
end


-------------------------------------------------------------------------------
-- 我的红包按钮点击
-------------------------------------------------------------------------------
function OrbMain:onBtnMy()
    self:open('panel_my')
end


-------------------------------------------------------------------------------
-- 规则按钮点击
-------------------------------------------------------------------------------
function OrbMain:onBtnRule()
    self:open('panel_rule_activity')
    --[[
    self.test_log_id = (self.test_log_id or 1) + 1
    self:addOpenLog({
        id = self.test_log_id, 
        from_id = self.test_log_id,                                -- 来源用户id
        from_avatar = "https://wx.qlogo.cn/mmopen/vi_32/H0GqDP7ibHo4qflNmqt52rV0PmdRInYgicjJMPxOLVJH6wYKcGFrazHR9K1luU61q2G80RUsXhz6rghWa51WxVRw/132",       -- 来源用户头像
        from_nickname = "东斜",					    -- 来源用户昵称
        to_id = 1,                                  -- 目标用户id
        total_fee = 0.3,       				    -- 拆得的金额
        type = "reg",        						-- 类型 own 自己拆，share自己分享，reg 注册，play 玩游戏
        create_time = "2019-01-01 10:00:00"			-- 时间
    })
    self:updateData({balance = self:getAmount() + 0.3})
    self:open('panel_main')
    --]]
end


return OrbMain
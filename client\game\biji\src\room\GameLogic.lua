local GameLogic = {}


--**************    扑克类型    ******************--
--混合牌型
GameLogic.OX_VALUEO				= 100


--最大手牌数目
GameLogic.MAX_CARDCOUNT			= 5
--牌库数目
GameLogic.FULL_COUNT			= 52
--正常手牌数目
GameLogic.NORMAL_COUNT			= 5

GameLogic.TYPE_DUIZI			= 101
GameLogic.TYPE_LIANGDUI			= 102
GameLogic.TYPE_SANTIAO			= 103
GameLogic.TYPE_SHUNZI			= 104
GameLogic.TYPE_TONGHUA			= 105
GameLogic.TYPE_HULU				= 106
GameLogic.TYPE_TIEZHI			= 107
GameLogic.TYPE_TONGHUASHUN		= 108


GameLogic.OX_YILONG             = 112
GameLogic.OX_QINGLONG           = 113

-- 抢水类型 -----start-------
GameLogic.OX_LIUDUI             = 109
GameLogic.OX_SANSHUNZI          = 110
GameLogic.OX_SANTONGHUA         = 111
-- 抢水类型 -----end-------

-- 抢水类型 -----start-------
GameLogic.OX_SANQING             	= 115
GameLogic.OX_QUANHEI       			= 116
GameLogic.OX_QUANHONG     			= 117
GameLogic.OX_QUANSHUNZI             = 118
GameLogic.OX_SHUANGSHUNQING       	= 119
GameLogic.OX_SHUANGSANTIAO     		= 120
GameLogic.OX_SITIAO     			= 121
GameLogic.OX_QUANSHUNQING			= 122
GameLogic.OX_SHUANGSITIAO			= 123
GameLogic.OX_QUANSANTIAO			= 124

-- 抢水类型 -----end-------


--取模
function GameLogic:mod(a,b)
    return a - math.floor(a/b)*b
end
--获得牌的数值
function GameLogic:getCardValue(cbCardData)
    return self:mod(cbCardData, 16)
end

function GameLogic:getCardLogicValue(cbCardData)
	local val = self:mod(cbCardData, 16)
	if val == 0x01 then
		val = val + 0x0D
	end
	return val
end

--获得牌的颜色（0 -- 4）
function GameLogic:getCardColor(cbCardData)
    return math.floor(cbCardData/16)
end

--拷贝表
function GameLogic:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
end

function GameLogic:sortSanQing( cbCardData )
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_c < b_c then 
			return true 
		elseif a_c == b_c and a_v < b_v then
			return true
		else
			return false
		end

	end)
end

function GameLogic:sortCard(cbCardData)
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_c < b_c then 
			return true 
		elseif a_c == b_c and a_v < b_v then
			return true
		else
			return false
		end

	end)
	local colors = {}
	local card_info = {}
	for i = 1, 4 do
		colors[i] = 0
	end
	for i = 1, #cbCardData do
		local color = self:getCardColor(cbCardData[i])
		colors[color + 1] = colors[color + 1] + 1
		card_info[i] = color + 1
	end
	local index_color = 0
	for i = 1, 4 do
		if colors[i] == 3 then
			index_color = i
			break
		end
	end


	local insert_index = 1
	for i = 1, #cbCardData do
		if card_info[i] == index_color then
			local v = table.remove(cbCardData, i)
			table.insert(cbCardData, 1, v)
			--insert_index = insert_index + 1
		end
	end
end

function GameLogic:sortLiuduiBan(cbCardData)
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		if a_v == 0x01 then
			a_v = a_v + 0x0D
		end
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		if b_v == 0x01 then
			b_v = b_v + 0x0D
		end
		local b_c = self:getCardColor(b)
		if a_v < b_v then 
			return true 
		elseif a_v == b_v and a_c < b_c then
			return true
		else
			return false
		end
	end)
	for i = 1, #cbCardData do
		print('liu dui ban before ', self:getCardValue(cbCardData[i]))
	end
	local single_card = nil
	local index = 0
	for i = 1, #cbCardData, 2 do
		if i == #cbCardData then
			single_card = cbCardData[i]
			index = i
			break
		end
		if self:getCardValue(cbCardData[i]) ~= self:getCardValue(cbCardData[i + 1]) then
			index = i
			single_card = cbCardData[i]
			
			break
		end
	end
	table.remove(cbCardData, index)
	table.insert(cbCardData, 1, single_card)
	for i = 1, #cbCardData do
		print('liu dui ban ', self:getCardValue(cbCardData[i]))
	end
end

function GameLogic:sortYiLong(cbCardData)
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v < b_v then 
			return true 
		elseif a_v == b_v and a_c < b_c then
			return true
		else
			return false
		end
	end)
end

function GameLogic:sortQuanSanTiao(cbCardData)
	local analyse = self:AnalysebCardData(cbCardData, #cbCardData)
	cbCardData = self:copyTab(analyse.cbCardData[3])
end

function GameLogic:sortShuangSanTiao(cbCardData)
	local analyse = self:AnalysebCardData(cbCardData, #cbCardData)
	local index = 1
	for i = 1, 2 do
		for j = 1, #analyse.cbCardData[i] do
			cbCardData[index] = analyse.cbCardData[i][j]
			index = index + 1
		end
	end
	for i = 1, #analyse.cbCardData[3] do
		cbCardData[index] = analyse.cbCardData[3][i]
		index = index + 1
	end
end

function GameLogic:sortShuangSiTiao(cbCardData)
	local analyse = AnalysebCardData(cbCardData, #cbCardData)
	cbCardData[1] = analyse.cbCardData[1][1]
	for i = 1, #analyse.cbCardData[4] do
		cbCardData[1 + i] = analyse.cbCardData[4][i]
	end
end

function GameLogic:sortSanFenTianXia(cbCardData)
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v < b_v then 
			return true 
		elseif a_v == b_v and a_c < b_c then
			return true
		else
			return false
		end
	end)
end

function GameLogic:sortSanTongHuaShun( cbCardData )
	-- body
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v < b_v then 
			return true 
		elseif a_v == b_v and a_c < b_c then
			return true
		else
			return false
		end
	end)

	local vec_color_cards = {}
	for i,v in ipairs(cbCardData) do
		local color = self:getCardColor(cbCardData[i])
		vec_color_cards[color] = vec_color_cards[color] or {}
		table.insert(vec_color_cards[color], cbCardData[i])
	end

	for i = 1, #vec_color_cards do
		local size = #vec_color_cards[i]
		if size ~= 3 and size ~= 5 and size ~= 10 and size ~= 8 and size ~= 13 then
			return
		end
	end

	table.sort(vec_color_cards, function (a, b)
		if #a < #b then
			return  true
		else
			return false
		end
	end)
	local index = 1
	for i = 1, #vec_color_cards do
		for j = 1, #vec_color_cards[i] do
			cbCardData[index] = vec_color_cards[i][j]
			index = index + 1
		end
	end
end

function GameLogic:sortSiTaoSanTiao(cbCardData)
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v < b_v then 
			return true 
		elseif a_v == b_v and a_c < b_c then
			return true
		else
			return false
		end
	end)
end


function GameLogic:sortSanShunzi(cbCardData)
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v > b_v then return true end
		if a_v == b_v and a_c > b_c then return true end
	end)
	local tmpCardCount = {}
	for i = 1, 14 do
		tmpCardCount[i] = 0
	end
	for i = 1, #cbCardData do
		local val = self:getCardValue(cbCardData[i])
		tmpCardCount[val] = tmpCardCount[val] + 1
		if val == 0x01 then
			tmpCardCount[14] = tmpCardCount[14] + 1
		end
	end

	
	for i = 14, 3, -1 do repeat
		
		if tmpCardCount[i] > 0 and tmpCardCount[i - 1] > 0 and tmpCardCount[i - 2] > 0 then
			local card_count = self:copyTab(tmpCardCount)
            card_count[i] = card_count[i] - 1
            card_count[i - 1] = card_count[i - 1] - 1
			card_count[i - 2] = card_count[i - 2] - 1
			local tmp = {}
            if i == 14 then
				card_count[1] = card_count[1] - 1
			end
			if i == 3 then
				card_count[14] = card_count[14] - 1
			end
			tmp = {i, i - 1, i - 2}

			for j = #card_count, 3, -1 do repeat
				if card_count[j] == 0 then
					break
				end
				local tmp_index = {}
				local cp_card_count = self:copyTab(card_count)
				do
					local k = j
					local is_break = false
					while k >= 3 do repeat
						if cp_card_count[k] == 0 then
							k = k - 1
							break
						end
						if cp_card_count[k] > 0 and
						cp_card_count[k - 1] > 0 and
						cp_card_count[k - 2] > 0  then
							cp_card_count[k] = cp_card_count[k] - 1
							cp_card_count[k - 1] = cp_card_count[k - 1] - 1
							cp_card_count[k - 2] = cp_card_count[k - 2] - 1
							table.insert(tmp_index,k)
							table.insert(tmp_index,k - 1)
							table.insert(tmp_index,k - 2)
							if k == 14 then
								cp_card_count[1] = cp_card_count[1] - 1
							end
							if k == 3 then
								cp_card_count[14] = cp_card_count[14] - 1
							end
						else
							if k == 14 then
								k = k - 1
							else
								is_break = true
								break
							end
						end
					until true
					if is_break then
						break
					end
					end
				end

				if i == 3 then
					dump(cp_card_count)
				end

				local is_not_sanshunzi = true
				for k = 1, #cp_card_count do
					if cp_card_count[k] ~= 0 then
						is_not_sanshunzi = false
						break
					end
				end

				if is_not_sanshunzi then
					for j = 1, #tmp_index do
						table.insert(tmp, tmp_index[j])
					end
					local tt = {}
					for j = 1, #tmp do
						local a = tmp[j]
						if a == 14 then
							a = 1
						end
						for k = 1, #cbCardData do
							if self:getCardValue(cbCardData[k]) == a then
								print('cur card is ', cbCardData[k], j)
								tt[j] = cbCardData[k]
								cbCardData[k] = 0
								break
							end
						end
					end
					for j = 1, #tt do
						cbCardData[j] = tt[j]
					end
					return
				end		

			until true
			end	
		end
	until true
	end
end

--获取牛牛
function GameLogic:getOxCard(cbCardData)
	local bTemp = {}
	--[[
	table.sort(cbCardData, function(a, b)  
		local a_v = self:getCardLogicValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardLogicValue(b)
		local b_c = self:getCardColor(b)
		if a_v > b_v then return true end
		if a_v == b_v and a_c > b_c then return true end
	end)
	--]]
	local bTempData = self:copyTab(cbCardData)
	local bSum = 0
	for i = 1, 5 do
		bTemp[i] = self:getCardLogicValue(cbCardData[i])
		bSum = bSum + bTemp[i]
	end

	for i = 1, 5 - 1 do
		for j = i + 1, 5 do
			if self:mod(bSum - bTemp[i] - bTemp[j], 10) == 0 then
				local bCount = 1
				for k = 1, 5 do
					if k ~= i and k ~= j then
						cbCardData[bCount] = bTempData[k]
						bCount = bCount + 1
					end
				end
				cbCardData[4] = bTempData[i]
				cbCardData[5] = bTempData[j]
				return true
			end
		end
	end

	return false
end

--获取类型
function GameLogic:getCardType(cbCardData, count)
	local tmpData = self:copyTab(cbCardData)
	table.sort(tmpData, function(a, b)  
		local a_v = self:getCardLogicValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardLogicValue(b)
		local b_c = self:getCardColor(b)
		if a_v > b_v then
			return true
		elseif a_v == b_v and a_c > b_c then
			return true 
		else
			return false
		end
	end)

	if count == 3 then
		if self:getCardLogicValue(tmpData[1]) == self:getCardLogicValue(tmpData[2])
			and self:getCardLogicValue(tmpData[1]) == self:getCardLogicValue(tmpData[3]) then
				return GameLogic.TYPE_SANTIAO
		end

		if self:getCardLogicValue(tmpData[1]) == self:getCardLogicValue(tmpData[2])
			or self:getCardLogicValue(tmpData[2]) == self:getCardLogicValue(tmpData[3]) then
				return GameLogic.TYPE_DUIZI
		end

		--return GameLogic.OX_VALUEO
	end

	--[[
	if count ~= 5 then
		return GameLogic.OX_VALUEO
	end
	--]]

	local type = GameLogic.OX_VALUEO
	for i = GameLogic.TYPE_TONGHUASHUN, GameLogic.TYPE_DUIZI, -1  do
		local is_has = self:isHasCurType(tmpData, i, count)
		if is_has then
			type = i
			break
		end
	end

	return type
end

function GameLogic:compareTongHuaOrSanPan(cbTempA, cbTempB)
	local a_len = #cbTempA
	local b_len = #cbTempB
	local len = a_len <= b_len and a_len or b_len
	
	local cp_a = {}
	local cp_index = 0
	for i = a_len, 1, -1 do
		if self:getCardValue(cbTempA[i]) == 0x01 then
			--table.insert(cp_a, 1, cbTempA[i] + 0x0D)
			table.insert(cp_a, 1, cbTempA[i])
			cp_index = cp_index + 1
		else
			break
		end
	end
	for i = 1, a_len - cp_index do
		table.insert(cp_a, cbTempA[i])
	end
	cp_index = 0
	local cp_b = {}
	for i = b_len, 1, -1 do
		if self:getCardValue(cbTempB[i]) == 0x01 then
			table.insert(cp_b, 1, cbTempB[i])
			cp_index = cp_index + 1
		else
			break
		end
	end
	for i = 1, b_len - cp_index do
		table.insert(cp_b, cbTempB[i])
	end

	for i = 1, a_len do
		print('a is ', i , cp_a[i])
	end
	for i = 1, b_len do
		print('b is ', i , cp_b[i])
	end
	return self:customCompare(cp_a, cp_b, len)	
end

function GameLogic:comapreAB(c_a, c_b)
	local a_v = self:getCardLogicValue(c_a)
	local b_v = self:getCardLogicValue(c_b)
		
	if a_v < b_v then
		return false
	elseif a_v > b_v then
		return true
	else 
		return c_a > c_b
	end
end

function GameLogic:customCompare(cp_a, cp_b, len)
	for i = 1, len do
		local a_v = self:getCardLogicValue(cp_a[i])
		local b_v = self:getCardLogicValue(cp_b[i])
		print('custm Compare is ', a_v, b_v, cp_a[i], cp_b[i])

		if a_v < b_v then
			return false
		elseif a_v > b_v then
			return true
		end
	end
	for i = 1, len do
		local a_v = self:getCardColor(cp_a[i])
		local b_v = self:getCardColor(cp_b[i])
		if a_v < b_v then
			return false
		elseif a_v > b_v then
			return true
		end
	end
end

function GameLogic:compareShunzi(cbTempA, cbTempB)
	local cp_a = {}
	local cp_index = 0
	local a_len = #cbTempA
	for i = a_len, 1, -1 do
		if self:getCardValue(cbTempA[i]) == 0x01 then
			table.insert(cp_a, 1, cbTempA[i])
			cp_index = cp_index + 1
		else
			break
		end
	end
	for i = 1, a_len - cp_index do
		table.insert(cp_a, cbTempA[i])
	end
	cp_index = 0
	local cp_b = {}
	for i = a_len, 1, -1 do
		if self:getCardValue(cbTempB[i]) == 0x01 then
			table.insert(cp_b, 1, cbTempB[i])
			cp_index = cp_index + 1
		else
			break
		end
	end
	for i = 1, a_len - cp_index do
		table.insert(cp_b, cbTempB[i])
	end
	
	local a_v = self:getCardLogicValue(cp_a[1])
	local b_v = self:getCardLogicValue(cp_b[1])
	if a_v > b_v then
		return true
	elseif a_v < b_v then
		return false
	else
		if a_v == 0x0E then
			a_v = self:getCardLogicValue(cp_a[2])
			b_v = self:getCardLogicValue(cp_b[2])
			
			if a_v > b_v then
				return true
			elseif a_v < b_v then
				return false
			else
				if cp_a[1] > cp_b[1] then
					return true
				else
					return false
				end
			end
		else
			if cp_a[1] > cp_b[1] then
				return true
			else
				return false
			end
		end
	end
end

function GameLogic:compareTieZhaHu(cbTempA, cbTempB)
	local len = #cbTempA
	local a_mid = math.floor(len / 2) + 1
	local b_mid = math.floor(#cbTempB / 2) + 1
	local a_v = self:getCardValue(cbTempA[a_mid])
	local b_v = self:getCardValue(cbTempB[b_mid])
	if a_v == 0x01 then
		return true
	elseif b_v == 0x01 then
		return false
	else
		return a_v > b_v and true or false		
	end

end

function GameLogic:comapreDuizi(cbTempA, cbTempB)
	local cp_a = {}
	local cp_index = 0
	local a_len = #cbTempA
	for i = a_len, 1, -1 do
		if self:getCardValue(cbTempA[i]) == 0x01 then
			table.insert(cp_a, 1, cbTempA[i])
			cp_index = cp_index + 1
		else
			break
		end
	end
	for i = 1, a_len - cp_index do
		table.insert(cp_a, cbTempA[i])
	end
	--dump(cp_a)
	cp_index = 0
	local cp_b = {}
	local b_len = #cbTempB
	for i = b_len, 1, -1 do
		if self:getCardValue(cbTempB[i]) == 0x01 then
			table.insert(cp_b, 1, cbTempB[i])
			cp_index = cp_index + 1
		else
			break
		end
	end
	for i = 1, b_len - cp_index do
		table.insert(cp_b, cbTempB[i])
	end
	--dump(cp_b)

	local a_l = self:getMaxDuizi(cp_a, a_len)
	local b_l = self:getMaxDuizi(cp_b, b_len)
	local len_l = #a_l < #b_l and #a_l or #b_l
	
	
	--dump(a_l)
	--dump(b_l)
	return self:customCompare(a_l, b_l, len_l)
end

function GameLogic:getMaxDuizi(cbData, count)
	local singleData = {}
	local ret_data = {}
	local cp_data = self:copyTab(cbData)

	for i = 1, count do repeat
		if cp_data[0] == 0 then
			break
		end
		local is_duizi = false
		for j = i + 1, count do
			if self:getCardValue(cp_data[i]) == self:getCardValue(cp_data[j]) then
				local max = cp_data[i] > cp_data[j] and cp_data[i] or cp_data[j]
				cp_data[i] = 0
				cp_data[j] = 0
				table.insert(ret_data, max)
			end
		end
		if cp_data[i] ~= 0 then
			table.insert(singleData, cp_data[i])
		end
	until true
	end

	for i = 1, #singleData do
		table.insert(ret_data, singleData[i])
	end
	return ret_data
end


function GameLogic:compare(cbFirst, cbSecond)
	local cbTempA = self:copyTab(cbFirst)
	local cbTempB = self:copyTab(cbSecond) 
	local type_a = self:getCardType(cbTempA, #cbTempA)
	local type_b = self:getCardType(cbTempB, #cbTempB)
	if type_a == GameLogic.TYPE_SANTIAO then
		type_a = 110
	end
	if type_b == GameLogic.TYPE_SANTIAO then
		type_b = 110
	end
	for i = 1, #cbTempA do
		print('compare data a ', cbTempA[i])
	end
	for i = 1, #cbTempB do
		print('compare data b ', cbTempB[i])
	end
	print('compare type ', type_a, type_b, #cbTempA, #cbTempB)
	if type_a < type_b then
		return false
	elseif type_a > type_b then
		return true
	end

	table.sort(cbTempA, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v > b_v then
			return true
		elseif a_v == b_v and a_c > b_c then
			return true 
		else
			return false
		end
	end)

	table.sort(cbTempB, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v > b_v then
			return true
		elseif a_v == b_v and a_c > b_c then
			return true 
		else
			return false
		end
	end)

	if type_a == GameLogic.OX_VALUEO or type_a == GameLogic.TYPE_TONGHUA then
		return self:compareTongHuaOrSanPan(cbTempA, cbTempB)
	elseif type_a == GameLogic.TYPE_SHUNZI or type_a == GameLogic.TYPE_TONGHUASHUN then
		return self:compareShunzi(cbTempA, cbTempB)
	elseif type_a == GameLogic.TYPE_SANTIAO then
		return self:compareTieZhaHu(cbTempA, cbTempB)
	elseif type_a == GameLogic.TYPE_DUIZI then
		return self:comapreDuizi(cbTempA, cbTempB)
	end


end

function GameLogic:isHasCurType(cbCardData, type, num)
	local bTempData = self:copyTab(cbCardData)
	local bTempData2 = self:copyTab(cbCardData)
	
	table.sort(bTempData, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_v > b_v then
			return true
		elseif a_v == b_v and a_c > b_c then
			return true 
		else
			return false
		end
	end)

	table.sort(bTempData2, function(a, b)  
		local a_v = self:getCardValue(a)
		local a_c = self:getCardColor(a)
		local b_v = self:getCardValue(b)
		local b_c = self:getCardColor(b)
		if a_c > b_c then 
			return true 
		elseif a_c == b_c and a_v > b_v then
			return true
		else
			return false
		end

	end)

	if type == GameLogic.TYPE_DUIZI then
		for i = 1, num - 1 do
			if self:getCardValue(bTempData[i]) == self:getCardValue(bTempData[i + 1]) then
				return true
			end
		end
	end

	
	if type == GameLogic.TYPE_SANTIAO then
		local bTempDataSan = self:copyTab(bTempData)
		local is_has = false
		for i = 1, num - 2 do
			if self:getCardValue(bTempDataSan[i]) == self:getCardValue(bTempDataSan[i + 1]) and 
				self:getCardValue(bTempDataSan[i]) == self:getCardValue(bTempDataSan[i + 2]) then
				print('TYPE_SANTIAO TYPE_HULU is has')
				bTempDataSan[i] = 0
				bTempData[i + 1] = 0
				bTempData[i + 2] = 0
				is_has = true
				break
			end
		end

		local is_has_duizi = false
		for i = 1, num - 1 do
			if self:getCardValue(bTempDataSan[i]) == self:getCardValue(bTempDataSan[i + 1]) and 
				bTempData[i] ~= 0 then
				is_has_duizi = true
				break
			end
		end

		if is_has then
			return true
		else
			return false
		end

	end

	if type == GameLogic.TYPE_SHUNZI then
		local list_has = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
		for i = 1, 13 do
			for j = 1, num do
				if bTempData[j] > 0 then
					if self:getCardValue(bTempData[j]) == i then
						list_has[i] = 1
						break
					end
				end
				
			end
		end
		if list_has[1] > 0 then
			list_has[14] = list_has[1]
		end
		local count = 0
		for i = 1, 14 do
			if list_has[i] == 1 then
				count = count + 1
				if count >= 3 then
					return true
				end
			else
				count = 0
			end
		end
	end

	
	if type == GameLogic.TYPE_TONGHUA then
		for i = 1, num - 2 do
			if self:getCardColor(bTempData2[i]) == self:getCardColor(bTempData2[i + 2]) then
				print('TYPE_TONGHUA is has')
				return true
			end
		end
	end



	if type == GameLogic.TYPE_TONGHUASHUN then
		local bTempDataTong = self:copyTab(bTempData)
		local list = self:getTongHuaShun(bTempDataTong, num)
		if #list > 0 then
			return true
		end
	end

	return false
	
end

function GameLogic:getDuizi(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	for i = 1, num - 1 do
		if tmpCard[i] ~= 0 then
			for j = i + 1, num do
				if self:getCardValue(tmpCard[i]) == self:getCardValue(tmpCard[j]) then
					local list = {i, j}
					--tmpCard[i] = 0
					--tmpCard[j] = 0
					table.insert(pos_index, list)
					--break
				end
			end
		end
		
	end

	if #pos_index >= 2 then
		table.sort(pos_index, function (a, b) 
			if self:getCardLogicValue(cbCardData[a[1]]) < self:getCardLogicValue(cbCardData[b[1]]) then
				return false
			elseif self:getCardLogicValue(cbCardData[a[1]]) > self:getCardLogicValue(cbCardData[b[1]]) then
				return true
			else return cbCardData[a[1]] > cbCardData[b[1]]
			end
		end)

		local last_index = pos_index[#pos_index]
		if self:getCardValue(cbCardData[last_index[1]]) == 0x01 then
			table.insert(pos_index, 1, last_index)
			table.remove(pos_index)
		end
	end

	return pos_index
end

function GameLogic:getLiangDui(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local tmp_list = {}
	local count = 0
	do 
		local i = 1
		while i < num do
			for j = i + 1, num do
				if cbCardData[i] == 0 then
					break
				end
				if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[j]) then
					cbCardData[i] = 0
					cbCardData[j] = 0
					local list = {i, j}
					table.insert(tmp_list, list)
					break
				end
			end
			i = i + 1
			for k = i, num do
				if cbCardData[k] ~= 0 then
					i = k
					break
				end
			end
		end
	end

--[[
	local list_dan = {}
	for i = 1, num do
		if cbCardData[i] ~= 0 then
			table.insert(list_dan, i)
		end
	end

	local tt = {}
	for i = 1, #tmp_list - 1 do
		for j = i + 1, #tmp_list do
			local list = {tmp_list[i][1], tmp_list[i][2],tmp_list[j][1], tmp_list[j][2]}
			table.insert(tt, list)
		end
	end

	for i = 1, #tt do
		for j = 1, #list_dan do
			local list = {tt[i][1], tt[i][2],tt[i][3], tt[i][4], list_dan[j]}
			table.insert(pos_index, list)
		end
	end
--]]
	
	if #tmp_list >= 2 then
		table.sort(tmp_list, function (a, b) 
			if self:getCardValue(tmpCard[a[1]]) > self:getCardValue(tmpCard[b[1]]) then
				return true
			else
				return false
			end
		end)

		local last_index = tmp_list[#tmp_list]
		if self:getCardValue(tmpCard[last_index[1]]) == 0x01 then
			table.insert(tmp_list, 1, last_index)
			table.remove(tmp_list)
		end
	end

	for i = 1, #tmp_list - 1 do
		for j = i + 1, #tmp_list do
			local list = {tmp_list[i][1], tmp_list[i][2],tmp_list[j][1], tmp_list[j][2]}
			table.insert(pos_index, list)
		end
	end

	return pos_index
end

function GameLogic:getSanTiao(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local tmp_list = {}
	
	for i = 1, num - 2 do
		for j = i + 1, num - 1 do
			local is_break = false
			if cbCardData[i] == 0 then
				break
			end
			if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[j]) and cbCardData[i] ~= 0 then
				for k = j + 1, num do
					if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[k]) then
						is_break = true
						local list = {i, j, k}
						cbCardData[i] = 0
						cbCardData[j] = 0
						cbCardData[k] = 0
						table.insert(pos_index, list)
						break
					end
				end
			end
			if is_break then
				break
			end				
		end
	end

	--[[
	for i = 1, num - 1 do
		if cbCardData[i] ~= 0 then
			for j = i + 1, num do
				if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[j]) then
					cbCardData[i] = 0
					cbCardData[j] = 0
					break
				end
			end
		end
	end

	local list_dan = {}
	for i = 1, num do
		if cbCardData[i] ~= 0 then
			table.insert(list_dan, i)
			print('dan card is ', i)
		end
	end

	local list_dan_dui = {}
	for i = 1, #list_dan - 1 do
		for j = i + 1, #list_dan do
			local list = {list_dan[i], list_dan[j]}
			table.insert(list_dan_dui, list)
		end
	end

	for i = 1, #tmp_list do
		for j = 1, #list_dan_dui do
			local list = {tmp_list[i][1], tmp_list[i][2], tmp_list[i][3], list_dan_dui[j][1], list_dan_dui[j][2]}
			table.insert(pos_index, list)
		end
	end
	--]]

	table.sort(pos_index, function (a, b) 
		if self:getCardValue(tmpCard[a[1]]) < self:getCardValue(tmpCard[b[1]]) then
			return false
		else
			return true
		end
	end)

	if #pos_index > 0 then
		local last_index = pos_index[#pos_index]
		if self:getCardValue(tmpCard[last_index[1]]) == 0x01 then
			table.insert(pos_index, 1, last_index)
			table.remove(pos_index)
		end
	end
	

	return pos_index
end

function GameLogic:getHuLu(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local tmp_list = {}
	
	for i = 1, num - 2 do
		for j = i + 1, num - 1 do
			local is_break = false
			if tmpCard[i] == 0 then
				break
			end
			if self:getCardValue(tmpCard[i]) == self:getCardValue(tmpCard[j]) then
				for k = j + 1, num do
					if self:getCardValue(tmpCard[i]) == self:getCardValue(tmpCard[k]) then
						is_break = true
						local list = {i, j, k}
						tmpCard[i] = 0
						tmpCard[j] = 0
						tmpCard[k] = 0
						table.insert(tmp_list, list)
						break
					end
				end
			end
			if is_break then
				break
			end				
		end
	end

	for i = 1, #tmp_list do
		local cp_cards = self:copyTab(cbCardData)
		for j = 1, #tmp_list[i] do
			cp_cards[tmp_list[i][j]] = 0
		end
		local list_dui = {}
		for i = 1, num - 1 do
			if cp_cards[i] ~= 0 then
				for j = i + 1, num do
					if self:getCardValue(cp_cards[i]) == self:getCardValue(cp_cards[j]) then
						cp_cards[i] = 0
						cp_cards[j] = 0
						local list = {i,j}
						table.insert(list_dui, list)
						break
					end
				end
			end
		end


		for j = 1, #list_dui do
			local list = {tmp_list[i][1], tmp_list[i][2], tmp_list[i][3], list_dui[j][1], list_dui[j][2]}
			table.insert(pos_index, list)
		end
	end
	--[[
	local list_dui = {}
	for i = 1, num - 1 do
		if cbCardData[i] ~= 0 then
			for j = i + 1, num do
				if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[j]) then
					cbCardData[i] = 0
					cbCardData[j] = 0
					local list = {i,j}
					table.insert(list_dui, list)
					break
				end
			end
		end
	end

	for i = 1, #tmp_list do
		for j = 1, #list_dui do
			local list = {tmp_list[i][1], tmp_list[i][2], tmp_list[i][3], list_dui[j][1], list_dui[j][2]}
			table.insert(pos_index, list)
		end
	end
	--]]

	return pos_index
end

function GameLogic:getTongHua(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local color = {}
	for i = 1, num do
		if cbCardData[i] ~= 0 then
			local cur_color = self:getCardColor(cbCardData[i])
			color[cur_color + 1] = color[cur_color + 1] or {}
			table.insert(color[cur_color + 1], i)
		end
		
	end
	print('color len is ', #color)
	for i = 1, 4 do
		local colors = color[i]
		if colors then
			local color_len = #colors
			if color_len >= 3 then
				for j = 1, color_len - 2 do
					for k = j + 1, color_len - 1 do
						for m = k +1, color_len  do
							
							local list = {colors[j], colors[k], colors[m]}
							table.sort(list, function (a, b) 
								local a_v = self:getCardValue(tmpCard[a])
								local b_v = self:getCardValue(tmpCard[b])
								if a_v == 0x01 then
									a_v = a_v + 0x0d
								end
								if b_v == 0x01 then
									b_v = b_v + 0x0d
								end
								if b_v < a_v then
									return true
								else
									return false
								end
							end)
							table.insert(pos_index, list)
						end
						
					end
					
				end
			end
		end
	end

	if #pos_index > 1 then
		table.sort(pos_index, function(a, b) 
			for i = 1, #a do
				local a_v = self:getCardValue(tmpCard[a[i]])
				local b_v = self:getCardValue(tmpCard[b[i]])
				if a_v == 0x01 then
					a_v = a_v + 0x0d
				end
				if b_v == 0x01 then
					b_v = b_v + 0x0d
				end
				if a_v > b_v then
					return true
				elseif a_v < b_v then
					return false
				end
			end
			for i = 1, #a do
				if tmpCard[a[i]] > tmpCard[b[i]] then
					return true
				else
					return false
				end
			end
			
		end)
	end

	return pos_index
end

function GameLogic:getTieZhi(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local tmp_list = {}
	
	for i = 1, num - 3 do
		for j = i + 1, num - 2 do
			local is_break = false
			if cbCardData[i] == 0 then
				break
			end
			if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[j])then
				for k = j + 1, num - 1 do
					if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[k]) then
						for m = k + 1, num do
							if self:getCardValue(cbCardData[i]) == self:getCardValue(cbCardData[m]) then
								is_break = true
								local list = {i, j, k, m}
								cbCardData[i] = 0
								cbCardData[j] = 0
								cbCardData[k] = 0
								cbCardData[m] = 0
								table.insert(pos_index, list)
								break
							end
						end						
					end
					if is_break then
						break
					end
				end
			end
			if is_break then
				break
			end				
		end
	end

--[[
	local list_dan = {}
	for i = 1, num do
		if cbCardData[i] ~= 0 then
			table.insert(list_dan, i)
		end
	end


	for i = 1, #tmp_list do
		for j = 1, #list_dan do
			local list = {tmp_list[i][1], tmp_list[i][2], tmp_list[i][3], tmp_list[i][4], list_dan[j]}
			table.insert(pos_index, list)
		end
	end
	--]]

	table.sort(pos_index, function (a, b) 
		if self:getCardValue(tmpCard[a[1]]) < self:getCardValue(tmpCard[b[1]]) then
			return false
		else
			return true
		end
	end)

	if #pos_index > 0 then
		local last_index = pos_index[#pos_index]
		if self:getCardValue(tmpCard[last_index[1]]) == 0x01 then
			table.insert(pos_index, 1, last_index)
			table.remove(pos_index)
		end
	end
	


	return pos_index
end

function GameLogic:getShunZi(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local tmpCardData = {}
	for i = 1, num do
		if tmpCard[i] ~= 0 then
			local val = self:getCardValue(tmpCard[i])
			local co = self:getCardColor(tmpCard[i])
			local list = {pos = i, value = val, color = co}
			if val == 1 then
				local cp_list = {pos = i, value = 14, color = co}
				table.insert(tmpCardData, cp_list)
			end
			table.insert(tmpCardData, list)
		end
		
	end

	table.sort(tmpCardData, function(a, b) 
		if a.value < b.value then
			return false
		elseif a.value == b.value then
			return a.color > b.color 
		else
			return true
		end
	end)

	for i = 1, #tmpCardData do
		print('shuzi data is ', tmpCardData[i].value, tmpCardData[i].pos, tmpCardData[i].color)
	end

	local tmp_pos_index = {}
	local len = #tmpCardData
	for i = 1, len - 2 do
		for j = i + 1, len - 1 do
			for k = j + 1, len do
				
				local a1 = tmpCardData[i].value - tmpCardData[j].value
				local a2 = tmpCardData[j].value - tmpCardData[k].value
				if a1 == a2 and a1 == 1 then
					local list = {tmpCardData[i], tmpCardData[j], tmpCardData[k] }
					print('shunzi list is ', tmpCardData[i].pos)
					table.sort(list, function(a, b)
						local a_v = a.value
						local b_v = b.value
						if a_v == 1 then
							a_v = 14
						end 
						if b_v == 1 then
							b_v = 14
						end
						if a_v > b_v then 
							return true 
						elseif a_v < b_v then
							return false
						else
							return a.color > b.color
						end
					end)
					table.insert(tmp_pos_index, list)
				end
				
			end
		end
	end

	table.sort(tmp_pos_index, function (tmpA, tmpB)
		local a = tmpA[1]
		local b = tmpB[1]
		local a_v = a.value
		local b_v = b.value
		if a_v == 1 then
			a_v = 14
		end 
		if b_v == 1 then
			b_v = 14
		end
		if a_v > b_v then 
			return true 
		elseif a_v < b_v then
			return false
		else
			return a.color > b.color
		end		
	end)

	for i = 1, #tmp_pos_index do
		local a = tmp_pos_index[i]
		local list = {a[1].pos, a[2].pos, a[3].pos}
		table.insert(pos_index, list)
	end

	return pos_index
end


function GameLogic:getTongHuaShun(cbCardData, num)
	local tmpCard = self:copyTab(cbCardData)
	local pos_index = {}
	local tmpCardData = {}
	for i = 1, num do
		if cbCardData[i] ~= 0 then
			local val = self:getCardValue(cbCardData[i])
			local co = self:getCardColor(cbCardData[i])
			local list = {pos = i, value = val, color = co}
			if val == 1 then
				local cp_list = {pos = i, value = 14, color = co}
				table.insert(tmpCardData, cp_list)
			end
			table.insert(tmpCardData, list)
		end
	end

	table.sort(tmpCardData, function(a, b) 
		if a.value < b.value then
			return false
		elseif a.value == b.value then
			return a.color > b.color 
		else
			return true
		end
	end)

	local len = #tmpCardData
	for i = 1, len - 2 do
		for j = i + 1, len - 1 do
			for k = j + 1, len do
				local a1 = tmpCardData[i].value - tmpCardData[j].value
				local a2 = tmpCardData[j].value - tmpCardData[k].value
						
				local c1 = tmpCardData[i].color
				local c2 = tmpCardData[j].color
				local c3 = tmpCardData[k].color
					
				if a1 == a2 and a1 == 1 then
					if c1 == c2 and c1 == c3 then
						local list = {tmpCardData[i].pos, tmpCardData[j].pos, tmpCardData[k].pos }
						table.insert(pos_index, list)
					end
					
				end
			end
		end
	end

	table.sort(pos_index, function (a, b) 
		local a_last_val = self:getCardValue(tmpCard[a[3]])
		local b_last_val = self:getCardValue(tmpCard[b[3]])
		local a_first_val = self:getCardValue(tmpCard[a[1]])
		local b_first_val = self:getCardValue(tmpCard[b[1]])
		if a_last_val == 0x01 and b_last_val == 0x01 then
			if a_first_val > b_first_val then
				return true
			elseif a_first_val == b_first_val then
				return tmpCard[a[3]] > tmpCard[b[3]] 	
			else
				return false
			end
		elseif a_last_val == 0x01 and b_last_val ~= 0x01 then
			return true
		elseif a_last_val ~= 0x01 and b_last_val == 0x01 then
			return false
		else
			if a_first_val > b_first_val then
				return true
			elseif a_first_val == b_first_val then
				return tmpCard[a[3]] > tmpCard[b[3]]

			else
				return false
			end
		end
	end)

	return pos_index
end

function GameLogic:getCardTypeIndex(cbCardData, type, num, start_index)
	local pos_index = nil
	local list_duizi = nil
	local bTempData = self:copyTab(cbCardData)

	if type == GameLogic.TYPE_DUIZI then
		pos_index = self:getDuizi(bTempData, num)
	elseif type == GameLogic.TYPE_SANTIAO then
		pos_index = self:getSanTiao(bTempData, num)
	elseif type == GameLogic.TYPE_TONGHUA then
		pos_index = self:getTongHua(bTempData, num)
	elseif type == GameLogic.TYPE_SHUNZI then
		pos_index = self:getShunZi(bTempData, num)
	elseif type == GameLogic.TYPE_TONGHUASHUN then
		pos_index = self:getTongHuaShun(bTempData, num)
	end
	
	return pos_index
end

function GameLogic:isQiangShui(type)
	
	if type >= GameLogic.OX_LIUDUI and  type <= GameLogic.OX_SANSHUNZI then
		return true
	end

	if type >= GameLogic.OX_SANFEN and  type <= GameLogic.OX_SANTONGHUASHUN then
		return true
	end

	return false
end

function GameLogic:AnalysebCardData(cbCardData, cbCardCount)
	local AnalyseResult = {}
	AnalyseResult.cbBlockCount = {0, 0, 0, 0}
	AnalyseResult.cbCardData = {}
	for i = 1, 4 do
		AnalyseResult.cbCardData[i] = {}
	end
	 
	local i = 1
	while i <= cbCardCount do
		local cbSameCount = 1
		local cbLogicValue = self:getCardLogicValue(cbCardData[i])

		--搜索同牌
		for j = i + 1, cbCardCount do
			if self:getCardLogicValue(cbCardData[j] ) ~= cbLogicValue then
				break
			end
			cbSameCount = cbSameCount + 1
        end
        
        if cbSameCount > 4 then
            print("这儿有错误")
            return
        end

		--设置结果
		local cbIndex = AnalyseResult.cbBlockCount[cbSameCount]
		AnalyseResult.cbBlockCount[cbSameCount] = AnalyseResult.cbBlockCount[cbSameCount] + 1
		--print('cbSameCount is ', cbSameCount)
		for j = 1, cbSameCount do
            --table.insert(AnalyseResult.cbCardData[cbSameCount], cbCardData[i + j - 1])
            AnalyseResult.cbCardData[cbSameCount][cbIndex * cbSameCount+j] = cbCardData[i + j - 1]
		end

		i = i + cbSameCount
	end
	--dump(AnalyseResult)

	return AnalyseResult
end

return GameLogic
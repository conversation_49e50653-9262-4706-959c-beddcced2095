local CardLayer = class("CardLayer", function(scene)
	local cardLayer = display.newLayer()
	return cardLayer
end)

local ExternalFun = cs.app.client('external.ExternalFun')
local extend = cs.app.client('system.extend')
local GameLogic = cs.app.game('room.GameLogic')
local cmd = cs.app.game('room.CMD_Game')

local CARD_PATH = 'card'

local LINHAI_MODE = true

local posBuhua = {cc.p(568, 507), cc.p(227, 354), cc.p(568, 204), cc.p(910, 354)}
local posHandCard = {cc.p(252, 556), cc.p(133, 150), cc.p(1082, 46), cc.p(980, 544)}
local posHandDownCard = {cc.p(515, 587), cc.p(137, 288), cc.p(481, 105), cc.p(1000, 310)}
local posDiscard = {cc.p(730, 439), cc.p(344, 487), cc.p(406, 281), cc.p(791, 196)}
local posBpBgCard = {cc.p(920, 580), cc.p(192, 518), cc.p(0, 87), cc.p(944, 164)}
local anchorPointHandCard = {cc.p(0, 0), cc.p(0, 0), cc.p(1, 0), cc.p(0, 1)}
local multipleTableCard = {{1, 0}, {0, 1}, {-1, 0}, {0, -1}}
local multipleDownCard = {{1, 0}, {0, -1}, {1, 0}, {0, -1}}
local multipleBpBgCard = {{-1, 0}, {0, -1}, {1, 0}, {0, 1}}
local posHuaOffs = {cc.p(3, 86), cc.p(-100, 3), cc.p(-3, -86), cc.p(100, -3)}

-- 所有牌的尺寸排列间隙定义
local pai = {
    -- 回放手牌(立着的)
    replay_hand = {
        {w=40, h=60, g=40, s=0.9, offset_x = 50, offset_y = 0, first_offset_x = -20, first_offset_y = 0},     -- 1号位（对家）
        {w=55, h=47, g=37, s=0.9, offset_x = -40, offset_y = 0, first_offset_x = 0, first_offset_y = -10},     -- 2号位（左家）
        {w=75, h=116,g=75, s=1.0, offset_x = 0, offset_y = 0, first_offset_x = 20, first_offset_y = 0},     -- 3号位（自己）
        {w=55, h=47, g=37, s=0.9, offset_x = 0, offset_y = 80, first_offset_x = 0, first_offset_y = 10},     -- 4号位（右家）
    },
    -- 摊牌手牌
    pile_hand = {
        {w=40, h=60, g=40, s=0.9, offset_x = 50, offset_y = 0, first_offset_x = -20, first_offset_y = 0},     -- 1号位（对家）
        {w=55, h=47, g=34, s=0.9, offset_x = -30, offset_y = -8, first_offset_x = 0, first_offset_y = -10},     -- 2号位（左家）
        {w=75, h=116,g=75, s=1.0, offset_x = 0, offset_y = 0, first_offset_x = 20, first_offset_y = 0},     -- 3号位（自己）
        {w=55, h=47, g=34, s=0.9, offset_x = 0, offset_y = -50, first_offset_x = 0, first_offset_y = 10},     -- 4号位（右家）
    },
    -- 手牌(立着的)
    hand = {
        {w=44, h=67, g=44,  first_offset_x = 0,  first_offset_y = 0},     -- 1号位（对家）
        {w=23, h=62, g=22,  first_offset_x = 0,  first_offset_y = 0},     -- 2号位（左家）
        {w=75, h=116,g=75,  first_offset_x = 20, first_offset_y = 0},     -- 3号位（自己）
        {w=23, h=62, g=22,  first_offset_x = 0, first_offset_y = 0},     -- 4号位（右家）
    },
    -- 堆牌（吃、碰、杠的堆）
    pile = {
        {w=40, h=60, g=40},     -- 1号位（对家）
        {w=55, h=47, g=36},     -- 2号位（左家）
        {w=54, h=79, g=54},     -- 3号位（自己）
        {w=55, h=47, g=36},     -- 4号位（右家）
    },
    -- 弃牌（打出去）
    out = {
        {w=40, h=60, g=40},     -- 1号位（对家）
        {w=55, h=47, g=36},     -- 2号位（左家）
        {w=40, h=60, g=40},     -- 3号位（自己）
        {w=55, h=47, g=36},     -- 4号位（右家）
    },
    -- 暗牌（扑在桌面的暗牌）
    back = {
        {w=44, h=67, g=44},     -- 1号位（对家）
        {w=55, h=47, g=36},     -- 2号位（左家）
        {w=75, h=116, g=75},    -- 3号位（自己）
        {w=55, h=47, g=36},     -- 4号位（右家）
    }
}

-- 手牌背景
local hand_card_bgs = {
	CARD_PATH .. "/card_back_up.png",
	CARD_PATH .. "/card_left.png", 
	CARD_PATH .. "/font_big/card_up.png",
	CARD_PATH .. "/card_right.png"
}
-- 手牌回放背景
local replay_card_bgs = {
	CARD_PATH .. "/font_small/card_down.png",
	CARD_PATH .. "/font_small_side/card_down.png", 
	CARD_PATH .. "/font_big/card_up.png",
	CARD_PATH .. "/font_small_side/card_down.png"
}
-- 手牌摊牌背景
local pile_card_bgs = {
	CARD_PATH .. "/font_small/card_down.png",
	CARD_PATH .. "/font_small_side/card_down.png", 
	CARD_PATH .. "/font_big/card_down.png",
	CARD_PATH .. "/font_small_side/card_down.png"
}

local CARD_HEIGHT = 116
local POP_HEIGHT = 30

local out_row_limit = {h=10, v=10}  -- 水平和垂直弃牌一行数量

local SIDE_BACK_SCALE = 0.73       -- 左右两侧平铺时需要缩放的比例
local DISCARD_SCALE = 0.9          -- 弃牌缩放比例
local DISCARD_HUA_SCALE = 0.75     -- 弃牌花牌缩放比例
local SIDE_CPG_CARD_SCALE = 0.75   -- 左右吃碰杠缩放比例
local COLOR_WHITE = display.COLOR_PURE_WHITE

CardLayer.TAG_BUMPORBRIDGE = 1
CardLayer.TAG_CARD_FONT = 1
CardLayer.TAG_LISTEN_FLAG = 2

CardLayer.ENUM_CARD_NORMAL = nil
CardLayer.ENUM_CARD_POPUP = 1
CardLayer.ENUM_CARD_MOVING = 2
CardLayer.ENUM_CARD_OUT = 3

CardLayer.Z_ORDER_TOP = 50
CardLayer.Z_ORDER_FLAG = 3

function CardLayer:onInitData()
	--body
	math.randomseed(os.time())
	self.cbCardData = {}
	self.cbCardCount = { cmd.MAX_COUNT, cmd.MAX_COUNT, cmd.MAX_COUNT, cmd.MAX_COUNT}
	self.nDiscardCount = {0, 0, 0, 0}
	self.nBpBgCount = {0, 0, 0, 0}
	self.nHuaCount = {0, 0, 0, 0}
	self.bCardGray = {}
	self.cbCardStatus = {}
	self.nCurrentTouchCardTag = 0
	self.currentTag = 0
	self.cbListenList = {}
	self.bWin = false
	self.currentOutCard = 0
	self.nTableTailCardTag = 112
	self.nRemainCardNum = 0
	self.posTemp = cc.p(0, 0)
	self.nZOrderTemp = 0
	self.nMovingIndex = 0
	self.bSendOutCardEnabled = false
	self.cbBpBgCardData = {{}, {}, {}, {}}
    self.bIsSpreadCardFinish = true
    self.cbPlayerMaxCardsNum = 14
    self.cbPlayerMaxWeaveNum = 4
end

function CardLayer:onResetData()
	--body
	for i = 1, cmd.GAME_PLAYER do
		self.cbCardCount[i] = cmd.MAX_COUNT
		self.nDiscardCount[i] = 0
		self.nHuaCount[i] = 0
		self.nBpBgCount[i] = 0
        -- 出的牌
        self.nodeDiscard[i]:removeAllChildren()
		-- 吃碰杠的牌
        self.nodeBpBgCard[i]:removeAllChildren()
		for j = 1, cmd.MAX_COUNT do
			local card = self.nodeHandCard[i]:getChildByTag(j)
            card:scale(card.first_scale)
            card:move(card.first_pos)
            card:texture(hand_card_bgs[i])
            card:removeAllChildren()
			card:setColor(COLOR_WHITE)
			card:hide()
			self.nodeHandDownCard[i]:getChildByTag(j):hide()
		end
	end
	self.cbCardData = {}
	self.bCardGray = {}
	self.cbCardStatus = {}
	self.nCurrentTouchCardTag = 0
	self.nMovingIndex = 0
	self.nZOrderTemp = 0
	self.currentTag = 0
	self.cbListenList = {}
	self.bWin = false
	self.currentOutCard = 0
	self.nTableTailCardTag = 112
	self.nRemainCardNum = 0
	self.posTemp = cc.p(0, 0)
	self:promptListenOutCard(nil)
    self:flagDiscard(false)
	self.bSendOutCardEnabled = false
	self.cbBpBgCardData = {{}, {}, {}, {}}
    self.nodeDiscardCard = {}
end

function CardLayer:ctor(scene)
    CARD_PATH = GlobalUserItem.getCardPath()    -- 重置为当前玩法的资源路径

	self._scene = scene
	self:onInitData()

	ExternalFun.registerTouchEvent(self, true)
	--桌牌
	--self.nodeTableCard = self:createTableCard()
	--手牌
	self.nodeHandCard = self:createHandCard()
	--铺着的手牌
	self.nodeHandDownCard = self:createHandDownCard()
	--弃牌
	self.nodeDiscard = self:createDiscard()
	--碰或杠牌
	self.nodeBpBgCard = self:createBpBgCard()
end


-------------------------------------------------------------------------------
-- 设置改变,重新设置牌面缩放
-------------------------------------------------------------------------------
function CardLayer:onSettingChange()
    local scale = GlobalUserItem.nCardFontScale / 100
    local reset = function(container)
        local children = container:getChildren()
        for i, card in ipairs(children) do
            local font = card:getChildByTag(1)
            if font then
                font:scale(scale)
            end
        end
    end
	for i = 1, cmd.GAME_PLAYER do
	    reset( self.nodeDiscard[i] )
	    reset( self.nodeBpBgCard[i] )
	    reset( self.nodeHandCard[i] )
    end
end

--生成手牌位置信息
function CardLayer:createHandCardInfo(i, config)
	local bVert = i == 1 or i == cmd.MY_VIEWID
	local width = config[i].w
	local height = config[i].h
    local scale = config[i].s or 1
	local fSpacing = config[i].g * scale
    local offset_x = config[i].offset_x or 0
    local offset_y = config[i].offset_y or 0
    local first_offset_x = config[i].first_offset_x
    local first_offset_y = config[i].first_offset_y
	local widthTotal = fSpacing * cmd.MAX_COUNT
	local heightTotal = height + fSpacing * ( cmd.MAX_COUNT - 1)
    local arr = {}
	for j = 1, cmd.MAX_COUNT do
        local pos = nil
		if i == 1 then
			pos = cc.p(width/2 + fSpacing*(j - 1) + offset_x, height/2 + offset_y)
		elseif i == 2 then
			pos = cc.p(width/2 + offset_x, height/2 + fSpacing*(j - 1) + offset_y)
		elseif i == cmd.MY_VIEWID then
			pos = cc.p(widthTotal - width/2 - fSpacing*(j - 1) + offset_x, height/2 + offset_y)
		elseif i == 4 then
			pos = cc.p(width/2 + offset_x, heightTotal - height/2 - fSpacing*(j - 1) + offset_y)
		end

        -- 第一张牌 特殊处理
        if j == 1 then
            pos = cc.p( pos.x + first_offset_x, pos.y + first_offset_y )
        end

        arr[j] = pos
    end

    return {bVert=bVert, width=width, height=height, widthTotal=widthTotal, heightTotal=heightTotal, scale=scale, pos=arr}
end

--创建立着的手牌
function CardLayer:createHandCard()
	local res = yl.IS_REPLAY_MODEL and replay_card_bgs or hand_card_bgs
	local nodeCard = {}
	for i = 1, cmd.GAME_PLAYER do
        local info = self:createHandCardInfo(i, yl.IS_REPLAY_MODEL and pai.replay_hand or pai.hand)

		nodeCard[i] = cc.Node:create()
			:move(posHandCard[i])
			:setContentSize(info.bVert and cc.size(info.widthTotal, info.height) or cc.size(info.width, info.heightTotal))
			:setAnchorPoint(anchorPointHandCard[i])
			:addTo(self, 1)
		for j = 1, cmd.MAX_COUNT do
            local pos = info.pos[j]
			local card = display.newSprite(res[i])
				:move(pos)
				:setTag(j)
				:setVisible(false)
                :scale(info.scale)
				:addTo(nodeCard[i])
            card.first_pos = pos
            card.first_scale = info.scale
			if i == cmd.MY_VIEWID or yl.IS_REPLAY_MODEL then
				--local cardData = GameLogic.MAGIC_DATA
                local cardData = 0x01
                cs.game.util.createCard({card, cardData}, i, 'hand')
				--提示听牌的小标记
                if cs.game.TING_PROMPT then
				    display.newSprite("room/icon_prompt_arrow.png")
					    :move(69/2, CARD_HEIGHT + 25)
					    :setTag(CardLayer.TAG_LISTEN_FLAG)
					    :setVisible(false)
					    :addTo(card)
                end
            end
            if i == 2 then
                card:setLocalZOrder(30 - j)	--修改了Z轴顺序，设置重叠效果   **** 需注意处理 ******
			end
		end
	end
	nodeCard[cmd.MY_VIEWID]:setLocalZOrder(2)

	return nodeCard
end

--创建铺着的手牌
function CardLayer:createHandDownCard()
	local fSpacing = {pai.back[1].g, pai.back[2].g, pai.back[3].g, pai.back[4].g}
	local res = 
	{
		CARD_PATH .. "/font_small/card_back.png",
		CARD_PATH .. "/font_small_side/card_back.png",
		CARD_PATH .. "/font_big/card_back.png",
		CARD_PATH .. "/font_small_side/card_back.png"
	}
	local nodeCard = {}
	for i = 1, cmd.GAME_PLAYER do
		nodeCard[i] = cc.Node:create()
			:move(posHandDownCard[i])
			:setAnchorPoint(cc.p(0.5, 0.5))
            :scale((i == 1 or i == 3) and 1 or SIDE_BACK_SCALE)
			:addTo(self, 2)
		for j = 1, cmd.MAX_COUNT do
			local card = display.newSprite(res[i])
				:setVisible(false)
				:setTag(j)
				:addTo(nodeCard[i])
			if i == 1 or i == 3 then
				card:move(fSpacing[i]*j, 0)
			else
				card:move(0, -fSpacing[i]*j)
			end
			if i == 2 then
				--card:setLocalZOrder(30 - j)
			end
		end
        if i == cmd.MY_VIEWID then
            nodeCard[i]:zorder(3)   -- 3号位需最高
        end
	end

	return nodeCard
end

--创建弃牌
function CardLayer:createDiscard()
	local nodeCard = {}
	for i = 1, cmd.GAME_PLAYER do
		nodeCard[i] = cc.Node:create()
			:move(posDiscard[i])
			:addTo(self)
	end
	nodeCard[1]:setLocalZOrder(2)

	return nodeCard
end
--创建碰或杠牌
function CardLayer:createBpBgCard()
	local nodeCard = {}
	for i = 1, cmd.GAME_PLAYER do
		nodeCard[i] = cc.Node:create()
			:move(posBpBgCard[i])
			:addTo(self)
	end
	nodeCard[cmd.MY_VIEWID]:setLocalZOrder(2)
	nodeCard[4]:setLocalZOrder(1)

	return nodeCard
end
function CardLayer:onTouchBegan(touch, event)
	local pos = touch:getLocation()
    self.touchPos = pos
    local posRelativeCard = self.nodeHandCard[cmd.MY_VIEWID]:convertToNodeSpace(pos)
    local cardParent = self.nodeHandCard[cmd.MY_VIEWID]
    -- print('测试牌', cardParent:getScale(), cardParent:isVisible())
	for i = 1, cmd.MAX_COUNT do
		local card = cardParent:getChildByTag(i)
		local cardRect = card:getBoundingBox()
		if cc.rectContainsPoint(cardRect, posRelativeCard) and card:isVisible() and not self.bCardGray[i] then
			--print("touch begin!", pos.x, pos.y)
			self.nCurrentTouchCardTag = i
			--缓存
			--self.posTemp.x, self.posTemp.y = card:getPosition()
            self.posTemp.x = card.first_pos.x
            self.posTemp.y = card.first_pos.y
			self.nZOrderTemp = card:getLocalZOrder()

            if LINHAI_MODE then
                card:py(CARD_HEIGHT/2 + POP_HEIGHT) -- 点击直接设置到弹出位置
                self.m_touch_offset = cc.pSub(cc.p(card:pos()), posRelativeCard)
            end

            self._scene._scene:PlaySound(cmd.RES_PATH.."sound/TOUCH_CARD.wav")

			--将牌补满(ui与值的对齐方式)
			local nCount = 0
			local num = math.mod(self.cbCardCount[cmd.MY_VIEWID], 3)
			if num == 2 then
				nCount = self.cbCardCount[cmd.MY_VIEWID]
			elseif num == 1 then
				nCount = self.cbCardCount[cmd.MY_VIEWID] + 1
			else
				assert(false)
			end
			local index = nCount - i + 1
            ---- 刷新牌
            self._scene._scene:updateCardInfo( true, self.cbCardData[index] )
			return true
		end
	end
    self.nCurrentTouchCardTag = 0
    local posY = CARD_HEIGHT /2
    --规避没点到牌的情况
    for i = 1, cmd.MAX_COUNT do
		local cardTemp = cardParent:getChildByTag(i)
		cardTemp:py(posY)
		cardTemp:setLocalZOrder(self.nZOrderTemp)
		self.cbCardStatus = {}
        self._scene._scene:updateCardInfo( false )
	end
	return false
end
function CardLayer:onTouchMoved(touch, event)
	local pos = touch:getLocation()
	--print("touch move!", pos.x, pos.y)
    local posRelativeCard = self.nodeHandCard[cmd.MY_VIEWID]:convertToNodeSpace(pos)
    --移动
	if self.nCurrentTouchCardTag ~= 0 then
		--将牌补满(ui与值的对齐方式)
		local nCount = 0
		local num = math.mod(self.cbCardCount[cmd.MY_VIEWID], 3)
		if num == 2 then
			nCount = self.cbCardCount[cmd.MY_VIEWID]
		elseif num == 1 then
			nCount = self.cbCardCount[cmd.MY_VIEWID] + 1
		else
			assert(false)
		end
		local index = nCount - self.nCurrentTouchCardTag + 1

		local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(self.nCurrentTouchCardTag)
		local dis = cc.pGetDistance(pos, self.touchPos)
        local delta = LINHAI_MODE and 5 or 30
        if dis > delta or self.cbCardStatus[index] == CardLayer.ENUM_CARD_MOVING then
        	card:setPosition(cc.pAdd(posRelativeCard, self.m_touch_offset or cc.p(0, 0)))
		    card:setLocalZOrder(CardLayer.Z_ORDER_TOP)
            self.cbCardStatus[index] = CardLayer.ENUM_CARD_MOVING
        end
	    -- 提示听牌
        if cs.game.TING_PROMPT then
		    local cbPromptHuCard = self._scene._scene:getListenPromptHuCard(self.cbCardData[index])
		    if self.nMovingIndex ~= index and self:checkIsMyTurn() then
			    self._scene:setListeningCard(cbPromptHuCard)
		    end
        end
		self.nMovingIndex = index
	end

	return true
end
function CardLayer:onTouchEnded(touch, event)
    local pos = touch:getLocation()
    -- --print("touch end!", pos.x, pos.y)
    local posRelativeCard = self.nodeHandCard[cmd.MY_VIEWID]:convertToNodeSpace(pos)
    local posY = CARD_HEIGHT/2
    local posUpY = CARD_HEIGHT/2 + POP_HEIGHT
    local posOutY = 190
    local cardParent = self.nodeHandCard[cmd.MY_VIEWID]
    local parentPos = cardParent:convertToNodeSpace(pos)
    for i = 1, cmd.MAX_COUNT do
		local card = cardParent:getChildByTag(i)
        local cardY = card:py()
		local cardRect = card:getBoundingBox()
		--if cc.rectContainsPoint(cardRect, posRelativeCard) and card:isVisible() and not self.bCardGray[i] then
        if self.nCurrentTouchCardTag == i then
			--将牌补满(ui与值的对齐方式)
			local nCount = 0
			local num = math.mod(self.cbCardCount[cmd.MY_VIEWID], 3)
			if num == 2 then
				nCount = self.cbCardCount[cmd.MY_VIEWID]
			elseif num == 1 then
				nCount = self.cbCardCount[cmd.MY_VIEWID] + 1
			else
				assert(false)
			end
			local index = nCount - i + 1		--算出这张牌对应牌值在self.cbCardData里的下标(cbCardData与cbCardStatus保持一致)
			if self.nCurrentTouchCardTag == i then
				if self.cbCardStatus[index] == CardLayer.ENUM_CARD_NORMAL then 		--原始状态
					--恢复
					self.cbCardStatus = {}
					for v = 1, cmd.MAX_COUNT do
						local cardTemp = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(v)
						cardTemp:setPositionY(posY)
					end
					--弹出
					self.cbCardStatus[index] = CardLayer.ENUM_CARD_POPUP
					card:setPositionY(posUpY)
					
                    -- 提示听牌
                    if cs.game.TING_PROMPT then
					    if self:checkIsMyTurn() then
						    local cbPromptHuCard = self._scene._scene:getListenPromptHuCard(self.cbCardData[index])
						    self._scene:setListeningCard(cbPromptHuCard)
					    end
                    end

				elseif self.cbCardStatus[index] == CardLayer.ENUM_CARD_POPUP then 		--弹出状态
					if self:checkIsMyTurn() then
						--出牌
                        self:touchSendOutCard(self.cbCardData[index])
                        card:setPosition(self.posTemp.x, posY)
					else
						--弹回
					end
					self.cbCardStatus[index] = CardLayer.ENUM_CARD_NORMAL
					card:setPositionY(posY)
				elseif self.cbCardStatus[index] == CardLayer.ENUM_CARD_MOVING then 		--移动状态
					--恢复
					self.cbCardStatus = {}
					for v = 1, cmd.MAX_COUNT do
						local cardTemp = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(v)
						cardTemp:setPositionY(posY)
					end
					self.cbCardStatus[index] = CardLayer.ENUM_CARD_POPUP
                    --print('当前的移动位置pos.y',parentPos.y)
					if cardY >= posUpY then
					   card:setPosition(self.posTemp.x, posUpY)
                    else
                       card:setPosition(self.posTemp.x, posY)
                       self.cbCardStatus[index] = CardLayer.ENUM_CARD_NORMAL
					end
                    card:setLocalZOrder(self.nZOrderTemp)
					--判断
					--local rectDiscardPool = cc.rect(324, 218, 686, 283)
					if self:checkIsMyTurn() and cardY >= posUpY + 10 then
						--出牌"
                        self:touchSendOutCard(self.cbCardData[index]) 
                        card:setPosition(self.posTemp.x, posY)
					end
				elseif self.cbCardStatus[index] == CardLayer.ENUM_CARD_OUT then 		--已出牌状态
					assert(false)
				end
				break
			end
		end
		--规避没点到牌的情况
		if i == cmd.MAX_COUNT then
			local cardTemp = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(self.nCurrentTouchCardTag)
            if cardTemp then
                cardTemp:setPosition(self.posTemp)
			    cardTemp:setLocalZOrder(self.nZOrderTemp)
            end
			self.cbCardStatus = {}
		end
	end
	self.nCurrentTouchCardTag = 0
	self.nZOrderTemp = 0
	return true
end

--检测是否是自己的回合
function CardLayer:checkIsMyTurn()
    return math.mod(self.cbCardCount[cmd.MY_VIEWID], 3) == 2
end

-- 初始化手牌
function CardLayer:initHandCard()
    local viewID = cmd.MY_VIEWID
    if #self.cbCardData > 0 then
        local cardParent = self.nodeHandCard[cmd.MY_VIEWID]
        for j = 1, cmd.MAX_COUNT do    
            local cardTemp = cardParent:getChildByTag(j)
            cardTemp:move(cardTemp.first_pos)
        end
        self.cbCardStatus = {}
        self.nCurrentTouchCardTag = 0
    end
end

--发牌
function CardLayer:sendCard(cmd_data, cbCardData, cardCount)
	assert(type(cbCardData) == "table" and type(cardCount) == "table")
    self.bIsSpreadCardFinish = false
	self.cbCardData = cbCardData
	self.cbCardCount = cardCount
    --dump(cmd_data, 'cmd_data', 9)
	self:updateRemainCardNum(cmd_data.cbLeftCardCount)
    GameLogic.SortCardList(self.cbCardData)
    local fDelayTime = 0.1
    local percent = cmd.NORMAL_MAX_COUNT / self:getPlayerMaxCardsNum()
	for i = 1, cmd.GAME_PLAYER do
		-- 测试 牌大小 调整
        if cardCount[i] > 0 then
			self:spreadCard(i, fDelayTime, 1)
		end
        if i == 2 or i == 4 then
            self.nodeHandDownCard[i]:scale( percent*SIDE_BACK_SCALE )
        else
            self.nodeHandDownCard[i]:scale( percent )
        end
	end
	local fDelayTimeMax = fDelayTime * cmd.MAX_COUNT + 0.3
	self:runMyAction(cc.Sequence:create(
		cc.DelayTime:create(fDelayTimeMax),
		cc.CallFunc:create(function(ref)
			self:spreadCardFinish()
		end)))
end

-- 获取手牌上限
function CardLayer:getPlayerMaxCardsNum()
    return math.max(self.cbPlayerMaxCardsNum, 14)
end

--伸展牌
function CardLayer:spreadCard(viewId, fDelayTime, nCurrentCount)
    local actions = {}
    for index = 1, self:getPlayerMaxCardsNum() do
        if index > self.cbCardCount[viewId] then
            break
        end
        table.insert(actions, cc.DelayTime:create(fDelayTime))
        table.insert(actions, cc.CallFunc:create(function(ref)
		    local nodeParent = self.nodeHandDownCard[viewId]
            local downCard = nodeParent:getChildByTag(index)
		    local downCardSize = downCard:getContentSize()
		    downCard:setVisible(true)
            if viewId == 1 or viewId == 3 then
			    nodeParent:setPositionX(posHandDownCard[viewId].x - downCardSize.width/2 * (index - 1))
		    else
			    nodeParent:setPositionY(posHandDownCard[viewId].y + downCardSize.height/2 * (index - 1) * SIDE_BACK_SCALE)
		    end
            self._scene._scene:PlaySound(cmd.RES_PATH.."sound/OUT_CARD.wav")		
	    end))
    end
    self:runMyAction(cc.Sequence:create(actions))
end

--发完牌
function CardLayer:spreadCardFinish()
	for i = 1, cmd.GAME_PLAYER do
		for j = 1, cmd.MAX_COUNT do
			self.nodeHandDownCard[i]:getChildByTag(j):setVisible(false)
		end
		self:setHandCard(i, self.cbCardCount[i], self.cbCardData)
	end
    -- 发牌完毕 标记 
    self.bIsSpreadCardFinish = true
    self._scene._scene.bSendCardFinsh = true
end

--抓牌
function CardLayer:catchCard(viewId, cmd_data)
    local cbCardData = cmd_data.cbCardData
    local bTail = cmd_data.bTail
    local cbLeftCardCount = cmd_data.cbLeftCardCount
    self:updateRemainCardNum( cbLeftCardCount )

    --printf( "玩家方位 %d 开始抓牌%d", viewId, cbCardData)
	assert(math.mod(self.cbCardCount[viewId], 3) == 1 or bTail, "Can't catch card!")
	self._scene._scene:playRandomSound(viewId)

	local HandCard = self.nodeHandCard[viewId]:getChildByTag(1)
    if self.bIsSpreadCardFinish then
	    HandCard:setVisible(true)
	end
    self.cbCardCount[viewId] = self.cbCardCount[viewId] + 1

    local cbCurCardData = self.cbCardData
    if yl.IS_REPLAY_MODEL then
        cbCurCardData = self.cbAllCardData[viewId]
    end

	if viewId == cmd.MY_VIEWID or yl.IS_REPLAY_MODEL then
		table.insert(cbCurCardData, cbCardData)
		--设置纹理
        cs.game.util.createCard( {HandCard, cbCardData}, viewId, 'hand' )
		--假如可以听牌
        if cs.game.TING_PROMPT then
		    local cbPromptCardData = self._scene._scene:getListenPromptOutCard()
		    if #cbPromptCardData > 0 then
			    self:promptListenOutCard(cbPromptCardData)
		    end
        end
	end
    --[[
    if cmd_data.bIsTouchControl and viewId == cmd.MY_VIEWID and not yl.IS_REPLAY_MODEL then
        self:makeCardGray( cmd_data.cbTouchControlCards[1] )
    end
    --]]
end

--检测受限制的牌
function CardLayer:checkControlCards(viewId, cbTouchControlCards)
    if viewId == cmd.MY_VIEWID and not yl.IS_REPLAY_MODEL then
        self:makeCardGray( cbTouchControlCards )
    end
end

--设置本方牌值（is_over_pile：局结束摊牌，此时meData是每个玩家各自的数据）
function CardLayer:setHandCard(viewId, cardCount, meData, is_over_pile)
	assert(type(meData) == "table")
    if not is_over_pile then
	    self.cbCardData = meData
	    self.cbCardCount[viewId] = cardCount
	    self.bSendOutCardEnabled = true
    end

    -- 测试 牌大小 调整
    local percent = cmd.NORMAL_MAX_COUNT / self:getPlayerMaxCardsNum()
    self.nodeHandCard[viewId]:scale( percent )
	--先全部隐藏
	for j = 1, cmd.MAX_COUNT do
		self.nodeHandCard[viewId]:getChildByTag(j):setVisible(false)
	end
	--再显示
	if cardCount ~= 0 then
		local nCount = 0
		local num = math.mod(cardCount, 3)
		if num == 2 then
			nCount = cardCount
		elseif num == 1 then
			nCount = cardCount + 1
		else
			assert(false)
		end
        local cbCardData = self.cbCardData
        if yl.IS_REPLAY_MODEL then
            cbCardData = self.cbAllCardData[viewId]
        elseif is_over_pile then
            cbCardData = meData
        end
        local info = not yl.IS_REPLAY_MODEL and is_over_pile and self:createHandCardInfo(viewId, pai.pile_hand) or nil
        local pile_font_offset = {cc.p(-2, -2), cc.p(16, -8), cc.p(0, 23), cc.p(16, -8)}
		for j = 1, cardCount do
			local card = self.nodeHandCard[viewId]:getChildByTag(nCount - j + 1)
			card:setVisible(true)
			if viewId == cmd.MY_VIEWID or yl.IS_REPLAY_MODEL or is_over_pile then
                --dump( self.cbCardCount )
                --printf(" set cbCardData %d  index %d  view %d num %d ", #cbCardData, j, viewId, self.cbCardCount[viewId])
                --print("牌", cbCardData[j], "位置", j, "长度", self.cbCardCount[viewId], '牌长度', #cbCardData )
                cs.game.util.createCard( {card, cbCardData[j]}, viewId, 'hand' )
                if not yl.IS_REPLAY_MODEL and is_over_pile then
                    card:texture(pile_card_bgs[viewId])
                    card:scale(info.scale)
                    card:pos(info.pos[card:getTag()])
                    local font = card:getChildByTag(1)
                    font:pos(cc.pAdd(cc.p(font:pos()), pile_font_offset[viewId]))
                end
			end
		end
	end
end

--删除手上的牌
function CardLayer:removeHandCard(viewId, cardData, bOutCard)
	assert(type(cardData) == "table")
	local cbRemainCount = self.cbCardCount[viewId] - #cardData
	if bOutCard and math.mod(cbRemainCount, 3) ~= 1 then
		return false
	end
    --print( "方位", viewId )
    --printf( "删除的牌数量%d   %d     %d", cbRemainCount, self.cbCardCount[viewId], #cardData )
	self.cbCardCount[viewId] = cbRemainCount
    local cbCurCardData = self.cbCardData
    if yl.IS_REPLAY_MODEL then
        cbCurCardData = self.cbAllCardData[ viewId ]
        --dump( self.cbAllCardData, "全部的牌", 3 )
        --print( "位置", index )
    end
    --printf( "删除的牌数量%d   %d     %d", cbRemainCount, self.cbCardCount[viewId], #cardData )
    --dump( cardData, "删除的牌", 9 )
    --dump( cbCurCardData, "手中的牌", 9)
	if viewId == cmd.MY_VIEWID or yl.IS_REPLAY_MODEL then
		for i = 1, #cardData do
			for j = 1, #cbCurCardData do
				if cbCurCardData[j] == cardData[i] then
					--printf( "remove card %d", cbCurCardData[j] )
                    table.remove( cbCurCardData, j )
					break
				end
                assert(j ~= #cbCurCardData, "WithOut this card to remove!")
			end
		end
		GameLogic.SortCardList( cbCurCardData )
	end
	self:setHandCard(viewId, self.cbCardCount[viewId], self.cbCardData)
	return true
end

--添加手牌
function CardLayer:addHandCard( viewId, cardData )
    assert(type(cardData) == "table")
    --dump(cardData, "添加牌", 9)
	local cbRemainCount = self.cbCardCount[viewId] + #cardData
    self.cbCardCount[viewId] = cbRemainCount
    local cbCurCardData = self.cbCardData
    if yl.IS_REPLAY_MODEL then
        cbCurCardData = self.cbAllCardData[ viewId ]
    end
    if viewId == cmd.MY_VIEWID or yl.IS_REPLAY_MODEL then
        for i = 1, #cardData do
            table.insert(cbCurCardData, cardData[i])
		end
		GameLogic.SortCardList( cbCurCardData )
	end
    self:setHandCard(viewId, self.cbCardCount[viewId], self.cbCardData)
	return true
end

-------------------------------------------------------------------------------
-- 出牌标记
-------------------------------------------------------------------------------
function CardLayer:flagDiscard(is_show, card)
    local sp_flag = self.m_discard_flag
    if not is_show then
        if sp_flag then
            sp_flag:stop():hide()
        end
        return
    end

    if not sp_flag then
        sp_flag = display.newSprite('room/icon_discard_flag.png')
        sp_flag:anchor(0.5, 0):zorder(CardLayer.Z_ORDER_FLAG):addTo(self)
        self.m_discard_flag = sp_flag
    end

    -- 计算牌中心点实际所在坐标
    local size = card:size()
    local pos = card:convertToWorldSpace( cc.p(size.width/2, size.height/2) )

    sp_flag:stop():show():pos(pos)
    sp_flag:runMyAction( cc.RepeatForever:create( cc.Sequence:create(
        cc.MoveBy:create(0.4, cc.p(0, 8)),
        cc.MoveBy:create(0.4, cc.p(0, -8))
    ) ) )
end

--牌打到弃牌堆
function CardLayer:discard(viewId, cardData, no_buhua_effect, no_flag)
    local card_index = GameLogic.SwitchToCardIndex(cardData)
    local is_hua = self.bCurHuaMak[card_index]
    local is_pi = GameLogic.IsPiCard(cardData)     
    if is_pi then
        is_hua = is_pi
    end
    local scale = is_hua and DISCARD_HUA_SCALE or DISCARD_SCALE
	local width = 0
	local height = 0
	local fSpacing = 0
	local posX = 0
	local posY = 0
	local pos = cc.p(0, 0)
	local nLimit = 0
	local fBase = 0
    local hua_h_offset, hua_v_offset = {}
	local count = self.nDiscardCount[viewId]
    if is_hua then
        count = self.nHuaCount[viewId]
    end
	local countTemp = count
	local bVert = viewId == 1 or viewId == cmd.MY_VIEWID
	if bVert then
		width = pai.out[1].w * scale
		height = pai.out[1].h * scale
		fSpacing = pai.out[1].g * scale
		nLimit = not is_hua and out_row_limit.h or 30

		while countTemp >= nLimit*2 do   	--超过一层
			fBase = fBase + height - 47 * scale
			countTemp = countTemp - nLimit*2
		end

		while countTemp >= nLimit do 		--超过一行
			posY = posY - 47 * scale
			countTemp = countTemp - nLimit
		end

		local posX = fSpacing*countTemp
		pos = viewId == cmd.MY_VIEWID and cc.p(posX, posY + fBase) or cc.p(-posX, -posY + fBase)
	else
		width = pai.out[2].w * scale
		height = pai.out[2].h * scale
		fSpacing = pai.out[2].g * scale
		nLimit = not is_hua and out_row_limit.v or 30

		while countTemp >= nLimit*2 do 		--超过一层
			fBase = fBase + 11 * scale
			countTemp = countTemp - nLimit*2
		end

		while countTemp >= nLimit do 		--超过一行
			posX = posX - width
			countTemp = countTemp - nLimit
		end

		local posY = fSpacing*countTemp
		pos = viewId == 4 and cc.p(-posX, posY + fBase) or cc.p(posX, -posY + fBase)
	end

    -- 花偏移
    if is_hua then
        pos = cc.pAdd(pos, posHuaOffs[viewId])
    end

    --print('已经出的牌:', cardData)

	--牌底
	local card = cs.game.util.createCard(cardData, viewId, 'out')
		:move(pos)
        :scale(scale)
		:setTag(count + 1 + (is_hua and 1000 or 0)) -- 花的tag要独立
		:addTo(self.nodeDiscard[viewId])            -- 花同样加入相同父节点，统一删除

    if not is_hua and not no_flag then
        self:flagDiscard(true, card)
    end

	--修改了Z轴顺序，设置重叠效果   **** 需注意处理 ******
	if viewId == 1 or viewId == 4 then
		local nOrder = 0
		if count >= nLimit*6 then
			assert(false)
		elseif count >= nLimit*4 then
			nOrder = 80 - (count - nLimit*4)
		elseif count >= nLimit*2 then
			nOrder = 80 - (count - nLimit*2)*2
		else
			nOrder = 80 - count*3
		end
		card:setLocalZOrder(nOrder)
	end
    if is_hua then
        local order = card:getLocalZOrder()
        if viewId == 1 then       -- 1号位置花牌确保整天最低
            card:zorder(order - 80)
        elseif viewId == 3 then   -- 3号位置花牌确保整天最高
            card:zorder(order + 80)
        end
    end

	-- 计数
    (is_hua and self.nHuaCount or self.nDiscardCount)[viewId] = count + 1

    -- 补花特效
    if is_hua and not no_buhua_effect and not is_pi then
        self._scene:showOperateFlag( viewId, GameLogic.WIK_BU_HUA )
        --[[
        local sprite = display.newSprite('room/icon_buhua.png')
        sprite:scale(0):runMyAction(cc.Sequence:create(
            cc.EaseBackOut:create(cc.ScaleTo:create(0.2, 1)),
            cc.DelayTime:create(1),
            cc.EaseBackIn:create(cc.ScaleTo:create(0.2, 0)),
            cc.RemoveSelf:create(true)
        ))
        sprite:zorder(200):pos(posBuhua[viewId]):addTo(self)
        --]]
        self._scene._scene:playCardOperateSound(viewId, true)   -- GameLayer
    end

    return card, is_hua
end

--从弃牌堆回收牌（有人要这张牌，碰或杠等）
function CardLayer:recycleDiscard(viewId)
    -- 从丢弃的牌堆进行回收
	local cardNode = self.nodeDiscard[viewId]:getChildByTag(self.nDiscardCount[viewId])
    if cardNode then
        cardNode:removeFromParent()
        self.nDiscardCount[viewId] = self.nDiscardCount[viewId] - 1
    end
    self:flagDiscard(false)
end

--碰或杠
function CardLayer:bumpOrBridgeCard(viewId, cbCardData, nShowStatus, center_card, provider_view_id, cbOperateCode, cbOldCardData)
	assert(type(cbCardData) == "table")
	local resFont = 
	{
		CARD_PATH .. "/font_small/",
		CARD_PATH .. "/font_small_side/", 
		CARD_PATH .. "/font_middle/",
		CARD_PATH .. "/font_small_side/"
	}
	local width = pai.pile[viewId].w
	local height = pai.pile[viewId].h
	local fSpacing = pai.pile[viewId].g
	local widthTotal = 0
	local heightTotal = 0

    local scale = viewId == cmd.MY_VIEWID and 1 or SIDE_CPG_CARD_SCALE
	local fN = {15, 15, 15, 15}
	local fParentSpacing = (fSpacing*3 + fN[viewId]) * scale
	local nodeParent = cc.Node:create()
        :scale(scale)
		:move(self.nBpBgCount[viewId]*fParentSpacing*multipleBpBgCard[viewId][1], 
				self.nBpBgCount[viewId]*fParentSpacing*multipleBpBgCard[viewId][2])
		:addTo(self.nodeBpBgCard[viewId])

	if nShowStatus ~= GameLogic.SHOW_CHI then
		--明杠  找到旧的那一堆，重新走流程
		if nShowStatus == GameLogic.SHOW_MING_GANG then
			nodeParentMG = self.nodeBpBgCard[viewId]:getChildByTag( cbCardData[1] )
			--assert(nodeParentMG, "None of this bump card!")
            --备用查找
			if not nodeParentMG then
			    nodeParentMG = self.nodeBpBgCard[viewId]:getChildByTag( center_card )
			end
            if nodeParentMG then
				self.nBpBgCount[viewId] = self.nBpBgCount[viewId] - 1
				nodeParent:removeFromParent()
				nodeParentMG:removeAllChildren()
				nodeParent = nodeParentMG
			end
        --抽牌 找到 那堆牌
        elseif nShowStatus == GameLogic.SHOW_CHOU_PAI then
            local children = self.nodeBpBgCard[viewId]:getChildren()
            for index, child in pairs(children) do
                local isSwitch = true
                for i = 1, 4 do
                    local data = child.cardInfo.cbCardData[i]
                    if data > 0 and data ~= cbOldCardData[i] then
                        isSwitch = false
                    end
                end
                if isSwitch then
                    nodeParent:removeFromParent()
                    nodeParent = child
                    cbOperateCode = child.cardInfo.cbOperateCode
                    center_card = child.cardInfo.cbCenterCard
                    nShowStatus = child.cardInfo.nShowStatus
                    self.nBpBgCount[viewId] = self.nBpBgCount[viewId] - 1
                    break
                end            
            end
		end
		nodeParent:setTag(cbCardData[1])
	end

    -- 牌组的数据 保存一份 抽牌的时候 需要使用
    local cardInfo = {}
    cardInfo.cbCardData = cbCardData
    cardInfo.nShowStatus = nShowStatus
    cardInfo.cbCenterCard = center_card
    cardInfo.cbOperateCode = cbOperateCode
    --dump(cbCardData, '牌组的数据', 9)
    if GameLogic.GetMagicCardCount( cbCardData ) > 0 then
        cardInfo.isReal = false
    else
        cardInfo.isReal = true
    end
    nodeParent.cardInfo = cardInfo

    -- 确定提供者箭头角度（默认朝下）
    local provider_dirs = {
        {-90, 0, 90},
        {180, -90, 0},
        {90, 180, -90},
        {0, 90, 180},
    }
    local dir = 0
    if math.abs(provider_view_id - viewId) == 2 then dir = 2
    elseif provider_view_id - viewId == 1 then dir = 3
    elseif provider_view_id - viewId == -1 then dir = 1
    elseif provider_view_id - viewId == 3 then dir = 1
    elseif provider_view_id - viewId == -3 then dir = 3
    end
    local rotation = provider_dirs[viewId][dir]
    --dump(cbCardData, '牌底', 9)
	for i = 1, #cbCardData do
		local cbCardDataValue = cbCardData[i]
        if cbCardDataValue > 0 then
            --牌底
		    local card = cs.game.util.createCard( cbCardDataValue, viewId, 'pile' )
			    :move(i*fSpacing*multipleBpBgCard[viewId][1], i*fSpacing*multipleBpBgCard[viewId][2])
			    :addTo(nodeParent)

            -- 确定哪一张牌上加箭头
            if rotation then    -- 如果provider是自己，rotation是nil
                local card_arrow = nil
	            if nShowStatus == GameLogic.SHOW_CHI then   -- 吃，哪张吃来的标哪张
                    if cbCardDataValue == center_card then
                        card_arrow = card
                    end
                else    -- 碰、杠
                    if nShowStatus == GameLogic.SHOW_PENG then    -- 碰
                        if i == dir then    -- 左中右对应顺序
                            card_arrow = card 
                        end
                    else    -- 杠
                        if (dir ~= 2 and dir == i) or (dir == 2 and i == 4) then    -- 非中间牌标和顺序一致，中间牌标在上方的第4张上
                            card_arrow = card 
                        end
                    end
                end
                if card_arrow then
                    local offset = {cc.p(0, 5), cc.p(0, 8), cc.p(0, 6), cc.p(0, 8)}
                    local pos = cc.pAdd( cc.p(card_arrow:size().width/2, card_arrow:size().height/2), offset[viewId] )
                    local arrow = display.newSprite('room/icon_prompt_arrow.png')
                    arrow:scale( viewId == 3 and 1 or 0.7 )
                    arrow:rotation(rotation):pos(pos):zorder(3):addTo(card_arrow)
                    arrow:setTag(2)
                end
            end

		    if viewId == 4 then
			    card:setLocalZOrder(5 - i)
		    end
		    if i == 4 then 		--杠
			    local moveUp = {14, 14, 14, 14}
			    card:move(2*fSpacing*multipleBpBgCard[viewId][1], 2*fSpacing*multipleBpBgCard[viewId][2] + moveUp[viewId])
			    card:setLocalZOrder(5)
		    elseif nShowStatus == GameLogic.SHOW_AN_GANG then 		--暗杠
			    card:setTexture(resFont[viewId].."card_back.png")
			    card:removeChildByTag(1)
                card:removeChildByTag(2)
		    end
		    --添加牌到记录里
		    if nShowStatus ~= GameLogic.SHOW_MING_GANG or i == 4 then
			    local pos = 1
			    while pos <= #self.cbBpBgCardData[viewId] do
				    if self.cbBpBgCardData[viewId][pos] == cbCardData[i] then
					    break
				    end
				    pos = pos + 1
			    end
			    table.insert(self.cbBpBgCardData[viewId], pos, cbCardData[i])
		    end 
        end
	end
	self.nBpBgCount[viewId] = self.nBpBgCount[viewId] + 1
end

--检查碰、杠牌里是否有这张牌
function CardLayer:checkBumpOrBridgeCard(viewId, cbCardData, operateCards)
    local isHavePeng = false
    local isIn, isHave = extend.execute(self, 'checkBumpOrBridgeCard', viewId, cbCardData, operateCards)
    if isIn then
        isHavePeng = isHave
    else
        local card = self.nodeBpBgCard[viewId]:getChildByTag(cbCardData)
	    if card then
            isHavePeng = true
	    end
    end
    return isHavePeng
end

function CardLayer:getBpBgCardData()
	return self.cbBpBgCardData
end

function CardLayer:gameEnded()
	self.bSendOutCardEnabled = false
end

function CardLayer:switchToCardRectX(data)
	assert(data, "this card is nil")
	local cardIndex = GameLogic.SwitchToCardIndex(data)
	local rectX = cardIndex == GameLogic.MAGIC_INDEX and 32 or cardIndex
	return rectX
end

-- 让牌亮其起来
function CardLayer:makeCardLight()
    local cbCardCount = #self.cbCardData
	--先全部变灰
	for i = cmd.MAX_COUNT, 1, -1 do
		local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(i)
		card:setColor(COLOR_WHITE)
        self.bCardGray[i] = false
	end
end

--使部分牌变灰（参数为不变灰的）
function CardLayer:makeCardGray(cbOutCardData)
	--assert(#self.cbListenList > 0 and math.mod(#self.cbCardData - 2, 3) == 0)
	local cbCardCount = #self.cbCardData
	--先全部变灰
	for i = cmd.MAX_COUNT, 1, -1 do
		local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(i)
		card:setColor(cc.c3b(100, 100, 100))
		self.bCardGray[i] = true
	end
	--将牌补满(ui与值的对齐方式)
	local nCount = 0
	local num = math.mod(cbCardCount, 3)
	if num == 2 then
		nCount = cbCardCount
	elseif num == 1 then
		nCount = cbCardCount + 1
	else
		assert(false)
	end
    --再恢复可打出的牌
    --dump(self.cbCardData, '再恢复可打出的牌', 9)
	for i = 1, #cbOutCardData do
		for j = 1, nCount do
			if cbOutCardData[i] == self.cbCardData[j] then
				--assert(cbOutCardData[i] ~= GameLogic.MAGIC_DATA, "The magic card can't out!")
				local nTag = nCount - j + 1
				local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(nTag)
				card:setColor(COLOR_WHITE)
				self.bCardGray[nTag] = false
			end
		end
	end
end


function CardLayer:promptListeningCard(cardData)
    if not cs.game.TING_PROMPT then return end
	--assert(#self.cbListenList > 0)

	if nil == cardData then
		assert(self.currentOutCard > 0)
		cardData = self.currentOutCard
	end

	for i = 1, #self.cbListenList do
		if self.cbListenList[i].cbOutCard == cardData then
			self._scene:setListeningCard(self.cbListenList[i].cbListenCard)
			break
		end
	end
end

function CardLayer:startListenCard()
    if not cs.game.TING_PROMPT then return end

	for i = 1, cmd.MAX_COUNT do
		local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(i)
		if i == 1 then
			card:setColor(COLOR_WHITE)
			self.bCardGray[i] = false
		else
			card:setColor(cc.c3b(100, 100, 100))
			self.bCardGray[i] = true
		end
	end
end

-- 
function CardLayer:promptListenOutCard(cbPromptOutData)
    if not cs.game.TING_PROMPT then return end

	--还原
	local cbCardCount = #self.cbCardData
	for i = 1, cmd.MAX_COUNT do
		local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(i)
		card:getChildByTag(CardLayer.TAG_LISTEN_FLAG):setVisible(false)
	end
	if cbPromptOutData == nil then
		return 
	end
	--校验
	assert(type(cbPromptOutData) == "table")
	assert(math.mod(cbCardCount - 2, 3) == 0, "You can't out card now!")
	for i = 1, #cbPromptOutData do
		if not GameLogic.IsMagicCard( cbPromptOutData[i] ) then
			for j = 1, cbCardCount do
				if cbPromptOutData[i] == self.cbCardData[j] then
					local nTag = cbCardCount - j + 1
					local card = self.nodeHandCard[cmd.MY_VIEWID]:getChildByTag(nTag)
					card:getChildByTag(CardLayer.TAG_LISTEN_FLAG):setVisible(true)
				end
			end
		end
	end
end

--设置 牌堆信息
function CardLayer:setTableCardByHeapInfo(viewId, cbHeapCardInfo, wViewHead, wViewTail)
	--[[
    local nViewMinTag = (cmd.GAME_PLAYER - viewId)*28 + 1
	local nViewMaxTag = (cmd.GAME_PLAYER - viewId)*28 + 28
	--从右数隐藏几张牌
	local nTagStart1 = nViewMinTag
	local nTagEnd1 = nViewMinTag + cbHeapCardInfo[1] - 1
	for i = nTagStart1, nTagEnd1 do
		--self.nodeTableCard:getChildByTag(i):setVisible(false)
	end
	--从左数隐藏几张牌
	local nTagStart2 = nViewMaxTag - cbHeapCardInfo[2] + 1
	local nTagEnd2 = nViewMaxTag
	for i = nTagStart2, nTagEnd2 do
		--self.nodeTableCard:getChildByTag(i):setVisible(false)
	end

	if viewId == wViewHead then
		self.currentTag = (cmd.GAME_PLAYER - viewId)*28 + cbHeapCardInfo[1] + 1
	elseif viewId == wViewTail then
		self.nTableTailCardTag = (cmd.GAME_PLAYER - viewId)*28 + (28 - cbHeapCardInfo[2])
	end
    --]]
	-- 牌堆总共剩余多少牌
	--self.nRemainCardNum = self.nRemainCardNum - cbHeapCardInfo[1] - cbHeapCardInfo[2]
	self._scene:setRemainCardNum(self.nRemainCardNum)
end

function CardLayer:touchSendOutCard(cbCardData)
	if not self.bSendOutCardEnabled then
		return false
	end
	self.currentOutCard = cbCardData
	self._scene:setListeningCard(nil)
	--发送消息
	return self._scene._scene:sendOutCard(cbCardData)
end

function CardLayer:outCardAuto()
	local nCount = #self.cbCardData
	if math.mod(nCount - 2, 3) ~= 0 then
		return false
	end
	for i = nCount, 1, -1 do
		local nTag = nCount - i + 1
		local bOk = not self.bCardGray[nTag]
		if not GameLogic.IsMagicCard( self.cbCardData[i] ) and bOk then
			self:touchSendOutCard(self.cbCardData[i])
			break
		end
	end

	return true
end
--检查手里的杠
function CardLayer:getGangCard(data)
	local cbCardCount = #self.cbCardData
	local num = 0
	if math.mod(cbCardCount, 3) == 2 then
		num = 4
	elseif math.mod(cbCardCount, 3) == 1 then
		num = 3
	else
		assert(false, "This is a not bridge card time!")
	end
	local cbCardIndex = GameLogic.DataToCardIndex(self.cbCardData)
	local index = GameLogic.SwitchToCardIndex(data)
	if cbCardIndex[index] == num then
		return data
	end
	for i = 1, GameLogic.NORMAL_INDEX_MAX do
		local dataTemp = GameLogic.SwitchToCardData(i)
		if cbCardIndex[i] == num then
			data = dataTemp
		end
	end
	return data
end

--用户必须点胡牌
function CardLayer:isUserMustWin()
    --[[
	-- local cbCardCount = #self.cbCardData
	-- local cbCardDataTemp = clone(self.cbCardData)
	-- GameLogic.SortCardList(cbCardDataTemp)
	-- --有普通牌
	-- local cbNormalCardTypeCount = 0
	-- local cbNormalCardTemp = 0
	-- for i = 1, cbCardCount do
	-- 	if cbCardDataTemp[i] == GameLogic.MAGIC_DATA then
	-- 		break
	-- 	end
	-- 	if cbNormalCardTemp ~= cbCardDataTemp[i] then
	-- 		cbNormalCardTypeCount = cbNormalCardTypeCount + 1
	-- 	end
	-- 	cbNormalCardTemp = cbCardDataTemp[i]
	-- end

	if #self.cbCardData == 2 then
		if self.cbCardData[1] == self.cbCardData[2] and 
			GameLogic.IsMagicCard( self.cbCardData[2] ) then
			return true
		end
	end
    --]]
	return false
end
--用户可以碰
function CardLayer:isUserCanBump()
	if #self.cbCardData == 4 then
		if self.cbCardData[1] == self.cbCardData[2] and 
			self.cbCardData[3] == self.cbCardData[4] and 
			GameLogic.IsMagicCard( self.cbCardData[4] ) then
			return false
		end
	end

	return true
end

--设置全部的手牌
function CardLayer:setAllCardData( data )
    self.cbAllCardData = data
end

--刷新剩余牌数
function CardLayer:updateRemainCardNum( num )
    self.nRemainCardNum = num
    self._scene:setRemainCardNum( num )
end

--刷新手牌的听牌信息
function CardLayer:updateTingCardsInfo( cardsDataInfo )
	local cardParent = self.nodeHandCard[ cmd.MY_VIEWID ]
    for i = 1, cmd.MAX_COUNT do
		local card = cardParent:getChildByTag(i)
        local ting_flag = card:getChildByTag(3)
        if ting_flag then
            ting_flag:hide()
        end
        --将牌补满(ui与值的对齐方式)
	    local nCount = 0
	    local num = math.mod(self.cbCardCount[ cmd.MY_VIEWID ], 3)
		if num == 2 then
			nCount = self.cbCardCount[ cmd.MY_VIEWID ]
		elseif num == 1 then
			nCount = self.cbCardCount[ cmd.MY_VIEWID ] + 1
		else
			assert(false)
		end
        if i <= nCount then
            local index = nCount - i + 1
            local cardData = self.cbCardData[index]
            -- print('刷新手牌的听牌信息', index, cardData, nCount, i)
            if cardData then
                local cbCardIndex = GameLogic.SwitchToCardIndex( cardData )
                if cardsDataInfo and cardsDataInfo.bIsTing then
                    local ting_info = cardsDataInfo.tingCardsItem[ cbCardIndex ]
                    if ting_info.tingCardsNum ~= 0 then
                        local ting_flag = card:getChildByTag(3)
                        if ting_flag then
                            ting_flag:show()
                        end
                    end
                end
            end
        end
	end
end

-- 获取手牌上限
function CardLayer:getPlayerMaxCardsNum()
    return math.max(self.cbPlayerMaxCardsNum, 14)
end

-- 
function CardLayer:changeCardColor( cardData, color )
    local cardParent = self.nodeHandCard[ cmd.MY_VIEWID ]
    -- 手牌
    local children = cardParent:getChildren()
    for i, card in ipairs(children) do
        if cardData then
            if (cardData and cardData == card.card_value) then
                card:setColor( color )
            else
                card:setColor( COLOR_WHITE )
            end
        else
            card:setColor( color )
        end    
    end
    
    for i = 1, cmd.GAME_PLAYER do
        -- 出的牌
        children = self.nodeDiscard[i]:getChildren()
        for i, card in ipairs(children) do
            if cardData then
                if (cardData and cardData == card.card_value) then
                    card:setColor( color )
                else
                    card:setColor( COLOR_WHITE )
                end
            else
                card:setColor( color )
            end    
        end
        -- 吃碰杠的牌
        children = self.nodeBpBgCard[i]:getChildren()
        for i, parent in ipairs(children) do
           for j, card in ipairs(parent:getChildren()) do
                if cardData then
                    if (cardData and cardData == card.card_value) then
                        card:setColor( color )
                    else
                        card:setColor( COLOR_WHITE )
                    end
                else
                    card:setColor( color )
                end    
           end
        end
    end
end

return CardLayer
-------------------------------------------------------------------------------
--  创世版1.0
--  当前游戏特定玩法扩展
--  @date 2018-02-24
--  @auth woodoo
-------------------------------------------------------------------------------

local extend = {}
--=============================================================================
--                      仙居麻将(kind:303)特殊逻辑
--=============================================================================
-------------------------------------------------------------------------------
-- 杠
-------------------------------------------------------------------------------
function extend:GameViewLayer_onButtonEventChi_303()
    local GameLogic = cs.app.game('room.GameLogic')
    if self.bIsCaiOperate then
        local cardsWaves = nil
        cardsWaves = GameLogic.AnyseChiCards(self._cardLayer.cbCardData, self.cbActionCard)
        if #cardsWaves > 1 then
            self.sp_game_btn:hide() -- 不能用self:HideGameBtn()
            self:showAllChoices( cardsWaves )
        elseif #cardsWaves == 1 then
            self._scene:sendOperateCard(cardsWaves[1].operate, cardsWaves[1].cards)
            self:HideGameBtn()  
        end
        return true
    end
end

-------------------------------------------------------------------------------
-- 碰
-------------------------------------------------------------------------------
function extend:GameViewLayer_onButtonEventPeng_303()
    local GameLogic = cs.app.game('room.GameLogic')
    --如果有这个标记 就需要前台自己计算可以 吃碰杠的组合 因为数组 太大了 
    local cardsWaves = nil
    if self.bIsCaiOperate then
        cardsWaves = GameLogic.AnysePengCards(self._cardLayer.cbCardData, self.cbActionCard)
        if #cardsWaves > 1 then
            self.sp_game_btn:hide() -- 不能用self:HideGameBtn()
            self:showAllChoices( cardsWaves )
        elseif #cardsWaves == 1 then
            self._scene:sendOperateCard(cardsWaves[1].operate, cardsWaves[1].cards)
            self:HideGameBtn()  
        end
        return true
    end
end

-------------------------------------------------------------------------------
-- 杠
-------------------------------------------------------------------------------
function extend:GameViewLayer_onButtonEventGang_303()
    local GameLogic = cs.app.game('room.GameLogic')
    local cmd = cs.app.game('room.CMD_Game')
    local cardsWaves = nil
    if self.bIsCaiOperate then
        local children = self._cardLayer.nodeBpBgCard[ cmd.MY_VIEWID ]:getChildren()
        local weaves = {}
        for index, child in pairs( children ) do
            table.insert(weaves, child.cardInfo)
        end
        cardsWaves = GameLogic.AnyseGangCards(self._cardLayer.cbCardData, self.cbActionCard, not self._cardLayer:checkIsMyTurn(), weaves )
        if #cardsWaves > 1 then
            self.sp_game_btn:hide() -- 不能用self:HideGameBtn()
            self:showAllChoices( cardsWaves )
        elseif #cardsWaves == 1 then
            self._scene:sendOperateCard(cardsWaves[1].operate, cardsWaves[1].cards)
            self:HideGameBtn()  
        end
        return true
    end
end

-------------------------------------------------------------------------------
-- 检查碰、杠牌里是否有这张牌
-------------------------------------------------------------------------------
function extend:CardLayer_checkBumpOrBridgeCard_303(viewId, cbCardData, operateCards)
    local isHavePeng = false
    local GameLogic = cs.app.game('room.GameLogic')
    if self._scene.bIsCaiOperate then
    	local children = self.nodeBpBgCard[viewId]:getChildren()
        for index, child in pairs(children) do
            if child.cardInfo.cbOperateCode == GameLogic.WIK_PENG then
                local isOk = true
                for i = 1, 3 do
                    if operateCards[i] > 0 and operateCards[i] ~= child.cardInfo.cbCardData[i] then
                        isOk = false
                        break
                    end
                end
                if isOk then
                    isHavePeng = true
                end
            end
        end
    end
    return self._scene.bIsCaiOperate, isHavePeng
end
--============================== end kind 303 ================================


return extend
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import time
import requests
import urllib3
import socket
import ssl

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_raw_http():
    """使用原始HTTP请求测试"""
    print("=== 原始HTTP请求测试 ===")
    
    # 构建HTTP请求
    host = 'lhmj.tuo3.com.cn'
    port = 80
    path = '/admin/api/v1/user/get_verify_code'
    
    # 准备POST数据
    params = {
        'uid': '123456',
        'phone': '13800138000',
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sorted_keys = sorted(params.keys())
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    sign_str = param_str + channel_key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    params['sign'] = sign
    
    # 构建POST数据
    post_data = '&'.join([f'{k}={v}' for k, v in params.items()])
    
    # HTTP请求头
    http_request = f"""POST {path} HTTP/1.1\r
Host: {host}\r
Content-Type: application/x-www-form-urlencoded\r
Content-Length: {len(post_data)}\r
User-Agent: Python-Test/1.0\r
Connection: close\r
\r
{post_data}"""
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30)
        sock.connect((host, port))
        
        # 发送HTTP请求
        sock.send(http_request.encode('utf-8'))
        
        # 接收响应
        response = b''
        while True:
            data = sock.recv(4096)
            if not data:
                break
            response += data
        
        sock.close()
        
        # 解析响应
        response_str = response.decode('utf-8', errors='ignore')
        print("原始HTTP响应:")
        print(response_str)
        
        # 分离头部和正文
        if '\r\n\r\n' in response_str:
            headers, body = response_str.split('\r\n\r\n', 1)
            print(f"\n响应头:\n{headers}")
            print(f"\n响应正文:\n{body}")
            
            # 检查状态码
            if 'HTTP/1.1 200' in headers:
                print("✅ HTTP请求成功")
                return True
            else:
                print("❌ HTTP请求失败")
        
    except Exception as e:
        print(f"❌ 原始HTTP请求失败: {e}")
    
    return False

def test_with_different_user_agents():
    """使用不同User-Agent测试"""
    print("\n=== 不同User-Agent测试 ===")
    
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        'okhttp/3.12.1',  # Android常用
        'CFNetwork/1220.1 Darwin/20.3.0',  # iOS常用
        'Python-urllib/3.8',
        'curl/7.68.0'
    ]
    
    # 基础参数
    params = {
        'uid': '123456',
        'phone': '13800138000',
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sorted_keys = sorted(params.keys())
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    sign_str = param_str + channel_key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    params['sign'] = sign
    
    url = 'http://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code'
    
    for i, ua in enumerate(user_agents):
        print(f"\n测试 {i+1}: {ua[:50]}...")
        
        try:
            headers = {
                'User-Agent': ua,
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }
            
            response = requests.post(
                url,
                data=params,
                headers=headers,
                timeout=30,
                allow_redirects=True
            )
            
            print(f"  状态码: {response.status_code}")
            print(f"  响应长度: {len(response.text)}")
            
            if response.status_code == 200:
                print(f"  响应内容: {response.text[:200]}...")
                try:
                    json_data = response.json()
                    print(f"  JSON: {json_data}")
                    if json_data.get('code') == 0:
                        print("  ✅ 成功!")
                        return True
                except:
                    print("  ⚠️ 非JSON响应")
            elif response.status_code == 502:
                print("  ❌ 502 Bad Gateway")
            else:
                print(f"  ❌ 状态码: {response.status_code}")
                print(f"  响应: {response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")
    
    return False

def test_alternative_endpoints():
    """测试其他可能的端点"""
    print("\n=== 测试其他端点 ===")
    
    test_urls = [
        'http://lhmj.tuo3.com.cn/',
        'http://lhmj.tuo3.com.cn/admin/',
        'http://lhmj.tuo3.com.cn/admin/api/',
        'http://lhmj.tuo3.com.cn/admin/api/v1/',
        'http://lhmj.tuo3.com.cn/api/v1/user/get_verify_code',  # 可能不需要/admin前缀
        'http://**************/admin/api/v1/user/get_verify_code',  # 直接IP访问
    ]
    
    for url in test_urls:
        print(f"\n测试: {url}")
        try:
            response = requests.get(url, timeout=10)
            print(f"  状态码: {response.status_code}")
            print(f"  响应长度: {len(response.text)}")
            if response.status_code == 200:
                print(f"  响应预览: {response.text[:100]}...")
            elif response.status_code == 404:
                print("  ❌ 404 Not Found")
            elif response.status_code == 502:
                print("  ❌ 502 Bad Gateway")
            else:
                print(f"  ⚠️ 状态码: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")

if __name__ == '__main__':
    print("开始详细诊断...")
    
    # 测试1: 原始HTTP请求
    success1 = test_raw_http()
    
    # 测试2: 不同User-Agent
    success2 = test_with_different_user_agents()
    
    # 测试3: 其他端点
    test_alternative_endpoints()
    
    if not (success1 or success2):
        print("\n=== 结论 ===")
        print("❌ 所有测试都失败了")
        print("🔍 可能的原因:")
        print("1. 服务器正在维护或宕机")
        print("2. API端点已更改")
        print("3. 需要特殊的认证或头部")
        print("4. 服务器配置问题")
        print("\n💡 建议:")
        print("1. 联系服务器管理员")
        print("2. 检查游戏内是否有其他API地址配置")
        print("3. 查看服务器日志")
        print("4. 尝试联系技术支持")

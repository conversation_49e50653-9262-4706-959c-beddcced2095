-------------------------------------------------------------------------------
--  创世版1.0
--  解散房间
--  @date 2017-07-31
--  @auth woodoo
-------------------------------------------------------------------------------
local DismissLayer = class("DismissLayer", cc.Layer)


-- 倒计时时间
local TOTAL_SECONDS = 3 * 60


-------------------------------------------------------------------------------
-- 构造方法
--  user_item: 请求解散人
--  user_list: 所有人
-------------------------------------------------------------------------------
function DismissLayer:ctor(user_item, user_list)
    print('DismissLayer:ctor...')
    self:enableNodeEvents()
    self.m_user_list = {{user_item, true}}

    for _, v in pairs(user_list) do
        if user_item.dwUserID ~= v.dwUserID then
            table.insert(self.m_user_list, {v, false})
        end
    end

    -- 载入主UI
    local main_node = helper.app.loadCSB('DissolveLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 同意和不同意按钮
    helper.logic.addListenerByName(self, {main_node:child('btn_agree, btn_not_agree')})

    main_node:child('label_1'):setRich( LANG{'ROOM_DISMISS_REPLY', name=user_item.szNickName} )
    self.m_index = 1

    for i = 2, 4 do
        local u = self.m_user_list[i]
        if u then
            main_node:child('label_' .. i):setRich( LANG{'ROOM_DISMISS_WAIT', name=u[1].szNickName} )
        else
            main_node:child('label_' .. i):setRich('')
        end
    end

    self.m_left_seconds = TOTAL_SECONDS
    self.m_target_time = os.time() + TOTAL_SECONDS

    if user_item.dwUserID == GlobalUserItem.dwUserID then
        self:doneProcess()
    end

    -- 3分钟定时器
    self:perform( handler(self, self.onTimer), 1, -1, 135, true )
end


-------------------------------------------------------------------------------
-- 获取剩余时间
-------------------------------------------------------------------------------
function DismissLayer:formatTime()
    return string.format('%02d:%02d', math.floor(self.m_left_seconds / 60), self.m_left_seconds % 60)
end


-------------------------------------------------------------------------------
-- 3分钟定时器
-------------------------------------------------------------------------------
function DismissLayer:onTimer()
    self.m_left_seconds = self.m_left_seconds - 1
    local str = self:formatTime()
    if self.m_has_choice then
        self.main_node:child('label_desc'):setString( str )
    else
        self.main_node:child('label_desc'):setString( LANG{'ROOM_DISMISS_DESC', time=str} )
    end

    if self.m_left_seconds == 0 then
        self:stop(135)
        if not self.m_has_choice then
            self:onBtnAgree()
        end
    end
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function DismissLayer:onExit()
    print('DismissLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 动画效果移除
-------------------------------------------------------------------------------
function DismissLayer:effectRemove()
    local mask = self:child('mask')
    if mask then mask:removeFromParent() end

    self:stop():runMyAction( cc.Sequence:create(
        cc.DelayTime:create(0.5),
        cc.MoveBy:create(0.2, cc.p(0, -600)),
        cc.RemoveSelf:create(true)
    ) )
end


-------------------------------------------------------------------------------
-- 已操作处理
-------------------------------------------------------------------------------
function DismissLayer:doneProcess()
    self.m_has_choice = true
    self.main_node:child('btn_agree'):hide()
    self.main_node:child('btn_not_agree'):hide()

    self.main_node:child('label_desc'):setString( self:formatTime() )
    self.main_node:child('label_desc'):py( self.main_node:child('btn_agree'):py() ):setFontSize(60)
end


-------------------------------------------------------------------------------
-- 玩家操作通知
-------------------------------------------------------------------------------
function DismissLayer:onPlayerNotify(user_item, is_agree)
    self.m_index = self.m_index + 1
    local key = is_agree and 'ROOM_DISMISS_AGREE' or 'ROOM_DISMISS_UNAGREE'
    self.main_node:child('label_' .. self.m_index):setRich( LANG{key, name=user_item.szNickName} )

    local index = self.m_index + 1
    for _, u in ipairs(self.m_user_list) do
        if u[1] == user_item then
            u[2] = true
        elseif not u[2] and index <= 4 then
            self.main_node:child('label_' .. index):setRich( LANG{'ROOM_DISMISS_WAIT', name=u[1].szNickName} )
            index = index + 1
        end
    end

    if user_item.dwUserID == GlobalUserItem.dwUserID then
        self:doneProcess()
    end
end


-------------------------------------------------------------------------------
-- 修正时间（可能是回到前台）
-------------------------------------------------------------------------------
function DismissLayer:resetTime(left_seconds)
    self.m_left_seconds = math.max(0, left_seconds or (self.m_target_time - os.time()))
end


-------------------------------------------------------------------------------
-- 同意按钮点击
-------------------------------------------------------------------------------
function DismissLayer:onBtnAgree(sender)
    self:doneProcess()
    PassRoom:getInstance():getNetFrame():sendRequestReply(1)
end


-------------------------------------------------------------------------------
-- 不同意按钮点击
-------------------------------------------------------------------------------
function DismissLayer:onBtnNotAgree(sender)
    self:doneProcess()
    PassRoom:getInstance():getNetFrame():sendRequestReply(0)
end


return DismissLayer
--[[--
斗地主游戏逻辑
2016.6
]]
local GameLogic = {}

GameLogic._CardData = { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, -- 方块
                        0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, -- 梅花
                        0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, -- 红桃
                        0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, -- 黑桃
                        0x4E, 0x4F } -- 小王,大王

GameLogic.GAME_PLAYER         = 3    -- 游戏人数
GameLogic.CARD_COUNT_NORMAL   = 17   -- 常规牌数
GameLogic.CARD_COUNT_MAX      = 20   -- 最大牌数
GameLogic.CARD_FULL_COUNT     = 54   -- 全牌数目

-- 扑克类型
GameLogic.CT_ERROR            = 0    -- 错误类型
GameLogic.CT_SINGLE           = 1    -- 单牌类型
GameLogic.CT_DOUBLE           = 2    -- 对牌类型
GameLogic.CT_THREE            = 3    -- 三条类型
GameLogic.CT_SINGLE_LINE      = 4    -- 单连类型
GameLogic.CT_DOUBLE_LINE      = 5    -- 对连类型
GameLogic.CT_THREE_LINE       = 6    -- 三连类型
GameLogic.CT_THREE_TAKE_ONE   = 7    -- 三带一单
GameLogic.CT_THREE_TAKE_TWO   = 8    -- 三带一对
GameLogic.CT_FOUR_TAKE_ONE    = 9    -- 四带两单
GameLogic.CT_FOUR_TAKE_TWO    = 10   -- 四带两对
GameLogic.CT_BOMB_CARD        = 12   -- 炸弹类型
GameLogic.CT_MISSILE_CARD     = 14   -- 火箭类型
GameLogic.CT_BOMB_SOFT        = 11   -- 软炸
GameLogic.CT_BOMB_LAIZI       = 13   -- 癞子炸
GameLogic.CT_PLANE            = 14   -- 飞机

--- 癞子牌
GameLogic.cbLaiziCardData = {0, 0, 0, 0}
GameLogic.cbLaiziCardCount = 0
GameLogic.cbLaiziCard = 0
GameLogic.cbLaiziLogicVal = 0
GameLogic.cbLaiziVal = 0
GameLogic.bIsNeedShowLaizi = false

function GameLogic:setLaiziCard( cbDataCard, cbCardCount, cbLaiziCard )
    GameLogic.cbLaiziCard = cbLaiziCard
    --GameLogic.cbLaiziCardCount = cbCardCount
    local count = 1
    local val = self:GetCardLogicValue(cbLaiziCard)
    GameLogic.cbLaiziLogicVal = val
    GameLogic.cbLaiziVal = self:GetCardValue(cbLaiziCard)
    GameLogic.cbLaiziCardData = {0, 0, 0, 0}
    -- body
    for i = 1, cbCardCount do
        if self:GetCardLogicValue(cbDataCard[i]) == val then
            GameLogic.cbLaiziCardData[count] = cbDataCard[i]
            count = count + 1
        end
    end
    GameLogic.cbLaiziCardCount = count - 1

    dump(GameLogic.cbLaiziCardData)
end

-- 创建空扑克数组
function GameLogic:emptyCardList( count )
    local tmp = {}
    for i = 1, count do
        tmp[i] = 0
    end
    return tmp
end



-- 获取余数
function GameLogic:mod(a, b)
    return a - math.floor(a/b)*b
end

-- 获取整数
function GameLogic:ceil(a, b)
    return math.ceil(a/b) - 1
end

-- 获取牌值(1-15)
function GameLogic:GetCardValue(nCardData)
    --return bit:_and(nCardData, 0X0F)    -- 数值掩码
    --return yl.POKER_VALUE[nCardData]
    return math.mod(nCardData, 16)
end

-- 获取花色(1-5)
function GameLogic:GetCardColor(nCardData)
    return bit:_and(nCardData, 0XF0)    --花色掩码
    --return yl.POKER_COLOR[nCardData]
end

-- 逻辑牌值(大小王、2、A、K、Q)
function GameLogic:GetCardLogicValue(nCardData)
    if nCardData == 0 then return 0 end
    local nCardValue = self:GetCardValue(nCardData)
    local nCardColor = self:GetCardColor(nCardData)
    if nCardData >= 0x4E then
        return nCardValue + 2
    end
    return nCardValue <= 2 and (nCardValue + 13) or nCardValue
end

-- 获取牌序 0x4F大王 0x4E小王 nil牌背 
function GameLogic:GetCardIndex(nCardData)
    if nCardData == 0x4E then
       return 53
    elseif nCardData == 0x4F then
       return 54
    elseif nCardData == nil then
       return 55
    end
    local nCardValue = self:GetCardValue(nCardData)
    local nCardColor = self:GetCardColor(nCardData)
    nCardColor = bit:_rshift(nCardColor, 4)
    return nCardColor * 13 + nCardValue
end

-- 癞子牌的显示排序 大小王->癞子->普通牌
function GameLogic:SortLaiziCardList( cbCardData )
    -- body

    GameLogic.bIsNeedShowLaizi = true
    local lai_val = self:GetCardValue(GameLogic.cbLaiziCard)
    table.sort(cbCardData, 
    function (a, b)
        
        local a_color = self:GetCardColor(a)
        local a_val = self:GetCardLogicValue(a)
        local b_color = self:GetCardColor(b)
        local b_val = self:GetCardLogicValue(b)
        if a_color == 0x40 and b_color ~= 0x40 then
            return true
        elseif a_color ~= 0x40 and b_color == 0x40 then
            return false
        elseif a_color == 0x40 and b_color == 0x40 then
            if a_val > b_val then 
                return true 
            else
                return false
            end
        else
            if a_val == lai_val and b_val ~= lai_val then
                return true
            elseif a_val ~= lai_val and b_val == lai_val then
                return false
            elseif a_val == lai_val and b_val == lai_val then
                if a_color > b_color then 
                    return true 
                else
                    return false
                end
            else
                if a_val > b_val then
                    return true
                elseif a_val < b_val then
                    return false
                else
                    return a_color > b_color
                end
            end
        end
    end)

end

--扑克排序
function GameLogic:SortCardList(cbCardData, cbCardCount, cbSortType)
    local cbSortValue = {}
    for i = 1, cbCardCount do
        local value = self:GetCardLogicValue(cbCardData[i])
        table.insert(cbSortValue, i, value)
    end
    if cbSortType == 0 then --大小排序
        for i=1,cbCardCount-1 do
            for j=1,cbCardCount-1 do
                if (cbSortValue[j] < cbSortValue[j+1]) or (cbSortValue[j] == cbSortValue[j+1] and cbCardData[j] < cbCardData[j+1]) then
                    local temp = cbSortValue[j]
                    cbSortValue[j] = cbSortValue[j+1]
                    cbSortValue[j+1] = temp
                    local temp2 = cbCardData[j]
                    cbCardData[j] = cbCardData[j+1]
                    cbCardData[j+1] = temp2
                end
            end
        end
    end
    return cbCardData
end

--某牌位置
function GameLogic:GetOneCardIndex(cbCardData,nCardData)
    local index = 1
    local value = self:GetCardLogicValue(nCardData)
    local i = 1
    while i <= #cbCardData do
        if nCardData == cbCardData[i] then
            index = i
            break
        end
        i = i + 1
    end
    return index
end

--插入位置
function GameLogic:GetAddIndex(cbCardData,nCardData)
    local index = #cbCardData+1
    local value = self:GetCardLogicValue(nCardData)
    local i = 1
    while i <= #cbCardData do
        local value2 = self:GetCardLogicValue(cbCardData[i])
        if (value > value2) or (value == value2 and nCardData > cbCardData[i])  then
            index = i
            break
        end
        i = i + 1
    end
    --print("插入位置:".. value ..",".. index)
    return index
end

--插入一张牌
function GameLogic:AddOneCard(cbCardData,nCardData,index)
    local cardDatas = {}
    local total = #cbCardData+1
    for i=1,total-1 do
        cardDatas[i] = cbCardData[i]
    end
    for i=total,index+1,-1 do
        cardDatas[i] = cardDatas[i-1]
    end
    cardDatas[index] = nCardData
    return cardDatas
end

--删除一张牌
function GameLogic:RemoveOneCard(cbCardData,index)
    local cardDatas = {}
    local total = #cbCardData-1
    for i=1,index-1 do
        cardDatas[i] = cbCardData[i]
    end
    for i=index,total do
        cardDatas[i] = cbCardData[i+1]
    end
    return cardDatas
end

--分析有序扑克
function GameLogic:AnalysebCardData(cbCardData, cbCardCount)
    --相同个数
    local cbBlockCount = {0,0,0,0}
    --相同的牌
    local cbCardDatas = {{},{},{},{}}
    local i = 1
    while i <= cbCardCount do
        local cbSameCount = 1
        local cbLogicValue = self:GetCardLogicValue(cbCardData[i])
        local j = i+1
        while j <= cbCardCount do
            local cbLogicValue2 = self:GetCardLogicValue(cbCardData[j])
            if cbLogicValue ~= cbLogicValue2 then
                break
            end
            cbSameCount = cbSameCount + 1
            j = j + 1
        end
        if cbSameCount > 4 then
            print("这儿有错误")
            return
        end
        cbBlockCount[cbSameCount] = cbBlockCount[cbSameCount] + 1
        local index = cbBlockCount[cbSameCount] - 1
        for k=1,cbSameCount do
            cbCardDatas[cbSameCount][index*cbSameCount+k] = cbCardData[i+k-1]
        end
        i = i + cbSameCount
    end
    --分析结构
    local tagAnalyseResult = {cbBlockCount,cbCardDatas}
    return tagAnalyseResult
end

--对比扑克
function GameLogic:CompareCard(cbFirstCard,cbFirstCount,cbNextCard,cbNextCount)
    -- for i=1,cbFirstCount do
    --     print("前家扑克 " .. GameLogic:GetCardLogicValue(cbFirstCard[i]))
    -- end
    -- for i=1,cbNextCount do
    --     print("下家扑克 " .. GameLogic:GetCardLogicValue(cbNextCard[i]))
    -- end
    local cbNextType = GameLogic:GetCardType(cbNextCard, cbNextCount)
    local cbFirstType = GameLogic:GetCardType(cbFirstCard, cbFirstCount)

    if cbNextType == GameLogic.CT_ERROR then
        return false
    end
    if cbFirstCount == 0 and cbNextType ~= GameLogic.CT_ERROR then
        return true
    end
    if cbNextType == GameLogic.CT_MISSILE_CARD then
        return true
    end
    if cbFirstType ~= GameLogic.CT_BOMB_LAIZI and cbNextType == GameLogic.CT_BOMB_LAIZI then
        return true
    end
    if cbFirstType == GameLogic.CT_BOMB_LAIZI and cbNextType ~= GameLogic.CT_BOMB_LAIZI then
        return false
    end
    if cbFirstType ~= GameLogic.CT_BOMB_CARD and cbNextType == GameLogic.CT_BOMB_CARD then
        return true
    end
    if cbFirstType == GameLogic.CT_BOMB_CARD and cbNextType ~= GameLogic.CT_BOMB_CARD then
        return false
    end
    if cbFirstType ~= GameLogic.CT_BOMB_SOFT and cbNextType == GameLogic.CT_BOMB_SOFT then
        return true
    end
    if cbFirstType == GameLogic.CT_BOMB_SOFT and cbNextType ~= GameLogic.CT_BOMB_SOFT then
        return false
    end
    if cbFirstType ~= cbNextType or cbFirstCount ~= cbNextCount then
        return false
    end
    --开始对比
    if (cbNextType == GameLogic.CT_SINGLE) or (cbNextType == GameLogic.CT_DOUBLE) or (cbNextType == GameLogic.CT_THREE) or (cbNextType == GameLogic.CT_SINGLE_LINE) or (cbNextType == GameLogic.CT_DOUBLE_LINE) or (cbNextType == GameLogic.CT_THREE_LINE)  or (cbNextType == GameLogic.CT_BOMB_CARD) then
       local cbNextLogicValue = GameLogic:GetCardLogicValue(cbNextCard[1])
       local cbFirstLogicValue = GameLogic:GetCardLogicValue(cbFirstCard[1])
       return cbNextLogicValue > cbFirstLogicValue
    elseif (cbNextType == GameLogic.CT_THREE_TAKE_ONE) or (cbNextType == GameLogic.CT_THREE_TAKE_TWO) then
        local nextResult = GameLogic:AnalysebCardData(cbNextCard, cbNextCount)
        local firstResult = GameLogic:AnalysebCardData(cbFirstCard, cbFirstCount)
        local cbNextLogicValue = GameLogic:GetCardLogicValue(nextResult[2][3][1])
        local cbFirstLogicValue = GameLogic:GetCardLogicValue(firstResult[2][3][1])
        return cbNextLogicValue > cbFirstLogicValue
    elseif (cbNextType == GameLogic.CT_FOUR_TAKE_ONE) or (cbNextType == GameLogic.CT_FOUR_TAKE_TWO) then
        local nextResult = GameLogic:AnalysebCardData(cbNextCard, cbNextCount)
        local firstResult = GameLogic:AnalysebCardData(cbFirstCard, cbFirstCount)
        local cbNextLogicValue = GameLogic:GetCardLogicValue(nextResult[2][4][1])
        local cbFirstLogicValue = GameLogic:GetCardLogicValue(firstResult[2][4][1])
        return cbNextLogicValue > cbFirstLogicValue
    end
    return false
end

function GameLogic:GetLaiziNum(cbCardData, cbCardCount, cbLaiziCard)
    if cbLaiziCard == 0 then return 0 end
    local cbLaiziLogicVal = self:GetCardLogicValue(cbLaiziCard)
    local cbLaiziNum = 0
    for i = 1, cbCardCount do
        if self:GetCardLogicValue(cbCardData[i]) == cbLaiziLogicVal then
            cbLaiziNum = cbLaiziNum + 1
        elseif cbCardData[i] > 0x40 and cbCardData[i] < 0x4E then
            cbLaiziNum = cbLaiziNum + 1
        end
    end

    return cbLaiziNum
end

function GameLogic:RemoveLaiziCards(cbCardData)
    local tmpCards = {}
    local tmpLaiziCards = {}
    
    for i = 1, #cbCardData do
        if (self:GetCardLogicValue(cbCardData[i]) ~= GameLogic.cbLaiziLogicVal) and 
        (cbCardData[i] < 0x40 or cbCardData[i] == 0x4E or cbCardData[i] == 0x4F) then
            table.insert(tmpCards, cbCardData[i])
        else 
            table.insert(tmpLaiziCards, cbCardData[i])
        end
    end

    return tmpCards,tmpLaiziCards
end


function GameLogic:RemoveKingCards(cbCardData)
    local tmpCards = {}
    
    for i = 1, #cbCardData do
        if cbCardData[i] ~= 0x4E and cbCardData[i] ~= 0x4F then
            table.insert(tmpCards, cbCardData[i])
        end
    end

    return tmpCards
end

function GameLogic:GetKingNum(cbCardData,cbCardCount)
    local count = 0
    for i = 1, cbCardCount do
        if cbCardData[i] == 0x4E or cbCardData[i] == 0x4F then
            count = count + 1
        end
    end

    return count
end


-- 点击出牌按钮后把所有可能的牌型组合找出,此时的数据必然要包含癞子牌(此时癞子已经时原来的那些值eg:0x21))
function GameLogic:GetAllTypeCards(cbCardData, cbCardCount, cbLaiziCard, cbTurnType, isPromptOut)
    cbTurnType = cbTurnType or 0
    isPromptOut = isPromptOut or false
    

    local tmpCards, tmpLaizdCards = self:RemoveLaiziCards(cbCardData)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --此牌类型
    local cbResultCardType = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard, cbResultCardType}

    if self:GetKingNum(cbCardData,cbCardCount) > 0 then
        return tagSearchCardResult
    end

    self:SortCardList(cbCardData,cbCardCount,0)
    local cbLaiziCardCount = self:GetLaiziNum(cbCardData,cbCardCount,cbLaiziCard)

    self:SortCardList(tmpCards,#tmpCards, 0)
    local analyseResult = GameLogic:AnalysebCardData(cbCardData, #cbCardData)

    local analyseResult1 = GameLogic:AnalysebCardData(tmpCards, #tmpCards)
    dump(analyseResult1)
    print('isPromptOut is ', isPromptOut)
    -- 判断是否是提示出牌， 如果是，直接判断类型，目前只适用于托管
    if isPromptOut == true then
        local type = GameLogic:GetCardType(cbCardData,cbCardCount)
        print('cur type is' , type)
        if type ~= GameLogic.CT_ERROR then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = type
            cbResultCard[cbResultCount] = {}
            for i = 1, cbCardCount do
                cbResultCard[cbResultCount][i] = cbCardData[i]
            end
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
            cbResultCount = cbResultCount - 1
            tagSearchCardResult[1] = cbResultCount
            return tagSearchCardResult
        end
    end

    if cbLaiziCardCount == 4 and cbCardCount == 4 then
        cbResultCardCount[cbResultCount] = cbCardCount
        cbResultCardType[cbResultCount] = GameLogic.CT_BOMB_LAIZI
        cbResultCard[cbResultCount] = {}
        for i = 1, cbCardCount do
            cbResultCard[cbResultCount][i] = cbCardData[i]
        end
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[4] = cbResultCardType
    end

    if cbCardCount == 4 and cbLaiziCardCount ~= 4 and analyseResult1[1][4 - cbLaiziCardCount] > 0 then
        cbResultCardCount[cbResultCount] = cbCardCount
        cbResultCardType[cbResultCount] = GameLogic.CT_BOMB_SOFT
        cbResultCard[cbResultCount] = {}
        if #tmpCards > 0 then
            for i = 1, #tmpCards do
                cbResultCard[cbResultCount][i] = tmpCards[i]
            end
            for i = #tmpCards + 1, cbCardCount do
                cbResultCard[cbResultCount][i] = self:MakeCardData(self:GetCardValue(tmpCards[1]), 4)
            end
        end
        cbResultCount = cbResultCount+1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[4] = cbResultCardType                

        if cbTurnType > 0 then
            cbResultCount = cbResultCount - 1
            tagSearchCardResult[1] = cbResultCount
            return tagSearchCardResult
        end
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_SINGLE) and cbCardCount == 1 then
        cbResultCardCount[cbResultCount] = cbCardCount
        cbResultCardType[cbResultCount] = GameLogic.CT_SINGLE
        cbResultCard[cbResultCount] = {}
        cbResultCard[cbResultCount][1] = cbCardData[1]
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[4] = cbResultCardType 
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_DOUBLE) and cbCardCount == 2 then
        cbResultCardCount[cbResultCount] = cbCardCount
        cbResultCardType[cbResultCount] = GameLogic.CT_DOUBLE
        cbResultCard[cbResultCount] = {}
        if #tmpCards > 0 then
            cbResultCard[cbResultCount][1] = tmpCards[1]
            cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(tmpCards[1]), 4)
        else
            cbResultCard[cbResultCount][1] = cbCardData[1]
            cbResultCard[cbResultCount][2] = cbCardData[2]
        end
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[4] = cbResultCardType
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_THREE) and cbCardCount == 3 then
        cbResultCardCount[cbResultCount] = cbCardCount
        cbResultCardType[cbResultCount] = GameLogic.CT_THREE
        cbResultCard[cbResultCount] = {}
    
        local isAdd = false
        if cbLaiziCardCount ~= 3 and analyseResult1[1][3 - cbLaiziCardCount] > 0  then
            for i = 1, #tmpCards do
                cbResultCard[cbResultCount][i] = tmpCards[i]
            end
            for i = #tmpCards + 1, 3 do
                cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(tmpCards[1]), 4)
            end
            isAdd = true
        elseif cbLaiziCardCount == 3 then
            for i = 1, 3 do
                cbResultCard[cbResultCount][i] = cbCardData[i]
            end
            isAdd = true
        end
        
        if isAdd == true then
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        
    end
    
    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_THREE_TAKE_ONE) and 
        cbCardCount == 4 then
            -- 软炸
            --[[
            if cbLaiziCardCount ~= 4 and analyseResult[1][4 - cbLaiziCardCount] > 0 then
                cbResultCardCount[cbResultCount] = cbCardCount
                cbResultCardType[cbResultCount] = GameLogic.CT_BOMB_SOFT
                cbResultCard[cbResultCount] = {}
                if #tmpCards > 0 then
                    for i = 1, #tmpCards do
                        cbResultCard[cbResultCount][i] = tmpCards[i]
                    end
                    for i = #tmpCards + 1, 3 do
                        cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(tmpCards[1]), 4)
                    end
                end
                cbResultCount = cbResultCount+1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType                
            else  -- 3带1
            --]]
                if cbLaiziCardCount == 1 and analyseResult1[1][2] == 1 then
                    cbResultCardCount[cbResultCount] = cbCardCount
                    cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_ONE
                    cbResultCard[cbResultCount] = {}
                    if #tmpCards > 0 then
                        for i = 1, 2 do
                            cbResultCard[cbResultCount][i] = analyseResult1[2][2][i]
                        end
                        cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][2][1]), 4)
                        cbResultCard[cbResultCount][4] = analyseResult1[2][1][1]
                    end
                    cbResultCount = cbResultCount+1
                    tagSearchCardResult[2] = cbResultCardCount
                    tagSearchCardResult[3] = cbResultCard
                    tagSearchCardResult[4] = cbResultCardType
                elseif cbLaiziCardCount == 2 and analyseResult1[1][1] == 2 then
                        cbResultCardCount[cbResultCount] = cbCardCount
                        cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_ONE
                        cbResultCard[cbResultCount] = {}
                        if #tmpCards > 0 then
                            cbResultCard[cbResultCount][1] = analyseResult1[2][1][1]
                            cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                            cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                            cbResultCard[cbResultCount][4] = analyseResult1[2][1][2]
                        end
                        cbResultCount = cbResultCount+1
                        tagSearchCardResult[2] = cbResultCardCount
                        tagSearchCardResult[3] = cbResultCard
                        tagSearchCardResult[4] = cbResultCardType
                end
            --end
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_THREE_TAKE_TWO) and cbCardCount == 5 then
        if cbLaiziCardCount == 1 then
            if analyseResult1[1][3] == 1 then
                cbResultCardCount[cbResultCount] = cbCardCount
                cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_TWO
                cbResultCard[cbResultCount] = {}
                if #tmpCards > 0 then
                    for i = 1, 3 do
                        cbResultCard[cbResultCount][i] = analyseResult[2][3][i]
                    end
                    cbResultCard[cbResultCount][4] = analyseResult[2][1][1]
                    cbResultCard[cbResultCount][5] = self:MakeCardData(self:GetCardValue(analyseResult[2][1][1]), 4)
                end
                cbResultCount = cbResultCount+1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            elseif analyseResult1[1][2] == 2 then
                cbResultCardCount[cbResultCount] = cbCardCount
                cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_TWO
                cbResultCard[cbResultCount] = {}
                --if #tmpCards > 0 then
                    for i = 1, 2 do
                        cbResultCard[cbResultCount][i] = analyseResult1[2][2][i]
                    end
                    cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][2][1]), 4)
                    for i = 1, 2 do
                        cbResultCard[cbResultCount][3 + i] = analyseResult1[2][2][2 + i]
                    end
                --end
                cbResultCount = cbResultCount+1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            end
        elseif cbLaiziCardCount == 2 then
            --一对一单 找到最大值
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_TWO
            cbResultCard[cbResultCount] = {}

            local isAdd = false
            -- 单大
            if analyseResult1[1][1] > 0 and analyseResult1[1][2] > 0 and
                self:GetCardLogicValue(analyseResult1[2][1][1]) > self:GetCardLogicValue(analyseResult1[2][2][1]) then
                cbResultCard[cbResultCount][1] = analyseResult1[2][1][1]
                cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                for i = 1, 2 do
                    cbResultCard[cbResultCount][3 + i] = analyseResult[2][2][i]
                end
                isAdd = true
            elseif analyseResult1[1][3] == 1 then
                cbResultCard[cbResultCount][1] = analyseResult1[3][3][1]
                cbResultCard[cbResultCount][2] = analyseResult1[3][3][2]
                cbResultCard[cbResultCount][3] = analyseResult1[3][3][3]
                for i = 1, 2 do
                    cbResultCard[cbResultCount][3 + i] = tmpLaizdCards[i]
                end
                isAdd = true
            else --对大
                if analyseResult1[1][2] > 0 then
                    for i = 1, 2 do
                        cbResultCard[cbResultCount][i] = analyseResult1[2][2][i]
                    end
                    cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][2][1]), 4)
                    cbResultCard[cbResultCount][4] = analyseResult1[2][1][1]
                    cbResultCard[cbResultCount][5] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                    isAdd = true
                end
                
            end
            if isAdd then
                cbResultCount = cbResultCount+1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            end
        elseif cbLaiziCardCount == 3 then
            --一对一单 找到最大值
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_TWO
            cbResultCard[cbResultCount] = {}
    
            --假如2单
            if analyseResult1[1][1] == 2 then
                cbResultCard[cbResultCount][1] = analyseResult1[2][1][1]
                cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]), 4)
                cbResultCard[cbResultCount][4] = analyseResult1[2][1][2]
                cbResultCard[cbResultCount][5] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][2]), 4)
            else --一对
                --对子大
                if analyseResult1[1][2] > 0 and  
                    self:GetCardLogicValue(analyseResult1[2][2][1]) > GameLogic.cbLaiziLogicVal then
                    for i = 1, 2 do
                        cbResultCard[cbResultCount][i] = analyseResult1[2][2][i]
                    end
                    cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][2][1]) ,4)
                    for i = 1, 2 do
                        cbResultCard[cbResultCount][3 + i] = tmpLaizdCards[i + 1]
                    end
                    isAdd = true
                else--癞子大
                    for i = 1, 3 do
                        cbResultCard[cbResultCount][i] = tmpLaizdCards[i]
                    end
                    for i = 1, 2 do
                        cbResultCard[cbResultCount][i + 3] = tmpCards[i]
                    end
                end
            end
                       
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        elseif cbLaiziCardCount == 4 then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_TWO
            cbResultCard[cbResultCount] = {}
            
            --单大
            if analyseResult1[1][1] > 0 and
                self:GetCardLogicValue(analyseResult1[2][1][1]) > GameLogic.cbLaiziLogicVal then
                
                cbResultCard[cbResultCount][1] = analyseResult1[2][1][1]
                cbResultCard[cbResultCount][2] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]) ,4)
                cbResultCard[cbResultCount][3] = self:MakeCardData(self:GetCardValue(analyseResult1[2][1][1]) ,4)
                for i = 1, 2 do
                    cbResultCard[cbResultCount][3 + i] = tmpLaizdCards[i + 1]
                end
            else--癞子大
                for i = 1, 3 do
                    cbResultCard[cbResultCount][i] = tmpLaizdCards[i]
                end
                
                cbResultCard[cbResultCount][4] = tmpCards[1]
                cbResultCard[cbResultCount][5] = self:MakeCardData(self:GetCardValue(tmpCards[i]) ,4)
                
            end
                   
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
    end

    --找到最大的4带1对
    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_FOUR_TAKE_ONE) and cbCardCount == 6 then
        local result = self:SearchSameCard(cbCardData, cbCardCount, 0, 4, cbLaiziCardCount)
        local maxCards = {}
        local max_val = 0
        local index = 0

        if result[1] > 0 then
            max_val = self:GetCardLogicValue(result[3][1][1])
            index = 1
            
            for i = 2, result[1] do
                local cur_val = self:GetCardLogicValue(result[3][i][1])
                if cur_val > max_val then
                    max_val = cur_val
                    index = i
                end
            end

            for i = 1, 4 do
                table.insert(maxCards, result[3][index][i])
            end

            for i = 1, cbCardCount do
                local bIsAdd = true
                local val = self:GetCardLogicValue(cbCardData[i])
                for j = 1, 4 do
                    local color = self:GetCardColor(maxCards[j])
                    if val == GameLogic.cbLaiziLogicVal and color == 0x40 then
                        bIsAdd = false
                        break
                    end

                    if val == max_val then
                        bIsAdd = false
                        break
                    end
                end
                if bIsAdd then
                    table.insert(maxCards, cbCardData[i])
                end
            end

            cbResultCardCount[cbResultCount] = cbCardCount
            if self:GetCardLogicValue(maxCards[5]) == self:GetCardLogicValue(maxCards[6]) then
                cbResultCardType[cbResultCount] = GameLogic.CT_FOUR_TAKE_ONE
            else
                cbResultCardType[cbResultCount] = GameLogic.CT_FOUR_TAKE_TWO
            end

            cbResultCard[cbResultCount] = {}
            for i = 1, 6 do
                cbResultCard[cbResultCount][i] = maxCards[i]
            end
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType

        end
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_FOUR_TAKE_TWO) and cbCardCount == 8 then
        local result = self:SearchSameCard(tmpCards, #tmpCards, 0, 4, cbLaiziCardCount)
        local maxCards = {}
        local max_val = 0
        local index = 0

        if result[1] > 0 then
            max_val = self:GetCardLogicValue(result[3][1][1])
            index = 1

            for i = 2, result[1] do
                local cur_val = self:GetCardLogicValue(result[3][i][1])
                if cur_val > max_val then
                    max_val = cur_val
                    index = i
                end 
            end
    
            for i = 1, 4 do
                table.insert(maxCards, result[3][index][i])
            end
    
            local tmpDuiCards = {}
    
            local cbLeftLaiziCount = cbLaiziCardCount
            for i = 1, cbCardCount do
                local bIsAdd = true
                local val = self:GetCardLogicValue(cbCardData[i])
                for j = 1, 4 do
                    local color = self:GetCardColor(maxCards[j])
                    if val == GameLogic.cbLaiziLogicVal and color == 0x40 and cbLeftLaiziCount > 0 then
                        bIsAdd = false
                        cbLeftLaiziCount = cbLeftLaiziCount - 1
                        break
                    end
    
                    if val == max_val then
                        bIsAdd = false
                        break
                    end
                end
                if bIsAdd then
                    table.insert(tmpDuiCards, cbCardData[i])                
                end
            end
            dump(maxCards)
            dump(tmpDuiCards)
    
            local as = self:AnalysebCardData(tmpDuiCards, #tmpDuiCards)
            local cn = self:GetLaiziNum(tmpDuiCards, #tmpDuiCards, GameLogic.cbLaiziCard)
            if cn == 1 and (as[1][2] == 1 or as[1][3] == 1) then
                if as.cbBlockCount[2] == 1 then
                    table.insert(maxCards, as[2][2][1])
                    table.insert(maxCards, as[2][2][2])
                    table.insert(maxCards, as[2][1][1])
                    table.insert(maxCards, self:MakeCardData(self:GetCardValue(as[2][1][1]), 4))
                else
                    table.insert(maxCards, as[2][3][1])
                    table.insert(maxCards, as[2][3][2])
                    table.insert(maxCards, as[2][3][3])
                    table.insert(maxCards, self:MakeCardData(self:GetCardValue(as[2][3][1]), 4))
                end
            elseif cn == 2 and as[1][1] == 2 then
                table.insert(maxCards, as[2][1][1])
                table.insert(maxCards, self:MakeCardData(self:GetCardValue(as[2][1][1]), 4))
                table.insert(maxCards, as[2][1][2])
                table.insert(maxCards, self:MakeCardData(self:GetCardValue(as[2][1][2]), 4))
            elseif cn == 3 and as[1][1] == 1 then
                table.insert(maxCards, as[2][1][1])
                table.insert(maxCards, self:MakeCardData(self:GetCardValue(as[2][1][1]), 4))
                table.insert(maxCards, as[2][3][2])
                table.insert(maxCards, as[2][3][3])
            else
                for i = 1, #tmpDuiCards do
                    table.insert(maxCards, tmpDuiCards[i])
                end
                if #maxCards < 8 then
                    local cbNeedAddCount = 8 - #maxCards
                    for i = 1, cbNeedAddCount do
                        
                        table.insert(maxCards, tmpLaizdCards[cbLaiziCardCount - cbNeedAddCount + i])
                    end
                    
                end
            end
            dump(maxCards)
    
            if self:GetCardLogicValue(maxCards[5]) == self:GetCardLogicValue(maxCards[6]) and 
                self:GetCardLogicValue(maxCards[7]) == self:GetCardLogicValue(maxCards[8]) then
                    cbResultCardCount[cbResultCount] = cbCardCount
                    cbResultCardType[cbResultCount] = GameLogic.CT_FOUR_TAKE_TWO
                    cbResultCard[cbResultCount] = {}
                    for i = 1, 8 do
                        cbResultCard[cbResultCount][i] = maxCards[i]
                    end
                    cbResultCount = cbResultCount+1
                    tagSearchCardResult[2] = cbResultCardCount
                    tagSearchCardResult[3] = cbResultCard
                    tagSearchCardResult[4] = cbResultCardType
            end
        end        
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_SINGLE_LINE) and cbCardCount >= 5 then
        print('ttttt is ', cbLaiziCardCount)
        local result = self:SearchLineCardType(cbCardData, cbCardCount, 0, 1, cbCardCount, cbLaiziCardCount)
        if result[1] > 0 then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = GameLogic.CT_SINGLE_LINE
            cbResultCard[cbResultCount] = {}
            for i = 1, cbCardCount do
                cbResultCard[cbResultCount][i] = result[3][result[1]][i]
            end
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_DOUBLE_LINE)  and cbCardCount >= 6 and math.mod(cbCardCount, 2) == 0 then
        local result = self:SearchLineCardType(cbCardData, cbCardCount, 0, 2, cbCardCount / 2, cbLaiziCardCount)
        if result[1] > 0 then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = GameLogic.CT_SINGLE_LINE
            cbResultCard[cbResultCount] = {}
            for i = 1, cbCardCount do
                cbResultCard[cbResultCount][i] = result[3][result[1]][i]
            end
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
    end

    if (cbTurnType == 0 or cbTurnType == GameLogic.CT_THREE_LINE) and cbCardCount >= 6 and math.mod(cbCardCount, 3) == 0 then
        local result = self:SearchLineCardType(cbCardData, cbCardCount, 0, 3, cbCardCount / 3, cbLaiziCardCount)
        if result[1] > 0 then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = GameLogic.CT_SINGLE_LINE
            cbResultCard[cbResultCount] = {}
            for i = 1, cbCardCount do
                cbResultCard[cbResultCount][i] = result[3][result[1]][i]
            end
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
    end

    cbResultCount = cbResultCount - 1
    tagSearchCardResult[1] = cbResultCount
    return tagSearchCardResult
end

--获取类型(此时癞子牌已经转换成正常的牌值eg:0x51)
function GameLogic:GetCardType(cbCardData, cbCardCount, cbLaiziCard)
    cbLaiziCard = cbLaiziCard or 0
    self:SortCardList(cbCardData,cbCardCount,0)
    local cbLaiziCardCount = self:GetLaiziNum(cbCardData,cbCardCount,cbLaiziCard)
    dump(cbCardData)
    
    --简单牌型
    if cbCardCount == 0 then        --空牌
        return GameLogic.CT_ERROR
    elseif cbCardCount == 1 then    --单牌
        return GameLogic.CT_SINGLE
    elseif cbCardCount == 2 then    --对牌火箭
        if cbCardData[1] == 0x4F and cbCardData[2] == 0x4E then
            return GameLogic.CT_MISSILE_CARD
        end
        if GameLogic:GetCardLogicValue(cbCardData[1]) == GameLogic:GetCardLogicValue(cbCardData[2]) then
            return GameLogic.CT_DOUBLE
        end
        if GameLogic.cbLaiziCard > 0 then
            if (cbCardData[1] == 0x4E or cbCardData[1] == 0x4F) and cbLaiziCardCount > 0 then
                return GameLogic.CT_ERROR
            end
            if cbLaiziCardCount == 1 then return GameLogic.CT_DOUBLE end
        end       

    end

    local tagAnalyseResult = GameLogic:AnalysebCardData(cbCardData, cbCardCount)
    dump(tagAnalyseResult)
    --四牌判断
    if tagAnalyseResult[1][4] > 0 then
        if tagAnalyseResult[1][4] == 1 and cbCardCount == 4 then
            local cbLaiziCount = 0
            for i = 1, cbCardCount do
                if cbCardData[i] > 0x40 and cbCardData[i] < 0x4E then 
                    cbLaiziCount = cbLaiziCount + 1
                end
            end

            if cbLaiziCount > 0 and cbLaiziCount < cbCardCount then 
                return GameLogic.CT_BOMB_SOFT
            end

            if cbLaiziCount == cbCardCount then
                return GameLogic.CT_BOMB_LAIZI
            end

            return GameLogic.CT_BOMB_CARD
        end
        if tagAnalyseResult[1][4] == 1 and cbCardCount == 6 then
            return GameLogic.CT_FOUR_TAKE_ONE
        end
        if (tagAnalyseResult[1][4] == 1 and cbCardCount == 8) and tagAnalyseResult[1][2] == 2 then
            return GameLogic.CT_FOUR_TAKE_TWO
        end
        return GameLogic.CT_ERROR
    end
    --三牌判断
    if tagAnalyseResult[1][3] > 0 then
        if tagAnalyseResult[1][3] > 1 then
            local cbCard = tagAnalyseResult[2][3][1]
            local cbFirstLogicValue = GameLogic:GetCardLogicValue(cbCard)
            if cbFirstLogicValue >= 15 then
                return GameLogic.CT_ERROR
            end
            for i=2,tagAnalyseResult[1][3] do
                local cbCard = tagAnalyseResult[2][3][(i-1)*3+1]
                local cbNextLogicValue = GameLogic:GetCardLogicValue(cbCard)
                if cbFirstLogicValue ~= cbNextLogicValue+i-1 then
                    return GameLogic.CT_ERROR
                end
            end
        elseif cbCardCount == 3 then
            return GameLogic.CT_THREE
        end
        if tagAnalyseResult[1][3]*3 == cbCardCount then
           return GameLogic.CT_THREE_LINE
        end
        if tagAnalyseResult[1][3]*4 == cbCardCount then
           return GameLogic.CT_THREE_TAKE_ONE
        end
        if tagAnalyseResult[1][3]*5 == cbCardCount  and tagAnalyseResult[1][2] == tagAnalyseResult[1][3] then
           return GameLogic.CT_THREE_TAKE_TWO
        end
        return GameLogic.CT_ERROR
    end
    --两张判断
    if tagAnalyseResult[1][2] >= 3 then
        local cbCard = tagAnalyseResult[2][2][1]
        local cbFirstLogicValue = GameLogic:GetCardLogicValue(cbCard)
        if cbFirstLogicValue >= 15 then
            return GameLogic.CT_ERROR
        end
        for i=2,tagAnalyseResult[1][2] do
            local cbCard = tagAnalyseResult[2][2][(i-1)*2+1]
            local cbNextLogicValue = GameLogic:GetCardLogicValue(cbCard)
            if cbFirstLogicValue ~= cbNextLogicValue+i-1 then
                return GameLogic.CT_ERROR
            end
        end
        if tagAnalyseResult[1][2] * 2 == cbCardCount then
            return GameLogic.CT_DOUBLE_LINE
        end
        return GameLogic.CT_ERROR
    end
    --单张判断
    if tagAnalyseResult[1][1] >= 5 and tagAnalyseResult[1][1] == cbCardCount then
        local cbCard = tagAnalyseResult[2][1][1]
        local cbFirstLogicValue = GameLogic:GetCardLogicValue(cbCard)
        if cbFirstLogicValue >= 15 then
            return GameLogic.CT_ERROR
        end
        for i=2,tagAnalyseResult[1][1] do
            local cbCard = tagAnalyseResult[2][1][i]
            local cbNextLogicValue = GameLogic:GetCardLogicValue(cbCard)
            if cbFirstLogicValue ~= cbNextLogicValue+i-1 then
                return GameLogic.CT_ERROR
            end
        end
        return GameLogic.CT_SINGLE_LINE
    end
    return GameLogic.CT_ERROR
end

--出牌搜索
function GameLogic:SearchOutCard(cbHandCardData,cbHandCardCount,cbTurnCardData,cbTurnCardCount, cbLaiziCard) 
    cbLaiziCard = cbLaiziCard or 0
    -- print("出牌搜索")
    -- for i=1,cbTurnCardCount do
    --     print("前家扑克 " .. GameLogic:GetCardLogicValue(cbTurnCardData[i]))
    -- end
    -- for i=1,cbHandCardCount do
    --     print("下家扑克 " .. GameLogic:GetCardLogicValue(cbHandCardData[i]))
    -- end
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --此牌类型
    local cbResultCardType = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard, cbResultCardType}
    --排序扑克
    local cbCardData = GameLogic:SortCardList(cbHandCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount

    --出牌分析
    local cbTurnOutType = GameLogic:GetCardType(cbTurnCardData, cbTurnCardCount)
    if cbTurnOutType == GameLogic.CT_ERROR then --错误类型
        --print("上家为空")
        --是否一手出完
        local final_type = GameLogic:GetCardType(cbCardData, cbCardCount)
        if final_type ~= GameLogic.CT_ERROR  then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCardType[cbResultCount] = final_type
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = cbCardData
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
            
        end
        --如果最小牌不是单牌，则提取
        local cbSameCount = 1
        if cbCardCount > 1 and (GameLogic:GetCardLogicValue(cbCardData[cbCardCount]) == 
            GameLogic:GetCardLogicValue(cbCardData[cbCardCount-1])) then
            cbSameCount = 2
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount][1] = cbCardData[cbCardCount]
            local cbCardValue = GameLogic:GetCardLogicValue(cbCardData[cbCardCount])
            local i = cbCardCount - 1
            while i >= 1 do
                if GameLogic:GetCardLogicValue(cbCardData[i]) == cbCardValue then
                    cbResultCard[cbResultCount][cbSameCount] = cbCardData[i]
                    cbSameCount = cbSameCount + 1
                end
                i = i - 1
            end
            cbResultCardCount[cbResultCount] = cbSameCount-1
            if cbResultCardCount[cbResultCount] == 2 then
                cbResultCardType[cbResultCount] = GameLogic.CT_DOUBLE
            elseif cbResultCardCount[cbResultCount] == 3 then
                cbResultCardType[cbResultCount] = GameLogic.CT_THREE
            elseif cbResultCardCount[cbResultCount] == 4 then
                cbResultCardType[cbResultCount] = GameLogic.CT_BOMB_CARD
            end
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --单牌
        local cbTmpCount = 1
        if cbSameCount ~= 2 then
            --print("单牌Pan")
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 1)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCardType[cbResultCount] = GameLogic.CT_SINGLE
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            end
        end
        --对牌
        if cbSameCount ~= 3 then
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 2)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCardType[cbResultCount] = GameLogic.CT_DOUBLE
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            end
        end
        --三条
        if cbSameCount ~= 4 then
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 3)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCardType[cbResultCount] = GameLogic.CT_THREE
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            end
        end
        --三带一单
        --print("三带一单")
        local tagSearchCardResult2 = GameLogic:SearchTakeCardType(cbCardData, cbCardCount, 0, 3, 1)
        cbTmpCount = tagSearchCardResult2[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult2[2][1]
            cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_ONE
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult2[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --print("三带一对")
        --三带一对
        local tagSearchCardResult3 = GameLogic:SearchTakeCardType(cbCardData, cbCardCount, 0, 3, 2)
        cbTmpCount = tagSearchCardResult3[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult3[2][1]
            cbResultCardType[cbResultCount] = GameLogic.CT_THREE_TAKE_TWO
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult3[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --单连
        print("单连")
        local tagSearchCardResult4 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 1, 0)
        cbTmpCount = tagSearchCardResult4[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult4[2][1]
            cbResultCardType[cbResultCount] = GameLogic.CT_SINGLE_LINE
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult4[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --连对
        print("连对")
        local tagSearchCardResult5 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 2, 0)
        cbTmpCount = tagSearchCardResult5[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult5[2][1]
            cbResultCardType[cbResultCount] = GameLogic.CT_DOUBLE_LINE
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult5[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --三连
        print("三连")
        local tagSearchCardResult6 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 3, 0)
        cbTmpCount = tagSearchCardResult6[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult6[2][1]
            cbResultCardType[cbResultCount] = GameLogic.CT_THREE_LINE
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult6[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --飞机
        print("飞机")
        local tagSearchCardResult7 = GameLogic:SearchThreeTwoLine(cbCardData, cbCardCount)
        cbTmpCount = tagSearchCardResult7[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult7[2][1]
            cbResultCardType[cbResultCount] = GameLogic.CT_PLANE
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult7[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        --炸弹
        if cbSameCount ~= 5 then
            --print("炸弹")
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 4)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCardType[cbResultCount] = GameLogic.CT_BOMB_CARD
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
            end
        end
        --火箭
        --print("搜索火箭")
        if (cbCardCount >= 2) and (cbCardData[1]==0x4F and cbCardData[2]==0x4E) then
            cbResultCardCount[cbResultCount] = 2
            cbResultCardType[cbResultCount] = GameLogic.CT_MISSILE_CARD
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = {cbCardData[1],cbCardData[2]}
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[4] = cbResultCardType
        end
        tagSearchCardResult[1] = cbResultCount - 1
        return tagSearchCardResult
    elseif cbTurnOutType == GameLogic.CT_SINGLE or cbTurnOutType == GameLogic.CT_DOUBLE or cbTurnOutType == GameLogic.CT_THREE then
        --单牌、对牌、三条
        local cbReferCard = cbTurnCardData[1]
        local cbSameCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE then
            cbSameCount = 2
        elseif cbTurnOutType == GameLogic.CT_THREE then
            cbSameCount = 3
        end
        local tagSearchCardResult21 = GameLogic:SearchSameCard(cbCardData, cbCardCount, cbReferCard, cbSameCount)
        cbResultCount = tagSearchCardResult21[1]
        cbResultCount = cbResultCount + 1
        cbResultCardType = tagSearchCardResult21[4]
        cbResultCardCount = tagSearchCardResult21[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult21[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
        tagSearchCardResult[4] = cbResultCardType

    elseif cbTurnOutType == GameLogic.CT_SINGLE_LINE or cbTurnOutType == GameLogic.CT_DOUBLE_LINE or cbTurnOutType == GameLogic.CT_THREE_LINE then
        --单连、对连、三连
        local cbBlockCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE_LINE then
            cbBlockCount = 2
        elseif cbTurnOutType == GameLogic.CT_THREE_LINE then
            cbBlockCount = 3
        end
        local cbLineCount = cbTurnCardCount/cbBlockCount
        local tagSearchCardResult31 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, cbTurnCardData[1], cbBlockCount, cbLineCount)
        cbResultCount = tagSearchCardResult31[1]
        cbResultCount = cbResultCount + 1
        cbResultCardType = tagSearchCardResult31[4]
        cbResultCardCount = tagSearchCardResult31[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult31[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
        tagSearchCardResult[4] = cbResultCardType

    elseif cbTurnOutType == GameLogic.CT_THREE_TAKE_ONE or cbTurnOutType == GameLogic.CT_THREE_TAKE_TWO then
        --三带一单、三带一对
        if cbCardCount >= cbTurnCardCount then
            if cbTurnCardCount == 4 or cbTurnCardCount == 5 then
                local cbTakeCardCount = (cbTurnOutType == GameLogic.CT_THREE_TAKE_ONE) and 1 or 2
                local tagSearchCardResult41 = GameLogic:SearchTakeCardType(cbCardData, cbCardCount, cbTurnCardData[3], 3, cbTakeCardCount)
                cbResultCount = tagSearchCardResult41[1]
                cbResultCount = cbResultCount + 1
                cbResultCardType = tagSearchCardResult41[4]
                cbResultCardCount = tagSearchCardResult41[2]
                tagSearchCardResult[2] = cbResultCardCount
                cbResultCard = tagSearchCardResult41[3]
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[1] = cbResultCount - 1
            else
                local cbBlockCount = 3
                local cbLineCount = cbTurnCardCount/(cbTurnOutType==GameLogic.CT_THREE_TAKE_ONE and 4 or 5)
                local cbTakeCardCount = cbTurnOutType==GameLogic.CT_THREE_TAKE_ONE and 1 or 2

                --搜索连牌
                local cbTmpTurnCard = cbTurnCardData
                cbTmpTurnCard = GameLogic:SortOutCardList(cbTmpTurnCard,cbTurnCardCount)
                local tmpSearchResult = GameLogic:SearchLineCardType(cbCardData,cbCardCount,cbTmpTurnCard[1],cbBlockCount,cbLineCount)
                cbResultCount2 = tmpSearchResult[1]
                --提取带牌
                local bAllDistill = true
                for i=1,cbResultCount2 do
                    local cbResultIndex = cbResultCount2-i+1
                    local cbTmpCardData = {}
                    for i=1,#cbCardData do
                        cbTmpCardData[i] = cbCardData[i]
                    end
                    local cbTmpCardCount = cbCardCount

                    --删除连牌
                    local removeResult = GameLogic:RemoveCard(tmpSearchResult[3][cbResultIndex],tmpSearchResult[2][cbResultIndex],cbTmpCardData,cbTmpCardCount)
                    cbTmpCardData = removeResult[2]
                    cbTmpCardCount = cbTmpCardCount - tmpSearchResult[2][cbResultIndex]
                    --分析牌
                    local TmpResult = GameLogic:AnalysebCardData(cbTmpCardData,cbTmpCardCount)
                    --提取牌
                    local cbDistillCard = {}
                    local cbDistillCount = 0
                    local j = cbTakeCardCount
                    while j <= 4 do
                        if TmpResult[1][j] > 0 then
                            if j == cbTakeCardCount and TmpResult[1][j] >= cbLineCount then
                                local cbTmpBlockCount = TmpResult[1][j]
                                for k=1,j*cbLineCount do
                                    cbDistillCard[k] = TmpResult[2][j][(cbTmpBlockCount-cbLineCount)*j+k]
                                end
                                cbDistillCount = j*cbLineCount
                                break
                            else
                                local k = 1
                                while k <= TmpResult[1][j] do
                                    local cbTmpBlockCount = TmpResult[1][j]
                                    for l=1,cbTakeCardCount do
                                        cbDistillCard[cbDistillCount+l] = TmpResult[2][j][(cbTmpBlockCount-k)*j+l]
                                    end
                                    cbDistillCount = cbDistillCount + cbTakeCardCount
                                    --提取完成
                                    if (cbDistillCount == cbTakeCardCount*cbLineCount) then
                                        break
                                    end
                                    k = k + 1
                                end
                            end
                        end
                        --提取完成
                        if (cbDistillCount == cbTakeCardCount*cbLineCount) then
                            break
                        end
                        j = j + 1
                    end
                    --提取完成
                    if (cbDistillCount == cbTakeCardCount*cbLineCount) then
                        --复制带牌
                        local cbCount = tmpSearchResult[2][cbResultIndex]
                        for n=1,cbDistillCount do
                            tmpSearchResult[3][cbResultIndex][cbCount+n] = cbDistillCard[n]
                        end
                        tmpSearchResult[2][cbResultIndex] = tmpSearchResult[2][cbResultIndex] + cbDistillCount
                    else
                        --否则删除连牌
                        bAllDistill = false
                        tmpSearchResult[2][cbResultIndex] = 0
                    end
                end
                --整理组合
                tmpSearchResult[1] = cbResultCount2
                for i=1,tmpSearchResult[1] do
                    if tmpSearchResult[2][i] ~= 0 then
                        tagSearchCardResult[2][cbResultCount] = tmpSearchResult[2][i]
                        tagSearchCardResult[3][cbResultCount] = tmpSearchResult[3][i]
                        cbResultCount = cbResultCount + 1
                        tagSearchCardResult[4][cbResultCount] = GameLogic.CT_PLANE
                    end
                end
                tagSearchCardResult[1] = cbResultCount - 1
            end
        end

    elseif cbTurnOutType == GameLogic.CT_FOUR_TAKE_ONE or cbTurnOutType == GameLogic.CT_FOUR_TAKE_TWO then
        --四带两单、四带两双
        local cbTakeCardCount = (cbTurnOutType == GameLogic.CT_FOUR_TAKE_ONE) and 1 or 2
        local cbTmpTurnCard = GameLogic:SortOutCardList(cbTurnCardData,cbTurnCardCount)
        local tagSearchCardResult51 = GameLogic:SearchTakeCardType(cbCardData, cbCardCount, cbTmpTurnCard[1], 4, cbTakeCardCount)
        cbResultCount = tagSearchCardResult51[1]
        cbResultCardType = tagSearchCardResult51[4]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult51[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult51[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
        tagSearchCardResult[4] = cbResultCardType
    end

    --搜索炸弹
    if (cbCardCount >= 4 and cbTurnOutType ~= GameLogic.CT_MISSILE_CARD) then
        local cbReferCard = 0
        if cbTurnOutType == GameLogic.CT_BOMB_CARD then
            cbReferCard = cbTurnCardData[1]
        end
        --搜索炸弹
        local tagSearchCardResult61 = GameLogic:SearchSameCard(cbCardData,cbCardCount,cbReferCard,4)
        local cbTmpResultCount = tagSearchCardResult61[1]
        for i=1,cbTmpResultCount do
            cbResultCardCount[cbResultCount] = tagSearchCardResult61[2][i]
            cbResultCardType[cbResultCount] = tagSearchCardResult61[4][i]
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = tagSearchCardResult61[3][i]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        end
        tagSearchCardResult[1] = cbResultCount - 1
    end

    --搜索火箭
    if (cbTurnOutType ~= GameLogic.CT_MISSILE_CARD) and (cbCardCount >= 2) and (cbCardData[1]==0x4F and cbCardData[2]==0x4E) then
        cbResultCardCount[cbResultCount] = 2
        cbResultCard[cbResultCount] = {cbCardData[1],cbCardData[2]}
        cbResultCardType[cbResultCount] = GameLogic.CT_MISSILE_CARD
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    end

    return tagSearchCardResult
end

--同牌搜索
function GameLogic:SearchSameCard(cbHandCardData, cbHandCardCount, cbReferCard, cbSameCardCount, cbLaiziNum)
    cbLaiziNum = cbLaiziNum or GameLogic.cbLaiziCardCount
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    local cbResultCardType = {}
    --结果扑克
    local cbResultCard = {}
    local type = GameLogic.CT_DOUBLE
    if cbSameCardCount == 2 then
        type = GameLogic.CT_DOUBLE
    elseif cbSameCardCount == 3 then
        type = GameLogic.CT_THREE
    elseif cbSameCardCount == 4 then
        type = GameLogic.CT_BOMB_CARD
    end
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard, cbResultCardType}
    --排序扑克
    
    --local cbCardData = self:RemoveKingCards(cbHandCardData) --GameLogic:SortCardList(cbHandCardData, cbHandCardCount, 0)
    local cbCardData = GameLogic:SortCardList(cbHandCardData,#cbHandCardData,0)
    local cbCardCount = #cbCardData --cbHandCardCount
    --分析结构
    local tagAnalyseResult = GameLogic:AnalysebCardData(cbCardData, cbCardCount)
    --dump(tagAnalyseResult, "tagAnalyseResult", 6)
    local cbReferLogicValue = (cbReferCard == 0 and 0 or GameLogic:GetCardLogicValue(cbReferCard))
    local cbBlockIndex = cbSameCardCount
    while cbBlockIndex <= 4 do
        for i=1,tagAnalyseResult[1][cbBlockIndex] do
            local cbIndex = (tagAnalyseResult[1][cbBlockIndex]-i)*cbBlockIndex+1
            local cbNowLogicValue = GameLogic:GetCardLogicValue(tagAnalyseResult[2][cbBlockIndex][cbIndex])
            if cbNowLogicValue > cbReferLogicValue then
                cbResultCardCount[cbResultCount] = cbSameCardCount
                cbResultCardType[cbResultCount] = type
                tagSearchCardResult[2] = cbResultCardCount
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount][1] = tagAnalyseResult[2][cbBlockIndex][cbIndex]
                for j=2,cbBlockIndex do
                    cbResultCard[cbResultCount][j] = tagAnalyseResult[2][cbBlockIndex][cbIndex+j-1]
                end --此处修改  
                tagSearchCardResult[3] = cbResultCard
                tagSearchCardResult[4] = cbResultCardType
                cbResultCount = cbResultCount + 1
            end
        end
        cbBlockIndex = cbBlockIndex + 1
    end
    
    if cbLaiziNum > 0 then
        local cbBlockIndex = 1
        while cbBlockIndex < cbSameCardCount do
            for i=1,tagAnalyseResult[1][cbBlockIndex] do repeat
                local cbIndex = (tagAnalyseResult[1][cbBlockIndex]-i)*cbBlockIndex+1
                if tagAnalyseResult[2][cbBlockIndex][cbIndex] == 0x4E or 
                    tagAnalyseResult[2][cbBlockIndex][cbIndex] == 0x4F then
                    break
                end
                local cbNowLogicValue = GameLogic:GetCardLogicValue(tagAnalyseResult[2][cbBlockIndex][cbIndex])
                if cbNowLogicValue == GameLogic.cbLaiziLogicVal then
                    break
                end
                if cbNowLogicValue > cbReferLogicValue and 
                    cbBlockIndex + cbLaiziNum >= cbSameCardCount and 
                    cbNowLogicValue ~= 0x0E + 2 and cbNowLogicValue ~= 0x0F + 2 then
                    cbResultCardCount[cbResultCount] = cbSameCardCount
                    if cbSameCardCount == 4 then
                        cbResultCardType[cbResultCount] = GameLogic.CT_BOMB_SOFT
                    else
                        cbResultCardType[cbResultCount] = type
                    end
                    tagSearchCardResult[2] = cbResultCardCount
                    cbResultCard[cbResultCount] = {}
                    cbResultCard[cbResultCount][1] = tagAnalyseResult[2][cbBlockIndex][cbIndex]
                    local curVal = self:GetCardValue(tagAnalyseResult[2][cbBlockIndex][cbIndex])
                    for j=2,cbBlockIndex do
                        cbResultCard[cbResultCount][j] = tagAnalyseResult[2][cbBlockIndex][cbIndex+j-1]
                    end --此处修改

                    local index = 1
                    for j = cbBlockIndex + 1, cbSameCardCount do
                        cbResultCard[cbResultCount][j] = self:MakeCardData(curVal, 4)
                        index = index + 1
                    end

                    tagSearchCardResult[3] = cbResultCard
                    tagSearchCardResult[4] = cbResultCardType
                    cbResultCount = cbResultCount + 1
                end
            until true
            end
            cbBlockIndex = cbBlockIndex + 1
        end
    end
    
    tagSearchCardResult[1] = cbResultCount - 1
    print('search same card is ... ')
    dump(tagSearchCardResult)
    return tagSearchCardResult
end

--带牌类型搜索(三带一，四带一等)
function GameLogic:SearchTakeCardType(cbHandCardData, cbHandCardCount, cbReferCard, cbSameCount, cbTakeCardCount)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    local cbResultCardType = {}
    --结果扑克
    local cbResultCard = {}
    local type = nil
    if cbSameCount == 3 and cbTakeCardCount == 1 then
        type = GameLogic.CT_THREE_TAKE_ONE
    elseif cbSameCount == 3 and cbTakeCardCount == 2 then
        type = GameLogic.CT_THREE_TAKE_TWO
    elseif cbSameCount == 4 and cbTakeCardCount == 1 then
        type = GameLogic.CT_FOUR_TAKE_ONE
    elseif cbSameCount == 4 and cbTakeCardCount == 2 then
        type = GameLogic.CT_FOUR_TAKE_TWO
    end
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard, cbResultCardType, cbResultCardType}
    if cbSameCount ~= 3 and cbSameCount ~= 4 then
        return tagSearchCardResult
    end
    if cbTakeCardCount ~= 1 and cbTakeCardCount ~= 2 then
        return tagSearchCardResult
    end
    if (cbSameCount == 4) and (cbHandCardCount < cbSameCount+cbTakeCardCount*2 or cbHandCardCount < cbSameCount+cbTakeCardCount) then
        return tagSearchCardResult
    end
    --排序扑克
    local cbCardData = GameLogic:SortCardList(cbHandCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount
    
    local sameCardResult = {}
    sameCardResult = GameLogic:SearchSameCard(cbCardData, cbCardCount, cbReferCard, cbSameCount)
    local cbSameCardResultCount = sameCardResult[1]

    if cbSameCardResultCount > 0 then
        --分析结构
        local tagAnalyseResult = GameLogic:AnalysebCardData(cbCardData, cbCardCount)
        --需要牌数
        local cbNeedCount = cbSameCount + cbTakeCardCount
        if cbSameCount == 4 then
            cbNeedCount = cbNeedCount + cbTakeCardCount
        end
        --提取带牌
        for i=1,cbSameCardResultCount do
            local bMere = false
            local j = cbTakeCardCount
            while j <= 4 do
                local k = 1
                while k <= tagAnalyseResult[1][j]  do
                    --从小到大
                    local cbIndex = (tagAnalyseResult[1][j]-k)*j+1
                    if GameLogic:GetCardLogicValue(sameCardResult[3][i][1]) ~= GameLogic:GetCardLogicValue(tagAnalyseResult[2][j][cbIndex]) then
                        --复制带牌
                        local cbCount = sameCardResult[2][i]
                        for l=1,cbTakeCardCount do
                            sameCardResult[3][i][cbCount+l] = tagAnalyseResult[2][j][cbIndex+l-1]
                        end
                        sameCardResult[2][i] = sameCardResult[2][i] + cbTakeCardCount
                        if sameCardResult[2][i] >= cbNeedCount then
                            --复制结果
                            cbResultCardCount[cbResultCount] = sameCardResult[2][i]
                            cbResultCardType[cbResultCount] = type
                            tagSearchCardResult[2] = cbResultCardCount
                            cbResultCard[cbResultCount] = {}
                            cbResultCard[cbResultCount] = sameCardResult[3][i]
                            tagSearchCardResult[3] = cbResultCard
                            tagSearchCardResult[4] = cbResultCardType
                            cbResultCount = cbResultCount + 1
                            tagSearchCardResult[1] = cbResultCount - 1
                            bMere = true
                            --下一组合
                            break
                        end
                    end
                    k = k+1
                end
                if bMere == true then
                    break
                end
                j = j + 1
            end
        end
    end
    tagSearchCardResult[1] = cbResultCount - 1
    return tagSearchCardResult
end

function GameLogic:GetLinkCard(cbCardData, cbCardCount)
    -- body
end

--连牌搜索
function GameLogic:SearchLineCardType(cbHandCardData, cbHandCardCount, cbReferCard, cbBlockCount, cbLineCount, cbKingNum)
    local bIsBaiBian = false
    cbKingNum = cbKingNum or 0
    if cbKingNum == 0 and GameLogic.cbLaiziCardCount > 0 then
        bIsBaiBian = true
        cbKingNum = GameLogic.cbLaiziCardCount
    end
    if cbKingNum > 0 then
        bIsBaiBian = true
    end
    print('cur search is baibian ', bIsBaiBian, cbKingNum)
    local type = nil
    if cbBlockCount == 1 then
        type = GameLogic.CT_SINGLE_LINE
    elseif cbBlockCount == 2 then
        type = GameLogic.CT_DOUBLE_LINE
    elseif cbBlockCount == 3 then
        type = GameLogic.CT_THREE_LINE
    end
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    local cbResultCardType = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard, cbResultCardType}
    --排序扑克
    local tmpCards = GameLogic:RemoveLaiziCards(cbHandCardData) 
    local cbCardData = GameLogic:SortCardList(tmpCards, #tmpCards, 0)--GameLogic:SortCardList(cbHandCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount --cbHandCardCount
    --连牌最少数
    local cbLessLineCount = 0
    if cbLineCount == 0 then
        if cbBlockCount == 1 then
            cbLessLineCount = 5
        elseif cbBlockCount == 2 then
            cbLessLineCount = 3
        else
            cbLessLineCount = 2
        end
    else
        cbLessLineCount = cbLineCount
    end
    print("连牌最少数 " , cbLessLineCount , cbKingNum, cbLineCount)
    local cbReferIndex = 3
    if cbReferCard ~= 0 then
        if (GameLogic:GetCardLogicValue(cbReferCard)-cbLessLineCount) >= 2 then
            cbReferIndex = GameLogic:GetCardLogicValue(cbReferCard)-cbLessLineCount+1+1
        end
    end 
    --超过A
    if cbReferIndex+cbLessLineCount > 15 then
        return tagSearchCardResult
    end
    --长度判断
    if cbHandCardCount < cbLessLineCount*cbBlockCount then
        return tagSearchCardResult
    end
    print("搜索顺子开始点 " .. cbReferIndex, cbBlockCount)
    local Distributing = GameLogic:AnalysebDistributing(cbCardData, #cbCardData)
    --dump(Distributing)
    --搜索顺子
    local cbNeedKingNum = 0
    local cbTmpLinkCount = 0
    local cbValueIndex=cbReferIndex
    local flag = false
    local cbNeedKingNumCp = 0
    local restartValIndex = cbValueIndex + 1
    while cbValueIndex <= 13 do
        print('cur cbValueIndex is ', cbValueIndex)
        if cbResultCard[cbResultCount] == nil then
            cbResultCard[cbResultCount] = {}
        end
        if Distributing[2][cbValueIndex][6] < cbBlockCount then
            if cbTmpLinkCount < cbLessLineCount  then
                if bIsBaiBian == false then
                    cbTmpLinkCount = 0
                    cbNeedKingNum = 0
                    --cbValueIndex = restartValIndex
                    --restartValIndex = restartValIndex + 1
                    flag = false
                else
                    cbTmpLinkCount = cbTmpLinkCount + 1
                    cbNeedKingNum = cbNeedKingNum + (cbBlockCount - Distributing[2][cbValueIndex][6])
                    if cbNeedKingNum > cbKingNum then
                        --[[
                        if cbTmpLinkCount > 1 then
                            cbValueIndex = cbValueIndex - 1
                        end
                        --]]
                        print('cur king num is reset ', cbNeedKingNum, restartValIndex)
                        cbTmpLinkCount = 0
                        cbNeedKingNum = 0
                        --cbValueIndex = cbValueIndex - 1
                        flag = true
                        cbValueIndex = restartValIndex - 1
                        restartValIndex = restartValIndex + 1
                    else
                        print('cur king num is ', cbNeedKingNum, cbValueIndex)
                        if cbLineCount == 0 then 
                            flag = false
                        else
                            flag = true
                        end
                    end
                end
            else
                cbValueIndex = cbValueIndex - 1
                flag = true
            end
        else
            cbTmpLinkCount = cbTmpLinkCount + 1
            if cbLineCount == 0 then
                flag = false
            else
                flag = true
            end
        end
        print('cbNeedKingNum is ', cbNeedKingNum, cbTmpLinkCount, cbLessLineCount)
        if flag == true then
            flag = false
            if cbTmpLinkCount >= cbLessLineCount and cbNeedKingNum <= cbKingNum then
                --复制扑克
                local cbCount = 0
                local cbIndex=(cbValueIndex-cbTmpLinkCount+1)
                while cbIndex <= cbValueIndex do
                    local cbTmpCount = 0
                    local cbColorIndex=1
                    while cbColorIndex <= 4 do --在四色中取一个
                        local cbColorCount = 1
                        while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                            cbCount = cbCount + 1
                            cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex, 5-cbColorIndex - 1)
                            tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                            cbTmpCount = cbTmpCount + 1
                            if cbTmpCount == cbBlockCount then
                                break
                            end
                            cbColorCount = cbColorCount + 1
                        end
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorIndex = cbColorIndex + 1
                        if cbColorIndex > 4 and cbTmpCount < cbBlockCount then
                            for i = 1, cbBlockCount - cbTmpCount do
                                cbCount = cbCount + 1
                                cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex, 4)
                                tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                                cbTmpCount = cbTmpCount + 1
                            end
                            break
                        end
                    end
                    cbIndex = cbIndex + 1
                end
                tagSearchCardResult[2][cbResultCount] = cbCount
                tagSearchCardResult[4][cbResultCount] = type
                cbResultCount = cbResultCount + 1
                if cbLineCount ~= 0 then
                    cbTmpLinkCount = cbTmpLinkCount - 1
                else
                    cbTmpLinkCount = 0
                end
            end
        end
        cbValueIndex = cbValueIndex + 1
    end
    print('search line out a')
    dump(tagSearchCardResult)

    --特殊顺子(寻找A)
    if cbTmpLinkCount >= cbLessLineCount-1 and cbValueIndex == 14 then
        --print("特殊顺子(寻找A)")
        if (Distributing[2][1][6] >= cbBlockCount) or (cbTmpLinkCount >= cbLessLineCount)
        or (cbKingNum > cbNeedKingNum) then
            if cbResultCard[cbResultCount] == nil then
                cbResultCard[cbResultCount] = {}
            end
            --复制扑克
            local cbCount = 0
            local cbIndex=(cbValueIndex-cbTmpLinkCount)
            while cbIndex <= 13 do
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                    if cbColorIndex > 4 and cbTmpCount < cbBlockCount then
                        for i = 1, cbBlockCount - cbTmpCount do
                            cbCount = cbCount + 1
                            cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex, 4)
                            tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                            cbTmpCount = cbTmpCount + 1
                        end
                        break
                    end
                end
                cbIndex = cbIndex + 1
            end
            --复制A
            if Distributing[2][1][6] >= cbBlockCount then
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][1][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(1,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                        if cbColorIndex > 4 and cbTmpCount < cbBlockCount then
                            for i = 1, cbBlockCount - cbTmpCount do
                                cbCount = cbCount + 1
                                cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex, 4)
                                tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                                cbTmpCount = cbTmpCount + 1
                            end
                            break
                        end
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
            end
            tagSearchCardResult[2][cbResultCount] = cbCount
            tagSearchCardResult[4][cbResultCount] = type
            cbResultCount = cbResultCount + 1
        end
    end

    tagSearchCardResult[1] = cbResultCount - 1
    dump(tagSearchCardResult)
    return tagSearchCardResult
end

--飞机搜索
function GameLogic:SearchThreeTwoLine(cbHandCardData, cbHandCardCount)
    --print("飞机搜索")
    --结果数目
    local cbSearchCount = 0
    --扑克数目
    local cbResultCardCount = {}
    local cbResultCardTYpe = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbSearchCount,cbResultCardCount,cbResultCard, cbResultCardTYpe}
    local tmpSingleWing = {cbSearchCount,cbResultCardCount,cbResultCard}
    local tmpDoubleWing = {cbSearchCount,cbResultCardCount,cbResultCard}

    --排序扑克
    local cbCardData = GameLogic:SortCardList(cbHandCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount

    local cbTmpResultCount = 0

    --搜索连牌
    local tmpSearchResult = GameLogic:SearchLineCardType(cbHandCardData,cbHandCardCount,0,3,0)
    cbTmpResultCount = tmpSearchResult[1]
    if cbTmpResultCount > 0 then
        --提取带牌
        local i = 1
        while i <= cbTmpResultCount do
            local flag = true
            local cbTmpCardData = {}
            local cbTmpCardCount = cbHandCardCount
            --不够牌
            if cbHandCardCount-tmpSearchResult[2][i] < tmpSearchResult[2][i]/3 then
                local cbNeedDelCount = 3
                while (cbHandCardCount + cbNeedDelCount - tmpSearchResult[2][i]) < (tmpSearchResult[2][i]-cbNeedDelCount)/3 do
                    cbNeedDelCount = cbNeedDelCount + 3
                end
                --不够连牌
                if (tmpSearchResult[2][i]-cbNeedDelCount)/3 < 2 then
                    flag = false
                else
                    flag = true
                end
                if flag == true then
                    --拆分连牌
                    local removeResult= GameLogic:RemoveCard(tmpSearchResult[3][i],cbNeedDelCount,tmpSearchResult[3][i],tmpSearchResult[2][i])
                    tmpSearchResult[3][i] = removeResult[2]
                    tmpSearchResult[2][i] = tmpSearchResult[2][i] - cbNeedDelCount
                end
            end
            if flag == true then
                flag = false
                --删除连牌
                for temp=1,#cbHandCardData do
                    cbTmpCardData[temp] = cbHandCardData[temp]
                end
                local removeResult1= GameLogic:RemoveCard(tmpSearchResult[3][i],tmpSearchResult[2][i],cbTmpCardData,cbTmpCardCount)
                cbTmpCardData = removeResult1[2]
                cbTmpCardCount = cbTmpCardCount - tmpSearchResult[2][i]

                --组合飞机
                local cbNeedCount = tmpSearchResult[2][i]/3
                local cbResultCount = tmpSingleWing[1]+1
                tmpSingleWing[3][cbResultCount] = tmpSearchResult[3][i]
                for j=1,cbNeedCount do
                    tmpSingleWing[3][cbResultCount][tmpSearchResult[2][i]+j] = cbTmpCardData[cbTmpCardCount-cbNeedCount+j]
                end
                tmpSingleWing[2][i] = tmpSearchResult[2][i] + cbNeedCount
                tmpSingleWing[1] = tmpSingleWing[1]+1

                local flag2 = true
                --不够带翅膀
                if cbTmpCardCount < tmpSearchResult[2][i]/3*2 then
                    local cbNeedDelCount = 3
                    while (cbTmpCardCount + cbNeedDelCount - tmpSearchResult[2][i]) < (tmpSearchResult[2][i]-cbNeedDelCount)/3*2 do
                        cbNeedDelCount = cbNeedDelCount + 3
                    end
                    --不够连牌
                    if (tmpSearchResult[2][i]-cbNeedDelCount)/3 < 2 then
                        flag2 = false
                    else
                        flag2 = true
                    end
                    if flag2 == true then
                        --拆分连牌
                        local removeResult= GameLogic:RemoveCard(tmpSearchResult[3][i],cbNeedDelCount,tmpSearchResult[3][i],tmpSearchResult[2][i])
                        tmpSearchResult[3][i] = removeResult[2]
                        tmpSearchResult[2][i] = tmpSearchResult[2][i] - cbNeedDelCount

                        --重新删除连牌
                        for temp=1,#cbHandCardData do
                            cbTmpCardData[temp] = cbHandCardData[temp]
                        end
                        local removeResult2= GameLogic:RemoveCard(tmpSearchResult[3][i],tmpSearchResult[2][i],cbTmpCardData,cbTmpCardCount)
                        cbTmpCardData = removeResult2[2]
                        cbTmpCardCount = cbTmpCardCount - tmpSearchResult[2][i]
                    end
                end
                if flag2 == true then
                    flag2 = false
                    --分析牌
                    local TmpResult = {}
                    TmpResult = GameLogic:AnalysebCardData(cbTmpCardData, cbTmpCardCount)
                    --提取翅膀
                    local cbDistillCard = {}
                    local cbDistillCount = 0
                    local cbLineCount = tmpSearchResult[2][i]/3
                    local j = 2
                    while j <= 4 do
                        if TmpResult[1][j] > 0 then
                            if (j+1 == 3) and TmpResult[1][j] >= cbLineCount then
                                local  cbTmpBlockCount = TmpResult[1][j]
                                for k=1,j*cbLineCount do
                                    cbDistillCard[k] = TmpResult[2][j][(cbTmpBlockCount-cbLineCount)*j+k]
                                end
                                cbDistillCount = j*cbLineCount
                            else
                                local k = 1
                                while k <= TmpResult[1][j] do
                                    local cbTmpBlockCount = TmpResult[1][j]
                                    for l=1,2 do
                                        cbDistillCard[cbDistillCount+l] = TmpResult[2][j][(cbTmpBlockCount-k)*j+l]
                                    end
                                    cbDistillCount = cbDistillCount + 2

                                    --提取完成
                                    if cbDistillCount == 2*cbLineCount then
                                        break
                                    end
                                    k = k + 1
                                end
                            end
                        end
                        --提取完成
                        if cbDistillCount == 2*cbLineCount then
                            break
                        end
                        j = j + 1
                    end
                    
                    --提取完成
                    if cbDistillCount == 2*cbLineCount then
                        --print("复制两对")
                        --复制翅膀
                        tmpDoubleWing[1] = tmpDoubleWing[1]+1
                        cbResultCount = tmpDoubleWing[1]
                        tmpDoubleWing[3][cbResultCount] = tmpSearchResult[3][i]
                        for n=1,cbDistillCount do
                            tmpDoubleWing[3][cbResultCount][tmpSearchResult[2][i]+n] = cbDistillCard[n]
                        end
                        tmpDoubleWing[2][cbResultCount] = tmpSearchResult[2][i] + cbDistillCount
                    end
                end
            end
            i = i + 1
        end
        --复制结果
        for m=1,tmpDoubleWing[1] do
            tagSearchCardResult[1] = tagSearchCardResult[1] + 1
            local cbResultCount = tagSearchCardResult[1]
            tagSearchCardResult[3][cbResultCount] = tmpDoubleWing[3][m]
            tagSearchCardResult[2][cbResultCount] = tmpDoubleWing[2][m]
            tagSearchCardResult[4][cbResultCount] = GameLogic.CT_PLANE
        end
        for m=1,tmpSingleWing[1] do
            tagSearchCardResult[1] = tagSearchCardResult[1] + 1
            local cbResultCount = tagSearchCardResult[1]
            tagSearchCardResult[3][cbResultCount] = tmpSingleWing[3][m]
            tagSearchCardResult[2][cbResultCount] = tmpSingleWing[2][m]
            tagSearchCardResult[4][cbResultCount] = GameLogic.CT_PLANE
        end
    end
    return tagSearchCardResult
end

--分析分布
function GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    local cbCardCount1 = 0
    local cbDistributing = {}
    for i=1,15 do
        local distributing = {}
        for j=1,6 do
            distributing[j] = 0
        end
        cbDistributing[i] = distributing
    end
    local Distributing = {cbCardCount1,cbDistributing}
    for i=1,cbCardCount do
        if cbCardData[i]~=0 then
            local cbCardColor = GameLogic:GetCardColor(cbCardData[i])
            local cbCardValue = GameLogic:GetCardValue(cbCardData[i])
            --分布信息
            cbCardCount1 = cbCardCount1 + 1
            cbDistributing[cbCardValue][5+1] = cbDistributing[cbCardValue][6]+1
            local color = bit:_rshift(cbCardColor,4) + 1
            cbDistributing[cbCardValue][color] = cbDistributing[cbCardValue][color]+1
        end
    end
    Distributing[1] = cbCardCount1
    Distributing[2] = cbDistributing
    -- print("总数：" .. Distributing[1])
    -- for i=1,15 do
    --     print("每张总数：" .. Distributing[2][i][6])
    -- end
    return Distributing
end

--构造扑克
function GameLogic:MakeCardData(cbValueIndex,cbColorIndex)
    --print("构造扑克 " ..bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)..",".. GameLogic:GetCardLogicValue(bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)))
    return bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)
end

---删除扑克
function GameLogic:RemoveCard(cbRemoveCard, cbRemoveCount, cbCardData, cbCardCount)
    local cbDeleteCount=0
    local cbTempCardData = {}
    for i=1,#cbCardData do
        cbTempCardData[i] = cbCardData[i]
    end
    local result = {false,cbCardData}
    --置零扑克
    local i = 1
    while i <= cbRemoveCount do
        local j = 1
        while j < cbCardCount do
            if cbRemoveCard[i] == cbTempCardData[j] then
                cbDeleteCount = cbDeleteCount + 1
                cbTempCardData[j] = 0
                break
            end
            j = j + 1
        end
        i = i + 1
    end
    if cbDeleteCount ~= cbRemoveCount then
        return result
    end
    --清理扑克
    local cbCardPos=1
    local datas = {}
    for i=1,cbCardCount do
        if cbTempCardData[i] ~= 0 then
            datas[cbCardPos] = cbTempCardData[i]
            cbCardPos = cbCardPos + 1
        end
    end
    result = {true,datas}
    return result
end 

--排列扑克
function GameLogic:SortOutCardList(cbCardData,cbCardCount)
    local resultCardData = {}
    local resultCardCount = 0
    --获取牌型
    local cbCardType = GameLogic:GetCardType(cbCardData,cbCardCount)
    if cbCardType == GameLogic.CT_THREE_TAKE_ONE or cbCardType == GameLogic.CT_THREE_TAKE_TWO then
        --分析牌
        local AnalyseResult = {}
        AnalyseResult = GameLogic:AnalysebCardData(cbCardData,cbCardCount)

        resultCardCount = AnalyseResult[1][3]*3
        resultCardData = AnalyseResult[2][3]
        
        for i=4,1,-1 do
            if i ~= 3 then
                if AnalyseResult[1][i] > 0 then
                    for j=1,AnalyseResult[1][i]*i do
                        resultCardData[resultCardCount+j] = AnalyseResult[2][i][j]
                    end
                    resultCardCount = resultCardCount + AnalyseResult[1][i]*i
                end
            end
        end
       
    elseif cbCardType == GameLogic.CT_FOUR_TAKE_ONE or cbCardType == GameLogic.CT_FOUR_TAKE_TWO then
        --分析牌
        local AnalyseResult = {}
        AnalyseResult = GameLogic:AnalysebCardData(cbCardData,cbCardCount)

        resultCardCount = AnalyseResult[1][4]*4
        resultCardData = AnalyseResult[2][4]
        
        for i=4,1,-1 do
            if i ~= 3 then
                if AnalyseResult[1][i] > 0 then
                    for j=1,AnalyseResult[1][i]*i do
                        resultCardData[resultCardCount+j] = AnalyseResult[2][i][j]
                    end
                    resultCardCount = resultCardCount + AnalyseResult[1][i]*i
                end
            end
        end
    end
    return resultCardData
end


return GameLogic
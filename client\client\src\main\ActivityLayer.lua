-------------------------------------------------------------------------------
--  创世版1.0
--  活动
--  @date 2017-07-26
--  @auth woodoo
-------------------------------------------------------------------------------
local ActivityLayer = class("ActivityLayer", cc.Layer)

local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")

-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ActivityLayer:ctor(activities, default_index)
    self:enableNodeEvents()
    self.m_activities = activities
    self.m_default_index = default_index

    -- 载入主UI
    local main_node = helper.app.loadCSB('ActivityLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('tab_template'):hide()

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function ActivityLayer:onEnterTransitionFinish()
    print('ActivityLayer:onEnterTransitionFinish...')
    self:createTabs()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ActivityLayer:onExit()
    print('ActivityLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 活动列表生成
-------------------------------------------------------------------------------
function ActivityLayer:createTabs()
    local listview = self.main_node:child('listview_tab')
    local template = self.main_node:child('tab_template')
    for i, data in ipairs(self.m_activities) do
        local tab = template:clone():show()
        tab.data = data
        tab.group = 1
        tab:child('text'):setString(data.title)
        helper.logic.initImageCheck(tab, false, 'common/btn_tab_s.png', 'common/btn_tab_n.png', 
            handler(self, self.onBtnItem))
        listview:pushBackCustomItem(tab)
    end
    template:removeFromParent()

	local first_item = listview:getItem( (self.m_default_index or 1) - 1 )
    helper.layout.scrollToItem(listview, first_item)
    first_item:toggle()
    self:onBtnItem(first_item)
end


-------------------------------------------------------------------------------
-- 活动点击
-------------------------------------------------------------------------------
function ActivityLayer:onBtnItem(sender)
    local data = sender.data
    if data.id == self.m_cur_id then return end
    --dump(data, '活动点击', 9)
    self.m_cur_id = data.id

    local listview = self.main_node:child('listview_content')
    listview:removeAllItems()
    self.main_node:removeChildByName('content_node')

    local res = data.res
    local arr = res:split('.')
    if arr[#arr] ~= 'lua' and arr[#arr] ~= 'luac' then
        local add = function(path)
            if tolua.isnull(self) then
                return
            end
            local img = ccui.ImageView:create()
            img:texture(path)
            listview:pushBackCustomItem(img)
            listview:show():jumpToTop()
            if data.btn == '' then
                return
            end
            local save_path, save_name, use_path = helper.app.getDownloadParam('activity', data.btn)
            local add_btn = function ( btn_path, taeget_node )
                if tolua.isnull(self) then
                    return
                end
                local btn = ccui.ImageView:create()
                btn:texture(btn_path)
                taeget_node:addChild(btn)
                btn:px(taeget_node:getContentSize().width/2)
                btn:py(btn:getContentSize().height/2 + 20)
                btn:setTouchEnabled(true)
                btn.data = data
                btn:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnActivity))
            end
            if cc.FileUtils:getInstance():isFileExist(use_path) then
                add_btn(use_path, img)
            else
                local callback_btn = function(filename) add_btn(use_path, img) end
                helper.app.download(data.btn, save_path, save_name, self, callback_btn)
            end
        end
        if cc.FileUtils:getInstance():isFileExist(res) then
            add(res)
        else
            local callback = function(filename) add(res) end
            helper.app.download(data.res_url, data.res_save_path, data.res_save_name, self, callback)
        end
    else
        listview:show():hide()
        table.remove(arr, #arr)
        local Object = require( table.concat(arr, '.') )
        local node = Object:create(data, listview:size())
        node:setName('content_node')
        node:anchor(0, 0):pos(listview:pos()):addTo(self.main_node)
    end
end

-------------------------------------------------------------------------------
-- 活动列表生成
-------------------------------------------------------------------------------
function ActivityLayer:onBtnActivity( sender )
    local data = sender.data
    local isEnd = true
    if data.skip_type == 'ROLE' then
        helper.link.toUser()
    elseif data.skip_type == 'MALL' then
        if table.nums(data.skip_param) <= 0 then
             helper.pop.message( LANG.WRONG_1 )
            return
        end
        helper.link.toMall( data.skip_param.name)
    elseif data.skip_type == 'PAY' then
        if table.nums(data.skip_param) <= 0 then
            helper.pop.message( LANG.WRONG_1 )
            return
        end
        isEnd = false
        self:doRequest(yl.URL_ORDER, self.onOrderResponse, {mall_id=data.skip_param.mall_id, good_id=data.skip_param.good_id})
    elseif data.skip_type == 'WHEEL' then
        helper.link.toTurntable()
    elseif data.skip_type == 'INVITE' then
        helper.link.toInvite()
    elseif data.skip_type == 'REDPACKET' then
        helper.link.toRedBag() 
    elseif data.skip_type == 'LINK' then
        local channel = MultiPlatform:getInstance():getChannel()
        local uid = GlobalUserItem.dwUserID
        local sign = md5(channel .. '-' .. uid .. '-' .. yl.MY_RED_BAG_URL_KEY)
        local params = string.format('?channel_id=%s&role_id=%d&sign=%s', channel, uid, sign)
        MultiPlatform:getInstance():openBrowser(data.skip_param.url .. params)
    end
    if isEnd then
        self:removeFromParent()
    end
end

-------------------------------------------------------------------------------
-- 请求
-------------------------------------------------------------------------------
function ActivityLayer:doRequest(url, func, params)
    helper.pop.waiting()
    local all_params = {uid=GlobalUserItem.dwUserID}
    if params then
        for k, v in pairs(params) do
            all_params[k] = v
        end
    end
    yl.GetUrl(url, 'post', all_params, handler(self, func) )
end

-------------------------------------------------------------------------------
-- 购买返回
-------------------------------------------------------------------------------
function ActivityLayer:onOrderResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end
    if not data.data then return end
    -- 调用平台跳转
    if data.data.url then
        MultiPlatform:getInstance():openBrowser(data.data.url)
    end
end
return ActivityLayer
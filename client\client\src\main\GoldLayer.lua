-------------------------------------------------------------------------------
--  创世版1.0
--  金币场大厅
--  @date 2017-10-25
--  @auth woodoo
-------------------------------------------------------------------------------
local GoldLayer = class("GoldLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GoldLayer:ctor()
    self:enableNodeEvents()

    -- 登录服务器网络短连接（每日标记用）
    local callback = function(code, msg, result)
        if code < 0 then
            helper.pop.message(ms)
        else
            -- do nothing
        end
    end
    self.m_frame = helper.app.createFrame(self, callback)

    -- 载入主UI
    local main_node = helper.app.loadCSB('GoldLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('item_template'):hide()
    main_node:child('empty_template'):hide()

    -- 暂时屏蔽奖券
    main_node:child('top_bar/bg_num_quan'):hide()
    main_node:child('top_bar/label_quan'):hide()
    main_node:child('top_bar/icon_quan'):hide()

    -- 初始化TopBar
    local top_bar = main_node:child('top_bar')
    top_bar:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    top_bar:child('btn_add_gold'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAddGold) )
    top_bar:child('btn_free_gold'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnFreeGold) )

    -- 红点检查
    if yl.IS_RED_POINT_OPEN and bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_GOLD_SHARE) == 0 then
        local sprite = display.newSprite('common/icon_red_point.png')
        sprite:setName('red_point')
        local btn = top_bar:child('btn_free_gold')
        sprite:pos(btn:size().width - 3, btn:size().height - 3):addTo(btn)
    end

    -- 金币刷新定时器
    self:perform(function()
        top_bar:child('label_gold'):setString( GlobalUserItem.lUserScore )
    end, 1, -1, nil, true)

    self:requestEntries()
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function GoldLayer:onEnterTransitionFinish()
    print('GoldLayer:onEnterTransitionFinish...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GoldLayer:onExit()
    print('GoldLayer:onExit...')
    helper.app.removeFrame(self.m_frame)
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function GoldLayer:onBtnBack(sender)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 加金币按钮点击
-------------------------------------------------------------------------------
function GoldLayer:onBtnAddGold(sender)
    helper.link.toMall( LANG.MALL_TAB_FANGKA )
end


-------------------------------------------------------------------------------
-- 免费金币按钮点击
-------------------------------------------------------------------------------
function GoldLayer:onBtnFreeGold(sender)
    if bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_GOLD_SHARE) == 0 then
        GlobalUserItem.lClientFlag = bit:_or(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_GOLD_SHARE)
        self.m_frame:onDailyFlag()
        sender:removeChildByName('red_point')
    end

    local share = helper.pop.shareImage('common/share_image.jpg', false, LANG.SHARE_GOLD, 'share_gold')
    share:showButtons('pyq')
end


-------------------------------------------------------------------------------
-- 更新金币
-------------------------------------------------------------------------------
function GoldLayer:updateGold(gold, quan)
    if fangka then
        self.main_node:child('top_bar/label_fangka'):setString(fangka)
        -- 更新全局房卡
        PassRoom:getInstance():getPlazaScene():updateFangka( tonumber(fangka) )
    end
    if quan then
        self.main_node:child('top_bar/label_reward'):setString(quan)
    end
end


-------------------------------------------------------------------------------
-- 请求入口列表
-------------------------------------------------------------------------------
function GoldLayer:requestEntries()
    -- 要按当前配置的插件过滤
    local kind_map = {}
    for i, plugin in ipairs(cs.app.plugins) do repeat
        if yl.is_reviewing and plugin.review then break end
        if plugin.kind then
            kind_map[plugin.kind] = plugin.name
        end
    until true end
    self.m_kind_map = kind_map

    local all_list = {}
    local data = GlobalUserItem.roomlist
    --dump(data, 'entries', 6)
    for i = 1, #data do
        local kind = data[i][1]
        local rooms = data[i][2]
        for k, v in ipairs(rooms) do
            if v.wServerType == yl.GAME_GENRE_GOLD and v.cbSubType == 0 and kind_map[v.wKindID] then
                table.insert(all_list, v)
            end
        end
    end
    self:initKinds(all_list)
end


-------------------------------------------------------------------------------
-- 初始化玩法
-------------------------------------------------------------------------------
function GoldLayer:initKinds(kinds)
    local listview = self.main_node:child('listview_items')
    local template = self.main_node:child('item_template')
    local empty_template = self.main_node:child('empty_template')
    local l_size = listview:size()
    local i_size = template:size()
    listview:removeAllItems()
    local col_count = 4
    
    -- 补齐至少两行
    local empties = math.ceil(#kinds / col_count) * col_count - #kinds  -- 先补齐4的倍数
    if #kinds < col_count * 2 then
        empties = col_count * 2 - #kinds
    end
    for i=1, empties do
        table.insert(kinds, {wKindID=0})
    end

    local row
    for i, config in ipairs(kinds) do
        local kind = config.wKindID
        if i % col_count == 1 then
            row = ccui.Layout:create()
            row:setClippingEnabled(true)
            row:size(i_size.width * col_count, i_size.height)
            listview:pushBackCustomItem(row)
        end
        item = (kind == 0 and empty_template or template):clone():show()
        item.server_id = config.wServerID
        if kind > 0 then
            item:setCascadeColorEnabled(true)
            item:addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnKind) )
            item:child('bg'):texture('common/bg_gold_kind_' .. kind .. '.png')
            item:child('label_name'):setString( self.m_kind_map[kind] or '' )
            item:child('label_condition'):setString( LANG{'GOLD_ENTER_LIMIT', num=config.lEnterScore} )
            local num = (config.dwOnLineCount + 3) * 7 + math.random(1, 10) -- 在线人数=（真实在牌桌内人数+3）*7+10以内随机数
            item:child('label_players'):setString(num)
            
            item:child('icon_players'):hide()   -- 暂时隐藏人数显示
            item:child('label_players'):hide()
        end
        local col = (i - 1) % col_count + 1
        item:pos((col - 1) * i_size.width, -i_size.height)
        item:addTo(row)

        item:runAction( cc.Sequence:create(
            cc.DelayTime:create(0.06*(i-1)),
            cc.EaseExponentialOut:create( cc.MoveBy:create(0.6, cc.p(0, i_size.height)) )
        ) )
    end

    empty_template:removeFromParent()
    template:removeFromParent()
    listview:jumpToTop()
end


-------------------------------------------------------------------------------
-- 玩法点击
-------------------------------------------------------------------------------
function GoldLayer:onBtnKind(sender)
    PassRoom:getInstance():onLoginServer(sender.server_id)
end


return GoldLayer
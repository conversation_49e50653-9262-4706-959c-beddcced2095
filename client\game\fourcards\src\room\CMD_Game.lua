local cmd =  {}

--游戏版本
cmd.VERSION 					= appdf.VersionValue(6,7,0,1)
--游戏标识
cmd.KIND_ID						= 101
	
--游戏人数
cmd.GAME_PLAYER					= 4

--每个人的起始手牌
cmd.GAME_CARD_NUM               = 55

--每个人最大的出牌数目
cmd.GAME_MAX_OUT_CARD			= 16

--视图位置
cmd.MY_VIEWID					= 3

--******************         游戏状态             ************--
--等待开始
cmd.GS_TK_FREE					= 0
--叫庄状态
cmd.GS_TK_CALL					= 100
--下注状态
cmd.GS_TK_SCORE					= 101
--游戏进行
cmd.GS_TK_PLAYING				= 102

--*********************      服务器命令结构       ************--
--游戏开始
cmd.SUB_S_GAME_START			= 100
--加注结果
cmd.SUB_S_ADD_SCORE				= 101
--用户强退
cmd.SUB_S_PLAYER_EXIT			= 102
--发牌消息
cmd.SUB_S_SEND_CARD				= 103
--游戏结束
cmd.SUB_S_GAME_END				= 104
--用户摊牌
cmd.SUB_S_OPERATE				= 105
--用户叫庄
cmd.SUB_S_CALL_BANKER			= 106
cmd.SUB_S_READY_STATE			= 113
cmd.SUB_S_RECORD				= 111								--房卡结算记录

--**********************    客户端命令结构        ************--
--用户叫庄
cmd.SUB_C_CALL_BANKER			= 1
--用户加注
cmd.SUB_C_ADD_SCORE				= 2
--用户摊牌
cmd.SUB_C_OPEN_CARD				= 3
--更新库存
cmd.SUB_C_STORAGE				= 6
--设置上限
cmd.SUB_C_STORAGEMAXMUL			= 7
--请求查询用户
cmd.SUB_C_REQUEST_QUERY_USER	= 8
--用户控制
cmd.SUB_C_USER_CONTROL			= 9

--********************       定时器标识         ***************--
--无效定时器
cmd.IDI_NULLITY					= 200
--开始定时器
cmd.IDI_START_GAME				= 201
--叫庄定时器
cmd.IDI_CALL_BANKER				= 202
--加注定时器
cmd.IDI_TIME_USER_ADD_SCORE		= 1
--摊牌定时器
cmd.IDI_TIME_OUT_CARD			= 2

--*******************        时间标识         *****************--
--叫庄定时器
cmd.TIME_USER_CALL_BANKER		= 10
--开始定时器
cmd.TIME_USER_START_GAME		= 10
--加注定时器
cmd.TIME_USER_ADD_SCORE			= 10
--摊牌定时器
cmd.TIME_USER_OPEN_CARD			= 10

--******************         游戏状态             ************--
--空闲开始
cmd.GAME_SCENE_FREE				= 0
--叫庄状态
cmd.GAME_SCENE_PLAY				= 100
--等待状态
cmd.GAME_SCENE_WAITING			= 200

--空闲状态
cmd.CMD_S_StatusFree = 
{
	--基础积分
	{k = "lCellScore", t = "int"},								--基础积分
	--时间信息
 	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
 	{k = "cbTimeOperateCard", t = "byte"},						--操作时间
 	{k = "cbTimeStartGame", t = "byte"},						--开始时间
	--历史积分
	{k = "lTurnScore", t = "score", l = {cmd.GAME_PLAYER}},		--积分信息
	{k = "lCollectScore", t = "score", l = {cmd.GAME_PLAYER}},	--积分信息
	{k = "cbPlayerCount", t = "byte"},
	{k = "cbMaCount", t = "byte"},
}

--游戏状态
cmd.CMD_S_StatusPlay = 
{
	--时间信息
	{k = "cbPlayStatus", t = "byte", l = {cmd.GAME_PLAYER}},							--出牌时间
	{k = "wBankerUser", t = "word"},						--叫分时间
	{k = "lTurnScore", t = "score", l = {cmd.GAME_PLAYER}},						--开始时间
	{k = "lCollectScore", t = "score", l = {cmd.GAME_PLAYER}},
	--游戏变量
	{k = "cbHandCardData", t = "byte", l = {cmd.GAME_CARD_NUM}},							--单元积分
	{k = "wCurrentUser", t = "word"},							--庄家用户
	{k = "cbOutCard", t = "byte", l = {cmd.GAME_MAX_OUT_CARD, cmd.GAME_MAX_OUT_CARD, cmd.GAME_MAX_OUT_CARD, cmd.GAME_MAX_OUT_CARD}},							--当前用户
	{k = "cbCardXian", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "wPreUser", t = "byte"},							--财神索引

	{k = "lScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "lCardScoreT", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "lCardScore", t = "score"},
	{k = "cbWinIndex", t = "byte", l = {cmd.GAME_PLAYER}},

	{k = "cbJushu", t = "byte"},							--动作扑克
	{k = 'cbUserCount', t = 'byte'},
}

cmd.CMD_S_GameStart = 
{
	{k = 'wBankerUser', t = 'word'},
	{k = 'cbPlayerStatus', t = 'byte', l = {cmd.GAME_PLAYER}},
	{k = 'cbJushu', t = 'byte'},
	{k = 'cbUserCount', t = 'byte'},
}

cmd.CMD_S_SendCard = 
{
	{k = "cbHandCardData", t = "byte", l = {cmd.GAME_CARD_NUM}},
	{k = "cbCurrentUser", t = "byte"},
	{k = "cbFirstUser", t = "byte"},
}

cmd.CMD_C_OxCard = 
{
	{k = "cbOp", t = "word"},
	{k = "cbCardData", t = "byte", l = {cmd.GAME_MAX_OUT_CARD}},
	{k = "cbIndex", t = "byte"},
	{k = "cbPeiyin", t = "byte"},
}

cmd.CMD_S_Operator = 
{
	{k = "wPlayerID", t = "word"},
	{k = "cbCanOut", t = "byte"},
	{k = "lCardScore", t = "score"},
	{k = "lCardScoreT", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "cbTurnStart", t = "byte"},
	{k = "cbOutCardState", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbIndex", t = "byte"},
	{k = "cbWinIndex", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbIsZha", t = "byte"},
	{k = "cbZhaUser", t = "word"},
	{k = "lScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "cbOutCards", t = "byte", l = {cmd.GAME_MAX_OUT_CARD}},
	{k = "cbCardXian", t = "byte"},
	{k = "cbIsRePush", t = "byte"},
	{k = "wOp", t = "word"},
	{k = "cbPeiyin", t = "byte"},
}

cmd.CMD_S_GameEnd = 
{
	{k = "lGameTax", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "lGameScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "lscore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "lCardScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "cbWinIndex", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbJiesan", t = "byte"},
	{k = "cbDuJiangUser", t = "byte"},
	{k = "lDuJiangScore", t = "score"},
	{k = "lAverageScore", t = "score"},
	{k = "cbLastUser", t = "byte"},
	{k = "cbLeftCard", t = "byte", l = {cmd.GAME_CARD_NUM}},
}

return cmd
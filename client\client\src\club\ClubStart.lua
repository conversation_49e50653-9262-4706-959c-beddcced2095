-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部启动器
--  @date 2018-01-13
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')


local ClubStart = class("ClubStart", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubStart:ctor()
    print('ClubStart:ctor...')
    self:enableNodeEvents()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubStart:onEnter()
    print('ClubStart:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_INFO, self, self.onClubListResp)
    
    ClubUtil.send(cmd.SUB_CLUB_INFO, cmd.CMD_GR_ID, {})
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubStart:onExit()
    print('ClubStart:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 俱乐部列表返回
-------------------------------------------------------------------------------
function ClubStart:onClubListResp(data)
    local clubs = LiveFrame:getInstance():resp(data, cmd.tagClub, true)
    if not clubs then return end

    -- 判断是否有俱乐部
    if #clubs > 0 then
        local path = cs.app.CLIENT_SRC .. 'club.ClubMainLayer'
        helper.pop.popLayer(path, nil, {clubs})
    else
        local path = cs.app.CLIENT_SRC .. 'club.ClubCreateLayer'
        helper.pop.popLayer(path)
    end
    self:removeFromParent()
end


return ClubStart

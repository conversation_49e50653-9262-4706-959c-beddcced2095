-------------------------------------------------------------------------------
--  创世版1.0
--  常用逻辑辅助方法类
--      访问方式：helper.logic.
--  @date 2017-06-05
--  @auth woodoo
-------------------------------------------------------------------------------
local LogicHelper = {}
helper = helper or {}
helper.logic = LogicHelper


-------------------------------------------------------------------------------
-- TopBar初始化
-------------------------------------------------------------------------------
function LogicHelper.initTopBar(parent_node, owner, onback_call, name)
    local top_bar = parent_node:child(name or 'top_bar')
    cs.app.client('system.TopBar').init(top_bar, owner, onback_call)
end


-------------------------------------------------------------------------------
-- 批量给按钮增加点击事件，名称为btn_ok，则认为点击事件时onBtnOk
-------------------------------------------------------------------------------
function LogicHelper.addListenerByName(event_owner, buttons, effects)
    effects = effects or {}
    for i, button in ipairs(buttons) do
	    local t = button:getName():split('_')
        table.map(t, function(v, k) return v:ucfirst() end)
        local event_name = 'on' .. table.concat(t, '')
        button:addTouchEventListener( helper.app[(effects[i] or 'comm') .. 'ClickHandler'](event_owner, event_owner[event_name]) )
    end
end


-------------------------------------------------------------------------------
-- 初始化图片切换形式的Checkbox
-------------------------------------------------------------------------------
function LogicHelper.initImageCheck(image, checked, on_image, off_image, click_call, effect)
    effect = effect or 'tint'
    image.checked = checked
    image:texture( checked and on_image or off_image )

    local uncheck_color = image._uncheck_color or cc.c3b(211, 211, 211)
    local text = image:child('text')
    if text then
        text._light_color = text:getTextColor()
    end
    if not checked then
        text:setTextColor(uncheck_color)
    end

    image.toggle = function(img)
        if img.group and img.checked then return end    -- 分组模式下，不允许取消选择自己
        img.checked = not img.checked
        img:texture( img.checked and on_image or off_image )
        if img.group and img.checked then
            if img:child('text') then
                img:child('text'):setTextColor( img:child('text')._light_color )
            end
            for i, node in ipairs( img:getParent():getChildren() ) do
                if node ~= img and node.group == img.group then
                    node.checked = false
                    node:texture( off_image )
                    if node:child('text') then
                        node:child('text'):setTextColor(uncheck_color)
                    end
                end
            end
        end
    end

    image:addTouchEventListener( helper.app[(effect or 'comm') .. 'ClickHandler'](image, function(sender)
        sender:toggle()
        click_call(sender)
    end) )
end


-------------------------------------------------------------------------------
-- 初始化通用数字键盘
-------------------------------------------------------------------------------
function LogicHelper.initKeyboard(keyboard, callback, max_num)
    max_num = max_num or 6
    local onBtnKey = function(keyboard, sender)
        local opts = {}
        local nums = keyboard.m_numbers or ''
        local len = #nums
        local code = sender.code
        if type(code) == 'number' then
            if len == max_num then return end
            nums = nums .. code
            opts = {'+', len + 1}

        elseif code == 'delete' then
            if len == 0 then return end
            nums = nums:sub(1, len - 1)
            opts = {'-', len}

        elseif code == 'reset' then
            nums = ''
            opts = {'-', 1}
        end
        keyboard.m_numbers = nums
        callback(nums, opts, sender)
    end

    local names = {'reset', 'delete'}
    for i = 0, 9 do
        names[#names+1] = i
    end
    for _, name in ipairs(names) do
        keyboard:child('btn_' .. name).code = name
        keyboard:child('btn_' .. name):addTouchEventListener( helper.app.tintClickHandler(keyboard, onBtnKey, false) ) -- false是不要重复点击延时保护
    end
end


-------------------------------------------------------------------------------
-- 通用房间描述生成
-------------------------------------------------------------------------------
function LogicHelper.makeRoomDesc(kind_cfg, params)
    local config = kind_cfg[params.kind]
    local ret = {'[' .. config.NAME .. ']'}

    -- 玩法字符串
    local t = {}
    local rule = params.rule
    local is_num = type(rule) == 'number'
    local len = is_num and 64 or #rule   -- 如果是数值，认为是个64位整数
    for i = 1, len do
        if i > 2 then   -- 1是标志位，2是码位，跳过
            local v = is_num and ((bit:_and(rule, bit:_lshift(1, i-1)) > 0 and 1 or 0)) or rule[i]
            local rule_key = 'RULE' .. (i - 3) .. (v == 1 and '' or '_NONE')
            local str = config[rule_key]
            if str then 
                t[#t + 1] = str
            end
        end
    end
    local wanfa = table.concat(t, '/')
    
    -- 局数
    local jushu = params.jushu
    if jushu and type(jushu) == 'number' then   -- 可能是“4局”之类的
        if jushu < 0 then
            if jushu > -10000 then
                if config.DAKUN_DESC then
                   local s, flag = string.gsub( config.DAKUN_DESC, "$kun", -jushu )
                   jushu = s
                else
                   jushu = LANG{'ROOM_JUSHU_KUN', kun = -jushu}
                end
            else
                jushu = LANG{'ROOM_JUSHU_QUAN', kun=-(jushu + 10000)}
            end
        end
    end

    -- 支付方式
    local pay_type = params.pay_type
    local fangka = params.fangka
    local zhifu = nil
    if fangka and pay_type then
        zhifu = LANG{'CREATE_ZHIFU_' .. pay_type, fangka = fangka}
    end

    -- 人数
    local renshu = params.renshu

    -- 底分
    local difen = params.difen and params.difen > 0 and params.difen or false

    local push = function(v, key)
        if v then
            table.insert(ret, LANG{'ROOM_DESC_' .. key, value=v})
        end
    end

    push(jushu, 'JUSHU')
    push(renshu, 'RENSHU')
    push(difen, 'DIFEN')
    push(zhifu, 'ZHIFU')
    push(wanfa, 'WANFA')

    return table.concat(ret, ', ')
end

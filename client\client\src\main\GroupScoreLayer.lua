-------------------------------------------------------------------------------
--  创世版1.0
--  连场战绩
--  @date 2019-06-12
--  @auth woodoo
-------------------------------------------------------------------------------
local GroupScoreLayer = class('GroupScoreLayer', cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GroupScoreLayer:ctor()
    self:enableNodeEvents()
    self:setName("subGroupScoreLayer")
    self:zorder(1)
    
    -- 载入主UI
    local main_node = helper.app.loadCSB('GroupScoreLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    
    main_node:child('group_template'):hide()
    main_node:child('item_template'):hide()

    for j = 1, cs.app.MAX_PLAYER do
		main_node:child('label_name' .. j):setString( '' )
        main_node:child('label_id' .. j):setString( '' )
        main_node:child('item_total/label_score' .. j):setString( '' )
	end

end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function GroupScoreLayer:onEnter()
    print('GroupScoreLayer:onExit...')
    PassRoom:getInstance():setScoreView(self)
    local room_data = PassRoom:getInstance().m_tabPriData
    if not room_data.dwRecordID then return end

    self.m_dwRecordID = room_data.dwRecordID
    if not self.m_dwRecordID or self.m_dwRecordID == 0 then return end

    helper.pop.waiting()
    PassRoom:getInstance():getNetFrame():onQueryScoreList(self.m_dwRecordID)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GroupScoreLayer:onExit()
    print('GroupScoreLayer:onExit...')
    PassRoom:getInstance():setScoreView(nil)
end


-------------------------------------------------------------------------------
-- 列表数据回调(PassRoom中调用，scores:tagRecordInfo)
-------------------------------------------------------------------------------
function GroupScoreLayer:onRecordList(scores, cmd)
    if cmd == PassRoom.cmd_pri_login.SUB_GR_QUERY_RECORD_INFO_RESULT then        -- 主列表
        if scores and #scores == 1 then
            self.m_record = scores[1]
            self:perform( function() 
                PassRoom:getInstance():getNetFrame():onQueryScoreDetail(self.m_dwRecordID)
            end, 0.01 )
        end
    elseif cmd == PassRoom.cmd_pri_login.SUB_GR_QUERY_RECORD_SCOR_RESULT then    -- 详情
        self.m_details = scores
    end

    if self.m_record and self.m_details then
        self:createList(self.m_record, self.m_details)
    end
end


-------------------------------------------------------------------------------
-- 设置分数
-------------------------------------------------------------------------------
function GroupScoreLayer:setScoreLabel(label, has_score, score)
    if has_score then score = tonumber(score) end
    label:setString( has_score and ((score > 0 and '+' or '') .. score) or '' )
    if has_score then
        label:setTextColor(score > 0 and cc.c4b(0, 180, 8, 255) or (score < 0 and cc.c4b(255, 0, 0, 255) or cc.c4b(108, 72, 50, 255)))
    end
end


-------------------------------------------------------------------------------
-- 创建列表
-------------------------------------------------------------------------------
function GroupScoreLayer:createList(record, details)
    self.group_items = {}
    self.groups = {}
    local main_node = self.main_node
    local item_template = self.main_node:child('item_template')
    local group_template = self.main_node:child('group_template')
    local per = math.max(1, record.nPlayCount)
    local group_num = math.ceil(#details / per)

    local player_num = 0
    for _, nick in ipairs(record.szNickName) do
        if nick and nick ~= '' then
            player_num = player_num + 1
        end
    end
	player_num = math.min(player_num, cs.app.MAX_PLAYER)
    record.nPlayerCount = player_num

	for j = 1, cs.app.MAX_PLAYER do
		main_node:child('label_name' .. j):setString( j <= player_num and record.szNickName[j] or '' )
        main_node:child('label_id' .. j):setString( j <= player_num and LANG{'SCORE_USER_ID', id=helper.str.formatUserID(record.dwUserID[j])} or '' )
        self:setScoreLabel(main_node:child('item_total/label_score' .. j), j <= player_num, record.nScore[j])
	end

    for g = 1, group_num do
        local detail_nodes = {}
        local user_scores = {}
        local first, last = (g - 1) * per + 1, math.min(g * per, #details)
        for i = first, last do
            local item = item_template:clone():show()
            item.origin_size = item:size()
            item:child('label_ju'):setString( LANG{'GAME_NO', index=(i - 1) % per + 1} )

            local ju = details[i]
            for j = 1, cs.app.MAX_PLAYER do
                self:setScoreLabel(item:child('label_score' .. j), j <= player_num, ju.nScore[j])
                user_scores[j] = (user_scores[j] or 0) + (j <= player_num and ju.nScore[j] or 0)
            end
            detail_nodes[#detail_nodes + 1] = item
            item:child('bg1'):setVisible(#detail_nodes % 2 == 1)
            item:child('bg2'):setVisible(#detail_nodes % 2 == 0)
            item:hide():addTo(main_node)
        end

        if #detail_nodes > 0 then
            local first_ju = details[first]
            local group = self:createGroup(g, record, user_scores, detail_nodes, first_ju)
            self.group_items[g] = detail_nodes
            self.groups[g] = group
        end
    end

    item_template:removeFromParent()
    group_template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 创建分组
-------------------------------------------------------------------------------
function GroupScoreLayer:createGroup(group_index, record, scores, detail_nodes, first_ju)
    local main_node = self.main_node
    local listview = main_node:child('listview')
    local template = main_node:child('group_template')
    
    local item = template:clone():show()
    item:child('label_room'):setString( group_index )
    item:child('label_title'):setString( LANG{'SCORE_TITLE_GROUP', room=record.szRoomID, base_score=record.wCellScore} )
    

    local t = first_ju.sysInsertTime
    local time = string.format('%02d-%02d-%02d %02d:%02d', t.wYear, t.wMonth, t.wDay, t.wHour, t.wMinute)
    item:child('label_time'):setString( time )

	for j = 1, cs.app.MAX_PLAYER do
        self:setScoreLabel(item:child('label_score' .. j), j <= record.nPlayerCount, scores[j])
	end

    item:child('btn_spread').other = item:child('btn_collapse')
    item:child('btn_collapse').other = item:child('btn_spread')
    item:child('btn_spread').group_index = group_index
    item:child('btn_collapse').group_index = group_index
    item:child('btn_spread'):show():addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnSpread) )
    item:child('btn_collapse'):hide():addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnCollapse) )
    listview:pushBackCustomItem(item)

    --[[
    for i, detail in ipairs(detail_nodes) do
        detail:hide()
        listview:pushBackCustomItem(detail)
    end
    --]]

    return item
end


-------------------------------------------------------------------------------
-- 展开、收起详情
-------------------------------------------------------------------------------
function GroupScoreLayer:showHideDetail(sender, visible)
    local listview = self.main_node:child('listview')
    local nodes = self.group_items[sender.group_index]
    if not nodes or #nodes == 0 then return end
    local group = self.groups[sender.group_index]
    local group_index = listview:getIndex(group)
    for i = #nodes, 1, -1 do
        local node = nodes[i]
        node:retain()
        if visible then
            node:show():removeFromParent(false)
            listview:insertCustomItem(node, group_index + 1)
        else
            local index = listview:getIndex(node)
            listview:removeItem(index)
            node:hide():addTo(self.main_node)
        end
        node:release()
    end
    listview:doLayout()
    sender.other:show()
    sender:hide()
end


-------------------------------------------------------------------------------
-- 显示详情列表
-------------------------------------------------------------------------------
function GroupScoreLayer:onBtnSpread(sender)
    self:showHideDetail(sender, true)
end


-------------------------------------------------------------------------------
-- 收起详情
-------------------------------------------------------------------------------
function GroupScoreLayer:onBtnCollapse(sender)
    self:showHideDetail(sender, false)
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GroupScoreLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


return GroupScoreLayer
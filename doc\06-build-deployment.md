# LHMJ313 构建部署文档

## 构建系统概述

LHMJ313 项目支持多平台构建，包括 Android、iOS、Windows、Linux 等平台。项目使用不同的构建工具和配置文件来适应各个平台的特性。

## 构建环境要求

### 通用要求
- **Cocos2d-x**: 3.13 版本
- **Python**: 2.7+ (用于构建脚本)
- **CMake**: 3.1+ (Linux/Windows 构建)

### Android 构建环境
- **Android Studio**: 2.0+
- **Android SDK**: API Level 22+
- **Android NDK**: r10e+
- **Gradle**: 1.3.0+
- **Java**: JDK 1.8+

### iOS 构建环境
- **Xcode**: 7.0+
- **iOS SDK**: 8.0+
- **macOS**: 10.10+

### Windows 构建环境
- **Visual Studio**: 2015+
- **Windows SDK**: 8.1+

## 项目配置文件

### 1. 引擎配置

#### .cocos-project.json
```json
{
    "engine_version": "cocos2d-x-3.13",
    "has_native": true,
    "project_type": "lua"
}
```

#### config.json (主配置)
```json
{
    "init_cfg": {
        "isLandscape": true,
        "isWindowTop": false,
        "name": "lhmj313",
        "width": 960,
        "height": 640,
        "entry": "src/main.lua",
        "consolePort": 6050,
        "uploadPort": 6060
    },
    "simulator_screen_size": [
        {
            "title": "iPhone 4 (960x640)",
            "width": 960,
            "height": 640
        }
    ]
}
```

### 2. 包配置系统

#### 包配置文件结构
```
client/package/{package_name}/client/src/pack.lua
```

#### 包配置示例
```lua
local pack = {
    -- 服务器配置
    LOGONSERVER = 'lhmj.tuo3.com.cn',
    LOGONPORT = 8607,
    
    -- 版本信息
    BASE_C_VERSION = 10,
    BASE_C_RESVERSION = 1,
    PACK_GAME_VERSIONS = '1|1',
    
    -- 微信配置
    WX_APPID = 'wx561cc267a5729725',
    WX_SECRET = '9af573ee5f0147955d1a4040f38d10be',
    
    -- 游戏配置
    PACK_GAMES = 'mahjong|fourcards',
    IS_APPSTORE = false,
    
    -- 推广链接
    MW_URL = 'https://a.mlinks.cc/AaHx',
    MW_URL_APPSTORE = 'https://a.mlinks.cc/Aavt',
}
```

## Android 构建

### 1. Gradle 构建配置

#### app/build.gradle
```gradle
android {
    compileSdkVersion 22
    buildToolsVersion "22.0.1"

    defaultConfig {
        applicationId "cn.duoduo100.lhmj"
        minSdkVersion 10
        targetSdkVersion 22
        versionCode 1
        versionName "1.0"
    }

    sourceSets.main {
        java.srcDir "src"
        res.srcDir "res"
        jniLibs.srcDir "libs"
        manifest.srcFile "AndroidManifest.xml"
        assets.srcDir "assets"
    }

    signingConfigs {
        release {
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PASSWORD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                signingConfig signingConfigs.release
            }
        }
    }
}

dependencies {
    compile fileTree(dir: 'libs', include: ['*.jar'])
    compile project(':libcocos2dx')
}
```

#### settings.gradle
```gradle
include ':libcocos2dx'
project(':libcocos2dx').projectDir = new File(settingsDir, '../../cocos2d-x/cocos/platform/android/libcocos2dx')
include ':lhmj313'
project(':lhmj313').projectDir = new File(settingsDir, 'app')
```

### 2. NDK 构建配置

#### Android.mk
```makefile
LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := cocos2dlua_shared
LOCAL_MODULE_FILENAME := libcocos2dlua

LOCAL_SRC_FILES := \
../../Classes/AppDelegate.cpp \
../../Classes/ide-support/SimpleConfigParser.cpp \
../../Classes/ide-support/RuntimeLuaImpl.cpp \
../../Classes/ide-support/lua_debugger.c \
../../Classes/ClientKernel.cpp \
../../Classes/cjson/strbuf.c \
../../Classes/cjson/fpconv.c \
../../Classes/cjson/lua_cjson.c \
../../Classes/cjson/lua_cjson_extensions.c \
../../Classes/LuaAssert/CMD_Data.cpp \
../../Classes/LuaAssert/LuaAssert.cpp \
../../Classes/LuaAssert/ry_MD5.cpp \
../../Classes/LuaAssert/ImageToByte.cpp \
../../Classes/LuaAssert/ClientSocket.cpp \
../../Classes/LuaAssert/DownAsset.cpp \
../../Classes/LuaAssert/UnZipAsset.cpp \
../../Classes/LuaAssert/CurlAsset.cpp \
../../Classes/LuaAssert/LogAsset.cpp \
../../Classes/LuaAssert/CircleBy.cpp \
../../Classes/LuaAssert/QR_Encode.cpp \
../../Classes/LuaAssert/QrNode.cpp \
../../Classes/LuaAssert/AESEncrypt.cpp \
../../Classes/LuaAssert/AudioRecorder/AudioRecorder.cpp \
../../Classes/LuaAssert/SpriteBlur.cpp \
hellolua/main.cpp

LOCAL_C_INCLUDES := $(LOCAL_PATH)/../../prebuilt \
                    $(LOCAL_PATH)/../../prebuilt/android \
                    $(LOCAL_PATH)/../../Classes \
                    $(LOCAL_PATH)/../../Classes/cjson \
                    $(LOCAL_PATH)/../../Classes/GlobalDefine \
                    $(LOCAL_PATH)/../../Classes/ide-support \
                    $(LOCAL_PATH)/../../Classes/LuaAssert

LOCAL_STATIC_LIBRARIES := cocos2d_lua_static

include $(BUILD_SHARED_LIBRARY)

$(call import-module,scripting/lua-bindings/proj.android)
```

### 3. 自动化构建脚本

#### build.rb (Ruby 构建脚本)
```ruby
#!/user/bin/ruby

require 'json'
require 'apktools/apkxml'
require 'pathname'

current_path = Pathname.new(File.dirname(__FILE__)).realpath

project_name = ARGV[0]
export_method = ARGV[1]
version_no = ARGV[2]
upload = ARGV[3]

# 解析JSON配置
json = File.read(File.join(current_path,'config.json'))
obj = JSON.parse(json)
proj = obj[project_name]

if proj.nil?
    puts "\033[31m打包失败：未找到对应项目！\033[0m\n"
    exit
end

info = proj[export_method]
channel_no = info["channel_no"]
package_name = proj["package_name"]
magicwindow_link = info["magicwindow_link"]
magicwindow_key = info["magicwindow_appkey"]

build_path = File.join(current_path,"build")
FileUtils.mkdir_p(build_path) unless File.exists?(build_path)

puts "开始生成资源..."
system "sh -x #{current_path}/make_res.sh #{project_name} #{package_name} #{magicwindow_link} #{magicwindow_key} #{version_no}"

puts "开始导出apk文件..."
puts "输出路径:#{build_path}"

today = Time.new
now = today.strftime("%Y%m%d%H%M%S")
```

### 4. 构建命令

#### 使用 Cocos Console
```bash
# 编译 Android 版本
cocos compile -p android --ap android-29 --proj-dir frameworks/runtime-src/proj.android_mowang -m release --compile-script 0

# 编译指定架构
cocos compile -p android --ap android-29 -m release --compile-script 0 --app-abi arm64-v8a
```

#### 使用 Gradle
```bash
# 进入 Android 项目目录
cd frameworks/runtime-src/proj.android-studio

# 构建 Debug 版本
./gradlew assembleDebug

# 构建 Release 版本
./gradlew assembleRelease
```

## iOS 构建

### 1. Xcode 项目配置

#### 项目设置
- **Bundle Identifier**: cn.duoduo100.lhmj
- **Deployment Target**: iOS 8.0
- **Architectures**: armv7, arm64
- **Valid Architectures**: armv7, armv7s, arm64

#### Info.plist 配置
```xml
<key>CFBundleDisplayName</key>
<string>多多临海麻将</string>
<key>CFBundleIdentifier</key>
<string>cn.duoduo100.lhmj</string>
<key>CFBundleVersion</key>
<string>1.0</string>
<key>LSRequiresIPhoneOS</key>
<true/>
<key>UIRequiredDeviceCapabilities</key>
<array>
    <string>armv7</string>
</array>
```

### 2. 构建脚本

#### package_ios.py
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import shutil
import subprocess

def build_ios_project(project_name, configuration, export_method):
    """构建 iOS 项目"""
    
    # 读取配置文件
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    project_config = config.get(project_name)
    if not project_config:
        print("错误：未找到项目配置")
        return False
    
    # 设置构建参数
    workspace_path = "frameworks/runtime-src/proj.ios_mac/lhmj313.xcworkspace"
    scheme = "lhmj313-mobile"
    
    # 执行构建
    build_cmd = [
        "xcodebuild",
        "-workspace", workspace_path,
        "-scheme", scheme,
        "-configuration", configuration,
        "-archivePath", "build/lhmj313.xcarchive",
        "archive"
    ]
    
    result = subprocess.call(build_cmd)
    return result == 0

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("用法: python package_ios.py <project_name> <configuration> <export_method>")
        sys.exit(1)
    
    project_name = sys.argv[1]
    configuration = sys.argv[2]
    export_method = sys.argv[3]
    
    success = build_ios_project(project_name, configuration, export_method)
    if success:
        print("构建成功")
    else:
        print("构建失败")
        sys.exit(1)
```

## Windows 构建

### 1. CMake 配置

#### CMakeLists.txt
```cmake
cmake_minimum_required(VERSION 2.8)

set(APP_NAME HelloLua)
project (${APP_NAME})

set(COCOS2D_ROOT ${CMAKE_SOURCE_DIR}/cocos2d-x)
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${COCOS2D_ROOT}/cmake/Modules/")

include(CocosBuildHelpers)

option(DEBUG_MODE "Debug or release?" ON)
option(USE_BULLET "Use bullet for physics3d library" ON)

if(DEBUG_MODE)
  set(CMAKE_BUILD_TYPE DEBUG)
else(DEBUG_MODE)
  set(CMAKE_BUILD_TYPE RELEASE)
endif(DEBUG_MODE)

# 编译器选项
if(MSVC)
  ADD_DEFINITIONS(-D_CRT_SECURE_NO_WARNINGS -D_SCL_SECURE_NO_WARNINGS
                  -wd4251 -wd4244 -wd4334 -wd4005 -wd4820 -wd4710
                  -wd4514 -wd4056 -wd4996 -wd4099)
else()
  set(CMAKE_C_FLAGS_DEBUG "-g -Wall -DCOCOS2D_DEBUG=1")
  set(CMAKE_CXX_FLAGS_DEBUG ${CMAKE_C_FLAGS_DEBUG})
  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c99")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -Wno-deprecated-declarations -Wno-reorder")
endif(MSVC)

# 源文件
set(GAME_SRC
  runtime-src/proj.linux/main.cpp
  runtime-src/Classes/AppDelegate.cpp
)

# 包含目录
include_directories(
  runtime-src/Classes
  ${COCOS2D_ROOT}/cocos
  ${COCOS2D_ROOT}/cocos/scripting/lua-bindings/manual
  ${COCOS2D_ROOT}/external/lua/lua
  ${COCOS2D_ROOT}/external/lua/tolua
  ${COCOS2D_ROOT}/cocos/audio/include
)

# 创建可执行文件
add_executable(${APP_NAME} ${GAME_SRC})

# 链接库
target_link_libraries(${APP_NAME} cocos2d luacocos2d)

# 设置输出目录
set(APP_BIN_DIR "${CMAKE_BINARY_DIR}/bin")
set_target_properties(${APP_NAME} PROPERTIES
     RUNTIME_OUTPUT_DIRECTORY  "${APP_BIN_DIR}")
```

### 2. 构建命令

```bash
# 创建构建目录
mkdir build
cd build

# 生成项目文件
cmake ..

# 构建项目
cmake --build . --config Release
```

## 自动化打包系统

### 1. Python 打包脚本

#### package.py
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import shutil
import subprocess

def package_android(project_name, only_res=False):
    """打包 Android 版本"""
    
    # 生成资源
    if not generate_resources(project_name):
        return False
    
    # 打包
    if not only_res:
        run_cmd = 'cocos'
        if sys.platform != 'win32':
            cmd_path = os.environ.get('COCOS_CONSOLE_ROOT')
            if cmd_path:
                run_cmd = 'python ' + os.path.join(cmd_path, 'cocos.py')
            else:
                raise Exception("Cannot find environment COCOS_CONSOLE_ROOT!")
        
        compile_proj_dir = os.path.join('frameworks', 'runtime-src', 'proj.android_mowang')
        command = run_cmd + ' compile -p android --ap android-29 --proj-dir ' + compile_proj_dir + ' -m release --compile-script 0'
        
        result = os.system(command)
        return result == 0
    
    return True

def generate_resources(project_name):
    """生成游戏资源"""
    
    # 读取项目配置
    config_path = os.path.join('frameworks', 'runtime-src', 'proj.android_mowang', 'config.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    project_config = config.get(project_name)
    if not project_config:
        print("错误：未找到项目配置")
        return False
    
    # 复制资源文件
    src_path = os.path.join('client', 'package', project_name)
    dst_path = os.path.join('frameworks', 'runtime-src', 'proj.android_mowang', 'assets')
    
    if os.path.exists(src_path):
        if os.path.exists(dst_path):
            shutil.rmtree(dst_path)
        shutil.copytree(src_path, dst_path)
    
    return True

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python package.py <project_name> [only_res]")
        sys.exit(1)
    
    project_name = sys.argv[1]
    only_res = len(sys.argv) > 2 and sys.argv[2] == 'only_res'
    
    success = package_android(project_name, only_res)
    if success:
        print("打包成功")
    else:
        print("打包失败")
        sys.exit(1)
```

### 2. 版本管理脚本

#### modify_params.sh
```bash
#!/bin/sh

project_path="$(cd `dirname $0`;pwd)"

project_name=$1
app_version=$2
hall_version=$3
game_version=$4
is_appstore=$5

lua_file=${project_path}/../../../client/package/${project_name}/client/src/pack.lua

# 修改版本信息
sed -i "s/BASE_C_VERSION      = .*/BASE_C_VERSION      = ${app_version},/" ${lua_file}
sed -i "s/BASE_C_RESVERSION   = .*,/BASE_C_RESVERSION   = ${hall_version},/" ${lua_file}
sed -i "s/PACK_GAME_VERSIONS  = .*,/PACK_GAME_VERSIONS  = '${game_version}',/" ${lua_file}
sed -i "s/IS_APPSTORE         = .*,/IS_APPSTORE         = '${is_appstore}',/" ${lua_file}

current_version_code=$(grep ANDROID_VER_CODE ${lua_file} | awk '{print $3}' | tr -d ',\r')
new_version_code=$((current_version_code + 1))
sed -i "s/ANDROID_VER_CODE    = .*/ANDROID_VER_CODE    = ${new_version_code},/" ${lua_file}

echo "版本更新完成: ${new_version_code}"
```

## 部署流程

### 1. 开发环境部署

1. **环境准备**
   - 安装 Cocos2d-x 3.13
   - 配置开发工具
   - 设置环境变量

2. **项目配置**
   - 克隆项目代码
   - 配置包参数
   - 设置签名证书

3. **依赖安装**
   - 安装第三方库
   - 配置 SDK 路径
   - 更新子模块

### 2. 生产环境部署

1. **资源准备**
   - 生成游戏资源
   - 压缩优化资源
   - 上传到 CDN

2. **版本构建**
   - 执行自动化构建
   - 生成安装包
   - 签名和验证

3. **发布部署**
   - 上传到应用商店
   - 更新服务器配置
   - 监控部署状态

### 3. 持续集成

#### CI/CD 流程
1. 代码提交触发构建
2. 自动执行测试
3. 构建多平台版本
4. 自动部署到测试环境
5. 人工验证后发布

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

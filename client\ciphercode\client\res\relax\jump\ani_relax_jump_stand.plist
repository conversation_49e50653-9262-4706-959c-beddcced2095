<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_relax_jump_stand_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,248},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,752},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,750},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,626},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,878},{218,126}}</string>
                <key>offset</key>
                <string>{9,5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,4},{218,126}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,878},{218,126}}</string>
                <key>offset</key>
                <string>{9,5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,4},{218,126}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_15.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,876},{218,126}}</string>
                <key>offset</key>
                <string>{9,5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,4},{218,126}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_16.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,752},{220,124}}</string>
                <key>offset</key>
                <string>{8,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{21,6},{220,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_17.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,626},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_18.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,624},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_19.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,500},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,124},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_20.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,500},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_21.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,498},{218,124}}</string>
                <key>offset</key>
                <string>{8,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_22.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,124},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_23.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,124},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_24.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,0},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,0},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{218,122}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,8},{218,122}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{442,374},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,372},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,248},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,248},{218,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{23,6},{218,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_stand_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,374},{220,124}}</string>
                <key>offset</key>
                <string>{8,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{21,6},{220,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_relax_jump_stand.png</string>
            <key>size</key>
            <string>{660,1004}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:c3dfc72dd445cdd01cd52d251880448b:40d44ea923220e0697af7aae8594b890:4d0f2c00439f00f064adf5c04b460b40$</string>
            <key>textureFileName</key>
            <string>ani_relax_jump_stand.png</string>
        </dict>
    </dict>
</plist>

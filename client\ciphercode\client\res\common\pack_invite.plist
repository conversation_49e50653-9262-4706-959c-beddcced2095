<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>icon_invite.png</key>
            <dict>
                <key>frame</key>
                <string>{{771,116},{93,81}}</string>
                <key>offset</key>
                <string>{-2,-6}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,17},{93,81}}</string>
                <key>sourceSize</key>
                <string>{97,103}</string>
            </dict>
            <key>invite_bg_content.png</key>
            <dict>
                <key>frame</key>
                <string>{{532,42},{129,129}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{129,129}}</string>
                <key>sourceSize</key>
                <string>{129,129}</string>
            </dict>
            <key>invite_bg_summary.png</key>
            <dict>
                <key>frame</key>
                <string>{{381,42},{253,100}}</string>
                <key>offset</key>
                <string>{0,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,2},{253,100}}</string>
                <key>sourceSize</key>
                <string>{255,102}</string>
            </dict>
            <key>invite_box.png</key>
            <dict>
                <key>frame</key>
                <string>{{663,139},{106,76}}</string>
                <key>offset</key>
                <string>{-4,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,21},{106,76}}</string>
                <key>sourceSize</key>
                <string>{114,114}</string>
            </dict>
            <key>invite_box_gray.png</key>
            <dict>
                <key>frame</key>
                <string>{{663,61},{106,76}}</string>
                <key>offset</key>
                <string>{-4,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,21},{106,76}}</string>
                <key>sourceSize</key>
                <string>{114,114}</string>
            </dict>
            <key>invite_btn_draw.png</key>
            <dict>
                <key>frame</key>
                <string>{{771,61},{96,53}}</string>
                <key>offset</key>
                <string>{0,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,2},{96,53}}</string>
                <key>sourceSize</key>
                <string>{96,55}</string>
            </dict>
            <key>invite_btn_green.png</key>
            <dict>
                <key>frame</key>
                <string>{{663,2},{185,57}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{185,57}}</string>
                <key>sourceSize</key>
                <string>{185,57}</string>
            </dict>
            <key>invite_close.png</key>
            <dict>
                <key>frame</key>
                <string>{{532,173},{73,120}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,0},{73,120}}</string>
                <key>sourceSize</key>
                <string>{105,122}</string>
            </dict>
            <key>invite_has_draw.png</key>
            <dict>
                <key>frame</key>
                <string>{{771,199},{79,48}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,0},{79,48}}</string>
                <key>sourceSize</key>
                <string>{87,48}</string>
            </dict>
            <key>invite_panel_left.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,42},{253,377}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,1},{253,377}}</string>
                <key>sourceSize</key>
                <string>{255,379}</string>
            </dict>
            <key>invite_progress.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,22},{653,18}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{653,18}}</string>
                <key>sourceSize</key>
                <string>{653,18}</string>
            </dict>
            <key>invite_progress_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{653,18}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{653,18}}</string>
                <key>sourceSize</key>
                <string>{653,18}</string>
            </dict>
            <key>invite_record_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{654,217},{107,73}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{107,73}}</string>
                <key>sourceSize</key>
                <string>{107,73}</string>
            </dict>
            <key>invite_title.png</key>
            <dict>
                <key>frame</key>
                <string>{{483,42},{220,47}}</string>
                <key>offset</key>
                <string>{1,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{2,0},{220,47}}</string>
                <key>sourceSize</key>
                <string>{222,49}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>pack_invite.png</string>
            <key>size</key>
            <string>{869,297}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:390d90e41e9312826eb9f064ae23c11b:960f545702f1cddd0db247936aaedd5a:86c07c988477a1449a5a48bb02879f71$</string>
            <key>textureFileName</key>
            <string>pack_invite.png</string>
        </dict>
    </dict>
</plist>

-------------------------------------------------------------------------------
--  创世版1.0
--  控件辅助方法类
--      访问方式：helper.comp.
--  @date 2017-06-12
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")

local CompHelper = {}
helper = helper or {}
helper.comp = CompHelper


-------------------------------------------------------------------------------
-- 创建二维码
-------------------------------------------------------------------------------
function CompHelper.createQr(content, size)
    local node = QrNode:createQrNode(content, size)
    -- c++中实际二维码的位置有问题，修正
    -- 二维码是一个尺寸为0的node，锚点在内容的左上角
    local qr = node:child('_@@_qr_node_name_@@_')
    local qr_real_size = size / node:getScale()
    local x = (node:size().width - qr_real_size) / 2
    local y = node:size().height + (qr_real_size - node:size().height) / 2
    qr:pos(x, y)
    return node
end


-------------------------------------------------------------------------------
-- 创建WebView
--  panel_place: 占位用的节点，通常是layout，用于WebView的定位和尺寸，会被该方法删除
--  events: 事件，如:{fail = function() ... end, js = function() ... end, finish = function ... end}
--  返回WebView对象，外部调用loadURL(url)。
--  注意：windows下返回nil
-------------------------------------------------------------------------------
function CompHelper.createWebView(panel_place, events)
    if device.platform == 'windows' then return end
    if not panel_place then return end

    local view = ccexp.WebView:create()
    view:anchor(panel_place:anchor()):pos(panel_place:pos())
    view:size(panel_place:size())
    view:addTo(panel_place:getParent())
    panel_place:removeFromParent()
        
    view:setScalesPageToFit(true)
    view:setJavascriptInterfaceScheme('csweb')
    ExternalFun.visibleWebView(view, false)

    local fail_callback = events and events.fail
    view:setOnDidFailLoading(function ( sender, url )
        print("open " .. url .. " fail")
        helper.pop.waiting({false, 'webview'})
         if fail_callback then
            fail_callback()
        end
   end)

    view:setOnShouldStartLoading(function(sender, url)
        print("onWebViewShouldStartLoading, url is ", url)     
        helper.pop.waiting({true, 'webview'})
        return true
    end)

    local finish_callback = events and events.finish
    view:setOnDidFinishLoading(function(sender, url)
        helper.pop.waiting({false, 'webview'})
        ExternalFun.visibleWebView(view, true)
        if finish_callback then
            finish_callback()
        end
    end)

    local js_callback = events and events.js
    if js_callback then
        view:setOnJSCallback(function ( sender, url )
            js_callback()
        end)
    end

    return view
end

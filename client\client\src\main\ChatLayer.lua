-------------------------------------------------------------------------------
--  创世版1.0
--  聊天
--  @date 2017-06-22
--  @auth woodoo
-------------------------------------------------------------------------------
local RichText = cs.app.client('system.RichText')
local EditBox = cs.app.client('system.EditBox')


local ChatLayer = class("ChatLayer", cc.Layer)


local shortcut_words = {}


-------------------------------------------------------------------------------
-- 载入默认短语
-------------------------------------------------------------------------------
function loadShortcutWords()
    for i = 1, 60 do
        local text = LANG['SHORTCUT_WORDS_' .. i]
        if not text then break end

        table.insert(shortcut_words, text)
    end
end
loadShortcutWords()


-------------------------------------------------------------------------------
-- 播放短语
-------------------------------------------------------------------------------
function ChatLayer.playShortcutWords(str, peiyin)
    for k, v in pairs(shortcut_words) do
        if v == str then
            helper.music.playPeiyinChat(peiyin, 'chat' .. k)
            return
        end
    end
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ChatLayer:ctor(net_frame, max_history)
    print('ChatLayer:ctor...')
    self:enableNodeEvents()
    self.m_net_frame = net_frame
    self.m_max_history = max_history or 30

    -- 载入主UI
    local main_node = helper.app.loadCSB('ChatLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    if yl.is_reviewing then
        main_node:child('bg/check_main'):hide()
    end

    local editor = EditBox.convertTextField( main_node:child('bg/panel_main/input'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    editor:setPlaceholderFontColor(display.COLOR_GRAY_BROWN)

    main_node:child('bg/template_text'):hide()
    main_node:child('bg/template_face'):hide()
    main_node:child('bg/panel_short'):hide()
    main_node:child('bg/panel_face'):hide()
    main_node:child('bg/panel_main'):hide()

    main_node:child('bg/check_text'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnTab) )
    main_node:child('bg/check_face'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnTab) )
    main_node:child('bg/check_main'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnTab) )
    main_node:child('bg/panel_main/btn_send'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnSend) )

    self:onBtnTab(main_node:child('bg/check_text'))
    self:effectShow()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ChatLayer:onExit()
    print('ChatLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 蒙版点击
-------------------------------------------------------------------------------
function ChatLayer:onPopClose()
    local this = self
    self.main_node:child('bg'):stop():runMyAction( cc.Sequence:create(
        cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
        cc.CallFunc:create( function() this:hide() end )
    ) )
end


-------------------------------------------------------------------------------
-- 效果显示
-------------------------------------------------------------------------------
function ChatLayer:effectShow()
    self:show()
    self.main_node:child('bg'):stop():scale(0):runMyAction( cc.Sequence:create(
        cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
    ) )
    if self:child('mask') then
        self:child('mask'):effectShow()
    end
end


-------------------------------------------------------------------------------
-- tab点击
-------------------------------------------------------------------------------
function ChatLayer:onBtnTab(sender)
    local main_node = self.main_node
    local cur_name = sender:getName()
    local t = {check_text='panel_short', check_face='panel_face', check_main='panel_main'}
    for k, v in pairs(t) do
        main_node:child('bg/' .. k):setSelected(k == cur_name)
        main_node:child('bg/' .. v):setVisible(k == cur_name)
    end
    if cur_name == 'check_text' then
        self:showTexts()
    elseif cur_name == 'check_face' then
        self:showFaces()
    else
        self:showMain()
    end
end


-------------------------------------------------------------------------------
-- 增加历史记录
-------------------------------------------------------------------------------
function ChatLayer:addHistory(name, msg)
    local listview = self.main_node:child('bg/panel_main/listview')
    if #listview:getItems() >= self.m_max_history then
        listview:removeItem(0)
    end
    local good_width = listview:size().width - 16
    local text = '<gr>' .. name .. ': <z>' .. (type(msg) == 'string' and msg or '')
    local rich = RichText.new(text, 20, nil, cs.app.FONT_NAME)
    local size = rich:size()
    if size.width > good_width then
        rich = RichText.new(text, 20, cc.size(good_width, 0), cs.app.FONT_NAME)
        size = rich:size()
    end
    if type(msg) ~= 'string' then
        local path = string.format('room/face_%02d.png', msg)
        if not cc.FileUtils:getInstance():isFileExist(path) then return end
        
        local face = ccui.ImageView:create()
        face:texture(path):scale(0.5)
        face:pos(size.width + 20, size.height/2):addTo(rich)
    end
    --[[
    if size.width > listview:size().width - 16 then
        local scale = (listview:size().width - 16) / size.width
        rich:scale(scale)
        rich:ignoreContentAdaptWithSize(false)
        rich:size(size.width*scale, size.height*scale)
    end
    --]]
    listview:pushBackCustomItem(rich)
    listview:jumpToBottom()
end


-------------------------------------------------------------------------------
-- 创建快捷语列表
-------------------------------------------------------------------------------
function ChatLayer:showTexts(sender)
    if self.m_text_created then return end
    self.m_text_created = true

    local listview = self.main_node:child('bg/panel_short/listview')
    local tempate = self.main_node:child('bg/template_text')

    for i, text in ipairs(shortcut_words) do
        local item = tempate:clone():show()
        item.index = i
        item:setCascadeColorEnabled(true)

        local label = item:child('text')
        label:setString(text)
        if label:size().width + 40 > item:size().width then
            label:scale( (item:size().width - 40) / label:size().width )
        end
        --[[缩略显示模式
        local arr = helper.str.splitUTF8(text)
        while label:size().width + 40 > item:size().width do
            local short_text = table.concat(arr, '', 1, #arr - 1) .. '...'
            label:setString(short_text)
            table.remove(arr, #arr)
        end
        --]]

        item:addTouchEventListener( helper.app.tintClickHandler(self, self.onTextClick) )

        listview:pushBackCustomItem(item)
    end

    --listview:forceDoLayout()
    listview:jumpToTop()

    tempate:removeFromParent()
end


-------------------------------------------------------------------------------
-- 快捷语点击
-------------------------------------------------------------------------------
function ChatLayer:onTextClick(sender)
    local msg = LANG['SHORTCUT_WORDS_' .. sender.index]
	if msg and self.m_net_frame and self.m_net_frame.sendTextChat then
        self:onPopClose()
		self.m_net_frame:sendTextChat(msg)
	end
end


-------------------------------------------------------------------------------
-- 创建表情列表
-------------------------------------------------------------------------------
function ChatLayer:showFaces(sender)
    if self.m_face_created then return end
    self.m_face_created = true

    local listview = self.main_node:child('bg/panel_face/listview')
    local tempate = self.main_node:child('bg/template_face')

    local cur_item = nil
    for i = 1, 60 do
        local col = (i - 1) % 5 + 1
        local path = string.format('room/face_%02d.png', i)
        if not cc.FileUtils:getInstance():isFileExist(path) then
            if col ~= 1 then    -- 如果新行已经创建，把未用的占位图标清除
                for j = col, 5 do
                    cur_item:child('face' .. j):removeFromParent()
                end
            end
            break
        end

        if col == 1 then
            cur_item = tempate:clone():show()
            listview:pushBackCustomItem(cur_item)
        end

        local icon = cur_item:child('face' .. col)
        icon:texture(path)
        icon.index = i
        icon:addTouchEventListener( helper.app.tintClickHandler(self, self.onFaceClick) )
    end

    --listview:forceDoLayout()
    listview:jumpToTop()

    tempate:removeFromParent()
end


-------------------------------------------------------------------------------
-- 表情点击
-------------------------------------------------------------------------------
function ChatLayer:onFaceClick(sender)
    local index = sender.index
	if self.m_net_frame and self.m_net_frame.sendBrowChat then
        self:onPopClose()
		self.m_net_frame:sendBrowChat(index)
	end
end


-------------------------------------------------------------------------------
-- 显示主面板
-------------------------------------------------------------------------------
function ChatLayer:showMain(sender)
end


-------------------------------------------------------------------------------
-- 手动输入发送
-------------------------------------------------------------------------------
function ChatLayer:onBtnSend(sender)
    local input = self.main_node:child('bg/panel_main/input')
    local msg = input:getString():trim()
    if msg == '' then return end
    input:setString('')
	if self.m_net_frame and self.m_net_frame.sendTextChat then
		self.m_net_frame:sendTextChat(msg)
	end
end


return ChatLayer
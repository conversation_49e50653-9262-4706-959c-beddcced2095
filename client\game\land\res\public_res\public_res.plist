<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>back_tip.png</key>
      <dict>
        <key>frame</key>
        <string>{{754,154},{1334,117}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <true/>
        <key>sourceColorRect</key>
        <string>{{0,0},{1334,117}}</string>
        <key>sourceSize</key>
        <string>{1334,117}</string>
      </dict>
      <key>bar_empty.png</key>
      <dict>
        <key>frame</key>
        <string>{{983,248},{366,24}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <true/>
        <key>sourceColorRect</key>
        <string>{{0,0},{366,24}}</string>
        <key>sourceSize</key>
        <string>{366,24}</string>
      </dict>
      <key>bar_full.png</key>
      <dict>
        <key>frame</key>
        <string>{{873,198},{366,24}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <true/>
        <key>sourceColorRect</key>
        <string>{{0,0},{366,24}}</string>
        <key>sourceSize</key>
        <string>{366,24}</string>
      </dict>
      <key>bt_cancle_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{754,78},{160,74}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,74}}</string>
        <key>sourceSize</key>
        <string>{160,74}</string>
      </dict>
      <key>bt_cancle_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{754,78},{160,74}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,74}}</string>
        <key>sourceSize</key>
        <string>{160,74}</string>
      </dict>
      <key>bt_close.png</key>
      <dict>
        <key>frame</key>
        <string>{{873,154},{40,42}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{40,42}}</string>
        <key>sourceSize</key>
        <string>{40,42}</string>
      </dict>
      <key>bt_close_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{873,632},{45,46}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <true/>
        <key>sourceColorRect</key>
        <string>{{0,0},{45,46}}</string>
        <key>sourceSize</key>
        <string>{45,46}</string>
      </dict>
      <key>bt_close_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{977,616},{45,46}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{45,46}}</string>
        <key>sourceSize</key>
        <string>{45,46}</string>
      </dict>
      <key>bt_ensure_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{754,2},{160,74}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,74}}</string>
        <key>sourceSize</key>
        <string>{160,74}</string>
      </dict>
      <key>bt_ensure_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{754,2},{160,74}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,74}}</string>
        <key>sourceSize</key>
        <string>{160,74}</string>
      </dict>
      <key>btn_chat_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{899,410},{82,76}}</string>
        <key>offset</key>
        <string>{0,4}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{5,0},{82,76}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>btn_chat_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{899,330},{82,78}}</string>
        <key>offset</key>
        <string>{0,3}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{5,0},{82,78}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>btn_setting_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{916,86},{86,80}}</string>
        <key>offset</key>
        <string>{-1,-2}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{2,4},{86,80}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>btn_setting_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{916,2},{86,82}}</string>
        <key>offset</key>
        <string>{-1,-1}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{2,2},{86,82}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>btn_trust_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{899,560},{70,76}}</string>
        <key>offset</key>
        <string>{0,3}</string>
        <key>rotated</key>
        <true/>
        <key>sourceColorRect</key>
        <string>{{11,1},{70,76}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>btn_trust_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{899,248},{82,80}}</string>
        <key>offset</key>
        <string>{0,2}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{5,0},{82,80}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>game_bg.png</key>
      <dict>
        <key>frame</key>
        <string>{{1004,2},{1,1}}</string>
        <key>offset</key>
        <string>{-29.5,29.5}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{1,1}}</string>
        <key>sourceSize</key>
        <string>{60,60}</string>
      </dict>
      <key>game_bg_0.png</key>
      <dict>
        <key>frame</key>
        <string>{{2,2},{1334,750}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <true/>
        <key>sourceColorRect</key>
        <string>{{0,0},{1334,750}}</string>
        <key>sourceSize</key>
        <string>{1334,750}</string>
      </dict>
      <key>game_leave.png</key>
      <dict>
        <key>frame</key>
        <string>{{899,488},{76,70}}</string>
        <key>offset</key>
        <string>{0,1}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{8,6},{76,70}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>game_leave_1.png</key>
      <dict>
        <key>frame</key>
        <string>{{915,168},{84,78}}</string>
        <key>offset</key>
        <string>{-1,-1}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{3,4},{84,78}}</string>
        <key>sourceSize</key>
        <string>{92,84}</string>
      </dict>
      <key>land_blank.png</key>
      <dict>
        <key>frame</key>
        <string>{{1004,2},{1,1}}</string>
        <key>offset</key>
        <string>{-29.5,29.5}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{1,1}}</string>
        <key>sourceSize</key>
        <string>{60,60}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>realTextureFileName</key>
      <string>public_res.png</string>
      <key>size</key>
      <string>{1024,2048}</string>
      <key>smartupdate</key>
      <string>$TexturePacker:SmartUpdate:8cb84a1a64e7ea8df645ec4ff17f5fab$</string>
      <key>textureFileName</key>
      <string>public_res.png</string>
    </dict>
  </dict>
</plist>


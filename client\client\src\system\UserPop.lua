-------------------------------------------------------------------------------
--  创世版1.0
--  玩家悬浮信息
--  @date 2017-06-23
--  @auth woodoo
-------------------------------------------------------------------------------
local UserPop = class('UserPop', cc.Layer)
local HeadSprite = require(appdf.EXTERNAL_SRC .. "HeadSprite")
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")


-------------------------------------------------------------------------------
-- 静态方法
-------------------------------------------------------------------------------
function UserPop.popForUser(parent, user_item, pos)
    local instance = UserPop:create()
    helper.app.addToScene(instance, parent)

    instance:initInfo(user_item)
    instance:effectShow(pos)
    
    return instance
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function UserPop:ctor()
    print('UserPop:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('UserPop.csb')
    self.main_node = main_node
    self:addChild(main_node)

    helper.pop.addMask(self, 100, true)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function UserPop:onExit()
    print('UserPop:onExit...')
end


-------------------------------------------------------------------------------
-- 效果显示
-------------------------------------------------------------------------------
function UserPop:effectShow(pos)
    local bg = self.main_node:child('bg')
    local ax, ay = 0, 0
    if pos.x > display.cx then ax = 1 end
    if pos.y > display.cy then ay = 1 end
    bg:anchor(ax, ay):pos(pos):stop():scale(0):runMyAction( cc.Sequence:create(
        cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
    ) )
    self:show()
    self:child('mask'):effectShow()
end


-------------------------------------------------------------------------------
-- 显示信息
-------------------------------------------------------------------------------
function UserPop:initInfo(user_item)
    local bg = self.main_node:child('bg')

    -- 头像
    local panel = bg:child('panel_avator')
    panel:removeAllChildren()

    local size = panel:size()
	local head = HeadSprite:createNormal(user_item, size.width)
    head:pos(size.width/2, size.height/2):addTo(panel)

    -- 月卡
    helper.app.addAvatorCard(panel, 0.7, nil, user_item.nMonthTicketType);

    -- 昵称
    bg:child('label_name'):setString( user_item.szNickName )

    -- ID
    bg:child('label_id'):setString( LANG{'ID_STR', id=helper.str.formatUserID(user_item.dwUserID)} )

    -- IP
	local ip = user_item.szIpAddress
    if ip then
        bg:child('label_ip'):setString( LANG{'IP_STR', ip=ip} )
    end

    if user_item.cbMobilePhone ~= 1 then
        bg:child('label_mobile'):setString( LANG.MOBILE_UNBIND )
        bg:child('label_mobile'):setTextColor( cc.c3b(201,75,10) )
    end
end


return UserPop
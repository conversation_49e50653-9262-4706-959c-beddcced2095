<?php
/**
 * @author: <PERSON>.z
 * @email: <EMAIL>
 * @website: http://www.jason-z.com
 * @version: 1.0
 * @date: 2019/3/5
 */

defined('BASEPATH') OR exit('No direct script access allowed');


class Lip extends CI_Controller
{
    private $_role;
    private $_score;

    function __construct()
    {
        parent::__construct();
        $this->load->model('mp_model');
        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('user_model');

        $game_id = $this->input->post('game_id');
        $role_id = $this->input->post('role_id');

        if(!$game_id||!$role_id) {
            echo json_encode(array('code'=>1,'msg'=>'参数不合法'));exit;
        }

        $this->player_model->set_database($game_id);

        $role = $this->player_model->get_role_info($role_id);

        if(!$role) {
            echo json_encode(array('code'=>1,'msg'=>'角色不存在'));exit;
        }

        $this->_role = $role;

        $this->_score = $this->player_model->get_role_score($this->_role['UserID']);


    }


    public function balance()
    {
        echo json_encode(array('code'=>0,'fangka'=> $this->_score['RoomCard']));exit;
    }

    public function pay()
    {
        $num = $this->input->post('num');

        if(!$num||!is_numeric($num)) {
            echo json_encode(array('code'=>1,'msg'=>'参数不合法'));exit;
        }

        if($num>$this->_score['RoomCard']) {
            echo json_encode(array('code'=>1,'msg'=>'余额不足'));exit;
        }

        $this->player_model->update_role_score($this->_role['UserID'],$num*(-1));

        $data = array(
            'SourceUserID'=>$this->_role['UserID'],
            'SBeforeCard'=>$this->_score['RoomCard'],
            'RoomCard'=>$num*(-1),
            'TargetUserID'=>0,
            'SBeforeDiamond'=>0,
            'Diamond'=>0,
            'TypeID'=>0,
            'Currency'=>0.0,
            'Gold'=>0,
            'Remarks'=>'Lip',
            'ClientIP'=>$this->getIp(),
            'CollectDate'=>date('Y-m-d H:i:s'),
            'KindID'=>0,
            'PlayerCount'=>0
        );

        $this->player_model->insert_roomcard_consume($data);

        echo json_encode(array('code'=>0,'msg'=> '购买成功'));exit;
    }

    public function getIp()
    {

        if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $cip = $_SERVER["HTTP_CLIENT_IP"];
        } else if (!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } else if (!empty($_SERVER["REMOTE_ADDR"])) {
            $cip = $_SERVER["REMOTE_ADDR"];
        } else {
            $cip = '';
        }
        preg_match("/[\d\.]{7,15}/", $cip, $cips);
        $cip = isset($cips[0]) ? $cips[0] : 'unknown';
        unset($cips);

        return $cip;
    }

}

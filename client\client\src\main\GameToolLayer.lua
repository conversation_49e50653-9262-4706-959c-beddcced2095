-------------------------------------------------------------------------------
--  工具
--  @date 2017-12-06
--  @auth mike
------------------------------------------------------------------------------
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
local GameToolLayer = class("GameToolLayer", cc.Layer)
-------------------------------------------------------------------------------
-- 创建方法
-------------------------------------------------------------------------------
function GameToolLayer:ctor()
    -- 载入主UI
    self:enableNodeEvents()
    local main_node = cc.CSLoader:createNode('public/GameToolLayer.csb')
    self.main_node = main_node
    self:addChild( main_node )
    self:initUI()
end

function GameToolLayer:initUI()
    local btn_change_server = self.main_node:child('panel_content/panel_server/btn_change_server')
    local btn_clean_server = self.main_node:child('panel_content/panel_server/btn_clean_server')
    local label_server_name = self.main_node:child('panel_content/panel_server/label_value')
    local btn_change_user = self.main_node:child('panel_content/panel_user/btn_change_user')
    local label_test_user = self.main_node:child('panel_content/panel_user/label_value')
    local label_rediction_admin = self.main_node:child('panel_content/panel_rediction_admin/label_value')

    local btn_refresh = self.main_node:child('panel_content/btn_refresh')
    local label_uuid = self.main_node:child('panel_content/panel_uuid/label_value')
    local btnSever = self.main_node:child('panel_content/btnSever')
    local ListServer = self.main_node:child('panel_content/ListServer')
    
    btn_clean_server:addTouchEventListener( helper.app.commClickHandler(self, self.onEventCleanServer))
    btn_change_server:addTouchEventListener( helper.app.commClickHandler(self, self.onEventChangeServer))
    btn_change_user:addTouchEventListener( helper.app.commClickHandler(self, self.onEventChangeUser))
    btn_refresh:addTouchEventListener( helper.app.commClickHandler(self, self.onEventRefresh))

    self.m_server_list_info = {}
    self.m_label_rediction_admin = label_rediction_admin
    self.m_label_test_user = label_test_user
    self.m_label_cur_server_name = label_server_name
    self.m_listServer = ListServer
    self.m_btnSever = btnSever
    self.m_btn_change_server = btn_change_server
    self.m_btn_change_user = btn_change_user
    self.m_btn_refresh = btn_refresh
    self.m_label_uuid = label_uuid
    
    self:getServerList()
    
    label_uuid:setString( MultiPlatform:getInstance():getMachineId())
end

--切换用户
function GameToolLayer:onEventChangeUser()
    local data = {}
    data["uuid"] = MultiPlatform:getInstance():getMachineId()
    data["user_id"] = tonumber( self.m_label_test_user:getStringValue() )
    self:updateUserServerInfo(data, false)  
end

--切换服务器
function GameToolLayer:onEventCleanServer()
    local data = {}
    data["uuid"] = MultiPlatform:getInstance():getMachineId()
    data["list_id"] = 0
    self:updateUserServerInfo(data, true)
end

--切换服务器
function GameToolLayer:onEventChangeServer()
    local data = {}
    data["uuid"] = MultiPlatform:getInstance():getMachineId()
    if self.m_label_cur_server_name.info then
        data["list_id"] = self.m_label_cur_server_name.info.list_id
        self:updateUserServerInfo(data, false)
    else
        helper.pop.message('没选服务器')
    end
end

--刷新
function GameToolLayer:onEventRefresh()
    self:getServerInfo()
end

function GameToolLayer:getServerInfo()
    yl.GetUrl(yl.URL_TOOL_SERVER_INFO, 'post', nil, handler(self, self.onServerInfoResponse))
end

function GameToolLayer:getServerList()
    yl.GetUrl(yl.URL_TOOL_SERVER_LIST, 'get', nil, handler(self, self.onServerListResponse))
    self:getServerInfo()
end

function GameToolLayer:updateUserServerInfo( data, isDelete)
    if isDelete then
        yl.GetUrl(yl.URL_TOOL_DELETE_UUID, 'get', data, handler(self, self.onUpdateServerInfoResponse))
    else
        yl.GetUrl(yl.URL_TOOL_INSERT_UUID, 'get', data, handler(self, self.onUpdateServerInfoResponse))
    end
end

-------------------------------------------------------------------------------
--服务器信息返回
-------------------------------------------------------------------------------
function GameToolLayer:onUpdateServerInfoResponse(data, response, http_status)
    helper.pop.message('操作完成')
    self:getServerInfo()
end

-------------------------------------------------------------------------------
--服务器信息返回
-------------------------------------------------------------------------------
function GameToolLayer:onServerInfoResponse(data, response, http_status)
    dump(data.data, '服务器信息返回1', 9)
    dump(self.m_server_list_info, '服务器列表信息返回1', 9)
    helper.pop.message('数据刷新')
    local isHaveSelectServer = false
    for k, server_info in pairs( self.m_server_list_info ) do
        if server_info.host_address == data.data.login_server and server_info.host_port == tostring(data.data.login_port) then
           self.m_label_cur_server_name:setString( server_info.server_name )
           isHaveSelectServer = true
           break
        end
    end
    if not isHaveSelectServer then
       self.m_label_cur_server_name:setString( 'null' )
    end
    if data.data.user_id then
        self.m_label_test_user:setString( data.data.user_id )
    else
        self.m_label_test_user:setString( '0' )
    end
    if data.data.host_url then
        self.m_label_rediction_admin:setString( data.data.host_url )
    else
        self.m_label_rediction_admin:setString('null')
    end
end


-------------------------------------------------------------------------------
--服务器列表信息返回
-------------------------------------------------------------------------------
function GameToolLayer:onServerListResponse(data, response, http_status)
    if not data then
        return
    end
    self.m_listServer:removeAllItems()
    self.m_server_list_info = data.data
    for k, server_info in pairs( self.m_server_list_info ) do
        local btn = self.m_btnSever:clone():show()
        btn:child('label_server'):setString( server_info.server_name )
        btn.info = server_info
        btn:addTouchEventListener( helper.app.commClickHandler(self, self.onEventClickServer))
        self.m_listServer:pushBackCustomItem( btn )
    end
end

--选择服务器
function GameToolLayer:onEventClickServer( sender )
    self.m_label_cur_server_name:setString( sender.info.server_name )
    self.m_label_cur_server_name.info = sender.info
end

return GameToolLayer
if nil == cc.XMLHttpRequest then
    return
end

cc.WEBSOCKET_OPEN     = 0
cc.WEBSOCKET_MESSAGE  = 1
cc.WEBSOCKET_CLOSE    = 2
cc.WEBSOCKET_ERROR    = 3

cc.WEBSOCKET_STATE_CONNECTING = 0
cc.WEBSOCKET_STATE_OPEN       = 1
cc.WEBSOCKET_STATE_CLOSING    = 2
cc.WEBSOCKET_STATE_CLOSED     = 3

cc.XMLHTTPREQUEST_RESPONSE_STRING       = 0
cc.XMLHTTPREQUEST_RESPONSE_ARRAY_BUFFER = 1
cc.XMLHTTPREQUEST_RESPONSE_BLOB         = 2
cc.XMLHTTPREQUEST_RESPONSE_DOCUMENT     = 3
cc.XMLHTTPREQUEST_RESPONSE_JSON         = 4

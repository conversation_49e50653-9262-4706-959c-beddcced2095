--
-- Author: zhong
-- Date: 2016-12-03 11:36:14
--
-- 私人房 命令
-- 登陆服务器: private_cmd.login
-- 游戏服务器: private_cmd.game

local private_struct = appdf.req(appdf.CLIENT_SRC .. "header.Struct_Private")
local private_define = appdf.req(appdf.CLIENT_SRC .. "header.Define_Private")

local private_cmd = {}
private_cmd.struct = private_struct
private_cmd.define = private_define

----------------------------------------------------------------------------------------------
-- 登陆服务器
local login = {}
------
-- 命令
------
-- 私人房间
login.MDM_MB_PERSONAL_SERVICE = 200

login.SUB_MB_QUERY_GAME_SERVER = 204                                    -- 创建房间
login.SUB_MB_QUERY_GAME_SERVER_RESULT = 205                             -- 创建结果
login.SUB_MB_SEARCH_SERVER_TABLE = 206                                  -- 搜索房间
login.SUB_MB_SEARCH_RESULT = 207                                        -- 搜索结果

login.SUB_MB_GET_PERSONAL_PARAMETER = 208                               -- 私人房间配置
login.SUB_MB_PERSONAL_PARAMETER = 209                                   -- 私人房间属性
login.SUB_MB_PERSONAL_FEE_PARAMETER = 212                               -- 私人房费用配置

login.SUB_MB_QUERY_PERSONAL_ROOM_LIST = 210                             -- 私人房列表
login.SUB_MB_QUERY_PERSONAL_ROOM_LIST_RESULT = 211                      -- 私人房列表

login.SUB_MB_DISSUME_SEARCH_SERVER_TABLE = 213                          -- 为解散桌子搜索ID
login.SUB_MB_DISSUME_SEARCH_RESULT = 214                                -- 解散时搜索私人房间返回结果

login.SUB_GR_USER_QUERY_ROOM_SCORE = 216                                -- 私人房间单个玩家请求房间成绩
login.SUB_GR_USER_QUERY_ROOM_SCORE_RESULT = 217                         -- 私人房间单个玩家请求房间成绩结果
login.SUB_GR_USER_QUERY_ROOM_SCORE_RESULT_FINSIH = 218                  -- 私人房间单个玩家请求房间成绩完成

login.SUB_MB_ROOM_CARD_EXCHANGE_TO_SCORE = 221                          -- 房卡兑换游戏币
login.SUB_GP_EXCHANGE_ROOM_CARD_RESULT = 222                            -- 房卡兑换游戏币结果

login.SUB_GR_QUERY_RECORD_INFO = 223                                    -- 新版战绩列表命令
login.SUB_GR_QUERY_RECORD_INFO_RESULT = 224                             -- 新版战绩列表返回
login.SUB_GR_QUERY_RECORD_SCORE = 225                                   -- 新版战绩详情命令
login.SUB_GR_QUERY_RECORD_SCOR_RESULT = 226                             -- 新版战绩详情返回

--mike 20170719
--回放命令
login.SUB_GR_QUERY_REPLAY			    = 227							    -- 回放查询
login.SUB_GR_QUERY_REPLAY_RESULT	    = 228							    -- 回放返回
login.SUB_GR_QUERY_SERVER_ID            = 229                               -- 查询是受有房间 需要连接

--mike 20180116
--//活动任务
login.SUB_GR_EVENT_LIST			         = 231							    -- 已开放活动列表	CMD_GR_UserIDValue 只填id=userid
login.SUB_GR_EVENT_LIST_RESULT	         = 232							    -- 已开放活动列表	tagEvent数组DataSize判断长度
login.SUB_GR_EVENT_GET_REWARD            = 233                              -- 活动领取奖励     CMD_GR_UserIDValue id=userid,value=pramid
login.SUB_GR_EVENT_GET_REWARD_RESULT     = 234                              -- 活动领取奖励
login.SUB_GR_EVENT_ACCEPT                = 235                              -- 活动接受         CMD_GR_UserIDValue  id=userid,value=eventid
login.SUB_GR_EVENT_ACCEPT_RESULT         = 236                              -- 活动接受结果     IDValueMsg
------
-- 消息结构
------
login.CMD_GR_UserIDValue = 
{
    {t = "dword", k = "dwUserID"},
    {t = "int",   k = "nValue"},
}

login.IDValueMsg = 
{
    {t = "int",   k = "nID"},
    {t = "int",   k = "nValue"},
    {t = "string", k = "szMsg", s = yl.LEN_TASK_NAME},
}

--红包任务
login.tagEventParam = 
{
    {t = "int",   k = "nParamID"},
    {t = "int",   k = "nEventID"},
    {t = "int",   k = "nRewardType"},
    {t = "int",   k = "nRewardNum"},
    {t = "string", k = "szName", s = yl.LEN_NICKNAME},
    {t = "string", k = "szGoal", s = yl.LEN_PHONE_MODE},
    {t = "int",   k = "GoalNum"},
    {t = "int",   k = "nDoneNum"},
    {t = "dword", k = "dwDoneTime"},
    {t = "byte",  k = "cbStatus"},                          --//当前状态0未完成，1可领奖，2已领,255标识没有接
}

--红包任务
login.tagEvent = 
{
    {t = "int",     k = "nEventID"},
    {t = "int",     k = "nLogoID"},
    {t = "string",  k = "szName", s = yl.LEN_NICKNAME},
    {t = "string",  k = "szMsg", s = 300 },
    {t = "int",     k = "nPreEventID"},
    {t = "dword",   k = "dwBeginTime"},
    {t = "dword",   k = "dwEndTime"},
    {t = "int",     k = "nParamCount"},
    {t = "table",   k = "params", d = login.tagEventParam, l = { 6 }},
    {t = "bool",    k = "bIsClose"},
    {t = "int",     k = "nRedPacket"},
    {t = "int",     k = "nWithDraw"},
}

-- mike 20170801
-- 查询房间
login.CMD_GR_QueryServerID =
{
    {k = "dwUserID", t = "dword"},
}

login.CMD_GR_QueryServerIDResult =
{
	{k = "dwServerID", t = "dword"},
}

-- mike 20170719
-- 查询回放数据
login.CMD_GP_QueryReplay = 
{
    --回放记录id
    {k = "dwDrawID", t = "dword"},
}

--数据结构头
login.tagReplayActionHead = 
{
	--主命令号
	{k = "wMainCmd", t = "word"},
    --命令号
	{k = "wCmd", t = "word"},
    --数据长度
	{k = "wDataLen", t = "word"},
}

-- 创建房间
login.CMD_MB_QueryGameServer = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 类型ID
    {t = "dword", k = "dwKindID"},
    -- 是否参与 (0 不参与; 1 参与)
    {t = "byte", k = "cbIsJoinGame"},
}

-- 创建结果
login.CMD_MB_QueryGameServerResult = 
{
    -- 房间ID
    {t = "dword", k = "dwServerID"},
    -- 是否可以创建房间
    {t = "bool", k = "bCanCreateRoom"},
    -- 错误描述
    {t = "string", k = "szErrDescrybe"}
}

-- 强制解散搜索房间
login.CMD_MB_SearchServerTable = 
{
    -- 房间ID
    {t = "tchar", k = "szServerID", s = private_define.ROOM_ID_LEN},
}

-- 进入游戏搜索房间
login.CMD_MB_SerchServerTableEnter = 
{
    -- 房间ID
    {t = "tchar", k = "szServerID", s = private_define.ROOM_ID_LEN},
    -- 类型ID
    {t = "dword", k = "dwKindID"},
    -- 加入类型 (见yl.SEARCH_ROOM_TYPE_ ...)
    {t = "byte", k = "cbEnterType"},
}

-- 搜索结果
login.CMD_MB_SearchResult = 
{
    -- 房间ID
    {t = "dword", k = "dwServerID"},
    -- 桌子ID
    {t = "dword", k = "dwTableID"},
    -- 房间类型
    {t = "word", k = "wKindID"},
}

-- 查询私人房间配置
login.CMD_MB_GetPersonalParameter = 
{
    -- 类型ID
    {t = "dword", k = "dwKindID"},
}

-- 查询私人房列表
login.CMD_MB_QeuryPersonalRoomList = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 类型ID
    {t = "dword", k = "dwKindID"},
}

-- 查询战绩列表
login.CMD_MB_QeuryScoreList = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 战绩ID
    {t = "dword", k = "dwRecordID"},
}

-- 查询战绩详情
login.CMD_MB_QeuryScoreDetail = 
{
    -- 记录ID
    {t = "dword", k = "dwRecordID"},
}

-- 私人房间列表信息
login.CMD_MB_PersonalRoomInfoList = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 配置信息
    {t = "table", k = "PersonalRoomInfo", d = private_struct.tagPersonalRoomInfo, l = {private_define.MAX_CREATE_PERSONAL_ROOM}}
}

-- 解散时搜索结果
login.CMD_MB_DissumeSearchResult = 
{
    -- 房间ID
    {t = "dword", k = "dwServerID"},
    -- 桌子ID
    {t = "dword", k = "dwTableID"},
}

-- 房卡兑换游戏币
login.CMD_GP_ExchangeScoreByRoomCard = 
{
    -- 用户标识
    {t = "dword", k = "dwUserID"},
    -- 房卡数量
    {t = "score", k = "lRoomCard"},
    -- machineid
    {t = "tchar", k = "szMachieID", s = yl.LEN_MACHINE_ID},
}

-- 房卡兑换游戏币结果
login.CMD_GP_ExchangeRoomCardResult = 
{
    -- 成功标识
    {t = "bool", k = "bSuccessed"},
    -- 当前游戏币
    {t = "score", k = "lCurrScore"},
    -- 当前房卡
    {t = "score", k = "lRoomCard"},
    -- 提示内容
    {t = "string", k = "szNotifyContent"}
}

private_cmd.login = login
-- 登陆服务器
----------------------------------------------------------------------------------------------


----------------------------------------------------------------------------------------------
-- 游戏服务器
local game = {}
------
-- 命令
------
-- 私人房间
game.MDM_GR_PERSONAL_TABLE = 210                                        -- 

game.SUB_GR_CREATE_TABLE = 1                                            -- 创建桌子
game.SUB_GR_CREATE_SUCCESS = 2                                          -- 创建成功
game.SUB_GR_CREATE_FAILURE = 3                                          -- 创建失败
game.SUB_GR_CANCEL_TABLE = 4                                            -- 解散桌子
game.SUB_GR_CANCEL_REQUEST = 5                                          -- 请求解散
game.SUB_GR_REQUEST_REPLY = 6                                           -- 请求答复
game.SUB_GR_REQUEST_RESULT = 7                                          -- 请求结果
game.SUB_GR_WAIT_OVER_TIME = 8                                          -- 超时等待
game.SUB_GR_PERSONAL_TABLE_TIP = 9                                      -- 提示信息
game.SUB_GR_PERSONAL_TABLE_END = 10                                     -- 结束消息
game.SUB_GR_HOSTL_DISSUME_TABLE = 11                                    -- 房主强制解散桌子
game.SUB_GR_CANCEL_TABLE_RESULT = 13                                    -- 强制解散结果
game.SUB_GR_CURRECE_ROOMCARD_AND_BEAN = 16                              -- 强制解散桌子后的游戏豆和房卡数量
game.SUB_GR_CHANGE_CHAIR_COUNT = 17                                     -- 改变椅子数量
game.SUB_GR_JOIN_GOLD_TABLE = 51                                        -- 加入金币场
game.SUB_GR_CONTINUE_PLAY = 61                                          -- 房卡场继续打
game.SUB_GF_PERSONAL_MESSAGE = 202                                      -- 私人房消息

game.CANCELTABLE_REASON_PLAYER = 0                                      -- 玩家取消
game.CANCELTABLE_REASON_SYSTEM = 1                                      -- 系统取消
game.CANCELTABLE_REASON_ENFOCE = 2                                      -- 强制解散桌子
game.CANCELTABLE_REASON_ERROR = 3                                       -- 错误取消

------
-- 消息结构
------
-- 创建桌子
game.CMD_GR_CreateTable = 
{
    -- 底分设置
    {t = "score", k = "lCellScore"},
    -- 局数索引
    {t = "byte", k = "cbPlayCountIndex"},
    -- 参与游戏的人数
    {t = "word", k = "wJoinGamePeopleCount"},
    -- 单独税率
    {t = "dword", k = "dwRoomTax"},
    -- 密码设置
    {t = "tchar", k = "szPassword", s = yl.LEN_PASSWORD},
    -- 游戏规则，0位标识，0表示未设置，1表示设置设置，后面+自定义规则数据
    {t = "byte", k = "cbGameRule", l = {private_define.RULE_LEN}},    -- 此处有错（只是未用到ExternalFun.read_netdata），应为l = {private_define.RULE_LEN} by cjg@2017.6.6
    -- 0房主支付，1AA支付
    {t = "byte", k = "cbPayType"},
    -- 是否续打
    {t = "byte", k = "cbCanContinue"},
}

-- 创建成功
game.CMD_GR_CreateSuccess = 
{
    -- 房间编号
    {t = "string", k = "szServerID", s = private_define.ROOM_ID_LEN},
    -- 局数索引
    {t = "byte", k = "cbPlayCountIndex"},
    -- 最大局数，<0表示打捆分数
    {t = "int", k = "nPlayCount"},
    -- 游戏豆
    {t = "double", k = "dBeans"},
    -- 房卡数量
    {t = "score", k = "lRoomCard"},
}

-- 创建失败
game.CMD_GR_CreateFailure = 
{
    -- 错误代码
    {t = "int", k = "lErrorCode"},
    -- 描述消息
    {t = "string", k = "szDescribeString"},
}

-- 取消桌子
game.CMD_GR_CancelTable = 
{
    -- 取消原因
    {t = "dword", k = "dwReason"},
    -- 描述消息
    {t = "string", k = "szDescribeString"--[[, s = 128]]},
}

-- 请求解散
game.CMD_GR_CancelRequest = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 桌子ID
    {t = "dword", k = "dwTableID"},
    -- 椅子ID
    {t = "dword", k = "dwChairID"},
}

-- 请求答复
game.CMD_GR_RequestReply = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 桌子ID
    {t = "dword", k = "dwTableID"},
    -- 用户答复(1 同意; 0 不同意)
    {t = "byte", k = "cbAgree"},
}

-- 请求结果
game.CMD_GR_RequestResult = 
{
    -- 桌子ID
    {t = "dword", k = "dwTableID"},
    -- 请求结果
    {t = "byte", k = "cbResult"},
}

-- 超时等待
game.CMD_GR_WaitOverTime = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
}

-- 提示信息
game.CMD_GR_PersonalTableTip = 
{
    -- 桌主ID
    {t = "dword", k = "dwTableOwnerUserID"},
    -- 局数索引
    {t = "byte", k = "cbPlayCountIndex"},
    -- 最大局数，<0表示打捆分数
    {t = "int", k = "nPlayCount"},
    -- 已玩局数
    {t = "dword", k = "dwPlayCount"},
    -- 已玩时间
    {t = "dword", k = "dwPlayTime"},
    -- 游戏底分
    {t = "score", k = "lCellScore"},
    -- 初始分数
    {t = "score", k = "lIniScore"},
    -- 房间编号
    {t = "string", k = "szServerID", s = private_define.ROOM_ID_LEN},
    -- 是否参与游戏
    {t = "byte", k = "cbIsJoinGame"},
    -- 金币场0, 积分场1
    {t = "byte", k = "cbIsGoldOrGameScore"},
    -- 游戏规则，0位标识，0表示未设置，1表示设置设置，后面+自定义规则数据
    {t = "byte", k = "cbGameRule", l = {private_define.RULE_LEN}},
    -- 解散回复数量。发起+投票人数
    {t = "dword", k = "dwReplyCount"},
    -- 解散请求答复。0未投票 1同意 -1拒绝
    {t = "int", k = "nRequestReply", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 解散投票结束时间
    {t = "dword", k = "dwCancelTableOverTime"},
    -- 解散投票剩余秒数
    {t = "int", k = "nCancelTableOverSeconds"},
    -- 解散发起人chairid
    {t = "word", k = "wCancelTableChairID"},
    -- 房卡消耗
    {t = "int", k = "nFee"},
    -- 支付类型
    {t = "byte", k = "cbPayType"},
    -- 椅子数目
    {t = "byte", k = "cbChairCount"},
    -- 俱乐部规则ID
    {t = "dword", k = "dwClubID"},
    -- 俱乐部规则唯一ID	
    {t = "dword", k = "dwClubRuleID"},
    -- 俱乐部自定义规则创建人ID	
    {t = "dword", k = "dwClubOwnerID"},
    -- 继续打回复数量。发起+投票人数
    {t = "dword", k = "m_dwContinueReplyCount"},
    -- 继续打请求答复。0未投票 1同意 -1拒绝
    {t = "int", k = "m_nContinueReply", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 继续打次数，初始0
    {t = "int", k = "m_nContinue"},
    -- 战绩ID
    {t = "dword", k = "dwRecordID"},
    -- 是否可以续打
    {t = "byte", k = "cbCanContinue"},
}

-- 结束消息
game.CMD_GR_PersonalTableEnd = 
{
    {t = "string", k = "szDescribeString", s = 128},
    {t = "score", k = "lScore", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 特殊信息长度 
    {t = "int", k = "nSpecialInfoLen"},
    -- 特殊信息数据
    {t = "byte", k = "cbSpecialInfo", l = {1000}},
    -- 最大番数
    {t = "dword", k = "dwMaxScore", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 最大胡型
    {t = "string", k = "szMaxTurnDesc", s = 64, l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 胡牌次数
    {t = "word", k = "wHuTimes", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 账单号
    {t = "score", k = "lBillNo"},
    -- 底分
    {t = "dword", k = "dwBaseScore"},
    -- 是否一局已开始 1开始，0未开始
    {t = "byte", k = "cbGameStart"},
    -- 胡牌次数
    {t = "word", k = "wWinTimes", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 胡牌次数
    {t = "word", k = "wLoseTimes", l = {private_define.PERSONAL_ROOM_CHAIR}},
    -- 时间
    {t = "dword", k = "dwTime"},
    -- 房间号
    {t = "string", k = "szRoomID", s = private_define.ROOM_ID_LEN},
    -- 实际人数
    {t = "byte", k = "cbMaxPlayers"},
}

-- 房主强制解散
game.CMD_GR_HostDissumeGame = 
{
    -- 用户ID
    {t = "dword", k = "dwUserID"},
    -- 桌子ID
    {t = "dword", k = "dwTableID"},
}

-- 解散桌子
game.CMD_GR_DissumeTable = 
{
    -- 是否解散成功
    {t = "byte", k = "cbIsDissumSuccess"},
    -- 桌子ID
    {t = "string", k = "szRoomID", s = private_define.ROOM_ID_LEN},
    -- 解散时间
    {t = "table", k = "sysDissumeTime", d = private_struct.SYSTEMTIME},
    -- 用户信息
    {t = "table", k = "PersonalUserScoreInfo", d = private_struct.tagPersonalUserScoreInfo, l = {private_define.PERSONAL_ROOM_CHAIR}}
}

-- 私人房消息
game.Personal_Room_Message = 
{
    -- 提示信息
    {t = "string", k = "szMessage", s = 260},
    -- 信息类型,暂时无用
    {t = "byte", k = "cbMessageType"},
}

-- 强制解散桌子后的游戏豆和房卡
game.CMD_GR_CurrenceRoomCardAndBeans = 
{
    -- 游戏豆
    {t = "double", k = "dbBeans"},
    -- 房卡
    {t = "score", k = "lRoomCard"},
}

-- 改变椅子数量
game.CMD_GR_ChangeChairCount = 
{
    -- 椅子数量
    {t = "dword", k = "dwChairCount"},
}

private_cmd.game = game
-- 游戏服务器
----------------------------------------------------------------------------------------------
return private_cmd
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 离开确认
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbLeave = class('OrbLeave', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbLeave:ctor(panel)
    print('OrbLeave:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('btn_leave'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    self.m_panel:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function OrbLeave:onBtnClose()
    self:closeTop()
end


-------------------------------------------------------------------------------
-- 继续按钮点击
-------------------------------------------------------------------------------
function OrbLeave:onBtnContinue()
    self:close()
end


return OrbLeave
[{"logtime": "2017/10/31 14:05:15", "logdata": "[string \"client/src/app/models/bit.lua\"]:69: attempt to index local 'self' (a number value)\nstack traceback:\n\t[string \"game/mahjong/src/MainScene.lua\"]:492: in function 'func'\n\t[string \"client/src/helper/AppHelper.lua\"]:285: in function <[string \"client/src/helper/AppHelper.lua\"]:261>"}, {"logtime": "2017/10/31 14:05:15", "logdata": "[string \"cocos/init.lua\"]:57: attempt to call global 'buglyReportLuaException' (a nil value)\nstack traceback:\n\t[string \"client/src/app/models/bit.lua\"]:69: in function '_or'\n\t[string \"game/mahjong/src/MainScene.lua\"]:492: in function 'func'\n\t[string \"client/src/helper/AppHelper.lua\"]:285: in function <[string \"client/src/helper/AppHelper.lua\"]:261>"}]
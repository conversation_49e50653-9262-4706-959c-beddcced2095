-------------------------------------------------------------------------------
--  创世版1.0
--  背包寄件地址
--  @date 2018-03-08
--  @auth woodoo
-------------------------------------------------------------------------------
local EditBox = cs.app.client('system.EditBox')


local BagPostLayer = class("BagPostLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function BagPostLayer:ctor(callback)
    print('BagPostLayer:ctor...')
    self:enableNodeEvents()
    self.m_callback = callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('BagPostLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    local input_name, input_phone, input_address = main_node:child('bg'):child('input_name, input_phone, input_address')
    EditBox.convertTextField(input_name, 'turn_bg_input.png')
    EditBox.convertTextField(input_phone, 'turn_bg_input.png')
    EditBox.convertTextField(input_address, 'turn_bg_input.png')

    main_node:child('bg/btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('bg/btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function BagPostLayer:onEnter()
    print('BagPostLayer:onEnterTransitionFinish...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function BagPostLayer:onExit()
    print('BagPostLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 确定按钮点击
-------------------------------------------------------------------------------
function BagPostLayer:onBtnOk(sender)
    local input_name, input_phone, input_address = self.main_node:child('bg'):child('input_name,input_phone,input_address')
    local name = input_name:getString():trim()
    local phone = input_phone:getString():trim()
    local address = input_address:getString():trim()
    if name == '' or phone == '' or address == '' then
        helper.pop.message( LANG.ITEM_POST_HINT )
        return
    end

    self.m_callback({name=name, phone=phone, address=address})
    self:removeFromParent()
end


return BagPostLayer
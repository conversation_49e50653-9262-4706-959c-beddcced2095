短信http接入协议
12344.1版本历史记录
版本	制订者	更改内容	修改时间	变更原因
V1.0.0	North	初始化版本	2021-1-14	初始化版本
				
				
				
				
				
				


一．固定模板短信下行（单个变量内容对多个号码）
1.请求
（1）请求地址：
http://open2.ucpaas.com/sms-server/templatesms
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8;
（3）请求包体：
属性	类型	约束	说明
clientid	String	必选	帐号，6位，
如：a00012,b00012
password	String	必选	密码，8－12位，MD5加密后32位，小写，
如：1bbd886460827015e5d605ed44252251
mobile	String	必选	发送手机号码，
国内短信不要加前缀，国际短信号码前须带相应的国家区号，如日本：0081，
支持多号码，号码之间用英文逗号隔开，最多500个。国内手机号码如：18612341234
国际号码如：0085265656565
templateid	String	必选	模板ID
param	String	可选	模板中对应的参数值，
1）参数值个数必须与模板中变量个数一致； 
2）格式为“参数值;参数值;参数值”的方式；
3）如果模板中没有占位符，此处可以不传。
例如：秒到;123;456
extend①	String	可选	自扩展端口，
1－4位，只能为数字，可以为空
(注：请先询问配置的通道是否支持自扩展端口，如果不支持，请填空)
uid②	String	可选	用户透传ID，
随状态报告返回，最长60位
sendtime	String	可选 	定时发送时间，为空表示立即发送，定时发送时间不得小于5分钟或者大于24小时。
定时发送格式(20161111090005)
注：
①extend字段用于客户传送由客户自行分配给子客户的扩展端口，用于上行短信回来与之对应。
如：某客户下有A、B、C三个子客户，并且该客户获得某通道两位自扩展，分别对其子客户自行分配的扩展端口依次为子客户A：01，子客户B：02，子客户C:03。
若子客户A在发送下行短信时将该扩展端口01填入此字段即可，上行短信将会把此字段的扩展端口01发给客户，用于客户区分哪个子客户的上行短信，子客户A可根据上行短信中的电话号码对应之前的下行手机号码；
②uid字段用户客户在单、群发短信时由客户生成并下发到平台的序列号（最长不超过60位），该uid将在应答、状态报告中返回给客户，用于客户区分或对应群发短信的批次。
（4）JSON请求示例:
{
"clientid":"test",
"password":"6918d0046aab6a1ee290f751e02bd0b2",
"mobile":"13800138000,13800138001,19800138002,19800138003",
"templateid":"1000",
"param":"秒到;123",
"extend":"00",
"uid":"00",
"sendtime": "20161111090005"
}

2.响应
（1）响应包体：
属性	类型	约束	说明
total_fee①	Int	必选	短信总计费条数
code	Int	　必选	短信请求响应返回码，
参考“请求响应返回码”定义的返回码
（详见第九章第1节）
msg	String	必选	短信请求响应返回中文描述，
参考“请求响应返回码”定义的中文描述
（详见第九章第1节）
uid④	String	可选	用户透传ID，随状态报告返回
data		可选	每个手机号发送的详细情况(code为0才有)
　fee②	Int	必选	成功发送的短信计费条数，
计费规则如下：
70个字一条，超出70个字时按每67字一条计费
（英文按字母个数计算）
　mobile	String	必选	接收短信的手机号码
　sid③	String	必选	短信标识符（用于匹配状态报告），
一个手机号对应一个sid
注：
①total_fee表示单（群）发短（长）短信总共计费的条数，该条数等于data域中各个fee字段数量之和；
②fee表示每个短信接收的手机号码收到短信的计费条数（长短信按照短信计费规则进行计费，长短信拆分最大不超过20条）；
③sid是短信平台产生的唯一标示，与后面返回的状态报告中的sid一一对应，用于下发短息与状态报告相对应；
④uid字段返回内容和第二章第1节请求中的“用户透传ID”一致，用于客户区分或对应单、群发短信的批次。
（2）JSON响应示例
{
		"total_fee":2,
　　"code":0,
　　"msg":"发送成功",
　　"uid":"1234",
　　"data":
　　[
　　{
　　"fee":1,
　　　　"mobile":"13800138000",
　　　　　"sid":"123456789123",
　　　　"uid":"1234"
　　　　　　},
　　{
　　　　"fee":1,
　　　　"mobile":"13800138001",
　　　　　"sid":"09faf6-5728-838d-95ed-e0e0cec4fd39",
　　　　"uid":"1234"
　　　　　　}
　　　　　]
　}

　　或者：
{
		"total_fee":0,
　　"code":-1,
　　"msg":"鉴权失败（帐号或密码错误）",
　　"uid":"1234",
　　　　}

二．变量模板短信下行（多变量内容对多号码）
1.请求
（1）请求地址：
http://open2.ucpaas.com/sms-server/variablesms
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8;
（3）请求包体：
属性	类型	约束	说明
clientid	String	必选	帐号，6位，
如：a00012,b00012
password	String	必选	密码，8－12位，MD5加密后32位，小写，
如：1bbd886460827015e5d605ed44252251
mobile	String	必选	发送手机号码，
国内短信不要加前缀，国际短信号码前须带相应的国家区号，如日本：0081，
支持多号码，号码之间用英文逗号隔开，最多500个。国内手机号码如：18612341234
国际号码如：0085265656565
templateid	String	必选	模板ID
param	String	可选	模板中对应的参数值，
1）参数值以竖线(|)分割，个数必须与号码个数一致； 
2）每个子参数以分号(;)分割，个数必须与模板中变量个数一致。
例如：秒到1;123|秒到2;456|秒到3;789
extend①	String	可选	自扩展端口，
1－4位，只能为数字，可以为空
(注：请先询问配置的通道是否支持自扩展端口，如果不支持，请填空)
uid②	String	可选	用户透传ID，
随状态报告返回，最长60位
sendtime	String	可选 	定时发送时间，为空表示立即发送，定时发送时间不得小于5分钟或者大于24小时。
定时发送格式(20161111090005)
注：
③extend字段用于客户传送由客户自行分配给子客户的扩展端口，用于上行短信回来与之对应。
如：某客户下有A、B、C三个子客户，并且该客户获得某通道两位自扩展，分别对其子客户自行分配的扩展端口依次为子客户A：01，子客户B：02，子客户C:03。
若子客户A在发送下行短信时将该扩展端口01填入此字段即可，上行短信将会把此字段的扩展端口01发给客户，用于客户区分哪个子客户的上行短信，子客户A可根据上行短信中的电话号码对应之前的下行手机号码；
④uid字段用户客户在单、群发短信时由客户生成并下发到平台的序列号（最长不超过60位），该uid将在应答、状态报告中返回给客户，用于客户区分或对应群发短信的批次。
（4）JSON请求示例:
{
"clientid":"test",
"password":"6918d0046aab6a1ee290f751e02bd0b2",
"mobile":"13800138000,13800138001,19800138002,19800138003",
"templateid":"1000",
"param":"秒到1;12|秒到2;34|秒到2;56|秒到2;78",
"extend":"00",
"uid":"00",
"sendtime":"20161111090005"
}

2.响应
（3）响应包体：
属性	类型	约束	说明
total_fee①	Int	必选	短信总计费条数
code	Int	必选	短信请求响应返回码，
参考“请求响应返回码”定义的返回码
（详见第九章第1节）
msg	String	必选	短信请求响应返回中文描述，
参考“请求响应返回码”定义的中文描述
（详见第九章第1节）
uid④	String	可选	用户透传ID，随状态报告返回
data		可选	每个手机号发送的详细情况(code为0才有)
　fee②	Int	必选	成功发送的短信计费条数，
计费规则如下：
70个字一条，超出70个字时按每67字一条计费
（英文按字母个数计算）
　mobile	String	必选	接收短信的手机号码
　sid③	String	必选	短信标识符（用于匹配状态报告），
一个手机号对应一个sid
注：
⑤total_fee表示单（群）发短（长）短信总共计费的条数，该条数等于data域中各个fee字段数量之和；
⑥fee表示每个短信接收的手机号码收到短信的计费条数（长短信按照短信计费规则进行计费，长短信拆分最大不超过20条）；
⑦sid是短信平台产生的唯一标示，与后面返回的状态报告中的sid一一对应，用于下发短息与状态报告相对应；
⑧uid字段返回内容和第二章第1节请求中的“用户透传ID”一致，用于客户区分或对应单、群发短信的批次。
（4）JSON响应示例
{
		"total_fee":2,
　　"code":0,
　　"msg":"发送成功",
　　"uid":"1234",
　　"data":
　　[
　　{
　　"fee":1,
　　　　"mobile":"13800138000",
　　　　　"sid":"123456789123",
　　　　"uid":"1234"
　　　　　　},
　　{
　　　　"fee":1,
　　　　"mobile":"13800138001",
　　　　　"sid":"09faf6-5728-838d-95ed-e0e0cec4fd39",
　　　　"uid":"1234"
　　　　　　}
　　　　　]
　}

　　或者：
{
		"total_fee":0,
　　"code":-1,
　　"msg":"鉴权失败（帐号或密码错误）",
　　"uid":"1234",
　　　　}

三．短信上行－推送方式
1.请求
（1）请求地址：
　　需要第三方自行配置URL地址，接受http post请求，消息格式：json表达式。
（2）请求包体：
属性	类型	约束	说明
mo_id①	String	必选	上行标识符
mobile	String	必选	短信发送端手机号码
content	String	必选	短信内容，
UTF-8编码，最长1024个字
extend②	String	可选	扩展端口
（注：此功能需要通道支持）
mo_time	String	必选	上行时间(20160402175215)
注：
①mo_id是由短信平台产生的唯一标示，可用于客户到平台查询上行情况；
②extend字段返回内容和第一章（或第二章）第1节请求中的“扩展端口”一致，请查看之前详细说明；
上行短信中的extend字段与客户自行分配给子客户的扩展端口相对应，子客户即可通过上行中的电话号码找到之前下发的下行短信与之对应；
（3）JSON请求示例:
[
　　{
    	"mo_id":"79a11e15-5363-4a0f-b3f0-46240bd3cea6",
　　"mobile":"13800138000",
　　　　　"content":"短信上行1",
　　　　　"extend":"00",
　　　　　"mo_time":"20160402175215"
　　},{},{}
]

2.响应
（1）响应包体：
属性	类型	约束	说明
code	Int	必选	返回0即可

（2）JSON响应示例:
成功：
{
　　"code":0
}




四．短信状态报告－推送方式
1.请求
（1）请求地址：
　　需要第三方自行配置URL地址，接受http post请求，消息格式：json表达式。 
（2）请求包体：
属性	类型	约束	说明
sid①	String	必选	短信标识符
uid②	String	可选	用户透传ID，
随状态报告返回
mobile	String	必选	短信接收端手机号码
report_status③	String	必选	状态码，
report_desc④	String	必选	状态报告代码，
代码分类：
1.运营商返回的状态报告代码；
2.短信平台返回的状态报告代码，参考“短信状态报告返回码”定义（详见第九章第2节）；
report_time	String	必选	状态报告时间(20160402175215)
注：
①sid字段返回内容和第一章（或第二章）第2节响应中的“短信标识符”一致，用于与之前下行短信的发送记录相对应；
②uid字段返回内容和第一章（或第二章）第2节响应中的“用户透传ID”一致，用于客户区分或对应单、群发短信的批次；
③report_status表示短信是否成功投递到手机，下行短信成功投递到手机上（即手机收到下行短信）状态码为SUCCESS
④report_desc字段将会有投递成功等相似字段出现（根据不同协议中文描述将会不一样），如“DELIVER”、“投递成功”等；下行短信没有投递到手机上（即手机没有收到下行短信）状态码为FAIL；④desc字段将会有投递失败等相似字段出现（根据不同协议中文描述将会不一样），如“空号”、“超频”等。

通过sid和mobile两字段可确定是哪个手机的下行短信状态报告；
通过report_status和report_desc字段可确定手机是否收到下行短信，并清楚下行短信投递状态；
通过report_time字段可确定下行短信的到达时间。
（3）JSON请求示例:
　[
    {
    "sid":"123456789123",
　　　"uid":"00",
    "mobile":"13800138000",
　　　"report_status":"SUCCESS",
　　　"report_desc":"DELIVRD",
　　　"report_time":"20160402175215"
　　},{},{}
　　]



2.响应
（1）响应包体：
属性	类型	约束	说明
code	Int	必选	返回0即可

（2）JSON响应示例:
成功：
{
　　　"code":0
}
返回http的状态码200就可以
五．短信上行－拉取方式
1.请求
（1）请求地址：
http://open2.ucpaas.com/sms-server/getmo
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8; 
（3）请求包体：
属性	类型	约束	说明
clientid	String	必选	帐号，6位，
如：a00012,b00012
password	String	必选	密码，8－12位，MD5加密后32位，小写，
如：1bbd886460827015e5d605ed44252251

（4）JSON请求示例：
{
"clientid":"test",
"password":"6918d0046aab6a1ee290f751e02bd0b2"
}




2.响应
（1）响应包体：
属性	类型	约束	说明
code	Int	必选	拉取上行请求响应返回码，
参考“请求响应返回码”定义的返回码（详见第九章第1节）
msg	String	必选	拉取上行请求响应返回的中文描述，
参考“请求响应返回码”定义的中文描述（详见第九章第1节）
data			上行的详细情况
当code<0时，无此数组
　mo_id①	String	必选	上行标识符
　mobile	String	必选	短信发送端手机号码
　content	String	必选	短信内容，
UTF-8编码，最长600个字
　extend②	String	可选	扩展端口
（注：此功能需要通道支持）
　mo_time	String	必选	上行时间
注：
①mo_id是由短信平台产生的唯一标示，可用于客户到平台查询上行情况；
②extend字段返回内容和第一章（或第二章）第1节请求中的“扩展端口”一致，请查看之前详细说明；
上行短信中的extend字段与客户自行分配给子客户的扩展端口相对应，子客户即可通过上行中的电话号码找到之前下发的下行短信与之对应；
（2）JSON响应示例:
{
　"code":0,
　"msg":"成功",
　"data":[
      {
    		"moid":"79a11e15-5363-4a0f-b3f0-46240bd3cea6",
　　　　　　"mobile":"13800138000",
　　　　　"content":"短信上行1",
　　　　　"extend":"00",
　　　　　"mo_time":"20160402175215"
　}
　　]
　　}
　　
包头/包体	实例	备注
Header	POST /xxx/xxx/xxx HTTP/1.1
Accept-Encoding:identity
Content-Length:157
Host:***********:9999
Accept:application/json
Content-Type:application/json;charset=utf-8	蓝色字体为可变部分，
保证路径正确,采用http的POST方式发送；
Body	{"code":0,"msg":"成功" ,"data":[{"moid":"79a11e15-5363-4a0f-b3f0-46240bd3cea6","mobile":"13800138000","content":"短信上行1","sign":"秒到","extend":"00","reply_time":"20160402175215"}]}	蓝色字体为可变部分
　　


六．短信状态报告－拉取方式
1.请求
（1）请求地址：
http://open2.ucpaas.com/sms-server/getreport
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8; 
（3）请求包体：
属性	类型	约束	说明
clientid	String	必选	帐号，6位，
如：a00012,b00012
password	String	必选	密码，8－12位，MD5加密后32位，小写，
如：1bbd886460827015e5d605ed44252251

（4）JSON请求示例：
{
"clientid":"test",
"password":"6918d0046aab6a1ee290f751e02bd0b2"
}


2.响应
（1）响应包体：
属性	类型	约束	说明
code	Int	必选	拉取状态报告请求响应返回码，
参考“请求响应返回码”定义的返回码（详见第九章第1节）
msg	String	必选	拉取状态报告请求响应返回的中文描述，
参考“短信请求响应返回码”定义的中文描述（详见第九章第1节）
data			状态报告的详细情况
当code<0时，无此数组
　sid①	String	必选	短信标识符
　uid②	String	可选	用户透传ID，
随状态报告返回
　mobile	String	必选	短信接收端手机号码
　report_status③	String	必选	状态码 SUCCESS/FAIL
　report_desc④	String	必选	状态报告代码，
代码分类：
1.运营商返回的状态报告代码；
2.短信平台返回的状态报告代码，参考“短信状态报告返回码”定义（详见第八章第2节）
　report_time	String	必选	状态报告时间(20160402175215)
注：
①sid字段返回内容和第一章（或第二章）第2节响应中的“短信标识符”一致，用于与之前下行短信的发送记录相对应；
②uid字段返回内容和第一章（或第二章）第2节响应中的“用户透传ID”一致，用于客户区分或对应单、群发短信的批次；
③report_status表示短信是否成功投递到手机，下行短信成功投递到手机上（即手机收到下行短信）状态码为SUCCESS；④desc字段将会有投递成功等相似字段出现（根据不同协议中文描述将会不一样），如“DELIVER”、“投递成功”等；下行短信没有投递到手机上（即手机没有收到下行短信）状态码为FAIL；④desc字段将会有投递失败等相似字段出现（根据不同协议中文描述将会不一样），如“空号”、“超频”等。

通过sid和mobile两字段可确定是哪个手机的下行短信状态报告；
通过report_status和report_desc字段可确定手机是否收到下行短信，并清楚下行短信投递状态；
通过report_time字段可确定下行短信的到达时间。
（2）JSON响应示例：
{
　"code":0,
　"msg":"成功" ,
　"data":[
      {
　　　　"sid":"123456789123",
　　　　"uid":"00",
　　　　"mobile":"13800138000",
　　　　"report_status":"SUCCESS",
　　　　"report_desc":" DELIVRD",
　　　　"report_time":"20160402175215"
　　　},
　　　{
           "sid":"123456789123",
　　"uid":"00",
           "mobile":"13800138001",
　　　　"report_status":"FAIL",
　　　　"report_desc":"MC:0001",
　　　　"report_time":"20160402175215"
　　　}
　　　　]
　　}
　　
包头/包体	实例	备注
Header	HTTP/1.1 200 OK
Accept-Encoding:identity
Content-Length:339
Host:***********:45302
Accept:application/json
Content-Type:application/json;charset=utf-8	蓝色字体为可变部分，
保证路径正确,采用http的POST方式发送；
Body	{"code":0,"msg":"成功" ,"data":[{"sid":"123456789123","uid":"00","mobile":"13800138000","report_status":"SUCCESS","desc":"DELIVRD","user_receive_time":"20160402175215"},{"sid":"123456789123","uid":"00","mobile":"13800138001","report_status":"FAIL","desc":"MC:0001","user_receive_time":"20160402175215"}]}	蓝色字体为可变部分
　　


七．余额查询（暂时没用）
1.产品包扣费请求
（1）请求地址：
http://open2.ucpaas.com/sms-server/getbalance
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8;
（3）请求包体：
属性	类型	约束	说明
clientid	String	必选	帐号，6位
如：a00012,b00012
password	String	必选	密码，8－12位，MD5加密后32位，小写
如：1bbd886460827015e5d605ed44252251

（4）JSON请求示例:
{
"clientid":"test",
"password":"6918d0046aab6a1ee290f751e02bd0b2"
}




包头/包体	实例	备注
Header	POST /sms-partner/access/test/getreport HTTP/1.1
Accept-Encoding:identity
Content-Length:67
Host:***********:9999
Accept:application/json
Content-Type:application/json;charset=utf-8	蓝色字体为可变部分，
保证路径正确,采用http的POST方式发送；
Body	{"clientid":"test","password":"6918d0046aab6a1ee290f751e02bd0b2"}	蓝色字体为可变部分




2.产品包扣费响应
（1）响应包体：
属性	类型	约束	说明
code	Int	必选	余额查询请求返回码，
参考“请求响应返回码”定义的返回码（详见第九章第1节）
msg	String	必选	余额查询请求应答返回中文描述
参考“请求响应返回码”定义的中文描述（详见第九章第1节）
data			余额的详细情况
当code<0时，无此数组
product_type	Int	必选	产品类型，
0：行业，
1：营销，
2：国际，
7：USSD，
8：闪信，
9：挂机短信
remain_quantity	String	必选	剩余总数量，普通短信：条，国际短信：元
注：
行业、营销、USSD、闪信和挂机短信等类型产品返回剩余总条数，国际类型产品返回剩余余额。 
（2）JSON响应示例：
{
　"code":0,
　"msg":"成功" ,
　"data":[
      {
   		    "product_type":0,
　　　　"remain_quantity":"1000"
　　　},
　　　{
           "product_type":1,
　　　　"remain_quantity":"800"
　　　},
　　　{
           "product_type":2,
　　　　"remain_quantity":"618.00"
　　　},
　　　{
           "product_type":7,
　　　　"remain_quantity":"700"
　　　},
　　　{
           "product_type":8,
　　　　"remain_quantity":"800"
　　　},
　　　{
           "product_type":9,
　　　　"remain_quantity":"900"
　　　}
　　　　]
　　}
失败回应：
{
"code":-13,
"msg":"您的费用信息不存在",
"data":null

}
　　
包头/包体	实例	备注
Header	HTTP/1.1 200 OK
Accept-Encoding:identity
Content-Length:314
Host:***********:45302
Accept:application/json
Content-Type:application/json;charset=utf-8	蓝色字体为可变部分，
保证路径正确,采用http的POST方式发送；
Body	{"code":0,"msg":"成功","data":[{"product_type":0,"remain_quantity":"1000"},{"product_type":1,"remain_quantity":"800"},{"product_type":2,"remain_quantity":"618.00"},{"product_type":7,"remain_quantity":"700"},{"product_type":8, "remain_quantity":"800"},{"product_type": 9,"remain_quantity":"900"}]}	蓝色字体为可变部分

八．直客余额查询
1.直客扣费请求
（1）请求地址：
http://open2.ucpaas.com/sms-server/getdirectbalance
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8;
（3）请求包体：
属性	类型	约束	说明
clientid	String	必选	帐号，6位
如：a00012,b00012
password	String	必选	密码，8－12位，MD5加密后32位，小写
如：1bbd886460827015e5d605ed44252251

（4）JSON请求示例:
{
"clientid":"test",
"password":"6918d0046aab6a1ee290f751e02bd0b2"
}

2.直客扣费响应
（3）响应包体：
属性	类型	约束	说明
code	Int	必选	余额查询请求返回码，
参考“请求响应返回码”定义的返回码（详见第十一章第1节）
msg	String	必选	余额查询请求应答返回中文描述
参考“请求响应返回码”定义的中文描述（详见第十一章第1节）
price	String	可选	余额的详细情况
当code<0时，无此数组
　yd_sms_price	String	必选	单位：厘，移动短信价格
　lt_sms_price	String	必选	单位：厘，联通短信价格
　dx_sms_price	String	必选	单位：厘，电信短信价格
　gj_sms_discount	String	必选	单位：厘，国际短信价格
　give_remain_number	Int	必选	赠送剩余条数
　balance	String	必选	单位：厘，子账号余额
PS：子账号如果是扣主类型，返回的是主账号的余额，否则返回子账号的余额。
（4）JSON响应示例：
{
　"code":0,
　"msg":"成功" ,
　"price":
      {
   	       	"yd _sms_price":"100",
　　　　　　　　"lt _sms_price":"100",
　　　　"dx _sms_price":"100",
　　　"gj _sms_discount":"5000",
　　　"give_remain_number":10,	
　　　"balance":"1000000"
　　　}
　}
失败回应：
{
"code":-13,
"msg":"您的费用信息不存在",
"price":null
}

九．联网认证（暂时没用）
1.请求
（1）请求地址：
http://172.16.5.17:8989/sms-server/authentication
注意：为了确保数据隐私和安全，用户需要通过http Post方式请求，消息格式：json表达式。
（2）http标准包头字段：
Accept:application/json;
Content-Type:application/json;charset=utf-8;
（3）请求包体：
属性	类型	约束	说明
serial_num	String	必选	32位ESMS系统序列号
如：36432e80ad265a106c2f5121c3c597ca（14位ESMS系统序列号生成时间_6位自增长序号：20180504120247_000000）
component_type	String	必选	1位组件类型
如：1
component_md5	String	必选	32位组件md5值
如：5d6f4f09665c7d667bbf3d074488db20
timestamp	String	必选	客户端时间时间戳
例如：1525797177
pub_key	String	必选	ESMS系统组件当前会话公钥，用于SMSP加密应答报文

（4） JSON请求示例:
{
  "serial_num":"36432e80ad265a106c2f5121c3c597ca",
  "component_type":"1",
  "component_md5":"5d6f4f09665c7d667bbf3d074488db20",
  "timestamp":"1525797177",
  "pub_key":""
}
包头/包体	实例	备注
Header	POST /esms/access/test/getreport HTTP/1.1
Accept-Encoding:identity
Content-Length:67
Host:***********:9999
Accept:application/json
Content-Type:application/json;charset=utf-8	蓝色字体为可变部分，
保证路径正确,采用http的POST方式发送；
Body	{"serial_num":"36432e80ad265a106c2f5121c3c597ca","component_type":"1","component_md5":"5d6f4f09665c7d667bbf3d074488db20","timestamp":"1525797177","pub_key":""}	蓝色字体为可变部分



2.响应
（1）响应包体：
属性	类型	约束	说明
code	Int	必选	联网认证请求返回码，
0：成功
-1：失败
expire_timestamp	Int	可选	过期时间戳，code为0时返回

（2）JSON响应示例：
成功应答：
{
　"code":0,
　"expire_timestamp":1525800355
　　}
失败应答：
{
　　"code":-1
}
　　
包头/包体	实例	备注
Header	HTTP/1.1 200 OK
Accept-Encoding:identity
Content-Length:314
Host:***********:45302
Accept:application/json
Content-Type:application/json;charset=utf-8	蓝色字体为可变部分，
保证路径正确,采用http的POST方式发送；
Body	{"code":0,"expire_timestamp":1525800355}	蓝色字体为可变部分
十一.新增模板申请接口
http://open2.ucpaas.com/sms-server/addsmstemplate
{
        "clientid":"test",
        "password" : "6918d0046aab6a1ee290f751e02bd0b2",
        "smstype":"4" //0:通知短信 4:验证码短信 5:会员营销
        "sign" : "签名",
        "content" : "内容"
 }
返回的json
{
        "code":0,
        "msg" : "成功"
}
模板审核状态推送参数（json）

{"clientId":"b11111","content":"短信内容","createTime":"2023-06-15 17:14:19","creator":"测试2","remark":"","sign":"短信签名","smsType":"通知","state":"审核通过","templateId":12345,"templateType":"固定模板"}
十二.返回码定义
1.请求返回码
返回码	描述	适用类型
0	成功	全部
-1	鉴权失败（帐号或密码错误）	全部
-2	账号余额不足	短信下行、
模板短信下行
-3	账号被注销	全部
-4	账号被锁定	短信下行、
模板短信下行、定时短信下行
-5	ip鉴权失败	全部
-6	发送号码为空	全部
-7	手机号码格式错误	短信下行、
模板短信下行、定时短信下行
-8	短信内容超长	短信下行、
模板短信下行、定时短信下行
-9	签名未报备	短信下行、
模板短信下行
-10	协议类型不匹配	短信下行、
模板短信下行、定时短信下行
-11	不允许拉取状态报告	短信状态报告
-12	访问频率过快	余额查询、
短信状态报告、
短信上行
-13	您的费用信息不存在	余额查询
-14	内部错误	余额查询、
短信状态报告、
短信上行
-15	用户对应的模板ID不存在、或模板未通过审核、或模板已删除	模板短信下行
-16	模板参数不匹配	模板短信下行
-17	USSD、闪信和挂机短信模板不允许发送国际号码	模板短信下行
-18	模板ID为空	模板短信下行
-19	模板参数含有非法字符	模板短信下行
-20	json格式错误	全部
-21	解析json失败	全部
-22	账号被冻结	短信下行、
模板短信下行、定时短信下行
-23	短信类型为空	短信下行、
定时短信下行
-24	短信内容为空	短信下行、
定时短信下行
-25	发送号码数量超过100个	短信下行、
模板短信下行
-26	未找到签名	短信下行、
定时短信下行
-27	签名长度过短	短信下行、
定时短信下行
-28	签名长度过长	短信下行、
定时短信下行
-29	发送号码黑名单	短信下行、
模板短信下行、
定时短信下行
-30	重复的发送号码	短信下行、
模板短信下行、
定时短信下行
-31	无查询结果	状态报告查询接口
-32	不允许拉取上行	短信上行
-33	定时短信时间格式错误	定时短信下行
-34	定时发送时间太短（小于5分钟）	定时短信下行
-35	定时发送时间太长（大于一天）	定时短信下行
-36	号码解压失败	定时短信下行
-37	联网认证失败	联网认证
-38	无效的extend参数值	短信下行、
定时短信下行
-39	靓号拦截	短信下行、
定时短信下行



2.状态报告返回码
返回码	描述
YX:1000	发送号码超频
YX:1006	关键字超频
YX:4000	系统内部错误4000
YX:4001	系统内部错误4001
YX:4002	短信处理超时
YX:4010	系统内部错误4010
YX:4011	系统内部错误4011
YX:4012	系统内部错误4012
YX:4014	系统内部错误4014
YX:4015	系统内部错误4015
YX:4016	系统内部错误4016
YX:4017	系统内部错误4017
YX:4018	系统内部错误4018
YX:4019	系统内部错误4019
YX:5001	系统内部错误5001
YX:5002	系统内部错误5002
YX:5004	系统内部错误5004
YX:5006	系统内部错误5006
YX:5007	系统内部错误5007
YX:5008	系统内部错误5008
YX:5009	订单余额不足
YX:7000	审核不通过
YX:8008	系统黑名单
YX:8010	系统内部错误8010
YX:8019	系统关键字
YX:9000	系统内部错误9000
YX:9001	号码格式错误
YX:9002	账号不存在
YX:9003	系统内部错误9003
YX:9004	无可用通道组
YX:9005	系统内部错误9005
YX:9006	无可选用通道
YX:9007	系统内部错误9007
YX:9008	系统内部错误9008
YX:9010	系统内部错误9010
YX:9011	系统内部错误9011
YX:9012	系统超频错误9012
YX:9013	系统内部错误9013
YX:9014	系统内部错误9014
YX:9015	系统内部错误9015



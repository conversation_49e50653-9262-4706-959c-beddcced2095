-------------------------------------------------------------------------------
--  红包
--  @date 2018-01-16
--  @auth mike
-------------------------------------------------------------------------------
local cmd_private = cs.app.client('header.CMD_Private')
-- 登陆服务器CMD
local cmd_pri_login = cmd_private.login

local RedBagLayer = class("RedBagLayer", cc.Layer)

---------------------------------c----------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RedBagLayer:ctor()
    print('RedBagLayer:ctor...')
    self:enableNodeEvents()
    
    PassRoom:getInstance():setViewFrame(self)
    self:setName('subRedBagLayer')
    -- 载入主UI
    local main_node = helper.app.loadCSB('RedBagLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.main_node:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose))
    self.main_node:child('btn_rule'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRule))
    self.main_node:child('btn_get_money'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnGetMoney))
    self.main_node:child('img_money/btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare))

    self.main_node:child('img_money/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCloseGetMoney))
    
    self.template = self.main_node:child('panel_main/mission_template')
    self.template:hide()
    
    self:getEventList()
end

-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function RedBagLayer:onEnterTransitionFinish()
    print('RedBagLayer:onEnterTransitionFinish...')
end

-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RedBagLayer:onExit()
    PassRoom:getInstance():setViewFrame(nil)
    print('RedBagLayer:onExit...')
end

-------------------------------------------------------------------------------
-- 退出
-------------------------------------------------------------------------------
function RedBagLayer:onBtnClose()
    self:removeFromParent()
end

-------------------------------------------------------------------------------
-- 退出 提现界面
-------------------------------------------------------------------------------
function RedBagLayer:onBtnCloseGetMoney()
    self.main_node:child('img_money'):hide()
end
-------------------------------------------------------------------------------
-- 获取金钱
-------------------------------------------------------------------------------
function RedBagLayer:onBtnGetMoney()
    if not self.event_id or not self.total_money or not self.withDraw then
        return
    end
    if self.total_money <  self.withDraw then
        helper.pop.message( '需获得'..self.withDraw..'元才可以提现哦' )
        return
    end
    self.main_node:child('img_money'):show()
end

-------------------------------------------------------------------------------
-- 规则
-------------------------------------------------------------------------------
function RedBagLayer:onBtnRule()
    if self.desc then
        helper.pop.popRule( self.desc )
    end
end

-------------------------------------------------------------------------------
-- 分享
-------------------------------------------------------------------------------
function RedBagLayer:onBtnShare( sender )
    --local share = helper.pop.shareImage(nil, false, ' ', 'share_money', self.doShareMoneyResp )
    local share = helper.pop.shareImage(nil, false, ' ', 'share_money', handler(self, self.doShareMoneyResp) )
    if share then
        share:hideHY()
    end
end

-- 
function RedBagLayer:doShareMoneyResp( resp )
    if tolua.isnull( self ) then
        return
    end
    self.main_node:child('img_money'):hide()
    local uid =  GlobalUserItem.dwUserID
    yl.GetUrl( yl.URL_WITHDRAW, 'post', { uid = uid, event_id = self.event_id, money = self.total_money }, handler(self, self.onGetMoneyResponse))
end

-- 
function RedBagLayer:doShareMissionResp( resp )
    if tolua.isnull( self ) then
        return
    end
    print('doShareMissionResp')
    self:getEventList()
end

-------------------------------------------------------------------------------
--  获取 初始化 数据
-------------------------------------------------------------------------------
function RedBagLayer:getEventList()
    PassRoom:getInstance():getNetFrame():onSendQueryEventList( GlobalUserItem.dwUserID )
end

-------------------------------------------------------------------------------
--  获取 初始化 数据
-------------------------------------------------------------------------------
function RedBagLayer:onListenEvent( data_buffer, cmd )
    if cmd_pri_login.SUB_GR_EVENT_LIST_RESULT == cmd then
        self:onReloadEventList( data_buffer )
    elseif cmd_pri_login.SUB_GR_EVENT_GET_REWARD_RESULT == cmd then
        self:onBtnGetRewardResult( data_buffer )
    elseif cmd_pri_login.SUB_GR_EVENT_ACCEPT_RESULT == cmd then
        self:onEventGetMission( data_buffer )
    end 
end

-------------------------------------------------------------------------------
--  获取 初始化 数据
-------------------------------------------------------------------------------
function RedBagLayer:onReloadEventList( data_buffer )
    local label_end_date = self.main_node:child('label_end_date')
    local len = #data_buffer
    if len > 0 then
        label_end_date:setString( helper.time.format(data_buffer[1].dwEndTime, '%Y-%m-%d'))
    end
    local list_view_mission = self.main_node:child('panel_main/list_view_mission')
    -- list_view_mission:removeAllItems()
    dump(data_buffer,'data_buffer',9)
    for i, event_info in ipairs( data_buffer ) do
        local node = self:getEventItemByEventID( event_info.nEventID )
        local isNew = false
        if not node then
            node = self.template:clone()
            list_view_mission:pushBackCustomItem( node )
            isNew = true
        end
        node:show()
        node.data = event_info
        if i == 1 then
            self:updateMoney( event_info.nRedPacket )
            self.event_id = event_info.nEventID
            self.total_money = event_info.nRedPacket
            self.withDraw = event_info.nWithDraw
            self.desc = event_info.szMsg:split('|')
        end
        local param_data = event_info.params[1][1]
        local label_title = node:child('img_bg/label_title')
        label_title:setString(LANG{'MISSION_DAY', day = i})
        local label_money = node:child('img_bg/img_node/img_open/label_money')
        label_money:setString(LANG{'MISSION_MONEY', money = param_data.nRewardNum})
        local label_desc_1 = node:child('img_bg/img_node/img_open/label_desc_1')
        label_desc_1:setString( param_data.szName)
        local btn_operate = node:child('img_bg/img_node/img_open/btn_operate')
        -- btn_operate:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOperate))
        local img_bg = node:child('img_bg')
        if isNew then
            img_bg:px( 140 + 1500 )
            local scale = 1
            local seq = cc.Sequence:create(cc.DelayTime:create(0.15*i), cc.MoveTo:create(0.2, cc.p(140, 200)), cc.CallFunc:create(function(a)
                local action = cc.Repeat:create(cc.Sequence:create(cc.ScaleTo:create(0.07, scale + 0.1), cc.ScaleTo:create(0.07, scale)), 2)
                a:child('img_node/img_close'):runMyAction(action) end ))
            img_bg:runMyAction( seq )       
        end
        local img_close = node:child('img_bg/img_node/img_close')
        local img_open = node:child('img_bg/img_node/img_open')

        local label_desc_2 = node:child('img_bg/img_node/img_open/label_desc_2')
        label_desc_2:setString(LANG{'MISSION_PERCENT', jindu = param_data.nDoneNum ..'/' .. param_data.GoalNum })

        local btn_operate = img_open:child('btn_operate') 
        btn_operate.data = param_data
        btn_operate:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnGetReward))
        local img_title = btn_operate:child('img_title') 
        --未领取的任务
        if param_data.cbStatus == 255 then
            img_close:show()
            img_open:hide()
            img_close.data = event_info
            img_close:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOpenRedBag))
            if event_info.bIsClose == false then
                local action2 = cc.RepeatForever:create(cc.Sequence:create(cc.DelayTime:create(3.0),cc.RotateTo:create(0.07, 10),cc.RotateTo:create(0.07, -10),cc.RotateTo:create(0.07, 10), cc.RotateTo:create(0.07, -10), cc.RotateTo:create(0.07, 0)))
                img_bg:child('img_node/img_close'):runMyAction( action2 )
                img_close:setCascadeOpacityEnabled(true)
            end
            if param_data.szGoal == 'SHARE' then 
                btn_operate:texture('#red_bag_red_btn.png')
                img_title:texture('#red_bag_word_share.png')
            else
                btn_operate:texture('#red_bag_red_btn_gray.png')
                img_title:texture('#red_bag_word_get.png')
            end 
        else 
            img_close:hide()
            img_open:show()
            
            --当前状态0未完成，1可领奖，2已领,255标识没有接
            if param_data.cbStatus == 0 then
                if param_data.szGoal == 'SHARE' then 
                    btn_operate:texture('#red_bag_red_btn.png')
                    img_title:texture('#red_bag_word_share.png')
                else
                    btn_operate:texture('#red_bag_red_btn_gray.png')
                    img_title:texture('#red_bag_word_get.png')
                end 
            elseif param_data.cbStatus == 1 then
                btn_operate:texture('#red_bag_red_btn.png')
                img_title:texture('#red_bag_word_get.png')
            elseif param_data.cbStatus == 2 then
                btn_operate:texture('#red_bag_red_btn_gray.png')
                img_title:texture('#red_bag_word_have_get.png')
            end
        end
    end
end

-------------------------------------------------------------------------------
--  领奖
-------------------------------------------------------------------------------
function RedBagLayer:onBtnGetReward( sender )
    if sender.data.szGoal == 'SHARE' and (sender.data.cbStatus == 0 or sender.data.cbStatus == 255) then
        local share = helper.pop.shareImage('common/share_image_red_bag.jpg', false, ' ', 'share_mission', handler(self, self.doShareMissionResp) )
        share:hideHY()
        return
    end
    local req = {}
    req.userID = GlobalUserItem.dwUserID
    req.paramID = sender.data.nParamID
    PassRoom:getInstance():getNetFrame():onSendGetReward( req )
end

-------------------------------------------------------------------------------
--  领奖
-------------------------------------------------------------------------------
function RedBagLayer:onBtnGetRewardResult( data_buffer )
    dump(data_buffer, '领奖励', 9)
    helper.pop.message(data_buffer.szMsg)
    if data_buffer.nID > 0 then
        self:runAction(cc.CallFunc:create(function(a)
            self:getEventList()
        end))
    end
end

-------------------------------------------------------------------------------
--  执行操作
-------------------------------------------------------------------------------
function RedBagLayer:onBtnOperate()
    local share = helper.pop.shareImage('common/share_image_red_bag.jpg', false, ' ', 'share_mission', handler(self, self.doShareMissionResp) )
    share:hideHY()
end

-------------------------------------------------------------------------------
--  开红包
-------------------------------------------------------------------------------
function RedBagLayer:onBtnOpenRedBag( sender )
    local req = {}
    req.userID = GlobalUserItem.dwUserID
    req.eventID = sender.data.nEventID
    PassRoom:getInstance():getNetFrame():onSendGetMission( req )
end

-------------------------------------------------------------------------------
--  刷新信息
-------------------------------------------------------------------------------
function RedBagLayer:updateMoney( money )
    local label_total_money = self.main_node:child('panel_main/img_money_bg/label_total_money')
    label_total_money:setText( money .. '元')
    local label_num = self.main_node:child('img_money/label_num')
    label_num:setString( money )
end

-------------------------------------------------------------------------------
--  接任务
-------------------------------------------------------------------------------
function RedBagLayer:onEventGetMission( data_buffer )
    dump(data_buffer, '接任务', 9)
    if data_buffer.nID > 0 then
        self:openRedBag( data_buffer.nID )
    end
    helper.pop.message(data_buffer.szMsg)
end

function RedBagLayer:openRedBag( eventID )
    local node = self:getEventItemByEventID( eventID )
    if node then
        local img_close = node:child('img_bg/img_node/img_close')
        local img_open = node:child('img_bg/img_node/img_open')
        img_close:setTouchEnabled(false)
        img_close:runMyAction(cc.FadeOut:create(0.5))
        img_open:show()
        img_open:setOpacity(0)
        img_open:runMyAction(cc.FadeIn:create(0.5))    
    end
end

--
function RedBagLayer:getEventItemByEventID( eventID )
    local list_view_mission = self.main_node:child('panel_main/list_view_mission')
    local children = list_view_mission:getChildren()
    for i, child in ipairs( children ) do
        if child.data.nEventID == eventID then
            return child
        end 
    end
end

-------------------------------------------------------------------------------
-- 提现返回
-------------------------------------------------------------------------------
function RedBagLayer:onGetMoneyResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then
        return
    end
    self:getEventList()
    helper.pop.message('提现成功')
end

return RedBagLayer
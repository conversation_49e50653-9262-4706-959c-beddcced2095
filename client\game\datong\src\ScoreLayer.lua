-------------------------------------------------------------------------------
--  创世版1.0
--  战绩
--      父类：ScoreBase
--      目前考虑到该功能比较通用，因此主要逻辑都写在父类中
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local ScoreBase = cs.app.client('system.ScoreBase')
local ScoreLayer = class('ScoreLayer', ScoreBase)


local DETAIL_NAME = '_score_detail_layer_'


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ScoreLayer:ctor()
    self.super.ctor(self)
    self:setName("subScoreLayer")
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ScoreLayer:onEnter()
    print('ScoreBase:onExit...')
    if self.super.onEnter then
        self.super.onEnter(self)
    end
    
    PassRoom:getInstance():setViewFrame(self)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ScoreLayer:onExit()
    print('ScoreBase:onExit...')
    if self.super.onExit then
        self.super.onExit(self)
    end

    PassRoom:getInstance():setViewFrame(nil)
end


-------------------------------------------------------------------------------
-- 显示详情列表(PassRoom中调用，scores:tagRecordScore)
-- @override
-------------------------------------------------------------------------------
function ScoreLayer:showDetailScores(scores)
    local detail_layer = helper.app.getFromScene(DETAIL_NAME)
    if detail_layer then
        detail_layer:showScores(scores)
    end
end


-------------------------------------------------------------------------------
-- 显示详情
-- @override
-------------------------------------------------------------------------------
function ScoreLayer:onBtnDetail(sender)
    self:hide()
    local record = sender.record
    local path = cs.game.SRC .. 'ScoreDetailLayer'
    local layer = helper.pop.popLayer(path, nil, {record, handler(self, function(a) a:show() end)})
    layer:setName(DETAIL_NAME)
end


return ScoreLayer
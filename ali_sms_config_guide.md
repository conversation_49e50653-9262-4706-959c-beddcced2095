# 阿里云短信服务配置指南

## 📋 替换完成情况

### ✅ 已完成的替换
1. **AliSmsService.php** - 阿里云短信服务类
2. **user.php** - 短信接口替换（云之讯 → 阿里云）
3. **测试脚本** - 集成测试脚本

### 🔧 需要配置的参数

#### 1. 阿里云账户配置
```php
// 在 user.php 中需要替换的配置
$accessKeyId = 'YOUR_REAL_ACCESS_KEY_ID';        // 替换为真实的AccessKey ID
$accessKeySecret = 'YOUR_REAL_ACCESS_KEY_SECRET'; // 替换为真实的AccessKey Secret
$signName = '快马互娱';                           // 短信签名（已配置）
$templateCode = 'SMS_229700066';                  // 短信模板代码（已配置）
```

#### 2. 获取阿里云配置步骤

##### 步骤1: 开通阿里云短信服务
1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 搜索"短信服务"并开通
3. 进入短信服务控制台

##### 步骤2: 创建AccessKey
1. 点击右上角头像 → AccessKey管理
2. 创建AccessKey（建议使用子账户）
3. 记录 AccessKey ID 和 AccessKey Secret

##### 步骤3: 配置短信签名
1. 在短信服务控制台 → 国内消息 → 签名管理
2. 添加签名：`快马互娱`
3. 等待审核通过（通常1-2个工作日）

##### 步骤4: 配置短信模板
1. 在短信服务控制台 → 国内消息 → 模板管理
2. 添加模板：`您的验证码是${code}，请在10分钟内使用。`
3. 获取模板代码（如：SMS_229700066）
4. 等待审核通过

## 🔄 替换对比

### 云之讯 vs 阿里云

| 项目 | 云之讯 | 阿里云 |
|------|--------|--------|
| **稳定性** | ⚠️ 一般 | ✅ 优秀 |
| **价格** | ✅ 便宜 | ⚠️ 稍贵 |
| **技术支持** | ⚠️ 有限 | ✅ 完善 |
| **文档质量** | ⚠️ 一般 | ✅ 详细 |
| **SSL问题** | ❌ 存在 | ✅ 无问题 |
| **API稳定性** | ⚠️ 偶有超时 | ✅ 稳定 |

### 接口格式对比

#### 云之讯接口
```php
// 请求
$ucpass->templateSMS($appId, $phone, $templateId, $code);

// 响应
{
    "resp": {
        "respCode": "000000",
        "respDesc": "成功"
    },
    "smsid": "771377271628906496"
}
```

#### 阿里云接口
```php
// 请求
$aliSms->sendVerifyCode($phone, $code);

// 响应
{
    "Code": "OK",
    "Message": "OK",
    "BizId": "123456789^0",
    "RequestId": "F655A8D5-B967-440B-8683-DAD6FF8DE990"
}
```

## 🧪 测试验证

### 1. 运行集成测试
```bash
python test_ali_sms_integration.py
```

### 2. 测试项目
- ✅ 阿里云API参数构建
- ✅ 验证码发送接口
- ✅ 手机登录接口
- ✅ 错误处理机制

### 3. 预期结果
```
阿里云短信集成测试报告:
============================================================
direct_api: ✅ 成功
verify_code: ✅ 成功
phone_login: ✅ 成功

总体结果: 3/3 个测试成功
```

## 🚀 部署步骤

### 1. 备份原文件
```bash
cp user.php user.php.backup
cp -r libraries/Ucpaas.class.php libraries/Ucpaas.class.php.backup
```

### 2. 部署新文件
```bash
# 复制阿里云短信服务类
cp AliSmsService.php /path/to/your/project/

# 更新user.php（已完成）
# 配置真实的AccessKey信息
```

### 3. 配置真实参数
在 `user.php` 中替换：
```php
$accessKeyId = 'LTAI5tBvKJhKJhKJhKJhKJhK';        // 替换为真实值
$accessKeySecret = 'YourAccessKeySecret123456789';   // 替换为真实值
```

### 4. 测试验证
```bash
# 运行测试脚本
python test_ali_sms_integration.py

# 检查日志
tail -f /var/log/php_errors.log
```

## 📊 监控和维护

### 1. 关键指标
- 短信发送成功率
- 响应时间
- 错误率
- 成本控制

### 2. 日志记录
```php
// 在user.php中添加详细日志
error_log("阿里云短信发送: $phone, $code, 结果: $result");
```

### 3. 错误处理
- 网络超时重试
- AccessKey错误处理
- 余额不足提醒
- 模板参数验证

## 🔒 安全建议

### 1. AccessKey安全
- 使用子账户AccessKey
- 定期轮换AccessKey
- 限制IP白名单
- 最小权限原则

### 2. 短信安全
- 验证码有效期控制
- 发送频率限制
- 手机号格式验证
- 防刷机制

### 3. 成本控制
- 设置日发送量限制
- 监控异常发送
- 余额预警机制

## 🆘 故障排查

### 常见问题及解决方案

#### 1. AccessKey错误
```
错误: InvalidAccessKeyId.NotFound
解决: 检查AccessKey ID是否正确
```

#### 2. 签名错误
```
错误: SignatureDoesNotMatch
解决: 检查AccessKey Secret和签名算法
```

#### 3. 模板不存在
```
错误: InvalidTemplate.NotFound
解决: 检查模板代码是否正确，模板是否审核通过
```

#### 4. 手机号格式错误
```
错误: InvalidPhoneNumber
解决: 检查手机号格式，确保是11位数字
```

## 📞 技术支持

### 阿里云技术支持
- 工单系统：阿里云控制台 → 工单
- 技术文档：https://help.aliyun.com/product/44282.html
- API文档：https://help.aliyun.com/document_detail/101414.html

### 紧急联系
- 如果短信服务完全不可用，可以临时启用其他验证方式
- 考虑配置多个短信服务商作为备用方案

-------------------------------------------------------------------------------
--  创世版1.0
--  局结算(小结算)
--  @date 2017-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')


local GameResultLayer = class("GameResultLayer", function(scene)
    return helper.app.loadCSB('GameResultLayer.csb')
end)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GameResultLayer:ctor(scene, cmd_data, zhuang_chair_id, player_count)
    self._scene = scene
    self.player_count = player_count
    self:setName('GameResultLayer')
    self:child('template'):hide()
    print('self.player_count is ', self.player_count)
    
    self:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    self:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )

    self:showResult(cmd_data, zhuang_chair_id)

end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 继续游戏按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnContinue(sender)
    self:doClose()
end


-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function GameResultLayer:showResult(cmd_data, zhuang_chair_id)
    --dump(cmd_data)
    local template = self:child('template')
    local game_layer = self._scene._scene
    local size = template:size()
    local gap = 10
    local nPlayerCount = self.player_count

    local first_win_id = game_layer.wOutCardIndex[game_layer.cbWinIndex[1]]
    local partner_id = game_layer.wOutCardIndex[game_layer.cbWinIndex[2]]
    if cmd_data.cbStatus == 2 then
        partner_id = game_layer.wOutCardIndex[game_layer.cbWinIndex[3]]
    elseif cmd_data.cbStatus == 3 then
        partner_id = game_layer.wOutCardIndex[game_layer.cbWinIndex[4]]
    end


    local start_x = display.width/2 + (nPlayerCount == 3 and -(size.width + gap) or -(nPlayerCount - 1) * 0.5 * (size.width + gap))
    for i = 1, nPlayerCount do repeat
        local index = game_layer.wOutCardIndex[i]
        local userItem = game_layer:getUserInfoByChairID(index)
        if userItem == nil then
            break
        end
        local panel = template:clone():show():addTo(self)
        panel:px(start_x):zorder(10)
        start_x = start_x + size.width + gap

        -- 头像
        local panel_avator = panel:child('head')
        head = PopupHead:create(self, userItem, 150, 150)
        head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

        -- 昵称
        panel:child('txt_name'):setString(userItem.szNickName)
        panel:child('txt_id'):setString('ID:' .. helper.str.formatUserID(userItem.dwUserID))

        --dump(userItem)
        panel:child('txt_score'):setString( cmd_data.lGameScore[1][userItem.wChairID + 1] )
        panel:child('txt_total_score'):setString(userItem.nTableScore + cmd_data.lGameScore[1][userItem.wChairID + 1])

        local img_order = panel:child('img_order')
        img_order:zorder(10)
        if cmd_data.cbJiesan and cmd_data.cbJiesan == 1 then
            img_order:hide()
        else
            local ming_sort = 1
            for j = 1, cmd.GAME_PLAYER do
                local index = cmd_data.wWinOrder[1][j]
                local chairid = self._scene._scene.wOutCardIndex[index]
                if userItem.wChairID == chairid then
                    ming_sort = j
                    break
                end
            end
            if first_win_id == userItem.wChairID then
                img_order:texture('word/1ming.png')
            elseif partner_id == userItem.wChairID then
                img_order:texture('word/king' .. tostring(ming_sort) .. 'ming.png')
            else
                img_order:texture('word/' .. tostring(ming_sort) .. 'ming.png')
            end
            
        end

       -- panel:child('txt_average_score'):setString('-'.. tostring(cmd_data.lAverageScore))
        

        --[[
        if game_layer:GetMeChairID() + 1 == i then
            local color = cc.c3b(0xFE, 0x0E, 0x0D)
            panel:child('txt_score'):setTextColor(color)
            panel:child('txt_gong_score'):setTextColor(color)
            panel:child('txt_order'):setTextColor(color)
            panel:child('txt_card_score'):setTextColor(color)
            panel:child('txt_average_score'):setTextColor(color)
            print('game_layer:GetMeChairID() is ', game_layer:GetMeChairID() )
        end
--]]

        if game_layer:GetMeChairID() == userItem.wChairID then
            local bIsWin = false
            if cmd_data.lGameScore[1][userItem.wChairID + 1] >= 0 then
                bIsWin = true
            end

            local title = self:child('title')
            if cmd_data.cbStatus == 1 and bIsWin then
                title:texture('word/shuangkou01.png')
            elseif cmd_data.cbStatus == 1 and not bIsWin then
                title:texture('word/shuangkou02.png')
            elseif cmd_data.cbStatus == 2 and bIsWin then
                title:texture('word/dankou01.png')
            elseif cmd_data.cbStatus == 2 and not bIsWin then
                title:texture('word/dankou02.png')
            elseif cmd_data.cbStatus == 3 and bIsWin then
                title:texture('word/pingkojjju.png')
            elseif cmd_data.cbStatus == 3 and not bIsWin then
                title:texture('word/pingkou.png')
            end
        end
    until true
    end
    self:hide()
    self:perform(function () self:show() end, 2, 1)
end


-------------------------------------------------------------------------------
-- 关闭界面
-------------------------------------------------------------------------------
function GameResultLayer:doClose()
    if yl.IS_REPLAY_MODEL then
       helper.app.getFromScene('game_room_layer'):onExitRoom()
    else
       local is_room_ended = PassRoom:getInstance().m_bRoomEnd
       print('is_room_ended is ', is_room_ended)
       if not is_room_ended then
            --self._scene.btStart:setVisible(true)
            self._scene:onButtonClickedEvent('btn_start')
       else
            local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
            if room_result_layer then
                room_result_layer:show()
            else
                GlobalUserItem.bWaitQuit = false
                local game_layer = self._scene._scene
                if game_layer then
                    game_layer:onExitRoom()
                end
            end
       end
    end
    self:removeFromParent()
end


return GameResultLayer
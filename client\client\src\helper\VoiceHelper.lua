-------------------------------------------------------------------------------
--  创世版1.0
--  语音录音辅助方法类
--      访问方式：helper.voice.
--  @date 2017-06-30
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")

local VoiceHelper = {}
helper = helper or {}
helper.voice = VoiceHelper


local VOICE_LAYER_NAME = '_voice_record_layer_'


-------------------------------------------------------------------------------
-- 开始录音
-------------------------------------------------------------------------------
function VoiceHelper.startRecord(room_frame)
	--防作弊不聊天
	if GlobalUserItem.isAntiCheat() then
        helper.pop.message( LANG.ANTICHEAT_CHAT_DENY )
		return
	end
	
	local layer = cs.app.client('system.VoiceRecorderKit').createRecorderLayer(room_frame)
    if layer then
        layer:setName(VOICE_LAYER_NAME)
	    helper.app.addToScene(layer)
    end
end


-------------------------------------------------------------------------------
-- 停止录音
-------------------------------------------------------------------------------
function VoiceHelper:stopRecord()
	local layer = helper.app.getFromScene(VOICE_LAYER_NAME)
	if layer then
		layer:removeRecorde()
	end
end


-------------------------------------------------------------------------------
-- 取消录音
-------------------------------------------------------------------------------
function VoiceHelper:cancelRecord()
	local layer = helper.app.getFromScene(VOICE_LAYER_NAME)
	if layer then
		layer:cancelVoiceRecord()
	end
end

-------------------------------------------------------------------------------
--  创世版1.0
--	框架语言
--  1、大写
--  2、对齐
--  两种用法:
--  1. LANG.PASS
--  2. LANG{'PASS_NAME', name='...'} 带参数
-------------------------------------------------------------------------------

-- 所有游戏通用的语言可以写在此处
LANG = {
    HEALTH_GAME         = '抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。',
    GAME_POLICY_AGREE   = '需要先同意本平台的《服务协议》和《隐私政策》',

    START_CONNECT_WEB   = '获取服务器信息...',
    START_WEB_ERROR     = '网络错误（$code）',
    START_NEW_PACKAGE   = '有新的版本，是否立即下载升级？',
    START_RETRY_MSG     = '$msg\n是否重试？',
    START_INSTALL_HINT  = '请安装新版本！',
    START_RESTART       = '更新完成，请重新启动游戏！',
    START_DOWN_FAIL     = '下载失败（$code），是否重试？',
    START_DOWN_BROWSER  = '下载失败（$code），请尝试用浏览器下载！',
    START_COMPLETE      = '完成',

    BACKGROUND_1        = '阳光沙滩',
    BACKGROUND_2        = '北国风光',
    BACKGROUND_3        = '恭贺新春',
    BACKGROUND_4        = '清新淡雅',

    ID_STR              = '帐号:$id',
    IP_STR              = '地址:$ip',

    DAY_NAME            = '$month月$day日',
    DAY_TODAY           = '今天',
    DAY_TOMORROW        = '明天',
    DAY_AFTER_TOMORROW  = '后天',

    BLANK               = '空',
    GOLD_NUM_POP        = '金币数量：$num',
    CLICK_JOIN          = '[点击立刻加入]',
    CMD_DATA_LEN_ERROR  = '命令数据长度错误（$len/$single）！',
    NO_GAME_CONFIG      = '游戏即将开放，敬请期待！',
    NO_KIND_CONFIG      = '即将开放，敬请期待！',
    ROOM_NOT_FOUND      = '房间未找到, 请重试!',
    DEFAULT_ROLL_MSG    = '推广员咨询微信号:$weixin。本平台仅供娱乐使用，请自觉远离赌博行为。',
    SHARE_FANGKA        = '每日首次分享至朋友圈，可获得一定数量房卡或奖券！',
    BIND_FANGKA         = '成功绑定推荐码后立即送$num张房卡',
    BIND_ALREADY        = '已绑定推荐码$id（$name），可更改',
    CONNECT_FAIL        = '建立连接失败！',
    UNKNOW_OPERATE      = '未知操作模式！',
    SURE_EXIT           = '确定要退出游戏吗？',
    NETWORK_ERROR       = '网络错误：$code！',
    SEND_ERROR          = '发送命令失败（$main:$sub）！',
    GAME_NEED_UPDATE    = '玩法需要更新，是否继续？',
    OPEN_SOON           = '即将开放，敬请期待！',
    RECOMMEND_CODE      = '推荐码: $code',
    BIND_HELP           = '请联系公众号：$value',
    SCORE_TITLE         = '$name（底分：$base_score）',
    SCORE_TITLE_GROUP   = '房间号：$room（底分：$base_score）',
    SCORE_USER_ID       = 'ID:$id',
    SCORE_BA            = '第$num把',
    FANGKA_INFO_1       = '代理加盟请联系：<h>$value<=>【公众号】',
    FANGKA_INFO_2       = '            或微信号：<h>$value<=>',
    FANGKA_INFO_3       = '            购买房卡：<h>游戏内点击商城购买',
    FANGKA_INFO_4       = '投诉建议与举报：<h>$value<=>【公众号】',
    MOBILE_BIND_SHOW    = '已绑定手机：$value',
    MOBILE_BIND_SENT    = '验证码已发送！',
    MOBILE_BIND_SUCC1   = '手机绑定成功（获得$num张房卡）！',
    MOBILE_BIND_SUCC    = '手机绑定成功！',
    MOBILE_BIND_TIME    = '$seconds秒后重新获取',
    MOBILE_BIND_BAD     = '请输入有效的手机号！',
    NOT_BIND_PHONE      = '暂未绑定手机',
    HAS_BOUND           = '已绑定',
    CHANGE_PHONE        = '改绑',
    INVALID_IDENTITY_NO = '无效的身份证号！',
    IDENTITY_BIND_SUCC  = '实名认证成功！',
    SELECT_DISTRICT     = '请先选择地区！',
    SIGN_IN_DESCS       = '房卡*1|房卡*1|奖券*20|奖券*30|房卡*1|奖券*50|房卡*2',
    NOT_FOUND_USER      = '未找到玩家信息$chair',

    MOBILE_LOGIN_BAD    = '请输入有效的手机号！',
    MOBILE_LOGIN_SENT   = '验证码已发送！',
    MOBILE_LOGIN_TIME   = '$seconds秒后重新获取',

    ARENA_START_AT      = '$prefix$time',
    ARENA_APPLY_FREE    = '免费报名',
    ARENA_APPLY_SHARE   = '分享成功报名',
    ARENA_APPLY_FANGKA  = '报名费：房卡x$num',
    ARENA_APPLY_GOLD    = '报名费：金币x$num',
    ARENA_APPLY_SUCC    = '报名成功！<|><r>请在比赛开始前回到比赛场界面，否则无法进入比赛。',
    ARENA_APPLY_E_SUCC  = '退赛成功！',
    ARENA_APPLY_MIN     = '满$num人开赛',
    ARENA_APPLY_PLAYERS = '报名人数：$num',
    ARENA_RESULT_TITLE  = '恭喜玩家<r+6>$name<=>在【$arena】中荣获',
    ARENA_WAIT_MATCH    = '等待开始...',
    ARENA_WAITING       = '匹配中...',
    ARENA_GOING         = '进行中...',
    ARENA_UNFINISH      = '您在【$arena】中的积分为：$score！<|>仍有其他玩家未完成比赛，比赛结束后奖励将直接发放到您的背包中。',

    REDARENA_RULE       = '规则：指定麻将二人玩法，玩家随机匹配进行游戏，7连胜后获得随机数量奖券。胡牌的玩家连胜数+1，失败者连胜终结，可使用复活卡进行复活，保留当前连胜场数。<|>连胜数：以每个自然日为单位，24:00将连胜数重置为0。<|>流局：若流局，则两人都不算输赢，可继续游戏。<|>奖券兑换：7连胜后获得的奖券可在兑换商城中兑换。<|>获奖次数：每人每天最多能获得3次。',
    REDARENA_WIN        = '再胜<y>$num场<=>即瓜分100万现金！',
    REDARENA_LOSE_0     = '很遗憾！您还没有获得胜利！<|>请重新开始游戏',
    REDARENA_LOSE_N     = '使用<y>$need_num<=>张复活卡，继续挑战！<|>已有复活卡$has_num张',
    REDARENA_LOSE_N_TIP = '求助好友，继续挑战！',
    REDARENA_LOSE_HELP  = '分享到微信群可立即复活',
    REDARENA_RELIVE     = '复 活',
    REDARENA_SUCCESS    = '在$date的比赛中取得第$times次7连胜，获得：',
    REDARENA_RELIVE_SUCC= '复活成功，请继续游戏！',
    REDARENA_SUCC_SHARE = '分享成功，获得一张复活卡！',
    REDARENA_FINISH     = '您今日已经完成$num次7连胜了，休息一下吧！',

    CLUB_SUCC           = '成功！',
    CLUB_CREATE_NOTICE  = '1. 推广员可申请创建亲友圈，自己的朋友可加入亲友圈进行游戏；<|>2. 每个推广员可最多创建5个亲友圈，每个玩家最多可加入5个亲友圈；<|>3. 玩家加入亲友圈时需亲友圈会长审核通过才可加入；<|>4. 亲友圈内创建的房间只能由亲友圈成员进入；<|>5. 本游戏禁止各种形式的赌博行为；<|>6. 提交申请后需人工审核，请勿重复申请，更多详情请咨询微信客服：$weixin',
    CLUB_CREATE_RULE    = '1.亲友圈人数上限人数为500人<|>2.亲友圈管理员有管理人员权限<|>3.亲友圈管理员有权建立和解散亲友圈',
    CLUB_APPLY_TIP      = '填写您的申请信息，等待对方通过',
    CLUB_APPLY_SUCC     = '亲友圈加入申请成功！',
    CLUB_SELECT         = '选择',
    CLUB_CREATE_SUCC    = '亲友圈创建成功，等待审核！',
    CLUB_SELECT_PLEASE  = '请先选择亲友圈地区！',
    CLUB_NAME_EMPTY     = '请输入亲友圈名称！',
    CLUB_NAME_BEYOND    = '亲友圈名称太长（$max字以内）！',
    CLUB_WEIXIN_EMPTY   = '请输入会长微信号！',
    CLUB_INVITE_TITLE   = '$app_name[亲友圈ID:$id]',
    CLUB_INVITE_MSG     = '邀请您进入亲友圈$club，极速组局，快速邀请',
    CLUB_MODIFY_NAME    = '请输入新的亲友圈名称：',
    CLUB_MODIFY_WEIXIN  = '请输入新的会长微信：',
    CLUB_QUERY_DISMISS  = '确定要解散亲友圈吗？',
    CLUB_QUERY_EXIT     = '确定要退出亲友圈吗？',
    CLUB_DISMISS_SUCC   = '解散成功！',
    CLUB_EXIT_SUCC      = '退出成功！',
    CLUB_APPLY_REFUSE   = '确定拒绝吗？',
    CLUB_APPLY_AGREE    = '确定同意吗？',
    CLUB_APPLY_A_SUCC   = '申请处理成功！',
    CLUB_MEMBER_KICK    = '确定要将该成员踢出亲友圈吗？',
    CLUB_KICK_SUCC      = '踢出成功！',
    CLUB_SET_MANAGER    = '确定要将该成员设为管理员吗？',
    CLUB_UNSET_MANAGER  = '确定要取消该管理员吗？',
    CLUB_SET_SUCC       = '设置成功！',
    CLUB_KIND_DELETE    = '确定要删除吗？',
    CLUB_MEMBER_STATUS0 = '离线',
    CLUB_MEMBER_STATUS2 = '忙碌',
    CLUB_MEMBER_STATUS3 = '空闲',
    CLUB_INFO           = '创建人：$owner    ID:$id',
    CLUB_INVITE_SUCC    = '邀请已成功发出！',
    CLUB_INVITE_NOTIFY  = '<g>$club<=>亲友圈成员<g>$member<=>邀请您进入<|><g>$rule',
    CLUB_INVITE_WARNING = '邀请太频繁，请稍候再试！',
    CLUB_ROOM_JUSHU     = '第$count/$limit局',
    CLUB_ROOM_JUSHU_KUN = '第$count局',
    CLUB_FREE_COUNT     = '空闲$count人',
    CLUB_BUSY_COUNT     = '忙碌$count人',

    ROOM_DESC_WANFA          = '玩法:$value',
    ROOM_DESC_JUSHU          = '局数:$value',
    ROOM_DESC_RENSHU         = '人数:$value',
    ROOM_DESC_DIFEN          = '底分:$value',
    ROOM_DESC_ZHIFU          = '房卡:$value',
    ROOM_DESC_JUSHU_KUN      = '$kun分一捆',
    ROOM_DESC_JUSHU_QUAN     = '$kun圈',

    AGREE               = '同意',
    NOT_AGREE           = '不同意',
    NOTICE              = '公告',
    GOLD                = '金币',
    SELF                = '自己',
    DAY                 = '天',
    LAST_DAYS           = '最近$num天',

    CREATE_WANFA_CHECK  = '<r>$desc<w>必选其一！',
    CREATE_ZHIFU_0      = '房主出$fangka张',
    CREATE_ZHIFU_1      = '每人出$fangka张',
    CREATE_ZHIFU_2      = '房主出$fangka张',
    CREATE_ZHIFU_3      = '大赢家出$fangka张',
    CREATE_ZHIFU_4      = '大赢家出$fangka张',
    ZHIFU_3_DESC        = '大赢家支付本场房费。若出现多个大赢家，则平摊后向下取整。',

    CREATE_JUSHU        = '$person <=-4>',
    CREATE_RENSHU       = '$num人',
    CREATE_ROOM_FAIL    = '发送创建房间失败！',
    ROOM_EXIT_GOLD      = '金币场不能中途退出，请在局结算时退出房间！',
    ROOM_EXIT_GOLD_QUERY= '确定要退出房间吗？',
    ROOM_EXIT_DENIED    = '不能中途退出！',
    ROOM_SCORE_READY    = '<y>$who<w>金币自动补足<y>$score',
    ROOM_ACT_TIP        = '操作过于频繁，请稍候再试！',
    ROOM_SHUFFLE_HINT   = '本局玩家<j>$name<=>洗牌',
    ROOM_SHUFFLE_CONFIRM= '是否使用1张房卡兑换$rate张洗牌卡并洗牌？',
    ROOM_SHUFFLE_CARD0  = '洗牌祈好运',
    ROOM_SHUFFLE_CARD   = '剩$num张洗牌卡',

    ROOM_DISMISS_APPLY  = '解散房间需其他玩家同意, 是否申请解散房间？',
    ROOM_DISMISS_QUERY  = '你是房主, 是否要解散该房间？',
    ROOM_DISMISS_REPLY  = '<b>$name<g>申请解散房间',
    ROOM_DISMISS_WAIT   = '<b>$name<=>等待选择...',
    ROOM_DISMISS_AGREE  = '<b>$name<g>同意解散',
    ROOM_DISMISS_UNAGREE= '<b>$name<r>不同意解散',
    ROOM_DISMISS_DESC   = '$time后无操作将自动同意',

    GAME_NO             = '第$index局',
    SCORE_NOTE          = '底  分：$base_score\n账单号：$bill_no',
    SHARE_GOLD          = '每日首次分享游戏到朋友圈，即可获得随机数量金币！',
    SHARE_SUCCESS       = '分享成功',
    ANTICHEAT_CHAT_DENY = '防作弊房间禁止聊天',
    SHARE_GET_FANGKA    = '获得房卡<g>x$num',
    SHARE_NO_FANGKA     = '感谢分享！',
    SHARE_GET_GOLD      = '获得金币<g>x$num',
    SHARE_NO_GOLD       = '感谢分享！',
    SHARE_GET_QUAN      = '获得奖券<g>x$num',
    SHARE_COPIED        = '您的房间信息已复制，请粘帖至聊天工具内分享！',
    IP_SAME_WARNING     = 'IP重复,请注意！',
    IP_SAME_NONE        = '已经没有重复IP！',
    BIND_RUID_EMPTY     = '请输入推荐码！',
    BIND_SUCCESS        = '绑定成功！房卡已送！',
    MOBILE_UNBIND       = '未绑定手机',
    LEFT_TIMES          = '剩余$times次',
    TURNTABLE_GLOBAL    = '恭喜玩家<j>$user<=>在幸运大转盘中获得<j>$prize x$num<=>，一起来参与吧~',
    TURNTABLE_DRAW_INFO = '姓名：<g>$name<|><=>电话：<g>$phone<|><=>地址：<g>$address',
    TURNTABLE_INPUT_HINT= '请先输入信息！',
    TURNTABLE_SUCCESS   = '领取成功！',
    TURNTABLE_VIEW      = '查看',
    INVITE_RENSHU       = '$num人',
    INVITE_FANGKA       = '房卡x$num',
    INVITE_TOTAL_RENSHU = '累计邀请人数：$num',
    INVITE_TOTAL_FANGKA = '累计获得房卡：$num',
    INVITE_DRAW_SUCCESS = '领取成功！',
    MALL_TAB_FANGKA     = '房卡商城',
    MALL_TAB_GOLD       = '金币商城',
    MALL_TAB_GIFT       = '礼包商城',
    MALL_TAB_QUAN       = '兑换商城',
    MALL_TAB_GUESS      = '竞猜币兑换',
    MALL_PRICE_1        = '￥$price',
    MALL_PRICE_2        = '<*1>$price',
    MALL_PRICE_3        = '<*2>$price',
    MALL_PRICE_4        = '$price张房卡',
    MALL_PRICE_5        = '<*3>$price',
    MALL_LEFT           = '剩余\n$num',
    MALL_BUY_SUCCESS    = '购买成功！请查看您的背包或帐号信息！',
    SHARE_APP_NAME      = '《$name》',
    ROOM_RESULT_COPIED  = '总成绩已复制，请前去粘帖！',
    ROOM_OWNER          = '房主',
    ROOM_SCORE          = '【$score】',

    GOLD_ENTER_LIMIT    = '$num准入',

    VOICE_FAILURE       = '访问麦克风失败或录音异常,请检查录音权限!',
    VOICE_PERMISSION    = '当前未获得麦克风权限,无法进行语音聊天!',
    VOICE_INTERVAL      = '录音间隔太短, 请稍后',
    VOICE_TIME_LEFT     = '剩余:$seconds秒',
    VOICE_TOO_SHORT     = '录音的时间过短，请重试',
    VOICE_CANCEL        = '取消录音',

    WARNING_IP_SAME     = 'IP相同', 
    WARNING_LOCATION_FAIL = '位置获取失败',
    WARNING_LOCATION_DIS = '距离过近',

    SHORTCUT_WORDS_1    = '快点吧，等到花儿都谢了。',
    SHORTCUT_WORDS_2    = '大家好，很高兴见到各位。',
    SHORTCUT_WORDS_3    = '又断线了，网络怎么这么差啊。',
    SHORTCUT_WORDS_4    = '和你合作真是太愉快了。',
    SHORTCUT_WORDS_5    = '我们交个朋友吧，能告诉我你的联系方式么？',
    SHORTCUT_WORDS_6    = '不要吵了，不要吵了，专心玩游戏吧。',
    SHORTCUT_WORDS_7    = '不要走，决战到天亮。',
    SHORTCUT_WORDS_8    = '各位真是不好意思，我要走了。',
    SHORTCUT_WORDS_9    = '再见了，我会想念大家的。',

    WRONG_1             = '配置异常',
    MISSION_PERCENT     = '当前进度($jindu)',
    MISSION_DAY         = '第$day天',
    MISSION_MONEY       = '$money元',

    ITEM_TYPE_1         = '道具',
    ITEM_TYPE_2         = '门票',
    ITEM_TYPE_3         = '皮肤',
    ITEM_TYPE_4         = '兑换',
    ITEM_TYPE_USE       = '使用',
    ITEM_TYPE_VIEW      = '查看',
    ITEM_OVER_DUE       = '过期',
    ITEM_HAS_NUM        = '拥有此道具$num件',
    ITEM_OVER_DUE_LEFT  = '过期时间$time',
    ITEM_POST_HINT      = '请先输入信息！',
    ITEM_USE_CONFIRM    = '确定要使用吗？',
    ITEM_USE_SUCC       = '使用成功',
    ITEM_POST_SUCC      = '保存成功，等待客户联系发件！',

    WCG_RULE_TITLE      = '活动规则',
    WCG_RULE            = '<g>下注类型：<=>下注仅支持竞猜币投注，投注奖励也为相应的竞猜币。<|><g>下注限制：<=>每个投注项1万竞猜币起投，单场比赛下注金额上限为1000万竞猜币。<|><g>下注时间：<=>比赛信息开启后即可下注，下注时间截止比赛开始。<|><g>竞猜币兑换：<=>1张房卡=1万竞猜币，活动结束后，竞猜币将自动兑换成房卡，不足1万的部分以1万计算。<|><g>奖励发放时间：<=>奖励发放截止比赛结束后的中午12点，例如14日22:00和15日2:00的比赛，奖励将会在15日中午12点前发放。',
    WCG_WIN_NAME        = '$name胜',
    WCG_PING            = '平局',
    WCG_UNOPEN          = '待开奖',
    WCG_WIN             = '猜中',
    WCG_LOSE            = '未猜中',
    WCG_BET_CAN         = '可得竞猜币',
    WCG_BET_GOT         = '赢得竞猜币',
    WCG_START_TIME      = '比赛时间\n$month-$day $hour:$minute',
    WCG_START_TIME2     = '$year年$month月$day日 $hour:$minute',
    WCG_HAS_BET         = '已投注：$bet',
    WCG_BET_FIRST       = '请先加注！',
    WCG_BET_CONFIRM     = '确定投注<r>$name<g>$num竞猜币<=>吗？',
    WCG_BET_SUCC        = '投注成功！',
    WCG_COIN_NOT_ENOUGH = '竞猜币不足！',
    WCG_BET_BTN_TEXT    = '$num万',

    ORB_OVER            = '您的参与次数已达上限!',
    ORB_DRAW_SUCC       = '提现成功，请前往公众号领取！',
    ORB_RULE_ACTIVITY   = '对于恶意邀请用户，我方有权利进行禁止，活动最终解释权归$app_name所有',
    ORB_RULE_DRAW       = '活动最终解释权归$app_name所有',
    ORB_SUMMARY_OFFSET  = '还差$amount元可提现',
    ORB_SHARE_TIP       = '还差$amount元提现，发给好友继续拆红包吧~',
    ORB_SHARE_TIP_DONE  = '发给好友继续拆更多红包吧~',
    ORB_AMOUNT          = '$amount元',
    ORB_STATUS_HAS_OPEN = '已拆',
    ORB_STATUS_NOT_GAME = '未游戏',
    ORB_STATUS_HAS_GAME = '已游戏',
    ORB_METHOD_OWN      = '首次拆红包',
    ORB_METHOD_SHARE    = '二次分享',
    ORB_METHOD_REG      = '已登录',
    ORB_METHOD_PLAY     = '已游戏',
    ORB_DRAW_STATUS_4   = '提现失败',
    ORB_DRAW_STATUS_3   = '审核失败',
    ORB_DRAW_STATUS_0   = '审核中',
    ORB_DRAW_STATUS_1   = '审核通过',
    ORB_DRAW_STATUS_2   = '已领取',
    ORB_NOTICE          = '$name极速提现$amount元',
    ORB_NOTICE_MAIN     = '$name\n提现$amount元',
    ORB_SHARE_TITLE_1   = '我刚刚领取了15元红包，你领了吗？',
    ORB_SHARE_TITLE_2   = '分享给好友就能领取15元红包，分享越多领的越多~',
    ORB_SHARE_TITLE_3   = '一年一次的红包福利来了，领了还能领！',
    ORB_SHARE_TITLE_4   = '每人都能领红包，动动手指就能完成领取！',
    ORB_SHARE_TITLE_5   = '[微信红包]您有一个15元现金红包待领取，请及时查看~',
    ORB_SHARE_DESC      = '就差你了，快帮我拆一下，一起领现金',
    ORB_SHARE_SUCC_1    = '帮拆好友进行游戏后可立即提现1元',
    ORB_SHARE_SUCC_2    = '分享给最亲近的人提现成功率高达98%哦',
}



local mt = {
    __index = function(self, key)
        print('language error:', key)
        if not cs or not cs.game then return end
        return cs.game._lang_ and cs.game._lang_[key]   -- 如果不存在则访问当前游戏的语言
    end,

    __call = function(self, item)
        if type(item) ~= 'table' then
            return self[item]
        else
            local key = item[1]
            local ret = self[key]
            if not ret then return '' end

            for k, v in pairs(item) do
                if type(k) == 'string' then
                    if type(v) == 'string' then v = v:gsub('%%', '%%%%') end	-- 防止替换中出现捕获引用
                    ret = ret:gsub('$' .. k, v)
                end
            end
            return ret
        end
    end
}
setmetatable(LANG, mt)
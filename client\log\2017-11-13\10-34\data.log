[{"logtime": "2017/11/13 10:34:25", "logdata": "[string \"client/src/helper/AppHelper.lua\"]:315: attempt to call method 'child' (a nil value)\nstack traceback:\n\t[string \"client/src/main/GamePlugin.lua\"]:46: in function 'showMask'\n\t[string \"client/src/main/GamePlugin.lua\"]:129: in function 'goUpdate'\n\t[string \"client/src/main/GamePlugin.lua\"]:116: in function 'handler'\n\t[string \"client/src/system/Alert.lua\"]:128: in function 'func'\n\t[string \"client/src/helper/AppHelper.lua\"]:285: in function <[string \"client/src/helper/AppHelper.lua\"]:261>"}]
[{"logtime": "2017/12/19 16:07:12", "logdata": [{"main": 3, "kindid": 301, "len": 28, "sub": 300}, {"main": 100, "kindid": 301, "len": 0, "sub": 2}, {"main": 3, "kindid": 301, "len": 0, "sub": 301}, {"main": 3, "kindid": 301, "len": 0, "sub": 301}, {"main": 200, "kindid": 301, "len": 5, "sub": 2}]}, {"logtime": "2017/12/19 16:07:27", "logdata": "[string \"game/mahjong/src/room/GameLayer.lua\"]:307: bad argument #1 to 'max' (number expected, got nil)\nstack traceback:\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:307: in function 'onEventGameScene'\n\t[string \"client/src/system/RoomFrame.lua\"]:722: in function 'onSocketFrameEvent'\n\t[string \"client/src/system/RoomFrame.lua\"]:121: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}, {"logtime": "2017/12/19 16:07:27", "logdata": "[string \"cocos/init.lua\"]:57: attempt to call global 'buglyReportLuaException' (a nil value)\nstack traceback:\n\t[C]: in function 'max'\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:307: in function 'onEventGameScene'\n\t[string \"client/src/system/RoomFrame.lua\"]:722: in function 'onSocketFrameEvent'\n\t[string \"client/src/system/RoomFrame.lua\"]:121: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}]
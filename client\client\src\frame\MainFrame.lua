-------------------------------------------------------------------------------
--  创世版1.0
--  大厅网络操作
--  @date 2017-07-04
--  @auth woodoo
-------------------------------------------------------------------------------
local login_cmd = appdf.req(appdf.HEADER_SRC .. "CMD_LogonServer")
local game_cmd = appdf.req(appdf.HEADER_SRC .. "CMD_GameServer")
local ExternalFun = require(appdf.EXTERNAL_SRC .. "ExternalFun")
local BaseFrame = appdf.req(appdf.CLIENT_SRC.."frame.BaseFrame")


local OP_SET_PEIYIN         = 'OP_SET_PEIYIN'        -- 设置配音
local OP_SHARE_GAME         = 'OP_SHARE_GAME'        -- 分享游戏
local OP_SET_DAILY_FLAG     = 'OP_SET_DAILY_FLAG'    -- 设置每日标记


local MainFrame = class("MainFrame", BaseFrame)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function MainFrame:ctor(view, callbcak)
	MainFrame.super.ctor(self, view, callbcak)
end


-------------------------------------------------------------------------------
-- 连接结果
-------------------------------------------------------------------------------
function MainFrame:onConnectCompeleted()
	print("MainFrame:onConnectCompeleted oprateCode=" .. self.m_operate)

    if self.m_operate == OP_SET_PEIYIN then
        self:sendPeiyin()
	elseif self.m_operate == OP_SHARE_GAME then
		self:sendShareGame()
	elseif self.m_operate == OP_SET_DAILY_FLAG then
		self:sendDailyFlag()
        self:onCloseSocket()    -- 该命令可能没有返回，直接关闭
	else
		self:onCloseSocket()
		if nil ~= self._callBack then
			self._callBack(-1,"未知操作模式！")
		end		
	end
end


-------------------------------------------------------------------------------
-- 网络信息(短连接)
-------------------------------------------------------------------------------
function MainFrame:onSocketEvent(main,sub,pData)
	local bCloseSocket = true
    print('onSocketEvent:', main, sub)
    if login_cmd.MDM_GP_USER_SERVICE == main then
        if login_cmd.SUB_GP_SET_PEIYIN == sub then
            self:onSubPeiyin(pData)
        elseif login_cmd.SUB_GP_SHARE_GAME == sub then
            self:onSubShareGame(pData)
        elseif login_cmd.SUB_GP_SET_CLIENT_FLAG == sub then
            self:onSubDailyFlag(pData)
		else
			local message = string.format("未知命令码：%d-%d", main, sub)
			if nil ~= self._callBack then
				self._callBack(-1,message);
			end	
		end
    end

	if bCloseSocket then
		self:onCloseSocket()
	end
end


-------------------------------------------------------------------------------
-- 连接
-------------------------------------------------------------------------------
function MainFrame:doConnect(operate)
    self.m_operate = operate
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(-1,"建立连接失败！")
    end
end


-------------------------------------------------------------------------------
-- 设置配音
-------------------------------------------------------------------------------
function MainFrame:onSetPeiyin(peiyin)
    self.m_peiyin = peiyin
    self:doConnect(OP_SET_PEIYIN)
end


-------------------------------------------------------------------------------
-- 分享游戏
-------------------------------------------------------------------------------
function MainFrame:onShareGame(share_type, param1)
    self.m_share_type = share_type
    self.m_share_param1 = param1
    self:doConnect(OP_SHARE_GAME)
end


-------------------------------------------------------------------------------
-- 每日标记
-------------------------------------------------------------------------------
function MainFrame:onDailyFlag()
    self:doConnect(OP_SET_DAILY_FLAG)
end


-------------------------------------------------------------------------------
-- 发送设置配音
-------------------------------------------------------------------------------
function MainFrame:sendPeiyin()
    local buffer = ExternalFun.create_netdata(login_cmd.CMD_GP_SetPeiyin)
    buffer:setcmdinfo(login_cmd.MDM_GP_USER_SERVICE, login_cmd.SUB_GP_SET_PEIYIN)
    buffer:pushdword( GlobalUserItem.dwUserID )
    buffer:pushword(self.m_peiyin)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(-1, "发送设置配音失败！")
    end
end


-------------------------------------------------------------------------------
-- 发送游戏分享
-------------------------------------------------------------------------------
function MainFrame:sendShareGame()
    local buffer = ExternalFun.create_netdata(login_cmd.CMD_GP_ShareGame)
    buffer:setcmdinfo(login_cmd.MDM_GP_USER_SERVICE, login_cmd.SUB_GP_SHARE_GAME)
    buffer:pushdword( GlobalUserItem.dwUserID )
    buffer:pushdword( self.m_share_param1 or 0 )
    buffer:pushdword( 0 )
    buffer:pushbyte( self.m_share_type or 0 )
    buffer:pushdword( 0 )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(-1, "发送游戏分享失败！")
    end
end


-------------------------------------------------------------------------------
-- 发送每日标记
-------------------------------------------------------------------------------
function MainFrame:sendDailyFlag()
    local buffer = ExternalFun.create_netdata(login_cmd.CMD_GP_SetClientFlag)
    buffer:setcmdinfo(login_cmd.MDM_GP_USER_SERVICE, login_cmd.SUB_GP_SET_CLIENT_FLAG)
    buffer:pushdword( GlobalUserItem.dwUserID )
    buffer:pushscore( GlobalUserItem.lClientFlag )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(-1, "发送每日标记失败！")
    end
end


-------------------------------------------------------------------------------
-- 配音设置返回
-------------------------------------------------------------------------------
function MainFrame:onSubPeiyin( pData )
    local ret = ExternalFun.read_netdata(login_cmd.CMD_GP_SetPeiyin, pData)
    if nil ~= self._callBack then
        self._callBack(login_cmd.SUB_GP_SET_PEIYIN, nil, ret)
    end
end


-------------------------------------------------------------------------------
-- 分享游戏返回
-------------------------------------------------------------------------------
function MainFrame:onSubShareGame( pData )
    local ret = ExternalFun.read_netdata(login_cmd.CMD_GP_ShareGame, pData)
    if nil ~= self._callBack then
        self._callBack(login_cmd.SUB_GP_SHARE_GAME, nil, ret)
    end
end


-------------------------------------------------------------------------------
-- 每日标记返回
-------------------------------------------------------------------------------
function MainFrame:onSubDailyFlag( pData )
    -- do nothing
end


return MainFrame
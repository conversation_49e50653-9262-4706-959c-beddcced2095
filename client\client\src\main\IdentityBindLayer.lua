-------------------------------------------------------------------------------
--  创世版1.0
--  身份绑定
--  @date 2018-03-19
--  @auth woodoo
-------------------------------------------------------------------------------
local EditBox = cs.app.client('system.EditBox')


local IdentityBindLayer = class("IdentityBindLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function IdentityBindLayer:ctor(callback)
    print('IdentityBindLayer:ctor...')
    self:enableNodeEvents()
    self.m_callback = callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('IdentityBindLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    local edit1 = EditBox.convertTextField( main_node:child('input_name'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    local edit2 = EditBox.convertTextField( main_node:child('input_identity'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    edit1:setPlaceholderFontColor(cc.c3b(220, 220, 220))
    edit2:setPlaceholderFontColor(cc.c3b(220, 220, 220))

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_cancel'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function IdentityBindLayer:onExit()
    print('IdentityBindLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 是否有效身份证号
-------------------------------------------------------------------------------
function IdentityBindLayer:isValidIdentity(card)
    local len = 18
    if #card ~= len then return false end

    local certmap = {1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2}
    local sum = 0
    for i = len-1, 1, -1 do
        local s = 2^i % 11
        sum = sum + s * card:sub(len - i, len - i)
    end
    return tostring(certmap[sum % 11 + 1]) == card:sub(len, len)
end


-------------------------------------------------------------------------------
-- 确定按钮点击
-------------------------------------------------------------------------------
function IdentityBindLayer:onBtnOk(sender)
    local name = self.main_node:child('input_name'):getString():trim()
    if name == '' then return end

    local id_no = self.main_node:child('input_identity'):getString():trim()
    if id_no == '' then return end
    if not self:isValidIdentity(id_no) then
        helper.pop.message( LANG.INVALID_IDENTITY_NO )
        return
    end

    self.m_callback(name, id_no)
    self:removeFromParent()
end


return IdentityBindLayer

# LHMJ313 技术架构文档

## 整体架构概述

LHMJ313 项目采用分层架构设计，基于 Cocos2d-x 3.13 引擎，使用 Lua 脚本进行游戏逻辑开发。整体架构分为以下几个层次：

```
┌─────────────────────────────────────────┐
│              Lua 游戏层                  │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  游戏逻辑   │  │   UI 界面层     │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            Lua 框架层                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  MVC 框架   │  │   网络通信      │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│           Cocos2d-x 引擎层               │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  渲染引擎   │  │   Lua 绑定      │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            原生平台层                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Android    │  │   iOS/Windows   │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 核心组件架构

### 1. 应用程序入口

#### AppDelegate (C++)
- **文件位置**: `frameworks/runtime-src/Classes/AppDelegate.cpp`
- **主要职责**:
  - 初始化 Cocos2d-x 引擎
  - 配置 Lua 脚本引擎
  - 注册自定义 Lua 绑定模块
  - 设置应用程序生命周期回调

```cpp
// 关键初始化流程
bool AppDelegate::applicationDidFinishLaunching() {
    // 设置帧率
    Director::getInstance()->setAnimationInterval(1.0f / 60.0f);
    
    // 初始化 Lua 引擎
    LuaEngine* engine = LuaEngine::getInstance();
    ScriptEngineManager::getInstance()->setScriptEngine(engine);
    
    // 注册自定义模块
    register_all_packages();
    
    // 执行主 Lua 脚本
    engine->executeScriptFile("client/src/main.lua");
}
```

#### 平台特定入口
- **Android**: `frameworks/runtime-src/proj.android_mowang/src/org/cocos2dx/lua/`
- **iOS**: `frameworks/runtime-src/proj.ios_mac/ios/AppController.mm`
- **Windows**: `frameworks/runtime-src/proj.win32/main.cpp`

### 2. Lua 脚本架构

#### 主入口脚本
- **文件**: `client/client/src/main.lua`
- **功能**: 
  - 设置搜索路径
  - 初始化 Cocos2d-x Lua 绑定
  - 启动应用程序主类

```lua
-- 设置资源搜索路径
cc.FileUtils:getInstance():addSearchPath("client/src/")
cc.FileUtils:getInstance():addSearchPath("client/res/")

-- 初始化框架
require "config"
require "cocos.init"

-- 启动应用
require("client.src.app.MyApp"):create():run()
```

#### MVC 框架
- **基类**: `src/packages/mvc/AppBase.lua`
- **视图基类**: `src/packages/mvc/ViewBase.lua`
- **架构特点**:
  - 支持多视图根目录
  - 自动场景管理
  - 资源绑定机制

### 3. 游戏模块架构

#### 模块化设计
每个游戏都是独立的模块，位于 `client/game/` 目录下：

```
client/game/
├── mahjong/           # 麻将游戏
├── land/              # 斗地主
├── fourcards/         # 四副牌
├── thirteen/          # 十三水
└── ...
```

#### 游戏模块结构
```
game_module/
├── src/
│   ├── game.lua       # 游戏配置
│   ├── init.lua       # 初始化脚本
│   ├── room/          # 房间逻辑
│   └── ui/            # 游戏界面
└── res/               # 游戏资源
```

### 4. 网络通信架构

#### 网络框架层次
```
┌─────────────────────────────────────┐
│         Lua 业务逻辑层               │
├─────────────────────────────────────┤
│         Frame 框架层                │
│  ┌─────────────┐ ┌─────────────────┐│
│  │  RoomFrame  │ │   LogonFrame    ││
│  └─────────────┘ └─────────────────┘│
├─────────────────────────────────────┤
│         Socket 封装层               │
│  ┌─────────────┐ ┌─────────────────┐│
│  │ BaseFrame   │ │  ClientSocket   ││
│  └─────────────┘ └─────────────────┘│
├─────────────────────────────────────┤
│         原生网络层                  │
│  ┌─────────────┐ ┌─────────────────┐│
│  │ TCP Socket  │ │   HTTP Client   ││
│  └─────────────┘ └─────────────────┘│
└─────────────────────────────────────┘
```

#### 核心网络组件
- **BaseFrame**: 网络通信基类
- **RoomFrame**: 房间网络管理
- **LogonFrame**: 登录网络管理
- **ClientSocket**: 原生 Socket 封装

### 5. 资源管理架构

#### 资源搜索路径
```lua
-- 基础路径
cc.FileUtils:getInstance():addSearchPath("client/src/")
cc.FileUtils:getInstance():addSearchPath("client/res/")

-- 动态下载路径
cc.FileUtils:getInstance():addSearchPath(device.writablePath.."download/")
cc.FileUtils:getInstance():addSearchPath(device.writablePath.."client/res/")
```

#### 资源热更新机制
- 支持增量资源下载
- 版本控制和校验
- 自动资源清理

### 6. 自定义 Lua 绑定

#### 绑定模块
项目扩展了大量自定义 Lua 绑定：

```cpp
// 注册自定义模块
register_all_cmd_data();        // 命令数据
register_all_client_socket();   // 网络 Socket
register_all_curlasset();       // HTTP 请求
register_all_logasset();        // 日志系统
register_all_QrNode();          // 二维码
register_all_AESEncrypt();      // 加密解密
register_all_recorder();        // 音频录制
```

#### 主要扩展功能
- **网络通信**: TCP Socket、HTTP 请求
- **加密解密**: AES 加密、MD5 校验
- **多媒体**: 音频录制、图片处理
- **工具类**: 二维码生成、日志记录

## 构建系统架构

### 1. 多平台构建支持

#### CMake 构建 (Linux/Windows)
- **主配置**: `frameworks/CMakeLists.txt`
- **引擎配置**: `frameworks/cocos2d-x/CMakeLists.txt`

#### Android 构建
- **Gradle 配置**: `frameworks/runtime-src/proj.android-studio/`
- **NDK 构建**: `frameworks/runtime-src/proj.android_mowang/jni/`

#### iOS 构建
- **Xcode 项目**: `frameworks/runtime-src/proj.ios_mac/`

### 2. 打包系统

#### 自动化打包脚本
- **Python 脚本**: `info/package.py`
- **Ruby 脚本**: `frameworks/runtime-src/proj.android_mowang/build.rb`
- **Shell 脚本**: 各种平台特定脚本

#### 多渠道支持
- 支持不同渠道的配置
- 自动化签名和发布
- 版本号管理

## 性能优化架构

### 1. 内存管理
- Lua 垃圾回收优化
- 纹理缓存管理
- 对象池模式

### 2. 渲染优化
- 批量渲染
- 纹理压缩
- 动态加载

### 3. 网络优化
- 连接池管理
- 数据压缩
- 断线重连

## 扩展性设计

### 1. 插件化架构
- 游戏模块独立加载
- 热更新支持
- 配置驱动

### 2. 多版本支持
- 包配置系统
- 渠道定制
- 功能开关

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

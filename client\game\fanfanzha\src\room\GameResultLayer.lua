-------------------------------------------------------------------------------
--  创世版1.0
--  局结算(小结算)
--  @date 2017-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')


local GameResultLayer = class("GameResultLayer", function(scene)
    return helper.app.loadCSB('GameResultLayer.csb')
end)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GameResultLayer:ctor(scene, cmd_data, zhuang_chair_id, player_count)
    self._scene = scene
    self.player_count = player_count
    self:setName('GameResultLayer')
    self:child('template'):hide()
    print('self.player_count is ', self.player_count)

     -- 有些情况下会有存在旧的小结算
     local last_layer = helper.app.getFromScene('subGameResultLayer')
     if last_layer then
        last_layer:removeFromParent() 
     end
 
     self:setName('subGameResultLayer')
    
    self:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    self:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )

    self:showResult(cmd_data, zhuang_chair_id)

end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 继续游戏按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnContinue(sender)
    self:doClose()
end


-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function GameResultLayer:showResult(cmd_data, zhuang_chair_id)
    --dump(cmd_data)
    local template = self:child('template')
    local game_layer = self._scene._scene
    local size = template:size()
    local gap = 10
    local nPlayerCount = self.player_count

    local first_win_id = game_layer.wOutCardIndex[1]
    local partner_id = game_layer.wOutCardIndex[3]

    local wMeChairID = game_layer:GetMeChairID()
    local chairid_index = game_layer:getIndex(wMeChairID)
    local partner_id_index = chairid_index + 2
    if partner_id_index >= 4 then partner_id_index = partner_id_index - 4 end
    partner_id = game_layer.wOutCardIndex[partner_id_index + 1]
    print('wMeChairID, chairid_index', wMeChairID, chairid_index, partner_id_index, partner_id)
    dump(game_layer.wOutCardIndex)

    local card_type_templete = self:child('Image_4')
  

    local tableId = game_layer._gameFrame:GetTableID()
    local start_x = display.width/2 + (nPlayerCount == 3 and -(size.width + gap) or -(nPlayerCount - 1) * 0.5 * (size.width + gap))
    for i = 1, nPlayerCount do repeat
        local index = game_layer.wOutCardIndex[i]
        local userItem = game_layer._gameFrame:getTableUserItem(tableId, index)
        --local userItem = game_layer:getUserInfoByChairID(i)
        if userItem == nil then
            break
        end
        local panel = template:clone():show():addTo(self)
        panel:px(start_x):zorder(10)
        start_x = start_x + size.width + gap

        -- 头像
        local panel_avator = panel:child('head')
        head = PopupHead:create(self, userItem, 95, 95)
        head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

        -- 昵称
        panel:child('txt_name'):setString(userItem.szNickName)
        panel:child('txt_id'):setString('ID:' .. helper.str.formatUserID(userItem.dwUserID))

        --房主标志
        local is_fangzhu = userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID
        panel:child('img_fangzhu'):setVisible(is_fangzhu)

        --队友标志
        local is_friend = userItem.wChairID == partner_id
        panel:child('img_friend'):setVisible(is_friend)
        print('is_friend', is_friend, userItem.wChairID, partner_id, panel:child('img_friend') )

        --dump(userItem)
        panel:child('txt_score'):setString( cmd_data.lGameScore[1][userItem.wChairID + 1] - cmd_data.lTongScore[1][userItem.wChairID + 1] )
        panel:child('txt_rank_score'):setString( cmd_data.lTongScore[1][userItem.wChairID + 1] )
        panel:child('txt_total_score'):setString(cmd_data.lGameScore[1][userItem.wChairID + 1])

        local png_ming = "word/1ming.png"
        if i > 1 then
            png_ming = "word/king" .. tostring(i) .. "ming.png"
        end
        panel:child('img_ming'):ignoreContentAdaptWithSize(true)
        panel:child('img_ming'):texture(png_ming)

        if cmd_data.lTongScore[1][userItem.wChairID + 1] < 0 then
            panel:child('img_rank'):texture('word/word_font_last.png')
        end

        local listview = panel:child('listview')
        -- 定型炸
        if cmd_data.cbDingUserCount > 0 then
            for i = 1, cmd_data.cbDingUserCount do
                local wChairID = cmd_data.wDingBombUser[1][i]
                if wChairID == userItem.wChairID then
                    local img = card_type_templete:clone()
                    img:texture('word/word_font_ding_bomb.png')
                    listview:pushBackCustomItem(img)
                end
            end
        end

        -- 连炸
        if cmd_data.cbLinkCardNum[1][userItem.wChairID + 1] > 0 then
            local img = card_type_templete:clone()
            img:texture('word/word_font_bomb_' .. tostring(cmd_data.cbLinkCardNum[1][userItem.wChairID + 1]) .. '.png')
            listview:pushBackCustomItem(img)
        end

        -- 同型炸
        if cmd_data.cbIsSameBomb[1][userItem.wChairID + 1] > 0 then
            local img = card_type_templete:clone()
            img:texture('word/word_font_bomb_same_color.png')
            listview:pushBackCustomItem(img)
        end

        local scrollview = panel:child('scrollview')
        self:showBombCard(scrollview, userItem.wChairID)

        if game_layer:GetMeChairID() == userItem.wChairID then
            local bIsWin = false
            if cmd_data.lGameScore[1][userItem.wChairID + 1] >= 0 then
                bIsWin = true
            end

            panel:child("dujiang"):show()

            local title = self:child('title')
            title:ignoreContentAdaptWithSize(true)
            if not bIsWin then
                title:texture('word/shibai.png')
            end
        end
    until true
    end
    self:hide()
    --self:perform(function () self:show() end, 2, 1)
end

function GameResultLayer:showBombCard(scrollview, wChairID)
    --local index = self._scene:getIndex(wChairID)

    local game_layer = self._scene._scene
    local card_templete = self:child('card_templete')
    local txt_score_templete = self:child('txt_score_templete')
    
    local cbBombNum = game_layer.cbBombNum[wChairID + 1]
	local cbBombCardNum = game_layer.cbBombCardNum[wChairID + 1]
	local cbBombCard = game_layer.cbBombCard[wChairID + 1]
    local size = scrollview:size()
    local cardSize = card_templete:size()
    local height = cbBombNum * (cardSize.height + 5)
    if height > size.height then
        size.height = height
    end    
    local view_size = size
	local start_pos = cc.p(0, size.height - cardSize.height / 2 - 5)
    local start_index = 0

    local width = 0
    for i = 1, cbBombNum do

	    local x = cardSize.width / 2 
	    for j = 1, cbBombCardNum[i] do
	    	local cardColor = GameLogic:getCardColor(cbBombCard[j + start_index])
	    	local cardValue = GameLogic:getCardValue(cbBombCard[j + start_index])
	    	local card = card_templete:clone()
	    	card:show()
	    	local png = string.format('smallcard/card%d%02d.png', cardColor, cardValue)
            card:texture(png)
            scrollview:addChild(card)
	    	card:pos(cardSize.width / 2 + (j - 1) * (10 + cardSize.width / 2), start_pos.y)
	    	x = cardSize.width / 2 + (j - 1) * (10 + cardSize.width / 2)
        end
        
        local txt = txt_score_templete:clone()
        txt:setString('+' .. tostring(game_layer.lBombCardScore[wChairID + 1][i]))
        txt:pos(txt:size().width / 2 + x + cardSize.width / 2 + 10, start_pos.y)
        txt:addTo(scrollview)

        local tmp_width = txt:size().width + x + cardSize.width / 2 + 20
        if width < tmp_width then
            width = tmp_width
        end
        start_index = start_index + cbBombCardNum[i]
        start_pos.y = start_pos.y - cardSize.height + 5
    end

    size.width = width
    --scrollview:setContentSize(size)
    --scrollview:setViewSize(view_size)
        
end


-------------------------------------------------------------------------------
-- 关闭界面
-------------------------------------------------------------------------------
function GameResultLayer:doClose()
    if yl.IS_REPLAY_MODEL then
       helper.app.getFromScene('game_room_layer'):onExitRoom()
    else
       local is_room_ended = PassRoom:getInstance().m_bRoomEnd
       print('is_room_ended is ', is_room_ended)
       if not is_room_ended then
            --self._scene.btStart:setVisible(true)
            self._scene:onButtonClickedEvent('btn_start')
       else
            local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
            if room_result_layer then
                room_result_layer:show()
            else
                GlobalUserItem.bWaitQuit = false
                local game_layer = self._scene._scene
                if game_layer then
                    game_layer:onExitRoom()
                end
            end
       end
    end
    self:removeFromParent()
end


return GameResultLayer
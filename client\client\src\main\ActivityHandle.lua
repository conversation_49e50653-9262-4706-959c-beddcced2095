-------------------------------------------------------------------------------
--  创世版1.0
--  活动通用处理类
--  @date 2017-07-26
--  @auth woodoo
-------------------------------------------------------------------------------
local ActivityHandle = class("ActivityHandle", cc.Node)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ActivityHandle:ctor(activity_data, size)
    self:enableNodeEvents()
    self.m_activity_data = activity_data

    self:size(size)
    self:createContent()
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function ActivityHandle:onEnterTransitionFinish()
    print('ActivityHandle:onEnterTransitionFinish...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ActivityHandle:onExit()
    print('ActivityHandle:onExit...')
end


-------------------------------------------------------------------------------
-- 活动列表生成
-------------------------------------------------------------------------------
function ActivityHandle:createContent()

    -- 类型载入UI
    --[[
    local main_node = helper.app.loadCSB('ActivityHandle.csb')
    self.main_node = main_node
    self:addChild(main_node)
    --]]
    local label = ccui.Text:create('脚本型活动', cs.app.FONT_NAME, 36)
    label:setTextColor( cc.c3b(0, 0, 0) )
    label:pos(self:size().width/2, self:size().height/2):addTo(self)
end


return ActivityHandle
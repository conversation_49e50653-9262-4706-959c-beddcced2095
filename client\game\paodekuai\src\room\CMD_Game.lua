local cmd =  {}

--游戏版本
cmd.VERSION 					= appdf.VersionValue(6,7,0,1)
--游戏标识
cmd.KIND_ID						= 101
	
--游戏人数
cmd.GAME_PLAYER					= 4

--每个人的起始手牌
cmd.GAME_CARD_NUM               = 55

cmd.MAX_COUNT					= 16

cmd.CHANGE_CHAIRID_NUM			= 2

--每个人最大的出牌数目
cmd.GAME_MAX_OUT_CARD			= 16

--视图位置
cmd.MY_VIEWID					= 3

--******************         游戏状态             ************--
--等待开始
cmd.GS_TK_FREE					= 0
--游戏进行
cmd.GS_TK_PLAYING				= 101

--*********************      服务器命令结构       ************--
--游戏开始
cmd.SUB_S_GAME_START			= 100
--用户强退
cmd.SUB_S_PLAYER_EXIT			= 102
--游戏结束
cmd.SUB_S_GAME_END				= 105
--用户摊牌
cmd.SUB_S_OUT_CARD				= 103
--用户摊牌
cmd.SUB_S_PASS_CARD				= 104
cmd.SUB_S_TRUSTEE               = 108
--用户叫庄
--cmd.SUB_S_CALL_BANKER			= 106
cmd.SUB_S_READY_STATE			= 113
cmd.SUB_S_RECORD				= 111								--房卡结算记录

--**********************    客户端命令结构        ************--
--用户叫庄
--cmd.SUB_C_CALL_BANKER			= 1
--用户加注
--cmd.SUB_C_ADD_SCORE				= 2
--用户摊牌
cmd.SUB_C_OUT_CARD				= 2
--用户摊牌
cmd.SUB_C_PASS_CARD				= 3
--用户摊牌
cmd.SUB_C_TRUSTEE				= 4

--更新库存
cmd.SUB_C_STORAGE				= 6
--设置上限
cmd.SUB_C_STORAGEMAXMUL			= 7
--请求查询用户
cmd.SUB_C_REQUEST_QUERY_USER	= 8
--用户控制
cmd.SUB_C_USER_CONTROL			= 9

--********************       定时器标识         ***************--
--无效定时器
cmd.IDI_NULLITY					= 200
--开始定时器
cmd.IDI_START_GAME				= 201
--叫庄定时器
cmd.IDI_CALL_BANKER				= 202
--加注定时器
cmd.IDI_TIME_USER_ADD_SCORE		= 1
--摊牌定时器
cmd.IDI_TIME_OUT_CARD			= 2

--*******************        时间标识         *****************--
--叫庄定时器
cmd.TIME_USER_CALL_BANKER		= 10
--开始定时器
cmd.TIME_USER_START_GAME		= 10
--加注定时器
cmd.TIME_USER_ADD_SCORE			= 10
--摊牌定时器
cmd.TIME_USER_OPEN_CARD			= 10

--******************         游戏状态             ************--
--空闲开始
cmd.GAME_SCENE_FREE				= 0
--叫庄状态
cmd.GAME_SCENE_PLAY				= 100
--等待状态
cmd.GAME_SCENE_WAITING			= 200

--空闲状态
cmd.CMD_S_StatusFree = 
{
	--基础积分
	{k = "lCellScore", t = "score"},								--基础积分
	--时间信息
 	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
 	{k = "cbTimeStartGame", t = "byte"},						--操作时间
	{k = "cbTimeHeadOutCard", t = "byte"},						--开始时间
	
}

--游戏状态
cmd.CMD_S_StatusPlay = 
{
	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
	{k = "cbTimeStartGame", t = "byte"},						--操作时间
	{k = "cbTimeHeadOutCard", t = "byte"},						--开始时间
	
	{k = "lCellScore", t = "score"},								--基础积分
	{k = "cbBombCount", t = "byte"},

	{k = "wBankerUser", t = "word"},
	{k = "wCurrentUser", t = "word"},
	{k = "wChairID", t = "word"},

	{k = "wTurnWiner", t = "word"},
	{k = "cbTurnCardCount", t = "byte"},
	{k = "cbTurnCardData", t = "byte", l = {cmd.MAX_COUNT}},

	{k = "cbHandCardData", t = "byte", l = {cmd.MAX_COUNT}},
	{k = "cbHandCardCount", t = "byte", l = {cmd.GAME_PLAYER - 1}},
	{k = "cbUserTrustee", t = "byte", l = {cmd.GAME_PLAYER - 1}},
}

cmd.CMD_S_GameStart = 
{
	{k = 'wStartUser', t = 'word'},
	{k = 'wCurrentUser', t = 'word'},
	{k = 'cbCardData', t = 'byte', l = {cmd.MAX_COUNT}},
}

cmd.CMD_S_SendCard = 
{
	{k = "cbHandCardData", t = "byte", l = {cmd.GAME_CARD_NUM}},
	{k = "cbCurrentUser", t = "byte"},
	{k = "cbFirstUser", t = "byte"},
}

cmd.CMD_C_OutCard = 
{
	{k = "cbCardCount", t = "byte"},	
	{k = "cbCardData", t = "byte", l = {cmd.MAX_COUNT}},
}

cmd.CMD_C_Pass = 
{
	{k = "cbOutIndex", t = "byte"},
}

cmd.CMD_C_TRUSTEE = 
{
	{k = "bTrustee", t = "byte"},
}

cmd.CMD_S_OutCard = 
{
	{k = "cbCardCount", t = "byte"},
	{k = "wCurrentUser", t = "word"},
	{k = "wOutCardUser", t = "word"},
	{k = "cbCardData", t = "byte", l = {cmd.MAX_COUNT}},
	{k = "bIsSingleCard", t = "bool"},
	{k = "cbLeftCardNum", t = "byte"},
}

cmd.CMD_S_PassCard = 
{
	{k = "cbTurnOver", t = "byte"},
	{k = "wCurrentUser", t = "word"},
	{k = "wPassCardUser", t = "word"},
}



cmd.CMD_S_GameEnd = 
{
	{k = "lCellScore", t = "score"},
	{k = "lGameScore", t = "score", l = {cmd.GAME_PLAYER - 1}},
	{k = "wBaoChairID", t = "word"},
	{k = "wWinChairID", t = "word"},
	{k = "cbBombCount", t = "byte"},
	{k = "cbEachBombCount", t = "byte", l = {cmd.GAME_PLAYER - 1}},
	{k = "cbCardCount", t = "byte", l = {cmd.GAME_PLAYER - 1}},
	{k = "cbCardData", t = "byte", l = {cmd.MAX_COUNT, cmd.MAX_COUNT, cmd.MAX_COUNT}},	
}

cmd.CMD_S_TRUSTEE = 
{
	{k = "wTrusteeUser", t = "word"},
	{k = "bTrustee", t = "byte"},
}

return cmd
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 分享成功
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbShareSuccess = class('OrbShareSuccess', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbShareSuccess:ctor(panel)
    print('OrbShareSuccess:ctor...')
    self.super.ctor(self, panel)

end


-------------------------------------------------------------------------------
-- 显示事件
-------------------------------------------------------------------------------
function OrbShareSuccess:onShow()
    local str = LANG['ORB_SHARE_SUCC_' .. math.random(1, 2)]
    self.m_panel:child('label_content'):setString(str)
end


return OrbShareSuccess
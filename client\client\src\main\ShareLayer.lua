-------------------------------------------------------------------------------
--  创世版1.0
--  分享
--  @date 2017-06-08
--  @auth woodoo
-------------------------------------------------------------------------------
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")


local ShareLayer = class("ShareLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ShareLayer:ctor(url, title, desc, pic_path, title_font, show_note, share_tag, share_target, callfunc )
    print('ShareLayer:ctor...')
    helper.app.removeFromScene('share_layer')   -- 签到分享是隐藏窗口的，此处防止分享过程意外导致的残留
    self:setName('share_layer')
    self:zorder(2)
    self:enableNodeEvents()
    self.m_param = {url=url, title=title, desc=desc, pic=pic_path, tag=share_tag, target=share_target}
    self.callfunc = callfunc
    -- 载入主UI
    local main_node = helper.app.loadCSB('ShareLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    if title_font then
        main_node:child('sp_title'):texture(title_font)
    end

    --main_node:child('label_note'):setRich( LANG{'SHARE_FANGKA', num=cs.game.SHARE_FANGKA} )
    local note = LANG.SHARE_FANGKA
    if type(show_note) == 'string' then -- 可以由外部传入
        note = show_note
        show_note = true
    end
    main_node:child('label_note'):setRich(note)
    main_node:child('label_note'):setVisible(show_note and true or false)

    -- 按钮
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_hy'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnHY) )
    main_node:child('btn_pyq'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnPYQ) )
    main_node:child('btn_copy'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCopy) )

    if not self.m_param.url then
        main_node:child('btn_copy'):hide()._always_hide = true
    end

    if main_node:child('btn_mowang') then
        if cs.app.IS_MOWANG_SHARE then
            main_node:child('btn_mowang'):show()
            main_node:child('btn_mowang'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnMoWang) )
        else
            main_node:child('btn_mowang'):hide()
            main_node:child('btn_mowang')._always_hide = true
        end
    end

    if main_node:child('btn_xianliao') then
        if cs.app.IS_XIANLIAO_SHARE then
            main_node:child('btn_xianliao'):show()
            main_node:child('btn_xianliao'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnXianLiao) )
        else
            main_node:child('btn_xianliao'):hide()
            main_node:child('btn_xianliao')._always_hide = true
        end
    end
    self:orderButtons()
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function ShareLayer:onEnterTransitionFinish()
    print('ShareLayer:onEnterTransitionFinish...')

    if self.m_param.tag then
        local callback = function(code, msg, result)
            if code < 0 then
                helper.pop.message(ms)
            else
                self:onSubShareGameResp(result)
            end
        end
	    self.m_frame = helper.app.createFrame(self, callback)

        --[[包中的分享图现在不加密了
        if device.platform ~= 'windows' then
		    -- 包內分享图因为是加密的(android/ios无法处理），需要在包外存在，不存在的话先从包中复制一份到外部
            copyImageOutPackage(self.m_param.pic, 'client/res/')
        end
        --]]
    end
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ShareLayer:onExit()
    print('ShareLayer:onExit...')
    helper.app.removeFrame(self.m_frame)
end


-------------------------------------------------------------------------------
-- 额外参数
-------------------------------------------------------------------------------
function ShareLayer:setParam(param1, param2, param_copy)
    self.m_param1 = param1
    self.m_param2 = param2
    self.m_param_copy = param_copy
    return self
end


-------------------------------------------------------------------------------
-- 开放按钮
-------------------------------------------------------------------------------
function ShareLayer:showButtons(str)
    local all_names = {'btn_hy', 'btn_pyq', 'btn_mowang', 'btn_xianliao', 'btn_copy'}
    local show_all = str == ''
    local shows = {}
    for i, name in ipairs(str:split(',')) do
        shows['btn_' .. name:trim()] = true
    end
    for i, name in ipairs(all_names) do
        local btn = self.main_node:child(name)
        btn:setVisible( (not btn._always_hide) and (show_all or shows[name]) )
    end

    if self.m_param_copy and self.m_param_copy.icon then
        self.main_node:child('btn_copy')._always_hide = false
        self.main_node:child('btn_copy'):show():texture(self.m_param_copy.icon)
    end

    self:orderButtons()
    return self
end


-------------------------------------------------------------------------------
-- 重新排序按钮
-------------------------------------------------------------------------------
function ShareLayer:orderButtons()
    local btn_hy, btn_pyq, btn_mowang, btn_xianliao, btn_copy = self.main_node:child('btn_hy,btn_pyq,btn_mowang,btn_xianliao,btn_copy')
    local visible_count = 0
    for i, btn in ipairs{btn_hy, btn_pyq, btn_mowang, btn_xianliao, btn_copy} do
        if btn:isVisible() then visible_count = visible_count + 1 end
    end
    local cx = self.main_node:size().width / 2
    local gap = visible_count == 4 and 116 or (visible_count == 3 and 140 or 200)
    local start_x = cx - gap * (visible_count - 1) / 2
    for i, btn in ipairs{btn_hy, btn_pyq, btn_mowang, btn_xianliao, btn_copy} do
        if btn:isVisible() then
            btn:px(start_x)
            start_x = start_x + gap
        end
    end
end


-------------------------------------------------------------------------------
-- 分享后发送服务器返回
-------------------------------------------------------------------------------
function ShareLayer:onSubShareGameResp(resp)
    if resp.dwRewardType > 0 then
        if resp.dwRewardType == 2 then
            helper.pop.message(LANG{'SHARE_GET_FANGKA', num=resp.dwAddInsureScore})
            PassRoom:getInstance():getPlazaScene():updateFangka( resp.dwCurInsureScore )
            
        elseif resp.dwRewardType == 1 then
            helper.pop.message(LANG{'SHARE_GET_GOLD', num=resp.dwAddInsureScore})
            PassRoom:getInstance():getPlazaScene():updateFangka( nil, resp.dwCurInsureScore )

        elseif resp.dwRewardType == 3 then
            helper.pop.message(LANG{'SHARE_GET_QUAN', num=resp.dwAddInsureScore})
            PassRoom:getInstance():getPlazaScene():updateFangka( nil, nil, resp.dwCurInsureScore )
        end
    end

    if resp.cbType == 0 then
        if resp.dwAddInsureScore <= 0 then
            helper.pop.message(LANG.SHARE_NO_FANGKA)
        end

    elseif resp.cbType == 1 or resp.cbType == 4 then    -- 金币或签到分享
        if resp.dwAddInsureScore <= 0 then
            helper.pop.message(LANG.SHARE_NO_GOLD)
        end

        --完成签到分享
        if resp.cbType == 4 and resp.nDays > 0 then
            GlobalUserItem.nSignDays = resp.nDays
        end

    --完成红包任务
    elseif resp.cbType == 2 then
    
    --完成红包现金分享
    elseif resp.cbType == 3 then

    end
    --print('分享', resp.cbType, self.callfunc)
    if self.callfunc then
        self.callfunc( resp )
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 分享
-------------------------------------------------------------------------------
function ShareLayer:doShare(share_type)
    local callback = function(isok)
        if isok == 'true' then
            self:perform(function()
                helper.pop.message( LANG.SHARE_SUCCESS )
                if not self.m_param.tag then
                    self:removeFromParent()
                end
            end, 0.5)

            if self.m_param.tag == 'share_game' then
                self.m_frame:onShareGame(0)
            elseif self.m_param.tag == 'share_gold' then
                self.m_frame:onShareGame(1)
            elseif self.m_param.tag == 'share_mission' then
                self.m_frame:onShareGame(2)
            elseif self.m_param.tag == 'share_money' then
                self.m_frame:onShareGame(3)
            elseif self.m_param.tag == 'share_sign_in' then
                self.m_frame:onShareGame(4)
            elseif self.m_param.tag == 'redarena_lose' then
                self.m_frame:onShareGame(5, self.m_param1)
            elseif self.m_param.tag == 'redarena_succ' then
                self.m_frame:onShareGame(6, self.m_param1)
            elseif self.m_param.tag == 'redarena_task' then
                self.m_frame:onShareGame(7, self.m_param1)
            elseif self.m_param.tag == 'redarena_task_circle' then
                self.m_frame:onShareGame(8, self.m_param1)
            else    --if self.m_param.tag == 'share_arena_apply' then
                if self.callfunc then
                    self.callfunc()
                end
            end
        else
            self:removeFromParent()
        end
    end

    if device.platform == 'windows' then
        callback('true')
        return
    end

    local param = self.m_param
    if param.pic then
        local full_path = cc.FileUtils:getInstance():fullPathForFilename(param.pic)
        MultiPlatform:getInstance():shareToTarget(share_type, callback, param.title,  param.desc, param.url, full_path, "true")

    elseif param.url then
        MultiPlatform:getInstance():shareToTarget(share_type, callback, param.title,  param.desc, param.url, "")

    else
        -- 截图分享
        -- self:removeFromParent() -- 注意截屏要先关闭自己，后续的代码不能出现self
        self:hide()
        local framesize = cc.Director:getInstance():getOpenGLView():getFrameSize()
        local area = cc.rect(0, 0, framesize.width, framesize.height)
        captureScreenWithArea(area, 'screenshot.jpg', function(ok, save_path)
            if not ok then return end
            MultiPlatform:getInstance():shareToTarget(share_type, callback, param.title, param.desc, param.url, save_path, "true")
        end)
    end
end


-------------------------------------------------------------------------------
-- 好友按钮点击
-------------------------------------------------------------------------------
function ShareLayer:onBtnHY(sender)
    self:doShare(yl.ThirdParty.WECHAT)
end


-------------------------------------------------------------------------------
-- 好友按钮点击
-------------------------------------------------------------------------------
function ShareLayer:onBtnPYQ(sender)
    self:doShare(yl.ThirdParty.WECHAT_CIRCLE)
end


-------------------------------------------------------------------------------
-- 默往按钮点击
-------------------------------------------------------------------------------
function ShareLayer:onBtnMoWang(sender)
    self:doShare(yl.ThirdParty.MOWANG)
end


-------------------------------------------------------------------------------
-- 闲聊按钮点击
-------------------------------------------------------------------------------
function ShareLayer:onBtnXianLiao(sender)
    self:doShare(yl.ThirdParty.XIANLIAO)
end


-------------------------------------------------------------------------------
-- 复制按钮点击
-------------------------------------------------------------------------------
function ShareLayer:onBtnCopy(sender)
    if self.m_param_copy then
        MultiPlatform:getInstance():copyToClipboard(self.m_param_copy.content)
        helper.pop.message( self.m_param_copy.toast )
    else
        local str = LANG.CLICK_JOIN:gsub('%[', '%%['):gsub('%]', '%%]')  -- 把'['和']'转移成普通字符
        local desc = self.m_param.desc:gsub(str, '')
        MultiPlatform:getInstance():copyToClipboard(self.m_param.title .. desc)
        helper.pop.message( LANG.SHARE_COPIED )
    end
    self:removeFromParent()
end


return ShareLayer
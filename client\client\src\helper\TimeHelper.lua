-------------------------------------------------------------------------------
--  创世版1.0
--  时间辅助方法类
--      访问方式：helper.time.
--  @date 2017-06-06
--  @auth woodoo
-------------------------------------------------------------------------------
local TimeHelper = {}
helper = helper or {}
helper.time = TimeHelper


-------------------------------------------------------------------------------
-- 通用格式化：2017-06-07 12:33:45
-------------------------------------------------------------------------------
function TimeHelper.format(time, fmt)
    return os.date(fmt or "%Y-%m-%d %H:%M:%S", time or os.time())
end


--------------------------------------------------------------------------------------
-- 时间戳转换成倒计时: 02:12:59
--------------------------------------------------------------------------------------
function TimeHelper.intToCountDown(time, has_day)
	local d = math.floor(time / 86400)
	local h = has_day and math.floor((time - d * 86400) / 3600) or math.floor(time / 3600)
	local m = math.floor((time - (math.floor((time - d * 86400) / 3600) ) * 3600 - d * 86400) / 60)
	local s = time % 60
	if time < 0 then
		d = 0
		h = 0
		m = 0
		s = 0
	end
	return (d < 1 or not has_day) and string.format('%02d:%02d:%02d', h, m, s) or 
		string.format('%d%s%02d:%02d:%02d', d, LANG.DAY, h, m, s)
end


-------------------------------------------------------------------------------
-- SYSTEMTIME结构转日期时间
-------------------------------------------------------------------------------
function TimeHelper.convertSysTime(sys_time, has_time, has_second)
    local ret = string.format('%d-%02d-%02d', sys_time.wYear, sys_time.wMonth, sys_time.wDay)
    if has_time then
        ret = ret .. string.format(' %02d:%02d', sys_time.wHour, sys_time.wMinute)
        if has_second then
            ret = ret .. string.format(':%02d', sys_time.wSecond)
        end
    end
    return ret
end

-------------------------------------------------------------------------------
--  创世版1.0
--  局结算(小结算)
--  @date 2017-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')


local GameResultLayer = class("GameResultLayer", function(scene)
    return helper.app.loadCSB('GameResultLayer.csb')
end)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GameResultLayer:ctor(scene, cmd_data, zhuang_chair_id, player_count)
    self._scene = scene
    self.player_count = player_count
    self:setName('GameResultLayer')
   
    print('self.player_count is ', self.player_count)
    
    --self:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    self:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )
    self:child('btnExit'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnExit) )

    self:showResult(cmd_data, zhuang_chair_id)

end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 继续游戏按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnContinue(sender)
    self:doClose()
end


-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function GameResultLayer:showResult(cmd_data, zhuang_chair_id)
    --dump(cmd_data)
    local bg = self:child("bg")
    local game_layer = self._scene._scene

    local nPlayerCount = self.player_count

    bg:child('txt_difen'):setString(tostring(cmd_data.lCellScore))

    

    --local start_x = display.width/2 + (nPlayerCount == 3 and -(size.width + gap) or -(nPlayerCount - 1) * 0.5 * (size.width + gap))
    for i = 1, nPlayerCount do repeat
        
        local userItem = game_layer:getUserInfoByChairID(i - 1)
        if userItem == nil then
            break
        end
        local panel = bg:child('panel' .. tostring(i))
       

        -- 昵称
        local szNickName = helper.str.getLenCharsFromString(userItem.szNickName, 10, '...')
        panel:child('txt_name'):setString(szNickName)
       -- panel:child('txt_id'):setString('ID:' .. helper.str.formatUserID(userItem.dwUserID))

        --dump(userItem)
        panel:child('txt_score'):setString( cmd_data.lGameScore[1][userItem.wChairID + 1] )
       
        panel:child('txt_bomb'):setString(cmd_data.cbEachBombCount[1][userItem.wChairID + 1])

        local win_num = cmd_data.cbCardCount[1][userItem.wChairID + 1]
        local show_num = win_num
        if show_num > 0 then show_num = -show_num end
        
        if show_num == 0 then 
            for j = 1, nPlayerCount do repeat
                if j == i then break end
                show_num = show_num + cmd_data.cbCardCount[1][j]
            until true
            end
        end
        panel:child('txt_win_card'):setString(tostring(show_num))

        local img_type = panel:child('img_type')
        img_type:show()
        print('win_num is ', win_num, cmd_data.wBaoChairID, userItem.wChairID)
        if win_num == cmd.MAX_COUNT then
            img_type:texture('word/word_font_close_door.png')
        elseif cmd_data.wBaoChairID == userItem.wChairID then
            img_type:texture('word/word_font_bao.png')
        --[[
        elseif cmd_data.cbEachBombCount[1][userItem.wChairID + 1] > 0 then
            img_type:texture('word/word_font_bomb' .. tostring(cmd_data.cbEachBombCount[1][userItem.wChairID + 1]) .. '.png')
        --]]
        else 
            img_type:hide()
        end

       -- panel:child('txt_average_score'):setString('-'.. tostring(cmd_data.lAverageScore))
        

        --[[
        if game_layer:GetMeChairID() + 1 == i then
            local color = cc.c3b(0xFE, 0x0E, 0x0D)
            panel:child('txt_score'):setTextColor(color)
            panel:child('txt_gong_score'):setTextColor(color)
            panel:child('txt_order'):setTextColor(color)
            panel:child('txt_card_score'):setTextColor(color)
            panel:child('txt_average_score'):setTextColor(color)
            print('game_layer:GetMeChairID() is ', game_layer:GetMeChairID() )
        end
--]]

        if game_layer:GetMeChairID() == userItem.wChairID then
            local bIsWin = false
            if cmd_data.lGameScore[1][userItem.wChairID + 1] >= 0 then
                bIsWin = true
            end

            if bIsWin then
                bg:texture('room/result_win_bg.png')
            end
        end
    until true
    end
    self:hide()
    self:perform(function () self:show() end, 2, 1)
end

function GameResultLayer:onBtnExit()
    -- body
    if self._scene and self._scene._scene and self._scene._scene.onQueryExitGame then
        self._scene._scene:onQueryExitGame()
        if self._scene.btStart then
            self._scene.btStart:show()
        end
    end
   self:removeFromParent()
end

-------------------------------------------------------------------------------
-- 关闭界面
-------------------------------------------------------------------------------
function GameResultLayer:doClose()

    if self._scene and self._scene.onButtonClickedEvent then
        self._scene:onButtonClickedEvent('btn_start')
    else
        PassRoom:getInstance():onLoginRoom(GlobalUserItem.dwCurServerID)
    end
    
    self:removeFromParent()
--[[
    if yl.IS_REPLAY_MODEL then
       helper.app.getFromScene('game_room_layer'):onExitRoom()
    else
       local is_room_ended = PassRoom:getInstance().m_bRoomEnd
       print('is_room_ended is ', is_room_ended)
       if not is_room_ended then
            --self._scene.btStart:setVisible(true)
            self._scene:onButtonClickedEvent('btn_start')
       else
            local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
            if room_result_layer then
                room_result_layer:show()
            else
                GlobalUserItem.bWaitQuit = false
                local game_layer = self._scene._scene
                if game_layer then
                    game_layer:onExitRoom()
                end
            end
       end
    end
    self:removeFromParent()
    --]]
end


return GameResultLayer
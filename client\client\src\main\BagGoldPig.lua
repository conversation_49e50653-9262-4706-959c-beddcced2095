-------------------------------------------------------------------------------
--  创世版1.0
--  敲金猪
--  @date 2019-01-26
--  @auth woodoo
-------------------------------------------------------------------------------
local BagGoldPig = class("BagGoldPig", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function BagGoldPig:ctor(items)
    print('BagGoldPig:ctor...')
    self:enableNodeEvents()
    self.m_items = items

    -- 载入主UI
    local main_node = helper.app.loadCSB('BagGoldPig.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.m_item_template = main_node:child('item_template'):hide()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function BagGoldPig:onEnter()
    print('BagGoldPig:onEnterTransitionFinish...')

    local pig = helper.app.createAnimation('common/ani_gold_pig', 14, 1.1, false, cc.RemoveSelf:create(true))
    pig:scale(2)
    helper.layout.addCenter(self.main_node, pig, {x=0, y=0})
    
    self:perform(function()
        local light = helper.app.createAnimation('common/ani_gold_pig_light', 11, 1, false, 
            cc.RemoveSelf:create(true)
        )
        light:scale(4)
        helper.layout.addCenter(self.main_node, light, {x=0, y=0})
        self:showItems()
    end, 0.4)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function BagGoldPig:onExit()
    print('BagGoldPig:onExit...')
end


-------------------------------------------------------------------------------
-- onPopClose
-------------------------------------------------------------------------------
function BagGoldPig:onPopClose()
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 显示道具弹出
-------------------------------------------------------------------------------
function BagGoldPig:showItems()
    local template = self.m_item_template
    local count = #self.m_items
    
    local col_count = math.max(3, math.ceil(count / 3))
    local row_count = math.ceil(count / col_count)

    local start_x = display.width/2 - (col_count - 1) * 200 / 2
    local start_y = display.height/2 + (row_count - 1) * 100 / 2
    for index, obj in ipairs(self.m_items) do
        local row = math.floor((index - 1) / col_count) -- 从0开始
        local col = (index - 1) % col_count -- 从0开始
        local x = start_x + col * 200
        local y = start_y - row * 100

        local item_id = obj.id
        local item_num = obj.num
        local item = template:clone():show()
        item:texture('common/item_' .. item_id .. '.png')
        item:child('num'):setString('x' .. item_num)
        local pos = cc.p(x, y)
        item:pos(display.cx, display.cy):addTo(self.main_node)
        item:scale(0)
        item:runAction(cc.Sequence:create(
            cc.DelayTime:create((index - 1) * 0.1),
            cc.Spawn:create(
                cc.ScaleTo:create(0.1, 1),
                cc.EaseBackOut:create(cc.MoveTo:create(0.2, pos))
            )
        ))
    end
end


return BagGoldPig
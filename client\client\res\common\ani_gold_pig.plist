<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_gold_pig_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,0},{178,174}}</string>
                <key>offset</key>
                <string>{-1,-57}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{40,130},{178,174}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,570},{240,186}}</string>
                <key>offset</key>
                <string>{-9,-63}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,130},{240,186}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,570},{240,186}}</string>
                <key>offset</key>
                <string>{-9,-63}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,130},{240,186}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,570},{240,186}}</string>
                <key>offset</key>
                <string>{-9,-63}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,130},{240,186}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,570},{240,186}}</string>
                <key>offset</key>
                <string>{-9,-63}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,130},{240,186}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,570},{240,186}}</string>
                <key>offset</key>
                <string>{-9,-63}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,130},{240,186}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{186,300},{182,222}}</string>
                <key>offset</key>
                <string>{0,-33}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{39,82},{182,222}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,300},{184,268}}</string>
                <key>offset</key>
                <string>{2,-10}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{40,36},{184,268}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{218,298}}</string>
                <key>offset</key>
                <string>{19,5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{40,6},{218,298}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{220,0},{178,174}}</string>
                <key>offset</key>
                <string>{-1,-57}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{40,130},{178,174}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,812},{178,190}}</string>
                <key>offset</key>
                <string>{-1,-49}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{40,114},{178,190}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{192,748},{202,196}}</string>
                <key>offset</key>
                <string>{-12,-39}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{17,101},{202,196}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{192,524},{222,202}}</string>
                <key>offset</key>
                <string>{-15,-55}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{4,114},{222,202}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
            <key>ani_gold_pig_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,570},{240,186}}</string>
                <key>offset</key>
                <string>{-9,-63}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{1,130},{240,186}}</string>
                <key>sourceSize</key>
                <string>{260,320}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_gold_pig.png</string>
            <key>size</key>
            <string>{394,990}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:9376886bbe63f737185c618af07d5f5f:6c31f91c21c41a0484fa997f94ac08a8:c0d12f8f0737589f6e064788989fbcb7$</string>
            <key>textureFileName</key>
            <string>ani_gold_pig.png</string>
        </dict>
    </dict>
</plist>

-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部命令、结构
--  @date 2018-01-19
--  @auth woodoo
-------------------------------------------------------------------------------
local private_struct = appdf.req(appdf.CLIENT_SRC .. "header.Struct_Private")


local CMD_Common = {
    MDM_CLUB_SERVICE            = 300,        -- 主
    SUB_CLUB_CREATE             = 301,        -- 创建俱乐部            tagClub / tabClubCreateResult
    SUB_CLUB_INFO               = 302,        -- 查询自己的俱乐部信息    null / tagClub数组
    SUB_CLUB_MEMBERS            = 303,        -- 查询成员信息   CMD_GR_IDValue clubid,pos,sort(0按加入时间，1按状态) / tagClubMember数组
    SUB_CLUB_APPLY              = 304,        -- 俱乐部加入申请 CMD_GR_IDMsg clubid,msg / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_APPLY_MEMBERS      = 305,        -- 查询加入申请   CMD_GR_IDValue clubid,pos / tagClubApplyMember数组
    SUB_CLUB_APPLY_APPROVE      = 306,        -- 批准加入申请   CMD_GR_IDValue clubid,userid,是否同意 / CMD_GR_IDMsg
    SUB_CLUB_MEMBER_KICK        = 307,        -- 踢成员 CMD_GR_IDValue clubid,userid
    SUB_CLUB_DISMISS            = 308,        -- 解散 CMD_GR_ID clubid / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_QUIT               = 309,        -- 退出 CMD_GR_ID clubid / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_EDIT_NAME          = 310,        -- 编辑 CMD_GR_IDMsg clubid / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_EDIT_ACCOUNT       = 311,        -- 编辑 CMD_GR_IDMsg clubid / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_EDIT_MSG           = 312,        -- 编辑 CMD_GR_IDMsg clubid / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_MEMBERS_REFRESH    = 313,        -- 查询成员信息   CMD_GR_IDValue clubid,pos,sort(0按加入时间，1按状态) / tagClubMember数组
    SUB_CLUB_PLAYER_COUNT       = 314,        -- 俱乐部空闲忙碌人数 tagClubPlayerCount
    SUB_CLUB_APPLY_COUNT        = 315,        -- 查询俱乐部加入申请数量   CMD_GR_ID clubid / CMD_GR_IDValue
    SUB_CLUB_SET_MANAGER        = 316,        -- 设置管理员 CMD_GR_IDValue clubid,userid,is_manager / CMD_GR_IDMsg id>0是错误
    SUB_CLUB_RULE_LIST          = 321,        -- 查看俱乐部玩法列表 CMD_GR_ID clubid /  tagClubRule数组
    SUB_CLUB_RULE_ADD           = 322,        -- 添加俱乐部玩法 tagClubRule /      tagClubRuleResult
    SUB_CLUB_RULE_EDIT          = 323,        -- 编辑俱乐部玩法 tagClubRule /      tagClubRuleResult
    SUB_CLUB_RULE_REMOVE        = 324,        -- 删除俱乐部玩法 CMD_GR_ID ruleid /      CMD_GR_IDValueMsg
    SUB_CLUB_TABLE_LIST         = 325,        -- 查看俱乐部房间列表 CMD_GR_ID clubid /  tagClubRuleTable数组
    SUB_CLUB_TABLE_CREATE       = 326,        -- 创建自定义俱乐部房间 tagClubRule /  CMD_GR_IDValueMsg
    SUB_CLUB_TABLE_JOIN         = 327,        -- 加入俱乐部房间 CMD_GR_IDValue clubid,ruleid,ownerid /  CMD_GR_IDValueMsg id=0正常，value房间号, msg错误消息
    SUB_CLUB_TABLE_INVITE       = 328,        -- 邀请加入俱乐部房间 tagClubRuleTableInvite  /  CMD_GR_IDValueMsg id=0正常，value房间号, msg错误消息
    SUB_CLUB_TABLE_INVITE_NOTIFY= 329,        -- 邀请加入俱乐部房间 tagClubRuleTable
    SUB_CLUB_RECORD_QUERY       = 330,        -- 俱乐部统计记录查询 CMD_GR_IDValue clubid,day1,day2开始日期结束日期(-1,0标识昨天， 0,1是今天)  / tagClubRecord数组

    MDM_SOCKET_SERVICE          = 350,        -- 设置Socket
    SUB_SET_SOCKET              = 350,        -- 设置长连接标志
    SUB_HEARTBEAT               = 351,        -- 心跳包
    SUB_SERVER_ONLINE           = 352,        -- 查询在线信息、金币场默认类型 CMD_GR_ID wKindID / CMD_ServerIDOnlines

    MDM_ITEM_SERVICE            = 400,        -- 道具
    SUB_SYSTEM_MSG_LIST         = 401,        -- 查询系统消息null  / tagSystemMsg数组
    SUB_CONFIG_ITEM_LIST        = 402,        -- 查询道具配置null  / tagConfigItem数组
    SUB_ITEM_LIST               = 403,        -- 查询道具null		/ tagItem数组
    SUB_ITEM_USE                = 404,        -- 使用道具 CMD_GR_LLIDValue		/ CMD_ItemUseResult 如果有错误填在Msg
    SUB_ITEM_ADDRESS            = 405,        -- 登记道具地址 tagItemAddress	/ CMD_GR_LLIDValueMsg 如果有错误填在Msg
    SUB_ITEM_ADD_LIST           = 406,        -- 新增道具推送   tagItem数组
    SUB_GOLD_INFO               = 407,        -- 查询金币信息   tagGoldInfo
    SUB_SET_GENER               = 408,        -- 设置性别   CMD_GR_ID  /  CMD_GR_ID
    SUB_SET_NAME_ID             = 409,        -- 实名认证   tagNameMsg  /  CMD_GR_IDMsg
    SUB_TYPE_VALUE_NOTIFY       = 410,        -- 房卡、金币等的单个值变化推送 CMD_GR_IDValue(id类型1金币 2房卡, value最新值)
    SUB_ROOM_CARD_INFO          = 411,        -- 查询房卡信息   tagRoomCardInfo
    SUB_GLOBAL_REWARD_MSG       = 412,        -- 全局奖励消息 CMD_GR_IDMsg

    MDM_MATCH_SERVICE           = 500,        -- 比赛场
    SUB_MATCH_LIST              = 501,        -- 查询比赛场null  / tagMatchInfo数组
    SUB_MATCH_DESC              = 502,        -- 查询描述CMD_GR_ID  / tagMatchDesc
    SUB_MATCH_APPLY             = 503,        -- 报名CMD_GR_ID  / CMD_GR_IDValueMsg
    SUB_MATCH_CANCEL_APPLY	    = 504,        -- 取消报名CMD_GR_ID  / CMD_GR_IDValueMsg
    SUB_MATCH_EXIT_UI	        = 505,        -- 玩家离开UI
    SUB_MATCH_TABLE_ID	        = 506,        -- 通知玩家进入CMD_GR_IDValue
    SUB_MATCH_RESULT	        = 507,        -- 推送比赛结果  MatchResultInfo数组
    SUB_MATCH_RESULT_UNFINISHED = 508,        -- 推送未完成比赛结果 CMD_GR_IDMsg(id:score,msg:比赛名字)

    MDM_GUESS_SERVICE           = 600,        -- 竞猜
    SUB_GUESS_LIST              = 601,        -- 查询竞猜列表null			/ tagGuess数组
    SUB_GUESS_MY_LIST           = 602,        -- 查询自己竞猜列表null		/ tagPlayerGuess数组
    SUB_GUESS_BET               = 603,        -- 竞猜下注CMD_GR_IDValue(GuessID,Type1,2,3, Bet)	  /   CMD_GR_IDMsg
    SUB_GUESS_RANK_LIST         = 604,        -- 竞猜排行榜				/ tagRank数组
    SUB_GUESS_COIN_NOTIFY       = 605,        -- 竞猜币推送CMD_GR_ID

    MDM_WIN_STREAK_SERVICE      = 650,        -- 万元红包赛
    SUB_WIN_STREAK_QUERY        = 651,        -- 查询连胜		CMD_GR_ID(玩法KindID) / WinStreakInfo
    SUB_WIN_STREAK_REBORN       = 652,        -- 复活			CMD_GR_ID(玩法KindID) / CMD_GR_IDMsg(id0成功其他失败)

    MDM_DAILY_EVENT_SERVICE     = 670,        -- 日常任务
    SUB_LIST_DAILY_EVENT        = 671,        -- 查看列表		CMD_GR_ID(类型Type) / tagDailyEvent
    SUB_GET_DAILY_EVENT_REWARD  = 672,        -- 领取奖励		CMD_GR_ID(EventID)  / CMD_GR_IDValueMsg(id0成功其他失败)
}

CMD_Common.CMD_GR_IDValue = {
	{k = "dwID", t = "dword"},
    {k = "nValue", t = "int"},
    {k = "nValue2", t = "int"},
}

CMD_Common.CMD_GR_ID = {
    {k = "dwID", t = "int"},
}

CMD_Common.CMD_GR_IDMsg = {
	{k = "dwID", t = "dword"},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},
}

CMD_Common.CMD_GR_IDValueMsg = {
	{k = "dwID", t = "dword"},
    {k = "nValue", t = "int"},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},
}

CMD_Common.CMD_GR_LLID = {
    {k = "llID", t = "score"},
}

CMD_Common.CMD_GR_LLIDMsg = {
	{k = "llID", t = "score"},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},
}

CMD_Common.CMD_GR_LLIDValue = {
    {k = "llID", t = "score"},
    {k = "nValue", t = "int"},
}

CMD_Common.CMD_GR_LLIDValueMsg = {
	{k = "llID", t = "score"},
    {k = "nValue", t = "int"},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},
}

-- 俱乐部
CMD_Common.tagClub = {
	{k = "dwClubID", t = "dword"},
	{k = "dwPresidentID", t = "dword"},
    {k = "sPresidentAccount", t = "string", s = yl.LEN_NICKNAME},
    {k = "szName", t = "string", s = yl.LEN_NICKNAME},
    {k = "nLogoID", t = "int"},
    {k = "szProvince", t = "string", s = yl.LEN_NICKNAME},
    {k = "szCity", t = "string", s = yl.LEN_NICKNAME},
    {k = "szArea", t = "string", s = yl.LEN_NICKNAME},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},  -- 滚动消息
    {k = "nMemberCount", t = "int"},
    {k = "szPresidentName", t = "string", s = yl.LEN_NICKNAME},
    {k = "cbIsManager", t = "byte"},   -- 是否管理员
}

-- 俱乐部成员
CMD_Common.tagClubMember = {
	{k = "dwClubID", t = "dword"},
	{k = "dwUserID", t = "dword"},
    {k = "sysJoinTime", t = "table", d = private_struct.SYSTEMTIME},
    {k = "szName", t = "string", s = yl.LEN_NICKNAME},
    {k = "szHeadHttp", t = "string", s = yl.LEN_HEAD_HTTP_SHORT},
    {k = "sysLastLogonDate", t = "table", d = private_struct.SYSTEMTIME},
    {k = "wIndex", t = "word"}, -- 分页查询的index
    {k = "wTotal", t = "word"}, -- 分页查询的总数
    {k = "cbStatus", t = "byte"},   -- 在线状态，0不在线，2忙碌, 3在线空闲
	{k = "dwLockServerID", t = "dword"},    -- 当前所在游戏服ID
    {k = "wClubTableID", t = "word"}, -- 俱乐部内创建的TableID 
    {k = "nPlayIndex", t = "int"},  -- 当前局,1-nPlayCount
    {k = "nPlayCount", t = "int"},  -- 总局数，负数表示打捆
    {k = "cbIsManager", t = "byte"},   -- 是否管理员
}

-- 俱乐部成员状态数量变化
CMD_Common.tagClubPlayerCount = {
	{k = "dwClubID", t = "dword"},
	{k = "nFreeCount", t = "int"},
	{k = "nBusyCount", t = "int"},
}

-- 申请成员
CMD_Common.tagClubApplyMember = {
	{k = "dwClubID", t = "dword"},
	{k = "dwUserID", t = "dword"},
    {k = "sysApplyTime", t = "table", d = private_struct.SYSTEMTIME},
    {k = "szName", t = "string", s = yl.LEN_NICKNAME},
    {k = "szHeadHttp", t = "string", s = yl.LEN_HEAD_HTTP_SHORT},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},  -- 申请文字
    {k = "wIndex", t = "word"}, -- 分页查询的index
    {k = "wTotal", t = "word"}, -- 分页查询的总数
}

-- 玩法
CMD_Common.tagClubRule = {
	{k = "dwRuleID", t = "dword"},              -- 唯一ID
	{k = "dwClubID", t = "dword"},
    {k = "wKindID", t = "word"},                -- 玩法类型
	{k = "wJoinGamePeopleCount", t = "word"},   -- 参加游戏的最大人数
    {k = "cbPayType", t = "byte"},              -- 0房主支付，1AA支付, 2房主不关心参与人数, 3大赢家支付, 4大赢家不关心参与人数
    {k = "cbPlayCountIndex", t = "byte"},       -- 局数选择索引
    {k = "nBaseScore", t = "int"},              -- 底分
    {k = "llGameRule", t = "score"},            -- 游戏规则
	{k = "dwOwnerID", t = "dword"},             -- 服务器填写用
    {k = "szRule", t = "string", s = yl.LEN_CLUB_RULE},  -- 规则文字
    {k = "cbCanContinue", t = "byte"},          -- 是否可以续打
}

-- 玩法创建结果
CMD_Common.tagClubRuleResult = {
	{k = "rule", t = "table", d = CMD_Common.tagClubRule},
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},  -- 错误文字
}

-- 玩法房间
CMD_Common.tagClubRuleTable = {
	{k = "rule", t = "table", d = CMD_Common.tagClubRule},
    {k = "cbPlayerCount", t = "byte"},
    {k = "dwUserID", t = "dword", l = {4}},
    {k = "szHeadHttp", t = "string", s = yl.LEN_HEAD_HTTP_SHORT, l = {4}},
    {k = "nRoomNo", t = "int"},
	{k = "dwClubTableSeqID", t = "dword"},          -- 递增序列号id（后台使用）
}

-- 邀请
CMD_Common.tagClubRuleTableInvite = {
	{k = "dwClubID", t = "dword"},
	{k = "dwRuleID", t = "dword"},
	{k = "dwOwnerID", t = "dword"},
	{k = "dwUserID", t = "dword"},             -- 被邀请人
}

-- 邀请Notify
CMD_Common.tagClubRuleTableInviteNotify = {
	{k = "nRoomNo", t = "int"},
    {k = "szRule", t = "string", s = yl.LEN_CLUB_RULE},  -- 规则文字
    {k = "szUserName", t = "string", s = yl.LEN_NICKNAME},
    {k = "szClubName", t = "string", s = yl.LEN_NICKNAME},
}

-- 统计记录
CMD_Common.tagClubRecord = {
    {k = "sysTime", t = "table", d = private_struct.SYSTEMTIME},    -- 日期
	{k = "nPlayers", t = "int"},        -- 人数
	{k = "nSetCount", t = "int"},       -- 大局
	{k = "nGameCount", t = "int"},      -- 小局
	{k = "nRoomCardCost", t = "int"},   -- 房卡消耗
	{k = "nRoomDiamondCost", t = "int"},-- 钻石消耗
}

-- 服务器在线信息
CMD_Common.ServerIDOnline = {
	{k = "wStructSize", t = "word"},
	{k = "wServerID", t = "word"},
	{k = "wOnlineCount", t = "word"},
    {k = "bIsDefault", t = "bool"},    -- 是否默认选中的
}

-- 系统消息列表
CMD_Common.tagSystemMsg = {
	{k = "wStructSize", t = "word"},
	{k = "szMsg", t = "string", s = 100},
	{k = "msgTime", t = "table", d = private_struct.SYSTEMTIME},
}

-- 道具配置列表
CMD_Common.tagConfigItem = {
	{k = "wStructSize", t = "word"},
	{k = "dwItemID", t = "dword"},
	{k = "szName", t = "string", s = 10},
    {k = "szDesc", t = "string", s = 64},
    {k = "szIcon", t = "string", s = 128},
	{k = "cbItemType", t = "byte"},
	{k = "cbTabType", t = "byte"},
	{k = "nStack", t = "int"},
}

-- 道具列表
CMD_Common.tagItem = {
	{k = "wStructSize", t = "word"},
	{k = "llID", t = "score"},
	{k = "dwItemID", t = "dword"},
    {k = "nNum", t = "int"},
	{k = "nOverDueSeconds", t = "int"}, -- 过期剩余秒数 
}

-- 道具实物寄件地址
CMD_Common.tagItemAddress = {
	{k = "llID", t = "score"},
	{k = "szAddress", t = "string", s = 64},
	{k = "szName", t = "string", s = 10},
	{k = "szPhoneNo", t = "string", s = 20},
}

-- 金币信息
CMD_Common.tagGoldInfo = {
	{k = "llGold", t = "score"},
	{k = "dwTicket", t = "dword"},
}

-- 名称消息（实名认证）
CMD_Common.tagNameMsg = {
	{k = "szName", t = "string", s = yl.LEN_NICKNAME},
	{k = "szMsg", t = "string", s = yl.LEN_NICKNAME},
}

-- 比赛场信息
CMD_Common.tagMatchInfo = {
    {k = "wStructSize", t = "word"},            -- 结构大小
    {k = "wKindID", t = "word"},                -- 玩法ID
    {k = "nServerID", t = "int"},               -- ServerID
    {k = "nID", t = "int"},                     -- 唯一ID
    {k = "nType", t = "int"},                   -- 0人满即开， 1定时比赛
    {k = "nMaxPlayers", t = "int"},             -- 最多报名人数
    {k = "nMinPlayers", t = "int"},             -- 最少开赛人数
    {k = "nApplyType", t = "int"},              -- 报名类型, 0免费, 1成功分享朋友圈，2消耗房卡, 3消耗金币
    {k = "nApplyPrice", t = "int"},             -- 报名价格
    {k = "nGameClock", t = "int"},              -- 定时比赛的每天开赛小时*100+分钟:2100是21:00	（服务端使用）
    {k = "szIcon", t = "string", s = 128},      -- 图标地址
    {k = "szName", t = "string", s = 20},       -- 比赛名次
    {k = "szDescShort", t = "string", s = 32},  -- 简短描述
    {k = "nGameClockTime", t = "int"},          -- 定时游戏下一场开始时间
    {k = "nApplyPlayers", t = "int"},           -- 报名人数
    {k = "bSelfApply", t = "bool"},             -- 自己是否已报名
    {k = "nStatus", t = "int"},                 -- 状态.0报名中,1等待开始,2已开始
}

-- 比赛详情
CMD_Common.tagMatchDesc = {
    {k = "nID", t = "int"},                         -- 比赛唯一ID
    {k = "szDescRule", t = "string", s = 512},      -- 规则
    {k = "szDescReward", t = "string", s = 512},    -- 奖励
}

-- 比赛结果
CMD_Common.MatchResultInfo = {
    {k = "wStructSize", t = "word"},            -- 结构大小
    {k = "nMatchID", t = "int"},
    {k = "szMatchName", t = "string", s = 20},
    {k = "nRank", t = "int"},
    {k = "nRewardTypes", t = "int", l = {5}},
    {k = "nRewardNums", t = "int", l = {5}},
    {k = "szIcon", t = "string", s = 128, l = {5}},
}


---------------
-- 世界杯竞猜 --
---------------
CMD_Common.tagGuess = {
    {k = "wStructSize", t = "word"},            -- 结构大小
    {k = "nGuessID", t = "int"},
    {k = "nTeamID1", t = "int"},
    {k = "nTeamID2", t = "int"},
    {k = "nOdds1", t = "int"},
    {k = "nOdds2", t = "int"},
    {k = "nOdds3", t = "int"},  -- 平赔率
    {k = "nPlayerCount1", t = "int"},
    {k = "nPlayerCount2", t = "int"},
    {k = "nPlayerCount3", t = "int"},  -- 平人数
    {k = "nRewardPool", t = "int"},
    {k = "sysMatchTime", t = "table", d = private_struct.SYSTEMTIME},
    {k = "nResult", t = "int"}, -- 结果，1:1胜, 2:2胜利, 3:平
    {k = "nStatus", t = "int"}, -- 状态 ,0竞猜中，1已开始比赛，2已发奖励
    {k = "szName", t = "string", s = 20},
    {k = "szTeamName1", t = "string", s = 20},
    {k = "szTeamName2", t = "string", s = 20},
}

CMD_Common.tagPlayerGuess = {
    {k = "wStructSize", t = "word"},            -- 结构大小
    {k = "nGuessID", t = "int"},
    {k = "nTeamID1", t = "int"},
    {k = "nTeamID2", t = "int"},
    {k = "nBet1", t = "int"},
    {k = "nBet2", t = "int"},
    {k = "nBet3", t = "int"},
    {k = "nWin1", t = "int"},
    {k = "nWin2", t = "int"},
    {k = "nWin3", t = "int"},
    {k = "nOdds1", t = "int"},
    {k = "nOdds2", t = "int"},
    {k = "nOdds3", t = "int"},
    {k = "nStatus", t = "int"}, -- 状态 ,0竞猜中，1已开始比赛，2已发奖励
    {k = "szName", t = "string", s = 20},
    {k = "szTeamName1", t = "string", s = 20},
    {k = "szTeamName2", t = "string", s = 20},
}

CMD_Common.tagGuessBetResult = {
    {k = "nErrorCode", t = "int"},
    {k = "szMsg", t = "string", s = 32},
    {k = "nGuessID", t = "int"},
    {k = "nPlayerCount1", t = "int"},
    {k = "nPlayerCount2", t = "int"},
    {k = "nPlayerCount3", t = "int"},
    {k = "nRewardPool", t = "int"},
    {k = "nBet1", t = "int"},
    {k = "nBet2", t = "int"},
    {k = "nBet3", t = "int"},
}

CMD_Common.tagGuessRank = {
    {k = "wStructSize", t = "word"},            -- 结构大小
    {k = "cbType", t = "byte"},                 -- 1:今日，2：昨日，3：总
    {k = "szName", t = "string", s = 20},
    {k = "nScore", t = "int"},
}

CMD_Common.WinStreakInfo = {
    {k = "nWin", t = "int"},            -- 连胜数量
    {k = "nFinishRound", t = "int"},    -- 本日完成的连胜轮次
    {k = "isLastLose", t = "bool"},     -- 最近这场是否失败了，失败需要用复活卡 
    {k = "nRebornCard", t = "int"},     -- 当前复活卡数量
    {k = "nNeedRebornCard", t = "int"}, -- 本次复活需要复活卡数量
    {k = "nTicket", t = "int"},         -- 最近一次获得奖券数量
}

CMD_Common.tagDailyEvent = {
    {k = "wStructSize", t = "word"},                            -- 结构大小
    {k = "nEventID", t = "int"},                                -- 任务配置ID
    {k = "nType", t = "int"},                                   -- 类型,1连胜场日常任务
    {k = "nRewardType", t = "int"},                             -- 奖励类型
    {k = "nRewardNum", t = "int"},                              -- 奖励数量
    {k = "szGoalType", t = "string", s = yl.LEN_PHONE_MODE},    -- 目标类型英文,
    {k = "nGoalNum", t = "int"},                                -- 需要的目标数量
    {k = "nDoneNum", t = "int"},                                -- 已完成的数量	
    {k = "cbStatus", t = "byte"},                               -- 当前状态0未完成，1可领奖，2已领奖	
    {k = "szName", t = "string", s = yl.LEN_NICKNAME},          -- 名称,
    {k = "szMsg", t = "string", s = 100},                       -- 描述,
}

CMD_Common.tagRoomCardInfo = {
    {k = "nRoomCard", t = "int"},                               -- 房卡（非绑定）
    {k = "nDiamond", t = "int"},                                -- 钻石（绑定房卡）
}

CMD_Common.CMD_ItemUseResult = {
	{k = "llID", t = "score"},                              -- 唯一ID
    {k = "dwItemID", t = "dword"},                          -- 道具ID
    {k = "nError", t = "int"},                              -- 错误代码
    {k = "szMsg", t = "string", s = yl.LEN_TASK_NAME},      -- 错误消息
    {k = "nRewardIDNums", t = "int", l = {20}},             -- 道具id,数量...
}

return CMD_Common
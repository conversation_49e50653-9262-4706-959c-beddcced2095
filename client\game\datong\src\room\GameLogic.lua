local GameLogic = {}


--**************    扑克类型    ******************--
--混合牌型
GameLogic.OX_VALUEO				= 100

GameLogic.USER_OPERATE_PASS     = 200
GameLogic.USER_OPERATE_HINT     = 201
GameLogic.USER_OPERATE_OUTCARD  = 202
GameLogic.USER_OPERATE_REPUSH 	= 203


--最大手牌数目
GameLogic.MAX_CARDCOUNT			= 5
--牌库数目
GameLogic.FULL_COUNT			= 52
--正常手牌数目
GameLogic.NORMAL_COUNT			= 5


--- 排序方案
GameLogic.ST_ORDER              = 0           -- 大小
GameLogic.ST_COUNT              = 1           -- 大小
GameLogic.ST_VALUE              = 2           -- 大小

--扑克类型
GameLogic.CT_ERROR					=	0	--错误类型
GameLogic.CT_SINGLE					=	1	--单牌类型
GameLogic.CT_SHUNZI					=	2	--顺子类型
GameLogic.CT_DOUBLE					=	3	--对子类型
GameLogic.CT_DOUBLE_LINK			=	4	--对连类型
GameLogic.CT_THREE				    =	5	--三条类型
GameLogic.CT_THREE_LINK				=	6	--三连类型
GameLogic.CT_THREE_PAIR				=	7	--三带对类型
GameLogic.CT_THREE_PAIR_LINK		=	8	--连三带对类型
GameLogic.CT_BOMB				    =	9	--炸弹类型
GameLogic.CT_BOMB_LINK              =   10  --排炸类型
GameLogic.CT_BOMB_TW                =   11  --天王炸弹



--取模
function GameLogic:mod(a,b)
    return a - math.floor(a/b)*b
end

--获得牌的数值
function GameLogic:getCardValue(cbCardData)
    return self:mod(cbCardData, 16)
end

--获得牌的数值
function GameLogic:getCardLogicValue(cbCardData)
	local val = self:mod(cbCardData, 16)
	if val == 0 then
		return val
	end
	if val >= 0x0E then
		val = val + 2
	elseif val <= 2 then
		val = val + 13
	end	
	return val
end

--获得牌的颜色（0 -- 4）
function GameLogic:getCardColor(cbCardData)
    return math.floor(cbCardData/16)
end

--拷贝表
function GameLogic:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
end

function GameLogic:SortCardList( cbCardData, cbCardCount, cbSortType )
	if cbCardCount <= 0 then
		return 
	end

	cbSortType = cbSortType or GameLogic.ST_ORDER

	if cbSortType == GameLogic.ST_ORDER then
		table.sort(cbCardData, 
			function(a, b) 
				local a_v = self:getCardLogicValue(a)
				local b_v = self:getCardLogicValue(b)
				if a_v > b_v then
					return true
				elseif a_v < b_v then
					return false
				else
					return a > b
				end
			end)
	elseif cbSortType == GameLogic.ST_VALUE then
		table.sort(cbCardData, function(a, b) 
			local a_v = self:getCardValue(a)
			local b_v = self:getCardValue(b)
			if a_v > b_v then
				return true
			elseif a_v < b_v then
				return false
			else
				return a > b
			end
		end)
	end
	
end

--排序
function GameLogic:sort(cbCardData, sort_card_num)
	sort_card_num = sort_card_num or #cbCardData
	self:SortCardList(cbCardData, sort_card_num)	
end

--是否连牌
function GameLogic:IsStructureLink(cbCardData, cbCardCount, cbCellCount)
	if self:mod(cbCardCount, cbCellCount) ~= 0 then
		return false
	end

	local cbBlockCount = cbCardCount / cbCellCount
    local cbFirstValue = self:getCardLogicValue(cbCardData[1])
 
	if cbFirstValue > 14 then return  false end

    for i = 2, cbBlockCount do
       -- print('cbBlockCount is ', cbBlockCount, self:getCardLogicValue(cbCardData[i * cbCellCount]))
		if cbFirstValue ~= (self:getCardLogicValue(cbCardData[i * cbCellCount]) + i - 1) then
			return false
		end
	end

	return true
end

function GameLogic:AnalysebCardData(cbCardData, cbCardCount)
	local AnalyseResult = {}
	AnalyseResult.cbBlockCount = {0, 0, 0, 0, 0, 0, 0, 0}
	AnalyseResult.cbCardData = {}
	for i = 1, 8 do
		AnalyseResult.cbCardData[i] = {}
	end
	 
	local i = 1
	while i <= cbCardCount do
		local cbSameCount = 1
		local cbLogicValue = self:getCardLogicValue(cbCardData[i])

		--搜索同牌
		for j = i + 1, cbCardCount do
			if self:getCardLogicValue(cbCardData[j] ) ~= cbLogicValue then
				break
			end
			cbSameCount = cbSameCount + 1
        end
        
        if cbSameCount > 8 then
            print("这儿有错误")
            return
        end

		--设置结果
		local cbIndex = AnalyseResult.cbBlockCount[cbSameCount]
		AnalyseResult.cbBlockCount[cbSameCount] = AnalyseResult.cbBlockCount[cbSameCount] + 1
		--print('cbSameCount is ', cbSameCount)
		for j = 1, cbSameCount do
            --table.insert(AnalyseResult.cbCardData[cbSameCount], cbCardData[i + j - 1])
            AnalyseResult.cbCardData[cbSameCount][cbIndex * cbSameCount+j] = cbCardData[i + j - 1]
		end

		i = i + cbSameCount
	end
	--dump(AnalyseResult)

	return AnalyseResult
end


function GameLogic:GetCardType(cbCardData, cbCardCount)
	local cbStarLevel = 0
	if cbCardCount == 0 then
		return GameLogic.CT_ERROR, cbStarLevel
	elseif cbCardCount == 1 then
		return GameLogic.CT_SINGLE, cbStarLevel
    end
    
    self:SortCardList(cbCardData,cbCardCount,GameLogic.ST_ORDER)

	print('card typ num :', cbCardCount)
	--排炸类型
	if cbCardCount >= 12 then
		local cbCardIndex = 1
		local cbBlockCount = 0
		
		while cbCardIndex <= cbCardCount  do
			local cbSameCount = 1
			local cbCardValue = self:getCardLogicValue(cbCardData[cbCardIndex])
			for i = cbCardIndex + 1, cbCardCount do
				if self:getCardLogicValue(cbCardData[i])  == cbCardValue then
					cbSameCount = cbSameCount + 1
				else
					break
				end
			end
			
			--连牌判断
			if cbSameCount >= 4 then
				cbBlockCount = cbBlockCount + 1
				cbCardIndex = cbCardIndex + cbSameCount
			else
				break
            end
            if cbCardIndex > cbCardCount then
                cbCardIndex = cbCardIndex - 1
                break
            end
		end
		

		--结果判断
		if cbBlockCount >= 3 and cbCardIndex == cbCardCount and 
			self:mod(cbCardCount,cbBlockCount) == 0 and 
			self:IsStructureLink(cbCardData, cbCardCount, cbCardCount / cbBlockCount)
			then
				cbStarLevel = cbCardCount / cbBlockCount
				return GameLogic.CT_BOMB_LINK, cbStarLevel
		end
	end

	print('普通分析 开始')
	local AnalyseResult = self:AnalysebCardData(cbCardData, cbCardCount)
    --dump("普通分析 结束")
    --dump(cbCardData)
    --dump(AnalyseResult)

	--同牌判断
	if cbCardCount == 3 and AnalyseResult.cbBlockCount[3] == 1 then return GameLogic.CT_THREE, cbStarLevel end
    if cbCardCount == 2 and AnalyseResult.cbBlockCount[2] == 1 then return GameLogic.CT_DOUBLE, cbStarLevel end
    if cbCardCount == 5 and AnalyseResult.cbBlockCount[3] == 1 and AnalyseResult.cbBlockCount[2] == 1 then return GameLogic.CT_THREE_PAIR, cbStarLevel end

	--天王炸弹
	if cbCardCount == 4 and cbCardData[1] == 0x4F and cbCardData[4] == 0x4E then
		cbStarLevel = 11
		return GameLogic.CT_BOMB_TW, cbStarLevel
	end

	--同相炸弹
	if cbCardCount >= 4 and cbCardCount <= 8 and AnalyseResult.cbBlockCount[cbCardCount] == 1 then
		cbStarLevel = cbCardCount
		return GameLogic.CT_BOMB, cbStarLevel
	end

	--对连类型
	if cbCardCount >= 4 and AnalyseResult.cbBlockCount[2] * 2 == cbCardCount then
		local cbDoubleCount = AnalyseResult.cbBlockCount[2] * 2
		if self:IsStructureLink(AnalyseResult.cbCardData[2],cbDoubleCount,2)==true then
			return GameLogic.CT_DOUBLE_LINK, cbStarLevel
		end
	end

	--三连类型
	if cbCardCount >= 6 and AnalyseResult.cbBlockCount[3] * 3 == cbCardCount then
		local cbThreeCount = AnalyseResult.cbBlockCount[3] * 3
		if self:IsStructureLink(AnalyseResult.cbCardData[3], cbThreeCount, 3)==true then
			return GameLogic.CT_THREE_LINK, cbStarLevel
		end
    end
    
    --三连带对类型
    if cbCardCount >= 10 and (AnalyseResult.cbBlockCount[3] * 3 + AnalyseResult.cbBlockCount[2] * 2) == cbCardCount then
        local cbThreeCount = AnalyseResult.cbBlockCount[3] * 3
        local cbDoubleCount = AnalyseResult.cbBlockCount[2] * 2   
        if self:IsStructureLink(AnalyseResult.cbCardData[3], cbThreeCount, 3) == true and 
            self:IsStructureLink(AnalyseResult.cbCardData[2], cbDoubleCount, 2) and
            AnalyseResult.cbBlockCount[3] == AnalyseResult.cbBlockCount[2] then
			return GameLogic.CT_THREE_PAIR_LINK, cbStarLevel
		end
    end

	-- 顺子类型
    if cbCardCount >= 5 and AnalyseResult.cbBlockCount[1] == cbCardCount then
        print('111111111')
		if self:IsStructureLink(AnalyseResult.cbCardData[1], cbCardCount, 1) == true then
			return GameLogic.CT_SHUNZI, cbStarLevel
		end
	end

	return  GameLogic.CT_ERROR, cbStarLevel
end 

function GameLogic:concat(a, b)
	local result = {}
	for _, v in ipairs(a) do
		table.insert( result, v)
	end
	for _, v in ipairs(b) do
		table.insert( result, v)
	end

	return result
end

-- 获取相同的牌
function GameLogic:anayseCard(cbCardData, card_count, is_sort_card_value, is_out_card)
	is_sort_card_value = is_sort_card_value or false
	is_out_card = is_out_card or false
	local max_king_card = {}
	local min_king_card = {}
	local vec_cards = {}
	local cards = {}
	local pre_card = 0
	local king_is_ok = false
	self:sort(cbCardData)
	--dump(cbCardData)
	for i = 1, card_count do repeat
		if cbCardData[i] == 0 then
			break
		end
		if cbCardData[i] == 0x4F then
			table.insert(max_king_card, cbCardData[i])
			break
		end
		if cbCardData[i] == 0x4E then
			table.insert(min_king_card, cbCardData[i])
			break
		end
		if not king_is_ok then
			pre_card = cbCardData[i];
			king_is_ok = true;
			table.insert(cards, cbCardData[i]);
			print('first not king ', self:getCardValue(cbCardData[i]))
			break
		end

		if self:getCardValue(pre_card) == self:getCardValue(cbCardData[i]) then
			table.insert(cards, cbCardData[i])
			break
		end
		--print('current card data ', self:getCardValue(cbCardData[i]))
		--dump(cards)
		table.insert(vec_cards, cards)
		cards = {}
		pre_card = cbCardData[i]
		table.insert(cards, cbCardData[i])
	until true
	end

	if #cards > 0 then
		table.insert(vec_cards, cards)
	end

	if #min_king_card > 0 then
		table.insert(vec_cards, 1, min_king_card)
	end

	if #max_king_card > 0 then
		table.insert(vec_cards, 1, max_king_card)
	end

	return vec_cards
end


function GameLogic:compareTwoCard(card_a, card_b)
	local a_v = self:getCardLogicValue(card_a)
	local b_v = self:getCardLogicValue(card_b)
	return a_v < b_v
end

function GameLogic:GuanCard(vec_cards, out_card, can_spilt_card)
	can_spilt_card = can_spilt_card or false
	local result = {}
	local is_has_zha = false
	--dump(out_card)
	for _, v in ipairs(vec_cards) do repeat
		if out_card.card_num == 0 then
			table.insert(result, v.card)
			break
		end
		if not is_has_zha then
			if v.card_num >= 4 then
				is_has_zha = true
			end
		end
		if out_card.card_num >= 4 then
			if out_card.card_num < v.card_num then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if v.is_single_king and not out_card.is_single_king then
					table.insert(result, v.card)
					break
				end
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end
			end
			
		end
		if out_card.card_num < 4 then
			if v.card_num >= 4 then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end 
			end
		end

	until true
	end

	if can_spilt_card == false then
		return result
	end

	if #result == 0 and not is_has_zha then
		for _, v in ipairs(vec_cards) do
			if self:compareTwoCard(out_card.card_real_v, v.card_v) and out_card.card_num <= #v.card then
				local ret_vec = {}
				for i = 1, out_card.card_num do
					table.insert(ret_vec, v.card[i])
				end
				table.insert(result, ret_vec)
			end
		end
	end
	return result
end

--三带一对
function GameLogic:SearchThreeDobuleSameCard( cbHandCardData, cbHandCardCount, cbThreeReferCard, cbDoubleReferCard )
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
	local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--local cbCardData = self:copyTab(cbHandCardData)
	local cbCardData = cbHandCardData
	local cbCardCount = cbHandCardCount
	if cbCardCount < 5 then
		return tagSearchCardResult
    end

    local cbReferLogicValue3 =  0 --(cbReferCard == 0 and 0 or GameLogic:getCardLogicValue(cbReferCard))
    if cbThreeReferCard and cbThreeReferCard ~= 0 then
        cbReferLogicValue3 = GameLogic:getCardLogicValue(cbThreeReferCard)
    end
    local cbReferLogicValue2 =  0 --(cbReferCard == 0 and 0 or GameLogic:getCardLogicValue(cbReferCard))
    if cbDoubleReferCard and cbDoubleReferCard ~= 0 then
        cbReferLogicValue2 = GameLogic:getCardLogicValue(cbDoubleReferCard)
    end
    

    local result1 = self:SearchSameCard(cbHandCardData, cbHandCardCount, cbThreeReferCard, 3, false)
    local result2 = self:SearchSameCard(cbHandCardData, cbHandCardCount, 0, 2)
    if result1[1] == 0 or result2[1] == 0 then
        return tagSearchCardResult
    end
    local index = 1
    --dump(result1)
    --dump(result2)
    for i = 1, result1[1] do repeat
        local bIsNeedBig = false
        local threeIndex = (i - 1) * 3 + 1
        if self:getCardLogicValue(result1[3][i][1]) < cbReferLogicValue3 then
            break
        elseif self:getCardLogicValue(result1[3][i][1]) == cbReferLogicValue3 then
            bIsNeedBig = true
        end
        for j = 1, result2[1] do repeat
            local twoIndex = (j - 1) * 2 + 1
            if bIsNeedBig then
                if self:getCardLogicValue(result2[3][j][1]) <= cbReferLogicValue2 then
                    break
                end
            end
            if self:getCardLogicValue(result2[3][j][1]) == self:getCardLogicValue(result1[3][i][1]) then
                break
            end
            cbResultCardCount[cbResultCount] = 5
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount][1] = result1[3][i][1]
            cbResultCard[cbResultCount][2] = result1[3][i][2]
            cbResultCard[cbResultCount][3] = result1[3][i][3]
            cbResultCard[cbResultCount][4] = result2[3][j][1]
            cbResultCard[cbResultCount][5] = result2[3][j][2]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        until true
        end
    until true
    end

    tagSearchCardResult[1] = cbResultCount - 1
    --dump(tagSearchCardResult)
    return tagSearchCardResult
end

--同牌搜索
function GameLogic:SearchSameCard(cbHandCardData, cbHandCardCount, cbReferCard, cbSameCardCount, bIsNeedBig)
    bIsNeedBig = bIsNeedBig or true
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
	local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--local cbCardData = self:copyTab(cbHandCardData)
	local cbCardData = cbHandCardData
	local cbCardCount = cbHandCardCount
	if cbCardCount < cbSameCardCount then
		return tagSearchCardResult
    end

    --排序扑克
    GameLogic:SortCardList(cbCardData, cbHandCardCount, GameLogic.ST_ORDER)
    --分析结构
    local tagAnalyseResult = GameLogic:AnalysebCardData(cbCardData, cbCardCount)
    --dump(tagAnalyseResult, "tagAnalyseResult", 6)
    local cbReferLogicValue =  0 --(cbReferCard == 0 and 0 or GameLogic:getCardLogicValue(cbReferCard))
    if cbReferCard and cbReferCard ~= 0 then
        cbReferLogicValue = GameLogic:getCardLogicValue(cbReferCard)
        if not bIsNeedBig then
            cbReferLogicValue = cbReferLogicValue - 1
        end
    end
    print('cbReferLogicValue num is ', cbReferLogicValue)
    local cbBlockIndex = cbSameCardCount
    while cbBlockIndex <= 8 do
        for i=1,tagAnalyseResult.cbBlockCount[cbBlockIndex] do
            local cbIndex = (tagAnalyseResult.cbBlockCount[cbBlockIndex] - i) * cbBlockIndex + 1
            local cbNowLogicValue = GameLogic:getCardLogicValue(tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex])
            local bIsAdd = false
            local search_num = 0
            if cbSameCardCount == cbBlockIndex then
                if cbNowLogicValue > cbReferLogicValue then
                    bIsAdd = true
                    search_num = cbSameCardCount
                end
            else
                if cbBlockIndex >= 4 then
                    bIsAdd = true
                    search_num = cbBlockIndex
                else
                    if cbNowLogicValue > cbReferLogicValue then
                        bIsAdd = true
                        search_num = cbSameCardCount
                    end
                end
            end
            if bIsAdd then
                cbResultCardCount[cbResultCount] = search_num
                tagSearchCardResult[2] = cbResultCardCount
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount][1] = tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex]
                for i=2, search_num do
                    cbResultCard[cbResultCount][i] = tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex+i-1]
                end --此处修改
                tagSearchCardResult[3] = cbResultCard
                cbResultCount = cbResultCount + 1
            end
        end
        cbBlockIndex = cbBlockIndex + 1
    end
    tagSearchCardResult[1] = cbResultCount - 1
    return tagSearchCardResult
end

--分析分布
function GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    local cbCardCount1 = 0
    local cbDistributing = {}
    for i=1,15 do
        local distributing = {}
        for j=1,6 do
            distributing[j] = 0
        end
        cbDistributing[i] = distributing
    end
    local Distributing = {cbCardCount1,cbDistributing}
    for i=1,cbCardCount do
        if cbCardData[i] ~= 0 then
            local cbCardColor = GameLogic:getCardColor(cbCardData[i])
            local cbCardValue = GameLogic:getCardValue(cbCardData[i])
            --分布信息
            cbCardCount1 = cbCardCount1 + 1
            cbDistributing[cbCardValue][5+1] = cbDistributing[cbCardValue][6]+1
            local color = bit:_rshift(cbCardColor,4) + 1
            cbDistributing[cbCardValue][color] = cbDistributing[cbCardValue][color]+1
        end
    end
    Distributing[1] = cbCardCount1
    Distributing[2] = cbDistributing
    -- print("总数：" .. Distributing[1])
    -- for i=1,15 do
    --     print("每张总数：" .. Distributing[2][i][6])
    -- end
    return Distributing
end

--构造扑克
function GameLogic:MakeCardData(cbValueIndex,cbColorIndex)
    --print("构造扑克 " ..bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)..",".. GameLogic:getCardLogicValue(bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)))
    return bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)
end

function GameLogic:SearchLineThreePairCardType(cbHandCardData, cbHandCardCount, cbReferCard, cbTwoReferCard, cbLineCount)
        --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}

    local result1 = self:SearchLineCardType(cbHandCardData, cbHandCardCount, cbReferCard, 3, cbLineCount, false)
    local result2 = self:SearchLineCardType(cbHandCardData, cbHandCardCount, 0, 2, cbLineCount)
    --dump(result1)
    --dump(result2)

    if result1[1] < 1 or result2[1] < 1 then
        return tagSearchCardResult
    end

    local value_num = {}
    for i = 1, 15 do
        value_num[i] = 0
    end
    dump(cbHandCardData)
    print('cbHandCardCount is ', cbHandCardCount)
    for i = 1, cbHandCardCount do repeat
        local v = self:getCardLogicValue(cbHandCardData[i])
        print('v is ', v)
        if v == nil or v > 15 then break end
        value_num[v] = value_num[v] + 1
    until true
    end


    for i = 1, result1[1] do repeat
        local lineCount = result1[2][i] / 3
        local is_need_big = false
        if cbReferCard ~= 0 and self:getCardLogicValue(result1[3][i][1]) == self:getCardLogicValue(cbReferCard) then
            is_need_big = true
        end
        for j = 1, result2[1] do repeat
            local twoLineCount = result2[2][j] / 2
            if lineCount > twoLineCount then
                break
            end

            if cbTwoReferCard ~= 0 and  is_need_big and 
                self:getCardLogicValue(result2[3][i][1]) <= self:getCardLogicValue(cbTwoReferCard) then
                break
            end
            
            cbResultCardCount[cbResultCount] = 5 * lineCount
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = {}
            local tmp_num = {}
            for k = 1, 15 do
                tmp_num[k] = 0
            end
            for k = 1, result1[2][i] do repeat
                cbResultCard[cbResultCount][k] = result1[3][i][k]
                local v = self:getCardLogicValue(result1[3][i][k])
                if v == nil or v > 15 then break end
                tmp_num[v] = tmp_num[v] + 1
            until true
            end
            local index = result1[2][i]
            for k = 1, lineCount * 2 do repeat
                cbResultCard[cbResultCount][index + k] = result2[3][j][k]
                local v = self:getCardLogicValue(result2[3][j][k])
                if v == nil or v > 15 then break end
                tmp_num[v] = tmp_num[v] + 1
            until true
            end
            local is_add = true
            for k = 1, 15 do
                if tmp_num[k] > value_num[k] then
                    is_add = false
                    break
                end
            end
            if not is_add then
                cbResultCard[cbResultCount] = {}
                cbResultCardCount[cbResultCount] = 0
                break
            end
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        until true
        end
    until true
    end

    cbResultCount = cbResultCount - 1
    tagSearchCardResult[1] = cbResultCount
    --dump(tagSearchCardResult)
    return tagSearchCardResult
end

--连牌搜索
function GameLogic:SearchLineCardType(cbHandCardData, cbHandCardCount, cbReferCard, cbBlockCount, cbLineCount, bIsNeedBig)
    bIsNeedBig = bIsNeedBig or true
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--排序扑克
	local cbCardData = cbHandCardData
    GameLogic:SortCardList(cbCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount
    --连牌最少数
    local cbLessLineCount = 0
    if cbLineCount == 0 then
        if cbBlockCount == 1 then
            cbLessLineCount = 5
        elseif cbBlockCount == 2 then
            cbLessLineCount = 2
        elseif cbBlockCount == 3 then
            cbLessLineCount = 2
        else
            cbLessLineCount = 3
        end
    else
        cbLessLineCount = cbLineCount
    end
    --print("连牌最少数 " .. cbLessLineCount)
    local cbReferIndex = 3
    if cbReferCard ~= 0 then
        if (GameLogic:getCardLogicValue(cbReferCard)-cbLessLineCount) >= 2 then
            cbReferIndex = GameLogic:getCardLogicValue(cbReferCard)-cbLessLineCount+1+1

            if not bIsNeedBig then
                cbReferIndex = cbReferIndex - 1
            end
        end
    end 
    --超过A
    if cbReferIndex+cbLessLineCount > 15 then
        return tagSearchCardResult
    end
    --长度判断
    if cbHandCardCount < cbLessLineCount * cbBlockCount then
        return tagSearchCardResult
    end
   -- print("搜索顺子开始点 " .. cbReferIndex)
    local Distributing = GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    --搜索顺子
    local cbTmpLinkCount = 0
    local cbValueIndex=cbReferIndex
    local flag = false
    while cbValueIndex <= 13 do
        if cbResultCard[cbResultCount] == nil then
            cbResultCard[cbResultCount] = {}
        end
        if Distributing[2][cbValueIndex][6] < cbBlockCount then
            if cbTmpLinkCount < cbLessLineCount  then
                cbTmpLinkCount = 0
                flag = false
            else
                cbValueIndex = cbValueIndex - 1
                flag = true
            end
        else
            cbTmpLinkCount = cbTmpLinkCount + 1
            if cbLineCount == 0 then
                flag = false
            else
                flag = true
            end
        end
        if flag == true then
            flag = false
            if cbTmpLinkCount >= cbLessLineCount then
                --复制扑克
                local cbCount = 0
                local cbIndex=(cbValueIndex-cbTmpLinkCount+1)
                while cbIndex <= cbValueIndex do
                    local cbTmpCount = 0
                    local cbColorIndex=1
                    while cbColorIndex <= 4 do --在四色中取一个
                        local cbColorCount = 1
                        while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                            cbCount = cbCount + 1
                            cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                            tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                            cbTmpCount = cbTmpCount + 1
                            if cbTmpCount == cbBlockCount then
                                break
                            end
                            cbColorCount = cbColorCount + 1
                        end
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorIndex = cbColorIndex + 1
                    end
                    cbIndex = cbIndex + 1
                end
                tagSearchCardResult[2][cbResultCount] = cbCount
                cbResultCount = cbResultCount + 1
                if cbLineCount ~= 0 then
                    cbTmpLinkCount = cbTmpLinkCount - 1
                else
                    cbTmpLinkCount = 0
                end
            end
        end
        cbValueIndex = cbValueIndex + 1
    end

    --特殊顺子(寻找A)
    if cbTmpLinkCount >= cbLessLineCount-1 and cbValueIndex == 14 then
        --print("特殊顺子(寻找A)")
        if (Distributing[2][1][6] >= cbBlockCount) or (cbTmpLinkCount >= cbLessLineCount) then
            if cbResultCard[cbResultCount] == nil then
                cbResultCard[cbResultCount] = {}
            end
            --复制扑克
            local cbCount = 0
            local cbIndex=(cbValueIndex-cbTmpLinkCount)
            while cbIndex <= 13 do
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
                cbIndex = cbIndex + 1
            end
            --复制A
            if Distributing[2][1][6] >= cbBlockCount then
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][1][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(1,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
            end
            tagSearchCardResult[2][cbResultCount] = cbCount
            cbResultCount = cbResultCount + 1
        end
    end
    tagSearchCardResult[1] = cbResultCount - 1
    return tagSearchCardResult
end

--出牌搜索
function GameLogic:SearchOutCard(cbHandCardData,cbHandCardCount,cbTurnCardData,cbTurnCardCount) 
    print("出牌搜索")
    --dump(cbTurnCardData)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
    
	--排序扑克
	local cbCardData = cbHandCardData --= self:copyTab(cbHandCardData)
	local cbCardCount = cbHandCardCount
    GameLogic:SortCardList(cbCardData, cbCardCount, GameLogic.ST_ORDER)
    GameLogic:SortCardList(cbTurnCardData, cbTurnCardCount, GameLogic.ST_ORDER)
	
    --出牌分析
	local cbTurnOutType, cbStarLevel = GameLogic:GetCardType(cbTurnCardData, cbTurnCardCount)
	print("cbTurnCardData type", cbTurnOutType, cbStarLevel)
    if cbTurnOutType == GameLogic.CT_ERROR then --错误类型
        print("上家为空")
		--是否一手出完
		local type, lvl = GameLogic:GetCardType(cbCardData, cbCardCount)
		print("cbTurnCardData type", type, lvl)
        if type ~= GameLogic.CT_ERROR  then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = cbCardData
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        --如果最小牌不是单牌，则提取
        local cbSameCount = 1  
		if cbCardCount > 1 and (GameLogic:getCardLogicValue(cbCardData[cbCardCount]) == 
		GameLogic:getCardLogicValue(cbCardData[cbCardCount-1])) then
            cbSameCount = 2
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount][1] = cbCardData[cbCardCount]
            local cbCardValue = GameLogic:getCardLogicValue(cbCardData[cbCardCount])
            local i = cbCardCount - 1
            while i >= 1 do
                if GameLogic:getCardLogicValue(cbCardData[i]) == cbCardValue then
                    cbResultCard[cbResultCount][cbSameCount] = cbCardData[i]
                    cbSameCount = cbSameCount + 1
                end
                i = i - 1
            end
            cbResultCardCount[cbResultCount] = cbSameCount-1
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
		--单牌
		print("单牌", cbSameCount)
        local cbTmpCount = 1
        if cbSameCount ~= 2 then
            --print("单牌Pan")
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 1)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
		--对牌
        print("对牌", cbSameCount)
        local tagSearchCardResult2 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 2)
        if cbSameCount ~= 3 then
            cbTmpCount = tagSearchCardResult2[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult2[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult2[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
		--三条
        print("三条", cbSameCount)
        local tagSearchCardResult3 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 3)
        if cbSameCount ~= 4 then

            if tagSearchCardResult3[1] > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult3[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult3[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end

        --三带一对
        if cbSameCount ~= 5 then
            local tagSearchCardResult8 = GameLogic:SearchThreeDobuleSameCard(cbCardData,cbCardCount,0,0)
            dump(tagSearchCardResult8)
            if tagSearchCardResult8[1] > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult8[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult8[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
      
        --单连
        print("单连", cbSameCount)
        local tagSearchCardResult4 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 1, 5)
        cbTmpCount = tagSearchCardResult4[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult4[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult4[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end

        --连对
        print("连对", cbSameCount)
        local tagSearchCardResult5 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 2, 0)
        cbTmpCount = tagSearchCardResult5[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult5[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult5[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        --三连
        print("三连", cbSameCount)
        local tagSearchCardResult6 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 3, 0)
        cbTmpCount = tagSearchCardResult6[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult6[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult6[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        
		--炸弹
		print("炸弹", cbSameCount)
        if cbSameCount ~= 5 then
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 4)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end

        local tagSearchCardResult7 = GameLogic:SearchLineThreePairCardType(cbCardData,cbCardCount,0,0,0)
        cbTmpCount = tagSearchCardResult7[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult7[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult7[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end

        tagSearchCardResult[1] = cbResultCount - 1
        return tagSearchCardResult
    elseif cbTurnOutType == GameLogic.CT_SINGLE or cbTurnOutType == GameLogic.CT_DOUBLE or cbTurnOutType == GameLogic.CT_THREE then
        --单牌、对牌、三条
        local cbReferCard = cbTurnCardData[1]
        local cbSameCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE then
            cbSameCount = 2
        elseif cbTurnOutType == GameLogic.CT_THREE then
            cbSameCount = 3
        end
        print('cbReferCard is ', cbReferCard)
        local tagSearchCardResult21 = GameLogic:SearchSameCard(cbCardData, cbCardCount, cbReferCard, cbSameCount)
        cbResultCount = tagSearchCardResult21[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult21[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult21[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    elseif cbTurnOutType == GameLogic.CT_THREE_PAIR then
        local mid_v = self:getCardLogicValue(cbTurnCardData[3])
        local first_v = self:getCardLogicValue(cbTurnCardData[1])
        local double_card = cbTurnCardData[1]
        if first_v == mid_v then
            double_card = cbTurnCardData[4]
        end
        local tagSearchCardResult4 = GameLogic:SearchThreeDobuleSameCard(cbCardData, cbCardCount, cbTurnCardData[3], double_card)
        dump(tagSearchCardResult4)
        if tagSearchCardResult4[1] > 0 then
            cbResultCount = tagSearchCardResult4[1]
            cbResultCount = cbResultCount + 1
            cbResultCardCount = tagSearchCardResult4[2]
            cbResultCard = tagSearchCardResult4[3]
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[1] = cbResultCount - 1
        end
    elseif cbTurnOutType == GameLogic.CT_SHUNZI or cbTurnOutType == GameLogic.CT_DOUBLE_LINK or cbTurnOutType == GameLogic.CT_THREE_LINK then
        --单连、对连、三连
        local cbBlockCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE_LINK then
            cbBlockCount = 2
        elseif cbTurnOutType == GameLogic.CT_THREE_LINK then
            cbBlockCount = 3
        end
        local cbLineCount = cbTurnCardCount/cbBlockCount
        local tagSearchCardResult31 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, cbTurnCardData[1], cbBlockCount, cbLineCount)
        cbResultCount = tagSearchCardResult31[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult31[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult31[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    elseif cbTurnOutType == GameLogic.CT_THREE_PAIR_LINK then
        local cpCard = self:copyTab(cbTurnCardData)
        local result = GameLogic:SearchLineCardType(cpCard,cbTurnCardCount,0,3, cbTurnCardCount / 5)        
        local mid_v = self:getCardLogicValue(result[3][1][1])
        self:removeCard(result[3][1], cpCard)
        local twoCard = cpCard[#cpCard]
        print('line card type is ', mid_v, self:getCardLogicValue(twoCard))

        local tagSearchCardResult7 = GameLogic:SearchLineThreePairCardType(cbCardData,cbCardCount,result[3][1][1], twoCard, cbTurnCardCount / 5)
        cbTmpCount = tagSearchCardResult7[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult7[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult7[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
    elseif cbTurnOutType == GameLogic.CT_BOMB_LINK then
        print('CT_BOMB_LINK is ,', cbStarLevel, cbTurnCardCount / cbStarLevel)
        local result = GameLogic:SearchLineCardType(cbCardData, cbCardCount,cbTurnCardData[1], cbStarLevel, cbTurnCardCount / cbStarLevel )        
        dump(result)
        cbTmpCount = result[1]
        if cbTmpCount > 0 then
            cbResultCount = result[1]
            cbResultCount = cbResultCount + 1
            cbResultCardCount = result[2]
            cbResultCard = result[3]
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
            tagSearchCardResult[1] = cbResultCount - 1
        end
    end

    --搜索炸弹
	if (cbCardCount >= 4 and cbTurnOutType ~= GameLogic.CT_BOMB_TW and cbTurnOutType ~= GameLogic.CT_BOMB_LINK) then
        local cbReferCard = 0
        local card_num = 4
        if cbTurnOutType == GameLogic.CT_BOMB then
            cbReferCard = cbTurnCardData[1]
            card_num = cbTurnCardCount
		end
        
        --搜索炸弹
        local tagSearchCardResult61 = GameLogic:SearchSameCard(cbCardData,cbCardCount,cbReferCard, card_num)
        local cbTmpResultCount = tagSearchCardResult61[1]
        for i=1,cbTmpResultCount do
            cbResultCardCount[cbResultCount] = tagSearchCardResult61[2][i]
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = tagSearchCardResult61[3][i]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        end
        tagSearchCardResult[1] = cbResultCount - 1
    end

    --搜索天王炸弹
    if (cbTurnOutType ~= GameLogic.CT_BOMB_TW) and (cbCardCount >= 4) and (cbCardData[1]==0x4F and cbCardData[4]==0x4E) then
        cbResultCardCount[cbResultCount] = 4
        cbResultCard[cbResultCount] = {cbCardData[1],cbCardData[2], cbCardData[3],cbCardData[4] }
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    end

    return tagSearchCardResult
end

function GameLogic:removeCard(removeCards, cbCards)
    for i = 1, #removeCards do
        if removeCards[i] == 0 then
            break
        end
        for j = 1, #cbCards do
            if self:getCardLogicValue(cbCards[j])  == self:getCardLogicValue(removeCards[i]) then
                cbCards[j] = 0
                break
            end
        end
    end
    

    local len = #cbCards
    do 
        local i = 1
        while i <= len do
            if cbCards[i] == 0 then
                table.remove(cbCards, i)
                len = len - 1
            else
                i = i + 1
            end
        end
    end
    
   
    --print('after reomve card , len is ',  #self.cbCardData)
end

return GameLogic
-------------------------------------------------------------------------------
--  创世版1.0
--  欢迎界面
--      功能：本地版本记录读取，如无记录，则解压原始大厅及附带游戏
--      注意：此时client和game目录下的任何代码都还未导入，不能使用
--  @date 2017-06-26
--  @auth woodoo
-------------------------------------------------------------------------------
local ClientUpdate = appdf.req('client.src.app.controllers.ClientUpdate')
local QueryDialog = appdf.req('client.src.app.views.layer.other.QueryDialog')
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. 'MultiPlatform')
appdf.req('client.src.system.language')


if device.platform == 'windows' then
    --DEV_TEST = {1, 1}   -- 更新测试用，指定目标client资源版本和game资源版本
end


-- 全局toast函数(ios/android端调用)
cc.exports.g_NativeToast = function (msg)
    local runScene = cc.Director:getInstance():getRunningScene()
    if nil ~= runScene then
        showToastNoFade(runScene, msg, 2)
    end
end


local StartScene = class('StartScene', cc.load('mvc').ViewBase)


-------------------------------------------------------------------------------
-- onCreate
-------------------------------------------------------------------------------
function StartScene:onCreate()
    self:setTag(1)

    local main_node = cc.CSLoader:createNode('csb/Welcome.csb')
    self.main_node = main_node
    main_node:addTo(self)

    -- 健康游戏公告
    local label_health = ccui.Text:create( LANG.HEALTH_GAME, 'Arial', 14)
    label_health:setTextColor( cc.c3b(40, 40, 40) )
    label_health:pos(main_node:getContentSize().width/2, 12):addTo(main_node)

    -- 提示文本
    self.m_label_tip = main_node:getChildByName('label_tip')
    
    -- 版本
    main_node:getChildByName('label_ver'):setString( appdf.app:getVersion() )

    self.m_panel_loading = main_node:getChildByName('panel_loading'):hide()
    self.m_loading_bar = self.m_panel_loading:getChildByName('loading_bar')
    self:updateBar(0)

    -- 资源同步队列
    self.m_tabUpdateQueue = {}

    self:initVersion()

    --版本同步
    self:httpNewVersion()
end


-------------------------------------------------------------------------------
-- 初始化版本
-------------------------------------------------------------------------------
function StartScene:initVersion()
    if device.platform == 'windows' then
        local vm = self:getApp():getVersionManager()
        vm:setVersion( appdf.BASE_C_VERSION )
        vm:setResVersion( appdf.BASE_C_RESVERSION )
        for k ,v in pairs( appdf.BASE_GAME ) do
            vm:setResVersion(v.version, v.game)
        end
    end
end


-------------------------------------------------------------------------------
-- 进入登录界面
-------------------------------------------------------------------------------
function  StartScene:EnterClient()
    if self.m_need_restart then
        QueryDialog:create(LANG.START_RESTART, function(bReTry)
                MultiPlatform:getInstance():restartGame()
            end, nil, QueryDialog.QUERY_SURE
        ):addTo(self)

        return
    end
    --[[
    print('------------------------------------')
    local t = {}
    for k, v in pairs( package.loaded ) do
        if not k:find('cocos%.') then
            table.insert(t, k)
        end
    end
    table.sort(t)
    for k, v in ipairs(t) do print(v) end
    print('------------------------------------')
    --]]
    
    -- 清理以备重新载入  -----------------------------------------------------
    ------------------------------------------------------------------------
    -- MyApp不重建，但内部变量要重建
    self:getApp()._version:release()
    self:getApp()._version = nil

    -- 单例要单独清除
    MultiPlatform._instance = nil

    for k, v in pairs( package.loaded ) do
        if k:find('client%.src') == 1 or k:find('packages%.') == 1 or k:find('app%.models%.') == 1 then
            package.loaded[k] = nil
        end
    end

    -- 全局变量清理
    local app = appdf.app
    _G['yl'] = nil
    _G['appdf'] = nil
    _G['LANG'] = nil
    cc.FileUtils:getInstance():purgeCachedEntries() -- 重要：否则会从缓存的路径读取
    require('client.src.app.models.AppDF')
    appdf.req('client.src.system.language')
    appdf.app = app
    ------------------------------------------------------------------------

    -- yl直接重写载入，因为有值设置
    if self.m_login_server or self.m_login_port or self.m_host_url then
        appdf.req(appdf.CLIENT_SRC..'frame.yl')
        if self.m_login_server and self.m_login_server ~= '' then
            yl.LOGONSERVER = self.m_login_server
        end
        if self.m_login_port and self.m_login_port ~= '' then
            yl.LOGONPORT = self.m_login_port
        end
        if self.m_host_url and self.m_host_url ~= '' then
            yl.ADMIN_URL = self.m_host_url
        end
    end

    --场景切换
    --self:getApp():enterSceneEx(appdf.CLIENT_SRC..'relax.RelaxScene', 'FADE', 0.3)
    ---[[
    local server_config = self:getApp():getServerConfig()
    if server_config.is_audit and server_config.is_audit == 1 and server_config.is_relax == 1 then    -- 是否审核状态
        self:getApp():enterSceneEx(appdf.CLIENT_SRC..'relax.RelaxScene', 'FADE', 0.3)
    else
        self:getApp():enterSceneEx(appdf.CLIENT_SRC..'main.LoginScene','FADE',0.3)
    end
    --]]
end


-------------------------------------------------------------------------------
-- 连接web服务器，同步版本
-------------------------------------------------------------------------------
function StartScene:httpNewVersion()    
    self.m_label_tip:setString( LANG.START_CONNECT_WEB )
    local this = self

    --数据解析
    local vcallback = function(datatable, response)
        --dump(datatable, 'use/reg', 6)
        local code = 0
         local msg = ''
         if type(datatable) ~= 'table' then
            code = -1
            msg = LANG{'START_WEB_ERROR', code = code}
        elseif not datatable.code then
            code = -2
            msg = LANG{'START_WEB_ERROR', code = code}
        elseif datatable.code ~= 0 then
            code = datatable.code
            msg = datatable.msg
        else
            local databuffer = datatable.data
            self.m_need_restart = databuffer.is_restart == 1
            if not yl.IS_GAME_TOOL then
                if databuffer.host_url and yl.REDIRECTION_ADMIN_TIMES <= 0 then
                    yl.REDIRECTION_ADMIN_TIMES = 1
                    yl.ADMIN_URL = databuffer.host_url
                    self.m_host_url = databuffer.host_url
                    print('重新定向..')
                    self:httpNewVersion()
                    return
                end
            end
            if DEV_TEST then
                databuffer.res_version = DEV_TEST[1]
            end

            -- 可能存在服务器重定向
            --local is_reviewing = databuffer.is_audit and databuffer.is_audit == 1
            --if not is_reviewing then -- 审核模式不重定向
                self.m_login_server = databuffer.login_server
                self.m_login_port = databuffer.login_port
            --end
            dump(databuffer, 'databuffer', 6)
            local client_version = databuffer.client_version
             this:getApp()._serverConfig = databuffer
             this:getApp()._updateUrl = databuffer.download_url
            this:getApp()._updateContent = databuffer.update_content
             --大厅版本
             this._newVersion = client_version
             --大厅资源版本
             this._newResVersion = databuffer.res_version
             --大厅新包地址
             this.m_package_url = databuffer.package_url

             local nNewV = this._newResVersion
            local nCurV = tonumber(this:getApp():getVersionManager():getResVersion())
            if nNewV and nCurV and nNewV > nCurV then
                -- 更新配置
                 local updateConfig = {}
                updateConfig.isClient = true
                updateConfig.newfileurl = this:getApp()._updateUrl..'/client/res/filemd5List.json'
                updateConfig.downurl = this:getApp()._updateUrl .. '/'
                updateConfig.dst = device.writablePath
                local targetPlatform = cc.Application:getInstance():getTargetPlatform()
                if cc.PLATFORM_OS_WINDOWS == targetPlatform then
                    updateConfig.dst = device.writablePath .. 'download/'
                end                    
                updateConfig.src = 'client/res/filemd5List.json'    -- 相对路径，可以从搜索路径搜索（首次可以访问到包中）
                if device.platform == 'windows' then
                    updateConfig.src = device.writablePath..'ciphercode/client/res/filemd5List.json'
                end
                table.insert(this.m_tabUpdateQueue, updateConfig)
            end         

             -- 游戏列表
            local main_game_version = nil
            local main_game = appdf.BASE_GAME[1]
             this:getApp()._gameList = {}
             for k, v in ipairs(databuffer.gamelist) do
                 local gameinfo = {}
                 gameinfo._Module = v.module_name:lower()
                 gameinfo._ClientVersion = client_version
                 gameinfo._ServerResVersion = v.res_version
                 gameinfo._SortId = v.sort_id or 0
                
                if DEV_TEST then
                    gameinfo._ServerResVersion = DEV_TEST[2]
                end

                if main_game and main_game.game == gameinfo._Module then
                    main_game_version = gameinfo._ServerResVersion
                end

                 -- 检查包中和本地是否存在
                local base_path = 'game/' .. gameinfo._Module .. '/src/game.lua'
                gameinfo._Active = cc.FileUtils:getInstance():isFileExist(base_path)

                gameinfo.kindlist = v.kindlist

                 table.insert(this:getApp()._gameList, gameinfo)
             end

             table.sort( this:getApp()._gameList, function(a, b)
                 return a._SortId > b._SortId
             end)
            --dump(this:getApp()._gameList, 'gamelist', 6)

            -- 主游戏更新
             if main_game_version then
                 local game_name = main_game.game
                 local version = tonumber(this:getApp():getVersionManager():getResVersion(game_name))
                 if not version or main_game_version > version then
                     local updateConfig2 = {}
                    updateConfig2.isClient = false
                    updateConfig2.newfileurl = this:getApp()._updateUrl..'/game/'..game_name..'/res/filemd5List.json'
                    updateConfig2.downurl = this:getApp()._updateUrl .. '/game/'
                    updateConfig2.dst = device.writablePath .. 'game/'
                    if device.platform == 'windows' then
                        updateConfig2.dst = device.writablePath .. 'download/game/'
                    end                        
                    updateConfig2.src = 'game/'..game_name..'/res/filemd5List.json'
                    if device.platform == 'windows' then
                        updateConfig2.src = device.writablePath..'ciphercode/game/'..game_name..'/res/filemd5List.json'
                    end
                    updateConfig2._ServerResVersion = main_game_version
                    updateConfig2._Module = game_name
                    table.insert(this.m_tabUpdateQueue, updateConfig2)
                 end                         
             end
         end
         this.m_label_tip:setString('')
         this:httpNewVersionCallBack(code, msg)
    end

    appdf.req(appdf.CLIENT_SRC..'frame.yl')
    yl.GetUrl(yl.URL_REG, 'post', nil, vcallback)
end


-------------------------------------------------------------------------------
-- web服务器返回
-------------------------------------------------------------------------------
function StartScene:httpNewVersionCallBack(code, msg)
    local this = self
    
    if code < 0 then
        -- 获取失败
        QueryDialog:create( LANG{'START_RETRY_MSG', msg=msg}, function(bReTry)
            if bReTry == true then
                this:httpNewVersion()
            else
                os.exit(0)
            end
        end):addTo(self)

    elseif code > 0 then
        -- 阻止登录
        QueryDialog:create(msg, function(bReTry)
                os.exit(0)
            end, nil, QueryDialog.QUERY_SURE
        ):addTo(self)
        
    else
        --升级判断
        local bUpdate = false
        local targetPlatform = cc.Application:getInstance():getTargetPlatform()
        if DEV_TEST or cc.PLATFORM_OS_WINDOWS ~= targetPlatform then
            bUpdate = self:updateClient()
        else
            self:getApp()._version:setResVersion(self._newResVersion)
        end
        
        if not bUpdate then
            --进入登录界面
            self.m_label_tip:setString( LANG.START_COMPLETE )
            self:runMyAction(cc.Sequence:create(
                cc.DelayTime:create(1),
                cc.CallFunc:create(function()
                    this:EnterClient()
                end)
                ))
        end
    end
end


-------------------------------------------------------------------------------
-- 检查更新app或资源
-------------------------------------------------------------------------------
function StartScene:updateClient()
    local newV = self._newVersion
    local curV = appdf.BASE_C_VERSION

    if not newV then return false end

    if newV > curV then   --更新APP
        self.m_label_tip:setString('')
        local str = self:getApp()._updateContent
        if not str or str == '' then
            str = LANG.START_NEW_PACKAGE
        end
        QueryDialog:create( str, function(bConfirm)
            if bConfirm == true then
                if device.platform == 'ios' then
                    self:downAppFromBrowser()
                else
                    self:downAppInner()
                end
            else
                os.exit(0)
            end        
        end):addTo(self)

        return true

    elseif newV == curV then
        self.m_total_updates = #self.m_tabUpdateQueue
        if self.m_total_updates > 0 then
            self:goUpdate()
            return true
        end
    end

    print('version did not need to update')
    return false
end


-------------------------------------------------------------------------------
-- 更新app（弹窗下载地址）
-------------------------------------------------------------------------------
function StartScene:downAppFromBrowser()
    self.m_panel_loading:hide()
    self.m_label_tip:setAnchorPoint( cc.p(0.5, 0.5) )
    self.m_label_tip:setPositionX( display.cx )
    self.m_label_tip:setString( LANG.START_INSTALL_HINT )
    self.m_label_tip:runMyAction( cc.RepeatForever:create( cc.Blink:create(1, 1) ) )

    local layer = display.newLayer():addTo(self)
    layer:registerScriptKeypadHandler(function(event)
        if event == 'backClicked' then
            cc.Director:getInstance():endToLua()
        end
    end)
    layer:setKeyboardEnabled(true)
    MultiPlatform:getInstance():openBrowser(self.m_package_url)
end


-------------------------------------------------------------------------------
-- 更新app（内部下载）
-------------------------------------------------------------------------------
function StartScene:downAppInner()
    local apk_name = 'cs_client.apk'

    --调用C++下载
    local luaj = require('cocos.cocos2d.luaj')
    local className = 'org/cocos2dx/lua/AppActivity'

    local sigs = '()Ljava/lang/String;'
    local ok, ret = luaj.callStaticMethod(className,'getSDCardDocPath',{},sigs)
    if ok then
        local dstpath = ret .. '/update/'
        local filepath = dstpath .. apk_name
        if cc.FileUtils:getInstance():isFileExist(filepath) then
            cc.FileUtils:getInstance():removeFile(filepath)
        end
        if false == cc.FileUtils:getInstance():isDirectoryExist(dstpath) then
            cc.FileUtils:getInstance():createDirectory(dstpath)
        end

        self.m_panel_loading:show()
        self:updateBar(0)
        self.m_panel_loading:getChildByName('label_progress'):setString('1/1')

        local this = self
        local url = self.m_package_url
        downFileAsync(url, apk_name, dstpath, function(main, sub)
            if main == appdf.DOWN_PRO_INFO then --进度信息
                this:updateBar(sub)
            elseif main == appdf.DOWN_COMPELETED then --下载完毕
                this.m_panel_loading:hide()

                --安装apk                        
                local args = {filepath}
                local sigs = '(Ljava/lang/String;)V'
                local ok,ret = luaj.callStaticMethod(className, 'installClient', args, sigs)
                if ok then
                    os.exit(0)
                else
                    this:downAppFromBrowser()
                end
            else
                if this.m_inner_down_apk_fail_times and this.m_inner_down_apk_fail_times > 0 then
                    QueryDialog:create( LANG{'START_DOWN_BROWSER', code=main}, function(bReTry)
                        if bReTry == true then
                            this:downAppFromBrowser()
                        else
                            os.exit(0)
                        end
                    end)
                    :setCanTouchOutside(false)
                    :addTo(this)
                else
                    QueryDialog:create( LANG{'START_DOWN_FAIL', code=main}, function(bReTry)
                        if bReTry == true then
                            this.m_inner_down_apk_fail_times = (this.m_inner_down_apk_fail_times or 0 ) + 1
                            this:downAppInner()
                        else
                            os.exit(0)
                        end
                    end)
                    :setCanTouchOutside(false)
                    :addTo(this)
                end
            end
        end)
    else
        self:downAppFromBrowser()
    end        
end


-------------------------------------------------------------------------------
-- 逐个下载资源
-------------------------------------------------------------------------------
function StartScene:goUpdate( )
    self.m_panel_loading:show()

    local config = self.m_tabUpdateQueue[1]
    if nil == config then
        self.m_panel_loading:hide()
        self.m_label_tip:setString( LANG.START_COMPLETE )
        self:runMyAction(cc.Sequence:create(
                cc.DelayTime:create(1),
                cc.CallFunc:create(function()
                    self:EnterClient()
                end)
        ))
    else
        local text_tip = self.m_panel_loading:getChildByName('label_progress')
        if nil ~= text_tip then
            local str = string.format('%d/%d', self.m_total_updates - #self.m_tabUpdateQueue + 1, self.m_total_updates)
            text_tip:setString(str)
        end

        ClientUpdate:create(config.newfileurl, config.dst, config.src, config.downurl):doUpdate(self)
    end    
end


-------------------------------------------------------------------------------
-- 下载进度（ClientUpdate回调）
-------------------------------------------------------------------------------
function StartScene:updateProgress(sub, msg, mainpersent)
    self:updateBar(mainpersent)
end


-------------------------------------------------------------------------------
-- 下载结果（ClientUpdate回调）
-------------------------------------------------------------------------------
function StartScene:updateResult(result,msg)
    local this = self
    if result == true then
        self:updateBar(0)

        local config = self.m_tabUpdateQueue[1]
        if nil ~= config then
            if true == config.isClient then
                --更新本地大厅版本
                self:getApp()._version:setResVersion(self._newResVersion)
            else
                self:getApp()._version:setResVersion(config._ServerResVersion, config._Module)
                for k,v in pairs(self:getApp()._gameList) do
                    if v._Module == config._Module then
                        v._Active = true
                    end
                end
            end
            table.remove(self.m_tabUpdateQueue, 1)
            self:goUpdate()
        else
            --进入登录界面
            self.m_label_tip:setString( LANG.START_COMPLETE )
            self:runMyAction(cc.Sequence:create(
                    cc.DelayTime:create(1),
                    cc.CallFunc:create(function()
                        this:EnterClient()
                    end)
            ))    
        end
    else
        self.m_panel_loading:hide()
        self:updateBar(0)

        --重试询问
        self.m_label_tip:setString('')
        QueryDialog:create( LANG{'START_RETRY_MSG', msg=msg}, function(bReTry)
            if bReTry == true then
                this:goUpdate()
            else
                os.exit(0)
            end
        end):addTo(self)
    end
end


-------------------------------------------------------------------------------
-- 更新进度条
-------------------------------------------------------------------------------
function StartScene:updateBar(percent)
    print('percent:', percent)
    local bar = self.m_loading_bar
    bar:setPercent(percent)
    local size = bar:getVirtualRendererSize()
    local bar_x = bar:getPositionX()
end


return StartScene
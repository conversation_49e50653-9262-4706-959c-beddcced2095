-------------------------------------------------------------------------------
--  创世版1.0
--  比赛场
--  @date 2018-03-27
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')

-- 报名类型
local APPLY_TYPE_FREE = 0   -- 免费
local APPLY_TYPE_SHARE = 1  -- 成功分享朋友圈
local APPLY_TYPE_FANGKA = 2 -- 消耗房卡
local APPLY_TYPE_GOLD = 3   -- 成功分享朋友圈

-- 比赛状态
local ARENA_STATUS_APPLING    = 0 -- 报名中
local ARENA_STATUS_WAITING    = 1 -- 报名中
local ARENA_STATUS_GOING      = 2 -- 报名中

-- 比赛类型
local ARENA_TYPE_PLAYERS      = 0 -- 人满即开
local ARENA_TYPE_TIME         = 1 -- 定时比赛


local ArenaLayer = class("ArenaLayer", cc.Layer)
                                                    

-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ArenaLayer:ctor()
    self:setName('arena_layer')
    self:enableNodeEvents()
    self.m_in_background = false    -- 是否在后台
    self.m_histories = {}
    self.m_file_images = {}
    self.m_arena_nodes = {}

    -- 载入主UI
    local main_node = helper.app.loadCSB('ArenaLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    self.panel_main = main_node:child('panel_main'):show()      -- 主面板
    self.panel_info = main_node:child('panel_info'):hide()      -- 详情面板

    self.panel_main:child('row_template'):hide()
    self.m_cur_panel = self.panel_main

    main_node:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    main_node:child('btn_exchange'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnExchange) )
    main_node:child('btn_add_gold'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAddGold) )

    -- 详情页的Tab初始化
    local tabs = {self.panel_info:child('btn_reward,btn_rule')}
    for i, tab in ipairs(tabs) do
        tab.index = i
        tab.group = 1
        helper.logic.initImageCheck(tab, false, 'common/btn_tab_arena_s.png', 'common/btn_tab_arena_n.png', 
            handler(self, self.onBtnDetailTab))
    end
    tabs[1]:toggle()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ArenaLayer:onEnter()
    print('ArenaLayer:onEnter...')
    helper.app.addBackgroundCallback(self, self.onBackground)

    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_LIST, self, self.onArenaListResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_DESC, self, self.onDetailResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_APPLY, self, self.onApplyMatchResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_CANCEL_APPLY, self, self.onExitMatchResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_TABLE_ID, self, self.onMatchTableNotify)
    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_RESULT, self, self.onMatchResultNotify)
    LiveFrame:getInstance():addListen(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_RESULT_UNFINISHED, self, self.onMatchUnfinishNotify)

    -- 不停刷新房卡
    self:perform(handler(self, self.refreshGold), 5, -1, nil, true)
    -- 不停刷新比赛场（显示要考虑更新、删除、新增）
    self:perform(handler(self, self.requestArenas), 10, -1, nil, true)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ArenaLayer:onExit()
    print('ArenaLayer:onExit...')
    helper.app.removeBackgroundCallback(self)

    -- 告诉服务器离开比赛场
    self:sendLeave()

    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- onBackground
-------------------------------------------------------------------------------
function ArenaLayer:onBackground(is_foreground)
    print('ArenaLayer:onBackground...', is_foreground)
    if not is_foreground and not self.m_in_background then  -- 进入后台
        self:sendLeave()
    end
    self.m_in_background = not is_foreground
end


-------------------------------------------------------------------------------
-- 发送离开比赛场命令
-------------------------------------------------------------------------------
function ArenaLayer:sendLeave()
    print('ArenaLayer:sendLeave...')

    -- 告诉服务器离开比赛场
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_EXIT_UI)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 刷新金币显示
-------------------------------------------------------------------------------
function ArenaLayer:refreshGold()
    self.main_node:child('label_gold'):setString( GlobalUserItem.lUserScore )
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnBack(sender)
    if self.m_cur_panel == self.panel_main then
        self:removeFromParent()
        return
    end
    self.m_cur_panel:hide()
    self.m_cur_panel = self.panel_main:show()
end


-------------------------------------------------------------------------------
-- 加金币按钮点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnAddGold(sender)
    helper.link.toMall( LANG.MALL_TAB_GOLD )
end


-------------------------------------------------------------------------------
-- 兑换商城按钮点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnExchange(sender)
    helper.link.toMall( LANG.MALL_TAB_QUAN )
end


-------------------------------------------------------------------------------
-- 详情按钮点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnDetail(sender)
    self.m_cur_panel:hide()
    self.m_cur_panel = self.panel_info:show()

    self.panel_info:child('btn_reward'):toggle()
    self.panel_info:child('listview_reward'):show():removeAllItems()
    self.panel_info:child('listview_rule'):hide():removeAllItems()

    local arena = sender:getParent().arena
    self.m_cur_arena = arena
    if not arena.detail then
        self:requestDetail(arena)
    else
        self:showDetail(arena.detail)
    end
end


-------------------------------------------------------------------------------
-- 请求场次列表
-------------------------------------------------------------------------------
function ArenaLayer:requestArenas()
    if not self:isVisible() then return end
    if self.m_in_background then return end
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_LIST)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 场次列表返回
-------------------------------------------------------------------------------
function ArenaLayer:onArenaListResp(data)
    local arenas = LiveFrame:getInstance():resp(data, cmd_common.tagMatchInfo, true)
    if not arenas then return false end
    
    self.m_arenas = arenas
    self:showArenas(arenas)
end


-------------------------------------------------------------------------------
-- 显示场次
-------------------------------------------------------------------------------
function ArenaLayer:showArenas(arenas)
    local panel_main = self.main_node:child('panel_main')
    local listview = panel_main:child('listview')
    local template = panel_main:child('row_template')

    local items = listview:getItems()

    local new_map = {}
    for i, arena in ipairs(arenas) do
        new_map[arena.nID] = arena
        local item = self.m_arena_nodes[arena.nID]
        if not item then
            item = template:clone():show()
            self.m_arena_nodes[arena.nID] = item

            item:child('label_name'):setString( arena.szName )
            item:child('label_reward'):setString( arena.szDescShort )
            item:child('btn_detail'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDetail) )
            item:child('btn_apply_match'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnApplyMatch) )
            item:child('btn_exit_match'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnExitMatch) )

            listview:pushBackCustomItem(item)

            -- 下载logo
            self:downloadIcon(item, arena.szIcon, self.m_file_images)
        end
        item.arena = arena

        -- 报名人数
        item:child('label_applies'):setString( LANG{'ARENA_APPLY_PLAYERS', num=arena.nApplyPlayers} )

        -- 是否已经报名，控制按钮，报名费
        item:child('btn_apply_match'):setVisible( arena.nStatus == 0 and not arena.bSelfApply )
        item:child('btn_exit_match'):setVisible( arena.nStatus == 0 and arena.bSelfApply )

        if arena.bSelfApply or arena.nStatus ~= 0 then
            item:child('label_cost'):hide()
        else
            local str = ''
            if arena.nApplyType == APPLY_TYPE_FREE then
                str = LANG.ARENA_APPLY_FREE
            elseif arena.nApplyType == APPLY_TYPE_SHARE then
                str = LANG.ARENA_APPLY_SHARE
            elseif arena.nApplyType == APPLY_TYPE_FANGKA then
                str = LANG{'ARENA_APPLY_FANGKA', num=arena.nApplyPrice}
            elseif arena.nApplyType == APPLY_TYPE_GOLD then
                str = LANG{'ARENA_APPLY_GOLD', num=arena.nApplyPrice}
            end
            item:child('label_cost'):show():setString(str)
        end

        -- 按比赛状态
        local label_status = item:child('label_status'):stop()
        if arena.nStatus == ARENA_STATUS_APPLING then  -- 报名中
            if arena.nType == ARENA_TYPE_PLAYERS then
                label_status:setString( LANG{'ARENA_APPLY_MIN', num=arena.nMinPlayers} )

            elseif arena.nType == ARENA_TYPE_TIME then
                local start_time = arena.nGameClockTime
                local now_time = os.time()
                local delta = start_time - now_time
                if delta <= 2 * 3600 then   -- 2小时以内倒计时
                    label_status:perform(function()
                        local str = helper.time.intToCountDown(delta)
                        delta = math.max(0, delta - 1)
                        if delta == 0 then
                            label_status:stop():setString( LANG.ARENA_WAIT_MATCH )
                        else
                            label_status:setString(str)
                        end
                    end, 1, -1, nil, true)
                
                else
                    local t_start = os.date('*t', start_time)
                    local time_pre = LANG{'DAY_NAME', month=t_start.month, day=t_start.day}
                    local date_start = os.date('%Y-%m-%d', start_time)
                    local pre_names = {LANG.DAY_TODAY, LANG.DAY_TOMORROW, LANG.DAY_AFTER_TOMORROW}
                    for i, pre in ipairs(pre_names) do
                        if os.date('%Y-%m-%d', now_time + 86400 * (i - 1)) == date_start then
                            time_pre = pre
                            break
                        end
                    end
                    label_status:setString( LANG{'ARENA_START_AT', prefix=time_pre .. '\n', time=os.date('%H:%M', start_time)} )
                end
            end

        elseif arena.nStatus == ARENA_STATUS_WAITING then -- 等待开始
            label_status:setString( LANG.ARENA_WAITING )

        elseif arena.nStatus == ARENA_STATUS_GOING then -- 进行中
            label_status:setString( LANG.ARENA_GOING )
        end 
    end

    -- 删除已不存在项目
    for id, item in pairs(self.m_arena_nodes) do
        if not new_map[id] then
            self.m_arena_nodes[id] = nil
            listview:removeItem( listview:getIndex(item) )
        end
    end

    if #items == 0 then -- 原始没有，表示首次
        listview:jumpToTop()
    end
end


-------------------------------------------------------------------------------
-- 下载图标
-------------------------------------------------------------------------------
function ArenaLayer:downloadIcon(node, path, cache_table)
    node:child('img_logo'):hide()
    if not path or path == '' then return end

    local function add(node, image)
        image:pos(node:child('img_logo'):pos()):addTo(node)
        node:child('img_logo'):removeFromParent()
        image:setName('img_logo')
    end
    helper.app.addURLImage(self, cache_table or {}, 'arenalogo', path, node, add)
end


-------------------------------------------------------------------------------
-- 请求详情
-------------------------------------------------------------------------------
function ArenaLayer:requestDetail(arena)
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = arena.nID} )
	cmd_data:setcmdinfo(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_DESC)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 详情返回
-------------------------------------------------------------------------------
function ArenaLayer:onDetailResp(data)
    local detail = LiveFrame:getInstance():resp(data, cmd_common.tagMatchDesc)
    if not detail then return false end

    if self.m_cur_arena.nID == detail.nID then
        self.m_cur_arena.detail = detail
        self:showDetail(detail)
    end
end


-------------------------------------------------------------------------------
-- 显示详情
-------------------------------------------------------------------------------
function ArenaLayer:showDetail(detail)
    local listview_reward = self.panel_info:child('listview_reward')
    local listview_rule = self.panel_info:child('listview_rule')
    dump(detail)
    local size = listview_reward:size()
    size.width = size.width - 30
    size.height = 0

    local rich = cs.app.client('system.RichText').new(detail.szDescReward, 25, size, cs.app.FONT_NAME, {['='] = cc.c3b(156, 47, 0)})
    helper.layout.pushEmpty(listview_reward, cc.size(15, 15))
    listview_reward:pushBackCustomItem(rich)
    helper.layout.pushEmpty(listview_reward, cc.size(15, 15))
    listview_reward:jumpToTop()

    rich = cs.app.client('system.RichText').new(detail.szDescRule, 25, size, cs.app.FONT_NAME, {['='] = cc.c3b(156, 47, 0)})
    helper.layout.pushEmpty(listview_rule, cc.size(15, 15))
    listview_rule:pushBackCustomItem(rich)
    helper.layout.pushEmpty(listview_rule, cc.size(15, 15))
    listview_rule:jumpToTop()
end


-------------------------------------------------------------------------------
-- 详情Tab点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnDetailTab(sender)
    local index = sender.index
    local listview_reward = self.panel_info:child('listview_reward')
    local listview_rule = self.panel_info:child('listview_rule')
    listview_reward:setVisible( index == 1 )
    listview_rule:setVisible( index == 2 )
end


-------------------------------------------------------------------------------
-- 报名按钮点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnApplyMatch(sender)
    local arena = sender:getParent().arena
    local func = function()
	    local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = arena.nID} )
	    cmd_data:setcmdinfo(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_APPLY)
        LiveFrame:getInstance():send(cmd_data)
    end
    if arena.nApplyType == APPLY_TYPE_SHARE then
        local share = helper.pop.shareImage('common/share_image.jpg', false, false, 'share_arena_apply', func)
        share:hideHY()
    else
        func()
    end
end


-------------------------------------------------------------------------------
-- 报名返回
-------------------------------------------------------------------------------
function ArenaLayer:onApplyMatchResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDValueMsg)
    if not ret then return false end
    if ret.dwID > 0 then
        helper.pop.message( ret.szMsg )
    else
        self:refreshGold()
        local alert = helper.pop.alert( LANG.ARENA_APPLY_SUCC )
        alert:setName('arena_apply_succ_alert')
        self:requestArenas()
    end
end


-------------------------------------------------------------------------------
-- 退赛按钮点击
-------------------------------------------------------------------------------
function ArenaLayer:onBtnExitMatch(sender)
    local arena = sender:getParent().arena
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = arena.nID} )
	cmd_data:setcmdinfo(cmd_common.MDM_MATCH_SERVICE, cmd_common.SUB_MATCH_CANCEL_APPLY)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 退赛返回
-------------------------------------------------------------------------------
function ArenaLayer:onExitMatchResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDValueMsg)
    if not ret then return false end

    if ret.dwID > 0 then
        helper.pop.message( ret.szMsg )
    else
        self:refreshGold()
        helper.pop.message( LANG.ARENA_APPLY_E_SUCC )
        self:requestArenas()
    end
end


-------------------------------------------------------------------------------
-- 比赛开始通知
-------------------------------------------------------------------------------
function ArenaLayer:onMatchTableNotify(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDValue)
    if not ret then return false end

    local server_id = ret.dwID
    local table_id = ret.nValue

    -- 查找kind
    local kind = nil
    for i, arena in ipairs(self.m_arenas) do
        if arena.nServerID == server_id then
            kind = arena.wKindID
            break
        end
    end
    if not kind then
        helper.pop.message('Not found kind.')
        return
    end

    local enter_game = helper.app.getGameConfigByKind(kind)
    if not enter_game then
        helper.pop.message( LANG.NO_GAME_CONFIG )
        return
    end 

    helper.app.checkGameUpdate(enter_game._Module, kind, function()
        helper.app.removeFromScene('arena_wait_layer')
        helper.app.removeFromScene('arena_apply_succ_alert')
        helper.pop.popLayer(cs.app.CLIENT_SRC .. 'main.ArenaWaitLayer')
        PassRoom:getInstance():onLoginServer(server_id)
    end)
end


-------------------------------------------------------------------------------
-- 检查小结算以显示通知界面
-------------------------------------------------------------------------------
function ArenaLayer:checkGameResult(func)
    local game_result_layer = helper.app.getFromScene('subGameResultLayer')
    if game_result_layer then
        game_result_layer:addCloseHook(func)
    else
        func()
    end
end


-------------------------------------------------------------------------------
-- 比赛结果推送
-------------------------------------------------------------------------------
function ArenaLayer:onMatchResultNotify(data)
    local results = LiveFrame:getInstance():resp(data, cmd_common.MatchResultInfo, true)
    if not results then return false end

    self:checkGameResult(function()
        local path = cs.app.CLIENT_SRC .. 'main.ArenaResultLayer'
        helper.pop.popLayer(path, nil, {results})
    end)
end


-------------------------------------------------------------------------------
-- 比赛未结束推送
-------------------------------------------------------------------------------
function ArenaLayer:onMatchUnfinishNotify(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDValueMsg)
    if not ret then return false end

    self:checkGameResult(function()
        helper.pop.alert( LANG{'ARENA_UNFINISH', score=ret.nValue, arena=ret.szMsg} )
    end)
end


return ArenaLayer

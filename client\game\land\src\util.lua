-------------------------------------------------------------------------------
--  创世版1.0
--  当前游戏辅助方法类
--      访问方式：cs.game.util.
--  @date 2017-06-21
--  @auth woodoo
-------------------------------------------------------------------------------

local util = {}

-- 牌字相对于背景的偏移
local card_font_offset = {
    font_big        = {x=0, y=-12},
    font_middle     = {x=0, y=8},
    font_small      = {x=0, y=5},
    font_small_side = {x=0, y=7},
}

-- 财神标记按尺寸配置缩放和位置
local caishen_fonts = {
    font_big        = {1, cc.p(0, -1)},
    font_middle     = {0.75, cc.p(1, 13)},
    font_small      = {0.6, cc.p(2, 13)},
    font_small_side = {0.6, cc.p(0, 0)},
}

-- 财神标记按视角配置旋转和位置
local caishen_views = {
    [1] = {180, cc.p(39, 48)},
    [2] = {90, cc.p(-1, 48)},
    [3] = {0, cc.p(-3, 0)},
    [4] = {-90, cc.p(56, 15)},
}


-------------------------------------------------------------------------------
-- 拆分牌值为花色和值
-------------------------------------------------------------------------------
function util.splitCardValue(card_value)
    local value = math.mod(card_value, 16)
    local color = math.floor( card_value / 16 )
    return color, value
end


-------------------------------------------------------------------------------
-- 创建麻将牌
--  牌的种类：
--      自己的手牌
--      自己的堆牌
--      对家的堆牌（对家和自己的弃牌）
--      左右的堆牌（左右的弃牌）
--  card_value: 牌值，包含数值和花色，如果是table：{背景，牌值}
--  view_id: 视角索引，1上，2左，3下（自己），4右
--  sub_type: 当view_id为3时有效，'hand'手牌, 'pile'堆牌, 'out'弃牌
-------------------------------------------------------------------------------
function util.createCard(card_value, view_id, sub_type, show_caishen)
    local CARD_PATH = GlobalUserItem.getCardPath()    -- 当前玩法的资源路径
    local folder_name = (view_id == 2 or view_id == 4) and 'font_small_side' or 'font_small'
    if view_id == 3 then
        folder_name = sub_type == 'hand' and 'font_big' or (sub_type == 'pile' and 'font_middle' or 'font_small')
    end

    -- 牌底
    local card = nil
    if type(card_value) == 'table' then
        card = card_value[1]
        card_value = card_value[2]
    else
        card = display.newSprite( string.format('%s/%s/card_%s.png', CARD_PATH, folder_name, (view_id == 3 and sub_type == 'hand' and 'up' or 'down')) )
    end

    -- 牌值
    card.card_value = card_value

    -- 字体
    local size = card:size()
    local x = size.width/2 + card_font_offset[folder_name].x
    local y = size.height/2 + card_font_offset[folder_name].y + (view_id == 4 and 1 or 0)
    
    local color, value = util.splitCardValue(card_value)
    local path = CARD_PATH .. '/' .. folder_name .. '/font_' .. color .. '_' .. value .. '.png'
    local cmd = cs.app.game('room.CMD_Game')
    if color < 0 
    or value < 1
    or color > 4
    or value > 9
    or (color == 4 and value > 8)
    or (color == 3 and value > 7) 
    then
        print('牌不符合规则............', path)
        path = CARD_PATH .. '/' .. folder_name .. '/card_back.png'
    end
    local font = card:getChildByTag(1)
    if not font then
        font = display.newSprite(path):setTag(1):pos(x, y):addTo(card)
    else
        font:texture(path)
    end
    font:scale( (GlobalUserItem.nCardFontScale or 85) / 100 )

	if view_id == 4 then
		font:setRotation(180)
	end	

    -- 财神角标
    local flag = card:getChildByTag(2)
    local GameLogic = cs.app.game('room.GameLogic')
    if ( show_caishen or GameLogic.IsMagicCard( card_value ) ) and not flag then
        util.createCaishenFlag(folder_name, view_id):addTo(card)
    elseif not GameLogic.IsMagicCard( card_value ) and flag then
        flag:removeFromParent()
    end
    local cmd = cs.app.game('room.CMD_Game')
    local ting_flag = card:getChildByTag(3)
    if view_id == cmd.MY_VIEWID and sub_type == 'hand' then
        if not ting_flag then
            ting_flag = util.createTingFlag():addTo(card):hide()
            ting_flag:setTag(3)
            card.ting_flag = ting_flag    
        end
    end
    return card
end


-------------------------------------------------------------------------------
-- 创建财神标记
-------------------------------------------------------------------------------
function util.createCaishenFlag(folder_name, view_id)
    local scale, pos1 = unpack(caishen_fonts[folder_name])
    local rotation, pos2 = unpack(caishen_views[view_id])
    local offset = cc.pAdd(pos1, pos2)
    local flag = display.newSprite('room/icon_cai.png'):anchor(0,0):scale(scale):rotation(rotation):pos(offset):zorder(1):setTag(2)
    return flag
end

-------------------------------------------------------------------------------
-- 创建听牌标记
-------------------------------------------------------------------------------
function util.createTingFlag()
    local flag = display.newSprite('room/icon_ting.png'):pos(cc.p(56, 18)):zorder(1)
    return flag
end

-------------------------------------------------------------------------------
-- 设置庄标记
-------------------------------------------------------------------------------
function util.setZhuang(node, zhuang_chair_id, cur_chair, lian_zhuang)
    local game_config = cs.game[GlobalUserItem.nCurGameKind]
    if game_config.ZHUANG == true then
        local is_zhuang = cur_chair == zhuang_chair_id
        node:setVisible(is_zhuang)
        node:texture('room/icon_zhuang.png')
        local label_num = node:child('label_num')
        if lian_zhuang and lian_zhuang > 0 then
           label_num:show():setString('x'..lian_zhuang)
        else
           label_num:hide()
        end
    else
        node:hide()
    end
end


-------------------------------------------------------------------------------
-- 设置庄标记
-------------------------------------------------------------------------------
function util.setFeng(node, dir)
    local game_config = cs.game[GlobalUserItem.nCurGameKind]
    if game_config.FENG == true then
        -- 计算每个人相对于庄的位置，庄自己是1，其它是2，3，4
        node:show()
        node:texture('room/icon_direct_' .. dir .. '.png')
    else
        node:hide()
    end
end


return util
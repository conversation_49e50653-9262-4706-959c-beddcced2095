-------------------------------------------------------------------------------
--  创世版1.0
--  地区选择
--  @date 2018-04-23
--  @auth woodoo
-------------------------------------------------------------------------------
local district_data = cs.app.client('system.district')



-------------------------------------------------------------------------------
-- 地图类
-------------------------------------------------------------------------------
local MapLayer = class("MapLayer", cc.Layer)



-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function MapLayer:ctor(show_name, csb_name, click_callback, name_arr)
    self:enableNodeEvents()
    self.m_click_callback = click_callback
    self.m_name_arr = name_arr
    self.m_show_name = show_name

    -- 载入主UI
    local main_node = helper.app.loadCSB(csb_name)
    self.main_node = main_node
    main_node:addTo(self)

    self:initUI()
end


-------------------------------------------------------------------------------
-- 初始化UI
-------------------------------------------------------------------------------
function MapLayer:initUI()
    local main_node = self.main_node
    local bg = main_node:child('bg')
    local delay = self.m_show_name == '' and 0.3 or 0
    for i, node in ipairs(bg:getChildren()) do
        node:scale(0):runAction( cc.Sequence:create( cc.DelayTime:create(delay), cc.ScaleTo:create(0.2, 1) ) )

        local btn = node:child('btn')
        local label = btn:child('text')
        local arr = label:getString():split('|')
        if #arr > 1 then
            label:setString(arr[1])
            btn.link_name = arr[2]
        end
        btn:setTouchEnabled(true)
        btn:addTouchEventListener( helper.app.commClickHandler(self, self.onNodeClick) )

        local show_name = label:getString()
        for _, real_name in ipairs(self.m_name_arr) do
            if real_name:match(show_name) then
                btn.real_name = (self.m_show_name ~= '' and (self.m_show_name .. '-') or '') .. real_name
                break
            end
        end

        if not btn.real_name then
            print('error..............', self.m_show_name, show_name)
            btn:getParent():hide()
        elseif cs.app.DISTRICT_HIDE_UNCONIFG and cs.app.district_kinds then
            local kinds = cs.app.district_kinds
            local names = btn.real_name:split('-')
            if #names <= 2 then -- 只检查省市
                for i, name in ipairs(names) do
                    kinds = kinds[name]
                    if not kinds then
                        btn:getParent():hide()
                        break
                    end
                end
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 节点点击
-------------------------------------------------------------------------------
function MapLayer:onNodeClick(sender)
    if self.m_current_node then
        self.m_current_node:texture('common/icon_district_pos_active.png')
    end
    self.m_current_node = sender
    self.m_current_node:texture('common/icon_district_pos_current.png')
    self.m_click_callback(sender)
end


-------------------------------------------------------------------------------
-- 取消当前选中节点
-------------------------------------------------------------------------------
function MapLayer:unselect()
    if self.m_current_node then
        self.m_current_node:texture('common/icon_district_pos_active.png')
        self.m_current_node = nil
    end
end


-------------------------------------------------------------------------------
-- 初始化UI
-------------------------------------------------------------------------------
function MapLayer:effectShow()
    local main_node = self.main_node
    local bg = main_node:child('bg')
    for i, node in ipairs(bg:getChildren()) do
        node:scale(0):runAction( cc.Sequence:create( cc.DelayTime:create(0.3), cc.ScaleTo:create(0.2, 1) ) )
    end
end






local DistrictSelectLayer = class("DistrictSelectLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 静态方法：读取本地大厅游戏插件
-------------------------------------------------------------------------------
function DistrictSelectLayer.LoadDefaultPlugins()
    local district = cc.UserDefault:getInstance():getStringForKey('mydistrict', '')
    if yl.is_reviewing then
        district = ' | | '
    end
    if district ~= '' then
        if yl.is_reviewing and cs.app.district_kinds_review then
            cs.app.plugins = cs.app.district_kinds_review
        else
            cs.app.plugins = require(cs.app.CLIENT_SRC .. 'system.district_kinds').getKinds(unpack(district:split('|')))
        end
        return true
    else
        return false
    end
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function DistrictSelectLayer:ctor(callback)
    self:enableNodeEvents()
    self:setName('guide_layer')
    self.m_callback = callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('DistrictMain.csb')
    self.main_node = main_node
    self.label_title = self.main_node:child('label_title')
    self.panel_main = main_node:child('panel_main')
    self.panel_place = main_node:child('panel_place')
    self.panel_detail = self.panel_main:child('panel_detail')
    self.panel_detail:px(display.width + self.panel_detail:size().width)
    self.btn_enter = main_node:child('panel_main/btn_enter')
    self:addChild(main_node)
    self.label_title._origin_str = self.label_title:getString()

    main_node:child('panel_main'):hide()
    main_node:child('panel_main/panel_detail/template'):hide()
    main_node:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    self.btn_enter:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnEnter) )
    self:showBtnEnter(false)

    -- 载入初始地图
    local has_start = false
    if cs.app.DISTRICT_TOP then
        local arr = cs.app.DISTRICT_TOP:split('|')
        if #arr == 2 then
            sender = {link_name=arr[2], real_name=arr[1]}
            if district_data.cities[sender.real_name] then
                self:loadProvince(sender)
                has_start = true
            elseif district_data.districts[sender.real_name] then
                self:loadCity(sender)
                has_start = true
            end
        end
    end
    if not has_start then
        self:loadMap(nil, handler(self, self.onBtnProvince), district_data.provinces)
    else
        -- 不是从全国开始，加个底图，否则是纯黑背景
        local bg = display.newSprite('common/bg_country.jpg')
        bg:zorder(-1):pos(display.cx, display.cy):addTo(self)
    end
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function DistrictSelectLayer:onEnter()
    print('DistrictSelectLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function DistrictSelectLayer:onExit()
    print('DistrictSelectLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 载入地图
-------------------------------------------------------------------------------
function DistrictSelectLayer:loadMap(sender, click_callback, init_callback)
    local show_name = sender and sender.real_name or ''
    local csb_name = 'District' .. (sender and sender.link_name:ucfirst() or 'Country') .. '.csb'
    local map = MapLayer:create(show_name, csb_name, click_callback, init_callback)
    map:addTo( self.main_node:child('panel_place') )
    return map
end


-------------------------------------------------------------------------------
-- 载入省地图
-------------------------------------------------------------------------------
function DistrictSelectLayer:loadProvince(sender)
    return self:loadMap(sender, handler(self, self.onBtnDistrict), district_data.cities[sender.real_name])
end


-------------------------------------------------------------------------------
-- 载入市地图
-------------------------------------------------------------------------------
function DistrictSelectLayer:loadCity(sender)
    return self:loadMap(sender, handler(self, self.onBtnDetail), district_data.districts[sender.real_name])
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function DistrictSelectLayer:onBtnBack()
    local children = self.panel_place:getChildren()
    local count = #children
    if count > 1 then
        children[count]:removeFromParent()
        local panel = children[count - 1]
        panel:unselect()
        local title = count == 2 and self.label_title._origin_str or panel.m_show_name
        self.label_title:setString(title)
        self.panel_main:hide()
        self.panel_detail:px(display.width + self.panel_detail:size().width)
        self:showBtnEnter(false)
    elseif self.m_is_scene then
        helper.pop.message( LANG.SELECT_DISTRICT )
    else
        self:removeFromParent()
    end
end


-------------------------------------------------------------------------------
-- 进入游戏按钮点击
-------------------------------------------------------------------------------
function DistrictSelectLayer:onBtnEnter()
    if not self.m_names then return end

    cc.UserDefault:getInstance():setStringForKey("mydistrict", table.concat(self.m_names, '|'))
    cs.app.plugins = require(cs.app.CLIENT_SRC .. 'system.district_kinds').getKinds(unpack(self.m_names))
    dump(cs.app.plugins)
    self.m_callback()
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 设置进入按钮
-------------------------------------------------------------------------------
function DistrictSelectLayer:showBtnEnter(visible)
    if visible then
        self.btn_enter:show()
        self.btn_enter:setTouchEnabled(true)
        self.btn_enter:setColor( cc.c3b(255, 255, 255) )
    else
        self.btn_enter:hide()
        self.btn_enter:setTouchEnabled(false)
        self.btn_enter:setColor( cc.c3b(120, 120, 120) )
    end
end


-------------------------------------------------------------------------------
-- 县选择
-------------------------------------------------------------------------------
function DistrictSelectLayer:onBtnDetail(sender)
    self:showBtnEnter(true)
    self.m_names = sender.real_name:split('-')
    self.main_node:child('panel_main'):show()
end


-------------------------------------------------------------------------------
-- 省点击
-------------------------------------------------------------------------------
function DistrictSelectLayer:onBtnProvince(sender)
    local province_name = sender.real_name
    if not province_name then return end

    self.m_names = nil
    self.label_title:setString(province_name)
    self:loadProvince(sender)
end


-------------------------------------------------------------------------------
-- 市点击
-------------------------------------------------------------------------------
function DistrictSelectLayer:onBtnDistrict(sender)
    local district_name = sender.real_name
    if not district_name then return end

    self.m_names = nil
    self.label_title:setString(district_name)
    local map = nil
    if sender.link_name then
        map = self:loadCity(sender)
    end
    if map then
        self:closeDetails()
        self:showBtnEnter(false)
    else
        self:showDetails(district_name)
    end
end


-------------------------------------------------------------------------------
-- 显示市所属县列表
-------------------------------------------------------------------------------
function DistrictSelectLayer:showDetails(district_name)
    self:showBtnEnter(false)
    local panel_main = self.main_node:child('panel_main'):show()
    local panel_detail = panel_main:child('panel_detail')

    --print('show district ... ', district_name)
    -- 找到下属县级市
    local districts = district_data.districts[district_name]
    if not districts then
        self:closeDetails()
        return
    end

    self.btn_enter:show()
    self.btn_enter:setTouchEnabled(false)
    self.btn_enter:setColor( cc.c3b(120, 120, 120) )

    local template, listview = panel_detail:child('template, listview')
    local size = template:size()
    listview:removeAllItems()
    helper.layout.pushEmpty(listview, cc.size(5, 1))
    for i, dist in ipairs(districts) do
        local btn = template:clone():show()
        local label = btn:child('text')
        btn.group = 1
        btn.real_name = district_name .. '-' .. dist
        btn._uncheck_color = label:getTextColor()
        helper.logic.initImageCheck(btn, false, 'common/btn_district_detail_s.png', 'common/btn_district_detail_n.png', 
            handler(self, self.onBtnDetail))
        listview:pushBackCustomItem(btn)
        
        label:setString(dist)
        if label:size().width + 10 > size.width then
            helper.layout.scaleToWidth(label, size.width - 10)
        end
    end
    helper.layout.pushEmpty(listview, cc.size(5, 1))

    if panel_detail:px() > display.width then
        panel_detail:stop():runAction(
            cc.MoveTo:create(0.2, cc.p(display.width, 0))
        )
    end
end


-------------------------------------------------------------------------------
-- 关闭详情列表
-------------------------------------------------------------------------------
function DistrictSelectLayer:closeDetails()
    local panel_detail = self.main_node:child('panel_main/panel_detail')
    panel_detail:stop():runAction( cc.MoveTo:create(0.2, cc.p(display.width + panel_detail:size().width, 0)) )
end


return DistrictSelectLayer

-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 我的红包
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbMy = class('OrbMy', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbMy:ctor(panel)
    print('OrbMy:ctor...')
    self.super.ctor(self, panel)

    panel:child('panel_open/template_row'):hide()
    panel:child('panel_draw/template_row'):hide()

    self.m_panel:child('btn_left'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnTab) )
    self.m_panel:child('btn_right'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnTab) )

    self:onBtnTab(self.m_panel:child('btn_left'))
end


-------------------------------------------------------------------------------
-- 显示事件
-------------------------------------------------------------------------------
function OrbMy:onShow()
    local listview = self.m_panel:child('panel_open/listview')
    listview:removeAllItems()
    local template = self.m_panel:child('panel_open/template_row')
    local open_logs = self:getOpenLogs()
    for i, log in ipairs(open_logs) do
        local item = template:clone():show()
        item:child('label_name'):setString(log.from_nickname)
        item:child('label_time'):setString(log.create_time:sub(1, #log.create_time - 3))
        item:child('label_amount'):setString( LANG{'ORB_AMOUNT', amount = log.total_fee} )

        local method = LANG['ORB_METHOD_' .. string.upper(log.type)]
        item:child('label_method'):setString(method)

        local icon = item:child('bg_avatar')
        local head = self:createHead(log.from_id, log.from_avatar, icon:size().width, 'orb/orb_bg_avatar_46.png')
        head:pos(icon:pos()):addTo(item)
        icon:hide()

        listview:pushBackCustomItem(item)
    end

    listview = self.m_panel:child('panel_draw/listview')
    listview:removeAllItems()
    template = self.m_panel:child('panel_draw/template_row')
    local draw_logs = self:getDrawLogs()
    for i, log in ipairs(draw_logs) do
        local item = template:clone():show()
        item.log = log
        item:child('label_time'):setString(log.create_time:sub(1, #log.create_time - 3))
        item:child('label_amount'):setString( LANG{'ORB_AMOUNT', amount = log.total_fee} )

        item:child('btn_draw'):setVisible(log.status == 1)
        item:child('label_status'):setVisible(log.status ~= 1)
        if log.status == 1 then
            item:child('btn_draw'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDraw) )
        else
            local status = LANG['ORB_DRAW_STATUS_' .. log.status]
            item:child('label_status'):setString(status)
        end

        listview:pushBackCustomItem(item)
    end
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbMy:onBtnTab(sender)
    local btn_left = self.m_panel:child('btn_left')
    local btn_right = self.m_panel:child('btn_right')
    local panel_open = self.m_panel:child('panel_open')
    local panel_draw = self.m_panel:child('panel_draw')
    local arr = {{btn_left, panel_open}, {btn_right, panel_draw}}
    for i, item in ipairs(arr) do
        local btn, panel = unpack(item)
        local selected = btn == sender
        if selected then
            btn:child('bg'):stop():runAction(cc.FadeIn:create(0.3))
        else
            btn:child('bg'):stop():runAction(cc.FadeOut:create(0.3))
        end
        panel:setVisible(selected)
    end
end


-------------------------------------------------------------------------------
-- 去领取按钮点击
-------------------------------------------------------------------------------
function OrbMy:onBtnDraw(sender)
    local item = sender:getParent()
    local log = item.log
    self:request('reward', {id = log.id}, function(data, response, http_status)
        if not helper.app.urlErrorCheck(data, response, http_status) then
            return
        end
        log.status = 2
        
        item:child('btn_draw'):hide()
        item:child('label_status'):show()
        local status = LANG['ORB_DRAW_STATUS_' .. log.status]
        item:child('label_status'):setString(status)

        helper.pop.message(LANG.ORB_DRAW_SUCC)
    end)
end


return OrbMy
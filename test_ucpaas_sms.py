#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import random
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_ucpaas_sms():
    """测试云之讯短信服务"""
    
    print("=== 云之讯短信服务测试 ===")
    
    # 云之讯短信配置（从 user.php 中提取）
    accountsid = '5cbc351f17a418686094747a62ffd946'
    token = 'fac1c2af0c05327677d33916ba841079'
    appId = '9dc983c028e54d5fbc97228e6af5344e'
    templateId = '174333'
    
    # 测试手机号（请替换为您的真实手机号）
    phone = '***********'
    
    # 生成验证码
    code = random.randint(1000, 9999)
    
    print(f"配置信息:")
    print(f"AccountSID: {accountsid}")
    print(f"AppID: {appId}")
    print(f"TemplateID: {templateId}")
    print(f"测试手机号: {phone}")
    print(f"验证码: {code}")
    print()
    
    # 云之讯API地址
    url = 'https://open.ucpaas.com/ol/sms/sendsms'
    
    # 构建请求数据
    data = {
        'sid': accountsid,
        'token': token,
        'appid': appId,
        'templateid': templateId,
        'param': str(code),
        'mobile': phone,
        'uid': ''
    }
    
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
    print()
    
    try:
        print("开始发送短信...")
        
        # 发送HTTP请求
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'LHMJ-SMS-Test/1.0'
        }
        
        response = requests.post(
            url, 
            json=data, 
            headers=headers,
            timeout=30,
            verify=False  # 忽略SSL证书验证（仅用于测试）
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        print()
        
        if response.status_code == 200:
            try:
                result_obj = response.json()
                print("=== 解析响应 ===")
                
                if 'resp' in result_obj:
                    resp_code = result_obj['resp'].get('respCode', '')
                    resp_desc = result_obj['resp'].get('respDesc', '')
                    
                    print(f"响应代码: {resp_code}")
                    print(f"响应描述: {resp_desc}")
                    
                    if resp_code == '000000':
                        print("✅ 短信发送成功！")
                        return True
                    else:
                        print("❌ 短信发送失败！")
                        print(f"错误代码: {resp_code}")
                        print(f"错误描述: {resp_desc}")
                        
                        # 分析常见错误
                        analyze_error_code(resp_code)
                        
                        return False
                else:
                    print("❌ 响应格式错误")
                    print(f"完整响应: {result_obj}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                return False
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False

def analyze_error_code(resp_code):
    """分析错误代码"""
    error_codes = {
        '160001': '账户余额不足',
        '160002': '账户状态异常',
        '160003': '手机号格式错误',
        '160004': '模板不存在或未审核',
        '160005': '签名错误',
        '160006': 'IP白名单限制',
        '160007': '发送频率限制',
        '160008': '内容包含敏感词',
        '160009': '模板参数错误',
        '160010': '账户权限不足',
        '160011': '短信通道异常',
        '160012': '手机号在黑名单中',
        '160013': '同一手机号发送过于频繁',
        '160014': '短信内容超长',
        '160015': '账户已停用',
    }
    
    if resp_code in error_codes:
        print(f"💡 错误分析: {error_codes[resp_code]}")
        
        if resp_code == '160001':
            print("   建议: 请充值账户余额")
        elif resp_code == '160004':
            print("   建议: 检查模板ID是否正确，确认模板已审核通过")
        elif resp_code == '160006':
            print("   建议: 将服务器IP添加到云之讯白名单")
        elif resp_code == '160007' or resp_code == '160013':
            print("   建议: 减少发送频率，等待一段时间后重试")
    else:
        print(f"💡 未知错误代码: {resp_code}")

def test_alternative_sms_config():
    """测试备用短信配置"""
    print("\n=== 测试备用短信配置 ===")
    
    # 从代码中看到的备用配置
    alt_accountsid = '9dc983c028e54d5fbc97228e6af5344e'
    alt_appId = 'a32820fbe04c40be9168d32bf1761579'
    
    print(f"备用 AccountSID: {alt_accountsid}")
    print(f"备用 AppID: {alt_appId}")
    print("这些配置在代码中被注释掉了，可能是旧的配置")

def print_troubleshooting_guide():
    """打印故障排除指南"""
    print("\n=== 故障排除指南 ===")
    print("1. 检查云之讯账户状态:")
    print("   - 登录云之讯控制台")
    print("   - 查看账户余额")
    print("   - 确认账户状态正常")
    print()
    print("2. 验证配置信息:")
    print("   - AccountSID 是否正确")
    print("   - Token 是否正确") 
    print("   - AppID 是否正确")
    print("   - 模板ID 是否正确且已审核")
    print()
    print("3. 检查网络和权限:")
    print("   - 服务器IP是否在白名单中")
    print("   - 网络是否能访问云之讯API")
    print("   - 防火墙是否阻止了请求")
    print()
    print("4. 检查发送频率:")
    print("   - 避免短时间内频繁发送")
    print("   - 同一手机号限制发送频率")
    print()
    print("5. 联系技术支持:")
    print("   - 如果问题持续，联系云之讯技术支持")
    print("   - 提供错误代码和详细日志")

if __name__ == '__main__':
    success = test_ucpaas_sms()
    
    if not success:
        test_alternative_sms_config()
        print_troubleshooting_guide()
        
        print("\n=== 下一步建议 ===")
        print("1. 如果是账户问题，联系云之讯客服")
        print("2. 如果是配置问题，检查服务端配置文件")
        print("3. 如果是网络问题，检查服务器网络设置")
        print("4. 考虑更换短信服务商（如阿里云、腾讯云）")
    else:
        print("\n🎉 短信服务正常！问题可能在服务器端的其他地方")
        print("建议检查:")
        print("1. 数据库连接是否正常")
        print("2. PHP环境是否正常")
        print("3. Web服务器配置是否正确")

-------------------------------------------------------------------------------
--  创世版1.0
--  设置
--  @date 2017-06-03
--  @auth woodoo
-------------------------------------------------------------------------------
local WechatUtil = appdf.req(appdf.CLIENT_SRC..'system.WechatUtil')


local SettingLayer = class("SettingLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function SettingLayer:ctor(external_class)
    print('SettingLayer:ctor...')
    self:enableNodeEvents()
    self.m_is_peiyin = GlobalUserItem.bVoiceAble

    self.m_old_scale = GlobalUserItem.nCardFontScale
    self.m_old_cloth = GlobalUserItem.nTablecloth

    -- 载入主UI
    local main_node = helper.app.loadCSB('SettingLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    self.panel_sound = main_node:child('panel_sound')
    self.panel_peiyin = main_node:child('panel_peiyin')
    self.panel_bg = main_node:child('panel_bg'):hide()
    self.panel_peiyin:child('peiyin_template'):hide()
    self.panel_bg:child('bg_template'):hide()
    self.sp_select = self.panel_bg:child('sp_select'):hide()

    if external_class then
        self:createExternal(external_class)
    else
        self.panel_bg:show()
        self:initBgList()
    end

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self, handler(self, self.onBtnBack))

    helper.logic.addListenerByName(self, {self.panel_sound:child('btn_peiyin')}, {'tint'})
    helper.logic.addListenerByName(self, {main_node:child('btn_toggle')})
    helper.logic.addListenerByName(self, {main_node:child('label_service,label_private')}, {'tint'})

    self.panel_sound:child('slider_music'):onEvent(handler(self, self.onSliderMusic))
    self.panel_sound:child('slider_effect'):onEvent(handler(self, self.onSliderEffect))
    self.panel_sound:child('slider_music'):setPercent(GlobalUserItem.nMusic)
    self.panel_sound:child('slider_effect'):setPercent(GlobalUserItem.nSound)

    self:toggleCheck( self.panel_sound:child('btn_peiyin'), self.m_is_peiyin )

    self:initPeiyinList()

    local str = appdf.app:getVersion()
    if not yl.is_reviewing then
        if yl.LOGONSERVER ==  '120.55.188.19' then
            if yl.LOGONPORT == 8601 then
                str = str..'    S1'
            elseif yl.LOGONPORT == 8602 then
                str = str..'    S2'
            else
                str = str..'    TestServer'
            end
        elseif yl.LOGONSERVER ==  'lhmj.tuo3.com.cn' or yl.LOGONSERVER == '101.37.125.116' then
        else
            str = str..'    OtherServer'
        end
    end
    main_node:child('label_version'):setString(str)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function SettingLayer:onEnterTransitionFinish()
    print('SettingLayer:onEnterTransitionFinish...')

    local callback = function(code, msg, result)
        if code < 0 then
            helper.pop.message(ms)
        else
            self:onSubPeiyinResp(result)
        end
    end
	self.m_frame = helper.app.createFrame(self, callback)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function SettingLayer:onExit()
    print('SettingLayer:onExit...')
    helper.app.removeFrame(self.m_frame)
end


-------------------------------------------------------------------------------
-- 返回按钮
-------------------------------------------------------------------------------
function SettingLayer:onBtnBack(sender)
    if self.m_external_node and self.m_external_node.onCloseCallback then
        self.m_external_node:onCloseCallback()
    end
    self:removeFromParent()
end

-------------------------------------------------------------------------------
-- 服务协议点击
-------------------------------------------------------------------------------
function SettingLayer:onLabelService(sender)
    self:showGamePolicy('service')
end


-------------------------------------------------------------------------------
-- 隐私政策点击
-------------------------------------------------------------------------------
function SettingLayer:onLabelPrivate(sender)
    self:showGamePolicy('private')
end


-------------------------------------------------------------------------------
-- 显示隐私政策(name为nil显示同意不同意选择窗口，否则显示对应的文本)
-------------------------------------------------------------------------------
function SettingLayer:showGamePolicy(name)
    local path = cs.app.CLIENT_SRC .. 'main.GamePolicyLayer'
    helper.pop.popLayer(path, nil, {function() end, name}, nil, true)
end


-------------------------------------------------------------------------------
-- 创建外部UI
-------------------------------------------------------------------------------
function SettingLayer:createExternal(external_class)
    local panel_place = self.main_node:child('panel_place')
    local node = require(external_class):create()
    node:pos(panel_place:pos()):addTo(self.main_node)
    self.m_external_node = node
end


-------------------------------------------------------------------------------
-- 初始化背景选择列表
-------------------------------------------------------------------------------
function SettingLayer:initBgList()
    local default = GlobalUserItem.nBackground
    local default_item
    local listview, template, sp_select = self.panel_bg:child('listview_bg, bg_template, sp_select')
    local index = 0
    local l_size = listview:size()
    local t_size = template:size()
    local row = nil
    for i = 1, 20 do
        local path = 'common/icon_bg_' .. i .. '.png'
        if cc.FileUtils:getInstance():isFileExist(path) then
            index = index + 1
            if index % 2 == 1 then
                row = ccui.Layout:create()
                row:size(l_size.width, t_size.height)
                listview:pushBackCustomItem(row)
            end
            local item = template:clone():show()
            item.index = i
            item:child('bg'):texture(path)
            item:child('text'):setString(LANG['BACKGROUND_' .. i] or '')
            item:addTouchEventListener( helper.app.tintClickHandler(self, self.onBgSelect) )
            local col = (index - 1) % 2
            item:pos(col * t_size.width , 0):addTo(row)

            if i == default then
                default_item = item
            end
        end
    end

    if default_item then
        self:onBgSelect(default_item, nil, true)
    end
end


-------------------------------------------------------------------------------
-- 背景项目选择
-------------------------------------------------------------------------------
function SettingLayer:onBgSelect(sender, event, no_save)
    local sp_select = self.sp_select:show()
    sp_select:retain()
    sp_select:removeFromParent()
    helper.layout.addCenter(sender:child('bg'), sp_select)
    sp_select:child('arrow'):stop():scale(0):runAction(cc.EaseBackOut:create(cc.ScaleTo:create(0.2, 1)))
    sp_select:release()
    if not no_save then
        GlobalUserItem.setBackground(sender.index)
        local main_scene = helper.app.getFromScene('main_scene')
        if main_scene and main_scene.changeBackground then
            main_scene:changeBackground(sender.index)
        end
    end
end


-------------------------------------------------------------------------------
-- 初始化配音列表
-------------------------------------------------------------------------------
function SettingLayer:initPeiyinList()
    local default_index = GlobalUserItem.wPeiyin + 1
    local listview = self.panel_peiyin:child('listview_peiyin')
    local template = self.panel_peiyin:child('peiyin_template')
    for i, name in ipairs( cs.app.PEIYINS ) do
        local item = template:clone():show()
        item.index = i
        item.group = 1
        item:child('text'):setString(name)
        item._uncheck_color = cc.c3b(100, 100, 100)
        helper.logic.initImageCheck(item, item.index == default_index, 'common/btn_check_on.png', 'common/btn_check_off.png', 
            handler(self, self.onPeiyinItemClick))
        listview:pushBackCustomItem(item)
    end
    template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 配音项目点击
-------------------------------------------------------------------------------
function SettingLayer:onPeiyinItemClick(sender)
    GlobalUserItem.wPeiyin = sender.index - 1
    self.m_frame:onSetPeiyin(GlobalUserItem.wPeiyin)

    -- 如果是游戏内，还要发送一个
    if cs.app.room_frame:isSocketServer() then
        cs.app.room_frame:SendPeiyin(GlobalUserItem.wPeiyin)
    end
end


-------------------------------------------------------------------------------
-- 配音设置返回
-------------------------------------------------------------------------------
function SettingLayer:onSubPeiyinResp(resp)
    print('Peiyin set success...', resp.wPeiyin)
end


-------------------------------------------------------------------------------
-- checkbox处理
-------------------------------------------------------------------------------
function SettingLayer:toggleCheck(img, checked)
    local pic = 'common/btn_toggle_' .. (checked and 'on' or 'off') .. '.png'
    img:texture(pic)
end


-------------------------------------------------------------------------------
-- 音乐音量变化
-------------------------------------------------------------------------------
function SettingLayer:onSliderMusic(event)
    if event.name ~= 'ON_PERCENTAGE_CHANGED' then return end
    local old_value = GlobalUserItem.nMusic
    local new_value = event.target:getPercent()
	GlobalUserItem.setMusicVolume(new_value)

    -- 因windows下音量无效，单独处理
    if device.platform == 'windows' and old_value ~= new_value then
        GlobalUserItem.setMusicAble(new_value > 0)
        if old_value == 0 and new_value > 0 then
            local game_layer = helper.app.getFromScene('game_room_layer')
            if game_layer then
                helper.music.playRoom()
            else
                helper.music.playPlaza()
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 音效音量变化
-------------------------------------------------------------------------------
function SettingLayer:onSliderEffect(event)
    if event.name ~= 'ON_PERCENTAGE_CHANGED' then return end
    local new_value = event.target:getPercent()
	GlobalUserItem.setEffectsVolume(new_value)

    -- 因windows下音量无效，单独处理
    if device.platform == 'windows' then
        GlobalUserItem.setSoundAble(new_value > 0)
    end
end


-------------------------------------------------------------------------------
-- 配音按钮点击
-------------------------------------------------------------------------------
function SettingLayer:onBtnPeiyin(sender)
    self.m_is_peiyin = not self.m_is_peiyin
    self:toggleCheck(sender, self.m_is_peiyin)
	GlobalUserItem.setVoiceAble(self.m_is_peiyin)
end


-------------------------------------------------------------------------------
-- 切换账号
-------------------------------------------------------------------------------
function SettingLayer:onBtnToggle(sender)
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    MultiPlatform:getInstance():thirdPartyLogout(yl.ThirdParty.WECHAT)
    WechatUtil.removeToken()
    yl.app:enterSceneEx(appdf.CLIENT_SRC.."main.LoginScene", "FADE", 0.3)
end


return SettingLayer
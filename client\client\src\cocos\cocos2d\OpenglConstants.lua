
if not gl then return end

gl.GCCSO_SHADER_BINARY_FJ   = 0x9260
gl._3DC_XY_AMD  = 0x87fa
gl._3DC_X_AMD   = 0x87f9
gl.ACTIVE_ATTRIBUTES    = 0x8b89
gl.ACTIVE_ATTRIBUTE_MAX_LENGTH  = 0x8b8a
gl.ACTIVE_PROGRAM_EXT   = 0x8259
gl.ACTIVE_TEXTURE   = 0x84e0
gl.ACTIVE_UNIFORMS  = 0x8b86
gl.ACTIVE_UNIFORM_MAX_LENGTH    = 0x8b87
gl.ALIASED_LINE_WIDTH_RANGE = 0x846e
gl.ALIASED_POINT_SIZE_RANGE = 0x846d
gl.ALL_COMPLETED_NV = 0x84f2
gl.ALL_SHADER_BITS_EXT  = 0xffffffff
gl.ALPHA    = 0x1906
gl.ALPHA16F_EXT = 0x881c
gl.ALPHA32F_EXT = 0x8816
gl.ALPHA8_EXT   = 0x803c
gl.ALPHA8_OES   = 0x803c
gl.ALPHA_BITS   = 0xd55
gl.ALPHA_TEST_FUNC_QCOM = 0xbc1
gl.ALPHA_TEST_QCOM  = 0xbc0
gl.ALPHA_TEST_REF_QCOM  = 0xbc2
gl.ALREADY_SIGNALED_APPLE   = 0x911a
gl.ALWAYS   = 0x207
gl.AMD_compressed_3DC_texture   = 0x1
gl.AMD_compressed_ATC_texture   = 0x1
gl.AMD_performance_monitor  = 0x1
gl.AMD_program_binary_Z400  = 0x1
gl.ANGLE_depth_texture  = 0x1
gl.ANGLE_framebuffer_blit   = 0x1
gl.ANGLE_framebuffer_multisample    = 0x1
gl.ANGLE_instanced_arrays   = 0x1
gl.ANGLE_pack_reverse_row_order = 0x1
gl.ANGLE_program_binary = 0x1
gl.ANGLE_texture_compression_dxt3   = 0x1
gl.ANGLE_texture_compression_dxt5   = 0x1
gl.ANGLE_texture_usage  = 0x1
gl.ANGLE_translated_shader_source   = 0x1
gl.ANY_SAMPLES_PASSED_CONSERVATIVE_EXT  = 0x8d6a
gl.ANY_SAMPLES_PASSED_EXT   = 0x8c2f
gl.APPLE_copy_texture_levels    = 0x1
gl.APPLE_framebuffer_multisample    = 0x1
gl.APPLE_rgb_422    = 0x1
gl.APPLE_sync   = 0x1
gl.APPLE_texture_format_BGRA8888    = 0x1
gl.APPLE_texture_max_level  = 0x1
gl.ARM_mali_program_binary  = 0x1
gl.ARM_mali_shader_binary   = 0x1
gl.ARM_rgba8    = 0x1
gl.ARRAY_BUFFER = 0x8892
gl.ARRAY_BUFFER_BINDING = 0x8894
gl.ATC_RGBA_EXPLICIT_ALPHA_AMD  = 0x8c93
gl.ATC_RGBA_INTERPOLATED_ALPHA_AMD  = 0x87ee
gl.ATC_RGB_AMD  = 0x8c92
gl.ATTACHED_SHADERS = 0x8b85
gl.BACK = 0x405
gl.BGRA8_EXT    = 0x93a1
gl.BGRA_EXT = 0x80e1
gl.BGRA_IMG = 0x80e1
gl.BINNING_CONTROL_HINT_QCOM    = 0x8fb0
gl.BLEND    = 0xbe2
gl.BLEND_COLOR  = 0x8005
gl.BLEND_DST_ALPHA  = 0x80ca
gl.BLEND_DST_RGB    = 0x80c8
gl.BLEND_EQUATION   = 0x8009
gl.BLEND_EQUATION_ALPHA = 0x883d
gl.BLEND_EQUATION_RGB   = 0x8009
gl.BLEND_SRC_ALPHA  = 0x80cb
gl.BLEND_SRC_RGB    = 0x80c9
gl.BLUE_BITS    = 0xd54
gl.BOOL = 0x8b56
gl.BOOL_VEC2    = 0x8b57
gl.BOOL_VEC3    = 0x8b58
gl.BOOL_VEC4    = 0x8b59
gl.BUFFER   = 0x82e0
gl.BUFFER_ACCESS_OES    = 0x88bb
gl.BUFFER_MAPPED_OES    = 0x88bc
gl.BUFFER_MAP_POINTER_OES   = 0x88bd
gl.BUFFER_OBJECT_EXT    = 0x9151
gl.BUFFER_SIZE  = 0x8764
gl.BUFFER_USAGE = 0x8765
gl.BYTE = 0x1400
gl.CCW  = 0x901
gl.CLAMP_TO_BORDER_NV   = 0x812d
gl.CLAMP_TO_EDGE    = 0x812f
gl.COLOR_ATTACHMENT0    = 0x8ce0
gl.COLOR_ATTACHMENT0_NV = 0x8ce0
gl.COLOR_ATTACHMENT10_NV    = 0x8cea
gl.COLOR_ATTACHMENT11_NV    = 0x8ceb
gl.COLOR_ATTACHMENT12_NV    = 0x8cec
gl.COLOR_ATTACHMENT13_NV    = 0x8ced
gl.COLOR_ATTACHMENT14_NV    = 0x8cee
gl.COLOR_ATTACHMENT15_NV    = 0x8cef
gl.COLOR_ATTACHMENT1_NV = 0x8ce1
gl.COLOR_ATTACHMENT2_NV = 0x8ce2
gl.COLOR_ATTACHMENT3_NV = 0x8ce3
gl.COLOR_ATTACHMENT4_NV = 0x8ce4
gl.COLOR_ATTACHMENT5_NV = 0x8ce5
gl.COLOR_ATTACHMENT6_NV = 0x8ce6
gl.COLOR_ATTACHMENT7_NV = 0x8ce7
gl.COLOR_ATTACHMENT8_NV = 0x8ce8
gl.COLOR_ATTACHMENT9_NV = 0x8ce9
gl.COLOR_ATTACHMENT_EXT = 0x90f0
gl.COLOR_BUFFER_BIT = 0x4000
gl.COLOR_BUFFER_BIT0_QCOM   = 0x1
gl.COLOR_BUFFER_BIT1_QCOM   = 0x2
gl.COLOR_BUFFER_BIT2_QCOM   = 0x4
gl.COLOR_BUFFER_BIT3_QCOM   = 0x8
gl.COLOR_BUFFER_BIT4_QCOM   = 0x10
gl.COLOR_BUFFER_BIT5_QCOM   = 0x20
gl.COLOR_BUFFER_BIT6_QCOM   = 0x40
gl.COLOR_BUFFER_BIT7_QCOM   = 0x80
gl.COLOR_CLEAR_VALUE    = 0xc22
gl.COLOR_EXT    = 0x1800
gl.COLOR_WRITEMASK  = 0xc23
gl.COMPARE_REF_TO_TEXTURE_EXT   = 0x884e
gl.COMPILE_STATUS   = 0x8b81
gl.COMPRESSED_RGBA_ASTC_10x10_KHR   = 0x93bb
gl.COMPRESSED_RGBA_ASTC_10x5_KHR    = 0x93b8
gl.COMPRESSED_RGBA_ASTC_10x6_KHR    = 0x93b9
gl.COMPRESSED_RGBA_ASTC_10x8_KHR    = 0x93ba
gl.COMPRESSED_RGBA_ASTC_12x10_KHR   = 0x93bc
gl.COMPRESSED_RGBA_ASTC_12x12_KHR   = 0x93bd
gl.COMPRESSED_RGBA_ASTC_4x4_KHR = 0x93b0
gl.COMPRESSED_RGBA_ASTC_5x4_KHR = 0x93b1
gl.COMPRESSED_RGBA_ASTC_5x5_KHR = 0x93b2
gl.COMPRESSED_RGBA_ASTC_6x5_KHR = 0x93b3
gl.COMPRESSED_RGBA_ASTC_6x6_KHR = 0x93b4
gl.COMPRESSED_RGBA_ASTC_8x5_KHR = 0x93b5
gl.COMPRESSED_RGBA_ASTC_8x6_KHR = 0x93b6
gl.COMPRESSED_RGBA_ASTC_8x8_KHR = 0x93b7
gl.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG = 0x8c03
gl.COMPRESSED_RGBA_PVRTC_2BPPV2_IMG = 0x9137
gl.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG = 0x8c02
gl.COMPRESSED_RGBA_PVRTC_4BPPV2_IMG = 0x9138
gl.COMPRESSED_RGBA_S3TC_DXT1_EXT    = 0x83f1
gl.COMPRESSED_RGBA_S3TC_DXT3_ANGLE  = 0x83f2
gl.COMPRESSED_RGBA_S3TC_DXT5_ANGLE  = 0x83f3
gl.COMPRESSED_RGB_PVRTC_2BPPV1_IMG  = 0x8c01
gl.COMPRESSED_RGB_PVRTC_4BPPV1_IMG  = 0x8c00
gl.COMPRESSED_RGB_S3TC_DXT1_EXT = 0x83f0
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR   = 0x93db
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR    = 0x93d8
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR    = 0x93d9
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR    = 0x93da
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR   = 0x93dc
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR   = 0x93dd
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR = 0x93d0
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR = 0x93d1
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR = 0x93d2
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR = 0x93d3
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR = 0x93d4
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR = 0x93d5
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR = 0x93d6
gl.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR = 0x93d7
gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_NV   = 0x8c4d
gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_NV   = 0x8c4e
gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_NV   = 0x8c4f
gl.COMPRESSED_SRGB_S3TC_DXT1_NV = 0x8c4c
gl.COMPRESSED_TEXTURE_FORMATS   = 0x86a3
gl.CONDITION_SATISFIED_APPLE    = 0x911c
gl.CONSTANT_ALPHA   = 0x8003
gl.CONSTANT_COLOR   = 0x8001
gl.CONTEXT_FLAG_DEBUG_BIT   = 0x2
gl.CONTEXT_ROBUST_ACCESS_EXT    = 0x90f3
gl.COUNTER_RANGE_AMD    = 0x8bc1
gl.COUNTER_TYPE_AMD = 0x8bc0
gl.COVERAGE_ALL_FRAGMENTS_NV    = 0x8ed5
gl.COVERAGE_ATTACHMENT_NV   = 0x8ed2
gl.COVERAGE_AUTOMATIC_NV    = 0x8ed7
gl.COVERAGE_BUFFERS_NV  = 0x8ed3
gl.COVERAGE_BUFFER_BIT_NV   = 0x8000
gl.COVERAGE_COMPONENT4_NV   = 0x8ed1
gl.COVERAGE_COMPONENT_NV    = 0x8ed0
gl.COVERAGE_EDGE_FRAGMENTS_NV   = 0x8ed6
gl.COVERAGE_SAMPLES_NV  = 0x8ed4
gl.CPU_OPTIMIZED_QCOM   = 0x8fb1
gl.CULL_FACE    = 0xb44
gl.CULL_FACE_MODE   = 0xb45
gl.CURRENT_PROGRAM  = 0x8b8d
gl.CURRENT_QUERY_EXT    = 0x8865
gl.CURRENT_VERTEX_ATTRIB    = 0x8626
gl.CW   = 0x900
gl.DEBUG_CALLBACK_FUNCTION  = 0x8244
gl.DEBUG_CALLBACK_USER_PARAM    = 0x8245
gl.DEBUG_GROUP_STACK_DEPTH  = 0x826d
gl.DEBUG_LOGGED_MESSAGES    = 0x9145
gl.DEBUG_NEXT_LOGGED_MESSAGE_LENGTH = 0x8243
gl.DEBUG_OUTPUT = 0x92e0
gl.DEBUG_OUTPUT_SYNCHRONOUS = 0x8242
gl.DEBUG_SEVERITY_HIGH  = 0x9146
gl.DEBUG_SEVERITY_LOW   = 0x9148
gl.DEBUG_SEVERITY_MEDIUM    = 0x9147
gl.DEBUG_SEVERITY_NOTIFICATION  = 0x826b
gl.DEBUG_SOURCE_API = 0x8246
gl.DEBUG_SOURCE_APPLICATION = 0x824a
gl.DEBUG_SOURCE_OTHER   = 0x824b
gl.DEBUG_SOURCE_SHADER_COMPILER = 0x8248
gl.DEBUG_SOURCE_THIRD_PARTY = 0x8249
gl.DEBUG_SOURCE_WINDOW_SYSTEM   = 0x8247
gl.DEBUG_TYPE_DEPRECATED_BEHAVIOR   = 0x824d
gl.DEBUG_TYPE_ERROR = 0x824c
gl.DEBUG_TYPE_MARKER    = 0x8268
gl.DEBUG_TYPE_OTHER = 0x8251
gl.DEBUG_TYPE_PERFORMANCE   = 0x8250
gl.DEBUG_TYPE_POP_GROUP = 0x826a
gl.DEBUG_TYPE_PORTABILITY   = 0x824f
gl.DEBUG_TYPE_PUSH_GROUP    = 0x8269
gl.DEBUG_TYPE_UNDEFINED_BEHAVIOR    = 0x824e
gl.DECR = 0x1e03
gl.DECR_WRAP    = 0x8508
gl.DELETE_STATUS    = 0x8b80
gl.DEPTH24_STENCIL8_OES = 0x88f0
gl.DEPTH_ATTACHMENT = 0x8d00
gl.DEPTH_BITS   = 0xd56
gl.DEPTH_BUFFER_BIT = 0x100
gl.DEPTH_BUFFER_BIT0_QCOM   = 0x100
gl.DEPTH_BUFFER_BIT1_QCOM   = 0x200
gl.DEPTH_BUFFER_BIT2_QCOM   = 0x400
gl.DEPTH_BUFFER_BIT3_QCOM   = 0x800
gl.DEPTH_BUFFER_BIT4_QCOM   = 0x1000
gl.DEPTH_BUFFER_BIT5_QCOM   = 0x2000
gl.DEPTH_BUFFER_BIT6_QCOM   = 0x4000
gl.DEPTH_BUFFER_BIT7_QCOM   = 0x8000
gl.DEPTH_CLEAR_VALUE    = 0xb73
gl.DEPTH_COMPONENT  = 0x1902
gl.DEPTH_COMPONENT16    = 0x81a5
gl.DEPTH_COMPONENT16_NONLINEAR_NV   = 0x8e2c
gl.DEPTH_COMPONENT16_OES    = 0x81a5
gl.DEPTH_COMPONENT24_OES    = 0x81a6
gl.DEPTH_COMPONENT32_OES    = 0x81a7
gl.DEPTH_EXT    = 0x1801
gl.DEPTH_FUNC   = 0xb74
gl.DEPTH_RANGE  = 0xb70
gl.DEPTH_STENCIL_OES    = 0x84f9
gl.DEPTH_TEST   = 0xb71
gl.DEPTH_WRITEMASK  = 0xb72
gl.DITHER   = 0xbd0
gl.DMP_shader_binary    = 0x1
gl.DONT_CARE    = 0x1100
gl.DRAW_BUFFER0_NV  = 0x8825
gl.DRAW_BUFFER10_NV = 0x882f
gl.DRAW_BUFFER11_NV = 0x8830
gl.DRAW_BUFFER12_NV = 0x8831
gl.DRAW_BUFFER13_NV = 0x8832
gl.DRAW_BUFFER14_NV = 0x8833
gl.DRAW_BUFFER15_NV = 0x8834
gl.DRAW_BUFFER1_NV  = 0x8826
gl.DRAW_BUFFER2_NV  = 0x8827
gl.DRAW_BUFFER3_NV  = 0x8828
gl.DRAW_BUFFER4_NV  = 0x8829
gl.DRAW_BUFFER5_NV  = 0x882a
gl.DRAW_BUFFER6_NV  = 0x882b
gl.DRAW_BUFFER7_NV  = 0x882c
gl.DRAW_BUFFER8_NV  = 0x882d
gl.DRAW_BUFFER9_NV  = 0x882e
gl.DRAW_BUFFER_EXT  = 0xc01
gl.DRAW_FRAMEBUFFER_ANGLE   = 0x8ca9
gl.DRAW_FRAMEBUFFER_APPLE   = 0x8ca9
gl.DRAW_FRAMEBUFFER_BINDING_ANGLE   = 0x8ca6
gl.DRAW_FRAMEBUFFER_BINDING_APPLE   = 0x8ca6
gl.DRAW_FRAMEBUFFER_BINDING_NV  = 0x8ca6
gl.DRAW_FRAMEBUFFER_NV  = 0x8ca9
gl.DST_ALPHA    = 0x304
gl.DST_COLOR    = 0x306
gl.DYNAMIC_DRAW = 0x88e8
gl.ELEMENT_ARRAY_BUFFER = 0x8893
gl.ELEMENT_ARRAY_BUFFER_BINDING = 0x8895
gl.EQUAL    = 0x202
gl.ES_VERSION_2_0   = 0x1
gl.ETC1_RGB8_OES    = 0x8d64
gl.ETC1_SRGB8_NV    = 0x88ee
gl.EXTENSIONS   = 0x1f03
gl.EXT_blend_minmax = 0x1
gl.EXT_color_buffer_half_float  = 0x1
gl.EXT_debug_label  = 0x1
gl.EXT_debug_marker = 0x1
gl.EXT_discard_framebuffer  = 0x1
gl.EXT_map_buffer_range = 0x1
gl.EXT_multi_draw_arrays    = 0x1
gl.EXT_multisampled_render_to_texture   = 0x1
gl.EXT_multiview_draw_buffers   = 0x1
gl.EXT_occlusion_query_boolean  = 0x1
gl.EXT_read_format_bgra = 0x1
gl.EXT_robustness   = 0x1
gl.EXT_sRGB = 0x1
gl.EXT_separate_shader_objects  = 0x1
gl.EXT_shader_framebuffer_fetch = 0x1
gl.EXT_shader_texture_lod   = 0x1
gl.EXT_shadow_samplers  = 0x1
gl.EXT_texture_compression_dxt1 = 0x1
gl.EXT_texture_filter_anisotropic   = 0x1
gl.EXT_texture_format_BGRA8888  = 0x1
gl.EXT_texture_rg   = 0x1
gl.EXT_texture_storage  = 0x1
gl.EXT_texture_type_2_10_10_10_REV  = 0x1
gl.EXT_unpack_subimage  = 0x1
gl.FALSE    = 0x0
gl.FASTEST  = 0x1101
gl.FENCE_CONDITION_NV   = 0x84f4
gl.FENCE_STATUS_NV  = 0x84f3
gl.FIXED    = 0x140c
gl.FJ_shader_binary_GCCSO   = 0x1
gl.FLOAT    = 0x1406
gl.FLOAT_MAT2   = 0x8b5a
gl.FLOAT_MAT3   = 0x8b5b
gl.FLOAT_MAT4   = 0x8b5c
gl.FLOAT_VEC2   = 0x8b50
gl.FLOAT_VEC3   = 0x8b51
gl.FLOAT_VEC4   = 0x8b52
gl.FRAGMENT_SHADER  = 0x8b30
gl.FRAGMENT_SHADER_BIT_EXT  = 0x2
gl.FRAGMENT_SHADER_DERIVATIVE_HINT_OES  = 0x8b8b
gl.FRAGMENT_SHADER_DISCARDS_SAMPLES_EXT = 0x8a52
gl.FRAMEBUFFER  = 0x8d40
gl.FRAMEBUFFER_ATTACHMENT_ANGLE = 0x93a3
gl.FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT    = 0x8210
gl.FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT    = 0x8211
gl.FRAMEBUFFER_ATTACHMENT_OBJECT_NAME   = 0x8cd1
gl.FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE   = 0x8cd0
gl.FRAMEBUFFER_ATTACHMENT_TEXTURE_3D_ZOFFSET_OES    = 0x8cd4
gl.FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE = 0x8cd3
gl.FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL = 0x8cd2
gl.FRAMEBUFFER_ATTACHMENT_TEXTURE_SAMPLES_EXT   = 0x8d6c
gl.FRAMEBUFFER_BINDING  = 0x8ca6
gl.FRAMEBUFFER_COMPLETE = 0x8cd5
gl.FRAMEBUFFER_INCOMPLETE_ATTACHMENT    = 0x8cd6
gl.FRAMEBUFFER_INCOMPLETE_DIMENSIONS    = 0x8cd9
gl.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT    = 0x8cd7
gl.FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_ANGLE = 0x8d56
gl.FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_APPLE = 0x8d56
gl.FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_EXT   = 0x8d56
gl.FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_IMG   = 0x9134
gl.FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_NV    = 0x8d56
gl.FRAMEBUFFER_UNDEFINED_OES    = 0x8219
gl.FRAMEBUFFER_UNSUPPORTED  = 0x8cdd
gl.FRONT    = 0x404
gl.FRONT_AND_BACK   = 0x408
gl.FRONT_FACE   = 0xb46
gl.FUNC_ADD = 0x8006
gl.FUNC_REVERSE_SUBTRACT    = 0x800b
gl.FUNC_SUBTRACT    = 0x800a
gl.GENERATE_MIPMAP_HINT = 0x8192
gl.GEQUAL   = 0x206
gl.GPU_OPTIMIZED_QCOM   = 0x8fb2
gl.GREATER  = 0x204
gl.GREEN_BITS   = 0xd53
gl.GUILTY_CONTEXT_RESET_EXT = 0x8253
gl.HALF_FLOAT_OES   = 0x8d61
gl.HIGH_FLOAT   = 0x8df2
gl.HIGH_INT = 0x8df5
gl.IMG_multisampled_render_to_texture   = 0x1
gl.IMG_program_binary   = 0x1
gl.IMG_read_format  = 0x1
gl.IMG_shader_binary    = 0x1
gl.IMG_texture_compression_pvrtc    = 0x1
gl.IMG_texture_compression_pvrtc2   = 0x1
gl.IMPLEMENTATION_COLOR_READ_FORMAT = 0x8b9b
gl.IMPLEMENTATION_COLOR_READ_TYPE   = 0x8b9a
gl.INCR = 0x1e02
gl.INCR_WRAP    = 0x8507
gl.INFO_LOG_LENGTH  = 0x8b84
gl.INNOCENT_CONTEXT_RESET_EXT   = 0x8254
gl.INT  = 0x1404
gl.INT_10_10_10_2_OES   = 0x8df7
gl.INT_VEC2 = 0x8b53
gl.INT_VEC3 = 0x8b54
gl.INT_VEC4 = 0x8b55
gl.INVALID_ENUM = 0x500
gl.INVALID_FRAMEBUFFER_OPERATION    = 0x506
gl.INVALID_OPERATION    = 0x502
gl.INVALID_VALUE    = 0x501
gl.INVERT   = 0x150a
gl.KEEP = 0x1e00
gl.KHR_debug    = 0x1
gl.KHR_texture_compression_astc_ldr = 0x1
gl.LEQUAL   = 0x203
gl.LESS = 0x201
gl.LINEAR   = 0x2601
gl.LINEAR_MIPMAP_LINEAR = 0x2703
gl.LINEAR_MIPMAP_NEAREST    = 0x2701
gl.LINES    = 0x1
gl.LINE_LOOP    = 0x2
gl.LINE_STRIP   = 0x3
gl.LINE_WIDTH   = 0xb21
gl.LINK_STATUS  = 0x8b82
gl.LOSE_CONTEXT_ON_RESET_EXT    = 0x8252
gl.LOW_FLOAT    = 0x8df0
gl.LOW_INT  = 0x8df3
gl.LUMINANCE    = 0x1909
gl.LUMINANCE16F_EXT = 0x881e
gl.LUMINANCE32F_EXT = 0x8818
gl.LUMINANCE4_ALPHA4_OES    = 0x8043
gl.LUMINANCE8_ALPHA8_EXT    = 0x8045
gl.LUMINANCE8_ALPHA8_OES    = 0x8045
gl.LUMINANCE8_EXT   = 0x8040
gl.LUMINANCE8_OES   = 0x8040
gl.LUMINANCE_ALPHA  = 0x190a
gl.LUMINANCE_ALPHA16F_EXT   = 0x881f
gl.LUMINANCE_ALPHA32F_EXT   = 0x8819
gl.MALI_PROGRAM_BINARY_ARM  = 0x8f61
gl.MALI_SHADER_BINARY_ARM   = 0x8f60
gl.MAP_FLUSH_EXPLICIT_BIT_EXT   = 0x10
gl.MAP_INVALIDATE_BUFFER_BIT_EXT    = 0x8
gl.MAP_INVALIDATE_RANGE_BIT_EXT = 0x4
gl.MAP_READ_BIT_EXT = 0x1
gl.MAP_UNSYNCHRONIZED_BIT_EXT   = 0x20
gl.MAP_WRITE_BIT_EXT    = 0x2
gl.MAX_3D_TEXTURE_SIZE_OES  = 0x8073
gl.MAX_COLOR_ATTACHMENTS_NV = 0x8cdf
gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS = 0x8b4d
gl.MAX_CUBE_MAP_TEXTURE_SIZE    = 0x851c
gl.MAX_DEBUG_GROUP_STACK_DEPTH  = 0x826c
gl.MAX_DEBUG_LOGGED_MESSAGES    = 0x9144
gl.MAX_DEBUG_MESSAGE_LENGTH = 0x9143
gl.MAX_DRAW_BUFFERS_NV  = 0x8824
gl.MAX_EXT  = 0x8008
gl.MAX_FRAGMENT_UNIFORM_VECTORS = 0x8dfd
gl.MAX_LABEL_LENGTH = 0x82e8
gl.MAX_MULTIVIEW_BUFFERS_EXT    = 0x90f2
gl.MAX_RENDERBUFFER_SIZE    = 0x84e8
gl.MAX_SAMPLES_ANGLE    = 0x8d57
gl.MAX_SAMPLES_APPLE    = 0x8d57
gl.MAX_SAMPLES_EXT  = 0x8d57
gl.MAX_SAMPLES_IMG  = 0x9135
gl.MAX_SAMPLES_NV   = 0x8d57
gl.MAX_SERVER_WAIT_TIMEOUT_APPLE    = 0x9111
gl.MAX_TEXTURE_IMAGE_UNITS  = 0x8872
gl.MAX_TEXTURE_MAX_ANISOTROPY_EXT   = 0x84ff
gl.MAX_TEXTURE_SIZE = 0xd33
gl.MAX_VARYING_VECTORS  = 0x8dfc
gl.MAX_VERTEX_ATTRIBS   = 0x8869
gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS   = 0x8b4c
gl.MAX_VERTEX_UNIFORM_VECTORS   = 0x8dfb
gl.MAX_VIEWPORT_DIMS    = 0xd3a
gl.MEDIUM_FLOAT = 0x8df1
gl.MEDIUM_INT   = 0x8df4
gl.MIN_EXT  = 0x8007
gl.MIRRORED_REPEAT  = 0x8370
gl.MULTISAMPLE_BUFFER_BIT0_QCOM = 0x1000000
gl.MULTISAMPLE_BUFFER_BIT1_QCOM = 0x2000000
gl.MULTISAMPLE_BUFFER_BIT2_QCOM = 0x4000000
gl.MULTISAMPLE_BUFFER_BIT3_QCOM = 0x8000000
gl.MULTISAMPLE_BUFFER_BIT4_QCOM = 0x10000000
gl.MULTISAMPLE_BUFFER_BIT5_QCOM = 0x20000000
gl.MULTISAMPLE_BUFFER_BIT6_QCOM = 0x40000000
gl.MULTISAMPLE_BUFFER_BIT7_QCOM = 0x80000000
gl.MULTIVIEW_EXT    = 0x90f1
gl.NEAREST  = 0x2600
gl.NEAREST_MIPMAP_LINEAR    = 0x2702
gl.NEAREST_MIPMAP_NEAREST   = 0x2700
gl.NEVER    = 0x200
gl.NICEST   = 0x1102
gl.NONE = 0x0
gl.NOTEQUAL = 0x205
gl.NO_ERROR = 0x0
gl.NO_RESET_NOTIFICATION_EXT    = 0x8261
gl.NUM_COMPRESSED_TEXTURE_FORMATS   = 0x86a2
gl.NUM_PROGRAM_BINARY_FORMATS_OES   = 0x87fe
gl.NUM_SHADER_BINARY_FORMATS    = 0x8df9
gl.NV_coverage_sample   = 0x1
gl.NV_depth_nonlinear   = 0x1
gl.NV_draw_buffers  = 0x1
gl.NV_draw_instanced    = 0x1
gl.NV_fbo_color_attachments = 0x1
gl.NV_fence = 0x1
gl.NV_framebuffer_blit  = 0x1
gl.NV_framebuffer_multisample   = 0x1
gl.NV_generate_mipmap_sRGB  = 0x1
gl.NV_instanced_arrays  = 0x1
gl.NV_read_buffer   = 0x1
gl.NV_read_buffer_front = 0x1
gl.NV_read_depth    = 0x1
gl.NV_read_depth_stencil    = 0x1
gl.NV_read_stencil  = 0x1
gl.NV_sRGB_formats  = 0x1
gl.NV_shadow_samplers_array = 0x1
gl.NV_shadow_samplers_cube  = 0x1
gl.NV_texture_border_clamp  = 0x1
gl.NV_texture_compression_s3tc_update   = 0x1
gl.NV_texture_npot_2D_mipmap    = 0x1
gl.OBJECT_TYPE_APPLE    = 0x9112
gl.OES_EGL_image    = 0x1
gl.OES_EGL_image_external   = 0x1
gl.OES_compressed_ETC1_RGB8_texture = 0x1
gl.OES_compressed_paletted_texture  = 0x1
gl.OES_depth24  = 0x1
gl.OES_depth32  = 0x1
gl.OES_depth_texture    = 0x1
gl.OES_element_index_uint   = 0x1
gl.OES_fbo_render_mipmap    = 0x1
gl.OES_fragment_precision_high  = 0x1
gl.OES_get_program_binary   = 0x1
gl.OES_mapbuffer    = 0x1
gl.OES_packed_depth_stencil = 0x1
gl.OES_required_internalformat  = 0x1
gl.OES_rgb8_rgba8   = 0x1
gl.OES_standard_derivatives = 0x1
gl.OES_stencil1 = 0x1
gl.OES_stencil4 = 0x1
gl.OES_surfaceless_context  = 0x1
gl.OES_texture_3D   = 0x1
gl.OES_texture_float    = 0x1
gl.OES_texture_float_linear = 0x1
gl.OES_texture_half_float   = 0x1
gl.OES_texture_half_float_linear    = 0x1
gl.OES_texture_npot = 0x1
gl.OES_vertex_array_object  = 0x1
gl.OES_vertex_half_float    = 0x1
gl.OES_vertex_type_10_10_10_2   = 0x1
gl.ONE  = 0x1
gl.ONE_MINUS_CONSTANT_ALPHA = 0x8004
gl.ONE_MINUS_CONSTANT_COLOR = 0x8002
gl.ONE_MINUS_DST_ALPHA  = 0x305
gl.ONE_MINUS_DST_COLOR  = 0x307
gl.ONE_MINUS_SRC_ALPHA  = 0x303
gl.ONE_MINUS_SRC_COLOR  = 0x301
gl.OUT_OF_MEMORY    = 0x505
gl.PACK_ALIGNMENT   = 0xd05
gl.PACK_REVERSE_ROW_ORDER_ANGLE = 0x93a4
gl.PALETTE4_R5_G6_B5_OES    = 0x8b92
gl.PALETTE4_RGB5_A1_OES = 0x8b94
gl.PALETTE4_RGB8_OES    = 0x8b90
gl.PALETTE4_RGBA4_OES   = 0x8b93
gl.PALETTE4_RGBA8_OES   = 0x8b91
gl.PALETTE8_R5_G6_B5_OES    = 0x8b97
gl.PALETTE8_RGB5_A1_OES = 0x8b99
gl.PALETTE8_RGB8_OES    = 0x8b95
gl.PALETTE8_RGBA4_OES   = 0x8b98
gl.PALETTE8_RGBA8_OES   = 0x8b96
gl.PERCENTAGE_AMD   = 0x8bc3
gl.PERFMON_GLOBAL_MODE_QCOM = 0x8fa0
gl.PERFMON_RESULT_AMD   = 0x8bc6
gl.PERFMON_RESULT_AVAILABLE_AMD = 0x8bc4
gl.PERFMON_RESULT_SIZE_AMD  = 0x8bc5
gl.POINTS   = 0x0
gl.POLYGON_OFFSET_FACTOR    = 0x8038
gl.POLYGON_OFFSET_FILL  = 0x8037
gl.POLYGON_OFFSET_UNITS = 0x2a00
gl.PROGRAM  = 0x82e2
gl.PROGRAM_BINARY_ANGLE = 0x93a6
gl.PROGRAM_BINARY_FORMATS_OES   = 0x87ff
gl.PROGRAM_BINARY_LENGTH_OES    = 0x8741
gl.PROGRAM_OBJECT_EXT   = 0x8b40
gl.PROGRAM_PIPELINE_BINDING_EXT = 0x825a
gl.PROGRAM_PIPELINE_OBJECT_EXT  = 0x8a4f
gl.PROGRAM_SEPARABLE_EXT    = 0x8258
gl.QCOM_alpha_test  = 0x1
gl.QCOM_binning_control = 0x1
gl.QCOM_driver_control  = 0x1
gl.QCOM_extended_get    = 0x1
gl.QCOM_extended_get2   = 0x1
gl.QCOM_perfmon_global_mode = 0x1
gl.QCOM_tiled_rendering = 0x1
gl.QCOM_writeonly_rendering = 0x1
gl.QUERY    = 0x82e3
gl.QUERY_OBJECT_EXT = 0x9153
gl.QUERY_RESULT_AVAILABLE_EXT   = 0x8867
gl.QUERY_RESULT_EXT = 0x8866
gl.R16F_EXT = 0x822d
gl.R32F_EXT = 0x822e
gl.R8_EXT   = 0x8229
gl.READ_BUFFER_EXT  = 0xc02
gl.READ_BUFFER_NV   = 0xc02
gl.READ_FRAMEBUFFER_ANGLE   = 0x8ca8
gl.READ_FRAMEBUFFER_APPLE   = 0x8ca8
gl.READ_FRAMEBUFFER_BINDING_ANGLE   = 0x8caa
gl.READ_FRAMEBUFFER_BINDING_APPLE   = 0x8caa
gl.READ_FRAMEBUFFER_BINDING_NV  = 0x8caa
gl.READ_FRAMEBUFFER_NV  = 0x8ca8
gl.RED_BITS = 0xd52
gl.RED_EXT  = 0x1903
gl.RENDERBUFFER = 0x8d41
gl.RENDERBUFFER_ALPHA_SIZE  = 0x8d53
gl.RENDERBUFFER_BINDING = 0x8ca7
gl.RENDERBUFFER_BLUE_SIZE   = 0x8d52
gl.RENDERBUFFER_DEPTH_SIZE  = 0x8d54
gl.RENDERBUFFER_GREEN_SIZE  = 0x8d51
gl.RENDERBUFFER_HEIGHT  = 0x8d43
gl.RENDERBUFFER_INTERNAL_FORMAT = 0x8d44
gl.RENDERBUFFER_RED_SIZE    = 0x8d50
gl.RENDERBUFFER_SAMPLES_ANGLE   = 0x8cab
gl.RENDERBUFFER_SAMPLES_APPLE   = 0x8cab
gl.RENDERBUFFER_SAMPLES_EXT = 0x8cab
gl.RENDERBUFFER_SAMPLES_IMG = 0x9133
gl.RENDERBUFFER_SAMPLES_NV  = 0x8cab
gl.RENDERBUFFER_STENCIL_SIZE    = 0x8d55
gl.RENDERBUFFER_WIDTH   = 0x8d42
gl.RENDERER = 0x1f01
gl.RENDER_DIRECT_TO_FRAMEBUFFER_QCOM    = 0x8fb3
gl.REPEAT   = 0x2901
gl.REPLACE  = 0x1e01
gl.REQUIRED_TEXTURE_IMAGE_UNITS_OES = 0x8d68
gl.RESET_NOTIFICATION_STRATEGY_EXT  = 0x8256
gl.RG16F_EXT    = 0x822f
gl.RG32F_EXT    = 0x8230
gl.RG8_EXT  = 0x822b
gl.RGB  = 0x1907
gl.RGB10_A2_EXT = 0x8059
gl.RGB10_EXT    = 0x8052
gl.RGB16F_EXT   = 0x881b
gl.RGB32F_EXT   = 0x8815
gl.RGB565   = 0x8d62
gl.RGB565_OES   = 0x8d62
gl.RGB5_A1  = 0x8057
gl.RGB5_A1_OES  = 0x8057
gl.RGB8_OES = 0x8051
gl.RGBA = 0x1908
gl.RGBA16F_EXT  = 0x881a
gl.RGBA32F_EXT  = 0x8814
gl.RGBA4    = 0x8056
gl.RGBA4_OES    = 0x8056
gl.RGBA8_OES    = 0x8058
gl.RGB_422_APPLE    = 0x8a1f
gl.RG_EXT   = 0x8227
gl.SAMPLER  = 0x82e6
gl.SAMPLER_2D   = 0x8b5e
gl.SAMPLER_2D_ARRAY_SHADOW_NV   = 0x8dc4
gl.SAMPLER_2D_SHADOW_EXT    = 0x8b62
gl.SAMPLER_3D_OES   = 0x8b5f
gl.SAMPLER_CUBE = 0x8b60
gl.SAMPLER_CUBE_SHADOW_NV   = 0x8dc5
gl.SAMPLER_EXTERNAL_OES = 0x8d66
gl.SAMPLES  = 0x80a9
gl.SAMPLE_ALPHA_TO_COVERAGE = 0x809e
gl.SAMPLE_BUFFERS   = 0x80a8
gl.SAMPLE_COVERAGE  = 0x80a0
gl.SAMPLE_COVERAGE_INVERT   = 0x80ab
gl.SAMPLE_COVERAGE_VALUE    = 0x80aa
gl.SCISSOR_BOX  = 0xc10
gl.SCISSOR_TEST = 0xc11
gl.SGX_BINARY_IMG   = 0x8c0a
gl.SGX_PROGRAM_BINARY_IMG   = 0x9130
gl.SHADER   = 0x82e1
gl.SHADER_BINARY_DMP    = 0x9250
gl.SHADER_BINARY_FORMATS    = 0x8df8
gl.SHADER_BINARY_VIV    = 0x8fc4
gl.SHADER_COMPILER  = 0x8dfa
gl.SHADER_OBJECT_EXT    = 0x8b48
gl.SHADER_SOURCE_LENGTH = 0x8b88
gl.SHADER_TYPE  = 0x8b4f
gl.SHADING_LANGUAGE_VERSION = 0x8b8c
gl.SHORT    = 0x1402
gl.SIGNALED_APPLE   = 0x9119
gl.SLUMINANCE8_ALPHA8_NV    = 0x8c45
gl.SLUMINANCE8_NV   = 0x8c47
gl.SLUMINANCE_ALPHA_NV  = 0x8c44
gl.SLUMINANCE_NV    = 0x8c46
gl.SRC_ALPHA    = 0x302
gl.SRC_ALPHA_SATURATE   = 0x308
gl.SRC_COLOR    = 0x300
gl.SRGB8_ALPHA8_EXT = 0x8c43
gl.SRGB8_NV = 0x8c41
gl.SRGB_ALPHA_EXT   = 0x8c42
gl.SRGB_EXT = 0x8c40
gl.STACK_OVERFLOW   = 0x503
gl.STACK_UNDERFLOW  = 0x504
gl.STATE_RESTORE    = 0x8bdc
gl.STATIC_DRAW  = 0x88e4
gl.STENCIL_ATTACHMENT   = 0x8d20
gl.STENCIL_BACK_FAIL    = 0x8801
gl.STENCIL_BACK_FUNC    = 0x8800
gl.STENCIL_BACK_PASS_DEPTH_FAIL = 0x8802
gl.STENCIL_BACK_PASS_DEPTH_PASS = 0x8803
gl.STENCIL_BACK_REF = 0x8ca3
gl.STENCIL_BACK_VALUE_MASK  = 0x8ca4
gl.STENCIL_BACK_WRITEMASK   = 0x8ca5
gl.STENCIL_BITS = 0xd57
gl.STENCIL_BUFFER_BIT   = 0x400
gl.STENCIL_BUFFER_BIT0_QCOM = 0x10000
gl.STENCIL_BUFFER_BIT1_QCOM = 0x20000
gl.STENCIL_BUFFER_BIT2_QCOM = 0x40000
gl.STENCIL_BUFFER_BIT3_QCOM = 0x80000
gl.STENCIL_BUFFER_BIT4_QCOM = 0x100000
gl.STENCIL_BUFFER_BIT5_QCOM = 0x200000
gl.STENCIL_BUFFER_BIT6_QCOM = 0x400000
gl.STENCIL_BUFFER_BIT7_QCOM = 0x800000
gl.STENCIL_CLEAR_VALUE  = 0xb91
gl.STENCIL_EXT  = 0x1802
gl.STENCIL_FAIL = 0xb94
gl.STENCIL_FUNC = 0xb92
gl.STENCIL_INDEX1_OES   = 0x8d46
gl.STENCIL_INDEX4_OES   = 0x8d47
gl.STENCIL_INDEX8   = 0x8d48
gl.STENCIL_PASS_DEPTH_FAIL  = 0xb95
gl.STENCIL_PASS_DEPTH_PASS  = 0xb96
gl.STENCIL_REF  = 0xb97
gl.STENCIL_TEST = 0xb90
gl.STENCIL_VALUE_MASK   = 0xb93
gl.STENCIL_WRITEMASK    = 0xb98
gl.STREAM_DRAW  = 0x88e0
gl.SUBPIXEL_BITS    = 0xd50
gl.SYNC_CONDITION_APPLE = 0x9113
gl.SYNC_FENCE_APPLE = 0x9116
gl.SYNC_FLAGS_APPLE = 0x9115
gl.SYNC_FLUSH_COMMANDS_BIT_APPLE    = 0x1
gl.SYNC_GPU_COMMANDS_COMPLETE_APPLE = 0x9117
gl.SYNC_OBJECT_APPLE    = 0x8a53
gl.SYNC_STATUS_APPLE    = 0x9114
gl.TEXTURE  = 0x1702
gl.TEXTURE0 = 0x84c0
gl.TEXTURE1 = 0x84c1
gl.TEXTURE10    = 0x84ca
gl.TEXTURE11    = 0x84cb
gl.TEXTURE12    = 0x84cc
gl.TEXTURE13    = 0x84cd
gl.TEXTURE14    = 0x84ce
gl.TEXTURE15    = 0x84cf
gl.TEXTURE16    = 0x84d0
gl.TEXTURE17    = 0x84d1
gl.TEXTURE18    = 0x84d2
gl.TEXTURE19    = 0x84d3
gl.TEXTURE2 = 0x84c2
gl.TEXTURE20    = 0x84d4
gl.TEXTURE21    = 0x84d5
gl.TEXTURE22    = 0x84d6
gl.TEXTURE23    = 0x84d7
gl.TEXTURE24    = 0x84d8
gl.TEXTURE25    = 0x84d9
gl.TEXTURE26    = 0x84da
gl.TEXTURE27    = 0x84db
gl.TEXTURE28    = 0x84dc
gl.TEXTURE29    = 0x84dd
gl.TEXTURE3 = 0x84c3
gl.TEXTURE30    = 0x84de
gl.TEXTURE31    = 0x84df
gl.TEXTURE4 = 0x84c4
gl.TEXTURE5 = 0x84c5
gl.TEXTURE6 = 0x84c6
gl.TEXTURE7 = 0x84c7
gl.TEXTURE8 = 0x84c8
gl.TEXTURE9 = 0x84c9
gl.TEXTURE_2D   = 0xde1
gl.TEXTURE_3D_OES   = 0x806f
gl.TEXTURE_BINDING_2D   = 0x8069
gl.TEXTURE_BINDING_3D_OES   = 0x806a
gl.TEXTURE_BINDING_CUBE_MAP = 0x8514
gl.TEXTURE_BINDING_EXTERNAL_OES = 0x8d67
gl.TEXTURE_BORDER_COLOR_NV  = 0x1004
gl.TEXTURE_COMPARE_FUNC_EXT = 0x884d
gl.TEXTURE_COMPARE_MODE_EXT = 0x884c
gl.TEXTURE_CUBE_MAP = 0x8513
gl.TEXTURE_CUBE_MAP_NEGATIVE_X  = 0x8516
gl.TEXTURE_CUBE_MAP_NEGATIVE_Y  = 0x8518
gl.TEXTURE_CUBE_MAP_NEGATIVE_Z  = 0x851a
gl.TEXTURE_CUBE_MAP_POSITIVE_X  = 0x8515
gl.TEXTURE_CUBE_MAP_POSITIVE_Y  = 0x8517
gl.TEXTURE_CUBE_MAP_POSITIVE_Z  = 0x8519
gl.TEXTURE_DEPTH_QCOM   = 0x8bd4
gl.TEXTURE_EXTERNAL_OES = 0x8d65
gl.TEXTURE_FORMAT_QCOM  = 0x8bd6
gl.TEXTURE_HEIGHT_QCOM  = 0x8bd3
gl.TEXTURE_IMAGE_VALID_QCOM = 0x8bd8
gl.TEXTURE_IMMUTABLE_FORMAT_EXT = 0x912f
gl.TEXTURE_INTERNAL_FORMAT_QCOM = 0x8bd5
gl.TEXTURE_MAG_FILTER   = 0x2800
gl.TEXTURE_MAX_ANISOTROPY_EXT   = 0x84fe
gl.TEXTURE_MAX_LEVEL_APPLE  = 0x813d
gl.TEXTURE_MIN_FILTER   = 0x2801
gl.TEXTURE_NUM_LEVELS_QCOM  = 0x8bd9
gl.TEXTURE_OBJECT_VALID_QCOM    = 0x8bdb
gl.TEXTURE_SAMPLES_IMG  = 0x9136
gl.TEXTURE_TARGET_QCOM  = 0x8bda
gl.TEXTURE_TYPE_QCOM    = 0x8bd7
gl.TEXTURE_USAGE_ANGLE  = 0x93a2
gl.TEXTURE_WIDTH_QCOM   = 0x8bd2
gl.TEXTURE_WRAP_R_OES   = 0x8072
gl.TEXTURE_WRAP_S   = 0x2802
gl.TEXTURE_WRAP_T   = 0x2803
gl.TIMEOUT_EXPIRED_APPLE    = 0x911b
gl.TIMEOUT_IGNORED_APPLE    = 0xffffffffffffffff
gl.TRANSLATED_SHADER_SOURCE_LENGTH_ANGLE    = 0x93a0
gl.TRIANGLES    = 0x4
gl.TRIANGLE_FAN = 0x6
gl.TRIANGLE_STRIP   = 0x5
gl.TRUE = 0x1
gl.UNKNOWN_CONTEXT_RESET_EXT    = 0x8255
gl.UNPACK_ALIGNMENT = 0xcf5
gl.UNPACK_ROW_LENGTH    = 0xcf2
gl.UNPACK_SKIP_PIXELS   = 0xcf4
gl.UNPACK_SKIP_ROWS = 0xcf3
gl.UNSIGNALED_APPLE = 0x9118
gl.UNSIGNED_BYTE    = 0x1401
gl.UNSIGNED_INT = 0x1405
gl.UNSIGNED_INT64_AMD   = 0x8bc2
gl.UNSIGNED_INT_10_10_10_2_OES  = 0x8df6
gl.UNSIGNED_INT_24_8_OES    = 0x84fa
gl.UNSIGNED_INT_2_10_10_10_REV_EXT  = 0x8368
gl.UNSIGNED_NORMALIZED_EXT  = 0x8c17
gl.UNSIGNED_SHORT   = 0x1403
gl.UNSIGNED_SHORT_1_5_5_5_REV_EXT   = 0x8366
gl.UNSIGNED_SHORT_4_4_4_4   = 0x8033
gl.UNSIGNED_SHORT_4_4_4_4_REV_EXT   = 0x8365
gl.UNSIGNED_SHORT_4_4_4_4_REV_IMG   = 0x8365
gl.UNSIGNED_SHORT_5_5_5_1   = 0x8034
gl.UNSIGNED_SHORT_5_6_5 = 0x8363
gl.UNSIGNED_SHORT_8_8_APPLE = 0x85ba
gl.UNSIGNED_SHORT_8_8_REV_APPLE = 0x85bb
gl.VALIDATE_STATUS  = 0x8b83
gl.VENDOR   = 0x1f00
gl.VERSION  = 0x1f02
gl.VERTEX_ARRAY_BINDING_OES = 0x85b5
gl.VERTEX_ARRAY_OBJECT_EXT  = 0x9154
gl.VERTEX_ATTRIB_ARRAY_BUFFER_BINDING   = 0x889f
gl.VERTEX_ATTRIB_ARRAY_DIVISOR_ANGLE    = 0x88fe
gl.VERTEX_ATTRIB_ARRAY_DIVISOR_NV   = 0x88fe
gl.VERTEX_ATTRIB_ARRAY_ENABLED  = 0x8622
gl.VERTEX_ATTRIB_ARRAY_NORMALIZED   = 0x886a
gl.VERTEX_ATTRIB_ARRAY_POINTER  = 0x8645
gl.VERTEX_ATTRIB_ARRAY_SIZE = 0x8623
gl.VERTEX_ATTRIB_ARRAY_STRIDE   = 0x8624
gl.VERTEX_ATTRIB_ARRAY_TYPE = 0x8625
gl.VERTEX_SHADER    = 0x8b31
gl.VERTEX_SHADER_BIT_EXT    = 0x1
gl.VIEWPORT = 0xba2
gl.VIV_shader_binary    = 0x1
gl.WAIT_FAILED_APPLE    = 0x911d
gl.WRITEONLY_RENDERING_QCOM = 0x8823
gl.WRITE_ONLY_OES   = 0x88b9
gl.Z400_BINARY_AMD  = 0x8740
gl.ZERO = 0x0
gl.VERTEX_ATTRIB_POINTER_VEC3    = 0
gl.VERTEX_ATTRIB_POINTER_COLOR4B = 1

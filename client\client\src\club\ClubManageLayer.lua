-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部管理
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local EditBox = cs.app.client('system.EditBox')
local ClubUtil = cs.app.client('club.ClubUtil')


local ClubManageLayer = class("ClubManageLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubManageLayer:ctor(club)
    print('ClubManageLayer:ctor...')
    self.m_club = club
    local main_node = ClubUtil.initUI(self, 'ClubManageLayer.csb')

    if not self.m_club.is_join or self.m_club.cbIsManager == 1 then
        ClubUtil.addRedPoint(main_node:child('btn_apply'), self.m_club.apply_num > 0)
    end

    self:initForm()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubManageLayer:onEnter()
    print('ClubManageLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_DISMISS, self, self.onDismissResp)
    ClubUtil.listen(cmd.SUB_CLUB_QUIT, self, self.onQuitResp)
    ClubUtil.listen(cmd.SUB_CLUB_EDIT_NAME, self, self.onEditNameResp)
    ClubUtil.listen(cmd.SUB_CLUB_EDIT_ACCOUNT, self, self.onEditWeixinResp)
    ClubUtil.listen(cmd.SUB_CLUB_EDIT_MSG, self, self.onEditNoticeResp)
    ClubUtil.listen(cmd.SUB_CLUB_APPLY_COUNT, self, self.onApplyCountResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubManageLayer:onExit()
    print('ClubManageLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
    local editor = self.main_node:child('panel_notice/input_notice')
    if editor then
        editor:removeEditHandler()
    end
end


-------------------------------------------------------------------------------
-- 显示俱乐部信息
-------------------------------------------------------------------------------
function ClubManageLayer:initForm()
    local club = self.m_club
    local main_node = self.main_node
    -- logo
    main_node:child('img_logo'):ignoreContentAdaptWithSize(true)
    main_node:child('img_logo'):texture('common/icon_club_logo_' .. club.nLogoID .. '.png')
    -- 名称
    main_node:child('panel_name/label_name'):setString(club.szName)
    -- 微信
    main_node:child('panel_weixin/label_weixin'):setString(club.sPresidentAccount or '')
    -- ID、地区
    main_node:child('panel_id/label_id'):setString(string.format('%06.0f', club.dwClubID))
    main_node:child('panel_id/label_district'):setString(club.szProvince or '')
    -- 成员数量
    main_node:child('panel_member/label_member'):setString(club.nMemberCount or 0)

    helper.logic.addListenerByName(self, {main_node:child('btn_share, btn_dismiss, panel_member/btn_view')})

    -- 如果不是自己创建的，隐藏一些按钮，显示退出按钮
    if club.is_join then
        if club.cbIsManager == 0 then
            main_node:child('btn_apply'):removeFromParent()
        else
            main_node:child('btn_apply'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnApply) )
        end
        main_node:child('btn_kind'):removeFromParent()
        main_node:child('btn_stat'):removeFromParent()
        main_node:child('panel_name/btn_modify_name'):removeFromParent()
        main_node:child('panel_weixin/btn_modify_weixin'):removeFromParent()
        main_node:child('btn_dismiss/font'):texture('word/font_btn_exit_club.png')
        main_node:child('panel_notice/label_notice'):setString(club.szMsg)
        main_node:child('panel_notice/input_notice'):removeFromParent()
    else
        main_node:child('panel_notice/label_notice'):removeFromParent()
        local editor = EditBox.convertTextField( main_node:child('panel_notice/input_notice'), 'common/bg_transparent.png', ccui.TextureResType.localType)
        editor:setString(club.szMsg)
        editor:onEditHandler(handler(self, self.onNoticeEdit))
        helper.logic.addListenerByName(self, {main_node:child(
            'btn_apply, btn_kind, btn_stat, panel_name/btn_modify_name, panel_weixin/btn_modify_weixin')})
    end

    -- 动画展示
    local names = {'panel_name', 'panel_id', 'panel_weixin', 'panel_member', 'panel_notice', 'panel_verify'}
    for i, name in ipairs(names) do
        local panel = main_node:child(name)
        panel:setCascadeOpacityEnabled(true)
        panel:opacity(0):px(panel:px() + 100):runAction( cc.Sequence:create(
            cc.DelayTime:create(i * 0.05),
            cc.Spawn:create(
                cc.FadeIn:create(0.2),
                cc.MoveBy:create(0.1, cc.p(-100, 0))
            )
        ) )
    end
end


-------------------------------------------------------------------------------
-- 管理申请信息按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnApply(sender)
    ClubUtil.open(self, 'club.ClubApplyLayer', nil, {self.m_club} )
end


-------------------------------------------------------------------------------
-- 玩法管理按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnKind(sender)
    ClubUtil.open(self, 'club.ClubKindLayer', nil, {self.m_club} )
end


-------------------------------------------------------------------------------
-- 统计按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnStat(sender)
    ClubUtil.open(self, 'club.ClubStatLayer', nil, {self.m_club} )
end


-------------------------------------------------------------------------------
-- 分享邀请按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnShare(sender)
    ClubUtil.shareClub(self.m_club.dwClubID, self.m_club.szName)
end


-------------------------------------------------------------------------------
-- 解散（退出）俱乐部按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnDismiss(sender)
    local msg = self.m_club.is_join and 'CLUB_QUERY_EXIT' or 'CLUB_QUERY_DISMISS'
    helper.pop.alert(LANG[msg], function()
        local commond = self.m_club.is_join and cmd.SUB_CLUB_QUIT or cmd.SUB_CLUB_DISMISS
        ClubUtil.send(commond, cmd.CMD_GR_ID, {dwID=self.m_club.dwClubID})
    end, true)
end


-------------------------------------------------------------------------------
-- 修改名称按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnModifyName(sender)
    helper.pop.alert(LANG.CLUB_MODIFY_NAME, function(text)
        if text == '' then return end
        local name_len = ClubUtil.getUTF8Len(text)
        if name_len > ClubUtil.CLUB_NAME_LEN then
            helper.pop.message( LANG{'CLUB_NAME_BEYOND', max=ClubUtil.CLUB_NAME_LEN} )
            return
        end

        self:sendEdit(cmd.SUB_CLUB_EDIT_NAME, text)
    end, true, self.m_club.szName)
end


-------------------------------------------------------------------------------
-- 修改微信按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnModifyWeixin(sender)
    helper.pop.alert(LANG.CLUB_MODIFY_WEIXIN, function(text)
        if text == '' then return end
        self:sendEdit(cmd.SUB_CLUB_EDIT_ACCOUNT, text)
    end, true, (self.m_club.sPresidentAccount or ''))
end


-------------------------------------------------------------------------------
-- 公告编辑器事件
-------------------------------------------------------------------------------
function ClubManageLayer:onNoticeEdit(event)
    --print(event.name, event.target)
    if event.name ~= 'return' then return end
    self:sendEdit(cmd.SUB_CLUB_EDIT_MSG, event.target:getString())
end


-------------------------------------------------------------------------------
-- 查看成员按钮点击
-------------------------------------------------------------------------------
function ClubManageLayer:onBtnView(sender)
    ClubUtil.open(self, 'club.ClubMemberLayer', nil, {self.m_club} )
end


-------------------------------------------------------------------------------
-- 移除俱乐部
-------------------------------------------------------------------------------
function ClubManageLayer:removeClub()
    local list_layer = helper.app.getFromScene('club_list_layer')
    if list_layer then
        list_layer:removeClub(self.m_club)
    end

    local main_layer = helper.app.getFromScene('club_main_layer')
    if main_layer then
        main_layer:removeClub(self.m_club)
    end

    ClubUtil.back(self)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 解散返回
-------------------------------------------------------------------------------
function ClubManageLayer:onDismissResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_DISMISS_SUCC )
        if tolua.isnull(self) then return end

        self:removeClub()
    end)
end


-------------------------------------------------------------------------------
-- 退出返回
-------------------------------------------------------------------------------
function ClubManageLayer:onQuitResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_EXIT_SUCC )
        if tolua.isnull(self) then return end

        self:removeClub()
    end)
end


-------------------------------------------------------------------------------
-- 发送编辑命令
-------------------------------------------------------------------------------
function ClubManageLayer:sendEdit(commond, text)
    local values = {dwID=self.m_club.dwClubID, szMsg=text}
    ClubUtil.send(commond, cmd.CMD_GR_IDMsg, values)
end


-------------------------------------------------------------------------------
-- 修改名称返回
-------------------------------------------------------------------------------
function ClubManageLayer:onEditNameResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_SUCC )
        if tolua.isnull(self) then return end

        self.m_club.szName = ret.szMsg
        self.main_node:child('panel_name/label_name'):setString(ret.szMsg)
    end)
end


-------------------------------------------------------------------------------
-- 修改微信返回
-------------------------------------------------------------------------------
function ClubManageLayer:onEditWeixinResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_SUCC )
        if tolua.isnull(self) then return end

        self.m_club.sPresidentAccount = ret.szMsg
        self.main_node:child('panel_weixin/label_weixin'):setString(ret.szMsg)
    end)
end


-------------------------------------------------------------------------------
-- 修改公告返回
-------------------------------------------------------------------------------
function ClubManageLayer:onEditNoticeResp(data)
    local r = ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_SUCC )
        if tolua.isnull(self) then return end

        self.m_club.szMsg = ret.szMsg
    end)
    if not r then
        self.main_node:child('panel_notice/input_notice'):setString(self.m_club.szMsg)
    end
end


-------------------------------------------------------------------------------
-- 申请检查返回
-------------------------------------------------------------------------------
function ClubManageLayer:onApplyCountResp(data)
    -- club对应main中的club，已经被更新
    if not self.m_club.is_join or self.m_club.cbIsManager == 1 then
        ClubUtil.addRedPoint(self.main_node:child('btn_apply'), self.m_club.apply_num > 0)
    end
end


return ClubManageLayer

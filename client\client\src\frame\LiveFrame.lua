-------------------------------------------------------------------------------
--  创世版3.0
--  大厅网络长连接
--  @date 2018-01-11
--  @auth woodoo
-------------------------------------------------------------------------------
local cmd_common = cs.app.client('header.CMD_Common')
local ExternalFun = cs.app.client('external.ExternalFun')
local BaseFrame = appdf.req(appdf.CLIENT_SRC..'frame.BaseFrame')
local MsgListener = appdf.req(appdf.CLIENT_SRC..'frame.MsgListener')


local LiveFrame = class('LiveFrame', BaseFrame)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function LiveFrame:ctor()
	LiveFrame.super.ctor(self)
    self.m_keep_heart_beat = true
	self.listen_manager = MsgListener.new()
    self.m_last_idle = os.time()
    self.m_is_background = false

    -- 设置缺省回调(self._callBack必须存在，否则BaseFrame中收到数据后会断开连接)
    self._callBack = function(cmd, data)
        if cmd < 0 then
            print('LiveFrame:', data)
        end
    end

    self.m_idle_handler = cc.Director:getInstance():getScheduler():scheduleScriptFunc(handler(self, self.onIdle), 7, false)
end


-------------------------------------------------------------------------------
-- 析构方法(需主动调用)
-------------------------------------------------------------------------------
function LiveFrame:dtor()
    self:disconnect()
    if self.m_idle_handler then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self.m_idle_handler)
        m_idle_handler = nil
    end
	if LiveFrame._instance == self then
        LiveFrame._instance = nil
    end
end


-------------------------------------------------------------------------------
-- 实现单例
-------------------------------------------------------------------------------
LiveFrame._instance = nil
function LiveFrame:getInstance()
    if nil == LiveFrame._instance then
        LiveFrame._instance = LiveFrame:create()
    end
    return LiveFrame._instance
end


function LiveFrame:setGlobalListen(obj, func) self.listen_manager:setGlobalListen(obj, func) end
function LiveFrame:addListen(main, sub, obj, func) self.listen_manager:addListen(main, sub, obj, func) end
function LiveFrame:removeListen(main, sub, obj) self.listen_manager:removeListen(main, sub, obj, true) end
function LiveFrame:removeListenByObj(obj) self.listen_manager:removeListenByObj(obj) end


-------------------------------------------------------------------------------
-- 进入后台
-------------------------------------------------------------------------------
function LiveFrame:onBackgroundCallback(is_foreground)
    if is_foreground then
        self.m_last_idle = os.time()
        self.m_is_background = false
    else
        self.m_is_background = true
    end
end


-------------------------------------------------------------------------------
-- idle定时器
-------------------------------------------------------------------------------
function LiveFrame:onIdle()
    --print('Live frame idle ...', os.time())
    ---[[
    if self.m_is_background then return end
    if self:isSocketServer() then
        if self.m_last_idle + 15 < os.time() then
            self:onCloseSocket()
        else
    	    local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID=GlobalUserItem.dwUserID} )
	        cmd_data:setcmdinfo(cmd_common.MDM_SOCKET_SERVICE, cmd_common.SUB_HEARTBEAT)
            self:send(cmd_data)
        end
    end
    --]]
end


-------------------------------------------------------------------------------
-- idle命令返回
-------------------------------------------------------------------------------
function LiveFrame:onIdleResp(data)
    --print('Live frame idle ... response.', os.time())
    self.m_last_idle = os.time()
end


-------------------------------------------------------------------------------
-- 连接
-------------------------------------------------------------------------------
function LiveFrame:connect()
    self.m_last_idle = os.time()

    if not self:onCreateSocket(yl.LOGONSERVER, yl.LOGONPORT) then
        self._callBack(-1, 'LiveFrame:Connection create failed!')
    end
end


-------------------------------------------------------------------------------
-- 连接成功
-------------------------------------------------------------------------------
function LiveFrame:onConnectCompeleted()
	print('LiveFrame:onConnectCompeleted...')
    self.m_last_idle = os.time()

    -- 认证
    local cmd_data = CCmd_Data:create(4)
	cmd_data:setcmdinfo(cmd_common.MDM_SOCKET_SERVICE, cmd_common.SUB_SET_SOCKET)
    cmd_data:pushdword(GlobalUserItem.dwUserID)
    self:send(cmd_data)
end


-------------------------------------------------------------------------------
-- 数据分发
-------------------------------------------------------------------------------
function LiveFrame:onSocketEvent(main, sub, pData)
    if main == cmd_common.MDM_SOCKET_SERVICE and sub == cmd_common.SUB_HEARTBEAT then
        self:onIdleResp(pData)
    end
    self.listen_manager:trigger(main, sub, pData)
end


-------------------------------------------------------------------------------
-- 发送
-------------------------------------------------------------------------------
function LiveFrame:send(buffer)
    if not self:sendSocketData(buffer) then
        self._callBack(-1, 'LiveFrame:send failed!')
    end
end


-------------------------------------------------------------------------------
-- 断开连接回调
-------------------------------------------------------------------------------
function LiveFrame:onCloseSocketCallback()
    print('LiveFrame close callback...')
    -- 实现断线重连(延时开始）
    local this = self
    if this.m_reconnect_handler then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(this.m_reconnect_handler)
    end
    this.m_reconnect_handler = cc.Director:getInstance():getScheduler():scheduleScriptFunc(function()
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(this.m_reconnect_handler)
        this.m_reconnect_handler = nil
        this:connect()
    end, 1, false)
end


-------------------------------------------------------------------------------
-- 主动断开（不会导致断线重连）
-------------------------------------------------------------------------------
function LiveFrame:disconnect()
    if self.m_reconnect_handler then
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry(self.m_reconnect_handler)
        self.m_reconnect_handler = nil
    end
    self:onCloseSocket(true)
end


-------------------------------------------------------------------------------
-- 数据通用处理
-------------------------------------------------------------------------------
function LiveFrame:resp(data, t, is_multi)
    local count = 0
	local len = data:getlen()
    local single_len = ExternalFun.calculate_netdata(t)
    if len > 0 then
        if t[1].k == 'wStructSize' and len > 2 then
            single_len = data:readword()
            data:resetread()
        end
        if is_multi then
            count = math.floor(len/single_len)
        else
            count = 1
        end
        --[[
	    if (len - count * single_len) ~= 0 then
            helper.pop.message( LANG{'CMD_DATA_LEN_ERROR', len=len, single=single_len} )
            count = 0
            return false
        end
        --]]
    end
    print( string.format('resp data receive_len:%d, single_len:%d, item_count:%d, is_multi:%s', len, single_len, count, (is_multi and 'true' or 'false')) )

    local ret = {}

    if count > 0 then
        for i = 1, count do
            local item = ExternalFun.read_netdata(t, data, single_len)
            table.insert(ret, item)
        end
    end

    return (is_multi or count == 0) and ret or ret[1]
end


return LiveFrame

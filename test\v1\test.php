<?php
/**
 * Created by PhpStorm.
 * User: tuo3
 * Date: 2017/3/19
 * Time: 上午10:49
 */

require(APPPATH.'/libraries/REST_Controller.php');

class Test extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        echo $this->getIp().PHP_EOL;
        echo $_SERVER['REMOTE_ADDR'];
    }

    //获取用户IP地址
    public function getIp()
    {

        if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $cip = $_SERVER["HTTP_CLIENT_IP"];
        } else if (!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } else if (!empty($_SERVER["REMOTE_ADDR"])) {
            $cip = $_SERVER["REMOTE_ADDR"];
        } else {
            $cip = '';
        }
        preg_match("/[\d\.]{7,15}/", $cip, $cips);
        $cip = isset($cips[0]) ? $cips[0] : 'unknown';
        unset($cips);

        return $cip;
    }

    public function wheel()
    {
        $num = $this->input->get('num');
        $id = $this->input->get('id');

        echo "模拟总次数:".$num.PHP_EOL;

        $this->load->model('server_model');

        $wheel = $this->server_model->get_wheel_by_id($id);

        $begin_time = $this->_get_milli_second();

        if($wheel) {
            $result = array();

            for($i=1;$i<=$num;$i++) {
                $prize = array();
                $index = null;
                $prize = $this->_get_rand($wheel['prizes'],$index);
                if($index) {
                    if(isset($result[$index])) {
                        $result[$index] += 1;
                    } else {
                        $result[$index] = 1;
                    }

                    $this->server_model->update_wheel_prize($id,$index+1,array('stock'=>'stock-1'));
//                    echo $this->db->last_query();
                }
            }
        }

        foreach($result as $k=>$v) {
            $item = $this->server_model->get_wheel_item_by_id($wheel['prizes'][$k]['item_id']);

            echo "奖品".($k+1).":".$item['name']."x".$wheel['prizes'][$k]['num'].",中奖次数:".$v.PHP_EOL;
        }

        $end_time = $this->_get_milli_second();

        echo "总耗时:".(($end_time-$begin_time)/1000).'s';

    }

    private function _get_milli_second() {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }


    private function _get_rand($proArr,&$index) {
        $result = array();
        foreach ($proArr as $key => $val) {

            // 检查库存
            if($val['stock']< 1) {
                $arr[$key] = 0;
            } else {
                $arr[$key] = $val['weight'];
            }
        }
        // 概率数组的总概率
        $proSum = array_sum($arr);
        if($proSum > 0) {
            asort($arr);
            // 概率数组循环
            foreach ($arr as $k => $v) {
                $randNum = mt_rand(1, $proSum);
                if ($randNum <= $v) {
                    $result = $proArr[$k];
                    $index = $k;
                    break;
                } else {
                    $proSum -= $v;
                }
            }
        }
        return $result;
    }

}
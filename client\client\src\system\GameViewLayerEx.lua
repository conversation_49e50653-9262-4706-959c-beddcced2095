-------------------------------------------------------------------------------
--  创世版3.0
--  GameViewLayer通用扩展
--  @date 2018-03-22
--  @auth woodoo
-------------------------------------------------------------------------------
local GameViewLayerEx = {}


function GameViewLayerEx.assign(obj)
	for k, v in pairs(GameViewLayerEx) do obj[k] = v end
end


-- 动作动画配置{（路径，帧数，持续时间，是否重复）, y偏移，缩放}
local room_act_configs = {
    [1] = {{'common/ani_room_act_meigui', 24, 1, false, cc.RemoveSelf:create(true)}, 20},
    [2] = {{'common/ani_room_act_jiezhi', 17, 0.7, false, cc.RemoveSelf:create(true)}, 35},
    [3] = {{'common/ani_room_act_zhadan', 13, 0.5, false, cc.RemoveSelf:create(true)}, 25, 2},
    [4] = {{'common/ani_room_act_paizhuan', 11, 0.5, false, cc.RemoveSelf:create(true)}, -22} ,
}


-------------------------------------------------------------------------------
-- onExit扩展（在onExit中调用）
-------------------------------------------------------------------------------
function GameViewLayerEx:onExitEx()
    -- 可能用到的序列帧动画移除
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("ani_room_act_meigui.plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey("ani_room_act_meigui.png")
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("ani_room_act_jiezhi.plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey("ani_room_act_jiezhi.png")
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("ani_room_act_paizhuan.plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey("ani_room_act_paizhuan.png")
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("ani_room_act_zhadan.plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey("ani_room_act_zhadan.png")
end


-------------------------------------------------------------------------------
-- 玩家动作播放
-------------------------------------------------------------------------------
function GameViewLayerEx:showUserAction(from_view_id, to_view_id, act_index)
    local path = 'common/icon_room_act_' .. act_index .. '.png'
    if not cc.FileUtils:getInstance():isFileExist(path) then return end
        
    local x1, y1 = self.m_node_player[from_view_id]:pos()
    local x2, y2 = self.m_node_player[to_view_id]:pos()
    local sp = display.newSprite(path)
    sp:zorder(10):addTo(self)
    sp:pos(x1, y1):runAction( cc.Sequence:create(
        cc.EaseIn:create( cc.MoveTo:create(0.6, cc.p(x2, y2)), 3 ),
        cc.CallFunc:create( function()
            if tolua.isnull(self) then return end
            local cfg = room_act_configs[act_index]
            if not cfg then return end
            local ani = helper.app.createAnimation( unpack(cfg[1]) )
            ani:scale(cfg[3] or 1):pos(x2, y2 + cfg[2]):zorder(10):addTo(self)
        end ),
        cc.RemoveSelf:create(true)
    ) )
end


-------------------------------------------------------------------------------
-- 初始数据扩展
-------------------------------------------------------------------------------
function GameViewLayerEx:onInitDataEx()
    -- 可能存在GameViewLayer创建前就已经存在的数据，需要导入
    local table_id = cs.app.room_frame:GetTableID()
    local chair_count = cs.app.room_frame:GetChairCount()
    for chair_id = 0, chair_count - 1 do
        local user_item = cs.app.room_frame:getTableUserItem(table_id, chair_id)
        if user_item then
            local view_id = self._scene:SwitchViewChairID(chair_id)
            self.m_sparrowUserItem[view_id] = user_item
        end
    end
end


-------------------------------------------------------------------------------
-- 初始玩家数据扩展
-------------------------------------------------------------------------------
function GameViewLayerEx:onInitUserEx(player)
    if GlobalUserItem.bIsRedArena then
        player:child('label_score'):hide()
        local sp = cc.Sprite:create()
        sp:setName('sp_win')
        sp:addTo(player):pos(player:child('label_score'):pos())
    end
end


-------------------------------------------------------------------------------
-- 更新玩家数据扩展
-------------------------------------------------------------------------------
function GameViewLayerEx:onUpdateUserEx(player, user_item)
    if GlobalUserItem.bIsRedArena then
        if not user_item then
            player:child('sp_win'):hide()
        else
            local win = math.max(0, math.min(7, user_item.nWin or 0))
            local sp_win = player:child('sp_win')
            if win == 0 then
                sp_win:hide()
            else
                sp_win:show():texture( 'word/font_continue_win_' .. win .. '.png' )
            end
        end
    end
end


return GameViewLayerEx

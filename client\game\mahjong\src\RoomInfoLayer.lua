-------------------------------------------------------------------------------
--  创世版1.0
--  私人房游戏顶层
--  @date 2018-01-02
--  @auth woodoo
-------------------------------------------------------------------------------
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')
local RoomInfoBase = cs.app.client('system.RoomInfoBase')


local RoomInfoLayer = class('RoomInfoLayer', RoomInfoBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomInfoLayer:ctor( game_layer )
    print('RoomInfoLayer:ctor...')
    self.super.ctor(self, game_layer)
end


-------------------------------------------------------------------------------
-- 生成邀请信息
-------------------------------------------------------------------------------
function RoomInfoLayer:makeShareInfo()
    local data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[GlobalUserItem.nCurGameKind]

    local name = config.NAME
    local brand = cs.game.BRAND
    local room_id = data.szServerID
    local jushu = data.nPlayCount
    local cost = data.nFee
    local pay_type = data.cbPayType
    local zhifu = LANG{'CREATE_ZHIFU_'..pay_type, fangka = cost}
    if jushu < 0 then
        if jushu > -10000 then
            if config.DAKUN_DESC then
               local s, flag = string.gsub( config.DAKUN_DESC, "$kun", -jushu )   -- abpqefg 1
               jushu = s
            else
               jushu = LANG{'ROOM_KUN', kun = -jushu}
            end
        else
            jushu = LANG{'ROOM_QUAN', kun=-(jushu + 10000)}
        end
    else
        jushu = LANG{'ROOM_JU', jushu=jushu}
    end
    local renshu = data.cbChairCount    -- 后面这个有过为0的bug， PassRoom:getInstance():getChairCount()
    local difen = data.lCellScore
    local wanfa = self:makeRuleStr()

    local url = helper.app.makeInviteUrl(room_id)
    if cs.app.INVITE_SHOW_SPREADER then
        brand = GlobalUserItem.szSpreaderName
        if not brand or brand == '' then brand = LANG.BLANK end
    end
    local title = LANG{'ROOM_INVITE_TITLE', brand = brand, name = name, room = room_id}
	local desc = LANG{'ROOM_INVITE_CONTENT', jushu = jushu, renshu = renshu, difen = difen, zhifu = zhifu, wanfa = wanfa }

    return url, title, desc
end


-------------------------------------------------------------------------------
-- 设置财神，外部调用
--  t_cai: 可以是单个数值表示只有一个财神，也可以是多个数值的数组表示多财神
-------------------------------------------------------------------------------
function RoomInfoLayer:setCaishen(t_cai)
    local panel_cai = self.main_node:child('panel_cai')
    panel_cai:removeAllChildren()
    if type(t_cai) == 'number' then t_cai = {t_cai} end

    local num = #t_cai
    local size = panel_cai:size()
    local scale = num == 1 and 0.8 or 0.5
    local x_arr = num == 1 and {size.width/2} or {size.width/2-35, size.width/2 + 35}
    if GameLogic.MAGIC_INDEX == (cmd.MAX_CARD_INDEX + 1) then
        local card = display.newSprite(GlobalUserItem.getCardPath() .. '/font_big/card_back.png')
        card:scale(scale):pos(x_arr[1], size.height/2):addTo(panel_cai)
    else
        for i, card_data in ipairs(t_cai) do
            if card_data > 0 then
                local card = cs.game.util.createCard(card_data, cmd.MY_VIEWID, 'hand')
                card:scale(scale):pos(x_arr[i], size.height/2):addTo(panel_cai)
                -- display.newSprite('room/icon_cai.png'):anchor(0,0):pos(-3, 0):addTo(card)
            end
        end         
    end
end

-------------------------------------------------------------------------------
-- 设置痞子
-- 
-------------------------------------------------------------------------------
function RoomInfoLayer:setPiCard( t_pi )
    local panel_pizi = self.main_node:child('panel_pizi')
    panel_pizi:removeAllChildren()
    if type(t_pi) == 'number' then t_pi = {t_pi} end
    local num = #t_pi
    local size = panel_pizi:size()
    local scale = num == 1 and 0.8 or 0.8
    local x_arr = num == 1 and {size.width/2} or {size.width/2-35, size.width/2 + 35}
    for i, card_data in ipairs( t_pi ) do
        if card_data > 0 then
            print('设置痞子', card_data)
            local card = cs.game.util.createCard(card_data, cmd.MY_VIEWID, 'hand')
            card:scale(scale):pos(x_arr[1], size.height/2):addTo(panel_pizi)
            -- display.newSprite('room/icon_pi.png'):anchor(0,0):pos(-3, 0):addTo(card)
        end
    end   
end


-------------------------------------------------------------------------------
-- 设置改变,重新设置牌面缩放
-------------------------------------------------------------------------------
function RoomInfoLayer:onSettingChange()
    local panel_cai = self.main_node:child('panel_cai')
    local scale = GlobalUserItem.nCardFontScale / 100
    for i, card in ipairs( panel_cai:getChildren() ) do
        local font = card:getChildByTag(1)
        if font then
            font:scale(scale)
        end
    end
end


-------------------------------------------------------------------------------
-- 设置改变,重新设置牌面缩放
-------------------------------------------------------------------------------
function RoomInfoLayer:updateSice( isShow , sice1, sice2 )
    local panel_cai = self.main_node:child('panel_sice')
    panel_cai:setVisible( isShow )
    if isShow then
       panel_cai:child('img_sice_1'):texture('common/shaizi/'..sice1..'.png') 
       panel_cai:child('img_sice_2'):texture('common/shaizi/'..sice2..'.png') 
    end
end

-------------------------------------------------------------------------------
-- 设置剩余牌，外部调用
-------------------------------------------------------------------------------
function RoomInfoLayer:setCunNum( num )
    if num < 0 then
        return
    end
    print('-- 设置存量', num )
    local label_cun = self.main_node:child('label_cun')
    label_cun:show()
    label_cun:setString(LANG{'GAME_CUN_NOTICE', cun = num})

end

return RoomInfoLayer
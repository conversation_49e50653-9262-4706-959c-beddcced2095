local GameLogic = {}


--**************    扑克类型    ******************--
--混合牌型
GameLogic.OX_VALUEO				= 100

GameLogic.USER_OPERATE_PASS     = 200
GameLogic.USER_OPERATE_HINT     = 201
GameLogic.USER_OPERATE_OUTCARD  = 202
GameLogic.USER_OPERATE_REPUSH 	= 203


--最大手牌数目
GameLogic.MAX_CARDCOUNT			= 5
--牌库数目
GameLogic.FULL_COUNT			= 52
--正常手牌数目
GameLogic.NORMAL_COUNT			= 5


--- 排序方案
GameLogic.ST_ORDER              = 0           -- 大小
GameLogic.ST_COUNT              = 1           -- 大小
GameLogic.ST_VALUE              = 2           -- 大小

--扑克类型
GameLogic.CT_ERROR					=	0	--错误类型
GameLogic.CT_SINGLE					=	1	--单牌类型
GameLogic.CT_DOUBLE					=	2	--对子类型
GameLogic.CT_THREE_TAKE				=	3	--三条类型
GameLogic.CT_SINGLE_LINK			=	4	--对连类型
GameLogic.CT_DOUBLE_LINK			=	5	--三连类型
GameLogic.CT_THREE_LESS				=	6	--顺子类型
GameLogic.CT_PLANE					=	7	--炸弹类型
GameLogic.CT_PLANE_LESS				=	8	--天王炸弹
GameLogic.CT_BOMB_CARD				=	9	--排炸类型
GameLogic.CT_BOMB_MAX				=	10	--排炸类型



--取模
function GameLogic:mod(a,b)
    return a - math.floor(a/b)*b
end

--获得牌的数值
function GameLogic:getCardValue(cbCardData)
    return self:mod(cbCardData, 16)
end

--获得牌的数值
function GameLogic:getCardLogicValue(cbCardData)
	local val = self:mod(cbCardData, 16)
	if val == 0 then
		return val
	end
	if val >= 0x0E then
		val = val + 2
	elseif val <= 2 then
		val = val + 13
	end	
	return val
end

--获得牌的颜色（0 -- 4）
function GameLogic:getCardColor(cbCardData)
    return math.floor(cbCardData/16)
end

--拷贝表
function GameLogic:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
end

function GameLogic:SortCardList( cbCardData, cbCardCount, cbSortType )
	if cbCardCount <= 0 then
		return 
	end

	cbSortType = cbSortType or GameLogic.ST_ORDER

	if cbSortType == GameLogic.ST_ORDER then
		table.sort(cbCardData, 
			function(a, b) 
				local a_v = self:getCardLogicValue(a)
				local b_v = self:getCardLogicValue(b)
				if a_v > b_v then
					return true
				elseif a_v < b_v then
					return false
				else
					return a > b
				end
			end)
	elseif cbSortType == GameLogic.ST_VALUE then
		table.sort(cbCardData, function(a, b) 
			local a_v = self:getCardValue(a)
			local b_v = self:getCardValue(b)
			if a_v > b_v then
				return true
			elseif a_v < b_v then
				return false
			else
				return a > b
			end
		end)
	end
	
end

--排序
function GameLogic:sort(cbCardData, sort_card_num)
	sort_card_num = sort_card_num or #cbCardData
	self:SortCardList(cbCardData, sort_card_num)	
end

--是否连牌
function GameLogic:IsStructureLink(cbCardData, cbCardCount, cbCellCount)
	if self:mod(cbCardCount, cbCellCount) ~= 0 then
		return false
	end

    local cbBlockCount = cbCardCount / cbCellCount
    
    if cbCellCount == 1 then
        self:SortCardList(cbCardData, cbCardCount, GameLogic.ST_VALUE)
        local cbFirstValue = self:getCardValue(cbCardData[1])
        local is_link = true
        for i = 2, cbBlockCount do
            -- print('cbBlockCount is ', cbBlockCount, self:getCardLogicValue(cbCardData[i * cbCellCount]))
             if cbFirstValue ~= (self:getCardValue(cbCardData[i]) + i - 1) then
                is_link = false
                break
             end
        end 

        if is_link then return true end
    end

    self:SortCardList(cbCardData, cbCardCount, GameLogic.ST_ORDER)
    local cbFirstValue = self:getCardLogicValue(cbCardData[1])

 
	if cbFirstValue > 15 then return  false end

    for i = 2, cbBlockCount do
       -- print('cbBlockCount is ', cbBlockCount, self:getCardLogicValue(cbCardData[i * cbCellCount]))
		if cbFirstValue ~= (self:getCardLogicValue(cbCardData[i * cbCellCount]) + i - 1) then
			return false
		end
	end

	return true
end

function GameLogic:AnalysebCardData(cbCardData, cbCardCount)
	local AnalyseResult = {}
	AnalyseResult.cbBlockCount = {0, 0, 0, 0}
	AnalyseResult.cbCardData = {}
	for i = 1, 4 do
		AnalyseResult.cbCardData[i] = {}
	end
	 
	local i = 1
	while i <= cbCardCount do
		local cbSameCount = 1
		local cbLogicValue = self:getCardLogicValue(cbCardData[i])

		--搜索同牌
		for j = i + 1, cbCardCount do
			if self:getCardLogicValue(cbCardData[j] ) ~= cbLogicValue then
				break
			end
			cbSameCount = cbSameCount + 1
        end
        
        if cbSameCount > 4 then
            print("这儿有错误")
            return
        end

		--设置结果
		local cbIndex = AnalyseResult.cbBlockCount[cbSameCount]
		AnalyseResult.cbBlockCount[cbSameCount] = AnalyseResult.cbBlockCount[cbSameCount] + 1
		--print('cbSameCount is ', cbSameCount)
		for j = 1, cbSameCount do
            --table.insert(AnalyseResult.cbCardData[cbSameCount], cbCardData[i + j - 1])
            AnalyseResult.cbCardData[cbSameCount][cbIndex * cbSameCount+j] = cbCardData[i + j - 1]
		end

		i = i + cbSameCount
	end
	--dump(AnalyseResult)

	return AnalyseResult
end


function GameLogic:GetCardType(cbCardData, cbCardCount)
	local cbStarLevel = 0
	if cbCardCount == 0 then
		return GameLogic.CT_ERROR, cbStarLevel
	elseif cbCardCount == 1 then
		return GameLogic.CT_SINGLE, cbStarLevel
    end
    
    self:SortCardList(cbCardData,cbCardCount,GameLogic.ST_ORDER)


	print('普通分析 开始')
	local AnalyseResult = self:AnalysebCardData(cbCardData, cbCardCount)
    --dump("普通分析 结束")
    --dump(cbCardData)
    --dump(AnalyseResult)

    --同牌判断
	if cbCardCount == 3 and AnalyseResult.cbBlockCount[3] == 1 then return GameLogic.CT_THREE_LESS, cbStarLevel end
	if cbCardCount == 2 and AnalyseResult.cbBlockCount[2] == 1 then return GameLogic.CT_DOUBLE, cbStarLevel end


	--同相炸弹
	if cbCardCount == 4 and AnalyseResult.cbBlockCount[4] == 1 then
		cbStarLevel = cbCardCount
		return GameLogic.CT_BOMB_CARD, cbStarLevel
    end
    if cbCardCount == 3 and AnalyseResult.cbBlockCount[3] == 1 then
        cbStarLevel = 5
        return GameLogic.CT_BOMB_MAX, cbStarLevel
    end
    
    if cbCardCount == 4 and AnalyseResult.cbBlockCount[3] == 1 then
        return GameLogic.CT_THREE_LESS, cbStarLevel        
    end

	--对连类型
	if cbCardCount >= 4 and AnalyseResult.cbBlockCount[2] * 2 == cbCardCount then
		local cbDoubleCount = AnalyseResult.cbBlockCount[2] * 2
		if self:IsStructureLink(AnalyseResult.cbCardData[2],cbDoubleCount,2)==true then
			return GameLogic.CT_DOUBLE_LINK, cbStarLevel
		end
    end
    
    if cbCardCount == 5 and AnalyseResult.cbBlockCount[3] ==1 then
		return GameLogic.CT_THREE_TAKE, cbStarLevel
	end

	--三连类型
    if cbCardCount >= 6 then
        local left_count = cbCardCount - AnalyseResult.cbBlockCount[3] * 3
        local cbThreeCount = AnalyseResult.cbBlockCount[3] * 3
        if left_count > 0 and AnalyseResult.cbBlockCount[3] > 0 and 
            self:IsStructureLink(AnalyseResult.cbCardData[3], cbThreeCount, 3)==true then
            if left_count >= 0 and left_count < AnalyseResult.cbBlockCount[3] * 2 then
                return GameLogic.CT_PLANE_LESS, cbStarLevel
            elseif left_count == AnalyseResult.cbBlockCount[3] * 2 then
                return GameLogic.CT_PLANE, cbStarLevel
            end
        end
	end

	-- 顺子类型
    if cbCardCount >= 5 and AnalyseResult.cbBlockCount[1] == cbCardCount then
        print('111111111')
		if self:IsStructureLink(AnalyseResult.cbCardData[1], cbCardCount, 1) == true then
			return GameLogic.CT_SINGLE_LINK, cbStarLevel
		end
	end

	return  GameLogic.CT_ERROR, cbStarLevel
end 

function GameLogic:concat(a, b)
	local result = {}
	for _, v in ipairs(a) do
		table.insert( result, v)
	end
	for _, v in ipairs(b) do
		table.insert( result, v)
	end

	return result
end

-- 获取相同的牌
function GameLogic:anayseCard(cbCardData, card_count, is_sort_card_value, is_out_card)
	is_sort_card_value = is_sort_card_value or false
	is_out_card = is_out_card or false
	local max_king_card = {}
	local min_king_card = {}
	local vec_cards = {}
	local cards = {}
	local pre_card = 0
	local king_is_ok = false
	self:sort(cbCardData)
	--dump(cbCardData)
	for i = 1, card_count do repeat
		if cbCardData[i] == 0 then
			break
		end
		if cbCardData[i] == 0x4F then
			table.insert(max_king_card, cbCardData[i])
			break
		end
		if cbCardData[i] == 0x4E then
			table.insert(min_king_card, cbCardData[i])
			break
		end
		if not king_is_ok then
			pre_card = cbCardData[i];
			king_is_ok = true;
			table.insert(cards, cbCardData[i]);
			print('first not king ', self:getCardValue(cbCardData[i]))
			break
		end

		if self:getCardValue(pre_card) == self:getCardValue(cbCardData[i]) then
			table.insert(cards, cbCardData[i])
			break
		end
		--print('current card data ', self:getCardValue(cbCardData[i]))
		--dump(cards)
		table.insert(vec_cards, cards)
		cards = {}
		pre_card = cbCardData[i]
		table.insert(cards, cbCardData[i])
	until true
	end

	if #cards > 0 then
		table.insert(vec_cards, cards)
	end

	if #min_king_card > 0 then
		table.insert(vec_cards, 1, min_king_card)
	end

	if #max_king_card > 0 then
		table.insert(vec_cards, 1, max_king_card)
	end

	return vec_cards
end


function GameLogic:compareTwoCard(card_a, card_b)
	local a_v = self:getCardLogicValue(card_a)
	local b_v = self:getCardLogicValue(card_b)
	return a_v < b_v
end

function GameLogic:GuanCard(vec_cards, out_card, can_spilt_card)
	can_spilt_card = can_spilt_card or false
	local result = {}
	local is_has_zha = false
	--dump(out_card)
	for _, v in ipairs(vec_cards) do repeat
		if out_card.card_num == 0 then
			table.insert(result, v.card)
			break
		end
		if not is_has_zha then
			if v.card_num >= 4 then
				is_has_zha = true
			end
		end
		if out_card.card_num >= 4 then
			if out_card.card_num < v.card_num then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if v.is_single_king and not out_card.is_single_king then
					table.insert(result, v.card)
					break
				end
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end
			end
			
		end
		if out_card.card_num < 4 then
			if v.card_num >= 4 then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end 
			end
		end

	until true
	end

	if can_spilt_card == false then
		return result
	end

	if #result == 0 and not is_has_zha then
		for _, v in ipairs(vec_cards) do
			if self:compareTwoCard(out_card.card_real_v, v.card_v) and out_card.card_num <= #v.card then
				local ret_vec = {}
				for i = 1, out_card.card_num do
					table.insert(ret_vec, v.card[i])
				end
				table.insert(result, ret_vec)
			end
		end
	end
	return result
end

--同牌搜索
function GameLogic:SearchSameCard(cbHandCardData, cbHandCardCount, cbReferCard, cbSameCardCount)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
	local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--local cbCardData = self:copyTab(cbHandCardData)
	local cbCardData = cbHandCardData
	local cbCardCount = cbHandCardCount
	if cbCardCount < cbSameCardCount then
		return tagSearchCardResult
    end

    --排序扑克
    GameLogic:SortCardList(cbCardData, cbHandCardCount, GameLogic.ST_ORDER)
    --分析结构
    local tagAnalyseResult = GameLogic:AnalysebCardData(cbCardData, cbCardCount)
    --dump(tagAnalyseResult, "tagAnalyseResult", 6)
    local cbReferLogicValue =  0 --(cbReferCard == 0 and 0 or GameLogic:getCardLogicValue(cbReferCard))
    if cbReferCard and cbReferCard ~= 0 then
        cbReferLogicValue = GameLogic:getCardLogicValue(cbReferCard)
    end
    print('cbReferLogicValue num is ', cbReferLogicValue)
    local cbBlockIndex = cbSameCardCount
    while cbBlockIndex <= 4 do
        for i=1,tagAnalyseResult.cbBlockCount[cbBlockIndex] do
            local cbIndex = (tagAnalyseResult.cbBlockCount[cbBlockIndex] - i) * cbBlockIndex + 1
            local cbNowLogicValue = GameLogic:getCardLogicValue(tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex])
            local bIsAdd = false
            local search_num = 0
            if cbSameCardCount <= cbBlockIndex then
                if cbNowLogicValue > cbReferLogicValue then
                    bIsAdd = true
                    search_num = cbSameCardCount
                end
            end
            --[[
            if cbReferLogicValue == 13 and cbSameCardCount == 3 then
                if cbBlockIndex >= 4 then
                    bIsAdd = false
                end
            end
            --]]
            if bIsAdd then
                cbResultCardCount[cbResultCount] = search_num
                tagSearchCardResult[2] = cbResultCardCount
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount][1] = tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex]
                for i=2, search_num do
                    cbResultCard[cbResultCount][i] = tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex+i-1]
                end --此处修改
                tagSearchCardResult[3] = cbResultCard
                cbResultCount = cbResultCount + 1
            end
        end
        cbBlockIndex = cbBlockIndex + 1
    end
    tagSearchCardResult[1] = cbResultCount - 1
    return tagSearchCardResult
end

--分析分布
function GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    local cbCardCount1 = 0
    local cbDistributing = {}
    for i=1,15 do
        local distributing = {}
        for j=1,6 do
            distributing[j] = 0
        end
        cbDistributing[i] = distributing
    end
    local Distributing = {cbCardCount1,cbDistributing}
    for i=1,cbCardCount do
        if cbCardData[i] ~= 0 then
            local cbCardColor = GameLogic:getCardColor(cbCardData[i])
            local cbCardValue = GameLogic:getCardValue(cbCardData[i])
            --分布信息
            cbCardCount1 = cbCardCount1 + 1
            cbDistributing[cbCardValue][5+1] = cbDistributing[cbCardValue][6]+1
            local color = bit:_rshift(cbCardColor,4) + 1
            cbDistributing[cbCardValue][color] = cbDistributing[cbCardValue][color]+1
        end
    end
    Distributing[1] = cbCardCount1
    Distributing[2] = cbDistributing
    -- print("总数：" .. Distributing[1])
    -- for i=1,15 do
    --     print("每张总数：" .. Distributing[2][i][6])
    -- end
    return Distributing
end

--构造扑克
function GameLogic:MakeCardData(cbValueIndex,cbColorIndex)
    --print("构造扑克 " ..bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)..",".. GameLogic:getCardLogicValue(bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)))
    return bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)
end

-- 判断是否有2这张牌
function GameLogic:IsHasTwoCard( cbHandCardData, cbHandCardCount )
    -- body
    for i = 1, cbHandCardCount do
        if self:getCardValue(cbHandCardData[i]) == 0x02 then
            return true
        end
    end

    return false
end

--连牌搜索
function GameLogic:SearchLineCardType(cbHandCardData, cbHandCardCount, cbReferCard, cbBlockCount, cbLineCount)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--排序扑克
	local cbCardData = cbHandCardData
    GameLogic:SortCardList(cbCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount
    --连牌最少数
    local cbLessLineCount = 0
    if cbLineCount == 0 then
        if cbBlockCount == 1 then
            cbLessLineCount = 5
        elseif cbBlockCount == 2 then
            cbLessLineCount = 2
        else
            cbLessLineCount = 3
        end
    else
        cbLessLineCount = cbLineCount
    end

    --print("连牌最少数 " .. cbLessLineCount)
    local cbReferIndex = 1
    if cbReferCard ~= 0 then
        if (GameLogic:getCardLogicValue(cbReferCard)-cbLessLineCount) >= 2 then
            cbReferIndex = GameLogic:getCardLogicValue(cbReferCard)-cbLessLineCount+1+1
        end
    end 
    --超过A
    if cbReferIndex+cbLessLineCount > 15 then
        return tagSearchCardResult
    end
    --长度判断
    if cbHandCardCount < cbLessLineCount * cbBlockCount then
        return tagSearchCardResult
    end
   -- print("搜索顺子开始点 " .. cbReferIndex)
    local Distributing = GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    --搜索顺子
    local cbTmpLinkCount = 0
    local cbValueIndex=cbReferIndex
    local flag = false
    while cbValueIndex <= 13 do
        if cbResultCard[cbResultCount] == nil then
            cbResultCard[cbResultCount] = {}
        end
        if Distributing[2][cbValueIndex][6] < cbBlockCount then
            if cbTmpLinkCount < cbLessLineCount  then
                cbTmpLinkCount = 0
                flag = false
            else
                cbValueIndex = cbValueIndex - 1
                flag = true
            end
        else
            cbTmpLinkCount = cbTmpLinkCount + 1
            if cbLineCount == 0 then
                flag = false
            else
                flag = true
            end
        end
        if flag == true then
            flag = false
            if cbTmpLinkCount >= cbLessLineCount then
                --复制扑克
                local cbCount = 0
                local cbIndex=(cbValueIndex-cbTmpLinkCount+1)
                while cbIndex <= cbValueIndex do
                    local cbTmpCount = 0
                    local cbColorIndex=1
                    while cbColorIndex <= 4 do --在四色中取一个
                        local cbColorCount = 1
                        while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                            cbCount = cbCount + 1
                            cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                            tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                            cbTmpCount = cbTmpCount + 1
                            if cbTmpCount == cbBlockCount then
                                break
                            end
                            cbColorCount = cbColorCount + 1
                        end
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorIndex = cbColorIndex + 1
                    end
                    cbIndex = cbIndex + 1
                end
                tagSearchCardResult[2][cbResultCount] = cbCount
                cbResultCount = cbResultCount + 1
                if cbLineCount ~= 0 then
                    cbTmpLinkCount = cbTmpLinkCount - 1
                else
                    cbTmpLinkCount = 0
                end
            end
        end
        cbValueIndex = cbValueIndex + 1
    end

    --特殊顺子(寻找A)
    if cbTmpLinkCount >= cbLessLineCount-1 and cbValueIndex == 14 then
        --print("特殊顺子(寻找A)")
        if (Distributing[2][1][6] >= cbBlockCount) or (cbTmpLinkCount >= cbLessLineCount) then
            if cbResultCard[cbResultCount] == nil then
                cbResultCard[cbResultCount] = {}
            end
            --复制扑克
            local cbCount = 0
            local cbIndex=(cbValueIndex-cbTmpLinkCount)
            while cbIndex <= 13 do
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
                cbIndex = cbIndex + 1
            end
            --复制A
            if Distributing[2][1][6] >= cbBlockCount then
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][1][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(1,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
            end
            tagSearchCardResult[2][cbResultCount] = cbCount
            cbResultCount = cbResultCount + 1
        end
    end
    tagSearchCardResult[1] = cbResultCount - 1
    return tagSearchCardResult
end

function GameLogic:SearchTakeCardType( cbHandCardData, cbHandCardCount, cbReferCard, cbSameCount, cbTakeCardCount)
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}

    self:SortCardList(cbHandCardData, cbHandCardCount, GameLogic.ST_ORDER)

    local result = GameLogic:SearchSameCard(cbHandCardData, cbHandCardCount, cbReferCard, cbSameCount)

    if result[1] == 0 then
        return tagSearchCardResult
    end
    --dump(result)

    --local result_analyse = GameLogic:AnalysebCardData(cbHandCardData, cbHandCardCount)
    local cbNeedCount = cbSameCount + cbTakeCardCount
    --print('cbNeedCount is ', cbNeedCount)
    if cbTakeCardCount == 2 then
        for i = 1, result[1] do
            local sameValue = self:getCardLogicValue(result[3][i][1])
            for j = 1, cbHandCardCount - 1 do repeat
                local cur_val = self:getCardLogicValue(cbHandCardData[j])
                if cur_val == sameValue then break end
                if j > 1 then
                    local pre_val = self:getCardLogicValue(cbHandCardData[j - 1])
                    if pre_val == cur_val then break end
                end
                for k = j + 1, cbHandCardCount do repeat
                    cur_val = self:getCardLogicValue(cbHandCardData[k])
                    if cur_val == sameValue then break end

                    if k > j + 1 then
                        local pre_val = self:getCardLogicValue(cbHandCardData[k - 1])
                        if pre_val == cur_val then break end
                    end

                    cbResultCardCount[cbResultCount] = cbNeedCount
                    tagSearchCardResult[2] = cbResultCardCount
                    cbResultCard[cbResultCount] = {}
                    
                    for m = 1, cbSameCount do
                        cbResultCard[cbResultCount][m] = result[3][i][m]
                    end --此处修改

                    cbResultCard[cbResultCount][cbSameCount + 1] = cbHandCardData[j]
                    cbResultCard[cbResultCount][cbSameCount + 2] = cbHandCardData[k]
                    tagSearchCardResult[3] = cbResultCard
                    cbResultCount = cbResultCount + 1
                until true
                end
            until true
            end
       
        end
    elseif cbTakeCardCount == 1 then
        for i = 1, result[1] do 
            local sameValue = self:getCardLogicValue(result[3][i][1])
            for j = 1, cbHandCardCount - 1 do repeat
                local cur_val = self:getCardLogicValue(cbHandCardData[j])
                if cur_val == sameValue then break end
                if j > 1 then
                    local pre_val = self:getCardLogicValue(cbHandCardData[j - 1])
                    if pre_val == cur_val then break end
                end

                cbResultCardCount[cbResultCount] = cbNeedCount
                tagSearchCardResult[2] = cbResultCardCount
                cbResultCard[cbResultCount] = {}
                
                for m = 1, cbSameCount do
                    cbResultCard[cbResultCount][m] = result[3][i][m]
                end --此处修改

                cbResultCard[cbResultCount][cbSameCount + 1] = cbHandCardData[j]
                tagSearchCardResult[3] = cbResultCard
                cbResultCount = cbResultCount + 1
            until true
            end
       
        end
    end
    cbResultCount = cbResultCount - 1
    tagSearchCardResult[1] = cbResultCount

    --dump(tagSearchCardResult)

    return tagSearchCardResult
end

--出牌搜索
function GameLogic:SearchOutCard(cbHandCardData,cbHandCardCount,cbTurnCardData,cbTurnCardCount) 
    print("出牌搜索")
    --dump(cbTurnCardData)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
    
	--排序扑克
	local cbCardData = cbHandCardData --= self:copyTab(cbHandCardData)
	local cbCardCount = cbHandCardCount
    GameLogic:SortCardList(cbCardData, cbCardCount, GameLogic.ST_ORDER)
    GameLogic:SortCardList(cbTurnCardData, cbTurnCardCount, GameLogic.ST_ORDER)
	
    --出牌分析
	local cbTurnOutType, cbStarLevel = GameLogic:GetCardType(cbTurnCardData, cbTurnCardCount)
	print("cbTurnCardData type", cbTurnOutType, cbStarLevel)
    if cbTurnOutType == GameLogic.CT_ERROR then --错误类型
        print("上家为空")
		--是否一手出完
		local type, lvl = GameLogic:GetCardType(cbCardData, cbCardCount)
		print("cbTurnCardData type", type, lvl)
        if type ~= GameLogic.CT_ERROR  then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = cbCardData
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        --如果最小牌不是单牌，则提取
        local cbSameCount = 1  
        --[[
		if cbCardCount > 1 and (GameLogic:getCardLogicValue(cbCardData[cbCardCount]) == 
		GameLogic:getCardLogicValue(cbCardData[cbCardCount-1])) then
            cbSameCount = 2
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount][1] = cbCardData[cbCardCount]
            local cbCardValue = GameLogic:getCardLogicValue(cbCardData[cbCardCount])
            local i = cbCardCount - 1
            while i >= 1 do
                if GameLogic:getCardLogicValue(cbCardData[i]) == cbCardValue then
                    cbResultCard[cbResultCount][cbSameCount] = cbCardData[i]
                    cbSameCount = cbSameCount + 1
                end
                i = i - 1
            end
            cbResultCardCount[cbResultCount] = cbSameCount-1
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        --]]
		--单牌
		print("单牌", cbSameCount)
        local cbTmpCount = 1
        if cbSameCount ~= 2 then
            --print("单牌Pan")
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 1)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
		--对牌
		print("对牌", cbSameCount)
        if cbSameCount ~= 3 then
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 2)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
		      
        --单连
        print("单连", cbSameCount)
        local tagSearchCardResult4 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 1, 0)
        cbTmpCount = tagSearchCardResult4[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult4[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult4[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end

        --连对
        print("连对", cbSameCount)
        local tagSearchCardResult5 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 2, 0)
        cbTmpCount = tagSearchCardResult5[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult5[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult5[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        
		--炸弹
		print("3带2", cbSameCount)
        local tagSearchCardResult1 = GameLogic:SearchTakeCardType(cbCardData, cbCardCount, 0, 3, 2)
        cbTmpCount = tagSearchCardResult1[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end

        --炸弹
		print("4", cbSameCount)
        local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 4, 0)
        cbTmpCount = tagSearchCardResult1[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end

        tagSearchCardResult[1] = cbResultCount - 1
        return tagSearchCardResult
    elseif cbTurnOutType == GameLogic.CT_SINGLE or cbTurnOutType == GameLogic.CT_DOUBLE then
        --单牌、对牌、三条
        local cbReferCard = cbTurnCardData[1]
        local cbSameCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE then
            cbSameCount = 2
        end
        print('cbReferCard is ', cbReferCard)
        local tagSearchCardResult21 = GameLogic:SearchSameCard(cbCardData, cbCardCount, cbReferCard, cbSameCount)
        cbResultCount = tagSearchCardResult21[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult21[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult21[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1

    elseif cbTurnOutType == GameLogic.CT_THREE_TAKE then
        --单牌、对牌、三条
        local cbReferCard = cbTurnCardData[3]
        local tagSearchCardResult21 = GameLogic:SearchTakeCardType(cbCardData, cbCardCount, cbReferCard, 3, 2)
        cbResultCount = tagSearchCardResult21[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult21[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult21[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    
    elseif cbTurnOutType == GameLogic.CT_PLANE then

        local result = GameLogic:AnalysebCardData(cbTurnCardData, cbTurnCardCount)
        
        --搜索炸弹
        local tagSearchCardResult61 = GameLogic:getPlane(cbCardData, cbCardCount, result.cbCardData[3][1], result.cbBlockCount[3])
        local cbTmpResultCount = tagSearchCardResult61[1]
        for i=1,cbTmpResultCount do
            cbResultCardCount[cbResultCount] = tagSearchCardResult61[2][i]
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = tagSearchCardResult61[3][i]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        end
        tagSearchCardResult[1] = cbResultCount - 1
    elseif cbTurnOutType == GameLogic.CT_SINGLE_LINK or cbTurnOutType == GameLogic.CT_DOUBLE_LINK then
        --单连、对连、三连
        local cbBlockCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE_LINK then
            cbBlockCount = 2
        end
        local cbLineCount = cbTurnCardCount/cbBlockCount
        local referCard = cbTurnCardData[1]
        if self:IsHasTwoCard(cbTurnCardData, cbTurnCardCount) then
            local tmpCards = self:copyTab(cbTurnCardData)
            self:SortCardList(tmpCards,cbTurnCardCount,GameLogic.ST_VALUE)
            referCard = tmpCards[1]
        end
        print('cbTurnOutType cbTurnOutType 11111')
        local tagSearchCardResult31 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, referCard, cbBlockCount, cbLineCount)
        print('cbTurnOutType cbTurnOutType 22222')
        cbResultCount = tagSearchCardResult31[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult31[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult31[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    end

    --搜索炸弹
	if (cbCardCount >= 4 and cbTurnOutType ~= GameLogic.CT_BOMB_MAX) then
        local cbReferCard = 0
        local card_num = 4
        if cbTurnOutType == GameLogic.CT_BOMB_CARD then
            cbReferCard = cbTurnCardData[2]
            card_num = cbTurnCardCount
		end
        
        --搜索炸弹
        local tagSearchCardResult61 = GameLogic:SearchSameCard(cbCardData,cbCardCount,cbReferCard, 4)
        local cbTmpResultCount = tagSearchCardResult61[1]
        for i=1,cbTmpResultCount do
            cbResultCardCount[cbResultCount] = tagSearchCardResult61[2][i]
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = tagSearchCardResult61[3][i]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        end
        tagSearchCardResult[1] = cbResultCount - 1
    end


    --搜索天王炸弹
    if (cbTurnOutType ~= GameLogic.CT_BOMB_MAX) and (cbCardCount >= 3) then
        local tagSearchCardResult61 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0x3D, 3)
        dump(tagSearchCardResult61)
        local cbTmpResultCount = tagSearchCardResult61[1]
        for i=1, cbTmpResultCount do repeat
            if GameLogic:getCardValue(tagSearchCardResult61[3][i][2]) ~= 0x01 then
                break
            end
            cbResultCardCount[cbResultCount] = tagSearchCardResult61[2][i]
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = tagSearchCardResult61[3][i]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
            break
        until ture
        end
        tagSearchCardResult[1] = cbResultCount - 1
    end

    --dump(tagSearchCardResult)
    return tagSearchCardResult
end

-- 获取飞机
function GameLogic:getPlane( cbHandCardData, cbHandCardCount, cbReferCard, cbLinkNum )
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}

    local cbNeedCount = 5 * cbLinkNum
    if cbNeedCount > cbHandCardCount then
        return tagSearchCardResult
    end


    local result = GameLogic:SearchLineCardType(cbHandCardData, cbHandCardCount, cbReferCard, 3, cbLinkNum)

    if result[1] == 0 then
        return tagSearchCardResult
    end

    for i = 1, result[1] do
        local link_val = {}
        for j = 1, cbLinkNum do
            local index = (j - 1) * 3 + 1
            local val = self:getCardLogicValue(result[3][i][index])
            table.insert(link_val, val)
        end
        local tmpCardData = {}
        for j = 1, cbHandCardCount do 
            local val = self:getCardLogicValue(cbCardData[j])
            local is_add = true
            for k = 1, cbLinkNum do
                if link_val[k] == val then
                    is_add = false
                    break
                end
            end
            if is_add then
                table.insert(tmpCardData, cbCardData[j])
            end
        end

        local left_count = #tmpCardData

        for j = 1, left_count - cbLinkNum * 2 + 1 do
            cbResultCardCount[cbResultCount] = cbNeedCount
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = {}
            
            for m = 1, result[i] do
                cbResultCard[cbResultCount][m] = result[3][i][m]
            end

            for m = 1, cbLinkNum * 2 do
                cbResultCard[cbResultCount][result[i] + m] = tmpCardData[j + m - 1]
            end
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        end
    end

    cbResultCount = cbResultCount - 1
    tagSearchCardResult[1] = cbResultCount

    return tagSearchCardResult
    
end



return GameLogic
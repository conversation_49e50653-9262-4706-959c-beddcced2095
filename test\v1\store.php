<?php
//活动接口
require(APPPATH . '/libraries/REST_Controller.php');
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Config.php');

class Store extends REST_Controller
{
    /*
     * 角色信息
     */
    private $_role;
    private $_game;
    private $_mall;

    private $_appId = '150155930270551';
    private $_appSecret = 'FdhPfq8XNzVQH27VEsPSO7A7k8WMK5XA';

    private $_hy_appId = '155384639152689';
    private $_hy_appSecret = 'kAOj4QXf0Lbym0uB1eIlnJeGq1NuPILI';

    private $_url = 'https://pay.ipaynow.cn';

    private $_hft_appId = 3401;
    private $_hft_appKey = '325f2568e19a4f73bb5d088c769863fb';

    public function __construct()
    {
        parent::__construct();
        $this->load->model('server_model');
        $this->load->model('user_model');
        $this->load->model('mp_model');
        $this->load->model('player_model');

        if ($this->uri->uri_string() != 'api/v1/store/notify' && $this->uri->uri_string() != 'api/v1/store/notify2' && $this->uri->uri_string() != 'api/v1/store/hft_notify'  && $this->uri->uri_string() != 'api/v1/store/hy_notify' ) {
            $this->player_model->set_database($this->_channel['game_id']);
            $this->_game = $this->server_model->get_game_by_id($this->_channel['game_id']);

            $role_id = $this->input->post('uid');

            if( $this->_game['game_id'] == 30) {
                $this->response(array('code' => 1, 'msg' => '请重启游戏客户端'));
            }

            // 获取角色信息
            $role = $this->player_model->get_role_info($role_id);

            if (!$role) {
                $this->response(array('code' => 1, 'msg' => '角色信息不存在'));
            }

            $this->_role = $role;

            $mall = $this->server_model->get_shelves_by_channel($this->_channel['channel_id']);//先根据渠道获取货架信息

            if (!$mall) {
                $this->response(array('code' => 1, 'msg' => '未查询到商城配置'));
            }

            $this->_mall = $mall;
        }
    }

    public function goods_post()
    {
        // 获取渠道对应的商城

        // 获取角色类型
        $role_type = 1;

//        $agent = $this->user_model->get_one_agent($this->_role['UserID'],$this->_channel['game_id']);
//
//        if($agent) {
//            if($agent['group'] == 3) {
//                $role_type = 2;
//            } else if($agent['group'] == 8) {
//                $role_type = 3;
//            }
//        }

        $result = $this->mp_model->get_role_is_first_order($this->_role['UserID'], $this->_channel['game_id']);//获取用户首充订单

        foreach ($this->_mall as $k => $v) {//循环货架信息
            $goods = $this->server_model->get_shelves_goods($v['mall_id'], $role_type, TRUE, $result);//根据货架绑定的模板ID获取绑定的商品
            $new_goods = [];
            foreach ($goods as $key => $good) {//循环商品
                $new_goods[$key]['id'] = $good['id'] * 1;
                $new_goods[$key]['name'] = $good['name'];
                $new_goods[$key]['desc'] = $good['desc'];
//            $new_goods[$k]['good_desc'] = $good['good_desc'];
                $new_goods[$key]['icon'] = $good['icon'];
                $new_goods[$key]['promotion_type'] = $good['promotion_type'] * 1;
                $new_goods[$key]['price_type'] = $good['price_type'] * 1;
                $new_goods[$key]['is_first'] = $good['is_first'] * 1;
                $new_goods[$key]['price'] = $good['price'] * 1;
                $new_goods[$key]['icon'] = str_replace('https://', 'http://', $good['icon']);
                $new_goods[$key]['stock'] = $good['stock'] * 1;
                $new_goods[$key]['favorable_desc'] = $good['favorable_desc'];
            }
            $result['goods'][$k]['name'] = $v['name'];//货架名
            $result['goods'][$k]['good'] = $new_goods;//商品列表
            $result['goods'][$k]['mall_id'] = $v['mall_id'];//模板ID
        }

        //获取用户房卡
        $score = $this->player_model->get_role_score($this->_role['UserID']);
        //获取用户金币
        $score2 = $this->player_model->get_role_score2($this->_role['UserID']);

        $result['fangka'] = $score['RoomCard'] * 1;
        $result['gold'] = (isset($score2['Score'])?$score2['Score']:0) + (isset($score2['ScoreT'])?$score2['ScoreT']:0);
        $result['jiangquan'] = isset($score2['Ticket']) ? $score2['Ticket'] : 0;
        $result['guess'] = isset($score2['GuessCoin']) ? $score2['GuessCoin'] : 0;

        $this->response(array('code' => 0, 'msg' => '', 'data' => $result));
    }

    public function order_post()
    {
        $good_id = $this->input->post('good_id');
        $mall_id = $this->input->post('mall_id');

        // 获取商品
        $mall_good = $this->server_model->get_shelves_good($mall_id, $good_id);

        if (empty($mall_good)) {
            $this->response(array('code' => 1, 'msg' => '商品不存在'));
        }

     //   if(in_array($mall_good['mall_id'],array(24,54,33,59,29,58))) 
//            if($this->_role['UserID'] != 2952) {
 if(in_array($mall_good['mall_id'],array(24,54,33,59,29,58))) 
{
	  $this->response(array('code' => 1, 'msg' => '请在首页按【传奇来了】按钮充值'));
}
else
{               
        $this->response(array('code' => 1, 'msg' => '微信支付系统升级中，请前往【'.$this->_game['mp_name'].'】公众号充值'));
}


        if ($mall_good['stock'] != '-99' && $mall_good['stock'] <= 0) {
            $this->response(array('code' => 1, 'msg' => '商品库存不足'));
        }

        // 现金支付
        if ($mall_good['price_type'] == 1) {

            $agent = $this->user_model->get_one_agent($this->_role['UserID'], $this->_game['game_id']);

            $this->load->helper('string');

            $order_no = date("YmdHis") . random_string('nozero', 3);

            $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'], $this->_game['game_id']);

            if (empty($mp_user)) {
                $mp_user['user_id'] = 0;
            }

            switch ($mall_good['good_type']) {
                case 1:
                    $good_type = '房卡';
                    break;
                case 2:
                    $good_type = '金币';
                    break;
                default:
                    $good_type = '房卡';
            }

            // 创建订单
            $data = array(
                'user_id' => $mp_user['user_id'],
                'role_id' => $this->_role['UserID'],
                'agent_id' => $agent ? $agent['id'] : 0,
                'spreader_id'=> $this->_role['SpreaderID'],
                'good_id' => $good_id,
                'game_id' => $this->_game['game_id'],
                'order_no' => $order_no,
                'is_first' => $mall_good['is_first'],
                'is_agent' => ($mall_good['type'] - 1),
                'total_fee' => $mall_good['price'],
                'is_mall' => 1,
                'mall_type' => $mall_good['good_type'],
                'mall_id' => $mall_id,
                'kind_id' => $this->_role['CommonKindID'],
                'create_time' => time()
            );

            $this->db->insert('mp_order', $data);

            $order_id = $this->db->insert_id();

//            if($this->_channel['channel_id'] == 60010002 && $this->_role['UserID'] == 2952) {
//                $this->_channel['pay_type'] = 'wx_app';
//            }

            if ($this->_channel['pay_type'] == 'wx_wap' || $this->_channel['pay_type'] == 'wx_wap_hy') {
                $param['appId'] = $this->_channel['pay_type'] == 'wx_wap'?$this->_appId:$this->_hy_appId;
                $param['deviceType'] = "0601";
                $param['frontNotifyUrl'] = base_url();
                $param['funcode'] = "WP001";
                $param['mhtCharset'] = "UTF-8";
                $param['mhtCurrencyType'] = "156";
                $param['mhtOrderAmt'] = $mall_good['price'] * 100;
                $param['mhtOrderDetail'] = $good_type;
                $param['mhtOrderName'] = $good_type;
                $param['mhtOrderNo'] = $order_no;
                $param['mhtOrderStartTime'] = date('YmdHis');
                $param['mhtOrderTimeOut'] = 3600;
                $param['mhtOrderType'] = "01";
                $param['mhtReserved'] = $order_no;
                $param['mhtSignType'] = "MD5";
                if($this->_channel['pay_type'] == 'wx_wap') {
                    $param['notifyUrl'] = base_url() . 'api/v1/store/notify';
                } else {
                    $param['notifyUrl'] = base_url() . 'api/v1/store/hy_notify';
                }
                $param['outputType'] = 2;
                $param['payChannelType'] = 13;
                $param['version'] = "1.0.0";

                $str = '';

                foreach ($param as $k => $v) {
                    if ($v) {
                        $str .= $k . '=' . $v . '&';
                    }
                }

                $str .= md5($this->_channel['pay_type'] == 'wx_wap'?$this->_appSecret:$this->_hy_appSecret);

                $param['mhtSignature'] = md5($str);

                $return = $this->_https_curl(http_build_query($param));

                parse_str($return, $result);

                if (isset($result['responseCode']) && ($result['responseCode'] == 'A001')) {
                    echo $this->response(array('code' => 0, 'msg' => '', 'data' => array('url' => $result['tn'], 'order_id' => $order_id)));
                } else {
                    echo $this->response(array('code' => 1, 'msg' => '下单错误'));
                }
            }  else if ($this->_channel['pay_type'] == 'wx_app') {
//                $mch_config = array(
//                    'appid' => 'wxd3b7fbc2c70a8d6a',
//                    'mchid' => '1487812592',
//                    'key' => '72bdea5fd144f5812697cf15bbd296d9',
//                    'appsecret' => 'ca6655b28e5ffb671ff2363b981e5f88',
//                );

                $mch_config = $this->mp_model->get_mch_config($this->_game['game_id']);

                if(!$mch_config) {
                    $this->response(array('code' => 1, 'msg' => '商户平台参数未配置'));
                }

                WxPayConfig::setConfig($mch_config);

                //②、统一下单
                $input = new WxPayUnifiedOrder();
                $input->SetAppid($mch_config['appid']);
                $input->SetBody($mall_good['name']);
                $input->SetAttach($order_id);
                $input->SetOut_trade_no($order_no);
                $input->SetTotal_fee($mall_good['price'] * 100);
                $input->SetTime_start(date("YmdHis"));
                $input->SetTime_expire(date("YmdHis", time() + 600));
                $input->SetGoods_tag("");
                $input->SetNotify_url(base_url()."api/v1/store/notify2");
                $input->SetTrade_type("APP");
                $result = WxPayApi::unifiedOrder($input);

                log_message("error", "WXAPP支付下单结果");
                log_message("error", var_export($result,TRUE));

                if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
                    $tools = new JsApiPay();
                    $parmas = $tools->GetAppApiParameters($result);
                    $this->response(array('code' => 0, 'msg' => '', 'data' => array('params' => array('info' => $parmas), 'order_id' => $order_id)));
                } else {
                    $this->response(array('code' => 1, 'msg' => '下单错误'));
                }
            } else if($this->_channel['pay_type'] == 'wx_h5') {
                $mp_config = $this->mp_model->get_mp_config($this->_game['game_id']);

                WxPayConfig::setConfig($mp_config);

                //②、统一下单
                $input = new WxPayUnifiedOrder();
                $input->SetAppid($mp_config['appid']);
                $input->SetBody($mall_good['price'] . '元');
                $input->SetAttach($order_id);
                $input->SetOut_trade_no($order_no);
                $input->SetTotal_fee($mall_good['price'] * 100);
                $input->SetTime_start(date("YmdHis"));
                $input->SetTime_expire(date("YmdHis", time() + 600));
                $input->SetGoods_tag("");
                $input->SetNotify_url(base_url()."api/v1/store/notify2");
                $input->SetTrade_type("MWEB");
                $result = WxPayApi::unifiedOrder($input);

                if($result['return_code'] == 'SUCCESS') {
                    if($result['result_code'] == 'SUCCESS') {
                        $url = base_url().'api/v1/weixin/h5?url='.urlencode($result['mweb_url'].'&redirect_url='.base_url().'api/v1/weixin/h5_result?channel_id='.$this->_channel['channel_id']);
                        echo $this->response(array('code' => 0, 'msg' => '','data'=> array('url'=>$url,'order_id'=>$order_id)));
                    } else {
                        echo $this->response(array('code' => 1, 'msg' => '下单错误，错误代码：'.$result['err_code'].'错误原因：'.$result['err_code_des']));
                    }
                } else {
                    echo $this->response(array('code' => 1, 'msg' => '下单错误:'.$result['return_msg']));
                }
            } else if($this->_channel['pay_type'] == 'hft_h5') {
                $parmas = [];

                $parmas['app_id'] = $this->_hft_appId;
                $parmas['pay_type'] = 2;
                $parmas['order_id'] = $order_no;
                $parmas['order_amt'] = $mall_good['price'];
                $parmas['notify_url'] = base_url() . 'api/v1/store/hft_notify';
                $parmas['return_url'] = base_url().'api/v1/weixin/h5_result?channel_id='.$this->_channel['channel_id'];
                $parmas['time_stamp'] = date('YmdHis');

                $sign_str = '';

                foreach ($parmas as $k=>$v) {
                    $sign_str .= $k.'='.$v.'&';
                }

                $sign_str .= 'key='.md5($this->_hft_appKey);

                $parmas['user_ip'] = $this->_get_client_ip();
                $parmas['sign'] = md5($sign_str);
                $parmas['goods_name'] = urlencode($mall_good['name']);
                $parmas['is_json'] =1;

                $this->_url = 'http://gateway.71pay.cn/Pay/GateWay10.shtml';

                $result_json = $this->_https_curl($parmas);

                $result = json_decode($result_json,TRUE);

                if($result) {
                    if($result['status_code'] == 0) {
                        echo $this->response(array('code' => 0, 'msg' => '','data'=> array('url'=>$result['pay_url'],'order_id'=>$order_id)));
                    } else {
                        $this->response(array('code' => 1, 'msg' => '下单失败，'.$result['status_msg']));
                    }
                } else {
                    $this->response(array('code' => 1, 'msg' => '下单失败:'.$result_json));
                }
            } else {
                $this->response(array('code' => 1, 'msg' => '未知的支付方式'));
            }

        }
        // 奖券支付
        else if ($mall_good['price_type'] == 3) {
            // 获取用户奖券信息
            $game_score_before = $this->player_model->get_role_score2($this->_role['UserID']);

            if ($game_score_before['Ticket'] < $mall_good['price']) {
                $this->response(array('code' => 1, 'msg' => '奖券余额不足'));
            }

            $this->player_model->update_role_ticket($this->_role['UserID'], $mall_good['price'] * (-1));

            $result = $this->_send_order_prize($this->_role['UserID'],$mall_good);

            // 插入奖券日志表
            $data = array(
                'UserID' => $this->_role['UserID'],
                'Msg' => $mall_good['good_id'],
                'AddV' => $mall_good['price'],
                'CurV' => $result['jiangquan'],
                'LogTime' => date('Y-m-d H:i:s')
            );

            $this->server_model ->set_database($this->_game['game_id']);

            $this->server_model->insert_ticket_log($data);

            $this->response(array('code' => 0, 'msg' => '', 'data' =>$result));

        }
        // 房卡
        else if ($mall_good['price_type'] == 4) {
            // 获取用户房卡
            $fangka_score_before = $this->player_model->get_role_score($this->_role['UserID']);

            if ($fangka_score_before['RoomCard'] < $mall_good['price']) {
                $this->response(array('code' => 1, 'msg' => '房卡余额不足'));
            }

            // 扣除房卡
            $this->player_model->update_role_score($this->_role['UserID'],$mall_good['price']*(-1));
            $result = $this->_send_order_prize($this->_role['UserID'],$mall_good);

            $this->response(array('code' => 0, 'msg' => '', 'data' =>$result));
        }
        //
        else if ($mall_good['price_type'] == 5) {
            // 获取用户竞猜币
            $game_score_before = $this->player_model->get_role_score2($this->_role['UserID']);

            if ($game_score_before['GuessCoin'] < $mall_good['price']) {
                $this->response(array('code' => 1, 'msg' => '竞猜币余额不足'));
            }

            // 扣除竞猜币
            $this->player_model->update_role_guess_coin($this->_role['UserID'],$mall_good['price']*(-1));
            $result = $this->_send_order_prize($this->_role['UserID'],$mall_good);

            $this->response(array('code' => 0, 'msg' => '', 'data' =>$result));
        }
        else {
            $this->response(array('code' => 1, 'msg' => '支付类型错误'));
        }
    }

    // 发送订单奖励
    private function _send_order_prize($role_id,$mall_good)
    {
        // 发奖操作
        switch ($mall_good['good_type']) {
            case 1:
                //添加房卡
                $this->player_model->update_role_score($role_id, $mall_good['score']);
                break;
            case 2:
                //添加金币
                $this->player_model->update_role_gold_score($role_id, $mall_good['score']);
                break;
            case 3:
                //增加道具
                $this->player_model->proc_role_item($role_id, $mall_good['item_id'], $mall_good['score'], 'Buy');
                break;
            case 4:
                // 添加竞猜币
                $this->player_model->update_role_guess_coin($role_id, $mall_good['score']);
            default:
                break;
        }

        $fangka_score = $this->player_model->get_role_score($this->_role['UserID']);
        $game_score_after = $this->player_model->get_role_score2($this->_role['UserID']);

        $data = array(
            'fangka' => $fangka_score['RoomCard'],
            'gold' => $game_score_after['Score'] + $game_score_after['ScoreT'],
            'jiangquan' => isset($game_score_after['Ticket'])?$game_score_after['Ticket']:0,
            'guess'=> isset($game_score_after['GuessCoin'])?$game_score_after['GuessCoin']:0
        );

        return $data;
    }

    public
    function order2_post()
    {

        $good_id = $this->input->post('good_id');
        $mall_id = $this->input->post('mall_id');

        // 获取商品
        $mall_good = $this->server_model->get_shelves_good($mall_id, $good_id);

        if (empty($mall_good)) {
            $this->response(array('code' => 1, 'msg' => '商品不存在'));
        }

        if ($mall_good['stock'] != '-99' && $mall_good['stock'] <= 0) {
            $this->response(array('code' => 1, 'msg' => '商品库存不足'));
        }

        $agent = $this->user_model->get_one_agent($this->_role['UserID'], $this->_game['game_id']);

        $this->load->helper('string');

        $order_no = date("YmdHis") . random_string('nozero', 3);

        $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'], $this->_game['game_id']);

        if (empty($mp_user)) {
            $mp_user['user_id'] = 0;
        }

        switch ($mall_good['good_type']) {
            case 1:
                $good_type = '房卡';
                break;
            case 2:
                $good_type = '金币';
                break;
            default:
                $good_type = '房卡';
        }

        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id' => $this->_role['UserID'],
            'agent_id' => $agent ? $agent['id'] : 0,
            'good_id' => $good_id,
            'game_id' => $this->_game['game_id'],
            'order_no' => $order_no,
            'is_first' => $mall_good['is_first'],
            'is_agent' => ($mall_good['type'] - 1),
            'total_fee' => $mall_good['price'],
            'is_mall' => 1,
            'mall_type' => $mall_good['good_type'],
            'mall_id' => $mall_id,
            'kind_id' => $this->_role['CommonKindID'],
            'create_time' => time()
        );

        $this->db->insert('mp_order', $data);

        $order_id = $this->db->insert_id();

        $param['appId'] = $this->_appId;
        $param['deviceType'] = "0601";
        $param['frontNotifyUrl'] = base_url();
        $param['funcode'] = "WP001";
        $param['mhtCharset'] = "UTF-8";
        $param['mhtCurrencyType'] = "156";
        $param['mhtOrderAmt'] = $mall_good['price'] * 100;
        $param['mhtOrderDetail'] = $good_type;
        $param['mhtOrderName'] = $good_type;
        $param['mhtOrderNo'] = $order_no;
        $param['mhtOrderStartTime'] = date('YmdHis');
        $param['mhtOrderTimeOut'] = 3600;
        $param['mhtOrderType'] = "01";
        $param['mhtReserved'] = $order_no;
        $param['mhtSignType'] = "MD5";
        $param['notifyUrl'] = base_url() . 'api/v1/store/notify';
        $param['outputType'] = 2;
        $param['payChannelType'] = 13;
        $param['version'] = "1.0.0";

        $str = '';

        foreach ($param as $k => $v) {
            if ($v) {
                $str .= $k . '=' . $v . '&';
            }
        }

        $str .= md5($this->_appSecret);

        $param['mhtSignature'] = md5($str);

        $return = $this->_https_curl(http_build_query($param));

        parse_str($return, $result);

        if (isset($result['responseCode']) && ($result['responseCode'] == 'A001')) {
            echo $this->response(array('code' => 0, 'msg' => '', 'data' => array('url' => $result['tn'], 'order_id' => $order_id)));
        } else {
            echo $this->response(array('code' => 1, 'msg' => '下单错误'));
        }
    }

    public
    function order3_post()
    {
        $good_id = $this->input->post('good_id');
        $mall_id = $this->input->post('mall_id');

        // 获取商品
        $mall_good = $this->server_model->get_shelves_good($mall_id, $good_id);

        if (empty($mall_good)) {
            $this->response(array('code' => 1, 'msg' => '商品不存在'));
        }

        if ($mall_good['stock'] != '-99' && $mall_good['stock'] <= 0) {
            $this->response(array('code' => 1, 'msg' => '商品库存不足'));
        }

        $agent = $this->user_model->get_one_agent($this->_role['UserID'], $this->_game['game_id']);

        $this->load->helper('string');

        $order_no = date("YmdHis") . random_string('nozero', 3);

        $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'], $this->_game['game_id']);

        if (empty($mp_user)) {
            $mp_user['user_id'] = 0;
        }

        switch ($mall_good['good_type']) {
            case 1:
                $good_type = '房卡';
                break;
            case 2:
                $good_type = '金币';
                break;
            default:
                $good_type = '房卡';
        }

        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id' => $this->_role['UserID'],
            'agent_id' => $agent ? $agent['id'] : 0,
            'good_id' => $good_id,
            'game_id' => $this->_game['game_id'],
            'order_no' => $order_no,
            'is_first' => $mall_good['is_first'],
            'is_agent' => ($mall_good['type'] - 1),
            'total_fee' => $mall_good['price'],
            'is_mall' => 1,
            'mall_type' => $mall_good['good_type'],
            'mall_id' => $mall_id,
            'kind_id' => $this->_role['CommonKindID'],
            'create_time' => time()
        );

        $this->db->insert('mp_order', $data);

        $order_id = $this->db->insert_id();

        $mp_config = array(
            'appid' => 'wxd3b7fbc2c70a8d6a',
            'mchid' => '1487812592',
            'key' => '72bdea5fd144f5812697cf15bbd296d9',
            'appsecret' => 'ca6655b28e5ffb671ff2363b981e5f88',
            'notify_url' => base_url() . 'api/v1/store/notify2'
        );

        WxPayConfig::setConfig($mp_config);

        //②、统一下单
        $input = new WxPayUnifiedOrder();
        $input->SetAppid($mp_config['appid']);
        $input->SetBody($mall_good['name']);
        $input->SetAttach($order_id);
        $input->SetOut_trade_no($order_no);
        $input->SetTotal_fee($mall_good['price'] * 100);
        $input->SetTime_start(date("YmdHis"));
        $input->SetTime_expire(date("YmdHis", time() + 600));
        $input->SetGoods_tag("");
        $input->SetNotify_url("https://lhmj.tuo3.com.cn/admin/index.php/api/v1/weixin/notify");
        $input->SetTrade_type("APP");
        $result = WxPayApi::unifiedOrder($input);

        if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
            $tools = new JsApiPay();
            $parmas = $tools->GetAppApiParameters($result);
            $this->response(array('code' => 0, 'msg' => '', 'data' => array('params' => array('info' => $parmas), 'order_id' => $order_id)));
        } else {
            $this->response(array('code' => 1, 'msg' => '下单错误'));
        }
    }

//    public function order2_post() {
//        $good_id = $this->input->post('good_id');
//        $mall_id = $this->input->post('mall_id');
//
//        // 获取商品
//        $mall_good = $this->server_model->get_shelves_good($mall_id,$good_id);
//
//        if(empty($mall_good)) {
//            $this->response(array('code' => 1, 'msg' => '商品不存在'));
//        }
//
//        if($mall_good['stock'] != '-99' && $mall_good['stock'] <= 0 ) {
//            $this->response(array('code' => 1, 'msg' => '商品库存不足'));
//        }
//
//        $agent = $this->user_model->get_one_agent($this->_role['UserID'],$this->_game['game_id']);
//
//        $this->load->helper('string');
//
//        $order_no = date("YmdHis") . random_string('nozero', 3);
//
//        $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'],$this->_game['game_id']);
//
//        if(empty($mp_user)) {
//            $mp_user['user_id'] = 0;
//        }
//
//        switch ($mall_good['good_type']){
//            case 1:
//                $good_type = '房卡';
//                break;
//            case 2:
//                $good_type = '金币';
//                break;
//            default:
//                $good_type = '房卡';
//        }
//
//        // 创建订单
//        $data = array(
//            'user_id' => $mp_user['user_id'],
//            'role_id'  => $this->_role['UserID'],
//            'agent_id'  => $agent?$agent['id']:0,
//            'good_id'  => $good_id,
//            'game_id'  => $this->_game['game_id'],
//            'order_no' => $order_no,
//            'is_first' => $mall_good['is_first'],
//            'is_agent' => ($mall_good['type']-1),
//            'total_fee' => $mall_good['price'],
//            'is_mall'  => 1,
//            'mall_type'=> $mall_good['good_type'],
//            'mall_id'   => $mall_id,
//            'kind_id'  =>$this->_role['CommonKindID'],
//            'create_time' => time()
//        );
//
//        $this->db->insert('mp_order', $data);
//
//        $order_id = $this->db->insert_id();
//
//        $mp_config = $this->mp_model->get_mp_config(6);
//
//        WxPayConfig::setConfig($mp_config);
//
//        //②、统一下单
//        $input = new WxPayUnifiedOrder();
//        $input->SetAppid($mp_config['appid']);
//        $input->SetBody($mall_good['price'] . '元');
//        $input->SetAttach($order_id);
//        $input->SetOut_trade_no($order_no);
//        $input->SetTotal_fee($mall_good['price'] * 100);
//        $input->SetTime_start(date("YmdHis"));
//        $input->SetTime_expire(date("YmdHis", time() + 600));
//        $input->SetGoods_tag("");
//        $input->SetNotify_url("https://lhmj.tuo3.com.cn/admin/index.php/api/v1/store/notify2");
//        $input->SetTrade_type("MWEB");
////        $input->SetOpenid($mp_user['openid']);
//        $result = WxPayApi::unifiedOrder($input);
//
//        if($result['return_code'] == 'SUCCESS') {
//            if($result['result_code'] == 'SUCCESS') {
//                $url = 'https://lhmj.tuo3.com.cn/admin/index.php/api/v1/weixin/h5?url='.urlencode($result['mweb_url']);
////                $url = 'https://lhmj.tuo3.com.cn/admin/index.php/api/v1/weixin/h5?url='.urlencode($result['mweb_url'].'&redirect_url=https://lhmj.tuo3.com.cn/admin/index.php/api/v1/weixin/h5_result?channel_id='.$this->_channel['channel_id']);
//                echo $this->response(array('code' => 0, 'msg' => '','data'=> array('url'=>$url,'order_id'=>$order_id)));
//            } else {
//                echo $this->response(array('code' => 1, 'msg' => '下单错误，错误代码：'.$result['err_code'].'错误原因：'.$result['err_code_des']));
//            }
//        } else {
//            echo $this->response(array('code' => 1, 'msg' => '下单错误:'.$result['return_msg']));
//        }
//    }

    public
    function query_post()
    {
        $order_ids = explode(',', $this->input->post('orders'));

        $success_ids = array();

        if ($order_ids) {
            foreach ($order_ids as $order_id) {
                $this->db->where('id', $order_id);
                $query = $this->db->get('mp_order');
                $order = $query->row_array();

                if ($order) {
                    if ($order['status'] == 1) {
                        array_push($success_ids, $order_id * 1);
                    }
                }
            }
        }

        if ($success_ids) {

            // 获取房卡
            $score = $this->player_model->get_role_score($this->_role['UserID']);
            //获取用户金币
            $score2 = $this->player_model->get_role_score2($this->_role['UserID']);

            $this->response(array('code' => 0, 'msg' => '', 'data' => array(
                'success' => $success_ids,
                'fangka' => $score['RoomCard'],
                'gold' => $score2['Score'] + $score2['ScoreT'],
                'jiangquan' => isset($score2['Ticket']) ? $score2['Ticket'] : 0,
                'guess' => isset($score2['GuessCoin']) ? $score2['GuessCoin'] : 0,
            )));
        } else {
            $this->response(array('code' => 0, 'msg' => ''));
        }
    }

    public
    function notify_post()
    {
        $data = file_get_contents('php://input');
        log_message("error", "现在支付商城回调");
        log_message("error", $data);

        parse_str($data, $data);

        if (!$this->_check_sign($data)) {
            echo "签名验证失败";
            exit;
        }

        $this->_deal_ipaynow_notify($data);

    }

    public
    function hy_notify_post()
    {
        $data = file_get_contents('php://input');
        log_message("error", "现在支付商城恒游回调");
        log_message("error", $data);

        parse_str($data, $data);

        if (!$this->_check_sign($data,'wx_wap_hy')) {
            echo "签名验证失败";
            exit;
        }

        $this->_deal_ipaynow_notify($data);

    }


    private function _deal_ipaynow_notify($data) {
        // 查找订单
        $this->db->where('order_no', $data['mhtOrderNo']);
        $query = $this->db->get('mp_order');
        $order = $query->row_array();

        if ($order['status'] == 1) {
            echo "success=Y";
            exit;
        }

        if ($data['transStatus'] != 'A001') {
            echo "订单状态异常";
            exit;
        }

        $this->_deal_order_notify($order,$data['channelOrderNo']);

        echo "success=Y";
    }

    private function _deal_order_notify($order,$channel_order_no) {
        $this->db->where('id', $order['id']);
        $this->db->update('mp_order', array('transaction_id' =>$channel_order_no, 'pay_time' => time(), 'status' => 1));

        $this->player_model->set_database($order['game_id']);

        // 获取商品信息
        $good = $this->server_model->get_good_by_id($order['good_id']);
        $role = $this->player_model->get_role_info($order['role_id']);

        switch ($good['good_type']) {
            case 1:
                //添加房卡
                $this->player_model->update_role_score($order['role_id'], $good['score']);
                if($good['is_month_card'] == 1) {
                    $this->player_model->insert_month_card($order['role_id'],$good['month_card_type']);
                }
                break;
            case 2:
                //添加金币
                $this->player_model->update_role_gold_score($order['role_id'], $good['score']);
                break;
            case 3:
                $this->player_model->proc_role_item($order['role_id'], $good['item_id'], $good['score'], 'Buy');
                break;
        }

        // 获取推荐代理
        if ($role && $role['SpreaderID'] > 0) {

            $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

            if ($agent) {

                $rate = 0.4;
                if ($order['game_id'] == 30) {
                    $rate = 0.376;
                }

                // 台州
                if ($order['game_id'] == 20) {
                    $rate = 0.5;
                }

                if (in_array($order['game_id'], array(32, 36, 54,55))) {
                    $rate = 0.5;
                }
                if (in_array($order['game_id'], array(35, 37))) {
                    $rate = 0.45;
                }

$file22 = fopen("tmp33.txt","a+");
fwrite($file22, "\r\n".date("Y-m-d H:i:s",time())."---"."game_id=".$order['game_id']."---rate=".$rate."---total_fee=". $order['total_fee']."---fee=".$rate * $order['total_fee']);
fclose($file22);

                $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                // 获取上级代理
                $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);
       
                // 判断其下级代理个数
                $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                $rate = 0.06;

                if ($order['game_id'] == 30) {
                    $rate = 0.047;
                }
                if (in_array($order['game_id'], array(36, 54))) {
                    $rate = 0.1;
                }
                if (in_array($order['game_id'], array(37))) {
                    $rate = 0.075;
                }

                if($order['game_id']==20) {
                    if($agent['district_id'] == 1 && $parent['district_id'] == 1) {
                        $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

$file22 = fopen("tmp33.txt","a+");
fwrite($file22, "\r\n"."---rate=".$rate."---fee=".$rate * $order['total_fee']);
fclose($file22);
                    }

                    if(($agent['district_id'] == 3 && $parent['district_id'] == 3)
                        ||($agent['district_id'] == 10 && $parent['district_id'] == 10)
                        ||($agent['district_id'] == 11 && $parent['district_id'] == 11))
                    {
                        if($parent['one_level_bonus'] == 1) {
                            $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);
$file22 = fopen("tmp33.txt","a+");
fwrite($file22, "\r\n"."---rate=".$rate."---fee=".$rate * $order['total_fee']);
fclose($file22);
                        }
                    }

                } else {
                    $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                    if ($parent['spreader_id'] > 0 && $order['game_id']!=55) {
                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                        // 判断其下级代理个数
                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                        $rate = 0.04;

                        if ($order['game_id'] == 30) {
                            $rate = 0.0188;
                        }
                        if (in_array($order['game_id'], array(36, 37, 54))) {
                            $rate = 0.05;
                        }

                        $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);
$file22 = fopen("tmp33.txt","a+");
fwrite($file22, "\r\n"."---rate=".$rate."---fee=".$rate * $order['total_fee']);
fclose($file22);
                    }

                }
            }

        }

        // 更新商品库存

        $mall_good = $this->server_model->get_shelves_good($order['mall_id'], $order['good_id']);

        if ($mall_good && $mall_good['stock'] != -99 && $mall_good['stock'] > 0) {
            $this->db->where('good_id', $order['good_id']);
            $this->db->where('mall_id', $order['mall_id']);
            $this->db->set('stock', 'stock - 1', FALSE);
            $this->db->update('shelves_goods');
        }

        if (in_array($order['game_id'], array(20, 36,37, 54))) {
            //触发存储过程
            $this->player_model->proc_pay_event($order['role_id'], $order['total_fee']);
        }
    }

    public
    function hft_notify_post()
    {
        log_message("error", "话付通支付回调");
        log_message("error", var_export($_POST,true));

        $data = $_POST;

        $sign_str = '';

        foreach ($data as $k=>$v) {
            if(in_array($k,['app_id','order_id','pay_seq','pay_amt','pay_result'])) {
                $sign_str .= $k.'='.$v.'&';
            }
        }

//        echo $sign_str;

        $sign = md5($sign_str.'key='.md5($this->_hft_appKey));

        if ($sign != $data['sign']) {
            echo "签名验证失败";
            exit;
        }

        // 查找订单
        $this->db->where('order_no', $data['order_id']);
        $query = $this->db->get('mp_order');
        $order = $query->row_array();

        if ($order['status'] == 1) {
            echo "ok";
            exit;
        }

        if ($data['pay_result'] != 20) {
            echo $data['result_desc'];
            exit;
        }

        $this->db->where('id', $order['id']);
        $this->db->update('mp_order', array('transaction_id' => $data['pay_seq'], 'pay_time' => time(), 'status' => 1));

        $this->player_model->set_database($order['game_id']);

        // 获取商品信息
        $good = $this->server_model->get_good_by_id($order['good_id']);
        $role = $this->player_model->get_role_info($order['role_id']);

        switch ($good['good_type']) {
            case 1:
                //添加房卡
                $this->player_model->update_role_score($order['role_id'], $good['score']);
                if($good['is_month_card'] == 1) {
                    $this->player_model->insert_month_card($order['role_id'],$good['month_card_type']);
                }
                break;
            case 2:
                //添加金币
                $this->player_model->update_role_gold_score($order['role_id'], $good['score']);
                break;
        }

        // 获取推荐代理
        if ($role && $role['SpreaderID'] > 0) {

            $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

            if ($agent) {

                $rate = 0.4;
                if ($order['game_id'] == 30) {
                    $rate = 0.376;
                }

                // 台州
                if ($order['game_id'] == 20) {
                    $rate = 0.5;
                }

                if (in_array($order['game_id'], array(32, 36, 54))) {
                    $rate = 0.5;
                }
                if (in_array($order['game_id'], array(35, 37))) {
                    $rate = 0.45;
                }

                $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                // 获取上级代理
                $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);

                // 判断其下级代理个数
                $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                $rate = 0.06;

                if ($order['game_id'] == 30) {
                    $rate = 0.047;
                }
                if (in_array($order['game_id'], array(36, 54))) {
                    $rate = 0.1;
                }
                if (in_array($order['game_id'], array(37))) {
                    $rate = 0.075;
                }

                if($order['game_id']==20) {
//                    if($agent['district_id'] == 1 && $parent['district_id'] == 1) {
                        $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);
//                    }
                } else {
                    $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                    if ($parent['spreader_id'] > 0) {
                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                        // 判断其下级代理个数
                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                        $rate = 0.04;

                        if ($order['game_id'] == 30) {
                            $rate = 0.0188;
                        }
                        if (in_array($order['game_id'], array(36, 37, 54))) {
                            $rate = 0.05;
                        }

                        $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

                    }

                }
            }

        }

        // 更新商品库存

        $mall_good = $this->server_model->get_shelves_good($order['mall_id'], $order['good_id']);

        if ($mall_good && $mall_good['stock'] != -99 && $mall_good['stock'] > 0) {
            $this->db->where('good_id', $order['good_id']);
            $this->db->where('mall_id', $order['mall_id']);
            $this->db->set('stock', 'stock - 1', FALSE);
            $this->db->update('shelves_goods');
        }

        if (in_array($order['game_id'], array(20, 30, 36, 37))) {
            //触发存储过程
            $this->player_model->proc_pay_event($order['role_id'], $order['total_fee']);
        }

        echo "ok";
    }

    public
    function notify2_post()
    {
        $data = file_get_contents('php://input');
        log_message("error", "微信APP支付商城回调");
        log_message("error", $data);

        $post_data = file_get_contents('php://input');

        $this->load->model('user_model');

        if (!$post_data) {
            echo json_encode(array('return_code' => 'FAIL', 'return_msg' => '参数非法'));
            exit;
        }
        //将XML转为array
        //禁止引用外部xml实体
        libxml_disable_entity_loader(true);
        $data = json_decode(json_encode(simplexml_load_string($post_data, 'SimpleXMLElement', LIBXML_NOCDATA)), true);

        if ($data['return_code'] == 'SUCCESS') {
            if ($data['result_code'] == 'SUCCESS') {
                $money = $data['cash_fee'];
                $out_trade_no = $data['out_trade_no'];
                $transaction_id = $data['transaction_id'];

                // 查找订单
                $this->db->where('order_no', $out_trade_no);
                $query = $this->db->get('mp_order');
                $order = $query->row_array();

                if ($order['status'] == 1) {
                    echo json_encode(array('return_code' => 'SUCCESS', 'return_msg' => '订单已交易'));
                    exit;
                }

                $this->_deal_order_notify($order, $transaction_id);

                echo json_encode(array('return_code' => 'SUCCESS', 'return_msg' => '订单更新成功'));
                exit;

            } else {
                echo json_encode(array('return_code' => 'FAIL', 'return_msg' => $data['error_code']));
                exit;
            }
        } else {
            echo json_encode(array('return_code' => 'FAIL', 'return_msg' => $data['error_code']));
            exit;
        }
    }

    public
    function _deal_order($order, $transaction_id)
    {
        $this->db->where('id', $order['id']);
        $this->db->update('mp_order', array('transaction_id' => $transaction_id, 'pay_time' => time(), 'status' => 1));

        $this->player_model->set_database($order['game_id']);

        // 获取商品信息
        $good = $this->server_model->get_good_by_id($order['good_id']);
        $role = $this->player_model->get_role_info($order['role_id']);

        switch ($good['good_type']) {
            case 1:
                //添加房卡
                $this->player_model->update_role_score($order['role_id'], $good['score']);
                break;
            case 2:
                //添加金币
                $this->player_model->update_role_gold_score($order['role_id'], $good['score']);
                break;
        }

        // 获取推荐代理
        if ($role && $role['SpreaderID'] > 0) {

            $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

            if ($agent) {

                $rate = 0.4;
                if ($order['game_id'] == 30) {
                    $rate = 0.376;
                }

                // 台州
                if ($order['game_id'] == 20) {
                    $rate = 0.5;
                }

                if (in_array($order['game_id'], array(32, 36, 54))) {
                    $rate = 0.5;
                }
                if (in_array($order['game_id'], array(35, 37))) {
                    $rate = 0.45;
                }

                $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                // 获取上级代理
                $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);

                // 判断其下级代理个数
                $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                $rate = 0.06;

                if ($order['game_id'] == 30) {
                    $rate = 0.047;
                }

                if (in_array($order['game_id'], array(36, 54))) {
                    $rate = 0.1;
                }
                if (in_array($order['game_id'], array(37))) {
                    $rate = 0.075;
                }


                if($order['game_id']==20) {
                    if($agent['district_id'] == 1&&$parent['district_id'] == 1) {
                        $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);
                    }
                } else {

                    $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                    if ($parent['spreader_id'] > 0) {
                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                        // 判断其下级代理个数
                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                        $rate = 0.04;

                        if ($order['game_id'] == 30) {
                            $rate = 0.0188;
                        }
                        if (in_array($order['game_id'], array(36, 37, 54))) {
                            $rate = 0.05;
                        }

                        $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

                    }
                }
            }
        }

        // 更新商品库存

        $mall_good = $this->server_model->get_shelves_good($order['mall_id'], $order['good_id']);

        if ($mall_good && $mall_good['stock'] != -99 && $mall_good['stock'] > 0) {
            $this->db->where('good_id', $order['good_id']);
            $this->db->where('mall_id', $order['mall_id']);
            $this->db->set('stock', 'stock - 1', FALSE);
            $this->db->update('shelves_goods');
        }

        if (in_array($order['game_id'], array(20, 36, 37,54))) {
            //触发存储过程
            $this->player_model->proc_pay_event($order['role_id'], $order['total_fee']);
        }

    }

    private
    function _check_sign($data,$type='wx_wap')
    {
        ksort($data);
        reset($data);

        $str = '';

        foreach ($data as $k => $v) {
            if ($k == 'signature') continue;
            if ($v != '') {
                $str .= $k . '=' . $v . '&';
            }
        }

        $str .= md5($type=='wx_wap'?$this->_appSecret:$this->_hy_appSecret);

        $sign = md5($str);

        if (isset($data['signature']) && $sign == $data['signature']) {
            return TRUE;
        }

        return FALSE;
    }

    private
    function _https_curl($data)
    {
        $curl = curl_init($this->_url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        $content = curl_exec($curl);
        curl_close($curl);

//        var_dump($content);

        return $content;
    }

    private  function _get_client_ip() {
        if(getenv('HTTP_CLIENT_IP') && strcasecmp(getenv('HTTP_CLIENT_IP'), 'unknown')) {
            $ip = getenv('HTTP_CLIENT_IP');
        } elseif(getenv('HTTP_X_FORWARDED_FOR') && strcasecmp(getenv('HTTP_X_FORWARDED_FOR'), 'unknown')) {
            $ip = getenv('HTTP_X_FORWARDED_FOR');
        } elseif(getenv('REMOTE_ADDR') && strcasecmp(getenv('REMOTE_ADDR'), 'unknown')) {
            $ip = getenv('REMOTE_ADDR');
        } elseif(isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], 'unknown')) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        return preg_match ( '/[\d\.]{7,15}/', $ip, $matches ) ? $matches [0] : '';
    }

    private function _ipaynow_receive_post($order) {

        $url = "";

        $this->db->select('type,account,name');
        $query = $this->db->get('receivers');
        $receivers = $query->result_array();

        $params = array(
            'mchId' => $this->_hy_appId,
            'outOrderNo' => $order['order_no'],
            'receivers' => json_encode($receivers),
            'transId' => $order['transaction_id'],
        );

        $params['sign'] = md5(http_build_query($params).'&'.md5($this->_hy_appSecret));;

        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        $content = curl_exec($curl);
        curl_close($curl);
    }

}
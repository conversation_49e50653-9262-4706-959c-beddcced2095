-------------------------------------------------------------------------------
--  创世版1.0
--  通用下拉列表
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local PopupMask	= cs.app.client('system.PopupMask')


local NumSelect = class('NumSelect', PopupMask)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function NumSelect:ctor(min, max, tip_lang, callback)
    self.super.ctor(self, nil, true)
    self:zorder(0)  -- 因为PopupMask的默认zorder是负的
    self.m_callback = callback
    self.m_num = min
    self.m_min = min
    self.m_max = max
    self.m_tip_lang = tip_lang

    local main_node = helper.app.loadCSB(csb or 'NumSelectLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('label_tip'):setRich(tip_lang)
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
    main_node:child('btn_minus'):addTouchEventListener( handler(self, self.onBtnStep) )
    main_node:child('btn_plus'):addTouchEventListener( handler(self, self.onBtnStep) )
    main_node:child('btn_minus').step = -1
    main_node:child('btn_plus').step = 1
    main_node:child('slider'):onEvent( handler(self, self.onSlider) )

    self:updateNum(self.m_num)
end


-------------------------------------------------------------------------------
-- 重载：蒙版收到点击事件
-------------------------------------------------------------------------------
function NumSelect:onMaskClick(sender, event)
    if event == cc.EventCode.ENDED then
        self:removeFromParent()
    end
end


-------------------------------------------------------------------------------
-- 更新显示
-------------------------------------------------------------------------------
function NumSelect:updateNum(num)
    self.m_num = math.max(self.m_min, math.min(self.m_max, num))
    self.main_node:child('label_num'):setString(self.m_num)
end


-------------------------------------------------------------------------------
-- 加减按钮点击
-------------------------------------------------------------------------------
function NumSelect:onBtnStep(sender, event)
    helper.app.tintClickEffect(sender, event)
    if event == cc.EventCode.ENDED then
        self:updateNum(self.m_num + sender.step)
        self.main_node:child('slider'):setPercent((self.m_num - self.m_min) * 100 / (self.m_max - self.m_min))
    end
end


-------------------------------------------------------------------------------
-- 拖动
-------------------------------------------------------------------------------
function NumSelect:onSlider(event)
    if event.name ~= 'ON_PERCENTAGE_CHANGED' then return end
    local percent = event.target:getPercent()
    local n = math.ceil(percent / 100 * (self.m_max - self.m_min) + self.m_min)
    self:updateNum(n)
end


-------------------------------------------------------------------------------
-- 确定点击
-------------------------------------------------------------------------------
function NumSelect:onBtnOk(sender)
    self.m_callback(self.m_num)
    self:removeFromParent()
end


return NumSelect
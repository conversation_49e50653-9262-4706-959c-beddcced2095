-------------------------------------------------------------------------------
--  创世版1.0
--  游戏插件
--  @date 2017-06-12
--  @auth woodoo
-------------------------------------------------------------------------------
local ClientUpdate = cs.app.client('app.controllers.ClientUpdate')


local GamePlugin = class("GamePlugin", ccui.Layout)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GamePlugin:ctor(item_callback)
    self:enableNodeEvents()
    self.m_item_callback = item_callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('GamePlugin.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('template'):hide()
    main_node:child('template_group'):hide()
    main_node:child('panel_group'):hide()
    main_node:child('panel_group'):addTouchEventListener( handler(self, self.onGroupClose) )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function GamePlugin:onEnter()
    print('GamePlugin:onEnter...')
    
    -- 屏幕居中分组面板
    local pos = self.main_node:convertToNodeSpace(cc.p(0, 0))
    local panel_group = self.main_node:child('panel_group')
    panel_group:pos(pos)

    -- 初始化列表
    self:initList()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GamePlugin:onExit()
    print('GamePlugin:onExit...')
end


-------------------------------------------------------------------------------
-- 刷新列表
-------------------------------------------------------------------------------
function GamePlugin:refresh()
    self:initList()
end


-------------------------------------------------------------------------------
-- 初始化列表
-------------------------------------------------------------------------------
function GamePlugin:initList()
    local plugins = clone(cs.app.plugins)

    local template_group = self.main_node:child('template_group')
    local template = self.main_node:child('template')

    -- 加入分组
    self.m_group_nodes = {}
    local addToGroup = function(group_name, kind_node)
        local parent = self.m_group_nodes[group_name]
        if not parent then
            parent = template_group:clone():show()
            parent.group = group_name
            helper.layout.addCenter(self.main_node:child('panel_group'), parent)
            self.m_group_nodes[group_name] = parent
        end
        parent:child('listview'):pushBackCustomItem(kind_node)
    end

    -- 创建玩法节点
    local createKind = function(plugin)
        local item = template:clone():show()
        item.config = plugin
        item:child('label_name'):setString(plugin.name)
        local file_name = plugin.icon
        if not file_name then
            file_name = plugin.link and 'icon_link_' .. plugin.link or 
            (plugin.kind and 'icon_kind_' .. plugin.kind or 'icon_game_' .. plugin.game)
        end
        local icon_path = 'common/' .. file_name .. '.png'
    	if cc.FileUtils:getInstance():isFileExist(icon_path) then
            local icon = display.newSprite(icon_path)
            --[[
            if not plugin._Active then
                local draw = cc.DrawNode:create()
                draw:setLineWidth(1)
                local size = icon:size()
                for j=13, size.height - 13, 2 do
                    draw:drawLine(cc.p(13, j), cc.p(size.width-15, j), cc.c4f(0, 0, 0, 0.6))
                end
                draw:addTo(icon)
            end
            --]]
            helper.layout.addCenter(item, icon)

            if plugin.subicon then
                for _, sub in ipairs(plugin.subicon) do
                    local sp = display.newSprite(sub[1])
                    sp:pos(sub[2]):addTo(icon)
                end
                icon:setCascadeColorEnabled(true)
            end

            if plugin.action then
                for _, cfg in ipairs(plugin.action) do
                    local node = icon
                    if cfg.child > 0 then
                        node = icon:getChildren()[cfg.child]
                    end
                    if cfg.rotate then
                        node:runAction( cc.RepeatForever:create( cc.RotateBy:create(cfg.rotate[1], cfg.rotate[2]) ) )
                    end
                end
            end

            if plugin.anim then
                local cfg = plugin.anim
                local act = cfg.delay and cc.DelayTime:create(cfg.delay) or nil
                local anim = helper.app.createAnimation('common/' .. cfg.name, cfg.frames, cfg.duration, true, act)
                anim:setName('anim')
                helper.layout.addCenter(icon, anim, cfg.offset)
            end
        end
        item:setCascadeColorEnabled(true)
        item:addTouchEventListener( helper.app.tintClickHandler(self, self.onItemClick) )

        return item
    end

    -- 创建所有玩法节点，sub的放入分组节点，其它的直接显示在桌面
    local listview = self.main_node:child('listview')
    listview:removeAllItems()
    local t_size = template:size()
    local l_size = listview:size()
    local panel_col
    local index = 0
    local addIndex = function()
        index = index + 1
        if index % 2 == 1 then
            panel_col = ccui.Layout:create()
            panel_col:size(t_size.width, l_size.height)
            listview:pushBackCustomItem(panel_col)
        end
    end
    for i, plugin in ipairs(plugins) do repeat
        -- 审核模式下需控制的插件直接跳过
        if yl.is_reviewing and plugin.review then
            break
        end

        -- 空白
        if plugin.empty then
            addIndex()
            break
        end

        local item = createKind(plugin)
        
        if plugin.sub == 1 then
            addToGroup(plugin.group, item)
        else
            addIndex()
            item:pos(t_size.width/2, 30 + ((index-1) % 2 == 0 and (t_size.height * 1.5 + 5) or (t_size.height * 0.5)))
            item:addTo(panel_col)
        end
    until true end

    -- 多于三项，动画展示
    if #plugins > 3 then
        listview:jumpToRight()
        listview:perform(function()
            listview:scrollToLeft(0.5, true)
        end, 1)
    end
end


-------------------------------------------------------------------------------
-- 显示分组内容
-------------------------------------------------------------------------------
function GamePlugin:showGroup(group_name, from_pos)
    local panel_group = self.main_node:child('panel_group'):show()
    panel_group:stop():runAction( cc.FadeIn:create(0.2) )
    for i, child in ipairs(panel_group:getChildren()) do
        if child.group == group_name then
            -- 动画显示
            child._target_pos = from_pos
            child:stop():show():scale(0):pos(from_pos):runAction(
                cc.Spawn:create(
                    cc.ScaleTo:create(0.1, 1),
                    cc.MoveTo:create(0.1, cc.p(display.width/2, display.height/2))
                )
            )
        elseif child.group then
            child:hide()
        end
    end
end


-------------------------------------------------------------------------------
-- 显示分组内容
-------------------------------------------------------------------------------
function GamePlugin:onGroupClose(sender, event)
    local panel_group = self.main_node:child('panel_group')
    panel_group:stop():runAction( cc.Sequence:create( cc.FadeOut:create(0.2), cc.Hide:create() ) )
    for i, child in ipairs(panel_group:getChildren()) do
        if child:isVisible() then
            -- 动画隐藏
            local target_pos = child._target_pos
            child:stop():runAction(
                cc.Spawn:create(
                    cc.ScaleTo:create(0.1, 0),
                    cc.MoveTo:create(0.1, target_pos)
                )
            )
        end
    end
end


-------------------------------------------------------------------------------
-- 初始化列表
-------------------------------------------------------------------------------
function GamePlugin:onItemClick(sender)
    if not sender.config.link and not sender.config.kind and sender.config.group ~= 'function' then
        local pos = sender:convertToWorldSpace(cc.p(sender:size().width/2, sender:size().height/2))
        pos = self.main_node:child('panel_group'):convertToNodeSpace(pos)
        self:showGroup(sender.config.group, pos)
    else
        self.m_item_callback(sender)
    end
end


return GamePlugin
<?php
/**
 * @author: <PERSON>.z
 * @email: <EMAIL>
 * @website: http://www.jason-z.com
 * @version: 1.0
 * @date: 2018/7/9
 */

require(APPPATH . '/libraries/REST_Controller.php');


class Agent extends CI_Controller
{
    private $_game;

    public function __construct()
    {
        parent::__construct();
        $this->load->model('server_model');
        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->load->library('ion_auth');

        $game_id = $this->input->post('game_id');

//        if(!in_array($game_id,[20])) {
//            show_response(1,'暂未开放申请，请耐心等待',array('is_agent'=>false));
//        }

        $this->_game = $this->server_model->get_game_by_id($game_id);

        if(! $this->_game){
            show_response(1,'未查询到游戏');
        }

        $this->player_model->set_database($game_id);
    }

    public function check()
    {
        $role_id = $this->input->post('role_id');

        $game = $this->_game;

        // 获取游戏区域
        $districts = $this->server_model->get_district_list($this->_game['game_id']);

//        $new_disticts = [];
//
//        foreach($districts as $k=>$district) {
//            $new_disticts[$k]['id'] = $district['id']*1;
//            $new_disticts[$k]['district_name'] = $district['district_name'];
//        }

        $game['districts'] = $districts;

        // 判断用户是否为代理
        $agent = $this->user_model->get_one_agent($role_id,$this->_game['game_id']);

        if($agent) {
            show_response(1,'你已经是代理了，请勿重复申请！',array('is_agent'=>true,'agent'=>$agent));
        }

        show_response(0,'',array('game'=>$game));
    }

    public function create() {
        $role_id = $this->input->post('role_id');
        $game_id = $this->input->post('game_id');
        $district_id = $this->input->post('district');
        $name = trim($this->input->post('name'));
        $phone =  trim($this->input->post('phone'));
        $wechat =  trim($this->input->post('wechat'));

        // 查询角色信息
        $agent = $this->user_model->get_one_agent($role_id,$this->_game['game_id']);

        if($agent) {
            show_response(1,'你已经是代理了，请勿重复申请！');
        }

        $this->db->where('role_id',$role_id);
        $this->db->where('game_id',$game_id);
        $this->db->where('create_time >',strtotime('-60 days'));
        $query = $this->db->get('agent_blacklist');

        if($query->num_rows()>0){
            show_response(1,'暂无资格申请');
        }


        $this->load->helper('string');
        $password = random_string('nozero', 6);

        $py = $this->_game['game_shortname'];

        $account = $py.sprintf('%06d',$role_id);

        // 生成系统帐号信息
        $additional_data = array(
            'init_password'=>$password,
            'nickname'=>$name,
            'phone'=>$phone,
            'address'=>'',
            'role_id'=>$role_id,
            'wechat'=>$wechat,
            'status'=>0,
            'group'=>3,
            'district_id'=>$district_id
        );

        $role = $this->player_model->get_role_info($role_id);

        if($role['SpreaderID']>0) {
            // 判断推荐码是否为代理
            $agent = $this->user_model->get_one_agent($role['SpreaderID'],$game_id);
            if($agent) {

                $additional_data['general_agent'] = $agent['general_agent'];
                $additional_data['spreader_id'] = $agent['id'];
            }
        } else {
            $additional_data['general_agent'] = 1;
        }


        $email = '<EMAIL>';

        $additional_data['create_user'] = 1;

        $user_id = $this->ion_auth->register($account, $password, $email, $additional_data);

        if(!$user_id) {
            show_response(1,'代理帐号生成失败，请重试');exit;
        }

        $this->db->insert('adminer_games',array('adminer_id'=>$user_id,'game_id'=>$game_id));
        $this->player_model->update_role_info($role_id,array('IsDaiLi'=>1));

        //更新代理昵称和推荐的玩家数
        $data['spreader_num'] = $this->player_model->get_role_spreader_num($role_id);
        $data['role_nickname'] = $this->player_model->get_role_nickname($role_id);

        if ($data){
            $this->user_model->update_agent_info($user_id,$data);
        }

        show_response(0,'',array(
            'account'=>$account,
            'init_password'=>$password
        ));

    }
}


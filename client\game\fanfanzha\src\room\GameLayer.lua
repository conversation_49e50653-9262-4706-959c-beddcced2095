local GameModel = appdf.req(appdf.CLIENT_SRC.."system.GameModel")

local GameLayer = class("GameLayer", GameModel)

local cmd = cs.app.game("room.CMD_Game")
local GameLogic = cs.app.game("room.GameLogic")
local GameViewLayer = cs.app.game("room.GameViewLayer")
local ExternalFun =  cs.app.client('external.ExternalFun')

function GameLayer:ctor(frameEngine, scene)
    GameLayer.super.ctor(self, frameEngine, scene)

    cs.game.IS_RECIVE_GAME_RESULT = false
    self.is_zhengdian = false
    self.isNeedPreOutIndex = true
    self.isGameStart = false

end

function GameLayer:CreateView()
    return GameViewLayer:create(self):addTo(self)
end

function GameLayer:OnInitGameEngine()
    GameLayer.super.OnInitGameEngine(self)
    self.cbPlayStatus = {0, 0, 0, 0}
    self.cbCardData = {}
    self.cbPartnerCardData = {}
    self.wOutCardIndex = {0, 0, 0, 0}
    self.wPreOutCardIndex = {0, 1, 2, 3}
    self.cbOutCardState = {}
    self.cbWinIndex = {}
    --self.anayseCard = {}
    self.wBankerUser = yl.INVALID_CHAIR
    self.wPeiyin = {0, 0, 0, 0}

    self.cbTimeOperateCard = 10
    self.cbTimeOutCard = 10
    self.cbTimeStartGame = 10
end

function GameLayer:OnResetGameEngine()
    GameLayer.super.OnResetGameEngine(self)
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 语音播放开始
function GameLayer:onUserVoiceStart( useritem, filepath )
    local view_id = self:SwitchViewChairID(useritem.wChairID)
    self._gameView:onUserVoiceStart(view_id)
    return view_id
end

-- 语音播放结束
function GameLayer:onUserVoiceEnded( view_id, filepath )
    self._gameView:onUserVoiceEnded(view_id)
end

function GameLayer:SwitchViewChairID(chair)
    if chair == nil then
        return cmd.MY_VIEWID
    end
    if chair == yl.INVALID_CHAIR then
        return cmd.MY_VIEWID
    end
    local viewid = yl.INVALID_CHAIR
    local nChairCount = cmd.GAME_PLAYER --self._gameFrame:GetChairCount()
    if self.MeChairID == nil or self.MeChairID == yl.INVALID_CHAIR then
        self.MeChairID = self:GetMeChairID()
       -- print('self chair id ', self.MeChairID)
    end

    --if self.chairid_index then
        --print('nChairID is : ', self.chairid_index, self.MeChairID)
       -- self.MeChairID = self.chairid_index
    --end
    
    local nChairID = self.MeChairID
    if chair ~= yl.INVALID_CHAIR and chair < nChairCount and nChairID ~= yl.INVALID_CHAIR then
        viewid = math.mod(chair + math.floor(cmd.GAME_PLAYER * 3 / 2) - nChairID, cmd.GAME_PLAYER) + 1
        --print('before viewid is : ', viewid, chair, nChairID)
        if nChairCount == 2 and viewid ~= 3 then        -- 两人时对方总在1号位
            viewid = 1
        elseif nChairCount == 3 and viewid == 1 then    -- 三人时1号位总空着
            --if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false and isCheckFix3 then
                if nChairID == 0 then
                    viewid = 2
                elseif nChairID == 2 then
                    viewid = 4
                end
            --end      
        end
    end
    return viewid
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair,time,clockId)
    -- body
    --[[
    if clockId == cmd.IDI_NULLITY then
        if time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_START_GAME then
        if time == 0 then
            self._gameFrame:setEnterAntiCheatRoom(false)--退出防作弊
            self:onExitTable()
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_CALL_BANKER then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CANCEL)
        end
    elseif clockId == cmd.IDI_TIME_USER_ADD_SCORE then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CHIP + 4)
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_TIME_OPEN_CARD then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_OPENCARD)
        end
    end

    --]]
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 用户配音改变
function GameLayer:onUserPeiyinChange(user_item)
    local wChairId = user_item.wChairID
    if self:isNeedIndex() then
        wChairId = self:getIndex(wChairId)
    end
	local view_id = self:SwitchViewChairID(wChairId)
    self.wPeiyin[view_id] = user_item.wPeiyin
    print('user_item is ', user_item.wPeiyin, view_id)
end

-- 刷新房卡数据
function GameLayer:updatePriRoom()
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end

-- 场景信息
function GameLayer:onEventGameScene(cbGameStatus, dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cbGameStatus
    --end
    
	local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            print('GameLayer _gameView ', wViewChairId, userItem.szNickName)
            --dump(userItem)
            --self.wPeiyin[wViewChairId] = userItem.wPeiyin
            --self._gameView:OnUpdateUser(wViewChairId, userItem)
        end
    end
    self._gameView:onResetView()

	if cbGameStatus == cmd.GS_TK_FREE	then				--空闲状态
        self:onSceneFree(dataBuffer)
    elseif cbGameStatus == cmd.GS_TK_PLAYING  then            --游戏状态
        --self:onScenePlaying(dataBuffer)
	end

    -- 启动 心跳
    self:startOrStopHeartBeat( true )
    --self:startOrStopReqLocation( true )
    
    self:dismissPopWait()
end

--空闲场景
function GameLayer:onSceneFree(dataBuffer)
    print("onSceneFree")

    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusFree, dataBuffer)
   
    local lCellScore = cmd_table.lCellScore
    --local lRoomStorageStart = dataBuffer:readscore(int64):getvalue()
    --local lRoomStorageCurrent = dataBuffer:readscore(int64):getvalue()
    self.cbTimeOperateCard = cmd_table.cbTimeOperateCard
    self.cbTimeOutCard = cmd_table.cbTimeOutCard
    self.cbTimeStartGame = cmd_table.cbTimeStartGame
  
    self._gameView:setCellScore(lCellScore)
    if not GlobalUserItem.isAntiCheat() then    --非作弊房间
        if not yl.IS_REPLAY_MODEL then
            self._gameView.btStart:setVisible(true)
        end
        
    end
    self.jushu = cmd_table.cbJushu
    self._gameView:showJuShu()
    local chairid = self:GetMeChairID()
    if self.chairid_index then
        chairid = self.chairid_index
    end
    self:SetGameClock(chairid, cmd.IDI_START_GAME, self.cbTimeStartGame)

    --[[
    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbGameRule[1][4] == 1 then
        self.is_zhengdian = true
    end
    --]]

    local tableId = self._gameFrame:GetTableID()
    local userItem = self._gameFrame:getTableUserItem(tableId, self:GetMeChairID())
    if userItem and yl.US_READY == userItem.cbUserStatus then
        self._gameView.btStart:hide()
    end

    self._gameView:changeChairID(cmd_table.wChairID[1])
end

function GameLayer:getIndex(chairid, bIsPreIndex)
    -- body
    bIsPreIndex = bIsPreIndex or false
    for i = 1, cmd.GAME_PLAYER do
        local id = self.wOutCardIndex[i]
        if bIsPreIndex then
            id = self.wPreOutCardIndex[i]
        end
        if chairid == id then
            return i - 1
        end
    end

    return 0
end


-- 游戏消息
function GameLayer:onEventGameMessage(sub,dataBuffer)
	if sub == cmd.SUB_S_GAME_START then 
		self:onSubGameStart(dataBuffer)
	--elseif sub == cmd.SUB_S_SEND_CARD then 
	--	self:onSubSendCard(dataBuffer)
	elseif sub == cmd.SUB_S_PLAYER_EXIT then 
		--self:onSubPlayerExit(dataBuffer)

    elseif sub == cmd.SUB_S_READY_STATE then 
		--self:onSubSendCard(dataBuffer)
        self.game_end = false
	else
		print("unknow gamemessage sub is"..sub)
	end
end

function GameLayer:getPlayNum()
    local num = 0
    for i = 1, cmd.GAME_PLAYER do
        if self.cbPlayStatus[i] == 1 then
            num = num + 1
        end
    end

    return num
end

--游戏开始
function GameLayer:onSubGameStart(dataBuffer)

    self.isGameStart = true
    self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
    if self._gameView._priView then
        self._gameView._priView:onRefreshInviteBtn()
    end

    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameStart, dataBuffer)

    --dump(cmd_data)
    local wCurrentViewId = self:SwitchViewChairID(cmd_data.wCurrentUser)
    if self:GetMeChairID() ~= cmd_data.wCurrentUser then
        return
    end

    self.wChairIDSave = self:GetMeChairID()

    print('start send card .....................')
    self.isNeedPreOutIndex = true
    self.wBankerUser = cmd_data.wBankerUser
    self.wCurrentUser = cmd_data.wBankerUser
    self.wRandNum = {}
    self.wBankerIndex = {}
    self.wVecBankerUser = {}
    self.rand_card = cmd_data.cbCard
    self.bChangeSit = cmd_data.bChangeSit
    self.bNeedTurn = cmd_data.bNeedTurn

    for i = 1, cmd.CHANGE_CHAIRID_NUM do
        self.wRandNum[i] = cmd_data.cbRandNum[1][i]
        self.wBankerIndex[i] = cmd_data.wBankerIndex[1][i]
        self.wVecBankerUser[i] = cmd_data.wBanker[1][i]
    end

    for i = 1, cmd.GAME_PLAYER do
        self.wPreOutCardIndex[i] = self.wOutCardIndex[i] 
        self.wOutCardIndex[i] = cmd_data.wOutCardIndex[1][i]
    end

    for i = 1, cmd.GAME_PLAYER do
        if self.wCurrentUser == self.wOutCardIndex[i] then
            self.wCurrentUserIndex = i - 1
            break
        end
    end
    
    for i = 1, cmd.MAX_COUNT do
        self.cbCardData[i] = cmd_data.cbCardData[1][i]
    end

   -- self._gameView:changeChairID()

    self.cbBombCard = {}
    self.cbBombCardNum = {}
    self.cbBombNum = {}
    self.lBombCardScore = {}
    self.lGameScore = {}

    for i = 1, cmd.GAME_PLAYER do
        self.cbBombNum[i] = cmd_data.cbBombNum[1][i]
        self.lGameScore[i] = cmd_data.lGameScore[1][i]
        self.cbBombCard[i] = {}
        self.cbBombCardNum[i] = {}
        self.lBombCardScore[i] = {}
        for j = 1, 10 do
            self.cbBombCardNum[i][j] = cmd_data.cbBombCardNum[i][j]
            self.lBombCardScore[i][j] = cmd_data.lBombCardScore[i][j]
        end
        for j = 1, cmd.MAX_COUNT do
            self.cbBombCard[i][j] = cmd_data.cbBombCard[i][j]
        end
    end

    self.jushu = cmd_data.cbJushu
    self._gameView:showJuShu()

    if self._gameView.is_end_jushu then
        self.gamelayerUserItem = {}
        self.gamelayerUserChairID = {}
        for i = 1, cmd.GAME_PLAYER do
            self.gamelayerUserItem[i] = GameLogic:copyUesrItem(self._gameView.m_sparrowUserItem[i]) 
            self.gamelayerUserChairID[i] = self.gamelayerUserItem[i].wChairID
        end
    end
    
    self._gameView:startGame() 

    GameLogic:SortCardList(self.cbCardData,#self.cbCardData,GameLogic.ST_ORDER)

    self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)

    self._gameView:createSelfCard(cmd.MAX_COUNT)

    self._gameView:gameSendCard(self:SwitchViewChairID(self:getIndex(self.wBankerUser, true)), cmd.GAME_PLAYER * cmd.MAX_COUNT)
    
    local path = cs.game.SRC .. 'room.GameResultLayer'
    helper.pop.popLayer(path, nil, {self._gameView, cmd_data, self.wBankerUser, cmd.GAME_PLAYER})

    self.isFirstOutCard = false
end

function GameLayer:getMeChairIDIndex(  )
    local wMeChairID = self:GetMeChairID()
    if not wMeChairID or wMeChairID == yl.INVALID_CHAIR then
        wMeChairID = self.wChairIDSave
    end
    for i = 1, cmd.GAME_PLAYER do
        if self.wOutCardIndex[i] == wMeChairID then
            self.chairid_index = i - 1
            break
        end
    end
    print('getMeChairIDIndex ', wMeChairID, self.wChairIDSave, self.chairid_index)

    self.MeChairID = self.chairid_index
end

function GameLayer:removeCard(out_cards)
    for i = 1, #out_cards do
        if out_cards[i] == 0 then
            break
        end
        for j = 1, #self.cbCardData do
            if self.cbCardData[j] == out_cards[i] then
                self.cbCardData[j] = 0
                break
            end
        end
    end
    

    local len = #self.cbCardData
    do 
        local i = 1
        while i <= len do
            if self.cbCardData[i] == 0 then
                table.remove(self.cbCardData, i)
                len = len - 1
            else
                i = i + 1
            end
        end
    end
   
    --print('after reomve card , len is ',  #self.cbCardData)
end

--用户强退
function GameLayer:onSubPlayerExit(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    self.cbPlayStatus[wPlayerID + 1] = 0
    self._gameView.nodePlayer[wViewChairId]:setVisible(false)
    self._gameView.bCanMoveCard = false
    self._gameView.btOpenCard:setVisible(false)
    --self._gameView.btPrompt:setVisible(false)
    self._gameView.spritePrompt:setVisible(false)
    self._gameView.spriteCardBG:setVisible(false)
    self._gameView:setOpenCardVisible(wViewChairId, false)
end

function GameLayer:sendOp()
    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_OutCard)
   -- cmd_data:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OUT_CARD)
   
    local len = #self._gameView.m_tSelectCards
    cmd_data:pushbyte(len)
    cmd_data:pushbyte(self.chairid_index)
    for i = 1, len do
        cmd_data:pushbyte(self._gameView.m_tSelectCards[i])
    end
    --[[
    for i = len + 1, cmd.MAX_COUNT do
        print('push data is ', i)
        cmd_data:pushbyte(0)
    end
    --]]
    
	return self:SendData(cmd.SUB_C_OUT_CARD, cmd_data)
end

function GameLayer:onPassOutCard(  )
    if self.isFirstOutCard == false then
        return 
    end
    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_Pass)
    --cmd_data:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_PASS_CARD)

    cmd_data:pushbyte(self.chairid_index)
    print('self.chairid_index is ', self.chairid_index)
    
	return self:SendData(cmd.SUB_C_PASS_CARD, cmd_data)
end

function GameLayer:openGameResultLayer()
    
end

--游戏结束
function GameLayer:onSubGameEnd(dataBuffer)
    self.game_end = true
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameEnd, dataBuffer)
    --dump(cmd_data)
    --self.game_result = cmd_data
    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.wWinOrder[1][i]
    end

    self._gameView.animateCard:stopAllActions()
    self._gameView:gameEnd(cmd_data.lGameScore)

    self._gameView.bCanMoveCard = false

    --self:perform( handler(self, self.openGameResultLayer), 2, 1)

    local cards = {}
    local card_num = cmd_data.cbCardCount
    for i = 1, card_num do
        table.insert(cards, cmd_data.cbCardData[1][i])
    end

    self._gameView:gameEndShowLeftCard(cmd_data.wCurrentUserIndex, cards, card_num)

    if self:GetMeChairID() ~= cmd_data.wCurrentUserIndex then
        return 
    end
    
    local path = cs.game.SRC .. 'room.GameResultLayer'
    helper.pop.popLayer(path, nil, {self._gameView, cmd_data, self.wBankerUser, cmd.GAME_PLAYER})

    --local index = self:GetMeChairID() + 1
    

    --self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_END.WAV")
    self._gameView.player_num = cmd.GAME_PLAYER
    print('self.m_userRecord is , ', self._gameView.player_num)
    self.m_userRecord = {}
	for i = 1, self._gameView.player_num do
		self.m_userRecord[i] = {}
		self.m_userRecord[i].cbHuCount =  1
		--self.m_userRecord[i].cbMingGang =  cmd_data.lGameScore[i]
		--self.m_userRecord[i].cbAnGang =  cmd_data.lGameScore[i]
		--self.m_userRecord[i].cbMaCount =  cmd_data.lGameScore[i]
		self.m_userRecord[i].lDetailScore = {}
	end
end

function GameLayer:getCardData(index)
    local index = self:GetMeChairID() + 1
    local data = self.cbCardData[index][i]
    return data
end

function GameLayer:getDetailScore()
	return self.m_userRecord
end

function GameLayer:getUserInfoByChairID(chairId)
	local viewId = self:SwitchViewChairID(chairId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getUserInfoByViewID(viewId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:onExitRoom()
    --self:startOrStopReqLocation( false )
    self:startOrStopHeartBeat( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end 
    end
    self:removeFromParent()
end

-- 刷新界面
function GameLayer:updateView()
end

--开始游戏
function GameLayer:onStartGame()
    -- body
    --self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
    self:KillGameClock()
    self._gameView:onResetView()
    self._gameView:startGame()
    self._gameFrame:SendUserReady()
end

--将视图id转换为普通id
function GameLayer:isPlayerPlaying(viewId)
    if viewId < 1 or viewId > cmd.GAME_PLAYER then
        print("view chair id error!")
        return false
    end

    for i = 1, cmd.GAME_PLAYER do
        if self:SwitchViewChairID(i - 1) == viewId then
            if self.cbPlayStatus[i] == 1 then
                return true
            end
        end
    end

    return false
end

function GameLayer:getMeCardLogicValue(num)
    local index = self:GetMeChairID() + 1

    --此段为测试错误
    if nil == index then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == num then
        showToast(self, "nil == index", 1)
        return false
    end
   
end

function GameLayer:getOxCard(cbCardData)
    return GameLogic:getOxCard(cbCardData)
end

--********************   发送消息     *********************--
function GameLayer:onBanker(cbBanker)
    local dataBuffer = CCmd_Data:create(1)
    --dataBuffer:setcmdinfo(yl.MDM_GF_GAME,cmd.SUB_C_CALL_BANKER)
    dataBuffer:pushbyte(cbBanker)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onAddScore(lScore)
	print("下注金币", lScore)
    local dataBuffer = CCmd_Data:create(8)
    --dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_ADD_SCORE)
    dataBuffer:pushscore(lScore)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onOpenCard(is_special_type)
    local index = self:GetMeChairID() + 1
    local bOx = GameLogic:getOxCard(self.cbCardData[index])
    if is_special_type == nil then
        is_special_type = 0
    end
    
    local dataBuffer = CCmd_Data:create(15)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OPEN_CARD)
    dataBuffer:pushbyte(1)
    for i = 1, cmd.GAME_CARD_NUM do
        if is_special_type == 0 then
            dataBuffer:pushbyte(self._gameView.cbOpenCardData[i]) 
        else
            dataBuffer:pushbyte(self.cbCardData[index]) 
        end
    end
    
    dataBuffer:pushbyte(is_special_type)

    return self._gameFrame:sendSocketData(dataBuffer)
end


-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
       self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
       --print('stop.............replay')
       self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
    if self._gameView.bCanNextReplay then
        if not PassRoom:getInstance():getNetFrame():doNextReplay() then
            self:startOrStopReplay( false )
        end
    end
     
end


-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:stop( yl.ActionTag.HEART )
            
            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )
            
        end
    else
        --print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end

-- 检测心跳
function GameLayer:checkHeartBeat()
    --print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end

-- 重连
function GameLayer:doReConnect()
    --print('网络重连。。。')
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()
    PassRoom:getInstance():getNetFrame():onSearchRoom( PassRoom:getInstance().m_tabPriData.szServerID )
end


-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
       -- print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
        --print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    --print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置 
function GameLayer:onCheckSocketIsOK()
   self:startCheckIsSocketOK( false )
   self.check_is_socket_ok_times = 0
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('subGameResultLayer')
    if view then
        view:removeFromParent()
    end
end

function GameLayer:updatePromptList(cards, handCards, outViewId, curViewId)
    self.m_tabCurrentCards = cards
    self.m_tabPromptList = {}

    print("## 提示 start ")
    local result = {}
    --if outViewId == curViewId then
        --self.m_tabCurrentCards = {}
        --result = GameLogic:SearchOutCard(handCards, #handCards, {}, 0)
    --else
    result = GameLogic:SearchOutCard(handCards, #handCards, cards, #cards)
    --end

    local resultCount = result[1]
   -- print("## 提示牌组 " .. resultCount)
   -- dump(result) 
    for i = resultCount, 1, -1 do
        local tmplist = {}
        local total = result[2][i]
        local cards = result[3][i]
        for j = 1, total do
            local cbCardData = cards[j] or 0
            table.insert(tmplist, cbCardData)
        end
        table.insert(self.m_tabPromptList, tmplist)
    end
    self.m_tabPromptCards = self.m_tabPromptList[#self.m_tabPromptList] or {}
    self._gameView.m_promptIdx = 0
end

function GameLayer:isNeedIndex(  )
    local a = self.wOutCardIndex[1] + self.wOutCardIndex[2] + self.wOutCardIndex[3] + self.wOutCardIndex[4]
    if a == 0 then
        return  false
    end
    return true
end

--用户状态
function GameLayer:onEventUserStatus(useritem,newstatus,oldstatus)
    if self.isGameStart then
        return 
    end
    if not self._gameView or not self._gameView.OnUpdateUser then
        return
    end
    local MyTable = self:GetMeTableID()
    local MyChair = self:GetMeChairID()

    if not MyTable or MyTable == yl.INVLAID_TABLE then
        return
    end

    --旧的清除
    if oldstatus.wTableID == MyTable then
        local index = oldstatus.wChairID
        --if self:isNeedIndex() then
        index = self:getIndex(oldstatus.wChairID, self.isNeedPreOutIndex)
        --end
        
       print('onEventUserStatusonEventUserStatus 1111111 index', index, oldstatus.wChairID, self.isNeedPreOutIndex )
        local viewid = self:SwitchViewChairID(index)
        if viewid and viewid ~= yl.INVALID_CHAIR then
            self._gameView:OnUpdateUser(viewid, nil)
            if PassRoom then
                PassRoom:getInstance():onEventUserState(viewid, useritem, true)
            end
        end
    end

    --更新新状态
    if newstatus.wTableID == MyTable then
        local index = newstatus.wChairID
        index = self:getIndex(newstatus.wChairID, self.isNeedPreOutIndex)
        print('onEventUserStatusonEventUserStatus 2222222222 index', index, newstatus.wChairID, self.isNeedPreOutIndex)
        local viewid = self:SwitchViewChairID(index)
        if viewid and viewid ~= yl.INVALID_CHAIR then
            self._gameView:OnUpdateUser(viewid, useritem)
            if PassRoom then
                PassRoom:getInstance():onEventUserState(viewid, useritem, false)
            end
        end
    end
    -- 定位没开启就用这个
    if not yl.IS_LOCATION_OPEN and GlobalUserItem.bPrivateRoom then
        if self.checkSameIP then
            self:checkSameIP()
        end
    end
    if self._gameView and self._gameView.updateLabsInfo then
       local isNeedWaring = false
       if newstatus.cbUserStatus and oldstatus.cbUserStatus and newstatus.cbUserStatus == yl.US_SIT and oldstatus.cbUserStatus <= yl.US_SIT then
           isNeedWaring = true
       end
       --print('测试数据', newstatus.wChairID, '新状态',newstatus.cbUserStatus,'老状态',oldstatus.cbUserStatus )
       self._gameView:updateLabsInfo( isNeedWaring )
    end
end

--用户积分
function GameLayer:onEventUserScore(useritem)
    if self.isGameStart then
        return 
    end
    if not self._gameView or not self._gameView.OnUpdateUser then
        return
    end
    local MyTable = self:GetMeTableID()
    
    if not MyTable or MyTable == yl.INVLAID_TABLE then
        return
    end 

    if  MyTable == useritem.wTableID then
        local index = useritem.wChairID
        index = self:getIndex(useritem.wChairID, self.isNeedPreOutIndex)
        print('onEventUserScoreonEventUserScoreonEventUserScoreonEventUserScore')
        local viewid = self:SwitchViewChairID(index)
        if viewid and viewid ~= yl.INVALID_CHAIR then
            self._gameView:OnUpdateUser(viewid, useritem)
        end
    end 
    
end


return GameLayer
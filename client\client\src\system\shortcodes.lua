-------------------------------------------------------------------------------
--  创世版1.0
--  项目扩展便捷方法
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
local RichText = import('.RichText')


--------------------------------------------------------------------------------
-- 按路径查找子元素
--------------------------------------------------------------------------------
local function getChildByPath(parent, path, ...)
    local mypath = string.format(path, ...)

    local names = string.split(mypath, '/')
    local node = parent
    for _, v in ipairs(names) do
        node = node:getChildByName(v)
        if not node then
            return nil
        end
    end
    return node
end


--------------------------------------------------------------------------------
-- 同时获取多个子元素，逗号分隔，可以是路径
-- paths: 'name1,name2,path/name3'
--------------------------------------------------------------------------------
local function getChildMultiple(parent, paths)
    local t = {}
    for _, path in ipairs(string.split(paths, ',')) do
        local node = getChildByPath(parent, path:trim())
        table.insert(t, node)
    end
    return unpack(t)
end





--==============================================================================
--	Node
--==============================================================================


local Node = cc.Node


--------------------------------------------------------------------------------
-- 获取子节点
--------------------------------------------------------------------------------
function Node:child(name)
    return getChildMultiple(self, name)
end


--------------------------------------------------------------------------------
-- 子节点描边
--	目前子节点必须是UIText
--------------------------------------------------------------------------------
function Node:childStroke(name, color, size)
    table.walk({getChildMultiple(self, name)}, function(v, k) v:stroke(color, size) end)
end


--------------------------------------------------------------------------------
-- 获取外形尺寸
--------------------------------------------------------------------------------
function Node:box()
    return self:getBoundingBox()
end


--------------------------------------------------------------------------------
-- 设置或获取尺寸
--	注意：当获取时不返回self，也就不能用于串联调用
--------------------------------------------------------------------------------
function Node:size(width, height)
    if not width then
        return self:getContentSize()
    elseif type(width) == "table" then
        self:setContentSize(width)
    else
        self:setContentSize(cc.size(width, height))
    end
    return self
end


--------------------------------------------------------------------------------
-- 设置或获取位置
--	注意：当获取时不返回self，也就不能用于串联调用
--------------------------------------------------------------------------------
function Node:pos(x, y)
    if not x then
        return self:getPositionX(), self:getPositionY()
    else
        if type(x) == 'table' then
            self:setPosition(x)
        else
            self:setPosition(x, y)
        end
        return self
    end
end


--------------------------------------------------------------------------------
-- 设置或获取X位置
--	注意：当获取时不返回self，也就不能用于串联调用
--------------------------------------------------------------------------------
function Node:px(x)
    if not x then
        return self:getPositionX()
    else
        self:setPositionX(x)
        return self
    end
end


--------------------------------------------------------------------------------
-- 设置或获取Y位置
--	注意：当获取时不返回self，也就不能用于串联调用
--------------------------------------------------------------------------------
function Node:py(y)
    if not y then
        return self:getPositionY()
    else
        self:setPositionY(y)
        return self
    end
end


--------------------------------------------------------------------------------
-- 设置或获取锚点
--	注意：当获取时不返回self，也就不能用于串联调用
--------------------------------------------------------------------------------
function Node:anchor(x, y)
    if not x then
        return self:getAnchorPoint()
    else
        if type(x) == 'table' then
            self:setAnchorPoint(x)
        else
            self:setAnchorPoint(cc.p(x, y))
        end
        return self
    end
end


--------------------------------------------------------------------------------
-- 下划线
--	color: cc.c4f
--	size: 线宽
--	indent：两端缩进
--	offset：上下偏移
--------------------------------------------------------------------------------
function Node:underline(color, size, indent, offset)
    color	= color or cc.c4f(1, 1, 1, 1)
    size	= size or 1
    indent	= indent or 0
    offset	= offset or 0

    local line_node = display.newLine({{indent, offset}, {self:size().width-indent, offset}}, {borderWidth=size, borderColor=color})
    line_node:pos(0, 0):addTo(self)

    return self
end


--------------------------------------------------------------------------------
-- 停止结点的所有动作
--	如果有tag，只停单个
--------------------------------------------------------------------------------
function Node:stop(tag)
    if tag then
        self:stopActionByTag(tag)
    else
        self:stopAllActions()
    end
    return self
end


--------------------------------------------------------------------------------
-- 定时执行
--	以动作方式模拟定时器，会随节点的onExit而停止
--	注意：该方法通过Action实现，所以期间执行stopAllActions会导致中断
--		如果要主动停止，可以设置tag，用stop(tag)停止
--	listener：回调
--	delay: 延时时间，无delay或delay<=0立即而且只执行一次
--	repeat_num：-1无限循环，0表示1次，其它表示次数
--	tag:动作tag
--	run_before:true先执行再延时，默认false(也就是先延时再执行）
--------------------------------------------------------------------------------
function Node:perform(listener, delay, repeat_num, tag, run_before)
    local action

    if not delay or delay <= 0 then
        listener(self)
        return self
    else
        local a_delay = cc.DelayTime:create(delay)
        local a_run = cc.CallFunc:create(function(sender) listener(sender) end)
        action = cc.Sequence:create(run_before and {a_run, a_delay} or {a_delay, a_run})
    end

    if repeat_num and type(repeat_num) == 'number' and repeat_num ~= 0 then
        if repeat_num < 0 then
            action = cc.RepeatForever:create(action)
        else
            action = cc.Repeat:create(action, repeat_num)
        end
    end
    
    if tag then
        action:setTag(tag)
    end

    self:runMyAction(action)

    return self
end





--==============================================================================
--	Sprite
--==============================================================================
local Sprite = cc.Sprite

--------------------------------------------------------------------------------
-- 载入纹理
--	path首字母是#也认为是frame
--------------------------------------------------------------------------------
function Sprite:texture(path)
    local is_frame_name = false
    if string.byte(path) == 35 then -- first char is #
        path = path:sub(2)
        is_frame_name = true
    end
    if is_frame_name then
        self:setSpriteFrame(path)
    else
        self:setTexture(path)
    end
    return self
end





--==============================================================================
--	Widget
--==============================================================================
local Widget = ccui.Widget


--------------------------------------------------------------------------------
-- 灰化
--	visible: 是否灰化
--	touchable：如果不为nil则同时设置
--	典型用法：灰化 gray(true, false) 恢复 gray(false, true)
--------------------------------------------------------------------------------
function Widget:gray(visible, touchable, params, opacity)
    visible = visible == nil and true or visible
    if touchable ~= nil then
        self:setTouchEnabled(touchable)
    end
    local gray_name = '_gray_'
    local gray_sprite
    local type_name = tolua.type(self)

    if type_name == 'ccui.Button' or type_name == 'ccui.ImageView' then
        local renderer = self:getVirtualRenderer()
        local gray_sprite = renderer:child(gray_name)
        if not gray_sprite and visible then
            local texture = renderer:getSprite():getTexture()
            gray_sprite = display.newGraySprite(texture, params or {0.299, 0.587, 0.114, 0})
            gray_sprite:setTextureRect(renderer:getSprite():getTextureRect(), renderer:getSprite():isTextureRectRotated(), renderer:getSprite():size())
            gray_sprite:setName(gray_name)
            gray_sprite:opacity(opacity or 200)
            --helper.layout.addChildCenter(renderer, gray_sprite)
            gray_sprite:anchor(0, 0):pos(0, 0):addTo(renderer)

            if type_name == 'ccui.Button' then
                self._old_title_color = self:getTitleColor()
                self:setTitleColor(display.COLOR_GRAY_BROWN)
            end
        elseif gray_sprite and not visible then
            gray_sprite:removeFromParent()
            if type_name == 'ccui.Button' then
                self:setTitleColor(self._old_title_color or display.COLOR_WHITE)
            end
        end
    end

    return self
end


--------------------------------------------------------------------------------
-- 描边
--------------------------------------------------------------------------------
function Widget:stroke(color, size)
    color = color or display.COLOR_STROKE
    size = size or 1.5

    local c_type = tolua.type(self)
    if c_type == 'ccui.Text' then
        self:enableShadow(color)
        self:setCascadeColorEnabled(true)	-- 必须：否则会导致编辑器设置的颜色无限
    elseif c_type == 'ccui.Button' then
        self:getTitleRenderer():enableShadow(color)
    end

    return self
end




--==============================================================================
--	ImageView
--==============================================================================
local ImageView = ccui.ImageView


--------------------------------------------------------------------------------
-- 载入纹理
--	is_frame_name默认false，表示从文件载入
--	path首字母是#也认为是frame
--------------------------------------------------------------------------------
function ImageView:texture(path, is_frame_name)
    if string.byte(path) == 35 then -- first char is #
        path = path:sub(2)
        is_frame_name = true
    end
    if is_frame_name then
        self:loadTexture(path, ccui.TextureResType.plistType)
    else
        self:loadTexture(path)
    end
    return self
end



--==============================================================================
--	Text
--==============================================================================
local Text = ccui.Text


--------------------------------------------------------------------------------
-- 设置富文本
--	自动设置匹配位置、锚点、颜色、尺寸等参数
--  注意：需要在上述参数设置后调用
--------------------------------------------------------------------------------
function Text:setRich(text)
    -- 因为Text错误地使用setColor而不是setTextColor作为字体颜色，所以需要处理一下
    self._origin_color = self._origin_color or self:getTextColor()
    -- 保存是因为后面会被强制改掉
    self._origin_ignore = self._origin_ignore or self:isIgnoreContentAdaptWithSize()
    -- 如果原始是带尺寸的，创建要用原始的，因为尺寸后面会被修改
    self._origin_size = self._origin_size or self:size()

    --self:setColor(cc.c3b(255, 255, 255))	-- 必须设白，否则变成混合色
    self:setString('')
    self:removeAllChildren()
    
    local rich = text
    if type(text) == 'string' or type(text) == 'number' then
        text = tostring(text)
        local area_size = not self._origin_ignore and self._origin_size or nil
        local color_escapes = {['='] = self._origin_color}
        rich = RichText.new(text, self:getFontSize(), area_size, cs.app.FONT_NAME, color_escapes)
        rich:setName('rich')
    end

    -- 如果不是默认指定了尺寸的话
    if self._origin_ignore then
        self:ignoreContentAdaptWithSize(false)	-- 必须false，否则会按内容自适应尺寸，但此时实际的内容时''，导致size()返回 0, 0
        self:size(rich:size())
    end
    rich:anchor(self:anchor()):pos(self:getAnchorPointInPoints()):addTo(self)
    return self
end

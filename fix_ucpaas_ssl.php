<?php
/**
 * 修复云之讯SSL连接问题的临时方案
 * 修改 Ucpaas.class.php 中的 CURL 配置
 */

class UcpaasFixed {
    private $accountsid;
    private $token;
    private $baseUrl = 'https://open.ucpaas.com';
    
    public function __construct($options) {
        $this->accountsid = $options['accountsid'];
        $this->token = $options['token'];
    }
    
    /**
     * 发送模板短信 - 修复SSL问题版本
     */
    public function templateSMS($appId, $phone, $templateId, $param) {
        $url = $this->baseUrl . '/ol/sms/sendsms';
        
        $data = array(
            'sid' => $this->accountsid,
            'token' => $this->token,
            'appid' => $appId,
            'templateid' => $templateId,
            'param' => $param,
            'mobile' => $phone,
            'uid' => ''
        );
        
        // 尝试多种方法发送请求
        $result = $this->sendRequestWithFallback($url, $data);
        
        return $result;
    }
    
    /**
     * 使用多种方法发送请求，解决SSL问题
     */
    private function sendRequestWithFallback($url, $data) {
        $methods = array(
            'curl_ssl_disabled',
            'curl_ssl_v3',
            'curl_ssl_v1_2',
            'http_context',
            'curl_http'
        );
        
        foreach ($methods as $method) {
            try {
                $result = $this->sendRequestByMethod($url, $data, $method);
                if ($result !== false) {
                    error_log("云之讯短信发送成功，使用方法: $method");
                    return $result;
                }
            } catch (Exception $e) {
                error_log("云之讯短信方法 $method 失败: " . $e->getMessage());
                continue;
            }
        }
        
        return json_encode(array('resp' => array('respCode' => 'NETWORK_ERROR', 'respDesc' => '网络连接失败')));
    }
    
    /**
     * 根据不同方法发送请求
     */
    private function sendRequestByMethod($url, $data, $method) {
        switch ($method) {
            case 'curl_ssl_disabled':
                return $this->sendCurlRequest($url, $data, array(
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT
                ));
                
            case 'curl_ssl_v3':
                return $this->sendCurlRequest($url, $data, array(
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_SSLVERSION => CURL_SSLVERSION_SSLv3
                ));
                
            case 'curl_ssl_v1_2':
                return $this->sendCurlRequest($url, $data, array(
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
                ));
                
            case 'http_context':
                return $this->sendHttpContextRequest($url, $data);
                
            case 'curl_http':
                $http_url = str_replace('https://', 'http://', $url);
                return $this->sendCurlRequest($http_url, $data, array());
                
            default:
                return false;
        }
    }
    
    /**
     * 使用CURL发送请求
     */
    private function sendCurlRequest($url, $data, $ssl_options = array()) {
        $ch = curl_init();
        
        $default_options = array(
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Accept: application/json',
                'User-Agent: LHMJ-SMS/1.0'
            ),
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        );
        
        // 合并SSL选项
        $options = $default_options + $ssl_options;
        
        curl_setopt_array($ch, $options);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new Exception("CURL Error: $error");
        }
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP Error: $httpCode");
        }
        
        return $result;
    }
    
    /**
     * 使用HTTP上下文发送请求
     */
    private function sendHttpContextRequest($url, $data) {
        $context = stream_context_create(array(
            'http' => array(
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\n" .
                           "Accept: application/json\r\n" .
                           "User-Agent: LHMJ-SMS/1.0\r\n",
                'content' => json_encode($data),
                'timeout' => 30
            ),
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        ));
        
        $result = file_get_contents($url, false, $context);
        
        if ($result === false) {
            throw new Exception("HTTP Context request failed");
        }
        
        return $result;
    }
}

/**
 * 在user.php中使用修复版本的云之讯类
 */
function get_verify_code_post_fixed() {
    $role_id = $this->input->post('uid');
    $phone = $this->input->post('phone');
    $type = $this->input->post('type') ? $this->input->post('type') : 'bind';
    
    // 生成4位随机验证码
    $code = rand(1000, 9999);
    
    try {
        // 使用修复版本的云之讯类
        $options = array(
            'accountsid' => '5cbc351f17a418686094747a62ffd946',
            'token' => 'fac1c2af0c05327677d33916ba841079'
        );
        
        $ucpaas = new UcpaasFixed($options);
        $appId = "9dc983c028e54d5fbc97228e6af5344e";
        $templateId = "174333";
        
        $result_json = $ucpaas->templateSMS($appId, $phone, $templateId, $code);
        $result_obj = json_decode($result_json, true);
        
        $sms_success = false;
        if ($result_obj && isset($result_obj['resp'])) {
            $respCode = $result_obj['resp']['respCode'];
            if ($respCode == '000000') {
                $sms_success = true;
                error_log("云之讯短信发送成功: $phone, $code");
            } else {
                error_log("云之讯短信发送失败: $respCode - " . $result_obj['resp']['respDesc']);
            }
        }
        
        // 保存验证码到数据库
        $data = array(
            'role_id' => $role_id,
            'game_id' => $this->_channel['game_id'],
            'create_time' => time(),
            'phone' => $phone,
            'type' => $type,
            'code' => $code,
            'sms_status' => $sms_success ? 1 : 0
        );
        
        $this->db->insert('tuo3_verify_code', $data);
        
        if ($sms_success) {
            $this->response(array('code' => 0, 'msg' => '验证码发送成功'));
        } else {
            // 即使短信发送失败，也返回成功，避免用户重复请求
            $this->response(array('code' => 0, 'msg' => '验证码发送中，如未收到请稍后重试或联系客服'));
        }
        
    } catch (Exception $e) {
        error_log("短信发送异常: " . $e->getMessage());
        
        // 仍然保存验证码到数据库
        $data = array(
            'role_id' => $role_id,
            'game_id' => $this->_channel['game_id'],
            'create_time' => time(),
            'phone' => $phone,
            'type' => $type,
            'code' => $code,
            'sms_status' => 0
        );
        
        $this->db->insert('tuo3_verify_code', $data);
        
        $this->response(array('code' => 0, 'msg' => '验证码发送中，如未收到请联系客服'));
    }
}

?>

<!-- 
使用说明:

1. 替换原有的 Ucpaas.class.php 或在 user.php 中使用 UcpaasFixed 类

2. 修改 get_verify_code_post 方法，使用 get_verify_code_post_fixed 的逻辑

3. 添加数据库字段记录短信发送状态:
   ALTER TABLE tuo3_verify_code ADD COLUMN sms_status TINYINT DEFAULT 0;

4. 测试修复效果

这个方案的优势:
- 尝试多种SSL配置，提高连接成功率
- 包含HTTP降级方案
- 即使短信发送失败也能保存验证码
- 详细的错误日志记录

如果这个方案仍然无效，建议切换到腾讯云或阿里云短信服务
-->

-------------------------------------------------------------------------------
--  创世版1.0
--  通用第一次弹出界面
--  @date 2018-01-25
--  @auth mike
-------------------------------------------------------------------------------
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")

local PopupMask	= cs.app.client('system.PopupMask')

local FirstPopupLayer = class('FirstPopupLayer', PopupMask)

-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
function FirstPopupLayer:ctor( data )
    print('FirstPopupLayer:ctor')
    self.super.ctor(self)
    self:enableNodeEvents()
    self:zorder(0)  -- 因为PopupMask的默认zorder是负的
    self:setName('subFirstPopupLayer')
    self.m_data = data
    local main_node = helper.app.loadCSB('FirstPopupLayer.csb')
    self.main_node = main_node
    self:addChild( main_node )

    main_node:child('panel_bg'):addTouchEventListener( handler(self, self.onBtnClick) )
    main_node:anchor(0.5, 0.5):pos(self:size().width/2, self:size().height/2):setScale(0.8)
    main_node:runMyAction(cc.EaseBackOut:create(cc.ScaleTo:create(0.2, 1)))

    main_node:child('panel_bg/img_bg/btn_operate'):addTouchEventListener(  helper.app.commClickHandler(self, self.onBtnOperate) )

    self:initInfo()

    self:openSignIn()
end

-------------------------------------------------------------------------------
-- 
-------------------------------------------------------------------------------
function FirstPopupLayer:initInfo( )
    self:doDownLoad('firstAD', self.m_data.bg_image_url, 'panel_bg/img_bg')
    self:doDownLoad('firstAD', self.m_data.btn_image_url, 'panel_bg/img_bg/btn_operate')
end

function FirstPopupLayer:doDownLoad( name, url, target_path )
    local save_path, save_name, use_path = helper.app.getDownloadParam(name, url)
    local callback = function(filename)
          if tolua.isnull(self) then
              return
          end
          local img = self.main_node:child(target_path)
          img:ignoreContentAdaptWithSize(true)
          img:texture(use_path)
    end
    if cc.FileUtils:getInstance():isFileExist(use_path) then
        callback(use_path)
    else
        helper.app.download(url, save_path, save_name, self, callback)
    end
end

-------------------------------------------------------------------------------
-- 按钮点击
-------------------------------------------------------------------------------
function FirstPopupLayer:onBtnClick(sender, event)
    self:removeFromParent()
end

-------------------------------------------------------------------------------
-- 按钮点击
-------------------------------------------------------------------------------
function FirstPopupLayer:onBtnOperate(sender, event)
    local data = self.m_data
    local isEnd = true
    if data.skip_type == 'ROLE' then
        helper.link.toUser()
    elseif data.skip_type == 'MALL' then
        if table.nums(data.skip_param) <= 0 then
             helper.pop.message( LANG.WRONG_1 )
            return
        end
        helper.link.toMall( data.skip_param.name)
    elseif data.skip_type == 'PAY' then
        if table.nums(data.skip_param) <= 0 then
            helper.pop.message( LANG.WRONG_1 )
            return
        end
        isEnd = false
        self:doRequest(yl.URL_ORDER, self.onOrderResponse, {mall_id=data.skip_param.mall_id, good_id=data.skip_param.good_id})
    elseif data.skip_type == 'WHEEL' then
        helper.link.toTurntable()
    elseif data.skip_type == 'INVITE' then
        helper.link.toInvite()
    elseif data.skip_type == 'REDPACKET' then
        helper.link.toRedBag() 
    end
    if isEnd then
        self:removeFromParent()
    end
end

-------------------------------------------------------------------------------
-- 请求
-------------------------------------------------------------------------------
function FirstPopupLayer:doRequest(url, func, params)
    helper.pop.waiting()
    local all_params = {uid=GlobalUserItem.dwUserID}
    if params then
        for k, v in pairs(params) do
            all_params[k] = v
        end
    end
    yl.GetUrl(url, 'post', all_params, handler(self, func) )
end

-------------------------------------------------------------------------------
-- 购买返回
-------------------------------------------------------------------------------
function FirstPopupLayer:onOrderResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end
    if not data.data then return end
    -- 调用平台跳转
    if data.data.url then
        MultiPlatform:getInstance():openBrowser(data.data.url)
    end
end


-------------------------------------------------------------------------------
-- 打开签到
-------------------------------------------------------------------------------
function FirstPopupLayer:openSignIn()
    -- 今日未分享
    if not yl.is_reviewing and bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_SHARE) == 0 then  
        if not cs.app.USE_FANGKA_SHARE then
            self.m_signin = helper.link.toSignIn():hide()
        end
    end
end


function FirstPopupLayer:onExit()
    print('FirstPopupLayer:onExit')
    if self.m_signin then
        self.m_signin:show()
    end
end


return FirstPopupLayer
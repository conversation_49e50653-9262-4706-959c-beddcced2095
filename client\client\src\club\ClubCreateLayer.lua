-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部创建
--  @date 2018-01-13
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')


local ROOM_NUMS = 6 -- 口令长度


local ClubCreateLayer = class("ClubCreateLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubCreateLayer:ctor(init_club_id)
    print('ClubCreateLayer:ctor...', init_club_id)
    self:setName('club_create_layer')
    self.m_club_adds = {}   -- 记录新建的俱乐部

    local main_node = ClubUtil.initUI(self, 'ClubCreateLayer.csb', self.onBtnBack)

    helper.logic.addListenerByName(self, {main_node:child('btn_join, btn_create, panel_create/btn_agree')})

    main_node:child('panel_join'):show()
    main_node:child('panel_create'):hide()

    for i = 1, ROOM_NUMS do
        main_node:child('panel_join/label_' .. i):setString('')
    end

    if init_club_id and #init_club_id == ROOM_NUMS then
        self:perform(function()
            main_node:child('panel_join/panel_keyboard').m_numbers = init_club_id
            self:onKeyboardNum(init_club_id, {'+'})
        end, 0.3)
    end

    -- 创建规则
    local label_notice = main_node:child('panel_create/label_notice')
    local notice = LANG{'CLUB_CREATE_NOTICE', weixin=cs.app.WEIXIN_KEFU}
    local rich = cs.app.client('system.RichText').new(notice,
        label_notice:getFontSize(), cc.size(label_notice:size().width, 0),
        cs.app.FONT_NAME, {['='] = label_notice:getTextColor()})
    main_node:child('panel_create/listview'):pushBackCustomItem(rich)
    label_notice:removeFromParent()

    helper.logic.initKeyboard(main_node:child('panel_join/panel_keyboard'), handler(self, self.onKeyboardNum))
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubCreateLayer:onEnter()
    print('ClubCreateLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_APPLY, self, self.onApplyResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubCreateLayer:onExit()
    print('ClubCreateLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function ClubCreateLayer:onBtnBack(sender)
    if #self.m_club_adds > 0 then   -- 如果有新建的俱乐部，需要告诉主UI
        local main_layer = helper.app.getFromScene('club_main_layer')
        if main_layer then
            main_layer:addClubs(self.m_club_adds)
        end
    end
    ClubUtil.back(self)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 加入按钮点击
-------------------------------------------------------------------------------
function ClubCreateLayer:onBtnJoin(sender)
    self.main_node:child('panel_join'):show()
    self.main_node:child('panel_create'):hide()
end


-------------------------------------------------------------------------------
-- 创建按钮点击
-------------------------------------------------------------------------------
function ClubCreateLayer:onBtnCreate(sender)
    self.main_node:child('panel_join'):hide()
    self.main_node:child('panel_create'):show()
end


-------------------------------------------------------------------------------
-- 创建按钮点击
-------------------------------------------------------------------------------
function ClubCreateLayer:onBtnAgree(sender)
    ClubUtil.open(self, 'club.ClubCreateFormLayer' )
end


-------------------------------------------------------------------------------
-- 键盘数字输入
-------------------------------------------------------------------------------
function ClubCreateLayer:onKeyboardNum(nums, opts, sender)
    local panel_join = self.main_node:child('panel_join')
    local len = #nums
    for i = 1, ROOM_NUMS do
        local new = i > len and '' or nums:sub(i, i)
        local label = panel_join:child('label_' .. i)
        label:setString(new)
    end

    if opts[1] == '+' then  -- 输入动作时显示动画
        panel_join:child('label_' .. len):stop():scale(0):runAction(cc.Sequence:create(
                cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
        ))
    end

    if len == ROOM_NUMS then
        -- 显示验证信息输入对话框
        self.m_app_club_id = nums
        local title = display.newSprite('word/font_title_submit_apply.png')
        helper.pop.alert({title, LANG.CLUB_APPLY_TIP}, handler(self, self.onApply), true, self.m_last_apply_info or '')
    end
end


-------------------------------------------------------------------------------
-- 键盘数字输入
-------------------------------------------------------------------------------
function ClubCreateLayer:onApply(text)
    self.m_last_apply_info = text
    local values = {dwID=tonumber(self.m_app_club_id), szMsg=text}
    ClubUtil.send(cmd.SUB_CLUB_APPLY, cmd.CMD_GR_IDMsg, values)
end


-------------------------------------------------------------------------------
-- 申请返回
-------------------------------------------------------------------------------
function ClubCreateLayer:onApplyResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_APPLY_SUCC )
    end)
end


-------------------------------------------------------------------------------
-- 创建成功（创建表单界面调用）
-------------------------------------------------------------------------------
function ClubCreateLayer:onClubCreated(club)
    table.insert(self.m_club_adds, club)
end


return ClubCreateLayer

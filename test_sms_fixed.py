#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import time
import requests
import urllib3
import ssl

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def generate_sign(params, channel_key):
    """生成签名"""
    # 按键名排序
    sorted_keys = sorted(params.keys())
    
    # 拼接所有值
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    
    # 加上渠道密钥
    sign_str = param_str + channel_key
    
    # MD5签名
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()

def test_sms_api_with_ssl_fix():
    """测试短信验证码接口 - 修复SSL问题"""
    
    # 基础参数
    params = {
        'uid': '123456',  # 测试用户ID
        'phone': '13800138000',  # 测试手机号（请替换为您的真实手机号）
        'type': 'login',  # 类型
        'uuid': 'TEST_DEVICE_ID',  # 设备ID
        'timestamp': str(int(time.time() * 1000)),  # 时间戳
        'channel': '50010001',  # 渠道号
        'c_version': '10',  # 客户端版本
        'res_version': '1',  # 资源版本
    }
    
    # 渠道密钥
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    
    # 生成签名
    sign = generate_sign(params, channel_key)
    params['sign'] = sign
    
    # 请求URL
    url = 'https://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code'
    
    print("=== 测试短信验证码接口 (SSL修复版) ===")
    print(f"URL: {url}")
    print(f"参数: {params}")
    print()
    
    # 方法1: 忽略SSL验证
    print("方法1: 忽略SSL验证")
    try:
        response = requests.post(
            url, 
            data=params, 
            timeout=30,
            verify=False  # 忽略SSL证书验证
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"JSON数据: {json_data}")
                
                if json_data.get('code') == 0:
                    print("✅ 短信发送成功！")
                    return True
                else:
                    print(f"❌ 短信发送失败: {json_data.get('msg', '未知错误')}")
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 方法2: 使用HTTP而不是HTTPS
    print("方法2: 使用HTTP协议")
    try:
        http_url = url.replace('https://', 'http://')
        print(f"HTTP URL: {http_url}")
        
        response = requests.post(
            http_url, 
            data=params, 
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"JSON数据: {json_data}")
                
                if json_data.get('code') == 0:
                    print("✅ 短信发送成功！")
                    return True
                else:
                    print(f"❌ 短信发送失败: {json_data.get('msg', '未知错误')}")
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 方法3: 自定义SSL上下文
    print("方法3: 自定义SSL上下文")
    try:
        # 创建自定义SSL上下文
        session = requests.Session()
        session.mount('https://', requests.adapters.HTTPAdapter())
        
        response = session.post(
            url, 
            data=params, 
            timeout=30,
            verify=False,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"JSON数据: {json_data}")
                
                if json_data.get('code') == 0:
                    print("✅ 短信发送成功！")
                    return True
                else:
                    print(f"❌ 短信发送失败: {json_data.get('msg', '未知错误')}")
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    return False

def test_server_connectivity():
    """测试服务器连通性"""
    print("=== 测试服务器连通性 ===")
    
    # 测试基本连接
    test_urls = [
        'https://lhmj.tuo3.com.cn',
        'http://lhmj.tuo3.com.cn',
        'https://lhmj.tuo3.com.cn/admin',
        'http://lhmj.tuo3.com.cn/admin'
    ]
    
    for url in test_urls:
        try:
            print(f"测试: {url}")
            response = requests.get(url, timeout=10, verify=False)
            print(f"  状态码: {response.status_code}")
            print(f"  响应长度: {len(response.text)} 字符")
            if response.status_code == 200:
                print("  ✅ 连接成功")
            else:
                print(f"  ⚠️ 状态码异常: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 连接失败: {e}")
        print()

if __name__ == '__main__':
    # 先测试服务器连通性
    test_server_connectivity()
    
    print("\n" + "="*60 + "\n")
    
    # 再测试短信接口
    success = test_sms_api_with_ssl_fix()
    
    if not success:
        print("\n=== 建议的解决方案 ===")
        print("1. 检查网络连接是否正常")
        print("2. 尝试使用VPN或更换网络环境")
        print("3. 联系服务器管理员检查SSL证书配置")
        print("4. 确认服务器是否正常运行")
        print("5. 检查防火墙是否阻止了连接")

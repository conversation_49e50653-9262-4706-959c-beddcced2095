<?php
require(APPPATH.'/libraries/REST_Controller.php');

class Feedback extends CI_Controller
{
	public function __construct() 
    {
    	parent::__construct();
    }

    /**
     * [add_post 添加新的]
     */
    public function create()
    {
		$uid = $this->input->post('uid');
		$channel_id = $this->input->post('channel_id');

		$phone = $this->input->post('phone');

		$content = trim($this->input->post('content'));

		$regx ='/^(1(([35][0-9])|(47)|[8][0126789]))\d{8}$/';

		if(!preg_match($regx,$phone)){
			$this->_response(array('status'=>FALSE,'message'=>'请输入正确的手机号码'),200);
		}

		if(!$content) {
			$this->_response(array('status'=>FALSE,'message'=>'请输入反馈内容'),200);
		}

		$data = array(
			'phone'=>$phone,
			'content'=>$content,
			'role_id'=>$uid,
			'channel_id'=>$channel_id,
			'create_time'=>time()
		);

		$this->db->insert('feedback',$data);

		if($this->db->insert_id()>0) {
			$this->_response(array('status'=>true,'message'=>'反馈成功'), 200); // OK (200) being the HTTP response code
		} else {
			$this->_response(array('status'=>false,'message'=>'反馈失败，请重试'), 200);
		}
	}

	private function _response($data) {
		echo json_encode($data);exit;
	}
}
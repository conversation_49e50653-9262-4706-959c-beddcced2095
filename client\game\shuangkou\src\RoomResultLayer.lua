-------------------------------------------------------------------------------
--  创世版1.0
--  房间结算(大结算)
--  @date 2017-06-16
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local MultiPlatform = cs.app.client('external.MultiPlatform')


local RoomResultLayer = class("RoomResultLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomResultLayer:ctor(game_layer, cmd_table)
    print('RoomResultLayer:ctor...')
    self:enableNodeEvents()
    self.m_game_layer = game_layer
    self:setName('subRoomResultLayer')
    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomResultLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('template'):hide()

    main_node:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    main_node:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )

    -- 初始化TopBar
    --helper.logic.initTopBar(main_node, self, handler(self, self.onBtnBack))

    self:showResult(game_layer, cmd_table)
end

-------------------------------------------------------------------------------
-- 返回按钮
-------------------------------------------------------------------------------
function RoomResultLayer:onBtnBack(sender)
    GlobalUserItem.bWaitQuit = false
    self.m_game_layer:onExitRoom()
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function RoomResultLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 显示结果
--  game_layer: GameLayer
-------------------------------------------------------------------------------
function RoomResultLayer:showResult(game_layer, cmd_table)
    local main_node = self.main_node
    local template = main_node:child('template')

    local label_bill = main_node:child('label_bill')
    label_bill:setString( LANG{'GAME_SCORE_NOTE', bill_no=cmd_table.lBillNo} )

    local score_list = cmd_table.lScore[1]

    -- 大赢家
    local score_max = 0
    for i, v in ipairs(score_list) do
        score_max = math.max(score_max, v)
    end

    -- 日志
    LogAsset:getInstance():logData( cjson.encode(score_list), true )

    --local tabUserRecord = game_layer:getDetailScore()
    local nPlayerCount = game_layer._gameView.player_num
    print('result nPlayerCount is : ', nPlayerCount)
    local size = template:size()
    local gap = 10
    local start_x = display.width/2 + (nPlayerCount == 3 and -(size.width + gap) or -(nPlayerCount - 1) * 0.5 * (size.width + gap))
    for i = 1, nPlayerCount do repeat
        local userItem = game_layer:getUserInfoByChairID(i - 1)
        if userItem == nil then
            break
        end
        local panel = template:clone():show():addTo(main_node)
        panel:px(start_x):zorder(10)
        start_x = start_x + size.width + gap

        -- 头像
        local panel_avator = panel:child('panel_avator')
        head = PopupHead:create(self, userItem, 150, 150)
        head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

        -- 昵称
        panel:child('label_name'):setString(userItem.szNickName)
        
        
        -- 玩家ID
        panel:child('label_id'):setString('ID:' .. helper.str.formatUserID(userItem.dwUserID))

        
        -- 胡牌次数
        panel:child('txt_win_times'):setString( cmd_table.wHuTimes[1][userItem.wChairID + 1] )

        local room_data = PassRoom:getInstance().m_tabPriData
        local failed_count = cmd_table.wLoseTimes[1][userItem.wChairID + 1] or 0
        panel:child('txt_failed_times'):setString( failed_count )

        -- 最大番数
        --panel:child('label_max_fan'):setString( cmd_table.dwMaxScore[1][i] )

        -- 最大胡型
        --panel:child('label_paixing'):hide() -- 暂时屏蔽
        --panel:child('label_paixing'):setString( cmd_table.szMaxTurnDesc[1][i] )
        --local str = LANG{'RESULT_NO', win_times=cmd_table.wWinTimes[1][i] or 0, lose_times=cmd_table.wLoseTimes[1][i] or 0}
       -- panel:child('label_result'):setString(str)

        -- 总成绩
        panel:child('label_score'):setString( (score_list[userItem.wChairID + 1] >= 0 and '+' or '') .. score_list[userItem.wChairID + 1] )

        --房主标志
        local is_fangzhu = userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID
        panel:child('img_fangzhu'):setVisible(is_fangzhu)

        --大赢家标志
        local is_winner = score_list[userItem.wChairID + 1] == score_max and score_list[userItem.wChairID + 1] > 0
        --panel:child( is_winner and 'bg_normal' or 'bg_win' ):removeFromParent()
        if is_winner then
            panel:child('img_winner'):show()
        end

        if game_layer:GetMeChairID() + 1 == i then
            local color = cc.c3b(0x76, 0xFF, 0x71)
            panel:child('label_score'):setColor(color)
            panel:child('label_total_score'):setColor(color)
            panel:child('txt_failed_times'):setColor(color)
            panel:child('txt_win_times'):setColor(color)
        end
    until true
    end
    --self:hide()
    --self:perform(function() self:show() end, 4.0)
end


return RoomResultLayer
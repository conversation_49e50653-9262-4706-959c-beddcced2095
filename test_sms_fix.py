#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import random
import urllib3
import ssl

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_ucpaas_with_multiple_ssl_configs():
    """使用多种SSL配置测试云之讯"""
    
    print("=== 测试云之讯多种SSL配置 ===")
    
    # 配置信息
    accountsid = '5cbc351f17a418686094747a62ffd946'
    token = 'fac1c2af0c05327677d33916ba841079'
    appId = '9dc983c028e54d5fbc97228e6af5344e'
    templateId = '174333'
    phone = '***********'
    code = random.randint(1000, 9999)
    
    data = {
        'sid': accountsid,
        'token': token,
        'appid': appId,
        'templateid': templateId,
        'param': str(code),
        'mobile': phone,
        'uid': ''
    }
    
    print(f"测试参数: 手机号={phone}, 验证码={code}")
    
    # 方法1: 使用自定义SSL上下文
    print("\n方法1: 自定义SSL上下文")
    try:
        import ssl
        
        # 创建不验证证书的SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # 使用requests-toolbelt或直接修改adapter
        session = requests.Session()
        
        # 创建自定义适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.ssl_ import create_urllib3_context
        
        class SSLAdapter(HTTPAdapter):
            def init_poolmanager(self, *args, **kwargs):
                ctx = create_urllib3_context()
                ctx.check_hostname = False
                ctx.verify_mode = ssl.CERT_NONE
                kwargs['ssl_context'] = ctx
                return super().init_poolmanager(*args, **kwargs)
        
        session.mount('https://', SSLAdapter())
        
        response = session.post(
            'https://open.ucpaas.com/ol/sms/sendsms',
            json=data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法1成功！")
                return True
        
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2: 降级到HTTP
    print("\n方法2: 降级到HTTP")
    try:
        response = requests.post(
            'http://open.ucpaas.com/ol/sms/sendsms',
            json=data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法2成功！")
                return True
        
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3: 使用代理或不同的User-Agent
    print("\n方法3: 修改请求头")
    try:
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (compatible; LHMJ-SMS/1.0)',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        response = requests.post(
            'https://open.ucpaas.com/ol/sms/sendsms',
            json=data,
            headers=headers,
            timeout=30,
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法3成功！")
                return True
        
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    return False

def test_tencent_cloud_sms_api():
    """测试腾讯云短信API连通性"""
    print("\n=== 测试腾讯云短信API连通性 ===")
    
    try:
        # 只测试连通性，不发送真实短信
        response = requests.get(
            'https://sms.tencentcloudapi.com/',
            timeout=10,
            verify=False
        )
        
        print(f"腾讯云短信API连通性: 状态码 {response.status_code}")
        
        if response.status_code in [200, 400, 403]:  # 这些都表示连接正常
            print("✅ 腾讯云短信API连接正常")
            return True
        else:
            print("⚠️ 腾讯云短信API响应异常")
            return False
            
    except Exception as e:
        print(f"❌ 腾讯云短信API连接失败: {e}")
        return False

def generate_migration_plan():
    """生成迁移计划"""
    print("\n=== 短信服务迁移计划 ===")
    
    print("基于测试结果，建议的迁移方案:")
    print()
    print("🎯 方案A: 立即迁移到腾讯云短信")
    print("优势:")
    print("- 连接稳定，无SSL问题")
    print("- 价格合理，服务可靠")
    print("- 文档完善，易于集成")
    print("- 支持多种编程语言")
    print()
    print("实施步骤:")
    print("1. 申请腾讯云账户并开通短信服务")
    print("2. 创建短信应用和模板")
    print("3. 获取SecretId和SecretKey")
    print("4. 修改user.php中的短信发送代码")
    print("5. 测试验证功能")
    print()
    print("🎯 方案B: 修复云之讯SSL问题")
    print("优势:")
    print("- 无需更换服务商")
    print("- 保持现有配置")
    print("- 成本较低")
    print()
    print("风险:")
    print("- SSL问题可能再次出现")
    print("- 依赖网络环境")
    print("- 维护成本较高")
    print()
    print("🎯 方案C: 多服务商备用")
    print("优势:")
    print("- 高可用性")
    print("- 降低单点故障风险")
    print("- 灵活切换")
    print()
    print("实施:")
    print("- 主用腾讯云，备用阿里云")
    print("- 自动故障转移")
    print("- 统一的接口封装")

if __name__ == '__main__':
    print("开始测试短信服务修复方案...")
    
    # 测试云之讯修复方案
    ucpaas_success = test_ucpaas_with_multiple_ssl_configs()
    
    # 测试腾讯云连通性
    tencent_success = test_tencent_cloud_sms_api()
    
    print(f"\n=== 测试结果总结 ===")
    print(f"云之讯修复方案: {'✅ 成功' if ucpaas_success else '❌ 失败'}")
    print(f"腾讯云连通性: {'✅ 正常' if tencent_success else '❌ 异常'}")
    
    # 生成建议
    if ucpaas_success:
        print("\n💡 建议: 可以继续使用云之讯，但建议添加备用方案")
    elif tencent_success:
        print("\n💡 建议: 立即迁移到腾讯云短信服务")
    else:
        print("\n💡 建议: 检查网络环境，考虑使用代理或联系网络管理员")
    
    # 生成详细的迁移计划
    generate_migration_plan()

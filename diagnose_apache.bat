@echo off
echo ===== Apache 深入诊断脚本 =====
echo.

echo 1. 检查 Apache 服务状态...
sc query Apache2.4
echo.

echo 2. 检查端口监听状态...
echo 检查端口 80:
netstat -ano | findstr :80
echo.
echo 检查端口 443:
netstat -ano | findstr :443
echo.

echo 3. 检查 Apache 进程...
tasklist | findstr httpd
tasklist | findstr apache
echo.

echo 4. 检查可能的错误...
echo 检查 Apache 错误日志 (最后 10 行):
if exist "E:\xampp\apache\logs\error.log" (
    powershell "Get-Content 'E:\xampp\apache\logs\error.log' | Select-Object -Last 10"
) else (
    echo 错误日志文件不存在
)
echo.

echo 5. 检查 Apache 访问日志 (最后 5 行):
if exist "E:\xampp\apache\logs\access.log" (
    powershell "Get-Content 'E:\xampp\apache\logs\access.log' | Select-Object -Last 5"
) else (
    echo 访问日志文件不存在
)
echo.

echo 6. 检查 PHP 状态...
echo 检查 PHP 进程:
tasklist | findstr php
echo.

echo 7. 测试本地连接...
echo 测试 localhost:
curl -I http://localhost/ 2>nul || echo curl 命令不可用
echo.

echo 8. 检查防火墙状态...
netsh advfirewall show allprofiles state
echo.

echo 9. 建议的检查项目:
echo - 检查 E:\xampp\apache\conf\httpd.conf 配置
echo - 检查 E:\xampp\apache\conf\extra\httpd-vhosts.conf
echo - 检查 PHP 配置 E:\xampp\php\php.ini
echo - 重启整个 XAMPP
echo - 检查磁盘空间
echo.

pause

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>turn_bg_block.png</key>
            <dict>
                <key>frame</key>
                <string>{{568,343},{167,163}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{6,8},{167,163}}</string>
                <key>sourceSize</key>
                <string>{179,179}</string>
            </dict>
            <key>turn_bg_content.png</key>
            <dict>
                <key>frame</key>
                <string>{{621,187},{112,103}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{112,103}}</string>
                <key>sourceSize</key>
                <string>{112,103}</string>
            </dict>
            <key>turn_bg_input.png</key>
            <dict>
                <key>frame</key>
                <string>{{568,2},{339,51}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{339,51}}</string>
                <key>sourceSize</key>
                <string>{339,51}</string>
            </dict>
            <key>turn_bg_panel.png</key>
            <dict>
                <key>frame</key>
                <string>{{461,525},{283,283}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{283,283}}</string>
                <key>sourceSize</key>
                <string>{283,283}</string>
            </dict>
            <key>turn_bg_plate.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{523,521}}</string>
                <key>offset</key>
                <string>{3,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,4},{523,521}}</string>
                <key>sourceSize</key>
                <string>{529,529}</string>
            </dict>
            <key>turn_bg_progress.png</key>
            <dict>
                <key>frame</key>
                <string>{{437,525},{415,22}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{2,0},{415,22}}</string>
                <key>sourceSize</key>
                <string>{419,22}</string>
            </dict>
            <key>turn_bg_title.png</key>
            <dict>
                <key>frame</key>
                <string>{{461,872},{257,30}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{257,30}}</string>
                <key>sourceSize</key>
                <string>{257,30}</string>
            </dict>
            <key>turn_btn_green.png</key>
            <dict>
                <key>frame</key>
                <string>{{621,292},{90,47}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,1},{90,47}}</string>
                <key>sourceSize</key>
                <string>{92,51}</string>
            </dict>
            <key>turn_close.png</key>
            <dict>
                <key>frame</key>
                <string>{{667,904},{71,71}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,1},{71,71}}</string>
                <key>sourceSize</key>
                <string>{73,73}</string>
            </dict>
            <key>turn_dot_dark.png</key>
            <dict>
                <key>frame</key>
                <string>{{607,953},{27,27}}</string>
                <key>offset</key>
                <string>{-1,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{2,3},{27,27}}</string>
                <key>sourceSize</key>
                <string>{33,33}</string>
            </dict>
            <key>turn_dot_light.png</key>
            <dict>
                <key>frame</key>
                <string>{{527,435},{31,31}}</string>
                <key>offset</key>
                <string>{-1,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,1},{31,31}}</string>
                <key>sourceSize</key>
                <string>{33,33}</string>
            </dict>
            <key>turn_font_get.png</key>
            <dict>
                <key>frame</key>
                <string>{{437,953},{168,43}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,1},{168,43}}</string>
                <key>sourceSize</key>
                <string>{170,45}</string>
            </dict>
            <key>turn_font_title.png</key>
            <dict>
                <key>frame</key>
                <string>{{461,810},{263,60}}</string>
                <key>offset</key>
                <string>{-1,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{263,60}}</string>
                <key>sourceSize</key>
                <string>{265,60}</string>
            </dict>
            <key>turn_font_write.png</key>
            <dict>
                <key>frame</key>
                <string>{{746,393},{140,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{9,3},{140,36}}</string>
                <key>sourceSize</key>
                <string>{158,42}</string>
            </dict>
            <key>turn_go.png</key>
            <dict>
                <key>frame</key>
                <string>{{726,810},{74,40}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{6,3},{74,40}}</string>
                <key>sourceSize</key>
                <string>{86,46}</string>
            </dict>
            <key>turn_plate.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,525},{433,433}}</string>
                <key>offset</key>
                <string>{1,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{49,48},{433,433}}</string>
                <key>sourceSize</key>
                <string>{529,529}</string>
            </dict>
            <key>turn_point.png</key>
            <dict>
                <key>frame</key>
                <string>{{621,2},{151,183}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{151,183}}</string>
                <key>sourceSize</key>
                <string>{151,183}</string>
            </dict>
            <key>turn_progress.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,960},{419,27}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{419,27}}</string>
                <key>sourceSize</key>
                <string>{419,27}</string>
            </dict>
            <key>turn_row_bg.png</key>
            <dict>
                <key>frame</key>
                <string>{{527,2},{431,39}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{431,39}}</string>
                <key>sourceSize</key>
                <string>{431,39}</string>
            </dict>
            <key>turn_tab_normal.png</key>
            <dict>
                <key>frame</key>
                <string>{{735,187},{204,47}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{4,5},{204,47}}</string>
                <key>sourceSize</key>
                <string>{212,59}</string>
            </dict>
            <key>turn_tab_selected.png</key>
            <dict>
                <key>frame</key>
                <string>{{461,904},{204,47}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,5},{204,47}}</string>
                <key>sourceSize</key>
                <string>{212,59}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>turntable.png</string>
            <key>size</key>
            <string>{784,1003}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:6d91917245eaae57a3200508b0f14f77:376358ffed1aa7d920b4baa39f1119a8:f273d4777b785ae84f5eab11dfeed2ba$</string>
            <key>textureFileName</key>
            <string>turntable.png</string>
        </dict>
    </dict>
</plist>

-------------------------------------------------------------------------------
--  创世版1.0
--  房卡
--  @date 2017-06-03
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local LiveFrame = cs.app.client('frame.LiveFrame')
local cmd_common = cs.app.client('header.CMD_Common')


local FangkaLayer = class("FangkaLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function FangkaLayer:ctor()
    print('FangkaLayer:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('CardInfoLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commCloseHandler(self) )
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function FangkaLayer:onEnterTransitionFinish()
    print('FangkaLayer:onEnterTransitionFinish...')

    -- 房卡详情查询
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ROOM_CARD_INFO, self, self.onCardInfoResp)

	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ROOM_CARD_INFO)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function FangkaLayer:onExit()
    print('FangkaLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 房卡信息返回
-------------------------------------------------------------------------------
function FangkaLayer:onCardInfoResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.tagRoomCardInfo)
    if not ret then return false end

    self.main_node:child('label_fangka'):setString(ret.nRoomCard)
    self.main_node:child('label_diamond'):setString(ret.nDiamond)
    PassRoom:getInstance():getPlazaScene():updateFangka( ret.nRoomCard + ret.nDiamond )
end


return FangkaLayer
-------------------------------------------------------------------------------
--  创世版1.0
--  房间结算(大结算)
--  @date 2017-06-16
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local MultiPlatform = cs.app.client('external.MultiPlatform')


local RoomResultLayer = class("RoomResultLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomResultLayer:ctor(game_layer, cmd_table)
    print('RoomResultLayer:ctor...')
    self:enableNodeEvents()
    self.m_game_layer = game_layer
    self:setName('subRoomResultLayer')
    self:zorder(1)
    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomResultLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('template'):hide()

    main_node:child('btn_to_hall'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    main_node:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )

    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.dwRecordID and room_data.m_nContinue and room_data.dwRecordID > 0 and room_data.m_nContinue > 0 then
        main_node:child('btn_detail'):show():addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDetail) )
    else
        main_node:child('btn_detail'):hide()
    end

    self.copy_result = ''
    self:showResult(game_layer, cmd_table)
end


-------------------------------------------------------------------------------
-- 返回按钮
-------------------------------------------------------------------------------
function RoomResultLayer:onBtnBack(sender)
    GlobalUserItem.bWaitQuit = false
    -- 加保护
    if self.m_game_layer and not tolua.isnull(self.m_game_layer) and self.m_game_layer.onExitRoom then
        self.m_game_layer:onExitRoom()
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function RoomResultLayer:onBtnShare(sender)
    local param = {
        content = self.copy_result,
        toast = LANG.ROOM_RESULT_COPIED,
        icon = 'common/icon_share_copy_score.png'
    }
    -- setParam要在前
    helper.pop.shareImage():setParam(nil, nil, param):showButtons('hy,mowang,xianliao,copy')
end


-------------------------------------------------------------------------------
-- 详情按钮点击
-------------------------------------------------------------------------------
function RoomResultLayer:onBtnDetail(sender)
    helper.link.toGroupScore()
end


-------------------------------------------------------------------------------
-- 显示结果
--  game_layer: GameLayer
-------------------------------------------------------------------------------
function RoomResultLayer:showResult(game_layer, cmd_table)
    local main_node = self.main_node
    local template = main_node:child('template')
    local copy_result = LANG{'SHARE_APP_NAME', name = cs.app.APP_NAME}

    local game_config = cs.game[GlobalUserItem.nCurGameKind]
    if game_config and game_config.NAME_SHARE then
        copy_result = copy_result .. '\n' .. game_config.NAME_SHARE
    end

    local label_bill = main_node:child('label_bill')
    label_bill:setString( LANG{'SCORE_NOTE', base_score=cmd_table.dwBaseScore, bill_no=cmd_table.lBillNo} )

    --dump(cmd_table, '显示结果', 9)
    --房间号 
    if cmd_table.szRoomID then
        local room_str = LANG{'RESULT_ROOM', room = cmd_table.szRoomID }
        main_node:child('label_room'):setString(room_str)
        copy_result = copy_result .. '\n' .. room_str
    else
        main_node:child('label_room'):setString('')
    end
    --日期
    if cmd_table.dwTime then
        local date = helper.time.format(cmd_table.dwTime, '%m-%d %H:%M')
        local date_str = LANG{'RESULT_DATE', date = date }
        main_node:child('label_date'):setString(date_str)
        copy_result = copy_result .. '\n' .. date_str
    else
        main_node:child('label_date'):setString('')
    end
    local score_list = cmd_table.lScore[1]

    -- 大赢家
    local score_max = 0
    for i, v in ipairs(score_list) do
        score_max = math.max(score_max, v)
    end

    -- 日志
    --LogAsset:getInstance():logData( cjson.encode(score_list), true )

    local nPlayerCount = cmd_table.cbMaxPlayers
    if nPlayerCount <= 0 then
        local tabUserRecord = game_layer:getDetailScore()
        nPlayerCount = #tabUserRecord
    end

    local size = template:size()
    local gap = 25
    local start_x = display.width/2 + (nPlayerCount == 3 and -(size.width + gap) or -(nPlayerCount - 1) * 0.5 * (size.width + gap))
    for i = 1, nPlayerCount do
        local userItem = game_layer:getUserInfoByChairID(i - 1)
        if userItem then
            copy_result = copy_result .. '\n'
            local panel = template:clone():show():addTo(main_node)
            panel:px(start_x)
            start_x = start_x + size.width + gap

            -- 头像
            local panel_avator = panel:child('panel_avator')

            head = PopupHead:create(self, userItem, 80, 100)
            head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

            -- 月卡
            if helper.app.addAvatorCard(panel_avator, 0.62, nil, userItem.nMonthTicketType) then
                panel:child('bg_avator'):hide()
            end

            -- 昵称
            if userItem and userItem.szNickName then
               panel:child('label_name'):setString(userItem.szNickName)
               copy_result = copy_result .. userItem.szNickName
            end

        
            -- 玩家ID
            if userItem and userItem.dwUserID then
                panel:child('label_id'):setString( helper.str.formatUserID(userItem.dwUserID) )
                copy_result = copy_result .. '\tID:' .. helper.str.formatUserID(userItem.dwUserID)
            end

            -- 胡牌次数
            panel:child('label_hu'):setString( cmd_table.wHuTimes[1][i] )

            -- 最大番数
            panel:child('label_fan'):setString( cmd_table.dwMaxScore[1][i] )

            -- 最大胡型
            panel:child('label_great'):hide() -- 暂时屏蔽
            --panel:child('label_great'):setString( cmd_table.szMaxTurnDesc[1][i] )

            -- 总成绩
            local score_str = (score_list[i] >= 0 and '+' or '') .. score_list[i]
            panel:child('label_score'):setString(score_str)
            copy_result = copy_result .. '\t' .. LANG{'ROOM_SCORE', score = score_str}

            --房主标志
            if userItem and userItem.dwUserID then
                local is_fangzhu = userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID
                panel:child('img_fangzhu'):setVisible(is_fangzhu)
                if is_fangzhu then
                    copy_result = copy_result .. '\t' .. LANG.ROOM_OWNER
                end
            end
            --大赢家标志
            local is_winner = score_list[i] == score_max and score_list[i] > 0
            panel:child( is_winner and 'bg_normal' or 'bg_win' ):removeFromParent()
            if not is_winner then
                panel:child('img_winner'):removeFromParent()
            end
        else
            -- 没有找到玩家信息
            helper.pop.message( LANG{'NOT_FOUND_USER', chair=i} )
        end
    end

    self.copy_result = copy_result
end


return RoomResultLayer
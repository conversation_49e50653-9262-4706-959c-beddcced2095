#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import random
import urllib3
import ssl
import socket

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_ucpaas_connectivity():
    """测试云之讯服务器连通性"""
    print("=== 测试云之讯服务器连通性 ===")
    
    host = 'open.ucpaas.com'
    
    # 1. DNS解析测试
    try:
        import socket
        ip = socket.gethostbyname(host)
        print(f"✅ DNS解析成功: {host} -> {ip}")
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")
        return False
    
    # 2. 端口连通性测试
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, 443))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口443连通性正常")
        else:
            print(f"❌ 端口443连接失败")
            return False
    except Exception as e:
        print(f"❌ 端口测试异常: {e}")
        return False
    
    # 3. HTTP连通性测试
    try:
        response = requests.get(f'https://{host}', timeout=10, verify=False)
        print(f"✅ HTTPS连接成功，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ HTTPS连接失败: {e}")
        return False

def test_ucpaas_sms_multiple_methods():
    """使用多种方法测试云之讯短信接口"""
    
    print("\n=== 云之讯短信接口多方法测试 ===")
    
    # 配置信息
    accountsid = '5cbc351f17a418686094747a62ffd946'
    token = 'fac1c2af0c05327677d33916ba841079'
    appId = '9dc983c028e54d5fbc97228e6af5344e'
    templateId = '174333'
    phone = '***********'  # 请替换为真实手机号
    code = random.randint(1000, 9999)
    
    print(f"测试参数: 手机号={phone}, 验证码={code}")
    
    # 请求数据
    data = {
        'sid': accountsid,
        'token': token,
        'appid': appId,
        'templateid': templateId,
        'param': str(code),
        'mobile': phone,
        'uid': ''
    }
    
    # 方法1: 忽略SSL验证
    print("\n方法1: 忽略SSL验证")
    try:
        url = 'https://open.ucpaas.com/ol/sms/sendsms'
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'LHMJ-SMS-Test/1.0'
        }
        
        response = requests.post(
            url, 
            json=data, 
            headers=headers,
            timeout=30,
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法1成功！")
                return True
            else:
                print(f"⚠️ 方法1返回错误: {result.get('resp', {}).get('respCode')}")
        
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2: 使用HTTP而不是HTTPS
    print("\n方法2: 使用HTTP协议")
    try:
        url = 'http://open.ucpaas.com/ol/sms/sendsms'
        
        response = requests.post(
            url, 
            json=data, 
            headers=headers,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法2成功！")
                return True
        
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3: 使用不同的User-Agent
    print("\n方法3: 模拟浏览器请求")
    try:
        url = 'https://open.ucpaas.com/ol/sms/sendsms'
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        session = requests.Session()
        session.verify = False
        
        response = session.post(
            url, 
            json=data, 
            headers=headers,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法3成功！")
                return True
        
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    # 方法4: 使用POST表单数据而不是JSON
    print("\n方法4: 使用表单数据")
    try:
        url = 'https://open.ucpaas.com/ol/sms/sendsms'
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }
        
        response = requests.post(
            url, 
            data=data,  # 使用data而不是json
            headers=headers,
            timeout=30,
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resp', {}).get('respCode') == '000000':
                print("✅ 方法4成功！")
                return True
        
    except Exception as e:
        print(f"❌ 方法4失败: {e}")
    
    return False

def test_alternative_sms_providers():
    """测试其他短信服务商的可用性"""
    print("\n=== 测试其他短信服务商连通性 ===")
    
    providers = [
        ('阿里云短信', 'dysmsapi.aliyuncs.com'),
        ('腾讯云短信', 'sms.tencentcloudapi.com'),
        ('网易云信', 'api.netease.im'),
        ('容联云通讯', 'app.cloopen.com'),
    ]
    
    for name, host in providers:
        try:
            response = requests.get(f'https://{host}', timeout=5, verify=False)
            print(f"✅ {name} ({host}) - 连接正常")
        except Exception as e:
            print(f"❌ {name} ({host}) - 连接失败: {str(e)[:50]}...")

def analyze_user_php_code():
    """分析user.php中的短信实现"""
    print("\n=== 分析user.php中的短信实现 ===")
    
    print("从代码中发现的问题:")
    print("1. 云之讯配置:")
    print("   - AccountSID: 5cbc351f17a418686094747a62ffd946")
    print("   - Token: fac1c2af0c05327677d33916ba841079") 
    print("   - AppID: 9dc983c028e54d5fbc97228e6af5344e")
    print("   - TemplateID: 174333")
    print()
    print("2. 数据库操作:")
    print("   - 表名: tuo3_verify_code")
    print("   - 字段: role_id, game_id, create_time, phone, type, code")
    print("   - 验证码有效期: 600秒 (10分钟)")
    print()
    print("3. 可能的问题:")
    print("   - 云之讯账户可能已过期或余额不足")
    print("   - 短信模板可能未审核或已失效")
    print("   - 服务器IP可能不在云之讯白名单中")
    print("   - 网络连接问题导致无法访问云之讯API")

if __name__ == '__main__':
    print("开始云之讯短信服务详细测试...")
    
    # 1. 测试连通性
    if not test_ucpaas_connectivity():
        print("\n❌ 云之讯服务器连通性测试失败")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 防火墙阻止")
        print("3. DNS解析问题")
        print("4. 云之讯服务器故障")
    else:
        # 2. 测试短信接口
        success = test_ucpaas_sms_multiple_methods()
        
        if not success:
            print("\n❌ 所有方法都失败了")
        else:
            print("\n✅ 找到可用的方法！")
    
    # 3. 测试其他短信服务商
    test_alternative_sms_providers()
    
    # 4. 分析代码
    analyze_user_php_code()
    
    print("\n=== 最终建议 ===")
    print("1. 如果云之讯连通性正常但短信发送失败:")
    print("   - 检查账户余额和状态")
    print("   - 验证配置参数")
    print("   - 联系云之讯技术支持")
    print()
    print("2. 如果云之讯连通性异常:")
    print("   - 检查服务器网络设置")
    print("   - 考虑更换短信服务商")
    print("   - 使用代理服务器")
    print()
    print("3. 临时解决方案:")
    print("   - 暂时禁用短信验证功能")
    print("   - 使用其他登录方式（微信登录）")
    print("   - 集成其他短信服务商")

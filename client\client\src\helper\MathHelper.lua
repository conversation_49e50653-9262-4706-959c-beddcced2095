-------------------------------------------------------------------------------
--  创世版1.0
--  时间辅助方法类
--      访问方式：helper.time.
--  @date 2017-06-06
--  @auth woodoo
-------------------------------------------------------------------------------
local MathHelper = {}
helper = helper or {}
helper.math = MathHelper


-------------------------------------------------------------------------------
-- 取整：2017-09-05  
-------------------------------------------------------------------------------
function MathHelper.getIntPart(x)
    if x <= 0 then
       return math.ceil(x)
    end
    if math.ceil(x) == x then
       x = math.ceil(x)
    else
       x = math.ceil(x) - 1
    end
    return x
end
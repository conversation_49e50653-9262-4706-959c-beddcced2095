#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import time
import requests
import hashlib

def check_ports():
    """检查端口状态"""
    print("检查端口状态...")
    
    try:
        # 检查端口 80
        result80 = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
        port80_listening = False
        port443_listening = False
        
        for line in result80.stdout.split('\n'):
            if ':80 ' in line and 'LISTENING' in line:
                print(f"✅ 端口 80 正在监听: {line.strip()}")
                port80_listening = True
            elif ':443' in line and 'LISTENING' in line:
                print(f"✅ 端口 443 正在监听: {line.strip()}")
                port443_listening = True
        
        if not port80_listening:
            print("❌ 端口 80 没有在监听")
        if not port443_listening:
            print("❌ 端口 443 没有在监听")
            
        return port80_listening, port443_listening
        
    except Exception as e:
        print(f"检查端口时出错: {e}")
        return False, False

def test_apache_response():
    """测试 Apache 响应"""
    print("\n测试 Apache 响应...")
    
    test_urls = [
        'http://localhost/',
        'http://127.0.0.1/',
        'http://lhmj.tuo3.com.cn/',
    ]
    
    for url in test_urls:
        try:
            print(f"测试: {url}")
            response = requests.get(url, timeout=5)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ 连接成功")
                print(f"  响应长度: {len(response.text)} 字符")
                return True
            elif response.status_code == 502:
                print(f"  ❌ 502 Bad Gateway")
            else:
                print(f"  ⚠️ 状态码: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接被拒绝")
        except Exception as e:
            print(f"  ❌ 错误: {e}")
    
    return False

def test_sms_api():
    """测试短信 API"""
    print("\n测试短信验证接口...")
    
    # 准备参数
    params = {
        'uid': '123456',
        'phone': '13800138000',
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sorted_keys = sorted(params.keys())
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    sign_str = param_str + channel_key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    params['sign'] = sign
    
    # 测试 API
    url = 'http://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code'
    
    try:
        print(f"请求: {url}")
        response = requests.post(url, data=params, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                if json_data.get('code') == 0:
                    print("🎉 短信验证接口恢复正常！")
                    return True
                else:
                    print(f"⚠️ API 错误: {json_data.get('msg')}")
                    print("💡 可能是短信服务商配置问题，但服务器已正常响应")
                    return True
            except:
                print("⚠️ 非JSON响应，但服务器已响应")
                return True
        elif response.status_code == 502:
            print("❌ 仍然是 502 错误")
            return False
        else:
            print(f"⚠️ 其他状态码: {response.status_code}")
            return True
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    print("=== Apache 重启后测试 ===")
    print("请确保您已经在 XAMPP 控制面板中重启了 Apache")
    print()
    
    # 等待几秒让 Apache 完全启动
    print("等待 Apache 启动...")
    time.sleep(3)
    
    # 检查端口
    port80, port443 = check_ports()
    
    if not port80 and not port443:
        print("\n❌ Apache 可能没有成功启动")
        print("💡 建议:")
        print("1. 检查 XAMPP 控制面板中 Apache 状态")
        print("2. 查看 Apache 错误日志")
        print("3. 以管理员身份运行 XAMPP")
        return
    
    # 测试基本响应
    if test_apache_response():
        print("\n✅ Apache 基本功能正常")
        
        # 测试短信 API
        if test_sms_api():
            print("\n🎉 所有测试通过！短信验证功能已恢复！")
        else:
            print("\n⚠️ 短信 API 仍有问题，但 Apache 已正常运行")
    else:
        print("\n❌ Apache 响应仍有问题")

if __name__ == '__main__':
    main()

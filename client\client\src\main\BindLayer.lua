-------------------------------------------------------------------------------
--  创世版1.0
--  绑定
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local BindLayer = class("BindLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function BindLayer:ctor()
    print('BindLayer:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('BindLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self)

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
    main_node:child('btn_question'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnQuestion) )
    
    self:updateDesc()

    self:showNums('')
    helper.logic.initKeyboard( main_node:child('keyboard'), handler(self, self.showNums), 10 )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function BindLayer:onExit()
    print('BindLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 更新绑定描述
-------------------------------------------------------------------------------
function BindLayer:updateDesc()
    if GlobalUserItem.dwSpreader == 0 then
        self.main_node:child('label_desc'):setString( LANG{'BIND_FANGKA', num=(cs.app.BIND_FANGKA_NUM or 10)} )
    else
        local id = GlobalUserItem.dwSpreader
        local name = GlobalUserItem.szSpreaderName
        self.main_node:child('label_desc'):setString( LANG{'BIND_ALREADY', id=id, name=name} )
    end
end


-------------------------------------------------------------------------------
-- 显示数值
-------------------------------------------------------------------------------
function BindLayer:showNums(s)
    self.m_ruid = s
    self.main_node:child('label_show'):setString(s)
end


-------------------------------------------------------------------------------
-- 帮助按钮点击
-------------------------------------------------------------------------------
function BindLayer:onBtnQuestion(sender)
    helper.pop.alert( LANG{'BIND_HELP', value=cs.app.WEIXIN_GONGZHONG} )
end


-------------------------------------------------------------------------------
-- 绑定按钮点击
-------------------------------------------------------------------------------
function BindLayer:onBtnOk(sender)
    if not self.m_ruid or self.m_ruid == '' then
        helper.pop.message( LANG.BIND_RUID_EMPTY )
        return
    end

    helper.pop.waiting()

    local uid =  GlobalUserItem.dwUserID
    local ruid = self.m_ruid
    yl.GetUrl(yl.URL_BIND, 'post', {uid=uid, ruid=ruid}, handler(self, self.onBindResponse) )
end


-------------------------------------------------------------------------------
-- 绑定返回
-------------------------------------------------------------------------------
function BindLayer:onBindResponse(data, response, http_status)
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    if data.ruid and data.ruid > 0 then -- 推荐人
        GlobalUserItem.dwSpreader = tonumber(data.ruid)
    end
    if data.rname then -- 推荐人名称
        GlobalUserItem.szSpreaderName = data.rname
    end
    self:updateDesc()

    if data.insure and data.insure > 0 then
        PassRoom:getInstance():getPlazaScene():updateFangka( data.insure )  -- 通知主界面刷新
    end
    PassRoom:getInstance():getPlazaScene():setRecommendBind()
    helper.pop.message( LANG.BIND_SUCCESS )
    
    -- 不能修改绑定的，直接关闭界面
    if not cs.app.CAN_CHANGE_BIND then
        self:removeFromParent()
    end
end


return BindLayer
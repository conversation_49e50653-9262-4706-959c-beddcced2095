local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')
local GameViewLayer = cs.app.game('room.GameViewLayer')
local ExternalFun =  cs.app.client('external.ExternalFun')
local GameModel = cs.app.client('system.GameModel')
local GameLayer = class("GameLayer", GameModel)


function GameLayer:ctor(frameEngine, scene)
    GameLayer.super.ctor(self, frameEngine, scene)
end


function GameLayer:CreateView()
    return GameViewLayer:create(self):addTo(self)
end


function GameLayer:OnInitGameEngine()
	self.lCellScore = 0
	self.cbTimeOutCard = 0
	self.cbTimeOperateCard = 0
	self.cbTimeStartGame = 0
	self.wCurrentUser = yl.INVALID_CHAIR
	self.wBankerUser = yl.INVALID_CHAIR
	self.cbPlayStatus = {0, 0, 0, 0}
	self.cbGender = {0, 0, 0, 0}
    self.wPeiyin = {0, 0, 0, 0}
	self.bTrustee = false
	self.nGameSceneLimit = 0
	self.cbAppearCardData = {} 		--已出现的牌
	self.bMoPaiStatus = false
	self.cbListenPromptOutCard = {}
	self.cbListenCardList = {}
	self.cbActionMask = nil
	self.bSendCardFinsh = false
	self.cbPlayerCount = 4
	self.lDetailScore = {}
	self.m_userRecord = {}
	self.cbMaCount = 0
	--房卡需要
	self.wRoomHostViewId = 0
    self.bIsShowCardInfo = false
    self.bIsShowTing = false
    --牌的信息
    self.cardsDataInfo = {} 
    --听牌信息
    self.cardsTingInfo = {} 
    --日志等级
    self.userLogLevel = 0
    self.userLog = ''
	--print("Hello Hello!")
end

function GameLayer:OnResetGameEngine()
    GameLayer.super.OnResetGameEngine(self)
    self._gameView:onResetData()
	self.nGameSceneLimit = 0
	self.bTrustee = false
	self.cbAppearCardData = {} 		--已出现的牌
	self.bMoPaiStatus = false
	self.cbActionMask = nil
end

--获取gamekind
function GameLayer:getGameKind()
    return cmd.KIND_ID
end

function GameLayer:onExitRoom()
    self:startOrStopReqLocation( false )
    self:startOrStopHeartBeat( false )
    self:startOrStopReplay( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        --print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end 
    end
    self:removeFromParent()
end

-- 椅子号转视图位置,注意椅子号从0~nChairCount-1,返回的视图位置从1~nChairCount
function GameLayer:SwitchViewChairID( chair )
    if isCheckFix3 == nil then isCheckFix3 = true end 
    local viewid = yl.INVALID_CHAIR
    local nChairCount = self._gameFrame:GetChairCount()
    local nChairID = self:GetMeChairID()
    if nChairID == yl.INVALID_CHAIR then
        nChairID = 0
    end
    if chair ~= yl.INVALID_CHAIR and chair < nChairCount then
        viewid = math.mod(chair + math.floor(cmd.GAME_PLAYER * 3/2) - nChairID, cmd.GAME_PLAYER) + 1
        if nChairCount == 2 and viewid ~= 3 then        -- 两人时对方总在1号位
            viewid = 1
        elseif nChairCount == 3 and viewid == 1 then    -- 三人时1号位总空着
            if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false and isCheckFix3 then
                if nChairID == 0 then
                    viewid = 2
                elseif nChairID == 2 then
                    viewid = 4
                end
            end            
        end
    end
    return viewid
end

function GameLayer:getRoomHostViewId()
	return self.wRoomHostViewId
end

function GameLayer:getUserInfoByChairID(chairId)
	local viewId = self:SwitchViewChairID(chairId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getUserInfoByViewID(viewId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getMaCount()
	--print("返回码数", self.cbMaCount)
	return self.cbMaCount
end

function GameLayer:onGetSitUserNum()
	local num = 0
	for i = 1, cmd.GAME_PLAYER do
		if nil ~= self._gameView.m_sparrowUserItem[i] then
			num = num + 1
		end
	end

    return num
end

function GameLayer:onEnterTransitionFinish()
    --self._scene:createVoiceBtn(cc.p(1250, 300))
    GameLayer.super.onEnterTransitionFinish(self)
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair, time, clockId)
    -- body
    if GlobalUserItem.bPrivateRoom then
    	return
    end

    local commonProcess = function(time)
        if not self.bTrustee then
            if time <= 0 then
                self:sendUserTrustee(true)
            elseif time <= 5 then
    			self:PlaySound(cmd.RES_PATH.."sound/GAME_WARN.wav")
    		end
        end
    end

    local meChairId = self:GetMeChairID()
    if clockId == cmd.IDI_START_GAME then
    	--托管
    	if self.bTrustee and self._gameView.btStart:isVisible() then
   --  		print("进去")
   --  		self._gameView:onButtonClickedEvent(GameViewLayer.BT_START)
   --  		--托管在上个函数被复原了，在下面重开
			-- self.bTrustee = true
			-- self._gameView.nodePlayer[cmd.MY_VIEWID]:getChildByTag(GameViewLayer.SP_TRUSTEE):setVisible(true)
			-- self._gameView.spTrusteeCover:setVisible(true)
    	end
    	--超时
		if time <= 0 then
			self._gameFrame:setEnterAntiCheatRoom(false)--退出防作弊，如果有的话
			--self:onExitTable()
		elseif time <= 5 then
    		self:PlaySound(cmd.RES_PATH.."sound/GAME_WARN.wav")
		end
    elseif clockId == cmd.IDI_OUT_CARD then
    	if chair == meChairId then
            --[[
    		--托管
    		if self.bTrustee then
				--self._gameView._cardLayer:outCardAuto()
    		end
    		--超时
    		if time <= 0 then
				self._gameView._cardLayer:outCardAuto()
    		elseif time <= 5 then
    			self:PlaySound(cmd.RES_PATH.."sound/GAME_WARN.wav")
    		end
            --]]
            commonProcess(time)
    	end
    elseif clockId == cmd.IDI_OPERATE_CARD then
    	if chair == meChairId then
            --[[
    		--托管
    		if self.bTrustee then
    -- 			if self._gameView._cardLayer:isUserMustWin() then
				-- 	self._gameView:onButtonClickedEvent(GameViewLayer.BT_WIN)
    -- 			end
				-- self._gameView:onButtonClickedEvent(GameViewLayer.BT_PASS)
				-- self._gameView._cardLayer:outCardAuto()
    		end
    		--超时
    		if time <= 0 then
    			if self._gameView._cardLayer:isUserMustWin() then
					self._gameView:onButtonClickedEvent(GameViewLayer.BT_WIN)
    			end
				self._gameView:onButtonClickedEvent(GameViewLayer.BT_PASS)
				self._gameView._cardLayer:outCardAuto()
    		elseif time <= 5 then
    			self:PlaySound(cmd.RES_PATH.."sound/GAME_WARN.wav")
    		end
            --]]
            commonProcess(time)
    	end
    end
end

--用户洗牌
function GameLayer:onUserShuffle(player_id, player_name)
    if player_id == GlobalUserItem.dwUserID then -- 如果是自己，通知结果界面成功
        local game_result = helper.app.getFromScene('subGameResultLayer')
        if game_result then
            game_result:onShuffleSucc()
        end
    end
    self._gameView:onUserShuffle(player_name)
end

--洗牌卡数量通知
function GameLayer:onUserShuffleCard(num)
    self.m_shuffle_card = num
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 语音播放开始
function GameLayer:onUserVoiceStart( useritem, filepath )
    local view_id = self:SwitchViewChairID(useritem.wChairID)
    self._gameView:onUserVoiceStart(view_id)
    return view_id
end

-- 语音播放结束
function GameLayer:onUserVoiceEnded( view_id, filepath )
    self._gameView:onUserVoiceEnded(view_id)
end

-- 场景消息
function GameLayer:onEventGameScene(cbGameStatus, dataBuffer)
    self:changeGameStatus( cbGameStatus )
	self.nGameSceneLimit = self.nGameSceneLimit + 1
	if self.nGameSceneLimit > 1 then
		--限制只进入场景消息一次
		return false
	end
	local wTableId = self:GetMeTableID()
	local wMyChairId = self:GetMeChairID()
	self._gameView:setRoomInfo(wTableId, wMyChairId)
    self.m_wMyChairId = wMyChairId
	--初始化用户信息
	for i = 1, cmd.GAME_PLAYER do
		local wViewChairId = self:SwitchViewChairID(i - 1)
        if wViewChairId ~= yl.INVALID_CHAIR then
		    local userItem = self._gameFrame:getTableUserItem(wTableId, i - 1)
		    self._gameView:OnUpdateUser(wViewChairId, userItem)
		    if userItem then
			    self.cbGender[wViewChairId] = userItem.cbGender
                self.wPeiyin[wViewChairId] = userItem.wPeiyin
			    if PassRoom and GlobalUserItem.bPrivateRoom then
				    if userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID then
					    self.wRoomHostViewId = wViewChairId
				    end
			    end
		    end
        end
	end

	if cbGameStatus == cmd.GAME_SCENE_FREE then
		--print("空闲状态")
		local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_StatusFree, dataBuffer)
		--dump(cmd_data, "空闲状态")

		self.lCellScore = cmd_data.lCellScore
		self.cbTimeOutCard = cmd_data.cbTimeOutCard
		self.cbTimeOperateCard = cmd_data.cbTimeOperateCard
		self.cbTimeStartGame = cmd_data.cbTimeStartGame
		self.cbPlayerCount = cmd_data.cbPlayerCount or 4
		self.cbMaCount = cmd_data.cbMaCount
		
        local userItem = self._gameFrame:getTableUserItem(wTableId, wMyChairId)
        if userItem and userItem.cbUserStatus < yl.US_READY then
		    self._gameView.btStart:show()

            local room_data = PassRoom:getInstance().m_tabPriData
            local is_club = room_data and room_data.dwClubID and room_data.dwClubID > 0

            if not is_club and (not GlobalUserItem.bPrivateRoom or wMyChairId == 0) then -- 不是俱乐部，金币场全自动，私人房房主
		        self._gameView.btStart:hide()
                self:sendGameStart()
            end
        end

		-- if GlobalUserItem.bPrivateRoom then
		-- 	--self._gameView.spClock:setVisible(false)
		-- 	self._gameView.asLabTime:setString("0")
		-- else
			self:SetGameClock(wMyChairId, cmd.IDI_START_GAME, self.cbTimeStartGame)
		--end

	elseif cbGameStatus == cmd.GAME_SCENE_PLAY then
        self._gameView.spClock:setVisible(true)
		--print("游戏状态")
		local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_StatusPlay, dataBuffer)
		--dump(cmd_data, "游戏状态", 9)
		--dump(cmd_data.cbOutCardDataEx, "cbOutCardDataEx")
        self.userLogLevel = cmd_data.cbUserLogLevel
        self.bIsShowCardInfo = false
        self.bIsShowTing = false 
        self:updateCardInfo( false, 0 )
		self.lCellScore = cmd_data.lCellScore
		self.cbTimeOutCard = cmd_data.cbTimeOutCard
		self.cbTimeOperateCard = cmd_data.cbTimeOperateCard
		self.cbTimeStartGame = cmd_data.cbTimeStartGame
		self.wCurrentUser = cmd_data.wCurrentUser
		self.wBankerUser = cmd_data.wBankerUser
		self.cbPlayerCount = cmd_data.cbPlayerCount or 4
		self.cbMaCount = cmd_data.cbMaCount
        --生牌数量
        self._gameView._priView.m_cbShengPaiNum = cmd_data.cbShengPaiNum
        --财牌替换
        self._gameView.bIsCaiOperate = cmd_data.bIsCaiOperate
        -- 最大手牌数
        self._gameView._cardLayer.cbPlayerMaxCardsNum = math.max(cmd_data.cbPlayerMaxCardsNum, 14)
        -- 最大吃碰杠数
        self._gameView._cardLayer.cbPlayerMaxWeaveNum = math.max(cmd_data.cbPlayerMaxWeaveNum, 4)
        -- 玩家位置
        self.cbPlayerPos = cmd_data.cbPlayerPos[1]
        -- 财神
        self:setCaishen( cmd_data.cbMagicIndex, cmd_data.cbMagicIndexCount, cmd_data.cbMagicIndexArray[1] )        
        -- 替换的牌.
        self:setReplaceCard(cmd_data.cbReplaceIndex)

        GameLogic.setSwitchCardData(GameLogic.MAGIC_DATA, GameLogic.REPLACE_DATA)
        -- 设置 痞子牌
        self:setPiCard( cmd_data.bCurPiMak[1], cmd_data.cbShowPiCardArray[1] )    
		--庄家
		self._gameView:setBanker( self:SwitchViewChairID(self.wBankerUser), cmd_data.cbLianZhuang, self.cbPlayerPos )
        --花牌标记
        self._gameView._cardLayer.bCurHuaMak = cmd_data.bCurHuaMak[1]
        self.bCurHuaMak = cmd_data.bCurHuaMak[1]
        --圈风
        PassRoom:getInstance().m_tabPriData.cbCurPlayRoundIndex = cmd_data.cbCurPlayRoundIndex
		--设置手牌
		local viewCardCount = {}
		for i = 1, cmd.GAME_PLAYER do
			local viewId = self:SwitchViewChairID(i - 1)
            if viewId ~= yl.INVALID_CHAIR then
			    viewCardCount[viewId] = cmd_data.cbCardCount[1][i]
			    if viewCardCount[viewId] > 0 then
				    self.cbPlayStatus[viewId] = 1
			    end
            end
		end
		local cbHandCardData = {}
		for i = 1, cmd.MAX_COUNT do
			local data = cmd_data.cbCardData[1][i]
			if data > 0 then 				--去掉末尾的0
				cbHandCardData[i] = data
			else
				break
			end
		end
		GameLogic.SortCardList(cbHandCardData) 		--排序
		local cbSendCard = cmd_data.cbSendCardData
		if cbSendCard > 0 and self.wCurrentUser == wMyChairId then
			for i = 1, #cbHandCardData do
				if cbHandCardData[i] == cbSendCard then
					table.remove(cbHandCardData, i)				--把刚抓的牌放在最后
					break
				end
			end
			table.insert(cbHandCardData, cbSendCard)
		end
		for i = 1, cmd.GAME_PLAYER do
            if viewCardCount[i] then    -- 非最大人数局时，可能不存在
			    self._gameView._cardLayer:setHandCard(i, viewCardCount[i], cbHandCardData)
            end
		end
		self.bSendCardFinsh = true
		--记录已出现牌
		self:insertAppearCard(cbHandCardData)
		--组合牌
		for i = 1, cmd.GAME_PLAYER do
			local wViewChairId = self:SwitchViewChairID(i - 1)
            if wViewChairId ~= yl.INVALID_CHAIR then
			    for j = 1, cmd_data.cbWeaveItemCount[1][i] do
				    local cbOperateData = {}
				    for v = 1, 4 do
					    local data = cmd_data.WeaveItemArray[i][j].cbCardData[1][v]
					    if data > 0 then
						    table.insert(cbOperateData, data)
					    end
				    end
				    local nShowStatus = GameLogic.SHOW_NULL
				    local cbParam = cmd_data.WeaveItemArray[i][j].cbParam
				    if cbParam == GameLogic.WIK_GANERAL then
					    if cbOperateData[1] == cbOperateData[2] then 	--碰
						    nShowStatus = GameLogic.SHOW_PENG
					    else 											--吃
						    nShowStatus = GameLogic.SHOW_CHI
					    end
				    elseif cbParam == GameLogic.WIK_MING_GANG then
					    nShowStatus = GameLogic.SHOW_MING_GANG
				    elseif cbParam == GameLogic.WIK_FANG_GANG then
					    nShowStatus = GameLogic.SHOW_FANG_GANG
				    elseif cbParam == GameLogic.WIK_AN_GANG then
					    nShowStatus = GameLogic.SHOW_AN_GANG
				    end
				    --dump(cmd_data.WeaveItemArray[i][j], "weaveItem")
                    local provider_view_id = self:SwitchViewChairID(cmd_data.WeaveItemArray[i][j].wProvideUser)
				    self._gameView._cardLayer:bumpOrBridgeCard(
                        wViewChairId, 
                        cbOperateData, 
                        nShowStatus, 
                        cmd_data.WeaveItemArray[i][j].cbCenterCard, 
                        provider_view_id, 
                        cbParam,
                        nil )
				    --记录已出现牌
				    self:insertAppearCard(cbOperateData)
			    end
            end
		end

		--设置牌堆
		local wViewHeapHead = self:SwitchViewChairID(cmd_data.wHeapHead)
		local wViewHeapTail = self:SwitchViewChairID(cmd_data.wHeapTail)
		for i = 1, cmd.GAME_PLAYER do
			local viewId = self:SwitchViewChairID(i - 1)
            if viewId ~= yl.INVALID_CHAIR then
			    for j = 1, cmd_data.cbDiscardCount[1][i] do
				    --已出的牌
				    self._gameView._cardLayer:discard(viewId, cmd_data.cbDiscardCard[i][j], true)   -- true不显示补花特效
				    --记录已出现牌
				    local cbAppearCard = {cmd_data.cbDiscardCard[i][j]}
				    self:insertAppearCard(cbAppearCard)
			    end
			    --牌堆
			    self._gameView._cardLayer:setTableCardByHeapInfo(viewId, cmd_data.cbHeapCardInfo[i], wViewHeapHead, wViewHeapTail)
			    --托管
			    self._gameView:setUserTrustee(viewId, cmd_data.bTrustee[1][i])
			    if viewId == cmd.MY_VIEWID then
				    self.bTrustee = cmd_data.bTrustee[1][i]
			    end
            end
		end
        
        self._gameView._cardLayer:updateRemainCardNum( cmd_data.cbLeftCardCount )
        --
		self._gameView._priView:setCunNum( cmd_data.iCurCun )
        --刚出的牌
		if cmd_data.cbOutCardData and cmd_data.cbOutCardData > 0 then
			local wOutUserViewId = self:SwitchViewChairID(cmd_data.wOutCardUser)
			self._gameView:showCardPlate(wOutUserViewId, cmd_data.cbOutCardData)
	        self._gameView.cbOutCardTemp = cmd_data.cbOutCardData
	        self._gameView.cbOutUserTemp = wOutUserViewId
		end
		--计时器
		self:SetGameClock(self.wCurrentUser, cmd.IDI_OUT_CARD, self.cbTimeOutCard)

        -- 显示为进行中状态
        self._gameView:changeToPlaying()

		--提示听牌数据
		self.cbListenPromptOutCard = {}
		self.cbListenCardList = {}
		for i = 1, cmd_data.cbOutCardCount do
			self.cbListenPromptOutCard[i] = cmd_data.cbOutCardDataEx[1][i]
			self.cbListenCardList[i] = {}
			for j = 1, cmd_data.cbHuCardCount[1][i] do
				self.cbListenCardList[i][j] = cmd_data.cbHuCardData[i][j]
			end
		end
		local cbPromptHuCard = self:getListenPromptHuCard(cmd_data.cbOutCardData)
		self._gameView:setListeningCard(cbPromptHuCard)
		--提示操作
		self._gameView:recognizecbActionMask(cmd_data.cbActionMask, cmd_data.cbActionCard, cmd_data.cbGangCards[1])
		if self.wCurrentUser == wMyChairId then
			self._gameView._cardLayer:promptListenOutCard(self.cbListenPromptOutCard)
		end
        PassRoom:getInstance().m_tabPriData.dwPlayCount = cmd_data.cbCurPlayCount
	else
		--print("\ndefault\n")
		return false
	end

    -- 启动 心跳
    self:startOrStopHeartBeat( true )
    self:startOrStopReqLocation( true )
    helper.pop.waiting({false, 'reconect'})

	self:dismissPopWait()

    self:updateCardInfo(false, 0)

	return true
end

-- 游戏消息
function GameLayer:onEventGameMessage(sub, dataBuffer)
    -- body
	if sub == cmd.SUB_S_GAME_START then 					--游戏开始
		return self:onSubGameStart(dataBuffer)
	elseif sub == cmd.SUB_S_OUT_CARD then 					--用户出牌
		return self:onSubOutCard(dataBuffer)
	elseif sub == cmd.SUB_S_SEND_CARD then 					--发送扑克
		return self:onSubSendCard(dataBuffer)
	elseif sub == cmd.SUB_S_OPERATE_NOTIFY then 			--操作提示
		return self:onSubOperateNotify(dataBuffer)
	elseif sub == cmd.SUB_S_HU_CARD then 					--听牌提示
		return self:onSubListenNotify(dataBuffer)
	elseif sub == cmd.SUB_S_OPERATE_RESULT then 			--操作命令
		return self:onSubOperateResult(dataBuffer)
	elseif sub == cmd.SUB_S_LISTEN_CARD then 				--用户听牌
		return self:onSubListenCard(dataBuffer)
	elseif sub == cmd.SUB_S_TRUSTEE then 					--用户托管
		return self:onSubTrustee(dataBuffer)
	elseif sub == cmd.SUB_S_GAME_CONCLUDE then 				--游戏结束
		return self:onSubGameConclude(dataBuffer)
	elseif sub == cmd.SUB_S_RECORD then 					--游戏记录
		return self:onSubGameRecord(dataBuffer)
	elseif sub == cmd.SUB_S_SET_BASESCORE then 				--设置基数
		self.lCellScore = dataBuffer:readint()
		return true
    elseif sub == cmd.SUB_S_UPDATE_OTHER_SCORE then         --刷新其他积分
        return self:onUpdateOtherScore(dataBuffer)
    elseif sub == cmd.SUB_S_TING then                       --听牌提示
        return self:onUpdateTingCardInfo(dataBuffer)                                         
    elseif sub == cmd.SUB_S_CARD_INFO then                  --牌的信息
        return self:onUpdateCardInfo(dataBuffer)
    elseif sub == cmd.SUB_S_CONTROL_CARD_NOTIFY then        --控制出牌提示
        return self:onControlCardsNotify(dataBuffer)
    elseif sub == cmd.SUB_S_SHOW_EFFECT then                --显示效果
        return self:onEffectNotify(dataBuffer)
	else
		assert(false, "default onEventGameMessage".. sub )
	end

	return false
end

--游戏开始
function GameLayer:onSubGameStart(dataBuffer)
    self._gameView.spClock:setVisible(true)
    --print("游戏开始")
    self.m_wMyChairId = self:GetMeChairID()
    self._gameView:initPlayers()
    self:changeGameStatus( cmd.GAME_SCENE_PLAY )
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameStart, dataBuffer)
    self.userLogLevel = cmd_data.cbUserLogLevel
    self.bIsShowCardInfo = false
    self.bIsShowTing = false 
    self:updateCardInfo( false, 0 )
    self.wBankerUser = cmd_data.wBankerUser
    -- 最大手牌数
    self._gameView._cardLayer.cbPlayerMaxCardsNum = math.max(cmd_data.cbPlayerMaxCardsNum, 14)
    -- 最大吃碰杠数
    self._gameView._cardLayer.cbPlayerMaxWeaveNum = math.max(cmd_data.cbPlayerMaxWeaveNum, 4)     
    -- 玩家位置
    self.cbPlayerPos = cmd_data.cbPlayerPos[1]
    local wViewBankerUser = self:SwitchViewChairID(self.wBankerUser)
    self._gameView:setBanker( wViewBankerUser, cmd_data.cbLianZhuang, self.cbPlayerPos )
	-- 显示财神
    self:setCaishen( cmd_data.cbMagicIndex, cmd_data.cbMagicIndexCount, cmd_data.cbMagicIndexArray[1] )
    -- 替换的牌.
    self:setReplaceCard(cmd_data.cbReplaceIndex)
    GameLogic.setSwitchCardData(GameLogic.MAGIC_DATA, GameLogic.REPLACE_DATA)
    -- 设置 痞子牌
    self:setPiCard( cmd_data.bCurPiMak[1], cmd_data.cbShowPiCardArray[1] )
    --花牌标记
    self._gameView._cardLayer.bCurHuaMak = cmd_data.bCurHuaMak[1]
    self.bCurHuaMak = cmd_data.bCurHuaMak[1]
    --生牌数量
    self._gameView._priView.m_cbShengPaiNum = cmd_data.cbShengPaiNum
    --财牌替换
    self._gameView.bIsCaiOperate = cmd_data.bIsCaiOperate
    --圈风
    PassRoom:getInstance().m_tabPriData.cbCurPlayRoundIndex = cmd_data.cbCurPlayRoundIndex
    local cbCardCount = {0, 0, 0, 0}
	for i = 1, cmd.GAME_PLAYER do
		local userItem = self._gameFrame:getTableUserItem(self:GetMeTableID(), i - 1)
        --print( "玩家:", userItem.wChairID, userItem.szNickName )
		local wViewID = self:SwitchViewChairID(i - 1)
        if wViewID ~= yl.INVALID_CHAIR then
		    self._gameView:OnUpdateUser(wViewID, userItem)
		    if userItem then
			    self.cbPlayStatus[wViewID] = 1
			    cbCardCount[wViewID] = self._gameView._cardLayer.cbPlayerMaxCardsNum - 1
		        local num = #cmd_data.cbCardData[i]
                for j = 1, num  do
                    if j > cbCardCount[wViewID] then
                        cmd_data.cbCardData[i][j] = nil
                    end
                end
            end
        end
	end
    
    local index = 1
	--开始发牌
    if yl.IS_REPLAY_MODEL then
       index = self:GetMeChairID() + 1
    end
    self._gameView:gameStart( cmd_data, cmd_data.cbCardData[index], cbCardCount )

	--记录已出现的牌
    if not yl.IS_REPLAY_MODEL then
        self:insertAppearCard( cmd_data.cbCardData[1] )
    else
        local tempCard = {}
        for i = 1, cmd.GAME_PLAYER do
            local chairID = i - 1
            local view = self:SwitchViewChairID( chairID )
            if view ~= yl.INVALID_CHAIR then
               tempCard[view] = cmd_data.cbCardData[i]
            end
        end
        self._gameView:setAllCardData( tempCard )
    end
	self.wCurrentUser = cmd_data.wBankerUser
	self.cbActionMask = cmd_data.cbUserAction
    self.cbActionCard = cmd_data.cbActionCard
    self.cbGangCards = cmd_data.cbGangCards
	self.bMoPaiStatus = true
	self.bSendCardFinsh = false
	self:PlaySound(cmd.RES_PATH.."sound/GAME_START.wav")
	-- 刷新房卡
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            --刷新局数
            PassRoom:getInstance().m_tabPriData.dwPlayCount = cmd_data.cbCurPlayCount
            self._gameView._priView:onRefreshInfo()
        end
    end

    for i = 1, cmd.GAME_PLAYER do
		local viewId = self:SwitchViewChairID(i - 1)
        if viewId ~= yl.INVALID_CHAIR then
            --dump(cmd_data.cbDiscardCard, 'gameStart', 9)
			for j = 1, cmd_data.cbDiscardCount[1][i] do
				--已出的牌
				self._gameView._cardLayer:discard(viewId, cmd_data.cbDiscardCard[i][j])
				--记录已出现牌
				local cbAppearCard = {cmd_data.cbDiscardCard[i][j]}
				self:insertAppearCard(cbAppearCard)
			end
        end
	end
    
    self._gameView._priView:setCunNum( cmd_data.iCurCun )
    
    --计时器
	self:SetGameClock(self.wCurrentUser, cmd.IDI_OUT_CARD, self.cbTimeOutCard)

    -- 显示为进行中状态
    self._gameView:changeToPlaying()

	return true
end

function GameLayer:setReplaceCard( cbReplaceIndex )
    GameLogic.REPLACE_INDEX = cbReplaceIndex + 1
    GameLogic.REPLACE_DATA = GameLogic.SwitchToCardData( GameLogic.REPLACE_INDEX )
end

function GameLayer:setCaishen(cbMagicIndex, cbMagicCount, cbMagicIndexArray)
    GameLogic.MAGIC_INDEX = cbMagicIndex + 1
    GameLogic.MAGIC_DATA = GameLogic.SwitchToCardData( GameLogic.MAGIC_INDEX )
    if cbMagicIndexArray then
        GameLogic.MAGIC_DATA_ARRAY 	= cbMagicIndexArray   -- 多个财神时的补充
    end
    if cbMagicCount then
        GameLogic.MAGIC_INDEX_COUNT = cbMagicCount    -- 多个财神时的补充
    end
    self._gameView.m_caishen = GameLogic.MAGIC_DATA 
    -- print( '多个财神时的补充', GameLogic.MAGIC_DATA )
    if nil ~= self._gameView._priView and nil ~= self._gameView._priView.setCaishen then
        -- 多财神 和 单财神的时候
        if cbMagicCount == 2 then
            self._gameView._priView:setCaishen( cbMagicIndexArray )
        else
            self._gameView._priView:setCaishen( self._gameView.m_caishen )
        end
    end
end

function GameLayer:setPiCard( cbPiCards, cbShowPiCards )
    GameLogic.setPiCard( cbPiCards )
    self._gameView._priView:setPiCard( cbShowPiCards )
end

--用户出牌
function GameLayer:onSubOutCard(dataBuffer)
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_OutCard, dataBuffer)
	--dump(cmd_data, "CMD_S_OutCard")
	--print("用户出牌", cmd_data.cbOutCardData)
    local wViewId = self:SwitchViewChairID(cmd_data.wOutCardUser)
    self._gameView._cardLayer:removeHandCard(wViewId, {cmd_data.cbOutCardData}, true)
						
	self._gameView:gameOutCard(wViewId, cmd_data.cbOutCardData)

	--记录已出现的牌
	if wViewId ~= cmd.MY_VIEWID then
		local cbAppearCard = {cmd_data.cbOutCardData}
		self:insertAppearCard(cbAppearCard)
	end

	self.bMoPaiStatus = false
	self:KillGameClock()
	self._gameView:HideGameBtn()
	self:PlaySound(cmd.RES_PATH.."sound/OUT_CARD.wav")
	self:playCardDataSound(wViewId, cmd_data.cbOutCardData)
	--轮到下一个
	self.wCurrentUser = cmd_data.wOutCardUser
	local wTurnUser = self.wCurrentUser + 1
	local wViewTurnUser = self:SwitchViewChairID(wTurnUser)
	while self.cbPlayStatus[wViewTurnUser] ~= 1 do
		wTurnUser = wTurnUser + 1
		if wTurnUser > 3 then
			wTurnUser = 0
		end
		wViewTurnUser = self:SwitchViewChairID(wTurnUser)
	end
	--设置听牌
	self._gameView._cardLayer:promptListenOutCard(nil)
	if wViewId == cmd.MY_VIEWID then
		local cbPromptHuCard = self:getListenPromptHuCard(cmd_data.cbOutCardData)
		self._gameView:setListeningCard(cbPromptHuCard)
		--听牌数据置空
		self.cbListenPromptOutCard = {}
		self.cbListenCardList = {}
        self._gameView._cardLayer:makeCardLight()
        self:updateCardInfo( false, 0 )
        self._gameView:child('panel_choice'):hide()
	end
	--设置时间 设置空的位置，不指向任何人
	self:SetGameClock(yl.INVALID_CHAIR, cmd.IDI_OUT_CARD, self.cbTimeOutCard)
	return true
end

--发送扑克(抓牌)
function GameLayer:onSubSendCard(dataBuffer)
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_SendCard, dataBuffer)
	--dump(cmd_data, "CMD_S_SendCard", 9)
	--print("发送扑克", cmd_data.cbCardData)

	self.wCurrentUser = cmd_data.wCurrentUser
	local wCurrentViewId = self:SwitchViewChairID(self.wCurrentUser)
	self._gameView:gameSendCard(wCurrentViewId, cmd_data)

	self:SetGameClock(self.wCurrentUser, cmd.IDI_OUT_CARD, self.cbTimeOutCard)

	self._gameView:HideGameBtn()
	if self.wCurrentUser == self:GetMeChairID()  then
		self._gameView:recognizecbActionMask(cmd_data.cbActionMask, cmd_data.cbCardData, cmd_data.cbGangCards[1])
		--自动胡牌
		if cmd_data.cbActionMask >= GameLogic.WIK_CHI_HU and self.bTrustee then
			self._gameView:onButtonClickedEvent(GameViewLayer.BT_WIN)
		end
	end

	--记录已出现的牌
	if wCurrentViewId == cmd.MY_VIEWID then
		local cbAppearCard = {cmd_data.cbCardData}
		self:insertAppearCard(cbAppearCard)
	end

	self.bMoPaiStatus = true
	self:PlaySound(cmd.RES_PATH.."sound/SEND_CARD.wav")
	if cmd_data.bTail then
		--self:playCardOperateSound(wCurrentViewId, true, nil)
	end
    
	return true
end

--操作提示
function GameLayer:onSubOperateNotify(dataBuffer)
	--print("操作提示")
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_OperateNotify, dataBuffer)
    local wOperateViewId = self:SwitchViewChairID(cmd_data.wOperateUser)
    if wOperateViewId == cmd.MY_VIEWID then
        self._gameView:recognizecbActionMask(cmd_data.cbActionMask, cmd_data.cbActionCard, cmd_data.cbGangCards[1])
    end
	return true
end

--听牌提示
function GameLayer:onSubListenNotify(dataBuffer)
	--print("听牌提示")
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_Hu_Data, dataBuffer)
	--dump(cmd_data, "CMD_S_Hu_Data")

	self.cbListenPromptOutCard = {}
	self.cbListenCardList = {}
	for i = 1, cmd_data.cbOutCardCount do
		self.cbListenPromptOutCard[i] = cmd_data.cbOutCardData[1][i]
		self.cbListenCardList[i] = {}
		for j = 1, cmd_data.cbHuCardCount[1][i] do
			self.cbListenCardList[i][j] = cmd_data.cbHuCardData[i][j]
		end
		--print("self.cbListenCardList"..i, table.concat(self.cbListenCardList[i], ","))
	end
	--print("self.cbListenPromptOutCard", table.concat(self.cbListenPromptOutCard, ","))
	return true
end

--操作结果
function GameLayer:onSubOperateResult(dataBuffer)
	--print("操作结果")
	local cmd_data = ExternalFun.read_netdata( cmd.CMD_S_OperateResult, dataBuffer )
	--dump(cmd_data, "CMD_S_OperateResult")
	if cmd_data.cbOperateCode == GameLogic.WIK_NULL then
		assert(false, "没有操作也会进来？")
		return true
	end
    -- 去除 吃碰杠 选项
    self._gameView:HideGameBtn()
    self._gameView:child('panel_choice'):hide()
	
    local wOperateViewId = self:SwitchViewChairID( cmd_data.wOperateUser )
    if wOperateViewId == cmd.MY_VIEWID then
        self:updateCardInfo( false, 0 )
    end

    if cmd_data.cbOperateCode < GameLogic.WIK_LISTEN or cmd_data.cbOperateCode == GameLogic.WIK_CHOU_PAI then 		--并非听牌
        local nShowStatus = GameLogic.SHOW_NULL
		local data1 = cmd_data.cbOperateCard[1][1]
		local data2 = cmd_data.cbOperateCard[1][2]
		local data3 = cmd_data.cbOperateCard[1][3]
        local data4 = cmd_data.cbOperateCard[1][4]
        local center_card = data1
		local cbOperateData = {}
		local cbRemoveData = {}
        local cbAddData = {}
        local cbOldOperateData = nil
		if cmd_data.cbOperateCode == GameLogic.WIK_GANG then
			cbOperateData = cmd_data.cbOperateCard[1]
			--检查杠的类型
			local cbCardCount = self._gameView._cardLayer.cbCardCount[wOperateViewId]
			if math.mod(cbCardCount - 2, 3) == 0 then   -- 抓到牌，可能是小明杠或暗杠
				if self._gameView._cardLayer:checkBumpOrBridgeCard(wOperateViewId, data1, cmd_data.cbOperateCard[1]) then
					nShowStatus = GameLogic.SHOW_MING_GANG
                    cbRemoveData = {data4}
				else
					nShowStatus = GameLogic.SHOW_AN_GANG
                    cbRemoveData = {data1, data2, data3, data4}
				end
			else    -- 别人打牌，可以是大明杠（放杠）
				nShowStatus = GameLogic.SHOW_FANG_GANG
                cbRemoveData = {data2, data3, data4}
			end
		elseif cmd_data.cbOperateCode == GameLogic.WIK_PENG then
			cbOperateData = cmd_data.cbOperateCard[1] --{data1, data1, data1}
			cbRemoveData = {data2, data3}
			nShowStatus = GameLogic.SHOW_PENG
		elseif cmd_data.cbOperateCode == GameLogic.WIK_RIGHT then
            center_card = data3
			cbOperateData = cmd_data.cbOperateCard[1]
            cbRemoveData = {data1, data2}
			nShowStatus = GameLogic.SHOW_CHI
		elseif cmd_data.cbOperateCode == GameLogic.WIK_CENTER then
            center_card = data2
			cbOperateData = cmd_data.cbOperateCard[1]
            cbRemoveData = {data1, data3}
			nShowStatus = GameLogic.SHOW_CHI
		elseif cmd_data.cbOperateCode == GameLogic.WIK_LEFT then
            center_card = data1
			cbOperateData = cmd_data.cbOperateCard[1]
            cbRemoveData = {data2, data3}
			nShowStatus = GameLogic.SHOW_CHI
        elseif cmd_data.cbOperateCode == GameLogic.WIK_CHOU_PAI then
            -- dump(cmd_data, '抽牌....', 9)
            ----修改标记 抽牌的前台替换 还有问题
            center_card = data1
			cbOperateData = cmd_data.cbOperateCard[1]
            cbOldOperateData = cmd_data.cbOldOperateCard[1]
		    nShowStatus = GameLogic.SHOW_CHOU_PAI
            for index = 1, 4 do
                local cbOldData = cbOldOperateData[index]
                local cbNewData = cbOperateData[index]
                if cbOldData > 0 and cbNewData > 0 and cbNewData ~= cbOldData then
                    cbRemoveData = { cbNewData }
                    cbAddData = { cbOldData }
                    break
                end
            end
		end

		local bAnGang = nShowStatus == GameLogic.SHOW_AN_GANG
        local provider_view_id = self:SwitchViewChairID(cmd_data.wProvideUser)

		self._gameView._cardLayer:bumpOrBridgeCard(wOperateViewId, 
        cbOperateData, 
        nShowStatus, 
        center_card, 
        provider_view_id,
        cmd_data.cbOperateCode,
        cbOldOperateData )

		local bRemoveSuccess = false
		if nShowStatus == GameLogic.SHOW_CHOU_PAI then
            --print('抽牌.....')
			self._gameView._cardLayer:removeHandCard(wOperateViewId, cbRemoveData, false)
            self._gameView._cardLayer:addHandCard(wOperateViewId, cbAddData)
		else
            --dump(cbRemoveData, '吃碰杠', 9)
			self._gameView._cardLayer:removeHandCard(wOperateViewId, cbRemoveData, false)
		end
        --self._gameView._cardLayer:recycleDiscard(self:SwitchViewChairID(cmd_data.wProvideUser))
	    --print("提供者不正常？", cmd_data.wProvideUser, self:GetMeChairID())
        --声音消除 谍报 
		--self:PlaySound(cmd.RES_PATH.."sound/PACK_CARD.wav")
		self:playCardOperateSound(wOperateViewId, false, cmd_data.cbOperateCode)

		--记录已出现的牌
		if wOperateViewId ~= cmd.MY_VIEWID then
			if nShowStatus == GameLogic.SHOW_AN_GANG then
				self:insertAppearCard(cbOperateData)
			elseif nShowStatus == GameLogic.SHOW_MING_GANG then
				self:insertAppearCard({data1})
			else
				self:insertAppearCard(cbRemoveData)
			end
		end
		--提示听牌
		if wOperateViewId == cmd.MY_VIEWID and cmd_data.cbOperateCode == GameLogic.WIK_PENG then
			self._gameView._cardLayer:promptListenOutCard(self.cbListenPromptOutCard)
		end
	end
	self._gameView:showOperateFlag(wOperateViewId, cmd_data.cbOperateCode)

	local cbTime = self.cbTimeOutCard - self.cbTimeOperateCard
	self:SetGameClock(cmd_data.wOperateUser, cmd.IDI_OUT_CARD, cbTime > 0 and cbTime or self.cbTimeOutCard)
    --摸牌的时候 需要 清理的牌 重置 
    if self._gameView.cbOutCardTemp ~= 0 and cs.game.DISCARD_IMMEDIATELY then
        self._gameView._cardLayer:recycleDiscard(self:SwitchViewChairID(cmd_data.wProvideUser))
    end
    self._gameView.cbOutCardTemp = 0
	return true
end

--用户听牌
function GameLayer:onSubListenCard(dataBuffer)
	--print("用户听牌")
	--local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_ListenCard, dataBuffer)
	--dump(cmd_data, "CMD_S_ListenCard")
	return true
end

--用户托管
function GameLayer:onSubTrustee(dataBuffer)
	--print("用户托管")
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_Trustee, dataBuffer)
	local wViewChairId = self:SwitchViewChairID( cmd_data.wChairID )
    self._gameView:setUserTrustee( wViewChairId, cmd_data.bTrustee )
    if cmd_data.wChairID == self:GetMeChairID() then
		self.bTrustee = cmd_data.bTrustee
	end
    if cmd_data.bTrustee then
		self:PlaySound(cmd.RES_PATH.."sound/GAME_TRUSTEE.wav")
	else
		self:PlaySound(cmd.RES_PATH.."sound/UNTRUSTEE.wav")
	end
    return true
end

--游戏结束
function GameLayer:onSubGameConclude(dataBuffer)
	--print("游戏结束")    
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameConclude, dataBuffer)
	--dump(cmd_data, "CMD_S_GameConclude")
    self._gameView:HideGameBtn()
    self:changeGameStatus( cmd.GAME_SCENE_WAITING )
    -- 分数 提示
    for i = 1, self.cbPlayerCount do
        local score = cmd_data.lGameScore[1][i]
        local wViewChairId = self:SwitchViewChairID(i - 1)
        self._gameView:popPlayerScore(wViewChairId, score)
    end
    
	local bMeWin = nil  	--nil：没人赢，false：有人赢但我没赢，true：我赢
    local is_zimo = false

	--提示胡牌标记
	for i = 1, cmd.GAME_PLAYER do
		local wViewChairId = self:SwitchViewChairID(i - 1)
        if wViewChairId ~= yl.INVALID_CHAIR then
		    if cmd_data.playerHu[1][i] then
			    bMeWin = false
                if i - 1 == cmd_data.wProvideUser then
                    is_zimo = true
                end
                local code = is_zimo and GameLogic.WIK_ZIMO or GameLogic.WIK_CHI_HU
			    self:playCardOperateSound(wViewChairId, false, code)
			    self._gameView:showOperateFlag(wViewChairId, code)
			    if wViewChairId == cmd.MY_VIEWID then
				    bMeWin = true
			    end
		    end
        end
	end
	--显示结算图层
	local resultList = {}
	local cbBpBgData = self._gameView._cardLayer:getBpBgCardData()
	for i = 1, cmd.GAME_PLAYER do
		local wViewChairId = self:SwitchViewChairID(i - 1)
        if wViewChairId ~= yl.INVALID_CHAIR then
		    local lScore = cmd_data.lGameScore[1][i]
		    local user = self._gameFrame:getTableUserItem(self:GetMeTableID(), i - 1)
		    if user then
			    local result = {}
			    result.userItem = user
			    result.lScore = lScore
			    result.is_hu = cmd_data.playerHu[1][i]
			    result.cbCardData = {}
                result.pos = self.cbPlayerPos[i] 
			    --手牌
			    for j = 1, cmd_data.cbCardCount[1][i] do
				    result.cbCardData[j] = cmd_data.cbHandCardData[i][j]
			    end
                GameLogic.SortCardList( result.cbCardData )
			    --碰杠牌
			    result.cbBpBgCardData = cbBpBgData[wViewChairId]
			    --插入
			    table.insert(resultList, result)

                -- 非回放，摊牌
                if not yl.IS_REPLAY_MODEL then
                    local cards = result.cbCardData
                    -- 因为自摸的牌不在结果的手牌当中，要加回去
                    if result.is_hu and cmd_data.wProvideUser == i - 1 then
                        cards = clone(result.cbCardData)    -- 不影响原始数据
                        table.insert(cards, cmd_data.cbProvideCard)
                    end
                    self._gameView._cardLayer:setHandCard(wViewChairId, #cards, cards, true)
                end
		    end
        end
	end
    cmd_data.cbPlayerMaxCardsNum =  self._gameView._cardLayer.cbPlayerMaxCardsNum

    ----------------------------------------------------------------------------------------
    -- 下面这段代码不用self做perform，也不包含self，因为self很可能瞬间就消失，也不能用cs.game
    --      而我们要确保GameResultLayer一定会被显示
    ----------------------------------------------------------------------------------------
	-- 显示小结算框
    local path = cs.game.SRC .. 'room.GameResultLayer'
    local gameResultLayer = helper.pop.popLayer(path, nil, {self._gameView, resultList, cmd_data, self.wBankerUser}):hide()
    local showResult = function()
	    cc.Director:getInstance():getRunningScene():runMyAction(cc.Sequence:create(cc.DelayTime:create(0.5), cc.CallFunc:create(function(ref)
            gameResultLayer:effectShow()
	    end)))

        if GlobalUserItem.bSoundAble then
            AudioEngine.playEffect( string.format('sound/%s.wav', bMeWin and 'ZIMO_WIN' or 'ZIMO_LOSE') )
        end
    end

    -- 如果有人胡，则让胡特效显示一会儿再显示结算(私人场或普通金币场或者未结束)
    if bMeWin ~= nil and (GlobalUserItem.bPrivateRoom or GlobalUserItem.bCommonGold) then
        cc.Director:getInstance():getRunningScene():perform(showResult, 1.3)
    else
        showResult()
    end
    ----------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------

	self.cbPlayStatus = {0, 0, 0, 0}
    self.bTrustee = false
    self.bSendCardFinsh = false
	self._gameView:gameConclude()

	-- if GlobalUserItem.bPrivateRoom then
	 	self._gameView.spClock:setVisible(false)
	-- 	self._gameView.asLabTime:setString("0")
	-- else
		self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, self.cbTimeStartGame)
	--end

    self._gameView:updateTingBtn( true )

	return true
end

--游戏记录（房卡）
function GameLayer:onSubGameRecord(dataBuffer)
	--print("游戏记录")
	local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_Record, dataBuffer)
	--dump(cmd_data, "CMD_S_Record")

	self.m_userRecord = {}
	local nInningsCount = cmd_data.nCount
	for i = 1, self.cbPlayerCount do
		self.m_userRecord[i] = {}
		self.m_userRecord[i].cbHuCount = cmd_data.cbHuCount[1][i]
		self.m_userRecord[i].cbMingGang = cmd_data.cbMingGang[1][i]
		self.m_userRecord[i].cbAnGang = cmd_data.cbAnGang[1][i]
		self.m_userRecord[i].cbMaCount = cmd_data.cbMaCount[1][i]
		self.m_userRecord[i].lDetailScore = {}
		for j = 1, nInningsCount do
			self.m_userRecord[i].lDetailScore[j] = cmd_data.lDetailScore[i][j]
		end
	end
	--dump(self.m_userRecord, "m_userRecord", 5)
end

-- 刷新信息
function GameLayer:onUpdateOtherScore(dataBuffer)
    --print('刷新其他信息')
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_UpdateOtherScore, dataBuffer)
    --dump(cmd_data, "CMD_S_UpdateOtherScore", 9)
    local player_score = {}
    local player_desc = {}
    for i = 1, cmd.GAME_PLAYER do
        local chair = i-1
        local viewID = self:SwitchViewChairID(chair)
        player_score[viewID] = cmd_data.player_score[1][i]
        player_desc[viewID] = cmd_data.player_desc[1][i]
    end
    self._gameView:updateOtherScore(cmd_data.cbType, player_score, player_desc)
    return true
end

-- 刷新听牌的信息
function GameLayer:onUpdateTingCardInfo(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_Ting, dataBuffer)
    local chair = cmd_data.wChairID
    local viewID = self:SwitchViewChairID(chair)
    if viewID == cmd.MY_VIEWID then
        --print('刷新听牌的信息', cmd_data.bIsTing, viewID, cmd.MY_VIEWID, chair)
        self.bIsShowTing = true
        if cmd_data.bIsTing then
            self.cardsTingInfo.tingCardsItem = cmd_data.TingCardsItem[1]
        else
            self.cardsTingInfo.tingCardsItem = {}
        end
        if cmd_data.bIsCurTing then
            self.cardsTingInfo.curTingCards = cmd_data.curTingCards
        else
            self.cardsTingInfo.curTingCards = nil
        end
        self.cardsTingInfo.bIsTing = cmd_data.bIsTing
        self.cardsTingInfo.bIsCurTing = cmd_data.bIsCurTing	
        self._gameView.cardsTingInfo = self.cardsTingInfo
        self._gameView._cardLayer:updateTingCardsInfo( self.cardsTingInfo )
        self._gameView:updateTingBtn()
    end
    return true
end

-- 出牌提示限制
function GameLayer:onControlCardsNotify( dataBuffer )
    --print('出牌提示限制')
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_ControlCardsNotify, dataBuffer)
    local chair = cmd_data.wOperateUser
    local viewID = self:SwitchViewChairID(chair)
    self._gameView._cardLayer:checkControlCards(viewID, cmd_data.cbTouchControlCards[1])
    return true
end

-- 显示效果的消息
function GameLayer:onEffectNotify( dataBuffer )
    --print('显示效果')
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_ShowEffect, dataBuffer)
    local chairID = cmd_data.wCurrentUser 
    local wEffectType = cmd_data.wEffectType
    local viewID = self:SwitchViewChairID(chairID)
    -- dump(cmd_data, '显示效果', 9)
    if wEffectType == yl.ShowEffectType.RuanAni then
        self._gameView:showOperateFlag( viewID, cmd_data.cbValue, nil, cmd_data.fDelayTime)
    elseif wEffectType == yl.ShowEffectType.RuanPath then
        self._gameView:showOperateFlag( viewID, cmd_data.cbValue, cmd_data.szEffectPath, cmd_data.fDelayTime )
    end
    if viewID ~= yl.INVALID_CHAIR then
        helper.music.playPeiyin( self.wPeiyin[viewID], cmd_data.szSound )
    end
    return true
end

-- 刷新牌的信息
function GameLayer:onUpdateCardInfo( dataBuffer )
    --print('刷新牌的信息')
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_CardInfo, dataBuffer)
    local chair = cmd_data.wChairID
    local viewID = self:SwitchViewChairID(chair)
    if viewID == cmd.MY_VIEWID then
        self.bIsShowCardInfo = true
        self.cardsDataInfo.bHandCardIndex = cmd_data.bHandCardIndex[1]
    end
    self.cardsDataInfo.bOutCardNum = cmd_data.bOutCardNum[1]
    self.cardsDataInfo.bLeftCardNum = cmd_data.bLeftCardNum[1]
    return true
end

-- 刷新
function GameLayer:updateCardInfo(isShow, cardData)
    -----牌的信息
    ------------------------------------------------
    local cbCardIndex = 0
    local leftNum = 0
    local isShowCardInfo = isShow
    --dump(self.cardsDataInfo, '刷新牌的信息', 9)
    if self.bIsShowCardInfo then
        if isShowCardInfo then
            cbCardIndex = GameLogic.SwitchToCardIndex(cardData)
            leftNum = self.cardsDataInfo.bLeftCardNum[cbCardIndex] 
            --print('刷新牌的信息', cbCardIndex, leftNum)
        else
            isShowCardInfo = false
        end
        if nil ~= self._gameView._cardLayer then
           --self._gameView._priView:updateCardsInfo( isShowCardInfo, cardData, leftNum )
           if isShowCardInfo then
                self._gameView._cardLayer:changeCardColor(cardData, display.COLOR_YELLOW)
           else
                self._gameView._cardLayer:changeCardColor(nil, display.COLOR_PURE_WHITE)
           end
        end
    end
    --------------------------------------------------
    local isShowTing = isShow
    local ting_info = nil
    if isShowTing and self.bIsShowTing then
        cbCardIndex = GameLogic.SwitchToCardIndex(cardData)
        ting_info = self.cardsTingInfo.tingCardsItem[cbCardIndex]
    else
        isShowTing = false
    end
    if nil ~= self._gameView and nil ~= self._gameView.updateCardsTingInfo then
       self._gameView:updateCardsTingInfo( isShowTing, ting_info )
    end
end

--*****************************    普通函数     *********************************--
--发牌完成
function GameLayer:sendCardFinish()
	--self:SetGameClock(self.wCurrentUser, cmd.IDI_OUT_CARD, self.cbTimeOutCard)
	
	--提示操作
	if self.cbActionMask then
		self._gameView:recognizecbActionMask(self.cbActionMask, self.cbActionCard, self.cbGangCards and self.cbGangCards[1])
	end

	--提示听牌
	if self.wBankerUser == self:GetMeChairID() then
		self._gameView._cardLayer:promptListenOutCard(self.cbListenPromptOutCard)
	end

	self.bSendCardFinsh = true
end

--解析筛子
function GameLayer:analyseSice(wSiceCount)
	local cbSiceCount1 = math.mod(wSiceCount, 256)
	local cbSiceCount2 = math.floor(wSiceCount/256)
	return cbSiceCount1, cbSiceCount2
end

--设置操作时间
function GameLayer:SetGameOperateClock()
	self:SetGameClock(self:GetMeChairID(), cmd.IDI_OPERATE_CARD, self.cbTimeOperateCard)
end

-- 用户配音改变
function GameLayer:onUserPeiyinChange(user_item)
	local view_id = self:SwitchViewChairID(user_item.wChairID)
    self.wPeiyin[view_id] = user_item.wPeiyin
end

-- 用户动作(献花、扔砖等)
function GameLayer:onUserAction(from_item, to_item, act_index)
	local from_view_id = self:SwitchViewChairID(from_item.wChairID)
	local to_view_id = self:SwitchViewChairID(to_item.wChairID)
    if from_view_id ~= yl.INVALID_CHAIR and to_view_id ~= yl.INVALID_CHAIR then
	    self._gameView:showUserAction(from_view_id, to_view_id, act_index)
    end
end

--播放麻将数据音效（哪张）
function GameLayer:playCardDataSound(viewId, cbCardData)
    local cardindex = GameLogic.SwitchToCardIndex(cbCardData)
	local isHua = self.bCurHuaMak[cardindex]
    if isHua then
        return
    end
    local names = {"wan", "tiao", "tong", "feng", "hua"}
    local color, value = cs.game.util.splitCardValue(cbCardData)
	local file = value .. names[color + 1]
    helper.music.playPeiyin( self.wPeiyin[viewId], file )
end

--播放麻将操作音效
function GameLayer:playCardOperateSound(viewId, bTail, operateCode)
	if operateCode == GameLogic.WIK_NULL then return end

	local file = ""
	if bTail then
		file = "buhua"
	else
		if operateCode == GameLogic.WIK_ZIMO then
			file = "zimo"
		elseif operateCode == GameLogic.WIK_CHI_HU then
			file = "hu"
		elseif operateCode == GameLogic.WIK_LISTEN then
			file = "ting"
		elseif operateCode == GameLogic.WIK_GANG then
			file = "gang"
		elseif operateCode == GameLogic.WIK_PENG then
			file = "peng"
		elseif operateCode <= GameLogic.WIK_RIGHT then
			file = "chi"
        elseif operateCode == GameLogic.WIK_CHENG_BAO then
			file = "santan"
		end
	end
    helper.music.playPeiyin( self.wPeiyin[viewId], file )
end

--播放随机聊天音效
function GameLayer:playRandomSound(viewId)
	local rand = math.random(25) - 1
	if rand <= 6 then
		local file = string.format('random%02d', rand)
        helper.music.playPeiyin( self.wPeiyin[viewId], file )
	end
end

--插入到已出现牌中
function GameLayer:insertAppearCard(cbCardData)
	assert(type(cbCardData) == "table")
	for i = 1, #cbCardData do
		table.insert(self.cbAppearCardData, cbCardData[i])
		--self._gameView:reduceListenCardNum(cbCardData[i])
	end
	table.sort(self.cbAppearCardData)
	local str = ""
	for i = 1, #self.cbAppearCardData do
		str = str..string.format("%x,", self.cbAppearCardData[i])
	end
	--print("已出现的牌:", str)
end

function GameLayer:getDetailScore()
	return self.m_userRecord
end

function GameLayer:getListenPromptOutCard()
	return self.cbListenPromptOutCard
end

function GameLayer:getListenPromptHuCard(cbOutCard)
	if not cbOutCard then
		return nil
	end

	for i = 1, #self.cbListenPromptOutCard do
		if self.cbListenPromptOutCard[i] == cbOutCard then
			assert(#self.cbListenCardList > 0 and self.cbListenCardList[i] and #self.cbListenCardList[i] > 0)
			return self.cbListenCardList[i]
		end
	end

	return nil
end

-- 刷新房卡数据
function GameLayer:updatePriRoom()
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end


-------------------------------------------------------------------------------
-- 相同IP检查
--  @overide 父类GameModel调用
-------------------------------------------------------------------------------
function GameLayer:checkSameIP()
    -- 汇总ip信息
    local ips = {}
    local count = 0
	for i = 1, cmd.GAME_PLAYER do
		local view_id = self:SwitchViewChairID(i - 1)
        if view_id ~= yl.INVALID_CHAIR then
		    local user_item = self._gameFrame:getTableUserItem(self:GetMeTableID(), i - 1)
            --user_item.szIpAddress 可能会是空值
            if user_item and user_item.szIpAddress then
                count = count  + 1
                ips[user_item.szIpAddress] = (ips[user_item.szIpAddress] or 0) + 1  -- 相同记录次数
            end
        end
    end

    -- 是否有相同检查
    local has_same = false
    for k, v in pairs(ips) do
        if k ~= '' and v > 1 then   -- 不为空且大于1表示有相同的
            has_same = true
            if not self.m_ip_warninged then
                self.m_ip_warninged = true
                helper.pop.message( LANG.IP_SAME_WARNING )
            end
            break
        end
    end
    
    -- 现在没有相同的，且之前警告过，那么要有不再重复的提示
    if not has_same and self.m_ip_warninged then
        self.m_ip_warninged = nil   -- 恢复，用于下次有重复时重新警告
        if count > 1 then
            helper.pop.message( LANG.IP_SAME_NONE )
        end
    end
end


--*****************************    发送消息     *********************************--
--开始游戏
function GameLayer:sendGameStart()
	self:SendUserReady()
	self:OnResetGameEngine()
end

--出牌
function GameLayer:sendOutCard(card)
	-- body
    --[[
	if card == GameLogic.MAGIC_DATA then
		return false
	end
    --]]
	self._gameView:HideGameBtn()
	--print("发送出牌", card)

	local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_OutCard)
	cmd_data:pushbyte(card)
	return self:SendData(cmd.SUB_C_OUT_CARD, cmd_data)
end

--操作扑克
function GameLayer:sendOperateCard(cbOperateCode, cbOperateCard)
	--print("发送操作提示：", cbOperateCode, table.concat(cbOperateCard, ","))
    self:AddUserLog('真实操作:' .. cbOperateCode .. table.concat(cbOperateCard, ","), true)
    self:sendUserLog()
	assert(type(cbOperateCard) == "table")
    if not self._gameView.b_is_game_btn_ok then
        return
    else
        self._gameView.b_is_game_btn_ok = false
    end
	--听牌数据置空
	self.cbListenPromptOutCard = {}
	self.cbListenCardList = {}
	self._gameView:setListeningCard(nil)
	self._gameView._cardLayer:promptListenOutCard(nil)

	--发送操作
	local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_OperateCard)
    --local cmd_data = CCmd_Data:create(4)
	cmd_data:pushbyte(cbOperateCode)
	for i = 1, 4 do
		cmd_data:pushbyte(cbOperateCard[i])
	end
	--dump(cmd_data, "operate")
	self:SendData(cmd.SUB_C_OPERATE_CARD, cmd_data)
end

--用户听牌
function GameLayer:sendUserListenCard(bListen)
	local cmd_data = CCmd_Data:create(1)
	cmd_data:pushbool(bListen)
	self:SendData(cmd.SUB_C_LISTEN_CARD, cmd_data)
end
--用户托管
function GameLayer:sendUserTrustee( isTrustee )
	if not self.bSendCardFinsh then
		return
	end
	local cmd_data = CCmd_Data:create(1)
	cmd_data:pushbool(isTrustee)
	self:SendData(cmd.SUB_C_TRUSTEE, cmd_data)
end

--发送扑克
function GameLayer:sendControlCard(cbControlGameCount, cbCardCount, wBankerUser, cbCardData)
	local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_SendCard)
	cmd_data:pushbyte(cbControlGameCount)
	cmd_data:pushbyte(cbCardCount)
	cmd_data:pushword(wBankerUser)
	for i = 1, #cbCardData do
		cmd_data:pushbyte(cbCardData[i])
	end
	self:SendData(cmd.SUB_C_SEND_CARD, cmd_data)
end

-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
       self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
       --print('stop.............replay')
       self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
     if not PassRoom:getInstance():getNetFrame():doNextReplay() then
        self:startOrStopReplay( false )
     end
end

-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )
        end
    else
        --print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end

-- 检测心跳
function GameLayer:checkHeartBeat()
    --print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end

-- 重连
function GameLayer:doReConnect()
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()

    local room_id, server_id = PassRoom:getInstance().m_tabPriData.szServerID, GlobalUserItem.dwCurServerID
    if room_id and (type(room_id) == 'string' and room_id ~= '' or type(room_id) == 'number' and romm_id > 0) then
        PassRoom:getInstance():getNetFrame():onSearchRoom( room_id, yl.SEARCH_ROOM_TYPE_RECONNECT )
    else
        PassRoom:getInstance():onLoginRoom(server_id, true)
    end
end

-- 刷新界面
function GameLayer:updateView()
    self._gameView._cardLayer:initHandCard()
    -- self:requestLocation()
end

-- 初始化位置包
function GameLayer:startOrStopReqLocation( status )
    if not yl.IS_LOCATION_OPEN or yl.IS_REPLAY_MODEL or not GlobalUserItem.bPrivateRoom then
        return
    end
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:requestLocation()
            --print('start.............LOCATION')
            self:stop( yl.ActionTag.LOCATION )
            self:perform( handler(self, self.requestLocation), 30, -1, yl.ActionTag.LOCATION )
        end
    else
        --print('stop.............LOCATION')
        self:stop( yl.ActionTag.LOCATION )
    end
end

-- 刷新位置 
function GameLayer:updateLocation()
   self._gameView:updateLabsInfo( false )
end

-- 定位 
function GameLayer:requestLocation()
    if not yl.IS_LOCATION_OPEN or not GlobalUserItem.bPrivateRoom then
        return
    end
    if tolua.isnull( self ) or not self._gameFrame then
        return
    end
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    local this = self
    MultiPlatform:getInstance():requestLocation(function(res)
        if tolua.isnull( this ) or not this._gameFrame then
            return
        end
        local ok, datatable = pcall(function()
	        return cjson.decode(res)
	    end)
        if ok then
            --dump(datatable, " location data mike ", 6) 
            if datatable.berror then
                datatable.longitude = 0
                datatable.latitude = 0
                datatable.accuracy = 0
            end
        else
            datatable = {}
            datatable.berror = true
            datatable.longitude = 0
            datatable.latitude = 0
            datatable.accuracy = 0
        end
        this._gameFrame:sendLocation( datatable )
    end)
end

-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
        --print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
        --print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    --print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置 
function GameLayer:onCheckSocketIsOK()
   self:startCheckIsSocketOK( false )
   self.check_is_socket_ok_times = 0
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('subGameResultLayer')
    if view then
        view:removeFromParent()
    end
end

-- 获取自己的ID
function GameLayer:GetMeChairID()
    if self.m_wMyChairId then
        return self.m_wMyChairId
    end
    return self._gameFrame:GetChairID() 
end

-- 获取用户日志等级
function GameLayer:GetUserLogLevel()
    if yl.IS_REPLAY_MODEL or not self.userLogLevel then
        return 0
    end
    return self.userLogLevel 
end

function GameLayer:AddUserLog( msg, isClean )
    if self:GetUserLogLevel() <= 0 then
        return
    end
    if isClean then
        self.userLog = ''
    end
    self.userLog = self.userLog .. msg
end

--发送用户行为日志
function GameLayer:sendUserLog( )
    if self.userLog == '' or yl.IS_REPLAY_MODEL or self:GetUserLogLevel() <= 0 then
        return
    end
    --print('发送用户行为日志', self.userLog )
	--local cmd_data = ExternalFun.create_netdata( cmd.CMD_C_UserLog )
    local cmd_data = CCmd_Data:create()
    cmd_data:pushstring( self.userLog, 128)
	self:SendData( cmd.SUB_C_USER_LOG, cmd_data )
    self.userLog = ''
end


--改变游戏状态
function GameLayer:changeGameStatus( cbGameStatus )
    self.m_cbGameStatus = cbGameStatus
    if self.m_cbGameStatus == cmd.GAME_SCENE_PLAY then
        self._gameView.btStart:hide()
    end
end

return GameLayer
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
云之讯短信HTTP接口测试脚本
基于 test_http.txt 文档中的接口规范
测试云之讯短信服务的各个功能
"""

import hashlib
import time
import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class UcpaasHTTPTester:
    def __init__(self):
        # 云之讯配置（从user.php中获取的实际配置）
        # 注意：这些是从代码中提取的配置，但可能需要更新
        self.accountsid = "5cbc351f17a418686094747a62ffd946"  # 从user.php获取
        self.token = "fac1c2af0c05327677d33916ba841079"      # 从user.php获取
        self.appId = "9dc983c028e54d5fbc97228e6af5344e"       # 从user.php获取

        # HTTP接口使用的是clientid/password，需要映射
        self.clientid = self.accountsid  # 使用accountsid作为clientid
        self.password = self.token       # 使用token作为password

        # 云之讯HTTP接口地址
        self.base_url = "http://open2.ucpaas.com"

        # 测试配置
        self.test_mobile = "***********"  # 测试手机号（请替换为真实手机号）
        self.template_id = "174333"       # 模板ID（从user.php中获取）
        
        self.session = requests.Session()
        
    def md5_encrypt(self, text):
        """MD5加密"""
        return hashlib.md5(text.encode('utf-8')).hexdigest().lower()
    
    def test_template_sms(self, mobile=None, param="1234"):
        """测试固定模板短信下行接口"""
        print("\n=== 测试固定模板短信下行接口 ===")
        
        if mobile is None:
            mobile = self.test_mobile
            
        url = f"{self.base_url}/sms-server/templatesms"
        
        # 构建请求数据
        data = {
            "clientid": self.clientid,
            "password": self.password,
            "mobile": mobile,
            "templateid": self.template_id,
            "param": param,
            "extend": "00",
            "uid": str(int(time.time())),
            "sendtime": ""  # 立即发送
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = self.session.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"解析后的JSON: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    code = result.get('code', -999)
                    msg = result.get('msg', '未知错误')
                    
                    if code == 0:
                        print("✅ 短信发送成功！")
                        total_fee = result.get('total_fee', 0)
                        print(f"计费条数: {total_fee}")
                        
                        if 'data' in result:
                            for item in result['data']:
                                print(f"手机号: {item.get('mobile')}, SID: {item.get('sid')}, 费用: {item.get('fee')}")
                        
                        return True, result
                    else:
                        print(f"❌ 短信发送失败")
                        print(f"错误代码: {code}")
                        print(f"错误描述: {msg}")
                        self.explain_error_code(code)
                        return False, result
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    return False, {'error': 'JSON解析失败'}
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False, {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_variable_sms(self, mobiles=None, params=None):
        """测试变量模板短信下行接口"""
        print("\n=== 测试变量模板短信下行接口 ===")
        
        if mobiles is None:
            mobiles = [self.test_mobile, "13800138001"]
        if params is None:
            params = ["验证码1;1234", "验证码2;5678"]
            
        url = f"{self.base_url}/sms-server/variablesms"
        
        # 构建请求数据
        data = {
            "clientid": self.clientid,
            "password": self.password,
            "mobile": ",".join(mobiles),
            "templateid": self.template_id,
            "param": "|".join(params),
            "extend": "00",
            "uid": str(int(time.time())),
            "sendtime": ""
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = self.session.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code', -999)
                
                if code == 0:
                    print("✅ 变量短信发送成功！")
                    return True, result
                else:
                    print(f"❌ 变量短信发送失败: {result.get('msg')}")
                    self.explain_error_code(code)
                    return False, result
            else:
                return False, {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_get_mo(self):
        """测试短信上行拉取接口"""
        print("\n=== 测试短信上行拉取接口 ===")
        
        url = f"{self.base_url}/sms-server/getmo"
        
        data = {
            "clientid": self.clientid,
            "password": self.password
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = self.session.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code', -999)
                
                if code == 0:
                    print("✅ 上行短信拉取成功！")
                    data_list = result.get('data', [])
                    print(f"上行短信数量: {len(data_list)}")
                    
                    for item in data_list:
                        print(f"上行ID: {item.get('mo_id')}")
                        print(f"手机号: {item.get('mobile')}")
                        print(f"内容: {item.get('content')}")
                        print(f"时间: {item.get('mo_time')}")
                        print("---")
                    
                    return True, result
                else:
                    print(f"❌ 上行短信拉取失败: {result.get('msg')}")
                    self.explain_error_code(code)
                    return False, result
            else:
                return False, {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_get_report(self):
        """测试短信状态报告拉取接口"""
        print("\n=== 测试短信状态报告拉取接口 ===")
        
        url = f"{self.base_url}/sms-server/getreport"
        
        data = {
            "clientid": self.clientid,
            "password": self.password
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = self.session.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code', -999)
                
                if code == 0:
                    print("✅ 状态报告拉取成功！")
                    data_list = result.get('data', [])
                    print(f"状态报告数量: {len(data_list)}")
                    
                    for item in data_list:
                        print(f"SID: {item.get('sid')}")
                        print(f"手机号: {item.get('mobile')}")
                        print(f"状态: {item.get('report_status')}")
                        print(f"描述: {item.get('report_desc')}")
                        print(f"时间: {item.get('report_time')}")
                        print("---")
                    
                    return True, result
                else:
                    print(f"❌ 状态报告拉取失败: {result.get('msg')}")
                    self.explain_error_code(code)
                    return False, result
            else:
                return False, {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_get_balance(self):
        """测试余额查询接口"""
        print("\n=== 测试余额查询接口 ===")
        
        url = f"{self.base_url}/sms-server/getbalance"
        
        data = {
            "clientid": self.clientid,
            "password": self.password
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = self.session.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code', -999)
                
                if code == 0:
                    print("✅ 余额查询成功！")
                    data_list = result.get('data', [])
                    
                    product_types = {
                        0: "行业短信",
                        1: "营销短信", 
                        2: "国际短信",
                        7: "USSD",
                        8: "闪信",
                        9: "挂机短信"
                    }
                    
                    for item in data_list:
                        product_type = item.get('product_type')
                        remain_quantity = item.get('remain_quantity')
                        type_name = product_types.get(product_type, f"未知类型({product_type})")
                        unit = "元" if product_type == 2 else "条"
                        
                        print(f"{type_name}: {remain_quantity}{unit}")
                    
                    return True, result
                else:
                    print(f"❌ 余额查询失败: {result.get('msg')}")
                    self.explain_error_code(code)
                    return False, result
            else:
                return False, {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def explain_error_code(self, code):
        """解释错误代码"""
        error_codes = {
            0: "成功",
            -1: "鉴权失败（帐号或密码错误）",
            -2: "账号余额不足",
            -3: "账号被注销",
            -4: "账号被锁定",
            -5: "ip鉴权失败",
            -6: "发送号码为空",
            -7: "手机号码格式错误",
            -8: "短信内容超长",
            -9: "签名未报备",
            -10: "协议类型不匹配",
            -11: "不允许拉取状态报告",
            -12: "访问频率过快",
            -13: "您的费用信息不存在",
            -14: "内部错误",
            -15: "用户对应的模板ID不存在、或模板未通过审核、或模板已删除",
            -16: "模板参数不匹配",
            -17: "USSD、闪信和挂机短信模板不允许发送国际号码",
            -18: "模板ID为空",
            -19: "模板参数含有非法字符",
            -20: "json格式错误",
            -21: "解析json失败",
            -22: "账号被冻结",
            -23: "短信类型为空",
            -24: "短信内容为空",
            -25: "发送号码数量超过100个",
            -26: "未找到签名",
            -27: "签名长度过短",
            -28: "签名长度过长",
            -29: "发送号码黑名单",
            -30: "重复的发送号码",
            -31: "无查询结果",
            -32: "不允许拉取上行",
            -33: "定时短信时间格式错误",
            -34: "定时发送时间太短（小于5分钟）",
            -35: "定时发送时间太长（大于一天）",
            -36: "号码解压失败",
            -37: "联网认证失败",
            -38: "无效的extend参数值",
            -39: "靓号拦截"
        }
        
        description = error_codes.get(code, f"未知错误代码: {code}")
        print(f"💡 错误说明: {description}")
        
        # 提供解决建议
        if code == -1:
            print("   建议: 检查clientid和password是否正确")
        elif code == -2:
            print("   建议: 充值账户余额")
        elif code == -5:
            print("   建议: 将服务器IP添加到白名单")
        elif code == -15:
            print("   建议: 检查模板ID是否正确，确认模板已审核通过")
        elif code == -16:
            print("   建议: 检查模板参数是否与模板匹配")

def run_comprehensive_test():
    """运行综合测试"""
    print("开始云之讯短信HTTP接口综合测试...")
    print("="*60)
    
    # 创建测试器
    tester = UcpaasHTTPTester()
    
    print("⚠️ 注意: 请先配置正确的clientid和password")
    print(f"当前配置: clientid={tester.clientid}, password={tester.password}")
    print()
    
    # 测试结果统计
    results = {}
    
    # 1. 测试余额查询
    print("1. 测试余额查询接口")
    success, result = tester.test_get_balance()
    results['balance'] = success
    
    # 2. 测试固定模板短信
    print("\n2. 测试固定模板短信接口")
    success, result = tester.test_template_sms(tester.test_mobile, "1234")
    results['template_sms'] = success
    
    # 3. 测试变量模板短信
    print("\n3. 测试变量模板短信接口")
    success, result = tester.test_variable_sms()
    results['variable_sms'] = success
    
    # 4. 测试上行短信拉取
    print("\n4. 测试上行短信拉取接口")
    success, result = tester.test_get_mo()
    results['get_mo'] = success
    
    # 5. 测试状态报告拉取
    print("\n5. 测试状态报告拉取接口")
    success, result = tester.test_get_report()
    results['get_report'] = success
    
    # 生成测试报告
    print("\n" + "="*60)
    print("测试报告:")
    print("="*60)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 个接口测试成功")
    
    if success_count == 0:
        print("\n💡 所有接口都失败，可能的原因:")
        print("1. clientid或password配置错误")
        print("2. 网络连接问题")
        print("3. 云之讯服务器问题")
        print("4. 账户状态异常")
    elif success_count < total_count:
        print("\n💡 部分接口失败，建议:")
        print("1. 检查失败接口的具体错误信息")
        print("2. 确认账户权限和配置")
        print("3. 联系云之讯技术支持")
    else:
        print("\n🎉 所有接口测试成功！云之讯短信服务正常")

if __name__ == '__main__':
    run_comprehensive_test()

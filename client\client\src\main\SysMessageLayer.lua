-------------------------------------------------------------------------------
--  创世版1.0
--  系统消息
--  @date 2018-03-06
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')

local PER_PAGE = 10

local SysMessageLayer = class("SysMessageLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function SysMessageLayer:ctor()
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('SysMessageLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)
    self.m_pager = main_node:child('pager'):hide()

    main_node:child('template'):hide()
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('pager/btn_left'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnPage) )
    self.m_pager:child('btn_right'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnPage) )
    self.m_pager:child('btn_left').is_left = true
    self.m_pager:child('label_page'):setString('0/0')

    self:requestMessages()
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function SysMessageLayer:onEnterTransitionFinish()
    print('SysMessageLayer:onEnterTransitionFinish...')
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_SYSTEM_MSG_LIST, self, self.onListResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function SysMessageLayer:onExit()
    print('SysMessageLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 请求消息列表
-------------------------------------------------------------------------------
function SysMessageLayer:requestMessages()
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_SYSTEM_MSG_LIST)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 列表返回
-------------------------------------------------------------------------------
function SysMessageLayer:onListResp(data)
    local messages = LiveFrame:getInstance():resp(data, cmd_common.tagSystemMsg, true)
    if not messages then return false end

    self.m_messages = messages
    if #messages <= PER_PAGE then
        self.m_pager:removeFromParent()
        self.m_pager = nil
        self:createItems(messages, 1)
    else
        self.m_pager:show()
        self:onBtnPage(self.m_pager:child('btn_right'))
    end
end


-------------------------------------------------------------------------------
-- 初始化玩法
-------------------------------------------------------------------------------
function SysMessageLayer:createItems(messages, page)
    local pager = self.m_pager
    if pager then
        pager:retain()
        pager:removeFromParent()
    end

    local listview = self.main_node:child('listview')
    local template = self.main_node:child('template')
    listview:removeAllItems()

    for i = (page - 1) * PER_PAGE + 1, math.min(#messages, page * PER_PAGE) do
        local msg = messages[i]
        item = template:clone():show()
        item:child('label_content'):setString( msg.szMsg )
        local str = string.format('%02d-%02d %02d:%02d', msg.msgTime.wMonth, msg.msgTime.wDay, msg.msgTime.wHour, msg.msgTime.wMinute)
        item:child('label_time'):setString(str)
        listview:pushBackCustomItem(item)
    end

    if pager then
        listview:pushBackCustomItem(pager)
        pager:release()
    end

    listview:jumpToTop()
end


-------------------------------------------------------------------------------
-- 翻页按钮点击
-------------------------------------------------------------------------------
function SysMessageLayer:onBtnPage(sender)
    local pager = self.m_pager
    local is_left = sender.is_left
    local page_count = pager.page_count or math.ceil(#self.m_messages / PER_PAGE)
    local cur_page = pager.cur_page or 0
    local btn_left, btn_right, label_page = pager:child('btn_left, btn_right, label_page')

    cur_page = cur_page + (is_left and -1 or 1)
    btn_left:setVisible(cur_page > 1)
    btn_right:setVisible(cur_page < page_count)
    label_page:setString(cur_page .. '/' .. page_count)
    self:createItems(self.m_messages, cur_page)

    pager.page_count = page_count
    pager.cur_page = cur_page
end


return SysMessageLayer
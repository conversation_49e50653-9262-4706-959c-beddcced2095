--
-- 私人房数据管理 全局模式

PassRoom = PassRoom or class("PassRoom")
local private_define = appdf.req(appdf.CLIENT_SRC .. "header.Define_Private")
local cmd_private = appdf.req(appdf.CLIENT_SRC .. "header.CMD_Private")

-- 私人房界面tag
local LAYTAG = private_define.tabLayTag
PassRoom.LAYTAG = LAYTAG
-- 游戏服务器登陆操作定义
local L_ACTION = private_define.tabLoginAction
PassRoom.L_ACTION = L_ACTION
-- 登陆服务器CMD
local cmd_pri_login = cmd_private.login
PassRoom.cmd_pri_login = cmd_pri_login
-- 游戏服务器CMD
local cmd_pri_game = cmd_private.game
PassRoom.cmd_pri_game = cmd_pri_game

local PassFrame = import(".PassFrame")

-- roomID 输入界面
local NAME_ROOMID_INPUT = "___private_roomid_input_layername___"

local targetPlatform = cc.Application:getInstance():getTargetPlatform()
function PassRoom:ctor()
    print('PassRoom:ctor............mike')
    --网络回调
    local privateCallBack = function(command, message, dataBuffer, notShow)
        if type(command) == "table" then
            if command.m == cmd_pri_login.MDM_MB_PERSONAL_SERVICE then
                return self:onPrivateLoginServerMessage(command.s, message, dataBuffer, notShow)
            elseif command.m == cmd_pri_game.MDM_GR_PERSONAL_TABLE then
                return self:onPrivateGameServerMessage(command.s, message, dataBuffer, notShow)
            end
        else
            self:popMessage(message, notShow)
            if -1 == command then
                self:dismissPopWait()
            end
        end
        
    end
    self._passFrame = PassFrame:create(self, privateCallBack)

    self:reSet()
end

-- 实现单例
PassRoom._instance = nil
function PassRoom:getInstance()
    if nil == PassRoom._instance then
        print("new instance")
        PassRoom._instance = PassRoom:create()
    end
    return PassRoom._instance
end

function PassRoom:reSet()
    -- 私人房模式游戏列表
    self.m_tabPriModeGame = {}
    -- 私人房列表
    self.m_tabPriRoomList = {}
    -- 创建记录
    self.m_tabCreateRecord = {}
    -- 参与记录
    self.m_tabJoinRecord = {}
    -- 私人房数据  CMD_GR_PersonalTableTip
    self.m_tabPriData = {}
    -- 私人房属性 tagPersonalRoomOption
    self.m_tabRoomOption = {}
    -- 私人房费用配置 tagPersonalTableParameter
    self.m_tabFeeConfigList = {}
    -- 是否自己房主
    self.m_bIsMyRoomOwner = false
    -- 私人房桌子号( 进入/查到到的 )
    self.m_dwTableID = yl.INVALID_TABLE
    -- 选择的私人房配置信息
    self.m_tabSelectRoomConfig = {}

    -- 大厅场景
    self._scene = nil
    -- 网络消息处理层
    self._viewFrame = nil
    -- 私人房信息层
    self._priView = nil

    -- 游戏服务器登陆动作
    self.m_nLoginAction = L_ACTION.ACT_NULL
    self.cbIsJoinGame = 0
    -- 是否已经取消桌子/退出
    self.m_bCancelTable = false
    -- 是否收到结算消息
    self.m_bRoomEnd = false
end

-- 当前游戏是否开启私人房模式
function PassRoom:isCurrentGameOpenPri( nKindID )
    return (self.m_tabPriModeGame[nKindID] or false)
end

-- 获取私人房
function PassRoom:getPriRoomByServerID( dwServerID )
    --[[
    local currentGameRoomList = self.m_tabPriRoomList[GlobalUserItem.nCurGameKind]
    print(dwServerID)
    if nil == currentGameRoomList then
        return nil
    end
    --]]
    for index, currentGameRoomList in pairs(self.m_tabPriRoomList) do
        for k,v in pairs(currentGameRoomList) do
            if v.wServerID == dwServerID then
                return v
            end
        end
    end
    --[[
    for k,v in pairs(currentGameRoomList) do
        if v.wServerID == dwServerID and v.wServerType == yl.GAME_GENRE_PERSONAL then
            return v
        end
    end
    --]]
    return nil
end

-- 顶层（最开始调用进入房间方法，比如进入金币场）登陆服务器，没有房间号
function PassRoom:onLoginServer( dwServerID, bLockEnter )
    local func = function()
        GlobalUserItem.szCurRoomID = nil
        self:onLoginRoom( dwServerID, bLockEnter )
    end
    local pServer = self:getPriRoomByServerID(dwServerID)
    if pServer then
        helper.app.checkRoom(func, pServer.wKindID)
    else
        func()
    end
end

-- 登陆私人房
function PassRoom:onLoginRoom( dwServerID, bLockEnter )
    local pServer = self:getPriRoomByServerID(dwServerID)
    if nil == pServer then
        print("PassRoom server null")
        helper.pop.message( LANG.ROOM_NOT_FOUND )
        return false
    end
    -- 登陆房间
    if nil ~= self._passFrame and nil ~= self._passFrame._gameFrame then
        bLockEnter = bLockEnter or false
        -- 锁表进入
        if bLockEnter then
            -- 动作定义
            PassRoom:getInstance().m_nLoginAction = PassRoom.L_ACTION.ACT_SEARCHROOM
        end
        self:showPopWait()
        GlobalUserItem.dwCurServerID = pServer.wServerID
        GlobalUserItem.bIsRedArena = pServer.wServerType == yl.GAME_GENRE_GOLD and pServer.cbSubType == 2   -- 是否红包赛
        GlobalUserItem.nCurGameKind = pServer.wKindID
        GlobalUserItem.bPrivateRoom = pServer.wServerType == yl.GAME_GENRE_PERSONAL
        GlobalUserItem.bCommonGold = nil
        self._passFrame._gameFrame:setEnterAntiCheatRoom(false)
        GlobalUserItem.nCurRoomIndex = pServer._nRoomIndex
        self._scene:onStartGame()
        self.m_bCancelTable = false
        return true
    end
    return false
end

-- 
function PassRoom:onEnterPlaza( scene, gameFrame )
    print("PassRoom:onEnterPlaza ==>", scene)
    self._scene = scene
    self._passFrame._gameFrame = gameFrame
    -- mike 20170724 暂时 先放这里 中断 恢复
    self:onEnterPlazaFinish()
end

function PassRoom:onExitPlaza()
    if nil ~= self._passFrame._gameFrame then
        self._passFrame._gameFrame = nil
    end
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
    cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
    self:exitRoom()
end

function PassRoom:onEnterPlazaFinish()
    -- 判断锁表
    if GlobalUserItem.dwLockServerID ~= 0 then
        GlobalUserItem.nCurGameKind = GlobalUserItem.dwLockKindID
        --[[
        -- 更新逻辑
        if not self._scene:updateGame(GlobalUserItem.dwLockKindID)
            and not self._scene:loadGameList(GlobalUserItem.dwLockKindID) then
            -- 
            local entergame = self._scene:getGameInfo(GlobalUserItem.dwLockKindID)
            if nil ~= entergame then
                self._scene:updateEnterGameInfo(entergame)
                --启动游戏
                print("PassRoom:onEnterPlazaFinish ==> lock pri game")
                return true, false, self:onLoginRoom(GlobalUserItem.dwLockServerID, true)
            end            
        end
        print("PassRoom:onEnterPlazaFinish ==> lock and update game")
        return true, true, false
        --]]
        return true, false, self:onLoginRoom(GlobalUserItem.dwLockServerID, true)
    end
    print("PassRoom:onEnterPlazaFinish ==> not lock game")
    return false, false, false
end

-- 登陆后进入房间列表
function PassRoom:onLoginEnterRoomList()
    -- 判断是否开启私人房
    if false == PassRoom:getInstance():isCurrentGameOpenPri(GlobalUserItem.nCurGameKind) then
        print("PassRoom:onLoginEnterRoomList: not open prigame")
        return false
    end

    if GlobalUserItem.dwLockServerID ~= 0 and GlobalUserItem.dwLockKindID == GlobalUserItem.nCurGameKind then
        print("PassRoom:onLoginEnterRoomList: onLoginRoom")
        --启动游戏
        return self:onLoginRoom(GlobalUserItem.dwLockServerID, true)
    else
        print("PassRoom:onLoginEnterRoomList: self._scene:onChangeShowMode(PassRoom.LAYTAG.LAYER_ROOMLIST)")
        self._scene:onChangeShowMode(PassRoom.LAYTAG.LAYER_ROOMLIST)
        return true
    end    
end

function PassRoom:onLoginPriRoomFinish()
    local bHandled = false
    if GlobalUserItem.bPrivateRoom and nil ~= self._viewFrame and nil ~= self._viewFrame.onLoginPriRoomFinish then
        bHandled = self._viewFrame:onLoginPriRoomFinish()
    elseif not GlobalUserItem.bPrivateRoom and GoldRoom then
        bHandled = GoldRoom:getInstance():onLoginRoomFinish()
    end
    -- 清理锁表
    GlobalUserItem.dwLockServerID = 0
    GlobalUserItem.dwLockKindID = 0
    if yl.IS_REPLAY_MODEL then
        print( '-- 退出游戏房间..............mike111111111111111111111111' );
        return
    end
    if not bHandled then
        local meUser = self:getMeUserItem()
        if nil == meUser then
            return
        end
        if (meUser.cbUserStatus == yl.US_FREE or meUser.cbUserStatus == yl.US_NULL) then
            -- 搜索登陆
            if self.m_nLoginAction == L_ACTION.ACT_SEARCHROOM then
                self:showPopWait()
                -- 进入游戏
                self:getNetFrame():sendEnterPrivateGame()
            -- 解散登陆
            elseif PassRoom:getInstance().m_nLoginAction == PassRoom.L_ACTION.ACT_DISSUMEROOM then
                self:showPopWait()
                -- 发送解散    
                self:getNetFrame():sendDissumeGame(self.m_dwTableID)
            else
                self._passFrame._gameFrame:onCloseSocket()
                self._passFrame:onCloseSocket()
                GlobalUserItem.nCurRoomIndex = -1
                print( '-- 退出游戏房间..............mike' );
                -- 退出游戏房间
                --[[
                if not tolua.isnull( self._scene ) then
                    self._scene:onKeyBack()
                end
                --]]
                local cur_gamelayer = helper.app.getFromScene('game_room_layer')
                if cur_gamelayer then
                    GlobalUserItem.bWaitQuit = false
                    cur_gamelayer:onExitRoom()
                end
            end
        elseif meUser.cbUserStatus == yl.US_PLAYING or meUser.cbUserStatus == yl.US_READY or meUser.cbUserStatus == yl.US_SIT then
            -- 搜索登陆
            if PassRoom:getInstance().m_nLoginAction == PassRoom.L_ACTION.ACT_SEARCHROOM
                or PassRoom:getInstance().m_nLoginAction == PassRoom.L_ACTION.ACT_ENTERTABLE then
                self:showPopWait()
                -- 切换游戏场景
                self._scene:onEnterTable()
                -- 发送配置
                self._passFrame._gameFrame:SendGameOption()
            -- 解散登陆
            elseif PassRoom:getInstance().m_nLoginAction == PassRoom.L_ACTION.ACT_DISSUMEROOM then
                self:showPopWait()
                -- 发送解散    
                self:getNetFrame():sendDissumeGame(self.m_dwTableID)
            end
        end
    end    
end

-- 用户状态变更( 进入、离开、准备 等)
function PassRoom:onEventUserState(viewid, useritem, bLeave)
    bLeave = bLeave or false
    if self.m_bCancelTable then
        return
    end
    if nil ~= self._priView and nil ~= self._priView.onRefreshInviteBtn then
        self._priView:onRefreshInviteBtn()
    end
end

function PassRoom:popMessage(message, notShow)
    if type(message) == "string" and "" ~= message then
        if notShow then
            print(message)
        else
            helper.pop.message(message)
        end
    end
end

function PassRoom:updateRoomInfo()
    if nil ~= self._priView and nil ~= self._priView.onRefreshInfo then
        self._priView:onRefreshInfo()
    end
    if nil ~= self._viewFrame and nil ~= self._viewFrame._gameView and nil ~= self._viewFrame._gameView.onRefreshInfo then
        self._viewFrame._gameView:onRefreshInfo()
    end
end

function PassRoom:onPrivateLoginServerMessage(result, message, dataBuffer, notShow)
    self:popMessage(message, notShow)

    if cmd_pri_login.SUB_MB_QUERY_PERSONAL_ROOM_LIST_RESULT == result 
        or cmd_pri_login.SUB_GR_QUERY_RECORD_INFO_RESULT == result
        or cmd_pri_login.SUB_GR_QUERY_RECORD_SCOR_RESULT == result then
        -- 列表记录
        if cmd_pri_login.SUB_GR_QUERY_RECORD_INFO_RESULT == result
            or cmd_pri_login.SUB_GR_QUERY_RECORD_SCOR_RESULT == result then
            if nil ~= self._scoreView and nil ~= self._scoreView.onRecordList then
                self._scoreView:onRecordList(dataBuffer, result)
            end
        end
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onReloadRecordList then
            self._viewFrame:onReloadRecordList(dataBuffer, result)
        end
    elseif cmd_pri_login.SUB_GR_EVENT_LIST_RESULT == result 
     or cmd_pri_login.SUB_GR_EVENT_GET_REWARD_RESULT == result 
      or cmd_pri_login.SUB_GR_EVENT_ACCEPT_RESULT == result then
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onListenEvent then
            self._viewFrame:onListenEvent(dataBuffer, result)
        end
    elseif cmd_pri_login.SUB_MB_PERSONAL_FEE_PARAMETER == result then
        print("PassRoom fee list call back")
    end
    self:dismissPopWait()
end

function PassRoom:onPrivateGameServerMessage(result, message, dataBuffer, notShow)
    self:popMessage(message, notShow)
    self:dismissPopWait()

    if cmd_pri_game.SUB_GR_CREATE_SUCCESS == result then    
        -- 创建成功    
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onRoomCreateSuccess then
            self._viewFrame:onRoomCreateSuccess()
        end
    elseif cmd_pri_game.SUB_GR_CANCEL_TABLE == result then
        print("PassRoom  SUB_GR_CANCEL_TABLE")
        GlobalUserItem.bWaitQuit = true
        self.m_bCancelTable = true
        -- 解散桌子
        if not self.m_bRoomEnd then
            helper.pop.alert(message.szDescribeString, function()
                GlobalUserItem.bWaitQuit = false
                if nil ~= self._viewFrame and nil ~= self._viewFrame.onExitRoom then
                    self._viewFrame:onExitRoom()
                end
            end)
        else
            helper.pop.message(message.szDescribeString)
        end
        self.m_bRoomEnd = false   

    elseif cmd_pri_game.SUB_GR_CANCEL_REQUEST == result then
        -- 请求解散
        -- message = game.CMD_GR_CancelRequest
        local useritem = self._passFrame._gameFrame._UserList[message.dwUserID]
        if nil == useritem then
            return
        end
        if self._viewFrame then
            self:createDimissLayer(useritem)
        end

    elseif cmd_pri_game.SUB_GR_REQUEST_REPLY == result then
        -- 请求答复
        -- message = game.CMD_GR_RequestReply
        local useritem = self._passFrame._gameFrame._UserList[message.dwUserID]
        if nil == useritem then
            return
        end
        local bHandled = false
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onCancellApply then
            bHandled = self._viewFrame:onCancellApply(useritem, message)
        end
        if not bHandled then
            local dismiss_layer = self._viewFrame:child('dismiss_layer')
            if dismiss_layer then
                dismiss_layer:onPlayerNotify(useritem, message.cbAgree == 1)
            end
        end

    elseif cmd_pri_game.SUB_GR_REQUEST_RESULT == result then
        -- 请求结果
        -- message = game.CMD_GR_RequestResult
        self:removeDimissLayer()

        if 0 == message.cbResult then
            helper.pop.message("解散房间请求未通过")
            return
        end

        self.m_bCancelTable = true
        local bHandled = false
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onCancelResult then
            bHandled = self._viewFrame:onCancelResult(message)
        end
        if not bHandled then
            
        end

    elseif cmd_pri_game.SUB_GR_CONTINUE_PLAY == result then
        -- 房卡场继续打
        -- message = game.CMD_GR_RequestReply
        local useritem = self._passFrame._gameFrame._UserList[message.dwUserID]
        if nil == useritem then
            return
        end
        if nil ~= self._viewFrame and nil ~= self._viewFrame._gameView and nil ~= self._viewFrame._gameView.updateRoomContinue then
            self._viewFrame._gameView:updateRoomContinue(useritem, message.cbAgree == 1)
        end

    elseif cmd_pri_game.SUB_GR_WAIT_OVER_TIME == result then
        -- 超时提示
        -- message = game.CMD_GR_WaitOverTime
        local useritem = self._passFrame._gameFrame._UserList[message.dwUserID]
        if nil == useritem then
            return
        end
        local curTag = nil
        if nil ~= self._scene and nil ~= self._scene._sceneRecord then
            curTag = self._scene._sceneRecord[#self._scene._sceneRecord]
        end
        helper.pop.alert(
            useritem.szNickName .. "断线等待超时, 是否继续等待?",
            {'继续等待', function() self:getNetFrame():sendRequestReply(0) end},
            {'不等了', function() self:getNetFrame():sendRequestReply(1) end}
        )

    elseif cmd_pri_game.SUB_GR_PERSONAL_TABLE_TIP == result then
        -- 游戏信息
        self:updateRoomInfo()

        -- 有解散界面需要恢复
        if self._viewFrame and message.dwReplyCount > 0 then
            self:createDimissLayer(nil, message)
        end
    elseif cmd_pri_game.SUB_GR_PERSONAL_TABLE_END == result then
        GlobalUserItem.bWaitQuit = true
        -- 屏蔽重连功能
        GlobalUserItem.bAutoConnect = false
        -- 结束消息
        if nil ~= self._priView and nil ~= self._priView.onPriGameEnd then
            self._priView:onPriGameEnd(message, dataBuffer)
        end
        self.m_bRoomEnd = true
    elseif cmd_pri_game.SUB_GR_CANCEL_TABLE_RESULT == result then        
        -- 解散结果
        -- message = game.CMD_GR_DissumeTable
        if 1 == message.cbIsDissumSuccess then
            showToast(self._viewFrame, "解散成功", 2)
        end
        -- 更新创建记录
        for k,v in pairs(self.m_tabCreateRecord) do
            if message.szRoomID == v.szRoomID then
                v.cbIsDisssumRoom = 1
                v.sysDissumeTime = message.sysDissumeTime
                local tt = v.sysDissumeTime
                v.sortTimeStmp = os.time({day=tt.wDay, month=tt.wMonth, year=tt.wYear, hour=tt.wHour, min=tt.wMinute, sec=tt.wSecond})
                break
            end
        end
        -- 排序
        table.sort( PassRoom:getInstance().m_tabCreateRecord, function(a, b)
            if a.cbIsDisssumRoom ~= b.cbIsDisssumRoom then
                return a.cbIsDisssumRoom > b.cbIsDisssumRoom
            elseif a.cbIsDisssumRoom == 0 and a.cbIsDisssumRoom == b.cbIsDisssumRoom then
                return a.createTimeStmp < b.createTimeStmp
            else
                return a.sortTimeStmp < b.sortTimeStmp
            end        
        end )
        --刷新列表
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onReloadRecordList then
            self._viewFrame:onReloadRecordList()
        end
    elseif cmd_pri_game.SUB_GF_PERSONAL_MESSAGE == result then
    elseif cmd_pri_game.SUB_GR_CURRECE_ROOMCARD_AND_BEAN == result then
        -- 解散后游戏信息
        if nil ~= self._scene and nil ~= self._scene.updateFangka then
            self._scene:updateFangka(dataBuffer.lRoomCard)
        end
    end
end


-------------------------------------------------------------------------------
-- 创建解散界面
-------------------------------------------------------------------------------
function PassRoom:createDimissLayer(useritem, message)
    local table_id = self._passFrame._gameFrame:GetTableID()
    if not useritem and message then
        useritem = self._passFrame._gameFrame:getTableUserItem(table_id, message.wCancelTableChairID)
    end
    -- 保护
    if not useritem then
        return
    end
    self:removeDimissLayer()       -- 理论上是不应该有的
    local users = self._viewFrame._gameView.m_sparrowUserItem   -- 当前桌子的所有人
    print('解散房间', #table.keys(users))
    if #table.keys(users) > 1 then
        local dismiss_layer = cs.app.client('main.DismissLayer'):create(useritem, users)
        dismiss_layer:zorder(20):setName('dismiss_layer')
        helper.pop.popLayer(dismiss_layer, self._viewFrame)

        if message then
            for chair_id, value in ipairs(message.nRequestReply[1]) do
                if value ~= 0 then
                    local item = self._passFrame._gameFrame:getTableUserItem(table_id, chair_id - 1)
                    if item and item.dwUserID ~= useritem.dwUserID then
                        dismiss_layer:onPlayerNotify(item, value == 1)
                    end
                end
            end
            dismiss_layer:resetTime(message.nCancelTableOverSeconds)
        end
    end
end


-------------------------------------------------------------------------------
-- 移除解散界面
-------------------------------------------------------------------------------
function PassRoom:removeDimissLayer()
    if self._viewFrame then
        local dismiss_layer = self._viewFrame:child('dismiss_layer')
        if dismiss_layer then 
            dismiss_layer:removeFromParent()
        end    
    end
end


-- 网络管理
function PassRoom:getNetFrame()
    return self._passFrame
end

-- 设置网络消息处理层
function PassRoom:setViewFrame(viewFrame)
    print( '设置网络消息处理层', viewFrame )
    self._viewFrame = viewFrame
end

-- 设置战绩消息处理层
function PassRoom:setScoreView(view)
    self._scoreView = view
end

-- 获取自己数据
function PassRoom:getMeUserItem()
    return self._passFrame._gameFrame:GetMeUserItem()
end

-- 获取游戏玩家数(椅子数)
function PassRoom:getChairCount()
    return self._passFrame._gameFrame:GetChairCount()
end

-- 获取大厅场景
function PassRoom:getPlazaScene()
    return self._scene
end

-- 进入游戏房间
function PassRoom:enterRoom( scene )
end

function PassRoom:exitRoom( )    
    self:setViewFrame(nil)
end

-- 进入游戏界面
function PassRoom:enterGame( gameLayer, scene )
    self:enterRoom(scene)
    local entergame = GlobalUserItem.m_tabEnterGame
    if nil ~= entergame then
        local layer = cs.app.game("RoomInfoLayer"):create( gameLayer )
        gameLayer._gameView:addChild(layer, gameLayer:getRoomInfoZOrder())
        gameLayer._gameView._priView = layer
        self._priView = layer
        -- 绑定回调
        self:setViewFrame(gameLayer)
    end
    self.m_bRoomEnd = false
end

-- 退出游戏界面
function PassRoom:exitGame()
    self._priView = nil
end

function PassRoom:showPopWait()
    helper.pop.waiting()
end

function PassRoom:dismissPopWait()
    helper.pop.waiting(false)
end

-- 请求退出游戏
function PassRoom:queryQuitGame( cbGameStatus )
    if self.m_bCancelTable then
        print("PassRoom:queryQuitGame 已经取消!")
        return
    end

    print('请求退出游戏 ', PassRoom:getInstance().m_bIsMyRoomOwner, 
    cbGameStatus, PassRoom:getInstance().m_tabPriData.dwPlayCount)
    if PassRoom:getInstance().m_bIsMyRoomOwner then
        helper.pop.alert( LANG.ROOM_DISMISS_QUERY, function()
            self:getNetFrame():sendRequestDissumeGame()
        end, true )
        return
    end
    
    -- 未玩且free
    if 0 == PassRoom:getInstance().m_tabPriData.dwPlayCount 
        and 0 == cbGameStatus then
        GlobalUserItem.bWaitQuit = false
        if nil ~= self._viewFrame and nil ~= self._viewFrame.onExitRoom then
            local wait = self._passFrame._gameFrame:StandUp(1)
            if wait then
                self:showPopWait()
            end
        end
        return
    end

    helper.pop.alert( LANG.ROOM_DISMISS_APPLY, function()
        self:getNetFrame():sendRequestDissumeGame()
    end, true)
end

-- 请求解散房间
function PassRoom:queryDismissRoom()
    if self.m_bCancelTable then
        print("PassRoom:queryQuitGame 已经取消!")
        return
    end

    helper.pop.alert( LANG.ROOM_DISMISS_APPLY, function()
        self:getNetFrame():sendRequestDissumeGame()
    end, true)
end

-- 私人房邀请好友
function PassRoom:priInviteFriend(frienddata, gameKind, wServerNumber, tableId, inviteMsg)
    if nil == frienddata or nil == self._scene.inviteFriend then
        return
    end
    if not gameKind then
        gameKind = GlobalUserItem.nCurGameKind
    else
        gameKind = tonumber(gameKind)
    end
    local id = frienddata.dwUserID
    if nil == id then
        return
    end
    local tab = {}
    tab.dwInvitedUserID = id
    tab.wKindID = gameKind
    tab.wServerNumber = wServerNumber or 0
    tab.wTableID = tableId or 0
    tab.szInviteMsg = inviteMsg or ""
    if FriendMgr:getInstance():sendInvitePrivateGame(tab) then
        local runScene = cc.Director:getInstance():getRunningScene()
        if nil ~= runScene then
            showToast(runScene, "邀请消息已发送!", 1)
        end
    end
end

-- 分享图片给好友
function PassRoom:imageShareToFriend( frienddata, imagepath, sharemsg )
    if nil == frienddata or nil == self._scene.imageShareToFriend then
        return
    end
    local id = frienddata.dwUserID
    if nil == id then
        return
    end
    self._scene:imageShareToFriend(id, imagepath, sharemsg)
end
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 首次分享获得
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbFirstShare = class('OrbFirstShare', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbFirstShare:ctor(panel)
    print('OrbFirstShare:ctor...')
    self.super.ctor(self, panel)
end


return OrbFirstShare
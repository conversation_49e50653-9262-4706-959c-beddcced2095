-------------------------------------------------------------------------------
--  创世版1.0
--  局结算(小结算)
--  @date 2017-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')


local GameResultLayer = class("GameResultLayer", function(scene)
    return helper.app.loadCSB('GameResultLayer.csb')
end)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GameResultLayer:ctor(scene, result_list, cmd_data, zhuang_chair_id)
    self._scene = scene
    self:child('template'):hide()
    
    self:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    self:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )

    -- 是否有扎鸟
    if cmd_data.m_cbNiaoCount > 0 then
        self:showNiao(result_list, cmd_data, zhuang_chair_id)
    else
        self:showResult(result_list, cmd_data, zhuang_chair_id)
    end
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 继续游戏按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnContinue(sender)
    self:doClose()
end


-------------------------------------------------------------------------------
-- 扎鸟过程特效
-------------------------------------------------------------------------------
function GameResultLayer:showNiao(result_list, cmd_data, zhuang_chair_id)
    -- 先隐藏界面元素
    for i, child in ipairs(self:getChildren()) do
        child:hide()
    end

    local sp_bg = display.newSprite('room/bg_zha_niao.png')
    sp_bg:pos(display.width/2, display.height/2):addTo(self)
    local size = sp_bg:size()
    display.newSprite('word/font_title_niao.png'):pos(size.width/2, size.height - 33):addTo(sp_bg)

    -- 计算起始位置
    local card_width = 75
    local gap = 25
    local count = cmd_data.m_cbNiaoCount
    local start_x = 0
    if count % 2 == 0 then
        start_x = size.width / 2 - (count - 1) * (card_width/2 + gap/2)
    else
        start_x = size.width / 2 - (math.ceil(count/2) - 1) * (card_width/2) - math.floor(count/2) * gap
    end

    -- 自己的chair id
    local chair_id = 0
    for i, item in ipairs(result_list) do
        if item.userItem.dwUserID == GlobalUserItem.dwUserID then
            chair_id = i - 1
            break
        end
    end

    local niao_cards = cmd_data.m_cbCardDataNiao[1]
    local niao_status = cmd_data.m_cNiaoStatus[1]
    local x = size.width / 2
    local y = size.height / 2 - 20
    for i = 1, count do
        local pos = cc.p(start_x, y)
        start_x = start_x + card_width + gap
        local card = cs.game.util.createCard(niao_cards[i], cmd.MY_VIEWID, 'hand'):pos(x, y - 320):addTo(sp_bg)

        local color, value = cs.game.util.splitCardValue( niao_cards[i] )
        if niao_status[i] ~= 255 then
            if niao_status[i] > 0 and niao_status[i] <= 4 then  -- 按[东南西北]方位抓中
                -- 标记方位
                local flag = display.newSprite('room/icon_direct_' .. niao_status[i] .. '.png')
                flag:pos(card:size().width/2, flag:size().height/2 + 5):addTo(card)
            end
            card:setColor( cc.c3b(255, 255, 0) )
        end

        card:hide():runAction( cc.Sequence:create(
            cc.DelayTime:create(i * 0.1),
            cc.Show:create(),
            cc.MoveTo:create(0.15, pos)
        ) )
    end

    self:perform(function()
        sp_bg:removeFromParent()
        self:showResult(result_list, cmd_data, zhuang_chair_id)
    end, count * 0.1 + 0.15 + 2.5)
end


-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function GameResultLayer:showResult(result_list, cmd_data, zhuang_chair_id)
    -- 先显示界面元素
    for i, child in ipairs(self:getChildren()) do
        child:show()
    end

    local width = 40

    -- 底分
    local difen = PassRoom:getInstance().m_tabPriData.lCellScore
    self:child('label_difen'):setString( LANG{'ROOM_DIFEN', value=difen} )
    
    -- 创建一张牌
    local createCard = function(card_value, parent, x, y)
        return cs.game.util.createCard(card_value, cmd.MY_VIEWID, 'out'):pos(x, y or 84):addTo(parent)
    end

    -- 是否流局
    local is_liuju = true
    for i, r in ipairs(result_list) do
        if r.is_hu then
            is_liuju = false
            break
        end
    end
    
    local template = self:child('template')
    local panel_offset_y = 134    -- 两行的锚点直接距离
    local panel_start_y = 92 + (4 - #result_list) * panel_offset_y / 2 -- 不足4行，修正到中间去
    for i = 1, cmd.GAME_PLAYER do
        if i <= #result_list then
            local chair_id = i - 1
            local panel = template:clone():show():addTo(self)
            panel:pos(display.width / 2 + 568, panel_start_y)
            panel:runAction( cc.Sequence:create(
                cc.DelayTime:create((i - 1) * 0.1),
                cc.EaseBackOut:create( cc.MoveTo:create(0.3, cc.p(display.width / 2, panel_start_y)) )
            ) )
            panel_start_y = panel_start_y + panel_offset_y

            -- 头像
            local panel_avator = panel:child('panel_avator')
            local head = PopupHead:create(self, result_list[i].userItem, 80, 100)
            head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

            -- 房主
            if chair_id ~= 0 then
                panel:child('img_fangzhu'):removeFromParent()
            end

            -- 输赢积分
            local score = result_list[i].lScore
            panel:child('label_score'):setString( (score >= 0 and '+' or '') .. score )

            -- 昵称
            panel:child('label_name'):setString(result_list[i].userItem.szNickName)

            -- 庄家
            cs.game.util.setZhuang(panel:child('img_dir'), #result_list, zhuang_chair_id, chair_id)
            -- 方位 
            cs.game.util.setFeng(panel:child('img_zhuang'), #result_list, zhuang_chair_id, chair_id)
            -- 个人麻将 ----------------------------
            local panel_card = panel:child('panel_card')
            local fX = 0

            -- 碰杠牌
            for j = 1, #result_list[i].cbBpBgCardData do
                createCard( result_list[i].cbBpBgCardData[j], panel_card, fX )
                fX = fX + width + (j == #result_list[i].cbBpBgCardData and 20 or 0)   -- 末尾空一点
            end

            -- 剩余手牌
            for j = 1, #result_list[i].cbCardData do 
                createCard( result_list[i].cbCardData[j], panel_card, fX )
                fX = fX + width
            end

            -- 番数
            local fan = cmd_data.wFanShu[1][chair_id+1]
            if fan and fan > 0 then
                panel:child('label_fan'):setString( LANG{'HU_TOTAL_FAN', value=fan} )
            else
                panel:child('label_fan'):hide()
            end

            -- 胡
            if result_list[i].is_hu then
                panel:child('bg_normal'):removeFromParent()
                fX = fX + 20
                createCard( cmd_data.cbProvideCard, panel_card, fX )  -- 胡的那张牌
                panel:child('img_type'):texture('word/font_over_hu.png')
                panel:child('label_hu'):setString( cmd_data.turnDesc[1][chair_id+1] )
            else
                panel:child('bg_win'):removeFromParent()
                panel:child('label_hu'):removeFromParent()

                if is_liuju then    -- 流局
                    panel:child('img_type'):ignoreContentAdaptWithSize(true)
                    panel:child('img_type'):texture('word/font_over_liuju.png')
                elseif cmd_data.wProvideUser == chair_id then   -- 放炮
                    panel:child('img_type'):texture('word/font_over_fangpao.png')
                else
                    panel:child('img_type'):removeFromParent()
                end
            end

            -- 鸟牌 换行
            if result_list[i].is_hu then
                fX = 0
                local niao_cards = cmd_data.m_cbCardDataNiao[1]
                local niao_status = cmd_data.m_cNiaoStatus[1]
                for j = 1, cmd_data.m_cbNiaoCount do
                    local card = createCard( niao_cards[j], panel_card, fX, 34 )
                    fX = fX + width

                    local color, value = cs.game.util.splitCardValue( niao_cards[j] )
                    if niao_status[j] == 0 or niao_status[j] == chair_id + 1 then
                        card:setColor( cc.c3b(255, 255, 0) )
                    else
                        card:setColor( cc.c3b(200, 200, 200) )
                    end
                end
            end
        end
    end
    template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 关闭界面
-------------------------------------------------------------------------------
function GameResultLayer:doClose()
    if yl.IS_REPLAY_MODEL then
       helper.app.getFromScene('game_room_layer'):onExitRoom()
    else
       local is_room_ended = PassRoom:getInstance().m_bRoomEnd
       if not is_room_ended then
            self._scene.btStart:setVisible(true)
            self._scene:onButtonClickedEvent('btn_start')
       else
            local room_result_layer = helper.app.getFromScene('_room_result_layer_')
            if room_result_layer then
                room_result_layer:show()
            end
       end
    end
    self:removeFromParent()
end


return GameResultLayer
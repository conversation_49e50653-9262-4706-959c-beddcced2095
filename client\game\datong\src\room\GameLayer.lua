local GameModel = appdf.req(appdf.CLIENT_SRC.."system.GameModel")

local GameLayer = class("GameLayer", GameModel)

local cmd = cs.app.game("room.CMD_Game")
local GameLogic = cs.app.game("room.GameLogic")
local GameViewLayer = cs.app.game("room.GameViewLayer")
local ExternalFun =  cs.app.client('external.ExternalFun')

function GameLayer:ctor(frameEngine, scene)
    GameLayer.super.ctor(self, frameEngine, scene)

    cs.game.IS_RECIVE_GAME_RESULT = false
    self.is_zhengdian = false

end

function GameLayer:CreateView()
    return GameViewLayer:create(self):addTo(self)
end

function GameLayer:OnInitGameEngine()
    GameLayer.super.OnInitGameEngine(self)
    self.cbPlayStatus = {0, 0, 0, 0}
    self.cbCardData = {}
    self.cbPartnerCardData = {}
    self.wOutCardIndex = {0, 0, 0, 0}
    self.wPreOutCardIndex = {0, 0, 0, 0}
    self.cbOutCardState = {}
    self.cbWinIndex = {}
    --self.anayseCard = {}
    self.wBankerUser = yl.INVALID_CHAIR
    self.wPeiyin = {0, 0, 0, 0}

    self.cbTimeOperateCard = 10
    self.cbTimeOutCard = 10
    self.cbTimeStartGame = 10
end

function GameLayer:OnResetGameEngine()
    GameLayer.super.OnResetGameEngine(self)
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 语音播放开始
function GameLayer:onUserVoiceStart( useritem, filepath )
    local view_id = self:SwitchViewChairID(useritem.wChairID)
    self._gameView:onUserVoiceStart(view_id)
    return view_id
end

-- 语音播放结束
function GameLayer:onUserVoiceEnded( view_id, filepath )
    self._gameView:onUserVoiceEnded(view_id)
end

function GameLayer:SwitchViewChairID(chair)
    if chair == nil then
        return cmd.MY_VIEWID
    end
    local viewid = yl.INVALID_CHAIR
    local nChairCount = self._gameFrame:GetChairCount()
    if self.MeChairID == nil or self.MeChairID == yl.INVALID_CHAIR then
        self.MeChairID = self:GetMeChairID()
       -- print('self chair id ', self.MeChairID)
    end

    if self.chairid_index then
        --print('nChairID is : ', self.chairid_index, self.MeChairID)
        self.MeChairID = self.chairid_index
    end
    
    local nChairID = self.MeChairID
    if chair ~= yl.INVALID_CHAIR and chair < nChairCount and nChairID ~= yl.INVALID_CHAIR then
        viewid = math.mod(chair + math.floor(cmd.GAME_PLAYER * 3 / 2) - nChairID, cmd.GAME_PLAYER) + 1
        --print('before viewid is : ', viewid, chair, nChairID)
        if nChairCount == 2 and viewid ~= 3 then        -- 两人时对方总在1号位
            viewid = 1
        elseif nChairCount == 3 and viewid == 1 then    -- 三人时1号位总空着
            --if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false and isCheckFix3 then
                if nChairID == 0 then
                    viewid = 2
                elseif nChairID == 2 then
                    viewid = 4
                end
            --end      
        end
    end
    return viewid
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair,time,clockId)
    -- body
    --[[
    if clockId == cmd.IDI_NULLITY then
        if time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_START_GAME then
        if time == 0 then
            self._gameFrame:setEnterAntiCheatRoom(false)--退出防作弊
            self:onExitTable()
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_CALL_BANKER then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CANCEL)
        end
    elseif clockId == cmd.IDI_TIME_USER_ADD_SCORE then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CHIP + 4)
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_TIME_OPEN_CARD then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_OPENCARD)
        end
    end

    --]]
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 用户配音改变
function GameLayer:onUserPeiyinChange(user_item)
    local wChairId = user_item.wChairID
    if self:isNeedIndex() then
        wChairId = self:getIndex(wChairId)
    end
	local view_id = self:SwitchViewChairID(wChairId)
    self.wPeiyin[view_id] = user_item.wPeiyin
    print('user_item is ', user_item.wPeiyin, view_id)
end

-- 刷新房卡数据
function GameLayer:updatePriRoom()
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end

-- 场景信息
function GameLayer:onEventGameScene(cbGameStatus, dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cbGameStatus
    --end
    
	local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            print('GameLayer _gameView ', wViewChairId, userItem.szNickName)
            --dump(userItem)
            --self.wPeiyin[wViewChairId] = userItem.wPeiyin
            --self._gameView:OnUpdateUser(wViewChairId, userItem)
        end
    end
    self._gameView:onResetView()

	if cbGameStatus == cmd.GS_TK_FREE	then				--空闲状态
        self:onSceneFree(dataBuffer)
    elseif cbGameStatus == cmd.GS_TK_PLAYING  then            --游戏状态
        self:onScenePlaying(dataBuffer)
	end

    -- 启动 心跳
    self:startOrStopHeartBeat( true )
    --self:startOrStopReqLocation( true )
    
    self:dismissPopWait()
end

--空闲场景
function GameLayer:onSceneFree(dataBuffer)
    print("onSceneFree")

    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusFree, dataBuffer)
   
    local lCellScore = cmd_table.lCellScore
    --local lRoomStorageStart = dataBuffer:readscore(int64):getvalue()
    --local lRoomStorageCurrent = dataBuffer:readscore(int64):getvalue()
    self.cbTimeOperateCard = cmd_table.cbTimeOperateCard
    self.cbTimeOutCard = cmd_table.cbTimeOutCard
    self.cbTimeStartGame = cmd_table.cbTimeStartGame
  
    self._gameView:setCellScore(lCellScore)
    if not GlobalUserItem.isAntiCheat() then    --非作弊房间
        if not yl.IS_REPLAY_MODEL then
            self._gameView.btStart:setVisible(true)
        end
        
    end
    self.jushu = cmd_table.cbJushu
    self._gameView:showJuShu()
    local chairid = self:GetMeChairID()
    if self.chairid_index then
        chairid = self.chairid_index
    end
    self:SetGameClock(chairid, cmd.IDI_START_GAME, self.cbTimeStartGame)

    --[[
    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbGameRule[1][4] == 1 then
        self.is_zhengdian = true
    end
    --]]

    local tableId = self._gameFrame:GetTableID()
    local userItem = self._gameFrame:getTableUserItem(tableId, self:GetMeChairID())
    if userItem and yl.US_READY == userItem.cbUserStatus then
        self._gameView.btStart:hide()
    end

    self._gameView:changeChairID(cmd_table.wChairID[1])
end

function GameLayer:getIndex(chairid)
    -- body
    for i = 1, cmd.GAME_PLAYER do
        if chairid == self.wOutCardIndex[i] then
            return i - 1
        end
    end

    return 1
end

--游戏场景
function GameLayer:onScenePlaying(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_StatusPlay, dataBuffer)
    --print('self chair id is ', self:GetMeChairID())
    dump(cmd_data)

    self.wCurrentUser = cmd_data.wCurrentUser
    self.wBankerUser = cmd_data.wBankerUser

    if cmd_data.wChairID == self:GetMeChairID() then
        for i = 1, cmd.GAME_PLAYER do
            self.wOutCardIndex[i] = cmd_data.wOutCardIndex[1][i]
        end

        self:getMeChairIDIndex()

        self.wCurrentUserIndex = self:getIndex(self.wCurrentUser)

        self._gameView:changeChairID(cmd_data.wOutCardIndex[1])

        for j = 1, cmd.MAX_COUNT do
            self.cbCardData[j] = cmd_data.cbHandCardData[1][j]
        end
    end
   
    local room_data = PassRoom:getInstance().m_tabPriData
    if cmd_data.cbHandCardCount[1][self:GetMeChairID() + 1] > 0 then
        self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)
        -- dump(self.anayseCard)
        self._gameView:createSelfCard(#self.cbCardData)
        self._gameView:showAllcards()
    end

    --self._gameView.player_num = cmd_data.cbUserCount
    ---self._gameView:gameSendCard(self:SwitchViewChairID(self.wCurrentUser), cmd.GAME_PLAYER * cmd.GAME_CARD_NUM)
    local out_cards = {}
    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.wWinOrder[1][i]
    end
   
    local real_card_num = cmd_data.cbTurnCardCount
    for i = 1, cmd_data.cbTurnCardCount do
        out_cards[i] = cmd_data.cbTurnCardData[1][i]
    end
    
    self:updatePriRoom()
    local banker_index = 0
    for i = 1, cmd.GAME_PLAYER do
        if self.wBankerUser == cmd_data.wOutCardIndex[1][i] then
            banker_index = i - 1
            break
        end
    end
    self._gameView:setBankerUser(self:SwitchViewChairID(banker_index))
    for i = 1, cmd.GAME_PLAYER do
        if self.wCurrentUser == cmd_data.wOutCardIndex[1][i] then
            self.wCurrentUserIndex = i - 1
            break
        end
    end

    if cmd_data.cbHandCardCount[1][self:GetMeChairID() + 1] > 0 and 
        cmd_data.wChairID == self:GetMeChairID() then
            self:removeZeroCard()
        if cmd_data.cbTurnCardCount == 0 and self.wCurrentUser == self:GetMeChairID() then
            self:updatePromptList({}, self.cbCardData)
        else
            self:updatePromptList(out_cards, self.cbCardData)
        end
        
    end
    

    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.wWinOrder[1][i]
    end
 

    self._gameView:showWinUser(true)

    self.jushu = cmd_data.cbJushu

    local outcard_id = cmd_data.wTurnWiner
    outcard_id = self:getIndex(outcard_id)
    self._gameView:gameScenePlaying(outcard_id, out_cards, real_card_num, cmd_data.bIsShowGiveUp)

    local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local chairID = self.wOutCardIndex[i]
        
        local userItem = self._gameFrame:getTableUserItem(tableId, chairID)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:recoverUserScore(wViewChairId, userItem.nTableScore)
            self._gameView:showLeftCardNum(wViewChairId, cmd_data.cbHandCardCount[1][chairID + 1])
            --print('recover score is ', userItem.nTableScore)
            --dump(useritem)
        end
    end

    local partner_id = cmd.INVALID_VIEWID
    for i = 1, cmd.GAME_PLAYER do
        if self.wOutCardIndex[i] == cmd_data.wBuyCardChairID then
            local index = i + 2
            if index > 4 then index = index - 4 end
            partner_id = self.wOutCardIndex[index]
            break
        end
    end

    if self:GetMeChairID() == partner_id and cmd_data.bIsShowBuyCard == true then
        self._gameView:showPartnerBuyCardStatus(true)
    end

    if self:GetMeChairID() == cmd_data.wBuyCardChairID and 
        cmd_data.bIsShowBuyCard == true then
            self._gameView:showGiveupOrBuyPanel()
    end

    self:SetGameClock(self.wCurrentUserIndex, cmd.IDI_TIME_OPEN_CARD, self.cbTimeOutCard or 10)
end

function GameLayer:removeZeroCard( )
    local len = #self.cbCardData
    do 
        local i = 1
        while i <= len do
            if self.cbCardData[i] == 0 then
                table.remove(self.cbCardData, i)
                len = len - 1
            else
                i = i + 1
            end
        end
    end
end

-- 游戏消息
function GameLayer:onEventGameMessage(sub,dataBuffer)
	if sub == cmd.SUB_S_GAME_START then 
		self:onSubGameStart(dataBuffer)
	--elseif sub == cmd.SUB_S_SEND_CARD then 
	--	self:onSubSendCard(dataBuffer)
	elseif sub == cmd.SUB_S_OUT_CARD then 
        self:onSubOutCard(dataBuffer)
    elseif sub == cmd.SUB_S_PASS_CARD then 
        self:onSubPassCard(dataBuffer)
    elseif sub == cmd.SUB_S_BUY_OR_GIVEUP then 
        self:onSubGiveupOrBuy(dataBuffer)
    elseif sub == cmd.SUB_S_START_OUT_CARD then 
        self:onSubStartCard(dataBuffer)
	elseif sub == cmd.SUB_S_PLAYER_EXIT then 
		--self:onSubPlayerExit(dataBuffer)
	elseif sub == cmd.SUB_S_GAME_END then 
		self:onSubGameEnd(dataBuffer)
    elseif sub == cmd.SUB_S_READY_STATE then 
		--self:onSubSendCard(dataBuffer)
        self.game_end = false
	else
		print("unknow gamemessage sub is"..sub)
	end
end

function GameLayer:getPlayNum()
    local num = 0
    for i = 1, cmd.GAME_PLAYER do
        if self.cbPlayStatus[i] == 1 then
            num = num + 1
        end
    end

    return num
end

--游戏开始
function GameLayer:onSubGameStart(dataBuffer)

    self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
    if self._gameView._priView then
        self._gameView._priView:onRefreshInviteBtn()
    end

    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameStart, dataBuffer)

    --dump(cmd_data)
    local wCurrentViewId = self:SwitchViewChairID(cmd_data.wCurrentUser)
    if self:GetMeChairID() ~= cmd_data.wCurrentUser then
        return
    end

    self.wBankerUser = cmd_data.wBankerUser
    self.wCurrentUser = cmd_data.wBankerUser
    self.wRandNum = {}
    self.wBankerIndex = {}
    self.wVecBankerUser = {}
    self.rand_card = cmd_data.cbCard
    self.bChangeSit = cmd_data.bChangeSit
    self.bNeedTurn = cmd_data.bNeedTurn

    for i = 1, cmd.CHANGE_CHAIRID_NUM do
        self.wRandNum[i] = cmd_data.cbRandNum[1][i]
        self.wBankerIndex[i] = cmd_data.wBankerIndex[1][i]
        self.wVecBankerUser[i] = cmd_data.wBanker[1][i]
    end

    for i = 1, cmd.GAME_PLAYER do
        self.wPreOutCardIndex[i] = self.wOutCardIndex[i] 
        self.wOutCardIndex[i] = cmd_data.wOutCardIndex[1][i]
    end

    for i = 1, cmd.GAME_PLAYER do
        if self.wCurrentUser == self.wOutCardIndex[i] then
            self.wCurrentUserIndex = i - 1
            break
        end
    end
    
    for i = 1, cmd.MAX_COUNT do
        self.cbCardData[i] = cmd_data.cbCardData[1][i]
        self.cbPartnerCardData[i] = cmd_data.cbPartnerCardData[1][i]
    end

   -- self._gameView:changeChairID()

   self.jushu = cmd_data.cbJushu
   self._gameView:showJuShu()
    
    self._gameView:startGame() 

    GameLogic:SortCardList(self.cbCardData,#self.cbCardData,GameLogic.ST_ORDER)


    self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)

    self._gameView:createSelfCard(cmd.MAX_COUNT)

    self._gameView:gameSendCard(self:SwitchViewChairID(self.wBankerUser), cmd.GAME_PLAYER * cmd.MAX_COUNT)
    

    self.isFirstOutCard = false
end

function GameLayer:getMeChairIDIndex(  )
    for i = 1, cmd.GAME_PLAYER do
        if self.wOutCardIndex[i] == self:GetMeChairID() then
            self.chairid_index = i - 1
            break
        end
    end
end

-- 出牌 过
function GameLayer:onSubPassCard(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_PassCard, dataBuffer)
    -- 处理出的牌
    --dump(cmd_data)
    local pre_wChairID = self.wOutCardIndex[cmd_data.wPassCardUserIndex]
    local OutChairId = self:SwitchViewChairID(cmd_data.wPassCardUserIndex)
    --print('onSubPassCard ***************', pre_wChairID, OutChairId, cmd_data.wPassCardUserIndex)
    if OutChairId == nil or OutChairId == yl.INVALID_CHAIR then
        return
    end

    helper.music.playPeiyin(self.wPeiyin[OutChairId], 'pass')
    --print('onSubPassCard **************')
    --self._gameView.flag_ready[OutChairId]:hide()
    self._gameView:clearOneOutCard(cmd_data.wPassCardUserIndex)
    self._gameView:showPassOp(cmd_data.wPassCardUserIndex)

    self._gameView:showCardScore(cmd_data.lCardScore[1])

    -- 清理出牌用户的上一轮出牌效果
    self.wCurrentUser = cmd_data.wCurrentUser
    self.wCurrentUserIndex = cmd_data.wCurrentUserIndex

    if cmd_data.wPassCardUserIndex == self.chairid_index then
        self._gameView.bCanMoveCard = false
        self._gameView:setBtnState(false) 
        self._gameView:showGiveupBtnStatus(false, false)
    end

    local curOutChairId = self:SwitchViewChairID(cmd_data.wCurrentUserIndex)

    if curOutChairId == nil or curOutChairId == yl.INVALID_CHAIR then
        return
    end
    print('onSubPassCard 33333333333333333')

    --[[
    if GlobalUserItem.bVoiceAble then
        AudioEngine.playEffect(GameViewLayer.RES_PATH..'sound/pass.mp3')
    end
    --]]
    
    self._gameView.flag_ready[curOutChairId]:hide()
    self._gameView:clearOneOutCard(cmd_data.wCurrentUserIndex)

    if cmd_data.cbTurnOver == 1 then
        self:runAction(cc.Sequence:create(cc.DelayTime:create(0.2), 
        cc.CallFunc:create(function(ref)
            for i = 1, cmd.GAME_PLAYER do
                self._gameView.flag_ready[i]:hide()
            end
            self._gameView:clearOutCard()
        end)
        ))

        self:updatePromptList({}, self.cbCardData, OutChairId, curOutChairId)
    end
    
    if self.wCurrentUser == self:GetMeChairID() then
        self._gameView.bCanMoveCard = true
        if cmd_data.cbTurnOver == 1 then
            self._gameView:setBtnState(true, 1) 
        else
            self._gameView:setBtnState(true, 0) 
        end
        self._gameView:showGiveupBtnStatus(cmd_data.bIsShowGiveUp, true)
    end
 
   self._gameView.m_mapSelectedCards = {}
   self._gameView.m_mapDragSelectCards = {}
   self._gameView.m_bSuggested = false
   self:SetGameClock(cmd_data.wCurrentUserIndex, cmd.IDI_TIME_OUT_CARD, self.cbTimeOutCard)
end

--发牌消息
function GameLayer:onSubSendCard(dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
        if self._gameView._priView then
            self._gameView._priView:onRefreshInviteBtn()
        end
    --end
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_SendCard, dataBuffer)
    self.wCurrentUser = cmd_data.cbFirstUser
    --self.wBankerUser = self.wCurrentUser
    local wCurrentViewId = self:SwitchViewChairID(cmd_data.cbCurrentUser)
    if wCurrentViewId ~= cmd.MY_VIEWID then
        return
    end
	
    for j = 1, cmd.GAME_CARD_NUM do
        self.cbCardData[j] = cmd_data.cbHandCardData[1][j]
    end

    --dump(self.cbCardData)
    self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)
    --dump(self.anayseCard)
    
    local card_num = cmd.GAME_CARD_NUM
    if self.is_zhengdian == false then
        card_num = card_num - 1
    end
    self._gameView:createSelfCard(card_num)

    self._gameView:gameSendCard(self:SwitchViewChairID(self.wBankerUser), cmd.GAME_PLAYER * card_num)
    
end

--用户出牌
function GameLayer:onSubOutCard(dataBuffer)
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_OutCard, dataBuffer)
    --dump(cmd_data)

    -- 处理出的牌
    local OutChairId = self:SwitchViewChairID(cmd_data.cbOutUserIndex)
    if OutChairId == nil or OutChairId == yl.INVALID_CHAIR then
        return
    end
    self.isFirstOutCard = true
    local wOutCardUser = cmd_data.wOutCardUser
    local pre_wChairID = self.wOutCardIndex[cmd_data.cbOutUserIndex]

    self._gameView.flag_ready[OutChairId]:hide()
    self._gameView:clearOneOutCard(cmd_data.wCurrentUserIndex)

    local card_xian = cmd_data.cbGongXian
    --[[
    if cmd_data.bIsBomb == true then
        card_xian = card_xian + 4
    end
    --]]
    self._gameView:showLeftCardNum(OutChairId, cmd_data.cbLeftCardNum)
    self._gameView:showOutCard(cmd_data.cbOutUserIndex, cmd_data.cbCardData[1], cmd_data.cbCardCount, cmd_data.bIsBomb, card_xian)

    if cmd_data.cbOutUserIndex == self.chairid_index then
        self._gameView.bCanMoveCard = false
        self._gameView:setBtnState(false) 
        self._gameView:showGiveupBtnStatus(false, false)
        self:removeCard(cmd_data.cbCardData[1])
        --dump(self.cbCardData)
        self.anayseCard = GameLogic:anayseCard(self.cbCardData, #self.cbCardData)

        self._gameView:createSelfCard(#self.cbCardData, true)
    end
    
    self._gameView.m_mapSelectedCards = {}
    self._gameView.m_mapDragSelectCards = {}
    
    local outCards = {}
    for i = 1, cmd_data.cbCardCount do
        if cmd_data.cbCardData[1][i] == 0 then
            break
        end
        table.insert(outCards, cmd_data.cbCardData[1][i])
    end
    
    --出牌音效
    if cmd_data.cbCardCount > 0 then
        
        local filename = nil
        local card_count = cmd_data.cbCardCount 
        GameLogic:SortCardList(outCards, card_count,GameLogic.ST_ORDER)
        local type = GameLogic:GetCardType(outCards, card_count)
        local card_data = outCards[1]
        local card_v = GameLogic:getCardValue(card_data)
        local is_king = false
        if card_data == 0x4E then
            card_v = 14
            is_king = true
        elseif card_data == 0x4F then
            card_v = 15
            is_king = true
        end

        if type == GameLogic.CT_BOMB or type == GameLogic.CT_BOMB_TW or
            type == GameLogic.CT_BOMB_LINK then
                filename = 'zhadan'
        elseif type == GameLogic.CT_SHUNZI then
            filename = 'shunzi'
        elseif type == GameLogic.CT_THREE then
            filename = 'sange'
        elseif type == GameLogic.CT_THREE_PAIR then
            filename = 'sandaier'
        elseif type == GameLogic.CT_THREE_LINK then
            filename = 'liansan'
        elseif type == GameLogic.CT_DOUBLE_LINK then
            filename = 'liandui'
        elseif type == GameLogic.CT_THREE_PAIR_LINK then
            filename = 'feiji'
        else 
            filename = string.format("%d_%d", card_count, card_v)  
        end

     
        if GlobalUserItem.bVoiceAble then
            --AudioEngine.playEffect(filename)
            helper.music.playPeiyin(self.wPeiyin[OutChairId], filename)

            if type == GameLogic.CT_BOMB_TW then
                AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/rocket.mp3")
            end
        end
    end


    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.wWinOrder[1][i]
    end
 
    self._gameView:showWinUser(true)

    -- 清理出牌用户的上一轮出牌效果
    self.wCurrentUser = cmd_data.wCurrentUser

    local curOutChairId = self:SwitchViewChairID(cmd_data.wCurrentUserIndex)

    if curOutChairId == nil or curOutChairId == yl.INVALID_CHAIR then
        return
    end
    self._gameView.flag_ready[curOutChairId]:hide()
    self._gameView:clearOneOutCard(cmd_data.wCurrentUserIndex)

    self._gameView:showCardScore(cmd_data.lCardScore[1])
    
    self:updatePromptList(outCards, self.cbCardData, OutChairId, curOutChairId)
    
    if self.wCurrentUser == self:GetMeChairID() then
        self._gameView.bCanMoveCard = true
        self._gameView:setBtnState(true, 0) 
        self._gameView:showGiveupBtnStatus(cmd_data.bIsShowGiveUp, true)        
    end
 
   
    self:SetGameClock(cmd_data.wCurrentUserIndex, cmd.IDI_TIME_OUT_CARD, self.cbTimeOutCard)
    
    self._gameView.m_bSuggested = false
    
    
    --AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/OPEN_CARD.wav")
end

function GameLayer:onSubStartCard( dataBuffer )
    -- body
    self._gameView:hideGiveupAndBuyPanel()
    self._gameView:showPartnerBuyCardStatus(false)
   
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_StartOutCard, dataBuffer)
    if cmd_data.wChairId == self:GetMeChairID() then
        self._gameView:showGiveupBtnStatus(false, true)

        self._gameView:showPartnerBuyCardStatus(false)
    end
    if cmd_data.cbIsBuy == 1 then
        local tableId = self._gameFrame:GetTableID()
        local userItemBuy = self._gameFrame:getTableUserItem(tableId, cmd_data.wBuyChairID)
        local userItem = self._gameFrame:getTableUserItem(tableId, cmd_data.wChairID)
        local hint = self._gameView.main_node:child('hint')
        local txt = hint:child('Text_14')
        txt:setString(LANG{'BUY_CARD_TIP', name1 = userItemBuy.szNickName, name2 =  userItem.szNickName})
        hint:show()
        hint:runAction(cc.Sequence:create(
            cc.DelayTime:create(3.0),
            cc.CallFunc:create(
                function (  )
                    hint:hide()
                end
            )
        ))
    end
end

--用户出牌
function GameLayer:onSubGiveupOrBuy(dataBuffer)
    self._gameView:hideGiveupAndBuyPanel()
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_BUYCard, dataBuffer)

    if cmd_data.wPartnerChairID == self:GetMeChairID() then
        self._gameView:showPartnerBuyCardStatus(true)
    end
    

    if self:GetMeChairID() ~= cmd_data.wChairID then
        return 
    end

    self._gameView:showGiveupOrBuyPanel()
end

function GameLayer:removeCard(out_cards)
    for i = 1, #out_cards do
        if out_cards[i] == 0 then
            break
        end
        for j = 1, #self.cbCardData do
            if self.cbCardData[j] == out_cards[i] then
                self.cbCardData[j] = 0
                break
            end
        end
    end
    

    local len = #self.cbCardData
    do 
        local i = 1
        while i <= len do
            if self.cbCardData[i] == 0 then
                table.remove(self.cbCardData, i)
                len = len - 1
            else
                i = i + 1
            end
        end
    end
   
    --print('after reomve card , len is ',  #self.cbCardData)
end

--用户强退
function GameLayer:onSubPlayerExit(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    self.cbPlayStatus[wPlayerID + 1] = 0
    self._gameView.nodePlayer[wViewChairId]:setVisible(false)
    self._gameView.bCanMoveCard = false
    self._gameView.btOpenCard:setVisible(false)
    --self._gameView.btPrompt:setVisible(false)
    self._gameView.spritePrompt:setVisible(false)
    self._gameView.spriteCardBG:setVisible(false)
    self._gameView:setOpenCardVisible(wViewChairId, false)
end

function GameLayer:sendGiveup(bIsGiveup, bIsBuy)
    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_BuyOrGiveUp)
    cmd_data:pushbyte(self.chairid_index)
    cmd_data:pushbyte(bIsGiveup)
    cmd_data:pushbyte(bIsBuy)
    return self:SendData(cmd.SUB_C_BUY_OR_GIVEUP, cmd_data)
end

function GameLayer:sendOp()
    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_OutCard)
   -- cmd_data:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OUT_CARD)
   
    local len = #self._gameView.m_tSelectCards
    cmd_data:pushbyte(len)
    cmd_data:pushbyte(self.chairid_index)
    for i = 1, len do
        cmd_data:pushbyte(self._gameView.m_tSelectCards[i])
    end
    --[[
    for i = len + 1, cmd.MAX_COUNT do
        print('push data is ', i)
        cmd_data:pushbyte(0)
    end
    --]]
    
	return self:SendData(cmd.SUB_C_OUT_CARD, cmd_data)
end

function GameLayer:onPassOutCard(  )
    if self.isFirstOutCard == false then
        return 
    end
    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_Pass)
    --cmd_data:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_PASS_CARD)

    cmd_data:pushbyte(self.chairid_index)
    print('self.chairid_index is ', self.chairid_index)
    
	return self:SendData(cmd.SUB_C_PASS_CARD, cmd_data)
end

function GameLayer:openGameResultLayer()
    
end

--游戏结束
function GameLayer:onSubGameEnd(dataBuffer)
    self.game_end = true
    local cmd_data = ExternalFun.read_netdata(cmd.CMD_S_GameEnd, dataBuffer)
    --dump(cmd_data)
    --self.game_result = cmd_data
    for i = 1, cmd.GAME_PLAYER do
        self.cbWinIndex[i] = cmd_data.wWinOrder[1][i]
    end

    self._gameView.animateCard:stopAllActions()
    self._gameView:gameEnd(cmd_data.lGameScore)

    self._gameView.bCanMoveCard = false

    --self:perform( handler(self, self.openGameResultLayer), 2, 1)

    local cards = {}
    local card_num = cmd_data.cbCardCount
    for i = 1, card_num do
        table.insert(cards, cmd_data.cbCardData[1][i])
    end

    self._gameView:gameEndShowLeftCard(cmd_data.wCurrentUserIndex, cards, card_num)

    if self:GetMeChairID() ~= cmd_data.wCurrentUserIndex then
        return 
    end

    self.cbBombCard = {}
    self.cbBombCardNum = {}
    self.cbBombNum = {}
    self.lBombCardScore = {}
    self.lGameScore = {}

    for i = 1, cmd.GAME_PLAYER do
        self.cbBombNum[i] = cmd_data.cbBombNum[1][i]
        self.lGameScore[i] = cmd_data.lGameScore[1][i]
        self.cbBombCard[i] = {}
        self.cbBombCardNum[i] = {}
        self.lBombCardScore[i] = {}
        for j = 1, 10 do
            self.cbBombCardNum[i][j] = cmd_data.cbBombCardNum[i][j]
            self.lBombCardScore[i][j] = cmd_data.lBombCardScore[i][j]
        end
        for j = 1, cmd.MAX_COUNT do
            self.cbBombCard[i][j] = cmd_data.cbBombCard[i][j]
        end
    end
    
    local path = cs.game.SRC .. 'room.GameResultLayer'
    helper.pop.popLayer(path, nil, {self._gameView, cmd_data, self.wBankerUser, cmd.GAME_PLAYER})

    --local index = self:GetMeChairID() + 1
    

    --self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_END.WAV")
    self._gameView.player_num = cmd.GAME_PLAYER
    print('self.m_userRecord is , ', self._gameView.player_num)
    self.m_userRecord = {}
	for i = 1, self._gameView.player_num do
		self.m_userRecord[i] = {}
		self.m_userRecord[i].cbHuCount =  1
		--self.m_userRecord[i].cbMingGang =  cmd_data.lGameScore[i]
		--self.m_userRecord[i].cbAnGang =  cmd_data.lGameScore[i]
		--self.m_userRecord[i].cbMaCount =  cmd_data.lGameScore[i]
		self.m_userRecord[i].lDetailScore = {}
	end
end

function GameLayer:getCardData(index)
    local index = self:GetMeChairID() + 1
    local data = self.cbCardData[index][i]
    return data
end

function GameLayer:getDetailScore()
	return self.m_userRecord
end

function GameLayer:getUserInfoByChairID(chairId)
	local viewId = self:SwitchViewChairID(chairId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getUserInfoByViewID(viewId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:onExitRoom()
    --self:startOrStopReqLocation( false )
    self:startOrStopHeartBeat( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end 
    end
    self:removeFromParent()
end

-- 刷新界面
function GameLayer:updateView()
end

--开始游戏
function GameLayer:onStartGame()
    -- body
    --self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
    self:KillGameClock()
    self._gameView:onResetView()
    self._gameView:startGame()
    self._gameFrame:SendUserReady()
end

--将视图id转换为普通id
function GameLayer:isPlayerPlaying(viewId)
    if viewId < 1 or viewId > cmd.GAME_PLAYER then
        print("view chair id error!")
        return false
    end

    for i = 1, cmd.GAME_PLAYER do
        if self:SwitchViewChairID(i - 1) == viewId then
            if self.cbPlayStatus[i] == 1 then
                return true
            end
        end
    end

    return false
end

function GameLayer:getMeCardLogicValue(num)
    local index = self:GetMeChairID() + 1

    --此段为测试错误
    if nil == index then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == num then
        showToast(self, "nil == index", 1)
        return false
    end
   
end

function GameLayer:getOxCard(cbCardData)
    return GameLogic:getOxCard(cbCardData)
end

--********************   发送消息     *********************--
function GameLayer:onBanker(cbBanker)
    local dataBuffer = CCmd_Data:create(1)
    --dataBuffer:setcmdinfo(yl.MDM_GF_GAME,cmd.SUB_C_CALL_BANKER)
    dataBuffer:pushbyte(cbBanker)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onAddScore(lScore)
	print("下注金币", lScore)
    local dataBuffer = CCmd_Data:create(8)
    --dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_ADD_SCORE)
    dataBuffer:pushscore(lScore)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onOpenCard(is_special_type)
    local index = self:GetMeChairID() + 1
    local bOx = GameLogic:getOxCard(self.cbCardData[index])
    if is_special_type == nil then
        is_special_type = 0
    end
    
    local dataBuffer = CCmd_Data:create(15)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OPEN_CARD)
    dataBuffer:pushbyte(1)
    for i = 1, cmd.GAME_CARD_NUM do
        if is_special_type == 0 then
            dataBuffer:pushbyte(self._gameView.cbOpenCardData[i]) 
        else
            dataBuffer:pushbyte(self.cbCardData[index]) 
        end
    end
    
    dataBuffer:pushbyte(is_special_type)

    return self._gameFrame:sendSocketData(dataBuffer)
end


-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
       self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
       --print('stop.............replay')
       self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
    if self._gameView.bCanNextReplay then
        if not PassRoom:getInstance():getNetFrame():doNextReplay() then
            self:startOrStopReplay( false )
        end
    end
     
end


-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:stop( yl.ActionTag.HEART )
            
            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )
            
        end
    else
        --print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end

-- 检测心跳
function GameLayer:checkHeartBeat()
    --print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end

-- 重连
function GameLayer:doReConnect()
    --print('网络重连。。。')
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()
    PassRoom:getInstance():getNetFrame():onSearchRoom( PassRoom:getInstance().m_tabPriData.szServerID )
end


-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
       -- print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
        --print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    --print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置 
function GameLayer:onCheckSocketIsOK()
   self:startCheckIsSocketOK( false )
   self.check_is_socket_ok_times = 0
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('subGameResultLayer')
    if view then
        view:removeFromParent()
    end
end

function GameLayer:updatePromptList(cards, handCards, outViewId, curViewId)
    self.m_tabCurrentCards = cards
    self.m_tabPromptList = {}

    print("## 提示 start ")
    local result = {}
    --if outViewId == curViewId then
        --self.m_tabCurrentCards = {}
        --result = GameLogic:SearchOutCard(handCards, #handCards, {}, 0)
    --else
    result = GameLogic:SearchOutCard(handCards, #handCards, cards, #cards)
    --end

    local resultCount = result[1]
   -- print("## 提示牌组 " .. resultCount)
    --dump(result) 
    for i = resultCount, 1, -1 do
        local tmplist = {}
        local total = result[2][i]
        local cards = result[3][i]
        for j = 1, total do
            local cbCardData = cards[j] or 0
            table.insert(tmplist, cbCardData)
        end
        table.insert(self.m_tabPromptList, tmplist)
    end
    self.m_tabPromptCards = self.m_tabPromptList[#self.m_tabPromptList] or {}
    self._gameView.m_promptIdx = 0
end

function GameLayer:isNeedIndex(  )
    local a = self.wOutCardIndex[1] + self.wOutCardIndex[2] + self.wOutCardIndex[3] + self.wOutCardIndex[4]
    if a == 0 then
        return  false
    end
    return true
end

--用户状态
function GameLayer:onEventUserStatus(useritem,newstatus,oldstatus)
    if not self._gameView or not self._gameView.OnUpdateUser then
        return
    end
    local MyTable = self:GetMeTableID()
    local MyChair = self:GetMeChairID()

    if not MyTable or MyTable == yl.INVLAID_TABLE then
        return
    end

    --旧的清除
    if oldstatus.wTableID == MyTable then
        local index = oldstatus.wChairID
        if self:isNeedIndex() then
            index = self:getIndex(oldstatus.wChairID)
        end
        
        local viewid = self:SwitchViewChairID(index)
        if viewid and viewid ~= yl.INVALID_CHAIR then
            self._gameView:OnUpdateUser(viewid, nil)
            if PassRoom then
                PassRoom:getInstance():onEventUserState(viewid, useritem, true)
            end
        end
    end

    --更新新状态
    if newstatus.wTableID == MyTable then
        local index = newstatus.wChairID
        if self:isNeedIndex() then
            index = self:getIndex(newstatus.wChairID)
        end
        local viewid = self:SwitchViewChairID(index)
        if viewid and viewid ~= yl.INVALID_CHAIR then
            self._gameView:OnUpdateUser(viewid, useritem)
            if PassRoom then
                PassRoom:getInstance():onEventUserState(viewid, useritem, false)
            end
        end
    end
    -- 定位没开启就用这个
    if not yl.IS_LOCATION_OPEN and GlobalUserItem.bPrivateRoom then
        if self.checkSameIP then
            self:checkSameIP()
        end
    end
    if self._gameView and self._gameView.updateLabsInfo then
       local isNeedWaring = false
       if newstatus.cbUserStatus and oldstatus.cbUserStatus and newstatus.cbUserStatus == yl.US_SIT and oldstatus.cbUserStatus <= yl.US_SIT then
           isNeedWaring = true
       end
       --print('测试数据', newstatus.wChairID, '新状态',newstatus.cbUserStatus,'老状态',oldstatus.cbUserStatus )
       self._gameView:updateLabsInfo( isNeedWaring )
    end
end

--用户积分
function GameLayer:onEventUserScore(useritem)
    if not self._gameView or not self._gameView.OnUpdateUser then
        return
    end
    local MyTable = self:GetMeTableID()
    
    if not MyTable or MyTable == yl.INVLAID_TABLE then
        return
    end 

    if  MyTable == useritem.wTableID then
        local index = useritem.wChairID
        if self:isNeedIndex() then
            index = self:getIndex(useritem.wChairID)
        end
        local viewid = self:SwitchViewChairID(index)
        if viewid and viewid ~= yl.INVALID_CHAIR then
            self._gameView:OnUpdateUser(viewid, useritem)
        end
    end 
end


return GameLayer
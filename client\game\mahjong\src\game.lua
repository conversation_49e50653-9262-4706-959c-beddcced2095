-------------------------------------------------------------------------------
--  创世版1.0
--  麻将
--  游戏配置
--      访问方式：cs.game.RES
--  @date 2017-06-05
--  @auth woodoo
-------------------------------------------------------------------------------
local name = 'mahjong'
local game = {
    DISCARD_IMMEDIATELY = true,
    -- 游戏模块名
    MODULE_NAME = name,
    -- 资源路径
    RES = cs.app.GAME_ROOT .. '/' .. name .. '/res/',
    -- 脚本路径
    SRC = cs.app.GAME_ROOT .. '.' .. name .. '.src.',
    -- 是否有小结算界面
    HAS_GAME_RESULT = true,
    -- 必然存在的配音文件
    MUST_PEIYIN = 'b_1wan.mp3',

    ----------------------------游戏逻辑---------------------------------------
    -- 是否可续打
    CAN_CONTINUE = true,
    -- 是否听牌提示
    TING_PROMPT = false,
    -- 最多人数
    MAX_PLAYER = 4,
    -- 品牌
    BRAND = '云来',
    -- 类型配置
    [301] = {
        NAME        = '三门麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8',
        RENSHU      = '4;3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',       -- 支付
        ZHIFU_DESC  = '<R>(已限时五折);2',
        WANFA_COL   = 3,
        WANFA       = '0,1,0|1,1,0|2,1,1;3,1,1;4,1,1', -- group,set_value,checked,return
        RULE0       = '不买马',
        RULE1       = '买一马',
        RULE2       = '买二马',
        RULE3       = '三摊承包',
        RULE3_NONE  = '不可三摊承包',
        RULE4       = '有财神',
        RULE4_NONE  = '无财神',
    },
        -- 类型配置
    [300] = {
        NAME        = '百搭麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8;24局,12;32局,16',
        RENSHU      = '4;3;2',
        DIFEN       = '1',
        WANFA_COL   = 2,
        ZHIFU       = '0;1',       -- 支付
        ZHIFU_DESC  = '<R>(限时特惠);2,3',
        WANFA       = '0,1,1|1,1,0', -- group,set_value,checked,return
        RULE0       = '抓2鸟',
        RULE1       = '抓4鸟',
    },
    [302] = {
        NAME        = '深圳麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8;24局,12;32局,16',
        RENSHU      = '4;3;2',
        DIFEN       = '1',
        WANFA_COL   = 2,
        ZHIFU       = '0;1',       -- 支付
        ZHIFU_DESC  = '<R>(限时特惠);2,3',
        WANFA       = '0,1,1|0,0,0;1,1,1|1,0,0;2,1,1|2,0,0', -- group,set_value,checked,return
        RULE0       = '流局算杠分',
	    RULE0_NONE  = '流局不算杠分',
	    RULE1  	    = '流局翻倍',
	    RULE1_NONE  = '流局不翻倍',
	    RULE2  	    = '可点炮',
	    RULE2_NONE  = '不可点炮',
    },
    [304] = {
        NAME        = '天台麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8',
        RENSHU      = '3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',       -- 支付
		ZHIFU_DESC  = '<R>(限时免费);3',        
        WANFA_COL   = 3,
        WANFA       = '0,1,1|1,1,0|2,1,0,1;3,1,1|4,1,0,1;5,1,1|6,1,0', -- group,set_value,checked,return
        RULE0       = '不买马',
        RULE1       = '买一马',
        RULE2       = '买二马',
        RULE3       = '300胡封顶',
        RULE4       = '500胡封顶',
        RULE5       = '首局房主庄',
        RULE6       = '首局随机庄',
    },
    [305] = {
        NAME        = '台州麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8',
        RENSHU      = '4;3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',       -- 支付
        WANFA_COL   = 3,
        WANFA       = '0,1,1', -- group,set_value,checked,return
        RULE0       = '台州玩法',
   },
   [306] = {
        NAME        = '寿县麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '1圈,4;2圈,8',
        RENSHU      = '4;3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',     -- 支付
        WANFA_COL   = 3,
        WANFA       = '0,1,1|0,0,0;1,1,0;2,1,0;3,1,0', -- group,set_value,checked,return
        RULE0       = '可放冲',
        RULE0_NONE  = '只自摸',
        RULE1       = '杠上开花翻倍',
        RULE2       = '独一翻倍',
        RULE3       = '幺九翻倍',
        },
   [307] = {
        NAME        = '舒城麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '1圈,4;2圈,8',
        RENSHU      = '4;3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',       -- 支付
        WANFA_COL   = 3,
        WANFA       = '0,1,1|1,1,0|2,1,0', -- group,set_value,checked,return
        RULE0       = '可放冲',
        RULE1       = '只自摸',
        RULE2       = '只自摸(加豹子)',
   },
   [308] = {
        NAME        = '霍邱麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '1圈,4;2圈,8',
        RENSHU      = '4;3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',    -- 支付
        WANFA_COL   = 3,
        WANFA       = '0,1,1',  -- group,set_value,checked,return
        RULE0       = '霍邱玩法',
   },
   [309] = {
        NAME        = '花麻麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        QUAN_FENG	= true,		-- 圈风标记
        JUSHU       = '8局,4;16局,8;24局,12;32局,16',
        RENSHU      = '4;3;2',
        DIFEN       = '1',
        WANFA_COL   = 2,
        WANFA_HEIGHT= 200,
        ZHIFU       = '0;1',       -- 支付
        ZHIFU_DESC  = '<R>(限时特惠);2,3',
        WANFA       = '0,1,1|1,1,0|2,1,0,1;3,1,1|4,1,0|5,1,0|6,1,0;7,1,1;8,1,0;9,1,0;10,1,0', -- group,set_value,checked,return
        RULE0       = '8花',
        RULE1  		= '12花',
        RULE2  	    = '24花',
        RULE3  		= '无花即可胡',
        RULE4  	    = '一花自摸二花放炮',
        RULE5  		= '一花自摸三花放炮',
		RULE6  		= '二花自摸三花放炮',
		RULE7  		= '可吃',
		RULE7_NONE  = '不可吃',
		RULE8  		= '无花果',
		RULE9  		= '杠开花翻倍（底不翻）',
		RULE10  	= '对对胡花翻倍（底不翻）',
    },
    [401] = {
        NAME        = '温岭麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8',
        SHENGPAI_DESC = '包牌',
        RENSHU      = '4;3;2',
        DIFEN       = '1;2;5;10;20',
        ZHIFU       = '0;1',    -- 支付
        WANFA_COL   = 3,
        WANFA       = '0,1,1|1,1,0',  -- group,set_value,checked,return
        RULE0       = '小闹玩法',
        RULE1       = '大闹玩法',
   },
   [402] = {
        NAME        = '杜桥麻将',
        ZHUANG      = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        FENG        = true,     -- 庄标记，zhuang"庄" | feng"东南西北"
        JUSHU       = '8局,4;16局,8;打孔400倒,12',
        SHENGPAI_DESC = '承包',
        RENSHU      = '4;3;2',
        DIFEN       = '1',
        ZHIFU       = '0;1',       -- 支付
        WANFA_COL   = 3,
        WANFA       = '0,1,1', -- group,set_value,checked,return
        RULE0       = '有杠开',
        RULE0_NONE  = '无杠开',
    },
}

helper.app.addGameSearchPath(name, appdf.PACK_PATH)

local function loadExt(ext)
    local path = string.gsub( game.SRC, '%.', '/' ) .. 'game_ext.' .. ext
    if cc.FileUtils:getInstance():isFileExist(path) then
        local game_ext = require(game.SRC .. 'game_ext')
        -- 注意，此处不能用元表，因为要保证game_ext中的内容优先
        --setmetatable(game, {__index = game_ext})
        for k, v in pairs(game_ext) do
            game[k] = v
        end
        return true
    end
end
local ret = loadExt('luac') or loadExt('lua')


return game

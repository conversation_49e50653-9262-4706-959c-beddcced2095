<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_room_act_jiezhi_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{844,156},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{858,0},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{844,86},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{788,0},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{774,148},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{704,148},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_15.png</key>
            <dict>
                <key>frame</key>
                <string>{{518,164},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_16.png</key>
            <dict>
                <key>frame</key>
                <string>{{448,164},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_17.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,190},{1,1}}</string>
                <key>offset</key>
                <string>{-124.5,124.5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{1,1}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{588,162},{114,86}}</string>
                <key>offset</key>
                <string>{-1,-15}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{67,97},{114,86}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{674,0},{146,112}}</string>
                <key>offset</key>
                <string>{-1,-2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{51,71},{146,112}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{540,0},{160,132}}</string>
                <key>offset</key>
                <string>{-1,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{44,57},{160,132}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{378,0},{162,160}}</string>
                <key>offset</key>
                <string>{0,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{44,43},{162,160}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,0},{190,186}}</string>
                <key>offset</key>
                <string>{-1,3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{29,29},{190,186}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{188,188}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{31,31},{188,188}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{378,164},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
            <key>ani_room_act_jiezhi_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{378,164},{68,84}}</string>
                <key>offset</key>
                <string>{0,-16}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{91,99},{68,84}}</string>
                <key>sourceSize</key>
                <string>{250,250}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_room_act_jiezhi.png</string>
            <key>size</key>
            <string>{934,248}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d6730c6e23c4223dcf4550d2a7c069fd:6e38abbe4ed6d3d75445a8c98ac6ca55:1b8f241b709cb0d7acaa34ded34717fa$</string>
            <key>textureFileName</key>
            <string>ani_room_act_jiezhi.png</string>
        </dict>
    </dict>
</plist>

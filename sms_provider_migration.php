<?php
/**
 * 短信服务商迁移方案
 * 从云之讯迁移到腾讯云短信或阿里云短信
 */

class TencentCloudSMS {
    private $secretId;
    private $secretKey;
    private $appId;
    private $signName;
    
    public function __construct($config) {
        $this->secretId = $config['secretId'];
        $this->secretKey = $config['secretKey'];
        $this->appId = $config['appId'];
        $this->signName = $config['signName'];
    }
    
    /**
     * 发送验证码短信
     */
    public function sendVerifyCode($phone, $code) {
        // 腾讯云短信API实现
        $url = 'https://sms.tencentcloudapi.com/';
        
        $params = array(
            'Action' => 'SendSms',
            'Version' => '2021-01-11',
            'Region' => 'ap-beijing',
            'SmsSdkAppId' => $this->appId,
            'SignName' => $this->signName,
            'TemplateId' => '1234567', // 替换为实际模板ID
            'PhoneNumberSet' => array($phone),
            'TemplateParamSet' => array($code)
        );
        
        // 生成签名和发送请求的代码...
        // 这里需要实现腾讯云的签名算法
        
        return $this->sendRequest($url, $params);
    }
    
    private function sendRequest($url, $params) {
        // HTTP请求实现
        // 返回结果
    }
}

class AliCloudSMS {
    private $accessKeyId;
    private $accessKeySecret;
    private $signName;
    
    public function __construct($config) {
        $this->accessKeyId = $config['accessKeyId'];
        $this->accessKeySecret = $config['accessKeySecret'];
        $this->signName = $config['signName'];
    }
    
    /**
     * 发送验证码短信
     */
    public function sendVerifyCode($phone, $code) {
        // 阿里云短信API实现
        $url = 'https://dysmsapi.aliyuncs.com/';
        
        $params = array(
            'Action' => 'SendSms',
            'Version' => '2017-05-25',
            'RegionId' => 'cn-hangzhou',
            'PhoneNumbers' => $phone,
            'SignName' => $this->signName,
            'TemplateCode' => 'SMS_123456789', // 替换为实际模板代码
            'TemplateParam' => json_encode(array('code' => $code))
        );
        
        // 生成签名和发送请求的代码...
        
        return $this->sendRequest($url, $params);
    }
    
    private function sendRequest($url, $params) {
        // HTTP请求实现
        // 返回结果
    }
}

/**
 * 修改后的user.php中的get_verify_code_post方法
 */
function get_verify_code_post_new() {
    $role_id = $this->input->post('uid');
    $phone = $this->input->post('phone');
    $type = $this->input->post('type') ? $this->input->post('type') : 'bind';
    
    // 生成4位随机验证码
    $code = rand(1000, 9999);
    
    // 选择可用的短信服务商
    $smsResult = false;
    
    // 方案1: 尝试腾讯云短信
    try {
        $tencentConfig = array(
            'secretId' => 'YOUR_TENCENT_SECRET_ID',
            'secretKey' => 'YOUR_TENCENT_SECRET_KEY',
            'appId' => 'YOUR_TENCENT_APP_ID',
            'signName' => '您的签名'
        );
        
        $tencentSMS = new TencentCloudSMS($tencentConfig);
        $smsResult = $tencentSMS->sendVerifyCode($phone, $code);
        
        if ($smsResult['success']) {
            error_log("腾讯云短信发送成功: $phone, $code");
        }
    } catch (Exception $e) {
        error_log("腾讯云短信发送失败: " . $e->getMessage());
    }
    
    // 方案2: 如果腾讯云失败，尝试阿里云
    if (!$smsResult['success']) {
        try {
            $aliConfig = array(
                'accessKeyId' => 'YOUR_ALI_ACCESS_KEY_ID',
                'accessKeySecret' => 'YOUR_ALI_ACCESS_KEY_SECRET',
                'signName' => '您的签名'
            );
            
            $aliSMS = new AliCloudSMS($aliConfig);
            $smsResult = $aliSMS->sendVerifyCode($phone, $code);
            
            if ($smsResult['success']) {
                error_log("阿里云短信发送成功: $phone, $code");
            }
        } catch (Exception $e) {
            error_log("阿里云短信发送失败: " . $e->getMessage());
        }
    }
    
    // 方案3: 如果都失败，尝试原来的云之讯（作为备用）
    if (!$smsResult['success']) {
        try {
            // 原来的云之讯代码...
            require(APPPATH . '/libraries/Ucpaas.class.php');
            $options['accountsid'] = '5cbc351f17a418686094747a62ffd946';
            $options['token'] = 'fac1c2af0c05327677d33916ba841079';
            $ucpass = new Ucpaas($options);
            $appId = "9dc983c028e54d5fbc97228e6af5344e";
            $templateId = "174333";
            $result_json = $ucpass->templateSMS($appId, $phone, $templateId, $code);
            
            // 解析结果...
            
        } catch (Exception $e) {
            error_log("云之讯短信发送失败: " . $e->getMessage());
        }
    }
    
    // 无论短信是否发送成功，都保存验证码到数据库
    // 这样可以在短信服务异常时，通过其他方式（如客服）提供验证码
    $data = array(
        'role_id' => $role_id,
        'game_id' => $this->_channel['game_id'],
        'create_time' => time(),
        'phone' => $phone,
        'type' => $type,
        'code' => $code,
        'sms_status' => $smsResult['success'] ? 1 : 0, // 记录短信发送状态
        'sms_provider' => $smsResult['provider'] ?? 'unknown' // 记录使用的服务商
    );
    
    $this->db->insert('tuo3_verify_code', $data);
    
    if ($smsResult['success']) {
        $this->response(array('code' => 0, 'msg' => '验证码发送成功'));
    } else {
        // 即使短信发送失败，也可以返回成功，让用户联系客服获取验证码
        $this->response(array('code' => 0, 'msg' => '验证码发送中，如未收到请联系客服'));
    }
}

?>

<!-- 
使用说明:

1. 申请腾讯云短信服务:
   - 登录腾讯云控制台
   - 开通短信服务
   - 创建应用和模板
   - 获取 SecretId 和 SecretKey

2. 申请阿里云短信服务:
   - 登录阿里云控制台  
   - 开通短信服务
   - 创建签名和模板
   - 获取 AccessKey

3. 修改数据库表结构:
   ALTER TABLE tuo3_verify_code ADD COLUMN sms_status TINYINT DEFAULT 0;
   ALTER TABLE tuo3_verify_code ADD COLUMN sms_provider VARCHAR(50) DEFAULT '';

4. 配置新的短信服务商参数

5. 测试新的短信功能

优势:
- 多重备用方案，提高成功率
- 记录短信发送状态，便于排查问题
- 即使短信失败也能保存验证码，可通过客服处理
-->

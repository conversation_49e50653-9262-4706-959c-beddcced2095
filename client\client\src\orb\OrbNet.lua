-------------------------------------------------------------------------------
--  创世版3.0
--  拆红包 - 网络交换
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------

local urls = {
    index = '/api/v1/dismantle/index',
    open = '/api/v1/dismantle/open',
    draw = '/api/v1/dismantle/withdraw',
    reward = '/api/v1/dismantle/reward'
}

local OrbNet = class("OrbNet")


-------------------------------------------------------------------------------
-- 请求
-------------------------------------------------------------------------------
function OrbNet.request(url, params, callback)
    ---[[
    url = urls[url]
    params = params or {}
    params.uid = GlobalUserItem.dwUserID
    yl.GetUrl(url, 'post', params, callback)
    --]]
    --[[
    local data = {
	    code = 0,
	    msg = "",
    }
    if url == 'index' then
        data.data = {
		    id = 1,
    	    role_id = 1,
    	    game_id = 1,
            balance = 0,									-- 红包余额
            time_left = 10000,                              -- 剩余时间
            status = 0,      								-- 红包状态 0：未拆 1：拆了9.9 2：拆了3.3 3：已结束
            one_status = 1,                                 -- 1元红包状态 0 = 未达标 1已达标 2已领取
            open_logs = {                
            },
            withdraw_logs = {
            },
            notices = {
                {nickname = '三千笔墨绘倾城', money = 15.1},
                {nickname = '醉清霜', money = 16.3},
            }
	    }
    elseif url == 'open' then
        if not params.is_share then
            data.data = {
                balance = 9.99,									-- 红包余额
                status = 1,      								-- 红包状态 0：未拆 1：拆了9.9 2：拆了3.3 3：已结束
                log = {
                    id = 1, 
                    from_id = 1,                                -- 来源用户id
                    from_avatar = "https://wx.qlogo.cn/mmopen/vi_32/H0GqDP7ibHo4qflNmqt52rV0PmdRInYgicjJMPxOLVJH6wYKcGFrazHR9K1luU61q2G80RUsXhz6rghWa51WxVRw/132",       -- 来源用户头像
                    from_nickname = "东斜",					    -- 来源用户昵称
                    to_id = 1,                                  -- 目标用户id
                    total_fee = 9.99,       				    -- 拆得的金额
                    type = "own",        						-- 类型 own 自己拆，share自己分享，reg 注册，play 玩游戏
                    create_time = "2019-01-01 10:00:00"			-- 时间
                }
            }
        else
            data.data = {
                balance = 13.32,									-- 红包余额
                status = 2,      								-- 红包状态 0：未拆 1：拆了9.9 2：拆了3.3 3：已结束
                log = {
                    id = 1, 
                    from_id = 1,                                -- 来源用户id
                    from_avatar = "https://wx.qlogo.cn/mmopen/vi_32/H0GqDP7ibHo4qflNmqt52rV0PmdRInYgicjJMPxOLVJH6wYKcGFrazHR9K1luU61q2G80RUsXhz6rghWa51WxVRw/132",       -- 来源用户头像
                    from_nickname = "东斜",					    -- 来源用户昵称
                    to_id = 1,                                  -- 目标用户id
                    total_fee = 3.33,       				    -- 拆得的金额
                    type = "share",        						-- 类型 own 自己拆，share自己分享，reg 注册，play 玩游戏
                    create_time = "2019-01-01 10:00:00"			-- 时间
                }
            }
        end
    elseif url == 'draw' then
        if params.type == 'one' then
            data.data = {
                log = {
                    id = 1,                             
                    total_fee = 1,                          -- 提现金额
                    status =  2,                                -- 提现状态 0 未审核 1审核通过 2 已领取
                    create_time = "2019-01-01 10:00:00"         -- 提现日期
                },
            }
        else
            data.data = {
                log = {
                    id = 2,                             
                    total_fee = 15.99,                          -- 提现金额
                    status =  1,                                -- 提现状态 0 未审核 1审核通过 2 已领取
                    create_time = "2019-01-01 10:00:00"         -- 提现日期
                },
            }
        end
    elseif url == 'reward' then
        data.data = {
            id = 2
        }
    end
    callback(data, '', 0)
    --]]
end


return OrbNet

-------------------------------------------------------------------------------
--  创世版1.0
--  金币场玩法主页
--  @date 2018-03-05
--  @auth woodoo
-------------------------------------------------------------------------------
local GoldKindLayer = class("GoldKindLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function <PERSON><PERSON>indLayer:ctor(kind_config)
    self.m_game = kind_config.game
    self.m_kind = kind_config.kind
    self:enableNodeEvents()
    self:setName('gold_kind_layer')

    -- 载入主UI
    local main_node = helper.app.loadCSB('GoldKindLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    -- 背景保持一致
    local bg_index = GlobalUserItem.nBackground
    local path = 'common/bg_main' .. (bg_index == 1 and '' or '_' .. bg_index) .. '.jpg'
    if cc.FileUtils:getInstance():isFileExist(path) then
        self.main_node:child('bg_main'):texture(path)
    end

    main_node:child('template'):hide()

    -- 初始化TopBar
    local top_bar = main_node:child('top_bar')
    top_bar:child('img_title'):texture('word/font_gold_title_' .. self.m_kind .. '.png')
    top_bar:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    top_bar:child('btn_quan_exchange'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnQuanExchange) )
    top_bar:child('btn_add_gold'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAddGold) )
    self:updateGold( GlobalUserItem.lUserScore, GlobalUserItem.dwTicket )

    helper.app.addGoldLabelEvent(top_bar:child('label_gold'))

    --main_node:child('btn_free_gold'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnFreeGold) )
    main_node:child('btn_free_gold'):hide() -- 暂时隐藏
    main_node:child('btn_rule'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRule) )
    main_node:child('btn_quick_start'):hide():addTouchEventListener( handler(self, self.onBtnQuickStart) )

    local listview = main_node:child('listview')
    helper.layout.pushEmpty(listview, {23, 23})
    local panel_left = main_node:child('panel_left')
    if yl.is_reviewing or kind_config.no_private then
        panel_left:removeFromParent()
    else
        panel_left:retain()
        panel_left:removeFromParent()
        listview:pushBackCustomItem(panel_left)
        panel_left:release()
        panel_left:child('btn_private'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnPrivate) )
        panel_left:child('btn_redarena'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnRedarena) )
        helper.layout.pushEmpty(listview, {5, 5})
    end

    self:getSpecialServer()

    -- 查询服务器状态
    local main_scene = helper.app.getFromScene('main_scene')
    main_scene:requestServerInfo(self.m_kind, handler(self, self.onServerInfo))
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function GoldKindLayer:onEnter()
    print('GoldKindLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GoldKindLayer:onExit()
    print('GoldKindLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnBack(sender)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 券兑换按钮点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnQuanExchange(sender)
    helper.link.toMall( LANG.MALL_TAB_QUAN )
end


-------------------------------------------------------------------------------
-- 加金币按钮点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnAddGold(sender)
    helper.link.toMall( LANG.MALL_TAB_GOLD )
end


-------------------------------------------------------------------------------
-- 免费金币按钮点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnFreeGold(sender)
    local share = helper.pop.shareImage('common/share_image.jpg', false, LANG.SHARE_GOLD, 'share_gold')
    share:hideHY()
end


-------------------------------------------------------------------------------
-- 玩法按钮点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnRule(sender)
    helper.link.toRule(self.m_kind)
end


-------------------------------------------------------------------------------
-- 约战点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnPrivate(sender)
    if not self.m_private_server then
        helper.pop.message( LANG.OPEN_SOON )
        return
    end
    local kind = self.m_kind
    helper.app.checkGameUpdate(self.m_game, kind, function()
        local path = cs.app.CLIENT_SRC .. 'main.RoomCreateLayer'
        helper.pop.popLayer(path, nil, kind):setName('room_create_layer')
    end)
end


-------------------------------------------------------------------------------
-- 红包赛点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnRedarena(sender)
    local this = self
    if not this.m_redarena_server then
        helper.pop.message( LANG.OPEN_SOON )
        return
    end
    helper.app.checkGameUpdate(this.m_game, this.m_kind, function()
        helper.pop.popLayer(cs.app.CLIENT_SRC .. 'main.RedArenaLayer', nil, {this.m_kind, this.m_redarena_server})
    end)
end


-------------------------------------------------------------------------------
-- 快速开始点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnQuickStart(sender, event)
    helper.app.commClickEffect(sender, event)
    if event ~= cc.EventCode.ENDED then return end

    if self.m_quick_start_server or self.m_first_server then   
        -- 开始创建后禁用创建按钮一段时间
        sender:setTouchEnabled(false)
        sender:perform(function()
            sender:setTouchEnabled(true)
        end, 3)

        local server = self.m_quick_start_server or self.m_first_server
        self:startGame(server)
    end
end


-------------------------------------------------------------------------------
-- 更新金币
-------------------------------------------------------------------------------
function GoldKindLayer:updateGold(gold, quan)
    if gold then
        self.main_node:child('top_bar/label_gold'):setString( helper.str.makeFormatNum(gold, 1) )
    end
    if quan then
        self.main_node:child('top_bar/label_quan'):setString(quan)
    end
end


-------------------------------------------------------------------------------
-- 获取私人房及红包赛服务器
-------------------------------------------------------------------------------
function GoldKindLayer:getSpecialServer()
    local data = GlobalUserItem.roomlist
    for i = 1, #data do
        local kind = data[i][1]
        local rooms = data[i][2]
        for k, v in ipairs(rooms) do
            if v.wKindID == self.m_kind then
                if v.wServerType == yl.GAME_GENRE_PERSONAL then
                    self.m_private_server = v.wServerID
                elseif v.wServerType == yl.GAME_GENRE_GOLD and v.cbSubType == 2 then
                    self.m_redarena_server = v.wServerID
                end
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 服务器状态返回
-------------------------------------------------------------------------------
function GoldKindLayer:onServerInfo(servers)
    self.m_server_infos = {}
    for i, v in ipairs(servers) do
        self.m_server_infos[v.wServerID] = v
    end

    local btn_quick_start = self.main_node:child('btn_quick_start'):show()
    btn_quick_start:py(btn_quick_start:py() - 100):runAction( cc.MoveBy:create(0.2, cc.p(0, 100)) )

    self:createEntries()
end


-------------------------------------------------------------------------------
-- 请求入口列表
-------------------------------------------------------------------------------
function GoldKindLayer:createEntries()
    local all_list = {}
    local data = GlobalUserItem.roomlist
    for i = 1, #data do
        local kind = data[i][1]
        local rooms = data[i][2]
        for k, v in ipairs(rooms) do
            if v.wServerType == yl.GAME_GENRE_GOLD and v.cbSubType == 0 and v.wKindID == self.m_kind then
                table.insert(all_list, v)
                if self.m_server_infos[v.wServerID] then
                    v.dwOnLineCount = self.m_server_infos[v.wServerID].wOnlineCount
                end
            end
        end
    end
    table.sort(all_list, function(v1, v2) return v1.wServerLevel < v2.wServerLevel end)
    self:initEntries(all_list)
end


-------------------------------------------------------------------------------
-- 初始化玩法
-------------------------------------------------------------------------------
function GoldKindLayer:initEntries(kinds)
    local listview = self.main_node:child('listview')
    local template = self.main_node:child('template')
    local l_size = listview:size()
    local i_size = template:size()
    local row_count = 2
    local row_gap = 13
    
    local col
    for i, config in ipairs(kinds) do
        if not self.m_first_server then
            self.m_first_server = config.wServerID
        end

        local info = self.m_server_infos[config.wServerID]
        if info and info.bIsDefault then
            self.m_quick_start_server = config.wServerID
        end

        local kind = config.wKindID
        if i % row_count == 1 then
            col = ccui.Layout:create()
            col:setClippingEnabled(true)
            col:size(i_size.width, i_size.height * row_count + (row_count - 1) * row_gap + 9)   -- 9是为了和左边的约战按钮对齐
            listview:pushBackCustomItem(col)
        end
        item = template:clone():show()
        item.server_id = config.wServerID
        item:setCascadeColorEnabled(true)
        item:addTouchEventListener( handler(self, self.onBtnEntry) )
        item:child('bg'):texture('common/bg_gold_level_' .. config.wServerLevel .. '.png')
        item:child('atlas_difen'):setString( config.lCellScore )
        item:child('img_difen'):px(item:child('atlas_difen'):px() + item:child('atlas_difen'):size().width/2 + item:child('img_difen'):size().width/2)
        item:child('label_desc'):setString( config.szRoomDesc )
        local base_online = info and info.wOnlineCount or config.dwOnLineCount
        local num = (base_online + 3) * 7 + math.random(1, 10) -- 在线人数=（真实在牌桌内人数+3）*7+10以内随机数
        item:child('label_player'):setString(num)
        item:child('img_hot'):setVisible( num >= 100 )

        local row = (i - 1) % row_count + 1
        item:pos(i_size.width, (row_count - row) * (i_size.height + row_gap))
        item:addTo(col)

        item:runAction( cc.Sequence:create(
            cc.DelayTime:create(0.06*(i-1)),
            cc.EaseExponentialOut:create( cc.MoveBy:create(0.6, cc.p(-i_size.width, 0)) )
        ) )
    end

    template:removeFromParent()
    listview:jumpToLeft()
end


-------------------------------------------------------------------------------
-- 玩法点击
-------------------------------------------------------------------------------
function GoldKindLayer:onBtnEntry(sender, event)
    helper.app.tintClickEffect(sender, event)
    if event ~= cc.EventCode.ENDED then return end

    -- 开始创建后禁用创建按钮一段时间
    sender:setTouchEnabled(false)
    sender:perform(function()
        sender:setTouchEnabled(true)
    end, 3)

    self:startGame(sender.server_id)
end


-------------------------------------------------------------------------------
-- 登录房间
-------------------------------------------------------------------------------
function GoldKindLayer:startGame(server)
    local kind = self.m_kind
    helper.app.checkGameUpdate(self.m_game, kind, function()
        local main_scene = helper.app.getFromScene('main_scene')
        if main_scene and main_scene.updateQuickKind then
            main_scene:updateQuickKind(kind)
        end
        PassRoom:getInstance():onLoginServer(server)
    end)
end


return GoldKindLayer
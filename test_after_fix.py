#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import time
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def generate_sign(params, channel_key):
    """生成签名"""
    sorted_keys = sorted(params.keys())
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    sign_str = param_str + channel_key
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()

def test_sms_after_apache_fix():
    """Apache 修复后测试短信接口"""
    print("=== Apache 修复后测试短信接口 ===")
    
    # 基础参数
    params = {
        'uid': '123456',
        'phone': '13800138000',  # 请替换为您的真实手机号
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sign = generate_sign(params, channel_key)
    params['sign'] = sign
    
    # 测试不同的 URL
    test_urls = [
        'http://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code',
        'https://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code',
        'http://lhmj.tuo3.com.cn:8607/admin/api/v1/user/get_verify_code',
    ]
    
    for url in test_urls:
        print(f"\n测试: {url}")
        
        try:
            if url.startswith('https'):
                response = requests.post(url, data=params, timeout=30, verify=False)
            else:
                response = requests.post(url, data=params, timeout=30)
            
            print(f"状态码: {response.status_code}")
            print(f"响应长度: {len(response.text)}")
            
            if response.status_code == 200:
                print(f"响应内容: {response.text}")
                try:
                    json_data = response.json()
                    print(f"JSON数据: {json_data}")
                    
                    if json_data.get('code') == 0:
                        print("✅ 短信发送成功！Apache 修复生效！")
                        return True
                    else:
                        print(f"⚠️ 短信发送失败: {json_data.get('msg', '未知错误')}")
                        print("💡 可能需要检查短信服务商配置或余额")
                except Exception as e:
                    print(f"⚠️ JSON解析失败: {e}")
                    print("💡 可能返回的是 HTML 错误页面")
            elif response.status_code == 502:
                print("❌ 仍然是 502 错误，Apache 可能还没完全修复")
            elif response.status_code == 404:
                print("❌ 404 错误，API 路径可能不正确")
            elif response.status_code == 500:
                print("❌ 500 错误，后端 PHP 代码可能有问题")
            else:
                print(f"⚠️ 其他状态码: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL 错误: {e}")
            print("💡 可能需要检查 SSL 证书配置")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
            print("💡 Apache 可能还没启动或端口仍被占用")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    return False

def test_basic_connectivity():
    """测试基本连通性"""
    print("=== 测试基本连通性 ===")
    
    test_urls = [
        'http://lhmj.tuo3.com.cn/',
        'https://lhmj.tuo3.com.cn/',
        'http://lhmj.tuo3.com.cn:8607/',
    ]
    
    for url in test_urls:
        print(f"\n测试: {url}")
        
        try:
            if url.startswith('https'):
                response = requests.get(url, timeout=10, verify=False)
            else:
                response = requests.get(url, timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 连接成功")
                print(f"响应长度: {len(response.text)} 字符")
                # 显示前100个字符
                content_preview = response.text[:100].replace('\n', ' ').replace('\r', ' ')
                print(f"内容预览: {content_preview}...")
            elif response.status_code == 502:
                print("❌ 502 Bad Gateway - Apache 后端仍有问题")
            elif response.status_code == 403:
                print("⚠️ 403 Forbidden - 权限问题")
            elif response.status_code == 404:
                print("⚠️ 404 Not Found - 路径不存在")
            else:
                print(f"⚠️ 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")

if __name__ == '__main__':
    print("Apache 修复后的测试...")
    print("请确保已经：")
    print("1. 解决了端口 443 的占用问题")
    print("2. 重启了 Apache 服务")
    print("3. 检查了 Apache 错误日志")
    print()
    
    # 先测试基本连通性
    test_basic_connectivity()
    
    print("\n" + "="*60 + "\n")
    
    # 再测试短信接口
    success = test_sms_after_apache_fix()
    
    if success:
        print("\n🎉 恭喜！短信验证接口已恢复正常！")
        print("现在可以重新启用客户端的手机登录功能了。")
    else:
        print("\n💡 如果仍有问题，请检查：")
        print("1. Apache 错误日志 (E:\\xampp\\apache\\logs\\error.log)")
        print("2. PHP 错误日志")
        print("3. 短信服务商配置")
        print("4. 数据库连接")
        print("5. 防火墙设置")

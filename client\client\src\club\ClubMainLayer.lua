-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部主场景
--  @date 2018-01-13
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubBrowser = cs.app.client('club.ClubBrowser')
local ClubUtil = cs.app.client('club.ClubUtil')


local REFRESH_MEMBERS_INTERVAL  = 6       -- 刷新成员定时器间隔
local REFRESH_MEMBERS_TAG       = 2387    -- 刷新成员定时器Tag
local REFRESH_ROOMS_INTERVAL    = 5       -- 刷新房间定时器间隔
local REFRESH_ROOMS_TAG         = 2388    -- 刷新房间定时器Tag


local ClubMainLayer = class("ClubMainLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubMainLayer:ctor(target_name, params)
    print('ClubMainLayer:ctor...')
    self.m_init_target = target_name
    self.m_init_params = params

    self.m_clubs = {}
    self:setName('club_main_layer')
    ClubBrowser.clean()

    local main_node = ClubUtil.initUI(self, 'ClubMainLayer.csb')

    main_node:child('tab_template'):hide()
    main_node:child('member_template'):hide()
    main_node:child('room_template'):hide()
    main_node:child('btn_diy_manager'):hide()

    local full_size = main_node:child('listview_member'):size()
    main_node:child('listview_member')._full_size = full_size
    main_node:child('listview_member')._mini_size = cc.size(full_size.width, full_size.height - 78)

    self:rollMessage('')

    helper.logic.addListenerByName(self, {main_node:child('btn_add, btn_my_club, btn_invite, btn_diy, btn_diy_manager')})


    local btn_add = self.main_node:child('btn_add')
    btn_add:retain()
    btn_add:removeFromParent()
    self.main_node:child('listview_club'):pushBackCustomItem(btn_add)
    btn_add:release()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubMainLayer:onEnter()
    print('ClubMainLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_INFO, self, self.onClubListResp)
    ClubUtil.listen(cmd.SUB_CLUB_MEMBERS_REFRESH, self, self.onMemberListResp)
    ClubUtil.listen(cmd.SUB_CLUB_PLAYER_COUNT, self, self.onMemberCountResp)
    ClubUtil.listen(cmd.SUB_CLUB_TABLE_CREATE, self, self.onDiyCreateResp)
    ClubUtil.listen(cmd.SUB_CLUB_TABLE_LIST, self, self.onRoomListResp)
    ClubUtil.listen(cmd.SUB_CLUB_TABLE_JOIN, self, self.onRoomJoinResp)
    ClubUtil.listen(cmd.SUB_CLUB_APPLY_COUNT, self, self.onApplyCountResp)
    ClubUtil.listen(cmd.SUB_CLUB_RULE_ADD, self, self.onAddResp)
    
    ClubUtil.send(cmd.SUB_CLUB_INFO, cmd.CMD_GR_ID, {})
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubMainLayer:onExit()
    print('ClubMainLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 激活(弹窗关闭后可能调用)
-------------------------------------------------------------------------------
function ClubMainLayer:onActive(pre_layer)
    if #self.m_clubs == 0 then
        if pre_layer and pre_layer:getName() == 'club_create_layer' then
            -- 创建界面返回时
            self:removeFromParent()
        else
            -- 从其它界面返回（解散、退出等导致已经没有俱乐部），要显示创建
            ClubUtil.open(self, 'club.ClubCreateLayer')
        end

    else
        local listview = self.main_node:child('listview_club')
        for i, tab in ipairs(listview:getItems()) do
            if tab.club then
                tab:child('text'):setString(tab.club.szName)
                if self.m_cur_club == tab.club then
                    self:onBtnClubTab(tab)
                end
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 俱乐部列表返回
-------------------------------------------------------------------------------
function ClubMainLayer:onClubListResp(data)
    local clubs = LiveFrame:getInstance():resp(data, cmd.tagClub, true)
    if not clubs then return end

    self.m_clubs = clubs

    -- 判断是否有俱乐部
    if #clubs > 0 then
        self:createClubTabs()

        if self.m_init_target == 'join' then
            ClubUtil.open(self, 'club.ClubCreateLayer', nil, self.m_init_params)
        end
    else
        ClubUtil.open(self, 'club.ClubCreateLayer', nil, self.m_init_params)
    end

    self.m_init_target = nil
    self.m_init_params = nil
end


-------------------------------------------------------------------------------
-- 创建Tab
-------------------------------------------------------------------------------
function ClubMainLayer:createOneTab(club)
    local listview = self.main_node:child('listview_club')
    local template = self.main_node:child('tab_template')
    club.is_join = club.dwPresidentID ~= GlobalUserItem.dwUserID
    club.apply_num = 0
    local tab = template:clone():show()
    tab.club = club
    tab.group = 1
    tab:child('text'):setString(club.szName)
    tab._uncheck_color = tab:child('text'):getTextColor()
    helper.layout.scaleToWidth(tab:child('text'), template:size().width * 0.9)
    helper.logic.initImageCheck(tab, false, 'common/btn_tab_rect_top_s.png', 'common/btn_tab_rect_top_n.png', 
        handler(self, self.onBtnClubTab))

    listview:insertCustomItem(tab, #listview:getItems() - 1)

    if not club.is_join then
        ClubUtil.sendApplyCountCmd(club.dwClubID)
    end
end


-------------------------------------------------------------------------------
-- 俱乐部Tab列表生成
-------------------------------------------------------------------------------
function ClubMainLayer:createClubTabs()
    local clubs = self.m_clubs
    local listview = self.main_node:child('listview_club')
    listview:setTouchEnabled(false)
    local template = self.main_node:child('tab_template')
    for i, club in ipairs(clubs) do repeat
        self:createOneTab(club)
    until true end

	local first_item = listview:getItem(0)
	local last_id = cc.UserDefault:getInstance():getIntegerForKey('lastclubid', 0)
	if last_id > 0 then
		for i, item in ipairs( listview:getItems() ) do
			if item.club and item.club.dwClubID == last_id then
				first_item = item
                helper.layout.scrollToItem(listview, first_item)
				break
			end
		end
	end

    first_item:toggle()
    self:onBtnClubTab(first_item)
end


-------------------------------------------------------------------------------
-- 俱乐部Tab点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnClubTab(sender)
    self.m_cur_club = sender.club
	cc.UserDefault:getInstance():setIntegerForKey('lastclubid', self.m_cur_club.dwClubID)
    local listview_member, listview_room, panel_notice, btn_diy_manager = self.main_node:child('listview_member, listview_room, panel_notice, btn_diy_manager')
    listview_member:removeAllItems()
    listview_room:removeAllItems()
    self.m_member_nodes = {}
    self.m_room_nodes = {}

    -- 会长特殊处理
    listview_member:size(self.m_cur_club.is_join and listview_member._full_size or listview_member._mini_size)
    btn_diy_manager:setVisible(not self.m_cur_club.is_join)

    self.main_node:child('label_free'):setString( LANG{'CLUB_FREE_COUNT', count=0} )
    self.main_node:child('label_busy'):setString( LANG{'CLUB_BUSY_COUNT', count=0} )

    -- 显示当前俱乐部好友
    self:stop(TAG_REFRESH_MEMBERS):perform(function()
        if not self:isVisible() then return end
        self.m_member_open = true   -- 成员列表是否开启(必要时关闭，防止管理界面的查看成员乱入）
        self.m_members = {}
        self:sendMemberList(0)
    end, REFRESH_MEMBERS_INTERVAL, -1, TAG_REFRESH_MEMBERS, true)

    -- 显示当前俱乐部房间
    self:stop(REFRESH_ROOMS_TAG):perform(handler(self, self.refreshClubRooms), REFRESH_ROOMS_INTERVAL, -1, REFRESH_ROOMS_TAG, true)

    -- 滚动消息
    self:rollMessage(self.m_cur_club.szMsg)
end


-------------------------------------------------------------------------------
-- 刷新当前俱乐部房间
-------------------------------------------------------------------------------
function ClubMainLayer:refreshClubRooms()
    if not self:isVisible() then return end
    self.m_rooms = {}
    self:sendRoomList(0)
end


-------------------------------------------------------------------------------
-- 滚动消息
-------------------------------------------------------------------------------
function ClubMainLayer:rollMessage(text)
    local panel_notice = self.main_node:child('panel_notice')
    local mask = self.main_node:child('panel_notice/mask')
	local msg = mask:child('label_notice')
    msg:stop():setString(text)

    -- 没有公告隐藏UI且调整列表高度
    local listview_room = self.main_node:child('listview_room')
    local full_size = listview_room._full_size
    if not full_size then
        listview_room._full_size = listview_room:size()
        full_size = listview_room._full_size
    end
    if text == '' then
        panel_notice:hide()
        listview_room:size(full_size)
        return
    else
        panel_notice:show()
        listview_room:size(full_size.width, full_size.height - 54)
    end

    local size = mask:size()
	local distance = size.width + msg:size().width
	local duration = distance / 100
    local start_pos = cc.p(ISLEFT and size.width or 0, size.height/2)
	msg:opacity(0):anchor(ISLEFT and 0 or 1, 0.5)
	msg:runMyAction( cc.RepeatForever:create( cc.Sequence:create(
        cc.Place:create(start_pos),
		cc.Spawn:create(
			cc.FadeIn:create(1),
			cc.MoveBy:create(duration, cc.p( (ISLEFT and -1 or 1) * distance, 0 )),
			cc.Sequence:create( cc.DelayTime:create(duration - 1), cc.FadeOut:create(0.5) )
		)
	) ) )
end


-------------------------------------------------------------------------------
-- 发送当前俱乐部成员列表命令
-------------------------------------------------------------------------------
function ClubMainLayer:sendMemberList(pos)
    if not self.m_cur_club then return end

    local values = {dwID=self.m_cur_club.dwClubID, nValue=pos, nValue2=1}
    ClubUtil.send(cmd.SUB_CLUB_MEMBERS_REFRESH, cmd.CMD_GR_IDValue, values)
end


-------------------------------------------------------------------------------
-- 成员状态数量变化通知
-------------------------------------------------------------------------------
function ClubMainLayer:onMemberCountResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd.tagClubPlayerCount)
    if not ret or not self.m_cur_club then return end

    self.main_node:child('label_free'):setString( LANG{'CLUB_FREE_COUNT', count=ret.nFreeCount} )
    self.main_node:child('label_busy'):setString( LANG{'CLUB_BUSY_COUNT', count=ret.nBusyCount} )
end


-------------------------------------------------------------------------------
-- 成员列表生成
-------------------------------------------------------------------------------
function ClubMainLayer:onMemberListResp(data)
    if not self.m_member_open then return end
    local members = LiveFrame:getInstance():resp(data, cmd.tagClubMember, true)
    if not members or not self.m_cur_club then return end

    local is_end = #members < ClubUtil.PER_PAGE -- 本页不足10条，认为没有下一页了
    if #members > 0 then
        if members[1].dwClubID ~= self.m_cur_club.dwClubID then return end  -- 防止其它请求的响应

        local template = self.main_node:child('member_template')
        for i, member in ipairs(members) do
            if member.cbStatus == 0 then    -- 已经出现离线，可以结束
                is_end = true
                break
            end
            self.m_members[member.dwUserID] = member
        end
    end

    -- 是否需要继续请求
    if not is_end then
        self:sendMemberList(members[#members].wIndex)
    else
        -- 所有数据下载完毕，开始处理...
        -- 数据处理，先分桌
        local tables = {}
        for uid, member in pairs(self.m_members) do
            local t = tables[member.dwLockServerID .. '-' .. member.wClubTableID]
            if not t then
                t = {}
                tables[member.dwLockServerID .. '-' .. member.wClubTableID] = t
            end
            table.insert(t, member)
        end

        -- 创建桌子列表
        local listview = self.main_node:child('listview_member')
        local template = self.main_node:child('member_template')
        for tid, members in pairs(tables) do
            local item = self.m_member_nodes[tid]
            if not item then
                item = template:clone():show()
                self.m_member_nodes[tid] = item
                for i = 1, 4 do
                    local panel = item:child('panel_' .. i)
                    if i <= #members then
                        local member = members[i]
                        panel.uid = member.dwUserID
                        ClubUtil.createPlayerHead(panel, member.dwUserID, member.szHeadHttp)
                    else
                        panel.uid = nil
                    end
                end
                listview:pushBackCustomItem(item)
            else
                -- 桌子已存在，更新头像列表
                local mm_ids = {}   -- 最新的uid列表
                for i, member in ipairs(members) do
                    mm_ids[member.dwUserID] = true
                end

                -- 删除已不存在的头像
                local mm_indexes = {}   -- 每个头像的位置
                local empties = {}      -- 可用空位
                for i = 1, 4 do
                    local uid = item:child('panel_' .. i).uid
                    if uid then -- 位置有人
                        if not mm_ids[uid] then -- uid不在最新的用户列表中，表示原来有，现已不在
                            item:child('panel_' .. i):removeAllChildren()
                            item:child('panel_' .. i).uid = nil
                            uid = nil
                        else
                            mm_indexes[uid] = i
                        end
                    end
                    if not uid then
                        table.insert(empties, i)
                    end
                end

                -- 需新增的头像
                for i, member in ipairs(members) do
                    if not mm_indexes[member.dwUserID] and #empties > 0 then -- 原来没有，需新增
                        local index = empties[1]
                        table.remove(empties, 1)
                        item:child('panel_' .. index).uid = member.dwUserID
                        ClubUtil.createPlayerHead(item:child('panel_' .. index), member.dwUserID, member.szHeadHttp)
                    end
                end
            end

            -- 局数信息显示
            local index = members[1].nPlayIndex
            local total = members[1].nPlayCount
            local ju_str = total > 0 and LANG{'CLUB_ROOM_JUSHU', count=index, limit=total} or LANG{'CLUB_ROOM_JUSHU_KUN', count=index}
            item:child('label_ju'):setString(ju_str)
        end

        -- 已不存在桌子删除
        for tid, item in pairs(self.m_member_nodes) do
            if not tables[tid] then
                self.m_member_nodes[tid] = nil
                listview:removeItem( listview:getIndex(item) )
            end
        end

        self.m_member_open = false
    end
end


-------------------------------------------------------------------------------
-- 发送当前俱乐部房间列表命令
-------------------------------------------------------------------------------
function ClubMainLayer:sendRoomList(pos)
    if not self.m_cur_club then return end

    ClubUtil.send(cmd.SUB_CLUB_TABLE_LIST, cmd.CMD_GR_ID, {dwID=self.m_cur_club.dwClubID})
end


-------------------------------------------------------------------------------
-- 房间列表生成
-------------------------------------------------------------------------------
function ClubMainLayer:onRoomListResp(data)
    local rooms = LiveFrame:getInstance():resp(data, cmd.tagClubRuleTable, true)
    if not rooms or not self.m_cur_club then return end

    local listview = self.main_node:child('listview_room')
    if #rooms > 0  then
        if rooms[1].rule.dwClubID ~= self.m_cur_club.dwClubID then return end  -- 防止切换tab后错误的响应

        local template = self.main_node:child('room_template')
        for i, room in ipairs(rooms) do
            local flag_id = room.rule.dwRuleID .. '-' .. room.dwClubTableSeqID
            self.m_rooms[flag_id] = room
            local item = self.m_room_nodes[flag_id]
            if not item then
                item = template:clone():show()
                self.m_room_nodes[flag_id] = item
                item:child('icon'):ignoreContentAdaptWithSize(true)
                item:child('icon'):texture('common/icon_kind_' .. room.rule.wKindID .. '.png'):scale(0.7)
                item:child('btn_start'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnRoomStart) )
                listview:pushBackCustomItem(item)
            end
            item.room = room
            item:child('btn_start/label_num'):setString(room.cbPlayerCount .. '/' .. room.rule.wJoinGamePeopleCount)
            item:child('label_rule'):setString(room.rule.szRule)

            for i=1, 4 do
                -- 占位显示：大于已加入人数且小于总人数
                item:child('panel_avator/p' .. i):setVisible(i > room.cbPlayerCount and i <= room.rule.wJoinGamePeopleCount)
                local panel = item:child('panel_avator/panel_' .. i)
                panel:removeAllChildren()
                if i <= room.cbPlayerCount then
                    ClubUtil.createPlayerHead(panel, room.dwUserID[1][i], room.szHeadHttp[1][i])
                end
            end
        end
    end

    -- 旧节点删除
    for id, item in pairs(self.m_room_nodes) do
        if not self.m_rooms[id] then
            self.m_room_nodes[id] = nil
            listview:removeItem( listview:getIndex(item) )
        end
    end
end


-------------------------------------------------------------------------------
-- 房间开始按钮点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnRoomStart(sender)
    self:sendRoomJoinCmd(sender:getParent().room)
end


-------------------------------------------------------------------------------
-- 发送当前俱乐部成员列表命令
-------------------------------------------------------------------------------
function ClubMainLayer:sendRoomJoinCmd(room)
    local values = {dwID=room.rule.dwClubID, nValue=room.rule.dwRuleID, nValue2=room.rule.dwOwnerID}
    ClubUtil.send(cmd.SUB_CLUB_TABLE_JOIN, cmd.CMD_GR_IDValue, values)
end


-------------------------------------------------------------------------------
-- 房间加入返回
-------------------------------------------------------------------------------
function ClubMainLayer:onRoomJoinResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDValueMsg, function(ret)
        if tolua.isnull(self) then return end
        self:enterRoom(ret.nValue)
    end)
end


-------------------------------------------------------------------------------
-- 申请检查返回
-------------------------------------------------------------------------------
function ClubMainLayer:onApplyCountResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd.CMD_GR_IDValue)
    if not ret then return end

    local need_red = false
    for i, club in ipairs(self.m_clubs) do
        if club.dwClubID == ret.dwID then
            club.apply_num = ret.nValue
        end
        if not club.is_join and club.apply_num > 0 then
            need_red = true
        end
    end
    ClubUtil.addRedPoint(self.main_node:child('btn_my_club'), need_red)
end


-------------------------------------------------------------------------------
-- 我的俱乐部按钮点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnMyClub(sender)
    if #self.m_clubs == 0 then return end
    ClubUtil.open(self, 'club.ClubListLayer', nil, {self.m_clubs} )
end


-------------------------------------------------------------------------------
-- 增加俱乐部加号点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnAdd(sender)
    ClubUtil.open(self, 'club.ClubCreateLayer' )
end


-------------------------------------------------------------------------------
-- 邀请朋友按钮点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnInvite(sender)
    if not self.m_cur_club then return end
    ClubUtil.shareClub(self.m_cur_club.dwClubID, self.m_cur_club.szName)
end


-------------------------------------------------------------------------------
-- 自定义桌按钮点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnDiy(sender)
    if not self.m_cur_club then return end
    ClubUtil.showKindCreator({}, handler(self, self.onDiyDone))
end


-------------------------------------------------------------------------------
-- 会长快速自定义桌入口按钮点击
-------------------------------------------------------------------------------
function ClubMainLayer:onBtnDiyManager(sender)
    if not self.m_cur_club then return end
    ClubUtil.showKindCreator({}, handler(self, self.onDiyManagerDone))
end


-------------------------------------------------------------------------------
-- 会长创建常用桌结果
-------------------------------------------------------------------------------
function ClubMainLayer:onDiyManagerDone(param)
    ClubUtil.sendKindCmd(cmd.SUB_CLUB_RULE_ADD, 0, self.m_cur_club.dwClubID, param)
    self:perform(handler(self, self.refreshClubRooms), 0.5) -- 立即执行刷新
end


-------------------------------------------------------------------------------
-- 新增玩法返回(主要是会长增加常用玩法响应，只需要错误提示，同时作为ClubKindLayer的提示)
-------------------------------------------------------------------------------
function ClubMainLayer:onAddResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd.tagClubRuleResult)
    if not ret then return end
    if ret.szMsg ~= '' then
        helper.pop.message(ret.szMsg)
        return
    end
end


-------------------------------------------------------------------------------
-- 自定义桌结果
-------------------------------------------------------------------------------
function ClubMainLayer:onDiyDone(param)
    ClubUtil.sendKindCmd(cmd.SUB_CLUB_TABLE_CREATE, 0, self.m_cur_club.dwClubID, param)
end


-------------------------------------------------------------------------------
-- 自定义桌返回
-------------------------------------------------------------------------------
function ClubMainLayer:onDiyCreateResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDValueMsg, function(ret)
        if tolua.isnull(self) then return end
        self:enterRoom(ret.nValue)
    end)
end


-------------------------------------------------------------------------------
-- 俱乐部创建(ClubCreateLayer调用)
-------------------------------------------------------------------------------
function ClubMainLayer:addClubs(clubs)
    for i, club in ipairs(clubs) do
        self:createOneTab(club)
        table.insert(self.m_clubs, club)
    end

    if not self.m_cur_club then
	    local first_item = self.main_node:child('listview_club'):getItem(0)
        first_item:toggle()
        self:onBtnClubTab(first_item)
    end
end


-------------------------------------------------------------------------------
-- 移除俱乐部(解散或退出俱乐部时调用)
-------------------------------------------------------------------------------
function ClubMainLayer:removeClub(club)
    local listview = self.main_node:child('listview_club')
    local items = listview:getItems()
    for i, item in ipairs(items) do
        if item.club and item.club.dwClubID == club.dwClubID then
            listview:removeItem(i - 1)
            table.remove(items, i)
        end
    end

    if #items == 1 then -- 剩下“+”
        self.m_cur_club = nil
        self.main_node:child('listview_club'):removeAllItems()
        self.main_node:child('listview_member'):removeAllItems()
    else
        items[1]:toggle()
        self:onBtnClubTab(items[1])
    end
end


-------------------------------------------------------------------------------
-- 进入房间
-------------------------------------------------------------------------------
function ClubMainLayer:enterRoom(room_id)
    room_id = string.format('%06d', room_id)
    helper.app.checkRoom(function()
        helper.pop.waiting()
        PassRoom:getInstance():getNetFrame():onSearchRoom( room_id, yl.SEARCH_ROOM_TYPE_JOIN )
    end, nil, room_id)
end


return ClubMainLayer

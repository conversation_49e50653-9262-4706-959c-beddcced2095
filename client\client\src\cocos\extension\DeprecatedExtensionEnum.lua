if nil == cc.Control then
    return
end

_G.kCCControlStepperPartMinus        = cc.CONTROL_STEPPER_PART_MINUS
_G.kCCControlStepperPartPlus         = cc.CONTROL_STEPPER_PART_PLUS
_G.kCCControlStepperPartNone         = cc.CONTROL_STEPPER_PART_NONE

_G.CCControlEventTouchDown       = cc.CONTROL_EVENTTYPE_TOUCH_DOWN
_G.CCControlEventTouchDragInside = cc.CONTROL_EVENTTYPE_DRAG_INSIDE 
_G.CCControlEventTouchDragOutside = cc.CONTROL_EVENTTYPE_DRAG_OUTSIDE
_G.CCControlEventTouchDragEnter = cc.CONTROL_EVENTTYPE_DRAG_ENTER
_G.CCControlEventTouchDragExit  = cc.CONTROL_EVENTTYPE_DRAG_EXIT
_G.CCControlEventTouchUpInside  = cc.CONTROL_EVENTTYPE_TOUCH_UP_INSIDE
_G.CCControlEventTouchUpOutside = cc.CONTROL_EVENTTYPE_TOUCH_UP_OUTSIDE
_G.CCControlEventTouchCancel    = cc.CONTROL_EVENTTYPE_TOUCH_CANCEL
_G.CCControlEventValueChanged   = cc.CONTROL_EVENTTYPE_VALUE_CHANGED 
_G.CCControlStateNormal         = cc.CONTROL_STATE_NORMAL
_G.CCControlStateHighlighted    = cc.CONTROL_STATE_HIGH_LIGHTED
_G.CCControlStateDisabled       = cc.CONTROL_STATE_DISABLED
_G.CCControlStateSelected       = cc.CONTROL_STATE_SELECTED

_G.kCCScrollViewDirectionHorizontal        = cc.SCROLLVIEW_DIRECTION_HORIZONTAL
_G.kCCScrollViewDirectionVertical          = cc.SCROLLVIEW_DIRECTION_VERTICAL
_G.kCCTableViewFillTopDown                 = cc.TABLEVIEW_FILL_TOPDOWN
_G.kCCTableViewFillBottomUp                = cc.TABLEVIEW_FILL_BOTTOMUP

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_gold_pig_light_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{324,486},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{282,768},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{162,486},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,768},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,486},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{282,324},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,324},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{282,162},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,162},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{282,0},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
            <key>ani_gold_pig_light_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{280,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{280,160}}</string>
                <key>sourceSize</key>
                <string>{280,160}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_gold_pig_light.png</string>
            <key>size</key>
            <string>{562,928}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:90a5b9093b9438d95546da323bc3212b:e7fda2618e6e1c962ca8f2658cb44137:49543f40555eb2b04e0b36a9e4d78898$</string>
            <key>textureFileName</key>
            <string>ani_gold_pig_light.png</string>
        </dict>
    </dict>
</plist>

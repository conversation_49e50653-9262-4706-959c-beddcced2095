-------------------------------------------------------------------------------
--  创世版1.0
--  局结算(小结算)
--  @date 2017-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')

local CARD_PATH = 'card'

local GameResultLayer = class("GameResultLayer", function(scene)
    return helper.app.loadCSB('GameResultLayer.csb')
end)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GameResultLayer:ctor(scene, result_list, cmd_data, zhuang_chair_id)
    CARD_PATH = GlobalUserItem.getCardPath()    -- 重置为当前玩法的资源路径

    self.m_shuffle_card = 0
    self.is_gold = not GlobalUserItem.bPrivateRoom
    self._scene = scene
    self:child('template'):hide()
    
    self:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    self:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )

    local is_room_ended = PassRoom:getInstance().m_bRoomEnd
    local is_open_shuffle = yl.app:getServerConfig().is_open_shuffle
    if yl.IS_REPLAY_MODEL or self.is_gold or is_room_ended or is_open_shuffle == 0 then
        self:child('btn_shuffle'):hide()
    else
        self:child('btn_shuffle'):show():zorder(1):px(display.cx)
        self:child('btn_share'):px(display.cx - self:child('btn_share'):size().width - 20)
        self:child('btn_continue'):px(display.cx + self:child('btn_continue'):size().width + 20)
        self:child('label_difen'):px(self:child('label_difen'):px() - 10)
        self:child('btn_shuffle'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShuffle) )

        -- 洗牌卡数量及动画
        local game_layer = helper.app.getFromScene('game_room_layer')
        local card_num = 0
        if game_layer then card_num = game_layer.m_shuffle_card or 0 end
        self.m_shuffle_card = card_num
        local str = card_num == 0 and LANG.ROOM_SHUFFLE_CARD0 or LANG{'ROOM_SHUFFLE_CARD', num = card_num}
        self:child('btn_shuffle'):child('tip/label'):setString(str)
        self:child('btn_shuffle'):child('tip'):runMyAction( cc.RepeatForever:create( cc.Sequence:create(
            cc.MoveBy:create(0.6, cc.p(0, 10)),
            cc.MoveBy:create(0.6, cc.p(0, -10))
        ) ) )
    end

    -- 金币场处理
    if self.is_gold then
        self:child('btn_exit'):px(display.cx):show():addTouchEventListener( helper.app.commClickHandler(self, self.onBtnExit) )
        self:child('btn_share'):px(display.cx - self:child('btn_share'):size().width - 40)
        self:child('btn_continue'):px(display.cx + self:child('btn_continue'):size().width + 40)
        self:child('label_difen'):px(self:child('label_difen'):px() - 10)

        -- 非普通金币场（不能中途退出，且超时自动点击“继续游戏”按钮）
        if not GlobalUserItem.bCommonGold then
            self:child('btn_exit'):hide()
            if not PassRoom:getInstance().m_bRoomEnd then
                self:perform(function()
                    if tolua.isnull(self) then return end
                    self:onBtnContinue()
                end, 10)
            end
        end
    end

    -- 后面会有全部隐藏显示动作，先做标记，否则会被错误的显示出来
    for i, child in ipairs(self:getChildren()) do
        child._hide_original = not child:isVisible()
    end

    -- 有些情况下会有存在旧的小结算
    local last_layer = helper.app.getFromScene('subGameResultLayer')
    if last_layer then
       last_layer:removeFromParent() 
    end

    self:setName('subGameResultLayer')
    
    self.cbPlayerMaxCardsNum = cmd_data.cbPlayerMaxCardsNum
    -- 是否有扎鸟
    if cmd_data.m_cbNiaoCount > 0 then
        self:showNiao(result_list, cmd_data, zhuang_chair_id)
    end

    self:showResult(result_list, cmd_data, zhuang_chair_id)
end


-------------------------------------------------------------------------------
-- 注册关闭hook
-------------------------------------------------------------------------------
function GameResultLayer:addCloseHook(func)
    if not self.m_close_hooks then self.m_close_hooks = {} end
    table.insert(self.m_close_hooks, func)
end


-------------------------------------------------------------------------------
-- 关闭（注意，所有self:removeFromParent用该方法代替）
-------------------------------------------------------------------------------
function GameResultLayer:closeSelf(func)
    if self.m_close_hooks then
        for i, func in ipairs(self.m_close_hooks) do
            func()
        end
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 动态显示
-------------------------------------------------------------------------------
function GameResultLayer:effectShow()
    self:show()
    if self.m_effect_shows_niao and #self.m_effect_shows_niao > 0 then
        for i, func in ipairs(self.m_effect_shows_niao) do
            func()
        end
        self.m_effect_shows_niao = nil
        return
    end

    if self.m_effect_shows and #self.m_effect_shows > 0 then
        -- 先显示界面元素
        for i, child in ipairs(self:getChildren()) do
            if not child._hide_original then
                child:show()
            end
        end

        for i, func in ipairs(self.m_effect_shows) do
            func()
        end
        self.m_effect_shows = nil

        -- 红包赛特殊处理
        if GlobalUserItem.bIsRedArena and not self.m_is_liuju then
            cs.app.client('main.RedArenaDialog').showDialog( GlobalUserItem.nCurGameKind )
        end

        return
    end
end


-------------------------------------------------------------------------------
-- 退出按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnExit(sender)
    -- 只有金币场才有有退出按钮
    if tolua.isnull(self._scene) or tolua.isnull(self._scene._scene) then   -- 可能底下房间已经退出
        self:closeSelf()
    else
        helper.pop.alert(LANG.ROOM_EXIT_GOLD_QUERY, function()
            if not tolua.isnull(self._scene) and not tolua.isnull(self._scene._scene) then
                local game_layer = self._scene._scene
                game_layer._gameFrame:setEnterAntiCheatRoom(false)
                game_layer:onExitTable()
            end
            self:closeSelf()
        end, true)
    end
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 洗牌按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShuffle(sender)
    local no_alert = cc.UserDefault:getInstance():getBoolForKey("shufflenoalert", false)
    local rate = yl.app:getServerConfig().shuffle_rate or 3
    if not no_alert and self.m_shuffle_card <= 0 then
        local path = cs.game.SRC .. 'room.ShuffleAlert'
        helper.pop.popLayer( path, nil, {rate, function(selected)
            if selected then
                cc.UserDefault:getInstance():setBoolForKey("shufflenoalert", true)
            end
            cs.app.room_frame:SendShuffle()
        end} )
    else
        cs.app.room_frame:SendShuffle()
    end
end


-------------------------------------------------------------------------------
-- 洗牌成功回调
-------------------------------------------------------------------------------
function GameResultLayer:onShuffleSucc()
    self:onBtnContinue()
end


-------------------------------------------------------------------------------
-- 继续游戏按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnContinue(sender)
    sender = sender or self:child('btn_continue')
    if sender.as_exit then
        self:addCloseHook(sender.as_exit)
    end
    if tolua.isnull(self._scene) or tolua.isnull(self._scene._scene) then   -- 可能底下房间已经退出
        self:closeSelf()
        if helper.app.getFromScene('game_room_layer') then
           GlobalUserItem.bWaitQuit = false
           helper.app.getFromScene('game_room_layer'):onExitRoom()
        end
    else
        if sender.as_exit then
            self:closeSelf()
            local game_layer = helper.app.getFromScene('game_room_layer')
            game_layer._gameFrame:setEnterAntiCheatRoom(false)
            game_layer:onExitTable()
        else
            self:doClose()
        end
    end
end


-------------------------------------------------------------------------------
-- 设置继续形同退出
-------------------------------------------------------------------------------
function GameResultLayer:setContinueAsExit(func)
    self:child('btn_continue').as_exit = func
end


-------------------------------------------------------------------------------
-- 扎鸟过程特效
-------------------------------------------------------------------------------
function GameResultLayer:showNiao(result_list, cmd_data, zhuang_chair_id)
    self.m_effect_shows_niao = {}

    -- 先隐藏界面元素
    for i, child in ipairs(self:getChildren()) do
        child:hide()
    end

    local sp_bg = display.newSprite('room/bg_zha_niao.png')
    sp_bg:pos(display.width/2, display.height/2):addTo(self):hide()
    local size = sp_bg:size()
    display.newSprite('word/font_title_niao.png'):pos(size.width/2, size.height - 33):addTo(sp_bg)

    table.insert(self.m_effect_shows_niao, function()
        sp_bg:show()
    end)

    -- 计算起始位置
    local card_width = 75
    local gap = 25
    local count = cmd_data.m_cbNiaoCount
    local start_x = 0
    if count % 2 == 0 then
        start_x = size.width / 2 - (count - 1) * (card_width/2 + gap/2)
    else
        start_x = size.width / 2 - (math.ceil(count/2) - 1) * (card_width/2) - math.floor(count/2) * gap
    end

    -- 自己的chair id
    local chair_id = 0
    for i, item in ipairs(result_list) do
        if item.userItem.dwUserID == GlobalUserItem.dwUserID then
            chair_id = i - 1
            break
        end
    end
    local my_chair_id = self._scene._scene:GetMeChairID()
    local niao_cards = cmd_data.m_cbCardDataNiao
    local niao_status = cmd_data.m_cNiaoStatus
    local niao_score = cmd_data.m_cbCardDataNiaoScore
    local x = size.width / 2
    local y = size.height / 2 - 20
    local isShowNiao = true
    if cmd_data.cbNiaoStatus == yl.NiaoStatus.AllNiaoHu and not result_list[my_chair_id + 1].is_hu then
        isShowNiao = false
    end
    if isShowNiao then
        for i = 1, count do
            local pos = cc.p(start_x, y)
            start_x = start_x + card_width + gap
            local niao_card = niao_cards[my_chair_id + 1][i]
            if niao_card > 0 then
                local card = nil
                if niao_card == yl.INVALID_BYTE then
                    card = display.newSprite(CARD_PATH .. '/font_big/card_back.png')
                else
                    card = cs.game.util.createCard(niao_card, cmd.MY_VIEWID, 'hand')
                end
                local color, value = cs.game.util.splitCardValue( niao_card )
                if niao_status[my_chair_id + 1][i] ~= 255 then
                    if niao_status[my_chair_id + 1][i] > 0 and niao_status[my_chair_id + 1][i] <= 4 then  -- 按[东南西北]方位抓中
                        -- 标记方位
                        local flag = display.newSprite('room/icon_direct_' .. niao_status[my_chair_id + 1][i] .. '.png')
                        flag:pos(card:size().width/2, flag:size().height/2 + 5):addTo(card)
                    end
                    card:setColor( cc.c3b(255, 255, 0) )
                end
                card:pos(x, y - 320):addTo(sp_bg)
                local niao_score = niao_score[my_chair_id + 1][i] 
                if niao_score > 0 then
                    local label = ccui.Text:create(LANG{'NIAO_CARD_SCORE', num = niao_score}, cs.game.FONT_NAME, 32)
                    label:stroke():pos(card:size().width/2, card:size().height + 5):addTo(card)
                end
                card:hide()

                local idx = i
                table.insert(self.m_effect_shows_niao, function()
                    card:runMyAction( cc.Sequence:create(
                        cc.DelayTime:create(idx * 0.1),
                        cc.Show:create(),
                        cc.MoveTo:create(0.15, pos)
                    ) )
            end)
            end
        end

        table.insert(self.m_effect_shows_niao, function()
            self:perform(function()
                sp_bg:removeFromParent()
                self:effectShow()
            end, count * 0.1 + 0.15 + 2.5)
        end)
    else
        sp_bg:removeFromParent()
        self.m_effect_shows_niao = nil
    end
end


-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function GameResultLayer:showResult(result_list, cmd_data, zhuang_chair_id)
    local width = 40

    -- 底分
    local difen = PassRoom:getInstance().m_tabPriData.lCellScore
    self:child('label_difen'):setString( LANG{'ROOM_DIFEN', value=difen} )
    
    -- 创建一张牌
    local createCard = function(card_value, parent, x, y)
        local card = nil
        if card_value == yl.INVALID_BYTE then
            card = display.newSprite(CARD_PATH .. '/font_small/card_back.png')
        else
            card = cs.game.util.createCard(card_value, cmd.MY_VIEWID, 'out')
        end
        return card:pos(x, y or 84):addTo(parent)
    end
    -- dump(cmd_data, '结束', 9)
    -- 是否流局
    local is_liuju = true
    local is_jiesan = false
    if cmd_data.bIsJieSan then
        is_jiesan = true
    end
    for i, r in ipairs(result_list) do
        if r.is_hu then
            is_liuju = false
            break
        end
    end
    self.m_is_liuju = is_liuju
    
    local template = self:child('template')
    local panel_offset_y = 134    -- 两行的锚点直接距离
    local panel_start_y = 92 + (4 - #result_list) * panel_offset_y / 2 -- 不足4行，修正到中间去
    self.m_effect_shows = {}
    for i = 1, cmd.GAME_PLAYER do
        if i <= #result_list then
            local chair_id = i - 1
            local panel = template:clone():addTo(self)
            panel:pos(display.width / 2 + 568, panel_start_y)

            local action_idx = i
            local action_start_y = panel_start_y
            table.insert(self.m_effect_shows, function()
                panel:show():runMyAction( cc.Sequence:create(
                    cc.DelayTime:create((action_idx - 1) * 0.1),
                    cc.EaseBackOut:create( cc.MoveTo:create(0.3, cc.p(display.width / 2, action_start_y)) )
                ) )
            end)

            panel_start_y = panel_start_y + panel_offset_y

            -- 头像
            local panel_avator = panel:child('panel_avator')

            local head = PopupHead:create(self, result_list[i].userItem, 80, 100)
            head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

            -- 月卡
            if helper.app.addAvatorCard(panel_avator, 0.62, nil, result_list[i].userItem.nMonthTicketType) then
                panel:child('bg_avator'):hide()
            end

            -- 房主
            if chair_id ~= 0 then
                panel:child('img_fangzhu'):removeFromParent()
            end

            -- 输赢积分
            local score = result_list[i].lScore
            panel:child('label_score'):setString( (score >= 0 and '+' or '') .. score )

            -- 积分名称(普通金币场需要改名叫“金币”)
            if GlobalUserItem.bCommonGold then
                panel:child('label_score_pre'):setString( LANG.GOLD )
            end

            -- 红包赛特殊处理
            if GlobalUserItem.bIsRedArena then
                panel:child('label_score'):hide()
                panel:child('label_score_pre'):hide()
                local win = math.max(0, math.min(7, result_list[i].userItem.nWin or 0))
                if win > 0 then
                    local sp = display.newSprite('word/font_continue_win_' .. win .. '.png')
                    sp:pos(panel:child('label_score_pre'):pos()):addTo(panel)
                end
            end

            -- 昵称
            panel:child('label_name'):setString(result_list[i].userItem.szNickName)

            -- 庄家
            cs.game.util.setZhuang(panel:child('img_zhuang'), zhuang_chair_id, chair_id, 0)
            
            -- 方位 
            cs.game.util.setFeng(panel:child('img_dir'), result_list[i].pos)

            -- 个人麻将 ----------------------------
            local panel_card = panel:child('panel_card')
			local label_hu = panel:child('panel_card/label_hu')
            local label_fan = panel:child('panel_card/label_fan')
            local fX = 0
            
            local scale = cmd.NORMAL_MAX_COUNT / self.cbPlayerMaxCardsNum
            panel_card:scale( scale )
            -- 碰杠牌
            for j = 1, cmd_data.cbWeaveItemCount[1][i] do
                local max = #cmd_data.WeaveItemArray[i]
                local cbOperateData = {}
                for v = 1, 4 do
					local data = cmd_data.WeaveItemArray[i][j].cbCardData[1][v]
					    if data > 0 then
						table.insert(cbOperateData, data)
					end
				end
                local weaveItem = cmd_data.WeaveItemArray[i][j]
                local nShowStatus = GameLogic.SHOW_NULL
				local cbParam = cmd_data.WeaveItemArray[i][j].cbParam
                if cbParam == GameLogic.WIK_GANERAL then
					if cbOperateData[1] == cbOperateData[2] then 	--碰
						nShowStatus = GameLogic.SHOW_PENG
					else 											--吃
						nShowStatus = GameLogic.SHOW_CHI
					end
				elseif cbParam == GameLogic.WIK_MING_GANG then
					    nShowStatus = GameLogic.SHOW_MING_GANG
				elseif cbParam == GameLogic.WIK_FANG_GANG then
					    nShowStatus = GameLogic.SHOW_FANG_GANG
				elseif cbParam == GameLogic.WIK_AN_GANG then
					    nShowStatus = GameLogic.SHOW_AN_GANG
				end
                for k = 1, #cbOperateData do
                    local cardData = cbOperateData[k]
                    --print('nShowStatus', nShowStatus)
                    if nShowStatus == GameLogic.SHOW_AN_GANG or nShowStatus == GameLogic.SHOW_MING_GANG or nShowStatus == GameLogic.SHOW_FANG_GANG then
                       if k < 4 then
                           local showCard = cardData
                           if nShowStatus == GameLogic.SHOW_AN_GANG then
                              showCard = yl.INVALID_BYTE
                           end
                           local cardNode = createCard( showCard, panel_card, fX )
                           if k == 2 then
                               createCard( cardData, cardNode, 20, 41.5 )
                           end
                           fX = fX + width
                       end
                    else
                        createCard( cardData, panel_card, fX )
                        fX = fX + width
                    end
                end
                fX = fX + width/2          
            end
            -- 剩余手牌
            for j = 1, #result_list[i].cbCardData do 
                createCard( result_list[i].cbCardData[j], panel_card, fX )
                fX = fX + width
            end

            local isDefault = true
            if cmd_data.scoreDesc then
                local scoreDesc = cmd_data.scoreDesc[1][chair_id+1]
                if scoreDesc and scoreDesc ~= '' then
                    label_fan:setString( scoreDesc )
                    isDefault = false
                end
            end
            -- 番数
            if isDefault then
                local fan = cmd_data.wFanShu[1][chair_id+1]
                if fan and fan > 0 then
                    label_fan:setString( LANG{'HU_TOTAL_FAN', value=fan} )
                else
                    label_fan:hide()
                end
            end

            -- 胡
            if result_list[i].is_hu then
                panel:child('bg_normal'):removeFromParent()
                fX = fX + 20
                createCard( cmd_data.cbProvideCard, panel_card, fX )  -- 胡的那张牌
                local is_zimo = cmd_data.wProvideUser == chair_id
                local pic = string.format('word/font_over_%s.png', is_zimo and 'zimo' or 'hu')
                panel:child('img_type'):texture(pic):scale(is_zimo and 0.9 or 1)
                label_hu:setString( cmd_data.turnDesc[1][chair_id+1] )
                local cur_x = label_hu:getContentSize().width
                local max_x = 450
                if cur_x > max_x then
                    label_hu:scale( max_x/cur_x )
                end
            else
                panel:child('bg_win'):removeFromParent()
                -- label_hu:removeFromParent()
                label_hu:setString( cmd_data.turnDesc[1][chair_id+1] )
                --解散
                if is_jiesan then
                    panel:child('img_type'):ignoreContentAdaptWithSize(true)
                    panel:child('img_type'):texture('word/font_over_jiesan.png')
                elseif is_liuju then    -- 流局
                    panel:child('img_type'):ignoreContentAdaptWithSize(true)
                    panel:child('img_type'):texture('word/font_over_liuju.png')
                elseif cmd_data.wProvideUser == chair_id then   -- 放炮
                    panel:child('img_type'):texture('word/font_over_fangpao.png')
                else
                    panel:child('img_type'):removeFromParent()
                end
            end

            fX = 0
            -- 鸟牌 换行
            if ( ( cmd_data.cbNiaoStatus == yl.NiaoStatus.SingleNiao or cmd_data.cbNiaoStatus == yl.NiaoStatus.AllNiaoHu )  and result_list[i].is_hu) or cmd_data.cbNiaoStatus == yl.NiaoStatus.AllNiao then
                local niao_cards = cmd_data.m_cbCardDataNiao[i]
                local niao_status = cmd_data.m_cNiaoStatus[i]
                local niao_count = 0
                for j = 1, cmd_data.m_cbNiaoCount do
                    if niao_cards[j] > 0 then
                        local card = createCard( niao_cards[j], panel_card, fX, 34 )
                        fX = fX + width
                        --local color, value = cs.game.util.splitCardValue( niao_cards[j] )
                        if niao_status[j] == 0 or niao_status[j] == chair_id + 1 then
                            card:setColor( cc.c3b(255, 255, 0) )
                        else
                            card:setColor( cc.c3b(255, 255, 255) )
                        end
                        niao_count = niao_count + 1
                    end
                end
                if niao_count > 0 then
                    fX = fX + width
                end
            end
            
            --花牌
            local out_hua_data = cmd_data.m_cbOutHua[i]
            local pick_hua_data = cmd_data.m_cbHuaPick[i]
            local cbOutPi = cmd_data.m_cbOutPi[i]
            local totalHuaNum = 0
            for j = 1, cmd.MAX_CARD_INDEX do
                 local huaCardNum = out_hua_data[j] or 0
                 local piCardNum = cbOutPi[j] or 0
                 totalHuaNum = totalHuaNum + huaCardNum + piCardNum
            end
            local isDie = false
            -- 是否需要叠加
            if totalHuaNum > 4 then
               isDie = true
            end
            -- dump(out_hua_data, '花牌', 9)
            for j = 1, cmd.MAX_CARD_INDEX do
                local huaCardNum = out_hua_data[j]
                if huaCardNum > 0 then
                    local huaCardIndex = j
                    local huaCardData =  GameLogic.SwitchToCardData(huaCardIndex)
                    local card = createCard( huaCardData, panel_card, fX, 34 )
                    fX = fX + width
                    if pick_hua_data[j] > 0 then
                        card:setColor( cc.c3b(255, 255, 0) ) 
                    else
                        card:setColor( cc.c3b(255, 255, 255) )
                    end
                    if huaCardNum > 1 then
                       if isDie then
                           local label_num = ccui.Text:create(huaCardNum, cs.game.FONT_NAME, 20)
                           label_num:anchor(0.5, 0):pos( cc.p(card:getContentSize().width/2, 2 ) ):stroke():addTo( card )
                       else
                            for m = 1, huaCardNum - 1 do
                                card = createCard( huaCardData, panel_card, fX, 34 )
                                fX = fX + width
                                if pick_hua_data[j] > 0 then
                                    card:setColor( cc.c3b(255, 255, 0) ) 
                                else
                                    card:setColor( cc.c3b(255, 255, 255) )
                                end
                            end
                       end
                    end
                end
            end

            --  痞子
            -- dump(out_hua_data, '花牌', 9)
            for j = 1, cmd.MAX_CARD_INDEX do
                local piCardNum = cbOutPi[j]
                if piCardNum > 0 then
                    local piCardIndex = j
                    local piCardData =  GameLogic.SwitchToCardData(piCardIndex)
                    local card = createCard( piCardData, panel_card, fX, 34 )
                    fX = fX + width
                    if piCardNum > 1 then
                       if isDie then
                           local label_num = ccui.Text:create(piCardNum, cs.game.FONT_NAME, 20)
                           label_num:anchor(0.5, 0):pos( cc.p(card:getContentSize().width/2, 2 ) ):stroke():addTo( card )
                       else
                            for m = 1, piCardNum - 1 do
                                card = createCard( piCardData, panel_card, fX, 34 )
                                fX = fX + width
                            end
                       end
                    end
                end
            end

        end
    end
    template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 关闭界面
-------------------------------------------------------------------------------
function GameResultLayer:doClose()
    if yl.IS_REPLAY_MODEL then
       helper.app.getFromScene('game_room_layer'):onExitRoom()
    else
       local is_room_ended = PassRoom:getInstance().m_bRoomEnd
       if not is_room_ended then
            self._scene.btStart:setVisible(true)
            self._scene:onButtonClickedEvent('btn_start')
       else
            local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
            if room_result_layer then
                room_result_layer:show()
            else
                GlobalUserItem.bWaitQuit = false
                helper.app.getFromScene('game_room_layer'):onExitRoom()
            end
       end
    end
    self:closeSelf()
end


return GameResultLayer
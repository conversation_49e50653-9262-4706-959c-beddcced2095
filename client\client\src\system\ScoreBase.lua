-------------------------------------------------------------------------------
--  创世版1.0
--  所有游戏战绩基类
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local ScoreBase = class("ScoreBase", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ScoreBase:ctor()
    print('ScoreBase:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('ScoreLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('item_template'):hide()

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self)
end


-------------------------------------------------------------------------------
-- onEnterTransitionFinish
-------------------------------------------------------------------------------
function ScoreBase:onEnterTransitionFinish()
    helper.pop.waiting()
    PassRoom:getInstance():getNetFrame():onQueryScoreList()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ScoreBase:onExit()
    print('ScoreBase:onExit...')
end


-------------------------------------------------------------------------------
-- 显示列表(PassRoom中调用，scores:tagRecordInfo)
-------------------------------------------------------------------------------
function ScoreBase:onReloadRecordList(scores, cmd)
    if cmd == PassRoom.cmd_pri_login.SUB_GR_QUERY_RECORD_INFO_RESULT then        -- 主列表
        self:showScores(scores)
    elseif cmd == PassRoom.cmd_pri_login.SUB_GR_QUERY_RECORD_SCOR_RESULT then    -- 详情
        self:showDetailScores(scores)
    end
end


-------------------------------------------------------------------------------
-- 显示详情列表(PassRoom中调用，scores:tagRecordScore)
-------------------------------------------------------------------------------
function ScoreBase:showDetailScores(scores)
    -- 子类需重载实现该方法
end


-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function ScoreBase:showScores(scores)
    local all_plugins = cs.app.plugins
    if not cs.app.NO_DISTRICT then  -- 有地区选择要用所有配置
        all_plugins = require(cs.app.CLIENT_SRC .. 'system.district_kinds').getAllKinds() -- 需获取所有，不能直接用cs.app.plugins
    end

    local names = {}
    for i, v in ipairs(all_plugins) do
        if v.kind then  -- 如果是游戏的话可能为nil
            names[v.kind] = v.name
        end
    end

    local main_node = self.main_node
    local listview = main_node:child('listview')
    local template = main_node:child('item_template')
    for i, record in ipairs(scores) do
        local kind_name = names[record.wKindID] or record.wKindID
        local item = template:clone():show()
        item.index = i
        item:child('label_title'):setString( LANG{'SCORE_TITLE', name=kind_name, base_score=record.wCellScore} )

        local t = record.sysCreateTime
        local time = string.format('%02d-%02d-%02d %02d:%02d', t.wYear, t.wMonth, t.wDay, t.wHour, t.wMinute)
        item:child('label_time'):setString( time )

		local num = 0
        for _, nick in ipairs(record.szNickName) do
            if nick and nick ~= '' then
                num = num + 1
            end
        end
		num = math.min(num, cs.app.MAX_PLAYER)
        record.nPlayerCount = num

		for j = 1, cs.app.MAX_PLAYER do
			item:child('label_name' .. j):setString( j <= num and record.szNickName[j] or '' )
            item:child('label_score' .. j):setString( j <= num and record.nScore[j] or '' )
		end

        item:child('btn_detail').record = record
        item:child('btn_detail'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDetail) )
        listview:pushBackCustomItem(item)
    end
    template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 显示详情
-------------------------------------------------------------------------------
function ScoreBase:onBtnDetail(sender)
    -- 子类需重载实现该方法
end


return ScoreBase
-------------------------------------------------------------------------------
--  创世版1.0
--  万元红包赛
--  @date 2018-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')
local RedArenaDialog = cs.app.client('main.RedArenaDialog')


local MAX_WIN_TIMES = 7     -- 最大连胜次数
local MAX_WIN7_TIMES = 3    -- 一天最多几次7连胜


local RedArenaLayer = class("Red<PERSON>renaLayer", cc.Layer)
                                                    

-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RedArenaLayer:ctor(kind, server_id)
    self:setName('red_arena_layer')
    self:enableNodeEvents()
    self.m_can_play = true
    self.m_kind = kind
    self.m_server_id = server_id

    -- 载入主UI
    local main_node = helper.app.loadCSB('RedArenaLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    main_node:child('btn_start'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnStart) )
    main_node:child('btn_rule'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRule) )
    main_node:child('bg_quan/btn_exchange'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnExchange) )
    main_node:child('bg_relive/btn_relive_more'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnMore) )

    for i = 1, MAX_WIN_TIMES do
        main_node:child('star' .. i):hide()
    end
    main_node:child('bg_relive/label_relive'):setString('')

    self:refreshQuan()
    self:requestInfo()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function RedArenaLayer:onEnter()
    print('RedArenaLayer:onEnter...')

    LiveFrame:getInstance():addListen(cmd_common.MDM_WIN_STREAK_SERVICE, cmd_common.SUB_WIN_STREAK_QUERY, self, self.onInfoResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RedArenaLayer:onExit()
    print('RedArenaLayer:onExit...')

    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 刷新奖券显示
-------------------------------------------------------------------------------
function RedArenaLayer:refreshQuan()
    self.main_node:child('bg_quan/label_quan'):setString( GlobalUserItem.dwTicket )
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function RedArenaLayer:onBtnBack(sender)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 玩法按钮点击
-------------------------------------------------------------------------------
function RedArenaLayer:onBtnRule(sender)
    local alert = helper.pop.alert( LANG.REDARENA_RULE )
    alert:resize(720, 550, 620)
end


-------------------------------------------------------------------------------
-- 更多复活卡按钮点击
-------------------------------------------------------------------------------
function RedArenaLayer:onBtnMore(sender)
    RedArenaDialog.showDialog(self.m_kind, {is_task=true})
end


-------------------------------------------------------------------------------
-- 兑换商城按钮点击
-------------------------------------------------------------------------------
function RedArenaLayer:onBtnExchange(sender)
    helper.link.toMall( LANG.MALL_TAB_QUAN )
end


-------------------------------------------------------------------------------
-- 开始按钮点击
-------------------------------------------------------------------------------
function RedArenaLayer:onBtnStart(sender)
    if not self.m_can_play then
        helper.pop.message( LANG{'REDARENA_FINISH', num=MAX_WIN7_TIMES} )
        return
    end
    self:startGame()
end


-------------------------------------------------------------------------------
-- 请求初始信息
-------------------------------------------------------------------------------
function RedArenaLayer:requestInfo()
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = self.m_kind} )
	cmd_data:setcmdinfo(cmd_common.MDM_WIN_STREAK_SERVICE, cmd_common.SUB_WIN_STREAK_QUERY)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 初始信息返回
-------------------------------------------------------------------------------
function RedArenaLayer:onInfoResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.WinStreakInfo)
    if not ret then return false end
    
    if ret.nFinishRound >= MAX_WIN7_TIMES then
        self.m_can_play = false
    end

    local win = ret.isLastLose and 0 or ret.nWin
    for i = 1, MAX_WIN_TIMES do
        self.main_node:child('star' .. i):setVisible( i <= win )
    end
    self.main_node:child('bg_relive/label_relive'):setString( 'x' .. ret.nRebornCard )
end


-------------------------------------------------------------------------------
-- 开始比赛
-------------------------------------------------------------------------------
function RedArenaLayer:startGame()
    PassRoom:getInstance():onLoginServer(self.m_server_id)
end


return RedArenaLayer

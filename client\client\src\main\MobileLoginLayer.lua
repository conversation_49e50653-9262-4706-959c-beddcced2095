-------------------------------------------------------------------------------
--  创世版1.0
--  手机登录
--  @date 2019-03-05
--  @auth woodoo
-------------------------------------------------------------------------------
local EditBox = cs.app.client('system.EditBox')


local URL_GET   = '/api/v1/user/get_verify_code'    -- 获取验证码
local URL_LOGIN = '/api/v1/user/phone_login'        -- 绑定手机号


local MobileLoginLayer = class("MobileLoginLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function MobileLoginLayer:ctor(callback)
    print('MobileLoginLayer:ctor...')
    self:enableNodeEvents()
    self.m_callback = callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('MobileLoginLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('bg_time'):hide()
    local edit1 = EditBox.convertTextField( main_node:child('input_mobile'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    local edit2 = EditBox.convertTextField( main_node:child('input_code'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    edit1:setPlaceholderFontColor(cc.c3b(220, 220, 220))
    edit2:setPlaceholderFontColor(cc.c3b(220, 220, 220))

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_cancel'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_get'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnGet) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function MobileLoginLayer:onExit()
    print('MobileLoginLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 获取验证码按钮点击
-------------------------------------------------------------------------------
function MobileLoginLayer:onBtnGet(sender)
    local phone = self.main_node:child('input_mobile'):getString():trim()
    if phone == '' then
        helper.pop.message(LANG.MOBILE_LOGIN_BAD)
        return
    end

    helper.pop.waiting()
    local params = {type='login', phone=phone}
    yl.GetUrl(URL_GET, 'post', params, handler(self, self.onGetVcodeResponse) )
end


-------------------------------------------------------------------------------
-- 确定按钮点击
-------------------------------------------------------------------------------
function MobileLoginLayer:onBtnOk(sender)
    local phone = self.main_node:child('input_mobile'):getString():trim()
    if phone == '' then return end
    if not phone:match('^%d+$') then
        helper.pop.message(LANG.MOBILE_LOGIN_BAD)
        return
    end
    local vcode = self.main_node:child('input_code'):getString():trim()
    if vcode == '' then return end

    helper.pop.waiting()
    local params = {phone=phone, code=vcode}
    yl.GetUrl(URL_LOGIN, 'post', params, handler(self, self.onLoginResponse) )
end


-------------------------------------------------------------------------------
-- 获取验证码返回
-------------------------------------------------------------------------------
function MobileLoginLayer:onGetVcodeResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end
    helper.pop.message(LANG.MOBILE_LOGIN_SENT)

    self.main_node:child('bg_time'):show()
    self.main_node:child('btn_get'):hide()
    local tag = 213
    local num = 60
    self:perform(function()
        self.main_node:child('bg_time/label_time'):setString( LANG{'MOBILE_LOGIN_TIME', seconds=num} )
        num = num - 1
        if num == -1 then
            self:stop(action)
            self.main_node:child('bg_time'):hide()
            self.main_node:child('btn_get'):show()
        end
    end, 1, -1, tag, true)
end


-------------------------------------------------------------------------------
-- 登录返回
-------------------------------------------------------------------------------
function MobileLoginLayer:onLoginResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    data = data.data

    if self.m_callback then
        self.m_callback(data)
    end
    self:removeFromParent()
end


return MobileLoginLayer
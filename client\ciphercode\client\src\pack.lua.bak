local pack = {
    -- 以下参数只在运行期使用
    LOGONSERVER         = 'lhmj.tuo3.com.cn',
    LOGONPORT           = 8606,
    BASE_C_VERSION      = 12,
    BASE_C_RESVERSION   = 1,
    PACK_GAME_VERSIONS  = '1',  -- 注意：要和PACK_GAMES对应
    WX_APPID            = 'wx561cc267a5729725',
    WX_SECRET           = '9af573ee5f0147955d1a4040f38d10be',

    -- 以下参数打包和运行期都会使用
    PACK_GAMES          = 'mahjong',    -- 第一个认为是主游戏
    IS_APPSTORE         = false,
    MW_URL              = 'https://ablkx7.mlinks.cc/AaHx',
    MW_URL_APPSTORE     = 'https://ablkx7.mlinks.cc/Aavt',

    -- 以下参数只在打包时使用
    PACKAGE_NAME        = 'cn.duoduo100.lhmj',
    ANDROID_VER_CODE    = 101,
    ANDROID_VER_NAME    = '6.1',
    CHANNEL             = '60010001',
    MW_APPID            = 'FABO52S4X9CP8THSYLTVZ4X9DKPSOT0A',
    MW_APPID_APPSTORE   = 'BUS02P3OYU30WX0I530TL3A8GETJ2NQM',
    MW_SCHEMA           = 'ddlhmj',
    AMAP_APIKEY         = 'e4ac0c271393f5419036944c4a850216',

	PROJECT_DIR 		= 'proj.android_mowang',
	MOWANG_APPID 		= 'qLzfnMSYyw',
    MOWANG_SCHEMA       = 'ddlhmj',
}

return pack

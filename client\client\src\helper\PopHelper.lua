-------------------------------------------------------------------------------
--  创世版1.0
--  弹窗辅助方法类
--      访问方式：helper.pop.
--  @date 2017-06-05
--  @auth woodoo
-------------------------------------------------------------------------------
local PopHelper = {}
helper = helper or {}
helper.pop = PopHelper


-------------------------------------------------------------------------------
-- 弹窗
--  layer: 可以是对象，也可以是类路径
--  parent：显示父对象，可以为nil（加入到runningScene）
--  layer_params: layer的初始化参数，仅当layer是类路径时有效，可以是单一参数或table，
--                  为table时自动调用unpack打开，所以不能保护nil
--  mask_opacity：蒙版透明度，默认120
--  mask_closable：点击蒙版是否可关闭，若可以关闭，layer若有onPopClose会被调用
-------------------------------------------------------------------------------
function PopHelper.popLayer(layer, parent, layer_params, mask_opacity, mask_closable, no_blur)
    if type(layer) == 'string' then
        local cls = require(layer)
        if type(layer_params) == 'table' then
            layer = cls:create( unpack(layer_params) )
        else
            layer = cls:create( layer_params )
        end
    end
    PopHelper.addMask(layer, mask_opacity, mask_closable, no_blur)
    parent = parent or cc.Director:getInstance():getRunningScene()
    parent:addChild(layer)

    return layer
end


-------------------------------------------------------------------------------
-- 增加弹窗模板
--  mask_opacity：蒙版透明度，默认120
--  mask_closable：点击蒙版是否可关闭，若可以关闭，layer若有onPopClose会被调用
-------------------------------------------------------------------------------
function PopHelper.addMask(parent, mask_opacity, mask_closable, no_blur)
    local mask = cs.app.client('system.PopupMask' ):create(mask_opacity, mask_closable, no_blur)
    mask:zorder(-123)
    parent:addChild(mask)
    return mask
end


-------------------------------------------------------------------------------
-- 弹窗引导蒙板
--  node: 引导节点，允许穿透操作的对象
--  mask_opacity：蒙版透明度，默认120
-------------------------------------------------------------------------------
function PopHelper.guide(node, parent, offset, mask_opacity)
    local layer = cs.app.client('system.GuideLayer'):create(node, offset, mask_opacity)
    layer:zorder(9999)
    helper.app.addToScene(layer, parent)
    return layer
end


-------------------------------------------------------------------------------
-- 弹出消息
-------------------------------------------------------------------------------
function PopHelper.message(msg, container)
    if not msg or (type(msg) == 'string' and msg == '') then return end

    local bg_sprite = display.newSprite('common/bg_pop_msg.png')
    if not bg_sprite then return end

    local bg_size = bg_sprite:size()

    local bg = ccui.ImageView:create()  -- display.newSprite('common/bg_pop_msg.png', display.cx, display.cy)
    bg:texture('common/bg_pop_msg.png')
    bg:setScale9Enabled(true)
    bg:setCapInsets(cc.rect(35, 20, bg_size.width - 35 * 2, bg_size.height - 20 * 2))
    bg:size(bg_size)

    container = container or cc.Director:getInstance():getRunningScene()

    if type(msg) == 'string' then
        msg = cs.app.client('system.RichText').new(msg, 25)
    end

    local msg_size = msg:size()

    -- 调整背景的长度
    if bg_size.width - msg_size.width < 80 then
        bg_size.width = msg_size.width + 80
        bg:size(bg_size)
    end
    
    msg:pos(bg_size.width/2, bg_size.height/2):addTo(bg)

    local name = '_pop_message_'
    bg:setCascadeOpacityEnabled(true)
    bg:setName(name)
    bg:zorder(cs.app.zorder.POP_MESSAGE):pos(display.cx, display.cy):addTo(container)

    -- 动画
    bg:opacity(0):runMyAction(cc.Sequence:create(
        cc.EaseOut:create(cc.FadeIn:create(0.3), 2),
        cc.DelayTime:create(2),
        cc.CallFunc:create(function()
            bg.start_move = true
        end),
        cc.Spawn:create(
            --cc.EaseIn:create( cc.MoveBy:create(0.8, cc.p(0, display.height/2 + 30)), 3 ),
            --cc.EaseIn:create( cc.ScaleTo:create( 0.2, 0.01), 3 ),
            cc.EaseIn:create( cc.FadeOut:create(0.3), 2 )
        ),
        cc.RemoveSelf:create(true)
    ))

    -- 所有前面的还没开始移动的往上移动一行
    for i, child in ipairs( container:getChildren() ) do
        if child:getName() == name and child ~= bg and not child.start_move then
            child:runMyAction(cc.MoveBy:create(0.1, cc.p(0, child:size().height + 1)))
        end
    end
end


-------------------------------------------------------------------------------
-- 通用等待
--  visible: 1、true or false
--           2、table: {true, 'key', timeout}
-- loading_type 需要显示的等待类型 暂时有 重连和 普通
-- offset_pos 等待的位置有时候需要 稍微调整
-------------------------------------------------------------------------------
function PopHelper.waiting(visible, parent_scene, force_remove)
    local scene = parent_scene or cc.Director:getInstance():getRunningScene()
    if not scene then return end

    if visible == nil then visible = true end
    if offset_pos == nil then offset_pos = cc.p(0, 0) end
    if loading_type == nil then loading_type = yl.LoadingTypes.NORMAL end

    local option = 'global'	-- 记录是何种操作导致
    local timeout = nil     -- 操作超时时间，秒，可选
    local loading_type = yl.LoadingTypes.NORMAL
    local offset_pos = cc.p(0, 0)
    if type(visible) == 'table' then
        if visible[5] then
            offset_pos = visible[5]
        end
        if visible[4] then
            loading_type = visible[4]
        end
        timeout = visible[3]
        option = visible[2]
        visible = visible[1]
    end

    local name = '_pop_waiting_'
    local waiting = scene:getChildByName(name)

    -- 存在且要求隐藏
    if waiting and not visible then
        -- 检查该标记是否有定时器
        if type(waiting.options[option]) == 'number' then
            waiting:stop(waiting.options[option])
        end
        waiting.options[option] = nil

        local do_remove = true
        for opt, v in pairs(waiting.options) do
            do_remove = false
            break
        end
        if do_remove or force_remove then
            waiting:removeFromParent()
        end

    -- 不存在且要求显示
    elseif not waiting and visible then
        waiting = require(cs.app.CLIENT_SRC .. 'system.PopupMask').new(0, nil, true):zorder(cs.app.zorder.POP_WAIT)
        waiting:setName(name)
        waiting:addTo(scene)
        waiting.options = {}
        waiting.timer_index = 0

        local bg = nil
        if loading_type == yl.LoadingTypes.RECONECT then
            bg = display.newSprite('common/ani_reconect_waiting_bg.png')
            local sprite = display.newSprite('common/ani_reconect_waiting.png')
            helper.layout.addCenter(bg, sprite)
            local action = cc.RepeatForever:create(
                cc.Sequence:create(
                    cc.FadeOut:create(0.5),
                    cc.FadeIn:create(0.5)
                ))
            sprite:runMyAction(action)
        else
            bg = display.newSprite('common/bg_waiting.png')
            local dice = helper.app.createAnimation('common/ani_waiting_dice', 6, 3, true)
            local light = helper.app.createAnimation('common/ani_waiting_light', 8, 1, true)
            helper.layout.addCenter(bg, dice)
            helper.layout.addCenter(bg, light)
        end
        helper.layout.addCenter(waiting, bg)
        bg:pos(cc.pAdd(cc.p(bg:px(), bg:py()), offset_pos))
        bg:hide():perform(function(sender) sender:show() end, 1)    -- 延时显示，跳过一些短暂的闪烁
    end

    if visible then
        -- 记录新值前检查该标记的之前值
        if type(waiting.options[option]) == 'number' then
            waiting:stop(waiting.options[option])
        end

        if timeout then
            waiting.timer_index = waiting.timer_index + 1
            waiting:perform(function()
                helper.pop.waiting({false, option}, parent_scene)
            end, timeout, nil, waiting.timer_index)
            waiting.options[option] = waiting.timer_index	-- 记录数值，表示有定时器
        else
            waiting.options[option] = true	-- 记录标记
        end
    end
end


-------------------------------------------------------------------------------
-- 弹出消息对话框
-------------------------------------------------------------------------------
function PopHelper.alert(msg, button1, button2, input_default)
	local alert = cs.app.client('system.Alert').new(msg, button1, button2, input_default)
	alert:addTo( cc.Director:getInstance():getRunningScene() )
	return alert
end


-------------------------------------------------------------------------------
-- 弹出规则框
-------------------------------------------------------------------------------
function PopHelper.popRule(msg)
	local alert = cs.app.client('system.PopupRuleLayer').new(msg)
	alert:addTo( cc.Director:getInstance():getRunningScene() )
	return alert
end


-------------------------------------------------------------------------------
-- 弹出下拉列表
--  remove_callback: 可选，没有时移除
-------------------------------------------------------------------------------
function PopHelper.drop(values, size, pos, callback, remove_callback, dir, parent, csb)
    local drop = cs.app.client('system.DropList').new(values, size, pos, callback, remove_callback, dir, csb)
    helper.app.addToScene(drop, parent)
	return drop
end


-------------------------------------------------------------------------------
-- 弹出数字选择
-------------------------------------------------------------------------------
function PopHelper.numSelect(min, max, tip_lang, callback)
    local layer = cs.app.client('system.NumSelect').new(min, max, tip_lang, callback)
    helper.app.addToScene(layer)
	return layer
end


-------------------------------------------------------------------------------
-- 分享链接
-------------------------------------------------------------------------------
function PopHelper.shareLink(url, title, desc, title_font, show_note, share_tag, share_target, callfunc)
    local path = cs.app.CLIENT_SRC .. 'main.ShareLayer'
    return helper.pop.popLayer(path, nil, {url, title, desc, false, title_font, show_note, share_tag, share_target, callfunc}, nil, true)
end


-------------------------------------------------------------------------------
-- 分享图片
--  pic_path: 图片路径，为空表示截图分享
-------------------------------------------------------------------------------
function PopHelper.shareImage(pic_path, title_font, show_note, share_tag, callfunc )
    local path = cs.app.CLIENT_SRC .. 'main.ShareLayer'
    return helper.pop.popLayer(path, nil, {false, false, false, pic_path, title_font, show_note, share_tag, nil, callfunc}, nil, true)
end



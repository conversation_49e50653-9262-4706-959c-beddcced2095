local cmd =  {}

--游戏版本
cmd.VERSION 					= appdf.VersionValue(6,7,0,1)
--游戏标识
cmd.KIND_ID						= 110
	
--游戏人数
cmd.GAME_PLAYER					= 4

--所有牌数
cmd.GAME_CARD_NUM               = 54

cmd.MAX_COUNT					= 14

cmd.CHANGE_CHAIRID_NUM			= 2

--每个人最大的出牌数目
cmd.GAME_MAX_OUT_CARD			= 16

--视图位置
cmd.MY_VIEWID					= 3

--******************         游戏状态             ************--
--等待开始
cmd.GS_TK_FREE					= 0
--晒牌
cmd.GAME_SCENE_SAI				= 100
--游戏进行
cmd.GS_TK_PLAYING				= 101

--*********************      服务器命令结构       ************--
--游戏开始
cmd.SUB_S_GAME_START			= 100
--用户强退
--cmd.SUB_S_PLAYER_EXIT			= 102
cmd.SUB_S_START_CARD			= 102
--游戏结束
cmd.SUB_S_GAME_END				= 105
--用户摊牌
cmd.SUB_S_OUT_CARD				= 103
--用户摊牌
cmd.SUB_S_PASS_CARD				= 104
--用户摊牌
cmd.SUB_S_SAIPAI				= 101
--用户叫庄
--cmd.SUB_S_CALL_BANKER			= 106
cmd.SUB_S_READY_STATE			= 113
cmd.SUB_S_RECORD				= 111								--房卡结算记录

--**********************    客户端命令结构        ************--
--用户叫庄
--cmd.SUB_C_CALL_BANKER			= 1
--用户加注
--cmd.SUB_C_ADD_SCORE				= 2
cmd.SUB_C_SAIPAI				= 1
--用户摊牌
cmd.SUB_C_OUT_CARD				= 2
--用户摊牌
cmd.SUB_C_PASS_CARD				= 3
--更新库存
cmd.SUB_C_STORAGE				= 6
--设置上限
cmd.SUB_C_STORAGEMAXMUL			= 7
--请求查询用户
cmd.SUB_C_REQUEST_QUERY_USER	= 8
--用户控制
cmd.SUB_C_USER_CONTROL			= 9

--********************       定时器标识         ***************--
--无效定时器
cmd.IDI_NULLITY					= 200
--开始定时器
cmd.IDI_START_GAME				= 201
--叫庄定时器
cmd.IDI_CALL_BANKER				= 202
--加注定时器
cmd.IDI_TIME_USER_ADD_SCORE		= 1
--摊牌定时器
cmd.IDI_TIME_OUT_CARD			= 2

--*******************        时间标识         *****************--
--叫庄定时器
cmd.TIME_USER_CALL_BANKER		= 10
--开始定时器
cmd.TIME_USER_START_GAME		= 10
--加注定时器
cmd.TIME_USER_ADD_SCORE			= 10
--摊牌定时器
cmd.TIME_USER_OPEN_CARD			= 10


--空闲状态
cmd.CMD_S_StatusFree = 
{
	--基础积分
	{k = "lCellScore", t = "score"},								--基础积分
	--时间信息
 	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
 	{k = "cbTimeTenShow", t = "byte"},						--操作时间
	{k = "cbTimeStartGame", t = "byte"},						--开始时间
	{k = "cbTimeHeadOutCard", t = "byte"},						--开始时间

	{k = "cbJushu", t = "byte"},						--开始时间
}

--空闲状态
cmd.CMD_S_SAIPAI = 
{
	--时间信息
 	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
 	{k = "cbTimeTenShow", t = "byte"},						--操作时间
	{k = "cbTimeStartGame", t = "byte"},						--开始时间
	{k = "cbTimeHeadOutCard", t = "byte"},						--开始时间

	--基础积分
	{k = "lCellScore", t = "score"},								--基础积分
	{k = "wBankerUser", t = "word"},						--开始时间
	{k = "wCurrentUser", t = "word"},						--开始时间
	{k = "cbJushu", t = "byte"},						--开始时间

	{k = "wTenUserShow", t = "word", l = {2}},
	{k = "cbTenUserCount", t = "byte"},						--开始时间
	{k = "wTenUser", t = "word", l = {2}},
	{k = "cbHandCardData", t = "byte", l = {cmd.MAX_COUNT}},
	{k = "cbHandCardCount", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "bIsSaiPai", t = "bool", l = {cmd.GAME_PLAYER}}
}

--游戏状态
cmd.CMD_S_StatusPlay = 
{
	--时间信息
	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
	{k = "cbTimeTenShow", t = "byte"},						--操作时间
   	{k = "cbTimeStartGame", t = "byte"},						--开始时间
	{k = "cbTimeHeadOutCard", t = "byte"},						--开始时间
	   
	{k = "lCellScore", t = "score"},							--出牌时间
	{k = "wBankerUser", t = "word"},						--开始时间
	{k = "wCurrentUser", t = "word"},						--叫分时间
	{k = "wChairID", t = "word"},						--叫分时间

	{k = "wTurnWiner", t = "word"},							--庄家用户
	{k = "cbTurnCardCount", t = "byte"},							--当前用户
	{k = "cbTurnCardData", t = "byte", l = {cmd.MAX_COUNT}},

	{k = "cbHandCardData", t = "byte", l = {cmd.MAX_COUNT}},
	{k = "cbHandCardCount", t = "byte", l = {cmd.GAME_PLAYER}},

	{k = "wTenUser", t = "word", l = {2}},

	--游戏变量
	{k = "wWinOrder", t = "word", l = {cmd.GAME_PLAYER}},	
							--单元积分
	{k = "bIsSaiPai", t = "bool", l = {cmd.GAME_PLAYER}},
	{k = "cbJushu", t = "byte"},
}

cmd.CMD_S_GameStart = 
{
	{k = 'wBankerUser', t = 'word'},
	{k = 'wCurrentUser', t = 'word'},
	{k = 'cbCardData', t = 'byte', l = {cmd.MAX_COUNT}},
	{k = 'wFirstUser', t = 'word'},
	{k = 'wTenUser', t = 'word', l = {2}},
	{k = 'cbCardNum', t = 'byte'},
	{k = 'cbTenCard', t = 'byte', l = {2}},
	{k = 'cbCardCount', t = 'byte', l = {cmd.GAME_PLAYER}},
	{k = "cbJushu", t = "byte"},						--开始时间
}

cmd.CMD_S_SendCard = 
{
	{k = "cbHandCardData", t = "byte", l = {cmd.GAME_CARD_NUM}},
	{k = "cbCurrentUser", t = "byte"},
	{k = "cbFirstUser", t = "byte"},
}

cmd.CMD_C_OutCard = 
{
	{k = "cbCardCount", t = "byte"},
	{k = "cbCardData", t = "byte", l = {cmd.MAX_COUNT}},
}

cmd.CMD_C_Pass = 
{
	{k = "cbOutIndex", t = "byte"},
}

cmd.CMD_C_SAIPAI = 
{
	{k = "cbIsSaiPai", t = "byte"},
}

cmd.CMD_S_SENDSAIPAI = 
{
	{k = "wChairID", t = "word"},
	{k = "cbIsSaiPai", t = "byte"},
	{k = "cbCard", t = "byte"},
}

cmd.CMD_S_StartCard = 
{
	{k = "wCurrentUser", t = "word"},
}

cmd.CMD_S_OutCard = 
{
	{k = "cbCardCount", t = "byte"},
	{k = "wCurrentUser", t = "word"},
	{k = "wOutCardUser", t = "word"},
	{k = "bIsBomb", t = "bool"},
	{k = "cbCardData", t = "byte", l = {cmd.MAX_COUNT}},
	{k = "cbLeftCardCount", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "wWinOrder", t = "word", l = {cmd.GAME_PLAYER}},
	{k = "cbRedCount", t = "byte"},
}

cmd.CMD_S_PassCard = 
{
	{k = "cbTurnOver", t = "byte"},
	{k = "wCurrentUser", t = "word"},
	{k = "wPassCardUser", t = "word"},
}

cmd.CMD_S_GameEnd = 
{
	{k = "lGameScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "wCurrentUser", t = "word"},
	{k = "wWinOrder", t = "word", l = {cmd.GAME_PLAYER}},
	{k = "wTenUser", t = "word", l = {2}},
	{k = "cbCardCount", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbCardData", t = "byte", l = {cmd.MAX_COUNT, cmd.MAX_COUNT, cmd.MAX_COUNT, cmd.MAX_COUNT}},
	{k = "cbJiesan", t = "byte"},
	{k = "lGameTax", t = "score"}
	
}

return cmd
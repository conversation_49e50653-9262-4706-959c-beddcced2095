-------------------------------------------------------------------------------
--  创世版1.0
--  服务协议和隐私政策
--  @date 2019-03-05
--  @auth woodoo
-------------------------------------------------------------------------------
local GamePolicyLayer = class("GamePolicyLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GamePolicyLayer:ctor(callback, default_name)
    print('GamePolicyLayer:ctor...', default_name)
    self:enableNodeEvents()
    self.m_callback = callback
    self.m_only_content = default_name and true or false

    -- 载入主UI
    local main_node = helper.app.loadCSB('GamePolicyLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.label_service = self.main_node:child('panel_policy/bg/label_service'):hide()
    self.label_private = self.main_node:child('panel_policy/bg/label_private'):hide()

    main_node:child('panel_agree/bg/label_service'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnService) )
    main_node:child('panel_agree/bg/label_private'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnPrivate) )
    main_node:child('panel_agree/bg/btn_agree'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAgree) )
    main_node:child('panel_agree/bg/btn_not_agree'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnNotAgree) )
    main_node:child('panel_policy'):addTouchEventListener( handler(self, self.onBtnClosePolicy) )
    main_node:child('panel_policy/bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClosePolicy) )

    if self.m_only_content then
        main_node:child('panel_agree'):hide()
        main_node:child('panel_policy'):setBackGroundColorOpacity(0)
        self:showPolicy(default_name)
    else
        main_node:child('panel_agree'):show()
        main_node:child('panel_policy'):hide()
    end
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GamePolicyLayer:onExit()
    print('GamePolicyLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 遮罩点击
-------------------------------------------------------------------------------
function GamePolicyLayer:onPopClose()
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 关闭文本
-------------------------------------------------------------------------------
function GamePolicyLayer:onBtnClosePolicy(sender, event)
    if event ~= cc.EventCode.ENDED then return end
    if self.m_only_content then
        self:removeFromParent()
    else
        self.main_node:child('panel_policy'):hide()
    end
end


-------------------------------------------------------------------------------
-- 同意按钮点击
-------------------------------------------------------------------------------
function GamePolicyLayer:onBtnAgree(sender)
    self.m_callback(true)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 获取验证码点击
-------------------------------------------------------------------------------
function GamePolicyLayer:onBtnNotAgree(sender)
    self.m_callback(false)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 服务协议点击
-------------------------------------------------------------------------------
function GamePolicyLayer:onBtnService(sender)
    self:showPolicy('service')
end


-------------------------------------------------------------------------------
-- 隐私政策点击
-------------------------------------------------------------------------------
function GamePolicyLayer:onBtnPrivate(sender)
    self:showPolicy('private')
end


-------------------------------------------------------------------------------
-- 显示文本
-------------------------------------------------------------------------------
function GamePolicyLayer:showPolicy(name)
    local panel_policy = self.main_node:child('panel_policy'):show()
    local bg = panel_policy:child('bg')
    local listview = bg:child('listview')
    local label_show = name == 'service' and self.label_service or self.label_private
    local label_hide = name == 'service' and self.label_private or self.label_service
    bg:child(name == 'service' and 'label_title_service' or 'label_title_private'):show()
    bg:child(name == 'service' and 'label_title_private' or 'label_title_service'):hide()
    
    local now_label = listview:getItem(0)
    if now_label then
        now_label:hide():retain()
        listview:removeAllItems()
        now_label:addTo(bg)
        now_label:release()
    end

    label_show:show():retain()
    label_show:removeFromParent()
    listview:pushBackCustomItem(label_show)
    listview:scrollToTop(0, false)
    label_show:release()
end


return GamePolicyLayer
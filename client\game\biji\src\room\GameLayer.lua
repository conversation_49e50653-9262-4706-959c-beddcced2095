local GameModel = appdf.req(appdf.CLIENT_SRC.."system.GameModel")

local GameLayer = class("GameLayer", GameModel)

local cmd = cs.app.game("room.CMD_Game")
local GameLogic = cs.app.game("room.GameLogic")
local GameViewLayer = cs.app.game("room.GameViewLayer")
local ExternalFun =  cs.app.client('external.ExternalFun')

function GameLayer:ctor(frameEngine, scene)
    GameLayer.super.ctor(self, frameEngine, scene)

    cs.game.IS_RECIVE_GAME_RESULT = false
    self.wPeiyin = {0, 0, 0, 0}
    self.maCardValue = -1
    --local cards = {0x17,0x07,0x18, 0x08,0x33,0x03,0x04,0x24, 0x39,0x09,0x2B,0x1B,0x2D}
    --GameLogic:sortLiuduiBan(cards)
end

function GameLayer:CreateView()
    return GameViewLayer:create(self):addTo(self)
end

function GameLayer:OnInitGameEngine()
    GameLayer.super.OnInitGameEngine(self)
    self.cbPlayStatus = {0, 0, 0, 0, 0}
    self.cbCardData = {}
    self.cbSpecailType = {}
    self.cbUseSpecialType = {}
    self.cbGiveup = {}
    self.cbSpecailWinDao = {}
    self.wBankerUser = yl.INVALID_CHAIR
end

function GameLayer:OnResetGameEngine()
    GameLayer.super.OnResetGameEngine(self)
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('GameResultLayer')
    if view then
        view:removeFromParent()
    end
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 语音播放开始
function GameLayer:onUserVoiceStart( useritem, filepath )
    local view_id = self:SwitchViewChairID(useritem.wChairID)
    self._gameView:onUserVoiceStart(view_id)
    return view_id
end

-- 语音播放结束
function GameLayer:onUserVoiceEnded( view_id, filepath )
    self._gameView:onUserVoiceEnded(view_id)
end

function GameLayer:SwitchViewChairID(chair)
    local viewid = yl.INVALID_CHAIR
    local nChairCount = self._gameFrame:GetChairCount()
    if self.MeChairID == nil or self.MeChairID == yl.INVALID_CHAIR then
        self.MeChairID = self:GetMeChairID()
        print('self chair id ', self.MeChairID)
    end
    
    local nChairID = self.MeChairID
    --print('nChairCount is : ', nChairCount)
    if chair ~= yl.INVALID_CHAIR and chair < nChairCount and nChairID ~= yl.INVALID_CHAIR then
        viewid = math.mod(chair + math.floor(cmd.GAME_PLAYER * 3 / 2) - nChairID, cmd.GAME_PLAYER) + 1
        print('before viewid is : ', viewid, chair, nChairID)
        if nChairCount == 2 and viewid ~= 3 then        -- 两人时对方总在1号位
            viewid = 1
        --[[
        elseif nChairCount == 3 and viewid == 1 then    -- 三人时1号位总空着
            if nChairID == 0 then
                viewid = 2
            elseif nChairID == 2 then
                viewid = 4
            end
        --]]
        end
    end
    return viewid
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair,time,clockId)
    -- body

    if chair == self:GetMeChairID() and time <= 0 and clockId == cmd.IDI_START_GAME then
        self._gameView.bCanMoveCard = false
	    self._gameView.spritePrompt:hide()
	    self:onOpenCard(0, 1)
    end
    --[[0

    if clockId == cmd.IDI_NULLITY then
        if time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_START_GAME then
        if time == 0 then
            self._gameFrame:setEnterAntiCheatRoom(false)--退出防作弊
            self:onExitTable()
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_CALL_BANKER then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CANCEL)
        end
    elseif clockId == cmd.IDI_TIME_USER_ADD_SCORE then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CHIP + 4)
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_TIME_OPEN_CARD then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_OPENCARD)
        end
    end

    --]]
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 场景信息
function GameLayer:onEventGameScene(cbGameStatus, dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cbGameStatus
    --end
    
	local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self.wPeiyin[wViewChairId] = userItem.wPeiyin
            print('GameLayer _gameView ', wViewChairId)
            self._gameView:OnUpdateUser(wViewChairId, userItem)
            self._gameView:recoverUserScore(wViewChairId, userItem.nTableScore)
        end
    end

    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbGameRule[1][4] == 1 then
        self.maCardValue = 1
    elseif room_data.cbGameRule[1][5] == 1 then
        self.maCardValue = 5
    elseif room_data.cbGameRule[1][6] == 1 then
        self.maCardValue = 10
    end

    self._gameView:onResetView()

	if cbGameStatus == cmd.GS_TK_FREE	then				--空闲状态
        self:onSceneFree(dataBuffer)
    elseif cbGameStatus == cmd.GS_TK_PLAYING  then            --游戏状态
        self:onScenePlaying(dataBuffer)
    end

    -- 启动 心跳
    self:startOrStopHeartBeat( true )
    --self:startOrStopReqLocation( true )
    
    self:dismissPopWait()
end

--空闲场景
function GameLayer:onSceneFree(dataBuffer)
    print("onSceneFree")

    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusFree, dataBuffer)
  
    self.jushu = cmd_table.cbJushu
    if not GlobalUserItem.isAntiCheat() then    --非作弊房间
        if not yl.IS_REPLAY_MODEL then
            self._gameView.btStart:setVisible(true)
        end
        self._gameView:setClockPosition(cmd.MY_VIEWID)
        self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    end

    local tableId = self._gameFrame:GetTableID()
    local userItem = self._gameFrame:getTableUserItem(tableId, self:GetMeChairID())
    if yl.US_READY == userItem.cbUserStatus then
        self._gameView.btStart:hide()
    end
    self._gameView:showJuShu()
    self._gameView:stopAllAction()
    if not yl.IS_REPLAY_MODEL then
        self._gameView.btStart:setVisible(false)
        --self.btYqhy:setVisible(false)
        self:onStartGame()
        --self:onClickReady()
    end

    self._gameView:setCellScore(cmd_table.lCellScore)
   
end

-- 刷新房卡数据
function GameLayer:updatePriRoom()
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end

--游戏场景
function GameLayer:onScenePlaying(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusPlay, dataBuffer)
    print("onScenePlaying")

    self._gameView:setCellScore(cmd_table.lCellScore)
   
    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = cmd_table.cbPlayStatus[1][i]
        self.cbGiveup[i] = cmd_table.cbGiveup[1][i]
        print("onScenePlaying cbPlayStatus", self.cbPlayStatus[i])
    end
    self.cbDynamicJoin = cmd_table.cbDynamicJoin
    local lTurnMaxScore = cmd_table.lTurnMaxScore
    local lTableScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTableScore[i] = cmd_table.lTableScore[1][i]
        if self.cbPlayStatus[i] == 1 and lTableScore[i] ~= 0 then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            --self._gameView:gameAddScore(wViewChairId, lTableScore[i])
        end
    end
    self.wBankerUser = cmd_table.wBankerUser
    for i = 1, cmd.GAME_PLAYER do
        self.cbCardData[i] = self.cbCardData[i] or {}
        for j = 1, cmd.GAME_CARD_NUM do
            self.cbCardData[i][j] = cmd_table.cbHandCardData[i][j]
            print('recover card is ', i, j, self.cbCardData[i][j])
        end
    end


    local lTurnScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTurnScore[i] = cmd_table.lTurnScore[1][i]
    end
    local lCollectScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lCollectScore[i] = cmd_table.lCollectScore[1][i]
    end
    self.recoverStatus = {}
    for i = 1, cmd.GAME_PLAYER do
        self.recoverStatus[i] = cmd_table.cbCurStatus[1][i]
        print('recoverStatus', self.recoverStatus[i])
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbSpecailType[i] = cmd_table.cbSpecailType[1][i] 
        print('cbSpecailType', self.cbSpecailType[i])
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbSpecailWinDao[i] = cmd_table.cbSpecailWinDao[1][i] 
        print('cbSpecailWinDao', self.cbSpecailWinDao[i])
    end
    
    self.jushu = cmd_table.cbJushu
    self._gameView:showJuShu()
    self._gameView.player_num = self:getPlayNum()

    --显示牌并开自己的牌    
    for i = 1, cmd.GAME_PLAYER do repeat
        local wViewChairId = self:SwitchViewChairID(i - 1)
        if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
            break
        end
       
        
        if self.cbPlayStatus[i] == 0 then
            break
        end

        for j = 1, cmd.GAME_CARD_NUM do
            local card = self._gameView.nodeCard[wViewChairId][j]
            --card:setVisible(true)
            if wViewChairId == cmd.MY_VIEWID then          --是自己则打开牌
                --print('recover card is ', wViewChairId, self.cbCardData[i][j])
                local value = GameLogic:getCardValue(self.cbCardData[i][j])
                local color = GameLogic:getCardColor(self.cbCardData[i][j])
                self._gameView:setCardTextureRect(wViewChairId, j, value, color)
            end
        end
        
    until true
    end

    self:updatePriRoom()
    
    self._gameView:setBankerUser(self:SwitchViewChairID(self.wBankerUser))
    self._gameView:gameScenePlaying()
    self:getTypeWithShow()
    self._gameView:setClockPosition()
    --self:SetGameClock(self:GetMeChairID(), cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
end

-- 游戏消息
function GameLayer:onEventGameMessage(sub,dataBuffer)
	if sub == cmd.SUB_S_CALL_BANKER then 
		self:onSubCallBanker(dataBuffer)
	elseif sub == cmd.SUB_S_SEND_CARD then 
		self:onSubSendCard(dataBuffer)
	elseif sub == cmd.SUB_S_OPEN_CARD then 
		self:onSubOpenCard(dataBuffer)
	elseif sub == cmd.SUB_S_PLAYER_EXIT then 
		self:onSubPlayerExit(dataBuffer)
	elseif sub == cmd.SUB_S_GAME_END then 
		self:onSubGameEnd(dataBuffer)
    elseif sub == cmd.SUB_S_READY_STATE then 
		--self:onSubSendCard(dataBuffer)
        self.game_end = false
	else
		print("unknow gamemessage sub is"..sub)
	end
end

function GameLayer:getPlayNum()
    local num = 0
    for i = 1, cmd.GAME_PLAYER do
        if self.cbPlayStatus[i] == 1 then
            num = num + 1
        end
    end

    if num > PassRoom:getInstance():getChairCount() then
        num = PassRoom:getInstance():getChairCount()
    end
    return num
end

--用户叫庄
function GameLayer:onSubCallBanker(dataBuffer)

    local wCallBanker = dataBuffer:readword()
    local bFirstTimes = dataBuffer:readbool()

    if bFirstTimes then
	    for i = 1, cmd.GAME_PLAYER do
	        local userItem = self._gameFrame:getTableUserItem(self._gameFrame:GetTableID(), i - 1)
	        if userItem and not self.cbDynamicJoin then
                print('222222222222222222222222')
	        	self.cbPlayStatus[i] = 1
	        end
	    end
    end

    --self._gameView:gameCallBanker(self:SwitchViewChairID(wCallBanker), bFirstTimes)
    --self._gameView:setClockPosition(self:SwitchViewChairID(wCallBanker))
    --self:SetGameClock(wCallBanker, cmd.IDI_CALL_BANKER, cmd.TIME_USER_CALL_BANKER)
end

--游戏开始
function GameLayer:onSubGameStart(dataBuffer)
    -- 局数
    --[[
    PassRoom:getInstance().m_tabPriData.dwPlayCount = PassRoom:getInstance().m_tabPriData.dwPlayCount + 1

    local int64 = Integer64:new()
    local lTurnMaxScore = dataBuffer:readscore(int64):getvalue()
    self.wBankerUser = dataBuffer:readword()
    print('cur bankuser is ', self.wBankerUser, PassRoom:getInstance().m_tabPriData.dwPlayCount)
    self._gameView:setBankerUser(self:SwitchViewChairID(self.wBankerUser))

    self._gameView:setTurnMaxScore(lTurnMaxScore)

    self._gameView:gameStart(self:SwitchViewChairID(self.wBankerUser))
    self._gameView:setClockPosition()
    self:SetGameClock(self.wBankerUser, cmd.IDI_TIME_USER_ADD_SCORE, cmd.TIME_USER_ADD_SCORE)
    --]]
end

-- 刷新界面
function GameLayer:updateView()
end

--用户下注
function GameLayer:onSubAddScore(dataBuffer)
    local int64 = Integer64:new()
    local wAddScoreUser = dataBuffer:readword()
    local lAddScoreCount = dataBuffer:readscore(int64):getvalue()

    local userViewId = self:SwitchViewChairID(wAddScoreUser)
    print("onSubAddScore",wAddScoreUser,  userViewId, lAddScoreCount)
    self._gameView:gameAddScore(userViewId, lAddScoreCount)

    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/ADD_SCORE.WAV")
end

--发牌消息
function GameLayer:onSubSendCard(dataBuffer)
    --if not yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cmd.GS_TK_PLAYING
        if self._gameView and self._gameView._priView then
            self._gameView._priView:onRefreshInviteBtn()
        end

    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_SendCard, dataBuffer)

    print('self chairid is ', self:GetMeChairID())
    dump(cmd_table)
    if cmd_table.wCurrentUser ~= self:GetMeChairID() then
        return
    end

    --end
    
    self.cbCardData[cmd_table.wCurrentUser + 1] = {}
    local tmpCard = {}
    local specailCard = {}
    for j = 1, cmd.GAME_CARD_NUM do
        local card = cmd_table.cbCardData[1][j]
        if GameLogic:getCardValue(card) == 0x01 then
            table.insert(specailCard, card)
        else
            table.insert(tmpCard, card)
        end
    end
    local index = 1
    for j = 1, #specailCard do
        self.cbCardData[cmd_table.wCurrentUser + 1][index] = specailCard[j]
        index = index + 1
    end
    for j = 1, #tmpCard do
        self.cbCardData[cmd_table.wCurrentUser + 1][index] = tmpCard[j]
        index = index + 1
    end
    


    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = cmd_table.cbPlayerStatus[1][i]
        print('cbPlayStatus', self.cbPlayStatus[i], i)
    end

    for i = 1, cmd.GAME_PLAYER do
        self._gameView.gamePlayStatus[i] = self.cbPlayStatus[i]
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbSpecailType[i] = cmd_table.cbSpecailType[1][i]
        print('cbSpecailType', self.cbSpecailType[i])
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbSpecailWinDao[i] = cmd_table.cbSpecailWinDao[1][i]
        print('cbSpecailWinDao', self.cbSpecailWinDao[i])
    end
    
    self.jushu = dataBuffer:readbyte()
    self._gameView:showJuShu()
    self._gameView.player_num = self:getPlayNum()
     -- 局数
    --PassRoom:getInstance().m_tabPriData.dwPlayCount = jushu
    print('cur jushu is ', jushu, self._gameView.player_num)
    dump(self.cbCardData)
    
    --打开自己的牌
    for i = 1, cmd.GAME_CARD_NUM do
        local index = self:GetMeChairID() + 1
        local data = self.cbCardData[index][i]
        local value = GameLogic:getCardValue(data)
        local color = GameLogic:getCardColor(data)
        local card = self._gameView.nodeCard[cmd.MY_VIEWID][i]
        --card:removeChildByName('ma_card_sign')
        self._gameView:setCardTextureRect(cmd.MY_VIEWID, i, value, color)
    end
    self._gameView:gameSendCard(self:SwitchViewChairID(self.wBankerUser), self:getPlayNum()*cmd.GAME_CARD_NUM)
    --[[
    for i = GameLogic.TYPE_DUIZI, GameLogic.TYPE_TONGHUASHUN do
        local index = self:GetMeChairID() + 1
        local is_show = GameLogic:isHasCurType(self.cbCardData[index], i, cmd.GAME_CARD_NUM)
        
        self._gameView:showBtn(16 + i - GameLogic.TYPE_DUIZI, is_show)
    end
    --]]
    self:getTypeWithShow()

    --self:KillGameClock()
    
    --self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
end

function GameLayer:getCurCards()
    local index = self:GetMeChairID() + 1
    local tmpCards = GameLogic:copyTab(self.cbCardData[index])
    for i = 1, cmd.GAME_CARD_NUM do
        if self._gameView.cbOpenCardData[i] and self._gameView.cbOpenCardData[i] ~= 0 then
            for j = 1, cmd.GAME_CARD_NUM do
                if tmpCards[j] == self._gameView.cbOpenCardData[i] then
                    tmpCards[j] = 0
                end
            end
        end
    end
    --[[
    for i = 1, #tmpCards do
        if tmpCards[i] == 0 then
            table.remove(tmpCards, i)
        end
    end
    --]]

    return tmpCards
end

function GameLayer:getTypeWithShow()
    local tmpCards = self:getCurCards()

    local len = #tmpCards
    do 
        local i = 1
        while i <= len do
            if tmpCards[i] == 0 then
                table.remove(tmpCards, i)
                len = len - 1
            else
                i = i + 1
            end
        end
    end

    for i = 1, #tmpCards do
        print('left card is ', tmpCards[i])
    end

    print('left card is ', #tmpCards)
    for i = GameLogic.TYPE_DUIZI, GameLogic.TYPE_TONGHUASHUN do
        
        local is_show = GameLogic:isHasCurType(tmpCards, i, #tmpCards)
        
        self._gameView:showBtn(16 + i - GameLogic.TYPE_DUIZI, is_show)
    end

    local index = self:GetMeChairID() + 1
    if self.cbSpecailType[index] and self.cbSpecailType[index] > 0 then
        self._gameView.img_specail:texture('room/btn_special.png')
        self._gameView.img_specail:setTouchEnabled(true)
    else
        self._gameView.img_specail:texture('room/paixingann02.png')
        self._gameView.img_specail:setTouchEnabled(false)
    end
end

function GameLayer:getPos(type, start_index)
    local tmpCards = self:getCurCards()
    
    local pos_index = GameLogic:getCardTypeIndex(tmpCards, type + tonumber(GameLogic.TYPE_DUIZI), #tmpCards, start_index)
    self._gameView:MvCard(pos_index, type)
end

--用户摊牌
function GameLayer:onSubOpenCard(dataBuffer)
    self.game_end = false
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_Open_Card, dataBuffer)
    local wPlayerID = cmd_table.wPlayerID
    local bOpen = cmd_table.bOpen
    self.open_card_special_type = {}
    self.open_card_isUseSpeicalType = {}
    self.cbIsGiveup = {}
    for i = 1, cmd.GAME_PLAYER do
        self.open_card_special_type[i] = cmd_table.bSpecialType[1][i]
        self.open_card_isUseSpeicalType[i] = cmd_table.bIsSPecaialType[1][i]
        self.cbIsGiveup[i] = cmd_table.cbIsGiveup[1][i]
    end

    for i = 1, cmd.GAME_PLAYER do
        if self.cbIsGiveup[i] == 1 then
            local wViewChairId = self:SwitchViewChairID(i -1)
            self._gameView:setOpenCardGiveup(wViewChairId, true)
        end
    end

    if self.cbIsGiveup[self:GetMeChairID() + 1] == 1 then

        if self._gameView.specail_node then
            self._gameView.specail_node:removeFromParent()
            self._gameView.specail_node = nil
        end

        if self._gameView.giveupnode then
            self._gameView.giveupnode:removeFromParent()
            self._gameView.giveupnode = nil
        end

        --self._gameView:setOpenCardGiveup(3, true)
        return
    end
    
    
    
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    print('onSubOpenCard wViewChairId is ', wViewChairId)
    if wViewChairId == cmd.MY_VIEWID then
        self:openCard(wPlayerID)
    else 
        self._gameView:setOpenCardVisible(wViewChairId, true)
    end
   
    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/OPEN_CARD.wav")
end

--用户强退
function GameLayer:onSubPlayerExit(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    self.cbPlayStatus[wPlayerID + 1] = 0
    self._gameView.nodePlayer[wViewChairId]:setVisible(false)
    self._gameView.bCanMoveCard = false
    self._gameView.btOpenCard:setVisible(false)
    --self._gameView.btPrompt:setVisible(false)
    self._gameView.spritePrompt:setVisible(false)
    self._gameView.spriteCardBG:setVisible(false)
    self._gameView:setOpenCardVisible(wViewChairId, false)
end

--游戏结束
function GameLayer:onSubGameEnd(dataBuffer)
    self.game_end = true
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_GameEnd, dataBuffer)
    dump(cmd_table)

    local lGameTax = {}
    for i = 1, cmd.GAME_PLAYER do
        lGameTax[i] = cmd_table.lGameTax[1][i]
    end

    self.lGameScore = {}
    for i = 1, cmd.GAME_PLAYER do
        self.lGameScore[i] = cmd_table.lGameScore[1][i]
        print('... gamescore i ', i,  self.lGameScore[i])
        if self.cbPlayStatus[i] == 1 then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:setUserTableScore(wViewChairId,  self.lGameScore[i])
            --self._gameView:runWinLoseAnimate(wViewChairId, lGameScore[i])
        end
    end

       --- 三道得分
    self.sandaoScore = {}
    for i = 1, cmd.GAME_PLAYER do
        self.sandaoScore[i] = {}
        for j = 1, 3 do
            self.sandaoScore[i][j] = cmd_table.m_lscore[i][j] 
            print('... sandao score i ', i, ' j ', j, self.sandaoScore[i][j])
        end 
    end
    --- 三道各自的类型
    self.sandaoType = {}
    for i = 1, cmd.GAME_PLAYER do
        self.sandaoType[i] = {}
        for j = 1, 3 do
            self.sandaoType[i][j] = cmd_table.m_cbCardType[i][j]
        end 
    end


    --开牌
    local data = {}
    for i = 1, cmd.GAME_PLAYER do
        if not self.cbCardData[i] then
            self.cbCardData[i] = {}
        end
        for j = 1, cmd.GAME_CARD_NUM do
            self.cbCardData[i][j] = cmd_table.cbCardData[i][j]
            print('... open card cbdata i ', i, ' j ', j, self.cbCardData[i][j])
        end
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbUseSpecialType[i] = cmd_table.cbUseSpcialType[1][i]
        self.cbGiveup[i] = cmd_table.cbGiveup[1][i]
    end
    
    self.cbSpeicalType = {}
    for i = 1, cmd.GAME_PLAYER do
        self.cbSpeicalType[i] = cmd_table.cbSpeicalType[1][i]
    end

     self.cbSpeicalScore = {}
    for i = 1, cmd.GAME_PLAYER do
        self.cbSpeicalScore[i] = cmd_table.cbSpeicalScore[1][i]
    end
    
    local is_jiesan = cmd_table.isJieSan
    

    local pre_player_num = self:getPlayNum()
    self._gameView.player_num = pre_player_num
    print('is_jiesan ... ', is_jiesan, self._gameView.player_num)

    if is_jiesan ~= 1 then
        for i = 1, cmd.GAME_PLAYER do
            if self.cbSpeicalType[i] == GameLogic.OX_SANTONGHUA - GameLogic.OX_VALUEO then
                GameLogic:sortCard(self.cbCardData[i])
            elseif self.cbSpeicalType[i] == GameLogic.OX_LIUDUI  - GameLogic.OX_VALUEO then
                GameLogic:sortLiuduiBan(self.cbCardData[i])
            elseif self.cbSpeicalType[i] == GameLogic.OX_SANSHUNZI  - GameLogic.OX_VALUEO then
                 GameLogic:sortSanShunzi(self.cbCardData[i])
            elseif self.cbSpeicalType[i] == GameLogic.OX_YILONG  - GameLogic.OX_VALUEO then
                GameLogic:sortYiLong(self.cbCardData[i])
            elseif self.cbSpeicalType[i] == GameLogic.OX_QINGLONG  - GameLogic.OX_VALUEO then
                GameLogic:sortYiLong(self.cbCardData[i])
            end  
        end
    end

    self.is_jiesan = is_jiesan
    self:sortIndex()
    self:createResultLayer()
    
    if is_jiesan == 1 then
        self.m_userRecord = {}
	    for i = 1, self:getPlayNum() do
	    	self.m_userRecord[i] = {}
	    	self.m_userRecord[i].cbHuCount =  self.lGameScore[i]
	    	self.m_userRecord[i].cbMingGang =  self.lGameScore[i]
	    	self.m_userRecord[i].cbAnGang =  self.lGameScore[i]
	    	self.m_userRecord[i].cbMaCount =  self.lGameScore[i]
	    	self.m_userRecord[i].lDetailScore = {}
	    end
        return
    end

    for i = 1, cmd.GAME_PLAYER do
        self._gameView.gamePlayStatus[i] = self.cbPlayStatus[i]
    end
    
    self._gameView:gameOpenCard()
       


    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = 0
    end

    --local index = self:GetMeChairID() + 1
    --self._gameView:gameEnd(lGameScore[index] > 0)

    self._gameView:setClockPosition(cmd.MY_VIEWID)
    self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_END.WAV")

    print('self.m_userRecord is , ', pre_player_num)
    self.m_userRecord = {}
	for i = 1, pre_player_num do
		self.m_userRecord[i] = {}
		self.m_userRecord[i].cbHuCount =  self.lGameScore[i]
		self.m_userRecord[i].cbMingGang =  self.lGameScore[i]
		self.m_userRecord[i].cbAnGang =  self.lGameScore[i]
		self.m_userRecord[i].cbMaCount =  self.lGameScore[i]
		self.m_userRecord[i].lDetailScore = {}
	end
end

-- 用户配音改变
function GameLayer:onUserPeiyinChange(user_item)
	local view_id = self:SwitchViewChairID(user_item.wChairID)
    self.wPeiyin[view_id] = user_item.wPeiyin
    print('user peiyin modify ', self.wPeiyin[view_id])
end

function GameLayer:createResultLayer()
    local result = {}
    for i = 1, self._gameView.player_num do
		local wViewChairId = self:SwitchViewChairID(i - 1)
        if wViewChairId ~= yl.INVALID_CHAIR then
            local user = self._gameFrame:getTableUserItem(self:GetMeTableID(), i - 1)
            if user then
                table.insert(result, user)
            end
        end
    end
    
    --local is_win = self.lGameScore[self:GetMeTableID() + 1] >= 0
    local layer = helper.app.getFromScene('GameResultLayer')
	if layer then
		layer:removeFromParent()
	end
    local path = cs.game.SRC .. 'room.GameResultLayer'
    helper.pop.popLayer(path, nil, {self._gameView, self, result, self.is_jiesan})
end

function GameLayer:sortIndex()
    self.sort_head = {}
    self.sort_mid = {}
    self.sort_foot = {}
    for i = 1, self._gameView.player_num do repeat

        if self.cbSpeicalType[i] > 0 then
            local val = {i, 10000 + self.cbSpeicalType[i]}
            table.insert(self.sort_head, val)
            table.insert(self.sort_mid, val)
            table.insert(self.sort_foot, val)
            break
        end
        local val_head = {i, self.sandaoScore[i][1]}
        table.insert(self.sort_head, val_head)
        local val_mid = {i, self.sandaoScore[i][2]}
        table.insert(self.sort_mid, val_mid)
        local val_foot = {i, self.sandaoScore[i][3]}
        table.insert(self.sort_foot, val_foot)
        until true
    end

    table.sort(self.sort_head, function(a, b)
        return a[2] < b[2]
    end)

    table.sort(self.sort_mid, function(a, b)
        return a[2] < b[2]
    end)

    table.sort(self.sort_foot, function(a, b)
        return a[2] < b[2]
    end)

    dump(self.sort_head)
end

function GameLayer:getCardData(index)
    local index = self:GetMeChairID() + 1
    local data = self.cbCardData[index][i]
    return data
end

function GameLayer:getDetailScore()
	return self.m_userRecord
end

function GameLayer:getUserInfoByChairID(chairId)
	local viewId = self:SwitchViewChairID(chairId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getUserInfoByViewID(viewId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:onExitRoom()
    --self:startOrStopReqLocation( false )
    self:startOrStopHeartBeat( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end 
    end
    self:removeFromParent()
end

--开始游戏
function GameLayer:onStartGame()
    -- body
    self:KillGameClock()
    self._gameView:onResetView()
    self._gameFrame:SendUserReady()
end

--将视图id转换为普通id
function GameLayer:isPlayerPlaying(viewId)
    if viewId < 1 or viewId > cmd.GAME_PLAYER then
        print("view chair id error!")
        return false
    end

    for i = 1, cmd.GAME_PLAYER do
        if self:SwitchViewChairID(i - 1) == viewId then
            if self.cbPlayStatus[i] == 1 then
                return true
            end
        end
    end

    return false
end

function GameLayer:sendCardFinish()
    self._gameView:setClockPosition()
    self:SetGameClock(self:GetMeChairID(), cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
end

function GameLayer:openCard(chairId)
    --排列cbCardData
    local viewId = self:SwitchViewChairID(chairId)
    local index = chairId + 1
   
     if self.open_card_isUseSpeicalType and self.open_card_isUseSpeicalType[index] and
        self.open_card_isUseSpeicalType[index] == 1 then
        local path = self._gameView:getSpeicalTypePngPath(self.open_card_special_type[index])
        self._gameView.special_flag[viewId]:show()
        self._gameView.special_flag[viewId]:texture(path)
        GameLogic:sortYiLong(self.cbCardData[index])
        if self.cbSpecailType[index] == GameLogic.OX_SANQING - GameLogic.OX_VALUEO then
            GameLogic:sortSanQing(self.cbCardData[index])
        elseif self.cbSpecailType[index] == GameLogic.OX_SHUANGSHUNQING  - GameLogic.OX_VALUEO then
            GameLogic:sortYiLong(self.cbCardData[index])
        elseif self.cbSpecailType[index] == GameLogic.OX_SHUANGSANTIAO  - GameLogic.OX_VALUEO then
            GameLogic:sortShuangSanTiao(self.cbCardData[index])
        elseif self.cbSpecailType[index] == GameLogic.OX_QUANSHUNQING - GameLogic.OX_VALUEO then
            GameLogic:sortSanShunzi(self.cbCardData[index])
        elseif self.cbSpecailType[index] == GameLogic.OX_SHUANGSITIAO  - GameLogic.OX_VALUEO then
            GameLogic:sortShuangSiTiao(self.cbCardData[index])
        elseif self.cbSpecailType[index] == GameLogic.OX_QUANSANTIAO  - GameLogic.OX_VALUEO then
            GameLogic:sortQuanSanTiao(self.cbCardData[index])
        end  
        --return
    end
 
    
    local compare_bg = self._gameView.compare_bg[viewId]
    compare_bg:show()
    self._gameView.score_bg[viewId]:hide()
    for i = 1, cmd.GAME_CARD_NUM do
        local card = compare_bg:child('card_' .. i)
        local data = self._gameView.cbOpenCardData[i]
        if self.open_card_isUseSpeicalType and self.open_card_isUseSpeicalType[index] and 
        self.open_card_isUseSpeicalType[index] == 1 then
            data = self.cbCardData[index][i]
        end
        if data == 0 then
            data = self.cbCardData[index][i]
        end
        print('compare_bg card is ', data, viewId)
        local value = GameLogic:getCardValue(data)
        local color = GameLogic:getCardColor(data)
        local png = string.format('card/card%d%02d.png', color, value)
        card:texture(png)
       self._gameView:addMaCardSign(card, value, color)
       
    end
    
    --self._gameView:openCard(viewId, cbOx)
end

function GameLayer:getMeCardLogicValue(num)
    local index = self:GetMeChairID() + 1

    --此段为测试错误
    if nil == index then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == num then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == self.cbCardData[index][num] then
        showToast(self, "nil == index", 1)
        return false
    end

    return GameLogic:getCardLogicValue(self.cbCardData[index][num])
end

function GameLayer:getOxCard(cbCardData)
    return GameLogic:getOxCard(cbCardData)
end

--********************   发送消息     *********************--
function GameLayer:onBanker(cbBanker)
    local dataBuffer = CCmd_Data:create(1)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME,cmd.SUB_C_CALL_BANKER)
    dataBuffer:pushbyte(cbBanker)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onAddScore(lScore)
	print("下注金币", lScore)
    local dataBuffer = CCmd_Data:create(8)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_ADD_SCORE)
    dataBuffer:pushscore(lScore)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onOpenCard(is_special_type, is_giveup)
    local index = self:GetMeChairID() + 1
    local bOx = GameLogic:getOxCard(self.cbCardData[index])
    is_special_type = is_special_type or 0

    is_giveup = is_giveup or 0

    local cmd_data = ExternalFun.create_netdata(cmd.CMD_C_OxCard)
    cmd_data:pushbyte(1)
    for i = 1, cmd.GAME_CARD_NUM do
        if is_special_type == 0 then
            if is_giveup == 1 then
                cmd_data:pushbyte(self.cbCardData[index][i]) 
            else
                cmd_data:pushbyte(self._gameView.cbOpenCardData[i])
            end
        else
            cmd_data:pushbyte(self.cbCardData[index][i]) 
        end
    end
    
    cmd_data:pushbyte(is_special_type)
    cmd_data:pushbyte(is_giveup)
    return self:SendData(cmd.SUB_C_OPEN_CARD, cmd_data)

end


-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
       self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
       print('stop.............replay')
       self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
    if self._gameView.bCanNextReplay then
        if not PassRoom:getInstance():getNetFrame():doNextReplay() then
            self:startOrStopReplay( false )
        end
    end
     
end


-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:stop( yl.ActionTag.HEART )
            
            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )
            
        end
    else
        print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end

-- 检测心跳
function GameLayer:checkHeartBeat()
    print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end

-- 重连
function GameLayer:doReConnect()
    print('网络重连。。。')
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()
    PassRoom:getInstance():onLoginRoom(GlobalUserItem.dwCurServerID )
    --PassRoom:getInstance():getNetFrame():onSearchRoom( PassRoom:getInstance().m_tabPriData.szServerID )
end


-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
        print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
        print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置 
function GameLayer:onCheckSocketIsOK()
   self:startCheckIsSocketOK( false )
   self.check_is_socket_ok_times = 0
end


return GameLayer
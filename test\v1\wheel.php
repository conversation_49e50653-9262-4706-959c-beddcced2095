<?php
//活动接口
require(APPPATH.'/libraries/REST_Controller.php');
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Config.php');

class Wheel extends REST_Controller{
    /*
     * 角色信息
     */
    private $_role;
    /*
    * 大转盘
    */
    private $_wheel;

    /**
     * Wheel constructor.
     */
    private $_game;

    public function __construct()
    {
        parent::__construct();

        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('activity_model');
        $this->player_model->set_database($this->_channel['game_id']);

        $this->_game = $this->server_model->get_game_by_id($this->_channel['game_id']);

        $role_id = $this->input->post('uid');

        // 获取角色信息
        $role = $this->player_model->get_role_info($role_id);

        if(!$role){
            $this->response(array('code'=>1,'msg'=>'角色信息不存在'));
        }

        $this->_role = $role;
        $template = $this->activity_model->get_channel_wheel_by_type($this->_channel['channel_id']);

        if(!$template) {
            $this->response(array('code'=>1,'msg'=>'未查询到大转盘活动1'));
        }

        $activities = $this->activity_model->get_wheel_with_template_template_id($template['id']);

        if(!$activities) {
            $this->response(array('code'=>1,'msg'=>'未查询到大转盘活动2'));
        }

        $wheel = $this->activity_model->get_wheel_by_id($activities['wheel_id']);

        if(!$wheel) {
            $this->response(array('code'=>1,'msg'=>'未查询到大转盘活动3'));
        }

        $this->_wheel = $wheel;

    }

    public function index_post()
    {
        $result = array();

        $result['status'] = 0;

        if($this->_wheel['begin_time']>time()) {
            $result['status'] = 1;
        }

        if($this->_wheel['end_time']<time()) {
            $result['status'] = 2;
        }

        $result['content'] = $this->_wheel['content'];
        $result['begin_time'] = $this->_wheel['begin_time']*1;
        $result['end_time'] = $this->_wheel['end_time']*1;

        $result['prizes'] = $this->_format_prizes($this->_wheel['prizes']);
        $result['progress_max'] = $this->_wheel['condition_v']*1;

        $my_logs = $this->player_model->get_role_wheel_logs($this->_wheel['id'],$this->_role['UserID'],$this->_channel['game_id']);
        $all_logs = $this->activity_model->get_wheel_latest_logs($this->_wheel['id'],$this->_channel['game_id'],10);

        $result['all_logs'] = $this->_format_logs($all_logs,'all');
        $result['my_logs'] = $this->_format_logs($my_logs,'my');

        $total = $this->_get_total_draws();//获取消耗房卡数
        $additional_num = $this->_get_addition_draws();//获取附加抽奖次数
        //如果是房卡消耗和每日结算类型
        if ($this->_wheel['condition_k'] == 'ROOMCARD_COST' && $this->_wheel['clearing_type'] == 1){
            $result['progress_value'] = ($total['cost_num'] % $this->_wheel['condition_v'])*1;
            $result['remain_draws'] = intval($total['surplus_lottery_num']);
        }elseif($this->_wheel['condition_k'] == 'SINGLE_RECHARGE_COST') {
            $result['progress_value'] = intval($total);
            $result['remain_draws'] = intval($total/$this->_wheel['condition_v']) - count($my_logs);
        }else{
            $result['progress_value'] = ($total % $this->_wheel['condition_v'])*1;
            $result['remain_draws'] = intval($total/$this->_wheel['condition_v']) - count($my_logs);
        }

        $result['remain_draws'] += $additional_num;

        $this->response(array('code'=>0,'msg'=>'','data'=>$result));

    }

    private function _get_total_draws()
    {
        $total = 0;

        switch($this->_wheel['condition_k']) {
            case 'ROOMCARD_COST':
                if ($this->_wheel['clearing_type'] == 1){//抽奖次数结算类型为每日
                    $total = $this->_surplus_lottery_num();
                }else if($this->_wheel['clearing_type'] == 2){//抽奖次数结算类型为活动结束
                    $total = $this->player_model->get_role_roomcard_cost($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time']);
                }
                break;
            case 'PLAY_LITTLE_GAME':
                $total = $this->player_model->get_role_game_nums($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time'],'small');
                break;
            case 'PLAY_BIG_GAME':
                $total = $this->player_model->get_role_game_nums($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time'],'big');
                break;
            case 'WIN_LITTLE_GAME':
                $total = $this->player_model->get_role_win_nums($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time']);
                break;
            case 'RECHARGE_COST':
//                $total = $this->player_model->get_role_recharge_cost($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time']);
                break;
            case 'SINGLE_RECHARGE_COST':
                $total = $this->player_model->get_role_single_recharge_count($this->_role['UserID'],$this->_channel['game_id'],$this->_wheel['begin_time'],$this->_wheel['end_time'],$this->_wheel['condition_v']);
                break;
            case 'CLUB_ROOMCARD_COST':
                $total = $this->player_model->get_role_club_roomcard_cost($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time']);
                break;
            case 'CLUB_LITTLE_GAME':
                $total = $this->player_model->get_role_game_nums($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time'],'small',true);
                break;
            case 'CLUB_BIG_GAME':
                $total = $this->player_model->get_role_game_nums($this->_role['UserID'],$this->_wheel['begin_time'],$this->_wheel['end_time'],'big',true);
                break;
            default:
                break;
        }

        return $total;
    }

    //获取附加抽奖次数
    private function _get_addition_draws()
    {
        switch($this->_wheel['addition_k']) {
            case 'share':
                $addition = $this->player_model->get_role_share_info($this->_role['UserID'],date('Y-m-d'));
                $num = empty(intval($addition/$this->_wheel['addition_v']))?0:1;
                break;
            default:
                $num = 0;
                break;
        }

        return $num;
    }

    /**
     * 抽奖
     */
    public function draw_post()
    {
        $result = array();

        if($this->_wheel['show_begin_time']>time()) {
            $this->response(array('code'=>1,'msg'=>'活动未开始'));
        }

        if($this->_wheel['show_end_time']<time()) {
            $this->response(array('code'=>1,'msg'=>'活动已过期'));
        }

        // 检查抽奖剩余次数
        $my_logs = $this->player_model->get_role_wheel_logs($this->_wheel['id'],$this->_role['UserID'],$this->_channel['game_id']);

        $total = $this->_get_total_draws();//获取消耗房卡数
        $additional_num = $this->_get_addition_draws();//获取附加抽奖次数

        if ($this->_wheel['condition_k'] == 'ROOMCARD_COST' && $this->_wheel['clearing_type'] == 1){
            $remain_draws = intval($total['surplus_lottery_num']);
        }else{
            $remain_draws = intval($total/$this->_wheel['condition_v']) - count($my_logs);
        }


        $remain_draws += $additional_num;

        if($remain_draws < 1) {
            $this->response(array('code'=>1,'msg'=>'无可用抽奖次数'));
        }

        $prize = $this->_get_rand($this->_wheel['prizes'],$index);

        if(!$prize) {
            $this->response(array('code'=>1,'msg'=>'抽奖失败'));
        }

        // 插入领奖日志
        $log = array(
            'role_id'   => $this->_role['UserID'],
            'role_name'   => $this->_role['NickName'],
            'wheel_id'    => $this->_wheel['id'],
            'item_id'     => $prize['item_id'],
            'item_num'    => $prize['num'],
            'game_id'     => $this->_channel['game_id'],
            'channel_id'  => $this->_channel['channel_id'],
            'draw_time'     => time()
        );

        $this->db->where('role_id',$this->_role['UserID']);
        $this->db->where('FROM_UNIXTIME(draw_time,"%Y-%m-%d")',date('Y-m-d'));
        $this->db->order_by('draw_time','desc');
        $query = $this->db->get('wheel_logs');
        $draw_logs = $query->row_array();

        if(!empty($draw_logs)){
            if($draw_logs['draw_time']+5 > time()){
                $this->response(array('code'=>1,'msg'=>'抽奖失败,请重新尝试'));
            }
        }

        if ($log_id = $this->player_model->insert_role_wheel_logs($log)) {
            // 更新库存
            $this->activity_model->update_wheel_prize($this->_wheel['id'],$index+1,array('stock'=>'stock-1'));

            $result['prize'] = $this->_format_prize($index,$prize,$log_id);
            $result['log'] = $this->_format_log($this->activity_model->get_wheel_log_by_id($log_id));
            $result['remain_draws'] = $remain_draws-1;

            $this->response(array('code'=>0,'msg'=>'','data'=>$result));

        } else {
            $this->response(array('code'=>1,'msg'=>'抽奖失败'));
        }
    }

    /**
     * 用户领奖
     */
    public function receive_post()
    {
        $receive_id = $this->input->post('id');

        $log = $this->activity_model->get_wheel_log_by_id($receive_id);

        if(!$log) {
            $this->response(array('code'=>1,'msg'=>'未查找到对应记录'));
        }

        if($log['status'] == 2) {
            $this->response(array('code'=>1,'msg'=>'奖励已领取，请勿重复领取'));
        }

        $item = $this->activity_model->get_wheel_item_by_id($log['item_id']);

        // 获取奖励的数量
        $num = $item['value']*$log['item_num'];

        $result = array();

        if($item['type'] == 2) { // 红包类
            // 判断用户是否关注
            $this->load->model('mp_model');

            $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'],$this->_channel['game_id']);

            if(!$mp_user) {
                $this->response(array('code'=>1,'msg'=>'请关注【'.$this->_game['mp_name'].'】微信公众号后按提示领取'));
            }
            // 执行发红包操作
            if(!$this->_send_redpack($mp_user['openid'],$num)) {
                $this->response(array('code'=>1,'msg'=>'领取失败，请重试'));
            }

            $this->activity_model->update_wheel_log($log['id'],array(
                'status'=>2,
                'receive_time'=>time()
            ));


        } else  if($item['type'] == 3) {
            // 更新用户资料

            $data = array();

            if($this->input->post('name')) {
                $data['name'] = $this->input->post('name');
            }

            if($this->input->post('phone')) {
                $data['phone'] = $this->input->post('phone');
            }

            if($this->input->post('address')) {
                $data['address'] = $this->input->post('address');
            }

            $data['status'] = 1;
            $data['receive_time'] = time();

            $this->activity_model->update_wheel_log($log['id'],$data);
        } else if($item['type'] == 4) {

                // 执行发送奖励操作
                if($this->player_model->insert_Ticket($this->_role['UserID'],$num,'5','wheel')) {

                    // 更新奖励记录
                    $this->activity_model->update_wheel_log($log['id'],array(
                        'status'=>2,
                        'receive_time'=>time()
                    ));

                }

                $role_score2 = $this->player_model->get_role_score2($this->_role['UserID']);

                $result['jianquan'] = $role_score2['Ticket']*1;

        }else if($item['type'] == 1) { // 房卡

            // 执行发送奖励操作
            if($this->player_model->update_role_score($this->_role['UserID'],$num)) {

                // 更新奖励记录
                $this->activity_model->update_wheel_log($log['id'],array(
                    'status'=>2,
                    'receive_time'=>time()
                ));

                //更新房卡记录
                // 插入记录
                $data = array(
                    'a_id'=>1,
                    'a_name'=>'admin',
                    'b_id'=>$this->_role['UserID'],
                    'b_name'=>$this->_role['NickName'],
                    'score'=>$num,
                    'type'=>4,
                    'is_order'=>0,
                    'remark'=>'大转盘赠送<ID:'.$receive_id.'>',
                    'game_id'=>$this->_channel['game_id'],
                    'create_time'=>date('Y-m-d H:i:s')
                );

                $this->db->insert('recharges',$data);
            }

            $role_score = $this->player_model->get_role_score($this->_role['UserID']);

            $result['fangka'] = $role_score['RoomCard']*1;

        } else if($item['type'] == 5) { // 道具

            $this->player_model->proc_role_item($this->_role['UserID'], $item['item_id'], $num, 'Wheel');

            $this->activity_model->update_wheel_log($log['id'],array(
                'status'=>2,
                'receive_time'=>time()
            ));

        }else {
             $this->response(array('code'=>1,'msg'=>'未知的道具类型'));

        }

        $result['prize'] =$this->_format_log($this->activity_model->get_wheel_log_by_id($receive_id));

        $this->response(array('code'=>0,'msg'=>'','data'=>$result));

    }
    
    private function _get_rand($proArr,&$index) {
        $result = array();
        foreach ($proArr as $key => $val) {

            // 检查库存
            if($val['stock']< 1) {
                $arr[$key] = 0;
            } else {
                $arr[$key] = $val['weight'];
            }
        }
        // 概率数组的总概率
        $proSum = array_sum($arr);
        if($proSum > 0) {
            asort($arr);
            // 概率数组循环
            foreach ($arr as $k => $v) {
                $randNum = mt_rand(1, $proSum);
                if ($randNum <= $v) {
                    $result = $proArr[$k];
                    $index = $k;
                    break;
                } else {
                    $proSum -= $v;
                }
            }
        }
        return $result;
    }

    /*
     * 格式化日志
     */
    private function _format_logs($logs,$type)
{
    $result = array();

    foreach($logs as $k=>$log)
    {
        $result[$k]['id'] = $log['id']*1;
        $result[$k]['role_id'] = $log['role_id']*1;
        $result[$k]['role_name'] = $log['role_name'];
        $result[$k]['item_id'] = $log['item_id']*1;
        $result[$k]['item_num'] = $log['item_num']*1;

        $item = $this->activity_model->get_wheel_item_by_id($log['item_id']);
        $result[$k]['item_type'] = $item['type']*1;
        $result[$k]['item_name'] = $item['name'];

        $result[$k]['draw_time'] = $log['draw_time']*1;
        $result[$k]['receive_time'] = $log['receive_time']*1;
        $result[$k]['status'] = $log['status']*1;

        if($type == 'my') {
            $result[$k]['name'] = $log['name'];
            $result[$k]['phone'] = $log['phone'];
            $result[$k]['address'] = $log['address'];
            $result[$k]['icon'] = str_replace('https://','http://',$item['icon']);
            $result[$k]['big_icon'] = str_replace('https://','http://',$item['big_icon']);
        }
    }

    if($type == 'all') {
        if ($this->_wheel['record1']) {
            array_unshift($result, $this->_format_record($this->_wheel['record1']));
        }

        if ($this->_wheel['record2']) {
            array_unshift($result, $this->_format_record($this->_wheel['record2']));
        }

        if(count($result)>10) {
            $result  = array_slice($result,0,10);
        }
    }

    return $result;
}

private function _format_record($record) {
    $arr = explode(',',$record);

    $data = array(
        'id'=>0,
        'role_id'=>0,
        'role_name'=>$arr[0],
        'item_id'=>0,
        'item_num'=>$arr[2],
        'item_type'=>0,
        'item_name'=>$arr[1],
        'draw_time'=>strtotime($arr[3]),
        'create_time'=>0,
        'status'=>1
    );

    return $data;
}

    private function _format_log($log)
    {
        $result = array();

        $result['id'] = $log['id']*1;
        $result['role_id'] = $log['role_id']*1;
        $result['role_name'] = $log['role_name'];
        $result['item_id'] = $log['item_id']*1;
        $result['item_num'] = $log['item_num']*1;

        $item = $this->activity_model->get_wheel_item_by_id($log['item_id']);
        $result['item_type'] = $item['type']*1;
        $result['item_name'] = $item['name'];

        $result['icon'] = str_replace('https://','http://',$item['icon']);
        $result['big_icon'] = str_replace('https://','http://',$item['big_icon']);

        $result['draw_time'] = $log['draw_time']*1;
        $result['receive_time'] = $log['receive_time']*1;
        $result['status'] = $log['status']*1;

        $result['name'] = $log['name'];
        $result['phone'] = $log['phone'];
        $result['address'] = $log['address'];

        return $result;
    }

    private function _format_prizes($prizes) {

        $result = array();

        foreach($prizes as $k=>$v) {
            $result[$k]['item_id'] = $v['item_id']*1;
            // 获取对应的商品
            $item = $this->activity_model->get_wheel_item_by_id($v['item_id']);
            $result[$k]['num'] = $v['num']*1;
            $result[$k]['name'] = $item['name'];
            $result[$k]['icon'] = str_replace('https://','http://',$item['icon']);
            $result[$k]['big_icon'] = str_replace('https://','http://',$item['big_icon']);

        }

        return $result;
    }

    private function _format_prize($index,$prize,$log_id) {
        $result = array();

        $item = $this->activity_model->get_wheel_item_by_id($prize['item_id']);

        $result['id'] = $log_id*1;
        $result['index'] = $index;
        $result['num'] =  $prize['num']*1;
        $result['name'] = $item['name'];
        $result['item_id'] = $item['id']*1;
        $result['item_type'] = $item['type']*1;
        $result['icon'] = str_replace('https://','http://',$item['icon']);
        $result['big_icon'] = str_replace('https://','http://',$item['big_icon']);

        return $result;
    }

    private function _send_redpack($open_id,$money)
    {
        $mp_config = $this->mp_model->get_mp_config($this->_channel['game_id']);
        WxPayConfig::setConfig($mp_config);
        $game_info = $this->mp_model->get_game_info($this->_channel['game_id']);

        $this->load->helper('string');
        $order_no = date("YmdHis") . random_string('nozero', 3);

        // 创建提现订单
        $data = array(
            'role_id' => $this->_role['UserID'],
            'role_name'  => $this->_role['NickName'],
            'order_no' => $order_no,
            'total_fee' => $money,
            'create_time' => time(),
            'game_id'=>$this->_channel['game_id']
        );

        $this->db->insert('mp_exchange_cash', $data);

        $id = $this->db->insert_id();

        if ($id){
            $input = new WxPaySendredpack();
            $input->SetMch_billno($order_no);//商户订单号
            $input->SetSend_name($game_info['game_name']);//商户名称
            $input->SetRe_openid($open_id);//用户openid
            $input->SetTotal_amount($money*100);//付款金额
            $input->SetTotal_num(1);//红包发放总人数
            $input->SetWishing($game_info['game_name']);//红包祝福语
            $input->SetAct_name($game_info['game_name']);//活动名称
            $input->SetRemark($game_info['game_name']);//备注信息
            if ($money >= 200){
                $input->SetScene_id('PRODUCT_3');//场景id
            }
            $order = WxPayApi::SendredpackOrder($input);
            
            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS'){
                $this->db->where('id',$id);
                $this->db->update('mp_exchange_cash',array('out_trade_no'=>$order['send_listid'],'status'=>1));

                return true;
            }else{
                $this->db->where('id',$id);
                $this->db->update('mp_exchange_cash',array('status'=>-1));

                log_message('error','发红包');
                log_message('error',var_export($order, TRUE));
                return false;
            }
        }else{
            return false;
        }
    }

    //每日结算 剩余抽奖次数
    private function _surplus_lottery_num()
    {
        //获取开始时间与结束时间相差天数
        $differ_day = intval(($this->_wheel['end_time']-$this->_wheel['begin_time'])/86400);

        $surplus_lottery_num = 0;//总剩余抽奖次数
        $lottery_num = 0;//每天抽奖次数

        $num['cost_num'] = 0;//消耗房卡数
        $num['surplus_lottery_num'] = 0;//总剩余抽奖次数

        //当天达到抽奖要求就加总抽奖次数未达标就清空第二天重新开始统计
        for ($i=$differ_day;$i>=0;$i--){
            $day = date('Y-m-d',$this->_wheel['end_time']-($i*86400));//日期由小到大

            $cost_num = $this->player_model->get_role_cost_count($this->_role['UserID'],$day);//用户消耗房卡数
            $lottery_record = $this->player_model->get_today_role_wheel_logs($this->_wheel['id'],$this->_role['UserID'],$this->_channel['game_id'],$day);//用户抽奖记录

            if (!empty($cost_num) || !empty($lottery_record)){//如果用户抽奖记录为空或者消耗房卡数为空就进入下次循环
                if ($cost_num >= $this->_wheel['condition_v']){//当天消耗房卡数达到消耗要求
                    if ($i == $differ_day){//如果是活动开始时间
                        $lottery_num = ($cost_num/$this->_wheel['condition_v'])-count($lottery_record);//当天剩余抽奖次数等于当天消耗房卡数除去消耗要求减去当天抽奖数
                    }else{//否则
                        if (!empty($cost_num)){//消耗房卡数不为空
                            $differ = ($cost_num/$this->_wheel['condition_v'])-count($lottery_record);//当天剩余抽奖次数等于当天消耗房卡数除去消耗要求减去当天抽奖数

                            if ($differ >= 0){//当天剩余抽奖次数大于零
                                $lottery_num = $differ;//当天剩余抽奖次数
                            }else if ((($cost_num/$this->_wheel['condition_v'])+$surplus_lottery_num)-count($lottery_record) >= 0)//如果当天消耗房卡数除去消耗要求加上总抽奖次数减去当天抽奖数大于零
                            {
                                $surplus_lottery_num = (($cost_num/$this->_wheel['condition_v'])+$surplus_lottery_num)-count($lottery_record);//总抽奖次数等于当天消耗房卡数除去消耗要求加上总抽奖次数减去当天抽奖数
                            }
                        }else{
                            $surplus_lottery_num = $surplus_lottery_num-count($lottery_record);//总抽奖次数等于总抽奖次数减去当天抽奖数
                        }
                    }
                }else if(!empty($lottery_record)){//抽奖数不为空
                    $surplus_lottery_num = $surplus_lottery_num-count($lottery_record);//总抽奖次数等于总抽奖次数减去当天抽奖数
                }
            }

            $surplus_lottery_num += empty($lottery_num)?0:intval($lottery_num);//总抽奖次数等于每天抽奖次数相加
            $lottery_num = 0;//清空当天抽奖次数
        }

        $num['cost_num'] = $this->player_model->get_role_cost_count($this->_role['UserID'],date('Y-m-d'));//获取当天的消耗房卡数
        $num['surplus_lottery_num'] = $surplus_lottery_num;//总剩余消耗房卡数
        return $num;//返回
    }
}
?>

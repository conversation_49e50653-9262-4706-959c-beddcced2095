-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 汇总
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbSummary = class('OrbSummary', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbSummary:ctor(panel)
    print('OrbSummary:ctor...')
    self.super.ctor(self, panel)
end


-------------------------------------------------------------------------------
-- 显示事件
-------------------------------------------------------------------------------
function OrbSummary:onShow()
    local amount = self:getAmount()
    local offset = self:getOffset()
    self.m_panel:child('label_amount'):setString(string.format('%.2f', amount))
    local label_amount = self.m_panel:child('label_offset')
    if offset > 0 then
        local str = LANG{'ORB_SUMMARY_OFFSET', amount = string.format('%.2f', offset)}
        label_amount:show():setString(str)
    else
        label_amount:hide()
    end

    self:startTimedown()
end


return OrbSummary
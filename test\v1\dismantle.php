<?php
/**
 * @author: <PERSON>.z
 * @email: <EMAIL>
 * @website: http://www.jason-z.com
 * @version: 1.0
 * @date: 2019/1/24
 */

require(APPPATH . '/libraries/REST_Controller.php');
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Config.php');
require_once(APPPATH . 'libraries/phpqrcode/phpqrcode.php');

class Dismantle extends REST_Controller
{

    /**
     * 角色信息
     */
    private $_role;

    /**
     * 游戏
     */
    private $_game;

    private $_dismantle;

    public function __construct()
    {
        parent::__construct();

        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('activity_model');

        $role_id = $_REQUEST["uid"];

        $this->player_model->set_database($this->_channel['game_id']);
        // 获取角色信息
        $role = $this->player_model->get_role_info($role_id);

        if(!$role){
            $this->response(array('code'=>1,'msg'=>'角色信息不存在'));
        }

        $this->_role = $role;

        $this->_game = $this->server_model->get_game_by_id($this->_channel['game_id']);

        if($this->_game['is_open_red_bag'] == 0) {
            $this->response(array('code'=>1,'msg'=>'未开启拆红包配置'));
        }

        // 获取当前用户提现15元以上的次数
        $result = $this->activity_model->get_dismantle_withdraw_count($this->_role['UserID'],$this->_game['game_id']);

        if(count($result)>3){
            $this->response(array('code'=>1,'msg'=>'你已经拆过3轮红包了'));
        }

        // 查询用户当前
        $dismantle = $this->activity_model->get_dismantle_by_uid($this->_role['UserID'],$this->_game['game_id']);

        if(!$dismantle) {
            // 创建
            $data = array(
                'role_id'=>$this->_role['UserID'],
                'game_id'=>$this->_game['game_id'],
                'create_time'=>time()
            );

            $id = $this->activity_model->insert_dismantle($data);

            $dismantle = $this->activity_model->get_dismantle_by_id($id);
        }

        $this->_dismantle = $dismantle;

    }

    public function index_post()
    {

        $data['id'] =  $this->_dismantle['id']*1;
        $data['role_id'] =  $this->_dismantle['role_id']*1;
        $data['game_id'] =  $this->_dismantle['game_id']*1;
        $data['balance'] =  $this->_dismantle['balance']*1;
        $data['time_left'] = $this->_dismantle['create_time']+86400*2-time();
        $data['status'] =  $this->_dismantle['status']*1;
        $data['one_status'] =  $this->_dismantle['one_status']*1;

        // 获取邀请记录
        $records =  $this->activity_model->get_dismantle_invite_roles($this->_dismantle['id']);
        foreach($records as $record) {
            if($record['role_id'] == 0) {
                // 根据UNIOND查询是否注册
                $role = $this->player_model->get_role_info_by_useruin($record['unionid']);

                if($role) {

                    $this->activity_model->update_dismantle_invite($record['id'],array('role_id'=>$role['UserID']));

                    // 发送拆红包奖励
                    $this->activity_model->update_dismantle($this->_dismantle['id'],array('balance'=>$this->_dismantle['balance']+0.3));
                    $data['balance'] += 0.3;

                    $param = array(
                        'game_id'=>$this->_game['game_id'],
                        'from_id'=>$role['UserID'],
                        'from_nickname'=>$role['NickName'],
                        'from_avatar'=>$role['HeadHttp'],
                        'to_id'=>$this->_role['UserID'],
                        'type'=>"reg",
                        'total_fee'=>0.3,
                        'create_time'=>time(),
                        'dismantle_id'=>$this->_dismantle['id']
                    );


                    // 更改邀请记录

                    $this->db->insert('dismantle_open',$param);

                    if($role['PlayTimeCount']>0) {
                        if($this->activity_model->update_dismantle_invite($record['id'],array('is_play'=>1))) {
                            // 发送拆红包奖励
                            $this->activity_model->update_dismantle($this->_dismantle['id'],array('balance'=>$this->_dismantle['balance']+0.3));

                            $data['balance'] += 0.3;

                            $param = array(
                                'game_id'=>$this->_game['game_id'],
                                'from_id'=>$role['UserID'],
                                'from_nickname'=>$role['NickName'],
                                'from_avatar'=>$role['HeadHttp'],
                                'to_id'=>$this->_role['UserID'],
                                'type'=>"play",
                                'total_fee'=>0.3,
                                'create_time'=>time(),
                                'dismantle_id'=>$this->_dismantle['id']
                            );

                            $this->db->insert('dismantle_open',$param);
                        }
                    }

                }
            } else {
                //查询是否玩过游戏
                if($record['is_play'] == 0) {
                    $role = $this->player_model->get_role_info($record['role_id']);
                    if($role['PlayTimeCount']>0) {
                        if($this->activity_model->update_dismantle_invite($record['id'],array('is_play'=>1))) {
                            // 发送拆红包奖励
                            $this->activity_model->update_dismantle($this->_dismantle['id'],array('balance'=>$this->_dismantle['balance']+0.3));
                            $data['balance'] += 0.3;

                            $param = array(
                                'game_id'=>$this->_game['game_id'],
                                'from_id'=>$role['UserID'],
                                'from_nickname'=>$role['NickName'],
                                'from_avatar'=>$role['HeadHttp'],
                                'to_id'=>$this->_role['UserID'],
                                'type'=>"play",
                                'total_fee'=>0.3,
                                'create_time'=>time(),
                                'dismantle_id'=>$this->_dismantle['id']
                            );

                            $this->db->insert('dismantle_open',$param);

                        }
                    }
                }

            }
        }

        if($this->_dismantle['one_status'] == 0) {
            // 判断是否有人玩过游戏
            $logs = $this->activity_model->get_dismantle_invite_play_count($this->_dismantle['id']);

            if(count($logs)>1) {
                // 更改1元状态
                $this->activity_model->update_dismantle($this->_dismantle['id'],array('one_status'=>1));
                $data['one_status'] = 1;
            }

        }

        // 获取拆记录
        $open_logs = $this->activity_model->get_dismantle_open_logs($this->_dismantle['id']);

        $new_open_logs = array();
        foreach ($open_logs as $k=>$log) {
            $new_open_logs[$k]['id'] = $log['id']*1;
            $new_open_logs[$k]['from_id'] = $log['from_id']*1;
            // 获取来源用户
            $from_role = $this->player_model->get_role_info($log['from_id']);
            $new_open_logs[$k]['from_avatar'] = $from_role['HeadHttp'];
            $new_open_logs[$k]['from_nickname'] = $from_role['NickName'];
            $new_open_logs[$k]['to_id'] = $log['to_id']*1;
            $new_open_logs[$k]['type'] = $log['type'];
            $new_open_logs[$k]['total_fee'] = $log['total_fee'];
            $new_open_logs[$k]['create_time'] = date('Y-m-d H:i:s',$log['create_time']);
        }

        $data['open_logs'] = $new_open_logs;

        // 获取提现记录
        $withdraw_logs = $this->activity_model->get_dismantle_withdraw_logs($this->_role['UserID'],$this->_game['game_id']);

        $new_withdraw_logs = array();

        foreach ($withdraw_logs as $k=>$log) {
            $new_withdraw_logs[$k]['id'] = $log['id']*1;
            $new_withdraw_logs[$k]['total_fee'] = $log['total_fee'];
            $new_withdraw_logs[$k]['status'] = $log['status']*1;
            $new_withdraw_logs[$k]['create_time'] = date('Y-m-d H:i:s',$log['create_time']);
        }

        $data['withdraw_logs'] = $new_withdraw_logs;

        $notice_logs = array();

        $sql =  "select nick_name from tuo3_role_demo order by rand() LIMIT 10";
        $query = $this->db->query($sql);
        $names = $query->result_array();

       foreach ($names as $k=>$v){
           $notice_logs[$k]['nickname'] = $v['nick_name'];
           $notice_logs[$k]['money'] = mt_rand(150,170)/10;
       }

        $data['notices'] = $notice_logs;


        $this->response(array('code'=>0,'msg'=>'','data'=>$data));

    }

    // 拆的动作
    public function open_post()
    {
        // 步骤
        $is_share = $this->input->post('is_share');

        if($this->_dismantle['status'] > 2) {
            $this->response(array('code'=>1,'msg'=>'请勿重复操作'));
        }

        if($this->_dismantle['status'] == 10) {
            $this->response(array('code'=>1,'msg'=>'操作中，请稍候'));
        }

        $this->_dismantle['status'] = 10;

        // 更改红包的金额步骤
        if($is_share == 1) {
            $data['status'] = 2;
            $data['balance'] = $this->_dismantle['balance']+3.33;
        } else {
            $data['status'] = 1;
            $data['balance'] = $this->_dismantle['balance']+9.99;
        }

        $this->activity_model->update_dismantle($this->_dismantle['id'],$data);

        $param = array(
            'game_id'=>$this->_game['game_id'],
            'from_id'=>$this->_role['UserID'],
            'from_nickname'=>$this->_role['NickName'],
            'from_avatar'=>$this->_role['HeadHttp'],
            'to_id'=>$this->_role['UserID'],
            'total_fee'=>  $is_share == 1?3.33:9.99,
            'type'=> $is_share == 1?'share':'own',
            'create_time'=>time(),
            'dismantle_id'=>$this->_dismantle['id']
        );

        $this->db->insert('dismantle_open',$param);

        $param['id'] = $this->db->insert_id();
        $param['create_time'] =  date('Y-m-d H:i:s',$param['create_time']);

//        $open_logs = $this->activity_model->get_dismantle_open_logs($this->_dismantle['id']);
//
//        $new_open_logs = array();
//        foreach ($open_logs as $k=>$log) {
//            $new_open_logs[$k]['id'] = $log['id']*1;
//            $new_open_logs[$k]['from_id'] = $log['from_id']*1;
//            // 获取来源用户
//            $from_role = $this->server_model->get_role_info($log['from_id']);
//            $new_open_logs[$k]['from_avatar'] = $from_role['HeadHttp'];
//            $new_open_logs[$k]['from_nickname'] = $from_role['Nickname'];
//            $new_open_logs[$k]['to_id'] = $log['to_id']*1;
//            $new_open_logs[$k]['type'] = $log['type'];
//            $new_open_logs[$k]['total_fee'] = $log['total_fee'];
//            $new_open_logs[$k]['create_time'] = date('Y-m-d H:i:s',$log['create_time']);
//        }

        $data['log'] = $param;

        $this->_dismantle['status'] = $data['status'];


        $this->response(array('code'=>0,'msg'=>'','data'=>$data));

    }

    public function withdraw_post()
    {
        // 类型
        $type = $this->input->post('type');

        $this->load->model('mp_model');

        $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'],$this->_channel['game_id']);

        if(!$mp_user) {
            $this->response(array('code'=>1,'msg'=>'未关注公众号'));
        }

        $withdraw_log = array();

        if($type == 'one') {
            // 检查一元是否提现过
            if($this->_dismantle['one_status'] == 2) {
                $this->response(array('code'=>1,'msg'=>'已提现过，请勿重复提现'));
            }

            if($this->_dismantle['one_status'] == 10) {
                $this->response(array('code'=>1,'msg'=>'操作中,请稍候'));
            }

            $this->activity_model->update_dismantle($this->_dismantle['id'],array('one_status'=>10));

            if(!$this->_send_redpack($mp_user['openid'],1)) {
                $this->activity_model->update_dismantle($this->_dismantle['id'],array('one_status'=>1));
                $this->response(array('code'=>1,'msg'=>'领取失败，请重试'));
            }

            $data = array(
                'one_status'=>2,
                'balance'=>$this->_dismantle['balance']-1
            );

            $this->activity_model->update_dismantle($this->_dismantle['id'],$data);

            $this->db->where('dismantle_id',$this->_dismantle['id']);
            $this->db->where('total_fee',1.0);
            $this->db->order_by('id','desc');
            $this->db->limit(1);
            $query = $this->db->get('dismantle_withdraw');
            $withdraw_log = $query->row_array();

        } else {
            // 检查余额
            if($this->_dismantle['balance']<15.00) {
                $this->response(array('code'=>1,'msg'=>'余额不足15元'));
            }

            $this->activity_model->update_dismantle($this->_dismantle['id'],array('balance'=>0));

            $this->load->helper('string');
            $order_no = date("YmdHis") . random_string('nozero', 3);
            // 创建一笔提现申请
            $param = array(
                'dismantle_id'=> $this->_dismantle['id'],
                'role_id' => $this->_role['UserID'],
                'nickname'=> $this->_role['NickName'],
                'open_time' => $this->_dismantle['create_time'],
                'order_no' => $order_no,
                'total_fee' => $this->_dismantle['balance'],
                'create_time' => time(),
                'game_id'=>$this->_channel['game_id']
            );

            $this->db->insert('dismantle_withdraw', $param);

            $withdraw_id = $this->db->insert_id();

            $withdraw_log = $this->activity_model->get_dismantle_withdraw_by_id($withdraw_id);

        }

        $new_withdraw_log = array();

        if($withdraw_log) {
            $new_withdraw_log['id'] = $withdraw_log['id']*1;
            $new_withdraw_log['total_fee'] = $withdraw_log['total_fee']*1;
            $new_withdraw_log['status'] = $withdraw_log['status']*1;
            $new_withdraw_log['create_time'] = date('Y-m-d H:i:s',$withdraw_log['create_time']);
        }


        $data['log'] = $new_withdraw_log;

        $this->response(array('code'=>0,'msg'=>'','data'=>$data));

    }

    public function reward_post() {
        $id = $this->input->post('id');

        $withdraw = $this->activity_model->get_dismantle_withdraw_by_id($id);

        if(!$withdraw) {
            $this->response(array('code'=>1,'msg'=>'记录不存在'));
        }

        if($withdraw['status'] == 0) {
            $this->response(array('code'=>1,'msg'=>'审核中'));
        }

        if($withdraw['status'] == -1) {
            $this->response(array('code'=>1,'msg'=>'审核未通过'));
        }

        if($withdraw['status'] == 2) {
            $this->response(array('code'=>1,'msg'=>'奖励已发放'));
        }

        if($withdraw['status'] == 10) {
            $this->response(array('code'=>1,'msg'=>'操作中，请稍候'));
        }


        $this->load->model('mp_model');

        $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'],$this->_channel['game_id']);

        if(!$mp_user) {
            $this->response(array('code'=>1,'msg'=>'未关注公众号'));
        }

        $this->db->where('id',$id);
        $this->db->update('dismantle_withdraw',array('status'=>10));

        if(!$this->_send_redpack($mp_user['openid'],$withdraw['total_fee'],$withdraw['id'])) {
            $this->db->where('id',$id);
            $this->db->update('dismantle_withdraw',array('status'=>1));

            $this->response(array('code'=>1,'msg'=>'领取失败，请重试'));
        }

        $this->response(array('code'=>0,'msg'=>''));


    }

    private function _send_redpack($open_id,$money,$id='')
    {
        $mp_config = $this->mp_model->get_mp_config($this->_channel['game_id']);
        WxPayConfig::setConfig($mp_config);
        $game_info = $this->mp_model->get_game_info($this->_channel['game_id']);

        $this->load->helper('string');
        $order_no = date("YmdHis") . random_string('nozero', 3);


        if($money == 1) {
            // 创建提现订单
            $data = array(
                'dismantle_id'=> $this->_dismantle['id'],
                'role_id' => $this->_role['UserID'],
                'nickname'=> $this->_role['NickName'],
                'open_time' => $this->_dismantle['create_time'],
                'order_no' => $order_no,
                'total_fee' => $money,
                'create_time' => time(),
                'game_id'=>$this->_channel['game_id']
            );

            $this->db->insert('dismantle_withdraw', $data);

            $id = $this->db->insert_id();
        }

        if ($id){
            $input = new WxPaySendredpack();
            $input->SetMch_billno($order_no);//商户订单号
            $input->SetSend_name($game_info['game_name']);//商户名称
            $input->SetRe_openid($open_id);//用户openid
            $input->SetTotal_amount($money*100);//付款金额
            $input->SetTotal_num(1);//红包发放总人数
            $input->SetWishing($game_info['game_name']);//红包祝福语
            $input->SetAct_name($game_info['game_name']);//活动名称
            $input->SetRemark($game_info['game_name']);//备注信息
            if ($money >= 200){
                $input->SetScene_id('PRODUCT_3');//场景id
            }
            $order = WxPayApi::SendredpackOrder($input);

            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS'){
                $this->db->where('id',$id);
                $this->db->update('dismantle_withdraw',array('out_trade_no'=>$order['send_listid'],'status'=>2));

                return true;
            }else{
                $this->db->where('id',$id);
                $this->db->update('dismantle_withdraw',array('status'=>4));

                log_message('error','发红包');
                log_message('error',var_export($order, TRUE));
                return false;
            }
        }else{
            return false;
        }
    }




}
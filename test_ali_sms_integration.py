#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云短信接口集成测试脚本
测试替换后的短信验证码接口是否正常工作
"""

import hashlib
import time
import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class AliSmsIntegrationTester:
    def __init__(self, base_url='https://lhmj.tuo3.com.cn'):
        self.base_url = base_url
        self.channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
        self.session = requests.Session()
        
    def generate_sign(self, params, channel_key):
        """生成签名"""
        # 按键名排序
        sorted_keys = sorted(params.keys())
        
        # 拼接所有值
        param_str = ''.join(str(params[key]) for key in sorted_keys)
        
        # 加上渠道密钥
        sign_str = param_str + channel_key
        
        # MD5签名
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    
    def test_ali_sms_verify_code(self, phone='13067894828'):
        """测试阿里云短信验证码接口"""
        print(f"\n=== 测试阿里云短信验证码接口 ===")
        print(f"手机号: {phone}")
        
        # 基础参数
        params = {
            'uid': '123456',  # 测试用户ID
            'phone': phone,   # 手机号
            'type': 'login',  # 类型: login/bind
            'uuid': 'TEST_DEVICE_ID',  # 设备ID
            'timestamp': str(int(time.time() * 1000)),  # 时间戳
            'channel': '50010001',  # 渠道号
            'c_version': '10',  # 客户端版本
            'res_version': '1',  # 资源版本
        }
        
        # 生成签名
        sign = self.generate_sign(params, self.channel_key)
        params['sign'] = sign
        
        # 请求URL
        url = f'{self.base_url}/admin/api/v1/user/get_verify_code'
        
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        
        try:
            # 发送POST请求
            response = self.session.post(url, data=params, timeout=30, verify=False)
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"JSON数据: {json_data}")
                    
                    if json_data.get('code') == 0:
                        print("✅ 阿里云短信验证码发送成功！")
                        return True, json_data
                    else:
                        print(f"❌ 阿里云短信验证码发送失败: {json_data.get('msg', '未知错误')}")
                        return False, json_data
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式")
                    return False, {'error': 'Invalid JSON response'}
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False, {'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_phone_login_with_ali_sms(self, phone='13067894828', code='1234'):
        """测试使用阿里云短信的手机登录"""
        print(f"\n=== 测试阿里云短信手机登录 ===")
        print(f"手机号: {phone}")
        print(f"验证码: {code}")
        
        # 基础参数
        params = {
            'phone': phone,   # 手机号
            'code': code,     # 验证码
            'uuid': 'TEST_DEVICE_ID',  # 设备ID
            'timestamp': str(int(time.time() * 1000)),  # 时间戳
            'channel': '50010001',  # 渠道号
            'c_version': '10',  # 客户端版本
            'res_version': '1',  # 资源版本
        }
        
        # 生成签名
        sign = self.generate_sign(params, self.channel_key)
        params['sign'] = sign
        
        # 请求URL
        url = f'{self.base_url}/admin/api/v1/user/phone_login'
        
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        
        try:
            # 发送POST请求
            response = self.session.post(url, data=params, timeout=30, verify=False)
            
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"JSON数据: {json_data}")
                    
                    if json_data.get('code') == 0:
                        print("✅ 阿里云短信手机登录成功！")
                        return True, json_data
                    else:
                        print(f"❌ 阿里云短信手机登录失败: {json_data.get('msg', '未知错误')}")
                        return False, json_data
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式")
                    return False, {'error': 'Invalid JSON response'}
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False, {'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_direct_ali_sms_api(self):
        """直接测试阿里云短信API"""
        print(f"\n=== 直接测试阿里云短信API ===")
        
        # 这里可以直接调用阿里云短信API进行测试
        # 模拟阿里云短信API调用
        print("模拟阿里云短信API调用...")
        
        # 构建阿里云短信请求参数
        ali_params = {
            'PhoneNumbers': '13067894828',
            'SignName': '快马互娱',
            'TemplateCode': 'SMS_229700066',
            'TemplateParam': json.dumps({'code': '1234'}),
            'Action': 'SendSms',
            'Version': '2017-05-25',
            'RegionId': 'cn-hangzhou',
            'Format': 'JSON',
            'Timestamp': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime()),
            'SignatureMethod': 'HMAC-SHA1',
            'SignatureVersion': '1.0',
            'SignatureNonce': str(int(time.time() * 1000)),
            'AccessKeyId': 'LTAI5tBvKJhKJhKJhKJhKJhK'  # 示例AccessKey
        }
        
        print(f"阿里云短信参数: {ali_params}")
        print("注意: 需要配置正确的AccessKey ID和Secret才能实际发送")
        
        return True, {'message': '阿里云短信API参数构建成功'}
    
    def compare_sms_providers(self):
        """比较不同短信服务商"""
        print(f"\n=== 短信服务商对比 ===")
        
        providers = {
            '云之讯': {
                '优点': ['接入简单', '价格便宜', '支持模板短信'],
                '缺点': ['SSL连接问题', '服务稳定性一般', '技术支持有限'],
                '状态': '已替换'
            },
            '阿里云': {
                '优点': ['服务稳定', '技术支持好', '功能丰富', '安全性高'],
                '缺点': ['价格稍高', '配置复杂'],
                '状态': '当前使用'
            },
            '腾讯云': {
                '优点': ['服务稳定', '价格合理', '接入简单'],
                '缺点': ['功能相对简单'],
                '状态': '备选方案'
            }
        }
        
        for provider, info in providers.items():
            print(f"\n{provider}:")
            print(f"  状态: {info['状态']}")
            print(f"  优点: {', '.join(info['优点'])}")
            print(f"  缺点: {', '.join(info['缺点'])}")

def run_ali_sms_integration_test():
    """运行阿里云短信集成测试"""
    print("开始阿里云短信接口集成测试...")
    print("="*60)
    
    # 创建测试器
    tester = AliSmsIntegrationTester()
    
    # 测试结果统计
    results = {}
    
    # 1. 直接测试阿里云短信API
    print("1. 直接测试阿里云短信API")
    success, result = tester.test_direct_ali_sms_api()
    results['direct_api'] = success
    
    # 2. 测试集成后的验证码接口
    print("\n2. 测试集成后的验证码接口")
    phone = '13067894828'  # 使用用户更新的手机号
    success, result = tester.test_ali_sms_verify_code(phone)
    results['verify_code'] = success
    
    if success:
        print(f"\n🎉 阿里云短信验证码发送成功！")
        
        # 3. 提示用户输入验证码进行登录测试
        print(f"\n请查看手机 {phone} 收到的验证码")
        user_code = input("请输入收到的验证码（或按回车跳过登录测试）: ").strip()
        
        if user_code:
            # 测试手机登录
            login_success, login_result = tester.test_phone_login_with_ali_sms(phone, user_code)
            results['phone_login'] = login_success
            
            if login_success:
                print(f"\n🎉 阿里云短信手机登录测试成功！")
            else:
                print(f"\n❌ 阿里云短信手机登录测试失败")
        else:
            print(f"\n跳过手机登录测试")
            results['phone_login'] = None
    else:
        print(f"\n❌ 阿里云短信验证码发送失败")
        results['phone_login'] = False
    
    # 4. 短信服务商对比
    tester.compare_sms_providers()
    
    # 生成测试报告
    print(f"\n" + "="*60)
    print(f"阿里云短信集成测试报告:")
    print("="*60)
    
    for test_name, success in results.items():
        if success is None:
            status = "⏭️ 跳过"
        elif success:
            status = "✅ 成功"
        else:
            status = "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for v in results.values() if v is True)
    total_count = sum(1 for v in results.values() if v is not None)
    
    print(f"\n总体结果: {success_count}/{total_count} 个测试成功")
    
    print(f"\n💡 下一步建议:")
    if success_count == total_count:
        print("1. ✅ 阿里云短信接口集成成功")
        print("2. ✅ 可以正式启用阿里云短信服务")
        print("3. 🔧 配置正确的AccessKey ID和Secret")
        print("4. 📊 监控短信发送成功率和成本")
    else:
        print("1. 🔧 检查阿里云短信配置")
        print("2. 🔑 确认AccessKey ID和Secret正确")
        print("3. 📋 验证短信签名和模板")
        print("4. 🌐 检查网络连接和防火墙设置")
        print("5. 📞 联系阿里云技术支持")

if __name__ == '__main__':
    run_ali_sms_integration_test()

-------------------------------------------------------------------------------
--  创世版1.0
--  所有游戏战绩详情基类
--  @date 2017-06-08
--  @auth woodoo
-------------------------------------------------------------------------------
local ScoreDetailBase = class("ScoreDetailBase", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ScoreDetailBase:ctor(record, close_callback)
    print('ScoreDetailBase:ctor...')
    self:enableNodeEvents()
    self.m_record = record
    self.m_close_callback = close_callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('ScoreDetailLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('item_template'):hide()
    main_node:child('item_total'):hide()

    main_node:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self, self.onClose)

    self:showBase()
end


-------------------------------------------------------------------------------
-- onEnterTransitionFinish
-------------------------------------------------------------------------------
function ScoreDetailBase:onEnterTransitionFinish()
    print('ScoreDetailBase:onEnterTransitionFinish...')
    helper.pop.waiting()
    PassRoom:getInstance():getNetFrame():onQueryScoreDetail(self.m_record.dwRecordID)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ScoreDetailBase:onExit()
    print('ScoreDetailBase:onExit...')
end


-------------------------------------------------------------------------------
-- 关闭
-------------------------------------------------------------------------------
function ScoreDetailBase:onClose()
    if self.m_close_callback then
        self.m_close_callback()
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function ScoreDetailBase:onBtnShare(sender)
    helper.pop.shareImage()
end


-------------------------------------------------------------------------------
-- 显示基本信息
-------------------------------------------------------------------------------
function ScoreDetailBase:showBase()
    local record = self.m_record
    local main_node = self.main_node
	local num = math.min(cs.app.MAX_PLAYER, #record.szNickName)
	for i = 1, cs.app.MAX_PLAYER do
		main_node:child('label_name' .. i):setString( i <= num and record.szNickName[i] or '' )
	end
end


-------------------------------------------------------------------------------
-- 显示列表，父界面调用
-------------------------------------------------------------------------------
function ScoreDetailBase:showScores(scores)
    --dump(scores,  "scores...........", 6)
    local record = self.m_record
    local main_node = self.main_node
    local listview = main_node:child('listview')
    local template = main_node:child('item_template')
	local num = record.nPlayerCount

    for i, ju in ipairs( scores ) do
        print('ScoreDetailBase show scores for is ', i, dump(ju))
        local item = template:clone():show()
        item.index = i
        item:child('label_ju'):setString( LANG{'GAME_NO', index=i} )

        for j = 1, cs.app.MAX_PLAYER do
            item:child('label_score' .. j):setString( j <= num and ju.nScore[j] or '' )
        end

        local btn_replay = item:child('btn_replay')
        if ju.bHasReplay then
            btn_replay.index = i
            btn_replay.drawID = ju.nDrawID
            btn_replay:addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnReplay) )
        else
            btn_replay:hide()
        end

        listview:pushBackCustomItem(item)
    end
    template:removeFromParent()

    local item_total = main_node:child('item_total'):show()
    item_total:retain()
	for i = 1, cs.app.MAX_PLAYER do
        item_total:child('label_score' .. i):setString( i <= num and record.nScore[i] or '' )
	end
    item_total:removeFromParent(false)
    listview:pushBackCustomItem(item_total)
    item_total:release()

    main_node:child('label_note'):setString( LANG{'SCORE_NOTE', base_score=record.wCellScore, bill_no=record.lBillID} )
end


-------------------------------------------------------------------------------
-- 回放按钮点击
-------------------------------------------------------------------------------
function ScoreDetailBase:onBtnReplay(sender)
    local drawID = sender.drawID
    --dump(record,  "record...........", 6)
    PassRoom:getInstance():getNetFrame():onSendQueryReplay(drawID)
end


return ScoreDetailBase
--
-- Author: zhong
-- Date: 2016-12-28 16:16:34
--
-- 私人房网络处理
local BaseFrame = appdf.req(appdf.CLIENT_SRC.."frame.BaseFrame")
local PassFrame = class("PassFrame",BaseFrame)
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")

local game_cmd = cs.app.client("header.CMD_GameServer")
local cmd_private = cs.app.client('header.CMD_Private')
local define_private = cs.app.client('header.Define_Private')
local struct_private = cs.app.client('header.Struct_Private')

local logincmd = appdf.req(appdf.HEADER_SRC .. "CMD_LogonServer")

-- 登陆服务器CMD
local cmd_pri_login = cmd_private.login
-- 游戏服务器CMD
local cmd_pri_game = cmd_private.game

PassFrame.OP_CREATEROOM = cmd_pri_login.SUB_MB_QUERY_GAME_SERVER                 -- 创建房间
PassFrame.OP_SEARCHROOM = cmd_pri_login.SUB_MB_SEARCH_SERVER_TABLE               -- 查询房间
PassFrame.OP_ROOMPARAM = cmd_pri_login.SUB_MB_GET_PERSONAL_PARAMETER             -- 私人房配置
PassFrame.OP_QUERYLIST = cmd_pri_login.SUB_MB_QUERY_PERSONAL_ROOM_LIST           -- 私人房列表
PassFrame.OP_DISSUMEROOM = cmd_pri_login.SUB_MB_DISSUME_SEARCH_SERVER_TABLE      -- 解散桌子
PassFrame.OP_EXCHANGEROOMCARD = cmd_pri_login.SUB_MB_ROOM_CARD_EXCHANGE_TO_SCORE -- 房卡兑换游戏币
PassFrame.OP_QUERYSCORELIST = cmd_pri_login.SUB_GR_QUERY_RECORD_INFO             -- 查询战绩列表
PassFrame.OP_QUERYSCOREDETAIL = cmd_pri_login.SUB_GR_QUERY_RECORD_SCORE          -- 查询战绩详情
PassFrame.OP_QUERYREPLAY = cmd_pri_login.SUB_GR_QUERY_REPLAY                     -- 查询回放
PassFrame.OP_QUERYSERVER = cmd_pri_login.SUB_GR_QUERY_SERVER_ID                  -- 查询连接的房间
PassFrame.OP_QUERYEVENTLIST = cmd_pri_login.SUB_GR_EVENT_LIST                    -- 查询活动列表
PassFrame.OP_QUERYGETREWARD = cmd_pri_login.SUB_GR_EVENT_GET_REWARD              -- 获取奖励
PassFrame.OP_ACCEPTMISSION = cmd_pri_login.SUB_GR_EVENT_ACCEPT                  -- 领任务


PassFrame.OP_LOCATION = logincmd.SUB_GR_SELF_LOCATION                             -- 发送定位信息

function LOGINSERVER(code)
    return { m = cmd_pri_login.MDM_MB_PERSONAL_SERVICE, s = code }
end
function GAMESERVER(code)
    return { m = cmd_pri_game.MDM_GR_PERSONAL_TABLE, s = code }
end

function PassFrame:ctor(view,callbcak)
    PassFrame.super.ctor(self,view,callbcak)
end

-- 创建房间
function PassFrame:onCreateRoom()
    --操作记录
    self._oprateCode = PassFrame.OP_CREATEROOM
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
    -- 动作定义
    PassRoom:getInstance().m_nLoginAction = PassRoom.L_ACTION.ACT_CREATEROOM
end

-- 查询房间
function PassFrame:onSearchRoom( roomId, enterType )
    --操作记录
    GlobalUserItem.szCurRoomID = roomId
    self._oprateCode = PassFrame.OP_SEARCHROOM
    self._roomId = roomId or ""
    self._roomEnterType = enterType or yl.SEARCH_ROOM_TYPE_JOIN
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 私人房间配置
function PassFrame:onGetRoomParameter()
    --操作记录
    self._oprateCode = PassFrame.OP_ROOMPARAM
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 查询私人房列表
function PassFrame:onQueryRoomList()
    --操作记录
    self._oprateCode = PassFrame.OP_QUERYLIST
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 解散房间
function PassFrame:onDissumeRoom( roomId )
    --操作记录
    self._oprateCode = PassFrame.OP_DISSUMEROOM
    self._roomId = roomId or ""
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 查询战绩列表
function PassFrame:onQueryScoreList(dwRecordID)
    --操作记录
    self._oprateCode = PassFrame.OP_QUERYSCORELIST
    self._oprateData = dwRecordID or 0
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 查询战绩详情
function PassFrame:onQueryScoreDetail(record_id)
    --操作记录
    self._oprateCode = PassFrame.OP_QUERYSCOREDETAIL
    self.m_score_record_id = record_id
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 房卡兑换游戏币
function PassFrame:onExchangeScore( lCount )
    --操作记录
    self._oprateCode = PassFrame.OP_EXCHANGEROOMCARD
    self._lExchangeRoomCard = lCount
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

--连接结果
function PassFrame:onConnectCompeleted()
    print("================== PassFrame:onConnectCompeleted ================== ==> " .. self._oprateCode)
    if self._oprateCode == PassFrame.OP_CREATEROOM then              -- 创建房间
        self:sendCreateRoom()
    elseif self._oprateCode == PassFrame.OP_SEARCHROOM then          -- 查询房间
        self:sendSearchRoom()
    elseif self._oprateCode == PassFrame.OP_ROOMPARAM then           -- 私人房配置
        self:sendQueryRoomParam()
    elseif self._oprateCode == PassFrame.OP_QUERYLIST then           -- 请求私人房列表
        self:sendQueryRoomList()
    elseif self._oprateCode == PassFrame.OP_DISSUMEROOM then         -- 解散桌子
        self:sendDissumeRoom()
    elseif self._oprateCode == PassFrame.OP_QUERYSCORELIST then      -- 战绩列表
        self:sendQueryScoreList( self._oprateData )
    elseif self._oprateCode == PassFrame.OP_QUERYSCOREDETAIL then    -- 战绩详情
        self:sendQueryScoreDetail()
    elseif self._oprateCode == PassFrame.OP_EXCHANGEROOMCARD then    -- 房卡兑换游戏币
        self:sendExchangeScore()
    elseif self._oprateCode == PassFrame.OP_QUERYREPLAY then         -- 回放查询
        self:sendQueryReplay( self._oprateData )
    elseif self._oprateCode == PassFrame.OP_QUERYSERVER then         -- 房间查询
        self:sendQueryServerID( self._oprateData )
    elseif self._oprateCode == PassFrame.OP_LOCATION then
        self:sendLocation( self._oprateData )
        self:onCloseSocket()
    elseif self._oprateCode == PassFrame.OP_QUERYEVENTLIST then      -- 查询活动列表
        self:sendQueryEventList( self._oprateData )
    elseif self._oprateCode == PassFrame.OP_QUERYGETREWARD then      -- 领奖
        self:sendGetReward( self._oprateData )
    elseif self._oprateCode == PassFrame.OP_ACCEPTMISSION then       -- 领任务
        self:sendGetMission( self._oprateData )
    else
        self:onCloseSocket()
        if nil ~= self._callBack then
            self._callBack(LOGINSERVER(-1),"未知操作模式！")
        end 
        PassRoom:getInstance():dismissPopWait()
    end
end

-- 建立 socket连接
function PassFrame:onSendQueryReplay( id )
    --操作记录
    self._oprateData = id;
    self._oprateCode = PassFrame.OP_QUERYREPLAY
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

function PassFrame:onSendLocation( locationData )
    --操作记录
    self._oprateData = locationData;
    self._oprateCode = PassFrame.OP_LOCATION
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 发送定位
function PassFrame:sendLocation( locationData )
    local buffer = ExternalFun.create_netdata(logincmd.CMD_GR_SelfLocation)
	buffer:setcmdinfo(logincmd.MDM_GP_USER_SERVICE, logincmd.SUB_GR_SELF_LOCATION)
    local status = 1
    if locationData.berror then
        status = -1
    end
    buffer:pushdword(GlobalUserItem.dwUserID)
    buffer:pushdouble(locationData.longitude)
    buffer:pushdouble(locationData.latitude)
    buffer:pushint(status)
    buffer:pushint(locationData.accuracy)
    --dump(buffer, '发送定位', 9)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送定位失败！")
    end
end

-- mike20170719
-- 发送查询回放
function PassFrame:sendQueryReplay( id )
    local buffer = ExternalFun.create_netdata( cmd_pri_login.CMD_GP_QueryReplay )
    buffer:setcmdinfo( cmd_pri_login.MDM_MB_PERSONAL_SERVICE , cmd_pri_login.SUB_GR_QUERY_REPLAY )
    buffer:pushdword( id )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1), "查询回放失败！")
    end
end

-- mike20180116
-- 建立 socket连接
function PassFrame:onSendGetReward( id )
    --操作记录
    self._oprateData = id
    self._oprateCode = PassFrame.OP_QUERYGETREWARD
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 发送获取奖励
function PassFrame:sendGetReward( data )
    local buffer = ExternalFun.create_netdata( cmd_pri_login.CMD_GR_UserIDValue )
    buffer:setcmdinfo( cmd_pri_login.MDM_MB_PERSONAL_SERVICE, cmd_pri_login.SUB_GR_EVENT_GET_REWARD )
    buffer:pushdword( data.userID )
    buffer:pushint( data.paramID )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1), "获取奖励失败！")
    end
end

--获取奖励的消息返回
function PassFrame:onSubGetRewardResult( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_login.IDValueMsg, pData)
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER( cmd_pri_login.SUB_GR_EVENT_GET_REWARD_RESULT ), nil, cmd_table)
    end
end

function PassFrame:onSendGetMission( data )
    --操作记录
    self._oprateData = data
    self._oprateCode = PassFrame.OP_ACCEPTMISSION
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 发送获取奖励
function PassFrame:sendGetMission( data )
    local buffer = ExternalFun.create_netdata( cmd_pri_login.CMD_GR_UserIDValue )
    buffer:setcmdinfo( cmd_pri_login.MDM_MB_PERSONAL_SERVICE, cmd_pri_login.SUB_GR_EVENT_ACCEPT )
    --print('发送获取奖励', data.userID, data.eventID)
    buffer:pushdword( data.userID )
    buffer:pushint( data.eventID )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1), "获取奖励失败！")
    end
end

--获取奖励的消息返回
function PassFrame:onSubGetMissionResult( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_login.IDValueMsg, pData)
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER( cmd_pri_login.SUB_GR_EVENT_ACCEPT_RESULT ), nil, cmd_table)
    end
end

-- mike20180116
-- 建立 socket连接
function PassFrame:onSendQueryEventList( id )
    --操作记录
    self._oprateData = id;
    self._oprateCode = PassFrame.OP_QUERYEVENTLIST
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- 发送查询活动列表
function PassFrame:sendQueryEventList( userID )
    local buffer = ExternalFun.create_netdata( cmd_pri_login.CMD_GR_UserIDValue )
    buffer:setcmdinfo( cmd_pri_login.MDM_MB_PERSONAL_SERVICE , cmd_pri_login.SUB_GR_EVENT_LIST )
    buffer:pushdword( userID )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1), "查询活动列表失败！")
    end
end

--
function PassFrame:onSubQueryEventListResult( pData )
    local ret = {}
    -- 计算数目
    local len = pData:getlen()
    local itemcount = math.floor(len/define_private.LEN_EVENT_LIST_DETAIL)
    for i = 1, itemcount do
        local cmd_table = ExternalFun.read_netdata(cmd_pri_login.tagEvent, pData)
        table.insert(ret, cmd_table)  
    end
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER(cmd_pri_login.SUB_GR_EVENT_LIST_RESULT), nil, ret)
    end
end

-- mike20170801
-- 发送查询房间
function PassFrame:onSendQueryServerID( id )
    --操作记录
    self._oprateData = id;
    self._oprateCode = PassFrame.OP_QUERYSERVER
    if not self:onCreateSocket(yl.LOGONSERVER,yl.LOGONPORT) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"建立连接失败！")
    end
end

-- mike20170801
-- 发送查询房间
function PassFrame:sendQueryServerID( id )
    local buffer = ExternalFun.create_netdata( cmd_pri_login.CMD_GR_QueryServerID )
    buffer:setcmdinfo( cmd_pri_login.MDM_MB_PERSONAL_SERVICE , cmd_pri_login.SUB_GR_QUERY_SERVER_ID )
    buffer:pushdword( id )
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1), "连接失败.. 再次尝试 ..")
    end
end

-- 发送创建房间
function PassFrame:sendCreateRoom()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_QueryGameServer)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_MB_QUERY_GAME_SERVER)
    buffer:pushdword(GlobalUserItem.dwUserID)
    buffer:pushdword(GlobalUserItem.nCurGameKind)
    buffer:pushbyte(PassRoom:getInstance().m_tabRoomOption.cbIsJoinGame)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送创建房间失败！")
    end
end

-- 发送查询私人房
function PassFrame:sendSearchRoom()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_SerchServerTableEnter)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_MB_SEARCH_SERVER_TABLE)
    buffer:pushstring(self._roomId, define_private.ROOM_ID_LEN)
    buffer:pushdword(GlobalUserItem.nCurGameKind)
    buffer:pushbyte(self._roomEnterType or yl.SEARCH_ROOM_TYPE_JOIN)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送查询房间失败！")
    end
end

-- 发送请求配置
function PassFrame:sendQueryRoomParam()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_GetPersonalParameter)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_MB_GET_PERSONAL_PARAMETER)
    buffer:pushdword(GlobalUserItem.nCurGameKind)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送请求配置失败！")
    end
end

-- 发送查询私人房列表
function PassFrame:sendQueryRoomList()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_QeuryPersonalRoomList)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_MB_QUERY_PERSONAL_ROOM_LIST)
    buffer:pushdword(GlobalUserItem.dwUserID)
    buffer:pushdword(GlobalUserItem.nCurGameKind)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送查询房间列表失败！")
    end
end

-- 发送解散房间
function PassFrame:sendDissumeRoom()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_SearchServerTable)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_MB_DISSUME_SEARCH_SERVER_TABLE)
    buffer:pushstring(self._roomId, define_private.ROOM_ID_LEN)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送解散房间失败！")
    end
end

-- 发送查询战绩列表
function PassFrame:sendQueryScoreList(dwRecordID)
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_QeuryScoreList)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_GR_QUERY_RECORD_INFO)
    buffer:pushdword(GlobalUserItem.dwUserID)
    buffer:pushdword(dwRecordID)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送查询战绩列表失败！")
    end
end

-- 发送查询战绩详情
function PassFrame:sendQueryScoreDetail()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_MB_QeuryScoreDetail)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_GR_QUERY_RECORD_SCORE)
    buffer:pushdword(self.m_score_record_id)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送查询战绩详情失败！")
    end
end

-- 发送房卡兑换游戏币
function PassFrame:sendExchangeScore()
    local buffer = ExternalFun.create_netdata(cmd_pri_login.CMD_GP_ExchangeScoreByRoomCard)
    buffer:setcmdinfo(cmd_pri_login.MDM_MB_PERSONAL_SERVICE,cmd_pri_login.SUB_MB_ROOM_CARD_EXCHANGE_TO_SCORE)
    buffer:pushdword(GlobalUserItem.dwUserID)
    buffer:pushscore(self._lExchangeRoomCard)
    buffer:pushstring(GlobalUserItem.szMachine,yl.LEN_MACHINE_ID)
    if not self:sendSocketData(buffer) and nil ~= self._callBack then
        self._callBack(LOGINSERVER(-1),"发送兑换游戏币失败！")
    end
end

-- 发送游戏服务器消息
function PassFrame:sendGameServerMsg( buffer )    
    if nil ~= self._gameFrame and self._gameFrame:isSocketServer() then
        if not self._gameFrame:sendSocketData(buffer) then
            self._callBack(GAMESERVER(-1),"发送解散游戏失败！")
        end
    end    
end

-- 发送进入私人房
function PassFrame:sendEnterPrivateGame()
    if nil ~= self._gameFrame and self._gameFrame:isSocketServer() then
        local c = yl.INVALID_TABLE,yl.INVALID_CHAIR
        -- 找座椅
        local chaircount = self._gameFrame._wChairCount
        for i = 1, chaircount  do
            local sc = i - 1
            if nil == self._gameFrame:getTableUserItem(PassRoom:getInstance().m_dwTableID, sc) then
                c = sc
                break
            end
        end
        print( "PassFrame:sendEnterPrivateGame ==> private enter " .. PassRoom:getInstance().m_dwTableID .. " ## " .. c)

        self._gameFrame:SitDown(PassRoom:getInstance().m_dwTableID, c)
        self._gameFrame:SendGameOption()
    end
end

-- 强制解散游戏
function PassFrame:sendDissumeGame( tableId )
    tableId = tableId or 0
    if nil ~= self._gameFrame and self._gameFrame:isSocketServer() then
        local buffer = ExternalFun.create_netdata(cmd_pri_game.CMD_GR_HostDissumeGame)
        buffer:setcmdinfo(cmd_pri_game.MDM_GR_PERSONAL_TABLE,cmd_pri_game.SUB_GR_HOSTL_DISSUME_TABLE)
        buffer:pushdword(GlobalUserItem.dwUserID)
        buffer:pushdword(tableId)
        if not self._gameFrame:sendSocketData(buffer) then
            self._callBack(GAMESERVER(-1),"发送解散游戏失败！")
        end
    end
end

--  请求解散游戏
function PassFrame:sendRequestDissumeGame()
    if nil ~= self._gameFrame and self._gameFrame:isSocketServer() then
        print("game socket sendRequestDissumeGame")
        local buffer = ExternalFun.create_netdata(cmd_pri_game.CMD_GR_CancelRequest)
        buffer:setcmdinfo(cmd_pri_game.MDM_GR_PERSONAL_TABLE,cmd_pri_game.SUB_GR_CANCEL_REQUEST)
        buffer:pushdword(GlobalUserItem.dwUserID)
        buffer:pushdword(self._gameFrame:GetTableID())
        buffer:pushdword(self._gameFrame:GetChairID())
        if not self._gameFrame:sendSocketData(buffer) then
            self._callBack(GAMESERVER(-1),"请求解散游戏失败！")
        end
    else
        print("游戏解散失败........")
    end
end

-- 回复请求
function PassFrame:sendRequestReply( cbAgree )
    if nil ~= self._gameFrame and self._gameFrame:isSocketServer() then
        local buffer = ExternalFun.create_netdata(cmd_pri_game.CMD_GR_RequestReply)
        buffer:setcmdinfo(cmd_pri_game.MDM_GR_PERSONAL_TABLE,cmd_pri_game.SUB_GR_REQUEST_REPLY)
        buffer:pushdword(GlobalUserItem.dwUserID)
        buffer:pushdword(self._gameFrame:GetTableID())
        buffer:pushbyte(cbAgree)
        if not self._gameFrame:sendSocketData(buffer) then
            self._callBack(GAMESERVER(-1),"回复请求失败！")
        end
    end
end

-- 请求再开一局
function PassFrame:sendContinuePlay(agree)
    if nil ~= self._gameFrame and self._gameFrame:isSocketServer() then
        local buffer = ExternalFun.create_netdata(cmd_pri_game.CMD_GR_RequestReply)
        buffer:setcmdinfo(cmd_pri_game.MDM_GR_PERSONAL_TABLE,cmd_pri_game.SUB_GR_CONTINUE_PLAY)
        buffer:pushdword(GlobalUserItem.dwUserID)
        buffer:pushdword(self._gameFrame:GetTableID())
        buffer:pushbyte(agree)
        if not self._gameFrame:sendSocketData(buffer) then
            self._callBack(GAMESERVER(-1),"回复请求失败！")
        end
    end
end

--网络信息(短连接)
function PassFrame:onSocketEvent( main,sub,pData )
    print("PassFrame:onSocketEvent ==> " .. main .. "##" .. sub)
    local needClose = true
    local dissmissPop = true
    if cmd_pri_login.MDM_MB_PERSONAL_SERVICE == main then
        if cmd_pri_login.SUB_MB_SEARCH_RESULT == sub then                           -- 房间搜索结果
            self:onSubSearchRoomResult(pData)
        elseif cmd_pri_login.SUB_MB_DISSUME_SEARCH_RESULT == sub then               -- 解散搜索结果
            self:onSubDissumeSearchReasult(pData)
        elseif cmd_pri_login.SUB_MB_QUERY_PERSONAL_ROOM_LIST_RESULT == sub then     -- 私人房列表
            self:onSubPrivateRoomList(pData)
        elseif cmd_pri_login.SUB_GR_QUERY_RECORD_INFO_RESULT == sub then            -- 战绩列表
            self:onSubScoreList(pData)
        elseif cmd_pri_login.SUB_GR_QUERY_RECORD_SCOR_RESULT == sub then            -- 战绩详情
            self:onSubScoreDetail(pData)
        elseif cmd_pri_login.SUB_MB_PERSONAL_PARAMETER == sub then                  -- 私人房间属性
            needClose = false
            PassRoom:getInstance().m_tabRoomOption = ExternalFun.read_netdata( struct_private.tagPersonalRoomOption, pData )
            PassRoom:getInstance().cbIsJoinGame = PassRoom:getInstance().m_tabRoomOption.cbIsJoinGame
        elseif cmd_pri_login.SUB_MB_PERSONAL_FEE_PARAMETER == sub then              -- 私人房费用配置
            self:onSubFeeParameter(pData)
        elseif cmd_pri_login.SUB_MB_QUERY_GAME_SERVER_RESULT == sub then            -- 创建结果
            dissmissPop = self:onSubGameServerResult(pData)
        elseif cmd_pri_login.SUB_GP_EXCHANGE_ROOM_CARD_RESULT == sub then           -- 房卡兑换游戏币结果
            self:onSubExchangeRoomCardResult(pData)  
        elseif cmd_pri_login.SUB_GR_QUERY_REPLAY_RESULT == sub then                 -- mike 20170719 -- 回放数据
            self:onSubQueryReplayResult(pData)  	
        elseif cmd_pri_login.SUB_GR_QUERY_SERVER_ID == sub then                   -- mike 20170801 -- 查询房间
            self:onSubQueryServerResult(pData)  
        elseif cmd_pri_login.SUB_GR_EVENT_LIST_RESULT == sub then                   -- mike 20180116 -- 查询活动列表
            self:onSubQueryEventListResult(pData)  
        elseif cmd_pri_login.SUB_GR_EVENT_GET_REWARD_RESULT == sub then             -- mike 20180116 -- 活动领取返回
            self:onSubGetRewardResult(pData) 	
        elseif cmd_pri_login.SUB_GR_EVENT_ACCEPT_RESULT == sub then                 -- mike 20180116 -- 领任务
            self:onSubGetMissionResult(pData) 	
        end
    end
    if needClose then        
        self:onCloseSocket()
        if dissmissPop then
            PassRoom:getInstance():dismissPopWait()
        end
    end    
end

-- mike 20170801 -- 查询房间数据
-- 查询房间数据返回
function PassFrame:onSubQueryServerResult( pData )
    helper.pop.waiting(false)
    local cmd_table = ExternalFun.read_netdata(cmd_pri_login.CMD_GR_QueryServerIDResult, pData)
    GlobalUserItem.dwLockServerID = cmd_table.dwServerID
    PassRoom:getInstance():onEnterPlazaFinish()
end

-- mike 20170719 -- 回放数据
-- 回放命令返回
function PassFrame:onSubQueryReplayResult( pData )
    cs.app.room_frame.isReplay = true
    if self.replayData then
       self.replayData:release()
    end
    self.replayData = pData
    self.replayData:retain() 
    self:doStartReplay()
end

-- 开始回放
function PassFrame:doStartReplay()
    self.replayCD = 0
    self.replayLastTime = yl.time()
    self.replayIsPause = false
    self.replaySpeed = 1
    local  pData = self.replayData
    printf( "replay1111  length %d",  pData:getlen() )
    --[[
    local byte = pData:readbyte()
    local str = '' 
    while byte do
        str = str .. byte
        byte = pData:readbyte() 
    end
    --]]
    pData:resetread()
    ----[[
    cs.app.room_frame:onInitData()
    -- result
    self:doNextReplay()
    -- config
    self:doNextReplay()
    
    PassRoom:getInstance():getPlazaScene():onEnterTable( true )
    
    -- tip
    self:doNextReplay()
    self.replayCD = 1.0
    ----]]
end

-- 单步调用回放数据
function PassFrame:doNextReplay( pData , isSkipCD ) 
    local realCD = self.replayCD / self.replaySpeed
    if not isSkipCD and ( yl.time() - self.replayLastTime < realCD or self.replayIsPause ) then
        return true
    end
    self.replayLastTime = yl.time()
    local data = self.replayData
    if pData ~= nil then
       data = pData
    end
    printf("replay  curDataLen %d", data:getcurlen() )
    local cmd_data = ExternalFun.read_netdata(cmd_pri_login.tagReplayActionHead, data) 
    local wCmd = cmd_data.wCmd
    local wMainCmd = cmd_data.wMainCmd
    local wDataLen = cmd_data.wDataLen
    local wCurlen = data:getcurlen()
    local wTotallen = data:getlen()
    if wCurlen >=  wTotallen then
        print("replay  wCurlen >=  wTotallen ")
        return false
    end
    printf("replay  Real..............curDataLen %d", wCurlen )
    printf("replay  wMainCmd %d, wCmd %d wDataLen %d", wMainCmd, wCmd, wDataLen)
    -- 长度 不符合 标准返回
    if wMainCmd and wCmd then
        if wCmd == cmd_pri_login.SUB_MB_SEARCH_RESULT then
            self:onSocketEvent(wMainCmd, wCmd, data)
        elseif wMainCmd == game_cmd.MDM_GR_USER and wCmd == yl.SUB_GR_USER_ENTER then
            cs.app.room_frame:onSocketUserEnter( data, wDataLen + wCurlen, true )
        else
            cs.app.room_frame:onSocketEvent( wMainCmd, wCmd, data )
        end
    else
        return false
    end    
    return true
end

-- 房间搜索结果
function PassFrame:onSubSearchRoomResult( pData )
    helper.pop.waiting({false, 'reconect'})
    local cmd_table = ExternalFun.read_netdata(cmd_pri_login.CMD_MB_SearchResult, pData)
    --dump(cmd_table, "CMD_MB_SearchResult", 6)
    if 0 == cmd_table.dwServerID then
        if nil ~= self._callBack then
            self._callBack(LOGINSERVER(cmd_pri_login.SUB_MB_SEARCH_RESULT), "该房间号不存在, 请重新输入!")
            local cur_game_layer = helper.app.getFromScene( 'game_room_layer' )
            if cur_game_layer then
                cur_game_layer:exitToMainScene()
            end
        end
        return
    end
    print('房间搜索结果', cmd_table.wKindID)
    -- 后面 onLoginRoom 方法里面 需要 使用 去获取 对应的 server列表
    --GlobalUserItem.nCurGameKind = cmd_table.wKindID
    
    -- 信息记录
    PassRoom:getInstance().m_dwTableID = cmd_table.dwTableID

    -- 动作定义
    PassRoom:getInstance().m_nLoginAction = PassRoom.L_ACTION.ACT_SEARCHROOM
    -- 发送登陆
    
    PassRoom:getInstance():onLoginRoom(cmd_table.dwServerID)
end

-- 解散搜索结果
function PassFrame:onSubDissumeSearchReasult( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_login.CMD_MB_DissumeSearchResult, pData)
    --dump(cmd_table, "CMD_MB_DissumeSearchResult", 6)

    -- 信息记录
    PassRoom:getInstance().m_dwTableID = cmd_table.dwTableID
    -- 动作定义
    PassRoom:getInstance().m_nLoginAction = PassRoom.L_ACTION.ACT_DISSUMEROOM
    -- 发送登陆
    PassRoom:getInstance():onLoginRoom(cmd_table.dwServerID)
end

-- 私人房列表
function PassFrame:onSubPrivateRoomList( pData )
    PassRoom:getInstance().m_tabCreateRecord = {}

    local cmd_table = ExternalFun.read_netdata(cmd_pri_login.CMD_MB_PersonalRoomInfoList, pData)
    local listinfo = cmd_table.PersonalRoomInfo[1]
    for i = 1, define_private.MAX_CREATE_PERSONAL_ROOM do
        local info = listinfo[i]
        if info.szRoomID ~= "" then
            info.lScore = info.lTaxCount--self:getMyReword(info.PersonalUserScoreInfo[1])
            -- 时间戳
            local tt = info.sysDissumeTime
            info.sortTimeStmp = os.time({day=tt.wDay, month=tt.wMonth, year=tt.wYear, hour=tt.wHour, min=tt.wMinute, sec=tt.wSecond})
            tt = info.sysCreateTime
            info.createTimeStmp = os.time({day=tt.wDay, month=tt.wMonth, year=tt.wYear, hour=tt.wHour, min=tt.wMinute, sec=tt.wSecond})
            table.insert(PassRoom:getInstance().m_tabCreateRecord, info)
        else
            break
        end
    end
    table.sort( PassRoom:getInstance().m_tabCreateRecord, function(a, b)
        if a.cbIsDisssumRoom ~= b.cbIsDisssumRoom then
            return a.cbIsDisssumRoom > b.cbIsDisssumRoom
        elseif a.cbIsDisssumRoom == 0 and a.cbIsDisssumRoom == b.cbIsDisssumRoom then
            return a.createTimeStmp < b.createTimeStmp
        else
            return a.sortTimeStmp < b.sortTimeStmp
        end        
    end )
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER(cmd_pri_login.SUB_MB_QUERY_PERSONAL_ROOM_LIST_RESULT))
    end
end

-- 战绩列表
function PassFrame:onSubScoreList( pData )
    local ret = {}
    -- 计算数目
    local len = pData:getlen()
    local itemcount = math.floor(len/define_private.LEN_ROOM_SCORE)
    print("PassFrame onSubScoreList ", len, define_private.LEN_ROOM_SCORE, itemcount)
    for i = 1, itemcount do
        local pServer = ExternalFun.read_netdata(struct_private.tagRecordInfo, pData)
        pServer.szNickName = pServer["szNickName"][1] -- 二维转一维
        pServer.nScore = pServer["nScore"][1] -- 二维转一维
        pServer.dwUserID = pServer["dwUserID"][1] -- 二维转一维
        table.insert(ret, pServer)        
    end
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER(cmd_pri_login.SUB_GR_QUERY_RECORD_INFO_RESULT), nil, ret)
    end
end

-- 战绩详情
function PassFrame:onSubScoreDetail( pData )
    local ret = {}
    -- 计算数目
    local len = pData:getlen()
    local itemcount = math.floor(len/define_private.LEN_ROOM_SCORE_DETAIL)
    print("PassFrame onSubScoreDetail " .. itemcount)
    for i = 1, itemcount do
        local pServer = ExternalFun.read_netdata(struct_private.tagRecordScore, pData)
        pServer.nScore = pServer.nScore[1] -- 二维转一维
        table.insert(ret, pServer)        
    end
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER(cmd_pri_login.SUB_GR_QUERY_RECORD_SCOR_RESULT), nil, ret)
    end
end

function PassFrame:getMyReword( list )
    if type(list) ~= "table" then
        return 0
    end
    for k,v in pairs(list) do
        if v["dwUserID"] == GlobalUserItem.dwUserID then
            return (tonumber(v.lScore) or 0)
        end
    end
    return 0
end

-- 私人房费用配置
function PassFrame:onSubFeeParameter( pData )
    PassRoom:getInstance().m_tabFeeConfigList = {}
    local len = pData:getlen()
    local count = math.floor(len/define_private.LEN_PERSONAL_TABLE_PARAMETER)
    for idx = 1, count do
        local param = ExternalFun.read_netdata( struct_private.tagPersonalTableParameter, pData )
        table.insert(PassRoom:getInstance().m_tabFeeConfigList, param)
    end
    table.sort( PassRoom:getInstance().m_tabFeeConfigList, function(a, b)
        return a.dwDrawCountLimit < b.dwDrawCountLimit
    end )
    --dump(PassRoom:getInstance().m_tabFeeConfigList, "SUB_MB_PERSONAL_FEE_PARAMETER", 6)
    if nil ~= self._callBack then
        self._callBack(LOGINSERVER(cmd_pri_login.SUB_MB_PERSONAL_FEE_PARAMETER))
    end
end

-- 创建结果
function PassFrame:onSubGameServerResult( pData )
    local cmd_table = ExternalFun.read_netdata( cmd_pri_login.CMD_MB_QueryGameServerResult, pData )
    --dump(cmd_table, "CMD_MB_QueryGameServerResult", 6)
    local tips = cmd_table.szErrDescrybe
    if false == cmd_table.bCanCreateRoom then
        if nil ~= self._callBack and type(tips) == "string" then
            self._callBack(LOGINSERVER(-1), tips)
        end
        return true
    end
    if 0 == cmd_table.dwServerID and true == cmd_table.bCanCreateRoom then
        if nil ~= self._callBack and type(tips) == "string" then
            self._callBack(LOGINSERVER(-1), tips)
        end
        return true
    end    
    -- 发送登陆
    PassRoom:getInstance():onLoginRoom(cmd_table.dwServerID)
    return false
end

-- 房卡兑换游戏币结果
function PassFrame:onSubExchangeRoomCardResult( pData )
    local cmd_table = ExternalFun.read_netdata( cmd_pri_login.CMD_GP_ExchangeRoomCardResult, pData )
    --dump(cmd_table, "CMD_GP_ExchangeRoomCardResult", 6)
    if true == cmd_table.bSuccessed then
        -- 个人财富
        GlobalUserItem.lUserScore = cmd_table.lCurrScore
        GlobalUserItem.lRoomCard = cmd_table.lRoomCard
        -- 通知更新        
        local eventListener = cc.EventCustom:new(yl.RY_USERINFO_NOTIFY)
        eventListener.obj = yl.RY_MSG_USERWEALTH
        cc.Director:getInstance():getEventDispatcher():dispatchEvent(eventListener)
    end

    if nil ~= self._callBack then
        self._callBack(LOGINSERVER(cmd_pri_login.CMD_GP_ExchangeRoomCardResult), cmd_table.szNotifyContent)
    end
end

--网络消息(长连接)
function PassFrame:onGameSocketEvent( main,sub,pData )    
    if cmd_pri_game.MDM_GR_PERSONAL_TABLE == main then
        if cmd_pri_game.SUB_GR_CREATE_SUCCESS == sub then               -- 创建成功
            self:onSubCreateSuccess(pData)
        elseif cmd_pri_game.SUB_GR_CREATE_FAILURE == sub then           -- 创建失败
            self:onSubCreateFailure(pData)
        elseif cmd_pri_game.SUB_GR_CANCEL_TABLE == sub then             -- 解散桌子
            self:onSubTableCancel(pData)
        elseif cmd_pri_game.SUB_GR_CANCEL_REQUEST == sub then           -- 请求解散
            self:onSubCancelRequest(pData)
        elseif cmd_pri_game.SUB_GR_REQUEST_REPLY == sub then            -- 请求答复
            self:onSubRequestReply(pData)
        elseif cmd_pri_game.SUB_GR_REQUEST_RESULT == sub then           -- 请求结果
            self:onSubReplyResult(pData)
        elseif cmd_pri_game.SUB_GR_WAIT_OVER_TIME == sub then           -- 超时等待
            self:onSubWaitOverTime(pData)
        elseif cmd_pri_game.SUB_GR_PERSONAL_TABLE_TIP == sub then       -- 提示信息/游戏信息
            self:onSubTableTip(pData)
        elseif cmd_pri_game.SUB_GR_PERSONAL_TABLE_END == sub then       -- 结束
            self:onSubTableEnd(pData)
        elseif cmd_pri_game.SUB_GR_CANCEL_TABLE_RESULT == sub then      -- 私人房解散结果
            self:onSubCancelTableResult(pData)
        elseif cmd_pri_game.SUB_GR_CURRECE_ROOMCARD_AND_BEAN == sub then -- 强制解散桌子后的游戏豆和房卡数量
            self:onSubCancelTableScoreInfo(pData)
        elseif cmd_pri_game.SUB_GR_CHANGE_CHAIR_COUNT == sub then       -- 改变椅子数量
            self:onSubChangeChairCount(pData)
        elseif cmd_pri_game.SUB_GR_CONTINUE_PLAY == sub then            -- 房卡场继续打
            self:onSubContinuePlay(pData)
        elseif cmd_pri_game.SUB_GF_PERSONAL_MESSAGE == sub then         -- 私人房消息
            local cmd_table = ExternalFun.read_netdata(cmd_pri_game.Personal_Room_Message, pData)
            if nil ~= self._callBack then
                self._callBack(GAMESERVER(-1), cmd_table.szMessage)
            end
            if self._gameFrame:isSocketServer() then
                self._gameFrame:onCloseSocket()
                GlobalUserItem.nCurRoomIndex = -1
            end
        end
    end
end

-- 创建成功
function PassFrame:onSubCreateSuccess( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_CreateSuccess, pData)
    -- 更新私人房数据
    PassRoom:getInstance().m_tabPriData.cbPlayCountIndex = cmd_table.cbPlayCountIndex
    PassRoom:getInstance().m_tabPriData.nPlayCount = cmd_table.nPlayCount
    PassRoom:getInstance().m_tabPriData.szServerID = cmd_table.szServerID
    -- 个人财富
    GlobalUserItem.dUserBeans = cmd_table.dBeans
    GlobalUserItem.lRoomCard = cmd_table.lRoomCard
    -- 通知更新        
    local eventListener = cc.EventCustom:new(yl.RY_USERINFO_NOTIFY)
    eventListener.obj = yl.RY_MSG_USERWEALTH
    cc.Director:getInstance():getEventDispatcher():dispatchEvent(eventListener)

    -- 
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_CREATE_SUCCESS))
    end
    if 0 == PassRoom:getInstance().m_tabRoomOption.cbIsJoinGame then
        if self._gameFrame:isSocketServer() then
            self._gameFrame:onCloseSocket()
        end
    else
        PassRoom:getInstance().m_nLoginAction = PassRoom.L_ACTION.ACT_SEARCHROOM
    end
end

-- 创建失败
function PassFrame:onSubCreateFailure( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_CreateFailure, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(-1), cmd_table.szDescribeString)
    end
end

-- 解散桌子
function PassFrame:onSubTableCancel( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_CancelTable, pData)
    --dump(cmd_table, "CMD_GR_CancelTable", 6)

    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_CANCEL_TABLE), cmd_table)
    end
end

-- 请求解散
function PassFrame:onSubCancelRequest( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_CancelRequest, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_CANCEL_REQUEST), cmd_table)
    end
end

-- 请求答复
function PassFrame:onSubRequestReply( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_RequestReply, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_REQUEST_REPLY), cmd_table)
    end
end

-- 请求结果
function PassFrame:onSubReplyResult( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_RequestResult, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_REQUEST_RESULT), cmd_table)
    end
end

-- 房卡场继续打
function PassFrame:onSubContinuePlay( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_RequestReply, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_CONTINUE_PLAY), cmd_table)
    end
end

-- 超时等待
function PassFrame:onSubWaitOverTime( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_WaitOverTime, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_WAIT_OVER_TIME), cmd_table)
    end
end

-- 提示信息
function PassFrame:onSubTableTip( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_PersonalTableTip, pData)
    PassRoom:getInstance().m_tabPriData = cmd_table
    PassRoom:getInstance().m_bIsMyRoomOwner = (cmd_table.dwTableOwnerUserID == GlobalUserItem.dwUserID)
    PassRoom:getInstance().cbIsJoinGame = PassRoom:getInstance().m_tabPriData.cbIsJoinGame
    if self._gameFrame and cmd_table.cbChairCount then
        self._gameFrame._wChairCount = cmd_table.cbChairCount
        local cur_game_layer = helper.app.getFromScene( 'game_room_layer' )
        if cur_game_layer then
            -- print('onSubTableTip..', initPlayers)
            cur_game_layer._gameView:initPlayers()
        end
    end
    print('onSubTableTip dwChairCount is ', self._gameFrame._wChairCount)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_PERSONAL_TABLE_TIP), cmd_table)
    end
    --dump(PassRoom:getInstance().m_tabPriData, "PassRoom:getInstance().m_tabPriData", 6)
end

-- 结束消息
function PassFrame:onSubTableEnd( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_PersonalTableEnd, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_PERSONAL_TABLE_END), cmd_table, pData)
    end
end

-- 私人房解散结果
function PassFrame:onSubCancelTableResult( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_DissumeTable, pData)
    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_CANCEL_TABLE_RESULT), cmd_table)
    end
    if self._gameFrame:isSocketServer() then
        self._gameFrame:onCloseSocket()
    end
end

-- 解散后财富信息
function PassFrame:onSubCancelTableScoreInfo( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_CurrenceRoomCardAndBeans, pData)
    -- 个人财富
    GlobalUserItem.dUserBeans = cmd_table.dbBeans
    GlobalUserItem.lRoomCard = cmd_table.lRoomCard
    -- 通知更新        
    local eventListener = cc.EventCustom:new(yl.RY_USERINFO_NOTIFY)
    eventListener.obj = yl.RY_MSG_USERWEALTH
    cc.Director:getInstance():getEventDispatcher():dispatchEvent(eventListener)

    if nil ~= self._callBack then
        self._callBack(GAMESERVER(cmd_pri_game.SUB_GR_CURRECE_ROOMCARD_AND_BEAN), nil, cmd_table)
    end
end

-- 改变椅子数量
function PassFrame:onSubChangeChairCount( pData )
    local cmd_table = ExternalFun.read_netdata(cmd_pri_game.CMD_GR_ChangeChairCount, pData)
    if nil ~= cmd_table.dwChairCount then
        self._gameFrame._wChairCount = cmd_table.dwChairCount
        print('modify dwChairCount is ', self._gameFrame._wChairCount)
    end
end


return PassFrame
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 分享
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")


local OrbShare = class('OrbShare', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbShare:ctor(panel)
    print('OrbShare:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('bg/label_notice'):hide()

    self.m_panel:addTouchEventListener( handler(self, self.onBtnClose) )
    self.m_panel:child('bg/btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )

    self.m_origin_pos = cc.p(self.m_panel:child('bg'):pos())
end


-------------------------------------------------------------------------------
-- 显示
-------------------------------------------------------------------------------
function OrbShare:onShow(params)
    local amount = self:getOffset()
    local label_amount = self.m_panel:child('bg/label_amount')
    if amount > 0 then
        local str = LANG{'ORB_SHARE_TIP', amount = string.format('%.2f', amount)}
        label_amount:setString(str)
    else
        label_amount:setString( LANG.ORB_SHARE_TIP_DONE )
    end

    self.m_panel:child('bg/panel_notice'):removeAllChildren()
    self.m_panel:perform(function()
        self:showNotice(self.m_panel:child('bg/panel_notice'), self.m_panel:child('bg/label_notice'), 'ORB_NOTICE')
    end, 0.2)

    self.m_params = params
    local bg = self.m_panel:child('bg')
    bg:stop()
    bg:py(self.m_origin_pos.y - 320)
    bg:runAction(cc.MoveTo:create(0.1, self.m_origin_pos))
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function OrbShare:onBtnClose()
    local bg = self.m_panel:child('bg')
    bg:stop()
    bg:runAction( cc.Sequence:create(
        cc.MoveTo:create(0.1, cc.pAdd(self.m_origin_pos, cc.p(0, -320))),
        cc.CallFunc:create(function()
            OrbUtil.close(self.m_panel)
        end)
    ) )
    
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function OrbShare:onBtnShare()
    local channel = MultiPlatform:getInstance():getChannel()
    local main_id = self:getMainId()
    local url = cs.app.ORB_SHARE_URL .. '?channel_id=' .. channel .. '&dismantle_id=' .. main_id
    local title = LANG['ORB_SHARE_TITLE_' .. math.random(1, 5)]
    local desc = LANG.ORB_SHARE_DESC
    local layer = helper.pop.shareLink(url, title, desc, false, false, false, false, function()
        self:onShareSuccess()
    end)
    layer:showButtons('hy')
    layer:hide():doShare(yl.ThirdParty.WECHAT)
end


-------------------------------------------------------------------------------
-- 分享成功
-------------------------------------------------------------------------------
function OrbShare:onShareSuccess()
    if self.m_params and self.m_params.callback then
        self.m_params.callback()
    end
    self:close()
    if not self.m_params.no_result then
        self:open('panel_share_success')
    end
end


return OrbShare
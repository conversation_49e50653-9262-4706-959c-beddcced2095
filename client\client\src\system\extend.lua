-------------------------------------------------------------------------------
--  创世版1.0
--  当前游戏特定玩法扩展
--  使用extend.execute进行hook，extend.override进行重载或新增方法
--      为确保扩展方法内部self的安全，定义时，扩展方法名需加类前缀，而且是':'定义
--      标准：function extend:类名_方法名_kind(...)
--      比如：function extend:GameViewLayer_onButtonEventChi_303(...)
--      使用：extend.execute(self, 'onButtonEventChi', <可选参数1>, ...)
--  @date 2018-02-24
--  @auth woodoo
-------------------------------------------------------------------------------

local extend = {}


-------------------------------------------------------------------------------
-- 按类型执行扩展方法
-- 使用: extend.execute(self, 'onButtonEventChi', <可选参数1>, ...)
-- 返回值：按需求设计返回值
--       有些方法需要中断调用处的流程，可以让方法返回true，如下使用
--       if extend.execute(self, 'onButtonEventChi') then return end
-------------------------------------------------------------------------------
function extend.execute(this, func_name, ...)
    if not cs.game or not cs.game.extend then return end
    local kind = GlobalUserItem.nCurGameKind
    local name = this.__cname
    local func = name .. '_' .. func_name .. '_' .. kind
    if cs.game.extend[func] then
        local ret = cs.game.extend[func](this, ...)
        return ret
    end
end


-------------------------------------------------------------------------------
-- 按类型重载
--  如果需要覆盖或新增类方法，可以在类定义末尾（return之前）执行该方法
--            比如：extend.override(GameViewLayer)
--  重载方法命名规则：function extend:类名_方法名_kind_override(...)
--             比如：function extend:GameViewLayer_onButtonEventChi_303_override(...)
-------------------------------------------------------------------------------
function extend.override(Class)
    if not cs.game or not cs.game.extend then return end
    local kind = GlobalUserItem.nCurGameKind
    local name = Class.__cname
    for k, v in pairs(cs.game.extend) do
        if type(v) == 'function' then
            local func_name = k:match(name .. '_([%w_]+)_' .. kind .. '_override')
            if func_name then
                Class[func_name] = v
            end
        end
    end
end


return extend
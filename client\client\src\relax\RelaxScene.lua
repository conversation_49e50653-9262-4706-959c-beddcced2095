-------------------------------------------------------------------------------
--  创世版1.0
--  小游戏
--  @date 2019-05-13
--  @auth woodoo
-------------------------------------------------------------------------------
if not yl then appdf.req(appdf.CLIENT_SRC..'frame.yl') end
if not cs then appdf.req(appdf.CLIENT_SRC..'system.init') end

local RelaxScene = class("RelaxScene", cc.load("mvc").ViewBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RelaxScene:ctor(app, name)
    self.super.ctor(self, app, name)
    local path = cs.app.CLIENT_SRC .. 'relax.Relax' .. (cs.app.RELAX or 'Jump')
    local cls = require(path)
    local layer = cls:create()
    self:addChild(layer)
end


return RelaxScene
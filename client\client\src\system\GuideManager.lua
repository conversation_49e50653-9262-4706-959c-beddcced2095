-------------------------------------------------------------------------------
--  创世版1.0
--  引导管理层
--  @date 2018-04-19
--  @auth woodoo
-------------------------------------------------------------------------------
local GuideLayer = class("GuideLayer")


local _instance = nil


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GuideLayer.getInScene()
    if not _instance then
        _instance = GuideLayer.new()
    end
    return _instance
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GuideLayer:ctor()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function GuideLayer:onEnter()
    print('GuideLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GuideLayer:onExit()
    print('GuideLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 开始引导步骤
-------------------------------------------------------------------------------
function GuideLayer:startStep(step_name)
    if step_name == 'newbie_gift' then
        local main_scene = helper.app.getFromScene('main_scene')
        if not main_scene then return end
        local node = main_scene.main_node:child('btn_my_red_bag')
        if not node then return end
        helper.pop.guide(node, nil, cc.p(0, -2))
    end
end


return GuideLayer

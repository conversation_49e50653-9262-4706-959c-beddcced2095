-------------------------------------------------------------------------------
--  创世版1.0
--  个人中心
--  @date 2018-03-19
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')
local HeadSprite = require(appdf.EXTERNAL_SRC .. "HeadSprite")


local PersonalCenterLayer = class("PersonalCenterLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function PersonalCenterLayer:ctor()
    print('PersonalCenterLayer:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('PersonalCenterLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_cert_mobile'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCertMobile) )
    main_node:child('btn_cert_identity'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCertIdentity) )
    main_node:child('btn_cert_weixin'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCertWeixin) )
    main_node:child('check_male'):addTouchEventListener( handler(self, self.onCheckSex) )
    main_node:child('check_female'):addTouchEventListener( handler(self, self.onCheckSex) )
    main_node:child('check_male').is_male = true

    self:initInfo()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function PersonalCenterLayer:onEnter()
    print('PersonalCenterLayer:onEnter...')
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_SET_GENER, self, self.onGenerChangeResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_SET_NAME_ID, self, self.onIdentityBindResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function PersonalCenterLayer:onExit()
    print('PersonalCenterLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 显示信息
-------------------------------------------------------------------------------
function PersonalCenterLayer:setBtnBound(btn, is_phone)
    if is_phone then
        btn:child('label'):setString( LANG.CHANGE_PHONE )
    else
        btn:setTouchEnabled(false)
        btn:texture('common/btn_long_gray.png')
        btn:child('label'):setString( lang_key or LANG.HAS_BOUND )
    end
end


-------------------------------------------------------------------------------
-- 显示信息
-------------------------------------------------------------------------------
function PersonalCenterLayer:initInfo()
    local main_node = self.main_node

    yl.ReviewCheck(main_node:child('label_rank_open'))

    -- 头像
    local panel = main_node:child('panel_avator')
    panel:removeAllChildren()

    local size = panel:size()
	local head = HeadSprite:createNormal(GlobalUserItem, 132)
    head:pos(size.width/2, size.height/2):addTo(panel)

    -- 月卡
    if helper.app.addAvatorCard(panel, 1) then
        main_node:child('bg_avator'):hide()
    end

    -- 性别
    main_node:child('check_male'):setSelected( GlobalUserItem.cbGender == 0 )
    main_node:child('check_female'):setSelected( GlobalUserItem.cbGender == 1 )

    -- vip
    -- todo:
    main_node:child('img_vip'):hide()

    -- 非金币场房卡显示在前（交换位置）
    if not cs.app.IS_GOLD_HALL then
        local x = main_node:child('label_gold'):px()
        main_node:child('label_gold'):px(main_node:child('label_fangka'):px())
        main_node:child('label_fangka'):px(x)
        x = main_node:child('icon_gold'):px()
        main_node:child('icon_gold'):px(main_node:child('icon_fangka'):px())
        main_node:child('icon_fangka'):px(x)
    end

    helper.app.checkFangkDiamond(main_node:child('icon_fangka'))

    main_node:child('label_name'):setString( GlobalUserItem.szNickName )
    main_node:child('label_id'):setString( helper.str.formatUserID(GlobalUserItem.dwUserID) )
    main_node:child('label_gold'):setString( helper.str.makeFormatNum(GlobalUserItem.lUserScore, 1) )
    main_node:child('label_quan'):setString( GlobalUserItem.dwTicket )
    main_node:child('label_fangka'):setString( GlobalUserItem.lRoomCard )
    helper.app.addGoldLabelEvent(main_node:child('label_gold'))

    local disableBtn = function(btn)
        btn:setTouchEnabled(false)
        btn:texture('common/btn_long_gray.png')
        btn:child('label'):setString( LANG.HAS_BOUND )
    end

    -- 手机绑定
    local mobile = GlobalUserItem.szMobilePhone
    if mobile and mobile:trim() ~= '' then
        main_node:child('label_phone'):setString(mobile)
        self:setBtnBound( main_node:child('btn_cert_mobile'), true )
    else
        main_node:child('label_phone'):setString( LANG.NOT_BIND_PHONE )
    end

    -- 身份绑定
    local identity = GlobalUserItem.szPassportID
    if identity and identity:trim() ~= '' then
        self:setBtnBound( main_node:child('btn_cert_identity') )
    end

    -- 微信绑定
    local weixin = 'a' -- todo:
    if weixin and weixin:trim() ~= '' then
        self:setBtnBound( main_node:child('btn_cert_weixin') )
    end
end


-------------------------------------------------------------------------------
-- 性别点击
-------------------------------------------------------------------------------
function PersonalCenterLayer:onCheckSex(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.tintClickEffect(sender, event)
    local check_male, check_female = self.main_node:child('check_male, check_female')
    check_male:setSelected(false)
    check_female:setSelected(false)
    sender:setSelected(true)
    local gender = sender.is_male and 0 or 1
    
    -- 保存
    if gender ~= GlobalUserItem.cbGender then
        GlobalUserItem.cbGender = gender
	    local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = GlobalUserItem.cbGender} )
	    cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_SET_GENER)
        LiveFrame:getInstance():send(cmd_data)
    end
end


-------------------------------------------------------------------------------
-- 手机认证按钮点击
-------------------------------------------------------------------------------
function PersonalCenterLayer:onBtnCertMobile(sender)
    local path = cs.app.CLIENT_SRC .. 'main.MobileBindLayer'
    local this = self
    local callback = function(is_bind)
        if not is_bind or tolua.isnull(this) then return end
        this:setBtnBound( this.main_node:child('btn_cert_mobile'), true )
    end
    helper.pop.popLayer(path, nil, {callback}, nil, true)
end


-------------------------------------------------------------------------------
-- 身份认证按钮点击
-------------------------------------------------------------------------------
function PersonalCenterLayer:onBtnCertIdentity(sender)
    local path = cs.app.CLIENT_SRC .. 'main.IdentityBindLayer'
    local this = self
    local callback = function(name, no)
        if tolua.isnull(this) then return end

        self.m_real_name = name   -- 临时备份
        self.m_identity_no = no
	    local cmd_data = ExternalFun.create_netdata( cmd_common.tagNameMsg, {szName = name, szMsg = no} )
	    cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_SET_NAME_ID)
        LiveFrame:getInstance():send(cmd_data)
    end
    helper.pop.popLayer(path, nil, {callback}, nil, true)
end


-------------------------------------------------------------------------------
-- 微信认证按钮点击
-------------------------------------------------------------------------------
function PersonalCenterLayer:onBtnCertWeixin(sender)
    local path = cs.app.CLIENT_SRC .. 'main.MobileBindLayer'
    local this = self
    local callback = function()
        if tolua.isnull(this) then return end
        this:setBtnBound( this.main_node:child('btn_cert_weixin') )
    end
    helper.pop.popLayer(path, nil, {callback}, nil, true)
end


-------------------------------------------------------------------------------
-- 性别修改返回
-------------------------------------------------------------------------------
function PersonalCenterLayer:onGenerChangeResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_ID)
    if not ret then return end
end


-------------------------------------------------------------------------------
-- 身份绑定返回
-------------------------------------------------------------------------------
function PersonalCenterLayer:onIdentityBindResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDMsg)
    if not ret then return end
    if ret.dwID > 0 then
        helper.pop.message(ret.szMsg)
    else
        GlobalUserItem.szTrueName = self.m_real_name
        GlobalUserItem.szPassportID = self.m_identity_no
        helper.pop.message( LANG.IDENTITY_BIND_SUCC ) 
        self:setBtnBound( self.main_node:child('btn_cert_identity') )
    end
end


return PersonalCenterLayer

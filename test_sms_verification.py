#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
短信验证码发送测试脚本
基于 test_http.txt 文档和 user.php 中的配置
专门测试验证码短信发送功能
"""

import hashlib
import time
import requests
import json
import random
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SMSVerificationTester:
    def __init__(self):
        # 从user.php中获取的云之讯配置
        self.accountsid = "5cbc351f17a418686094747a62ffd946"
        self.token = "fac1c2af0c05327677d33916ba841079"
        self.appId = "9dc983c028e54d5fbc97228e6af5344e"
        self.template_id = "174333"
        
        # HTTP接口配置
        self.clientid = self.accountsid
        self.password = self.token
        self.base_url = "http://open2.ucpaas.com"
        
        # 测试手机号
        self.test_mobile = "***********"  # 请替换为真实手机号
        
    def test_connectivity(self):
        """测试云之讯服务器连通性"""
        print("=== 测试云之讯服务器连通性 ===")
        
        test_urls = [
            "http://open2.ucpaas.com",
            "http://open2.ucpaas.com/sms-server/templatesms",
            "https://open.ucpaas.com",  # 原来的API地址
        ]
        
        for url in test_urls:
            try:
                print(f"测试: {url}")
                response = requests.get(url, timeout=10, verify=False)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code in [200, 404, 405]:  # 这些都表示连接正常
                    print(f"  ✅ 连接正常")
                else:
                    print(f"  ⚠️ 状态码异常: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 连接失败: {e}")
        print()
    
    def test_verification_sms(self, mobile=None, code=None):
        """测试验证码短信发送"""
        print("=== 测试验证码短信发送 ===")
        
        if mobile is None:
            mobile = self.test_mobile
        if code is None:
            code = str(random.randint(1000, 9999))
            
        print(f"手机号: {mobile}")
        print(f"验证码: {code}")
        
        # 方法1: 使用HTTP接口（推荐）
        success = self.test_http_template_sms(mobile, code)
        
        if not success:
            # 方法2: 使用原来的HTTPS接口
            print("\n尝试使用原来的HTTPS接口...")
            success = self.test_https_original_api(mobile, code)
        
        return success
    
    def test_http_template_sms(self, mobile, code):
        """使用HTTP模板短信接口"""
        print("\n--- 使用HTTP模板短信接口 ---")
        
        url = f"{self.base_url}/sms-server/templatesms"
        
        # 构建请求数据（按照文档格式）
        data = {
            "clientid": self.clientid,
            "password": self.password,
            "mobile": mobile,
            "templateid": self.template_id,
            "param": code,  # 验证码参数
            "extend": "00",
            "uid": str(int(time.time())),
            "sendtime": ""  # 立即发送
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"解析后的JSON: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    code_result = result.get('code', -999)
                    msg = result.get('msg', '未知错误')
                    
                    if code_result == 0:
                        print("✅ HTTP接口发送成功！")
                        
                        # 显示详细信息
                        total_fee = result.get('total_fee', 0)
                        print(f"计费条数: {total_fee}")
                        
                        if 'data' in result:
                            for item in result['data']:
                                print(f"手机号: {item.get('mobile')}")
                                print(f"短信ID: {item.get('sid')}")
                                print(f"费用: {item.get('fee')} 条")
                        
                        return True
                    else:
                        print(f"❌ HTTP接口发送失败")
                        print(f"错误代码: {code_result}")
                        print(f"错误描述: {msg}")
                        self.explain_error_code(code_result)
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response.text}")
                    return False
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_https_original_api(self, mobile, code):
        """使用原来的HTTPS接口（作为备用）"""
        print("\n--- 使用原来的HTTPS接口 ---")
        
        url = 'https://open.ucpaas.com/ol/sms/sendsms'
        
        # 构建请求数据（原来的格式）
        data = {
            'sid': self.accountsid,
            'token': self.token,
            'appid': self.appId,
            'templateid': self.template_id,
            'param': code,
            'mobile': mobile,
            'uid': ''
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"解析后的JSON: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查两种可能的响应格式
                    if 'resp' in result:
                        # 格式1: {"resp": {"respCode": "000000", "respDesc": "成功"}}
                        resp_code = result['resp'].get('respCode', '')
                        resp_desc = result['resp'].get('respDesc', '')

                        print(f"响应代码: {resp_code}")
                        print(f"响应描述: {resp_desc}")

                        if resp_code == '000000':
                            print("✅ HTTPS接口发送成功！")
                            return True
                        else:
                            print(f"❌ HTTPS接口发送失败")
                            print(f"错误代码: {resp_code}")
                            print(f"错误描述: {resp_desc}")
                            return False
                    elif 'code' in result:
                        # 格式2: {"code": "000000", "msg": "成功", "smsid": "xxx"}
                        code = result.get('code', '')
                        msg = result.get('msg', '')
                        smsid = result.get('smsid', '')

                        print(f"响应代码: {code}")
                        print(f"响应描述: {msg}")
                        if smsid:
                            print(f"短信ID: {smsid}")

                        if code == '000000':
                            print("✅ HTTPS接口发送成功！")
                            return True
                        else:
                            print(f"❌ HTTPS接口发送失败")
                            print(f"错误代码: {code}")
                            print(f"错误描述: {msg}")
                            return False
                    else:
                        print(f"❌ 未知的响应格式: {result}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    return False
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def explain_error_code(self, code):
        """解释错误代码"""
        error_codes = {
            0: "成功",
            -1: "鉴权失败（帐号或密码错误）",
            -2: "账号余额不足",
            -3: "账号被注销",
            -4: "账号被锁定",
            -5: "ip鉴权失败",
            -15: "用户对应的模板ID不存在、或模板未通过审核、或模板已删除",
            -16: "模板参数不匹配",
            -20: "json格式错误",
            -21: "解析json失败"
        }
        
        description = error_codes.get(code, f"未知错误代码: {code}")
        print(f"💡 错误说明: {description}")
        
        # 提供解决建议
        if code == -1:
            print("   建议: 检查clientid和password是否正确")
            print(f"   当前clientid: {self.clientid}")
            print(f"   当前password: {self.password}")
        elif code == -2:
            print("   建议: 充值账户余额")
        elif code == -5:
            print("   建议: 将服务器IP添加到云之讯白名单")
        elif code == -15:
            print("   建议: 检查模板ID是否正确，确认模板已审核通过")
            print(f"   当前模板ID: {self.template_id}")
        elif code == -16:
            print("   建议: 检查模板参数是否与模板匹配")
    
    def test_balance_query(self):
        """测试余额查询"""
        print("\n=== 测试余额查询 ===")
        
        url = f"{self.base_url}/sms-server/getbalance"
        
        data = {
            "clientid": self.clientid,
            "password": self.password
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=utf-8'
        }
        
        try:
            response = requests.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30,
                verify=False
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code', -999)
                
                if code == 0:
                    print("✅ 余额查询成功！")
                    data_list = result.get('data', [])
                    
                    for item in data_list:
                        product_type = item.get('product_type')
                        remain_quantity = item.get('remain_quantity')
                        
                        type_names = {0: "行业短信", 1: "营销短信", 2: "国际短信"}
                        type_name = type_names.get(product_type, f"类型{product_type}")
                        unit = "元" if product_type == 2 else "条"
                        
                        print(f"  {type_name}: {remain_quantity}{unit}")
                    
                    return True
                else:
                    print(f"❌ 余额查询失败: {result.get('msg')}")
                    self.explain_error_code(code)
                    return False
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

def main():
    """主测试函数"""
    print("开始短信验证码发送测试...")
    print("="*60)
    
    # 创建测试器
    tester = SMSVerificationTester()
    
    print("配置信息:")
    print(f"ClientID: {tester.clientid}")
    print(f"Password: {tester.password}")
    print(f"AppID: {tester.appId}")
    print(f"TemplateID: {tester.template_id}")
    print(f"测试手机号: {tester.test_mobile}")
    print()
    
    # 1. 测试连通性
    tester.test_connectivity()
    
    # 2. 测试余额查询
    balance_success = tester.test_balance_query()
    
    # 3. 测试验证码发送
    print("\n" + "="*60)
    sms_success = tester.test_verification_sms()
    
    # 生成测试报告
    print("\n" + "="*60)
    print("测试报告:")
    print("="*60)
    
    print(f"余额查询: {'✅ 成功' if balance_success else '❌ 失败'}")
    print(f"短信发送: {'✅ 成功' if sms_success else '❌ 失败'}")
    
    if sms_success:
        print("\n🎉 短信验证码发送成功！")
        print("请检查手机是否收到验证码短信")
    else:
        print("\n❌ 短信验证码发送失败")
        print("\n💡 可能的解决方案:")
        print("1. 检查云之讯账户状态和余额")
        print("2. 确认模板ID和参数格式正确")
        print("3. 检查IP白名单设置")
        print("4. 联系云之讯技术支持")
        print("5. 考虑更换短信服务商（如腾讯云、阿里云）")

if __name__ == '__main__':
    main()

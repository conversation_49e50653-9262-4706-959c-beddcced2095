#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import time
import requests
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def generate_sign(params, channel_key):
    """生成签名"""
    # 按键名排序
    sorted_keys = sorted(params.keys())
    
    # 拼接所有值
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    
    # 加上渠道密钥
    sign_str = param_str + channel_key
    
    # MD5签名
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()

def test_sms_api():
    """测试短信验证码接口"""
    
    # 基础参数
    params = {
        'uid': '123456',  # 测试用户ID
        'phone': '13800138000',  # 测试手机号
        'type': 'login',  # 类型
        'uuid': 'TEST_DEVICE_ID',  # 设备ID
        'timestamp': str(int(time.time() * 1000)),  # 时间戳
        'channel': '50010001',  # 渠道号
        'c_version': '10',  # 客户端版本
        'res_version': '1',  # 资源版本
    }
    
    # 渠道密钥
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    
    # 生成签名
    sign = generate_sign(params, channel_key)
    params['sign'] = sign
    
    # 请求URL
    url = 'https://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code'
    
    print("=== 测试短信验证码接口 ===")
    print(f"URL: {url}")
    print(f"参数: {params}")
    
    try:
        # 发送请求
        response = requests.post(url, data=params, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"JSON数据: {json_data}")
                
                if json_data.get('code') == 0:
                    print("✅ 短信发送成功！")
                else:
                    print(f"❌ 短信发送失败: {json_data.get('msg', '未知错误')}")
            except:
                print("❌ 响应不是有效的JSON格式")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")

if __name__ == '__main__':
    test_sms_api()
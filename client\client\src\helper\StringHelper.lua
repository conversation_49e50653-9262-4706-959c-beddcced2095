-------------------------------------------------------------------------------
--  创世版1.0
--  字符串辅助方法类
--      访问方式：helper.str.
--  @date 2017-06-23
--  @auth woodoo
-------------------------------------------------------------------------------
local StringHelper = {}
helper = helper or {}
helper.str = StringHelper


-------------------------------------------------------------------------------
-- 格式化用户ID
-------------------------------------------------------------------------------
function StringHelper.formatUserID(uid)
    return string.format('%06.0f', uid)
end


-------------------------------------------------------------------------------
-- 分割utf8
-------------------------------------------------------------------------------
function StringHelper.splitUTF8(str)
    local len  = #str
    local left = 0
    local arr  = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
    local t = {}
    local start = 1
    local wordLen = 0
    while len ~= left do
        local tmp = string.byte(str, start)
        local i   = #arr
        while arr[i] do
            if tmp >= arr[i] then
                break
            end
            i = i - 1
        end
        wordLen = i + wordLen
        local tmpString = string.sub(str, start, wordLen)
        start = start + i
        left = left + i
        t[#t + 1] = tmpString
    end

    return t
end


-------------------------------------------------------------------------------
-- 获取字符串中的n个字节(中文：2个字节，数字和英文算一个字节)
-------------------------------------------------------------------------------
function StringHelper.getLenCharsFromString(str, nLen, suffix)
    local chars = helper.str.splitUTF8(str)
    local len = 0
    local retStr = ''
    local is_need_suffix = false
    for i, v in ipairs(chars) do
        len = len + (#v > 1 and 2 or 1)
        if len <= nLen then
            retStr = retStr .. v
        else
            is_need_suffix = true
            break
        end
    end
    if is_need_suffix then
        retStr = retStr .. suffix
    end
    
    return  retStr
end


-------------------------------------------------------------------------------
-- 生成url请求参数字符串
-------------------------------------------------------------------------------
function StringHelper.makeUrlParams(params)
    local t = {}
    for k, v in pairs(params) do
		table.insert(t, k .. '=' .. v)
	end
    return table.concat(t, '&')
end


-------------------------------------------------------------------------------
-- 格式化数字, bit显示几位小数
-------------------------------------------------------------------------------
function StringHelper.makeFormatNum(num, bit)
    bit = bit or 1
    local million = 100000000
    local thousand = 10000
    local hundred_million = 1000000
    local remainder = num / million
    local remainder_t = num / thousand
    local remainder_h = num / hundred_million
    if remainder >= 1 then
        local format = '%.0' .. bit .. 'f' .. '亿'
        return string.format(format, remainder)
    elseif remainder_h >= 1 then
        local format = '%d' .. '万'
        return string.format(format, remainder_t)
    elseif remainder_t >= 1 then
        local format = '%.0' .. bit .. 'f' .. '万'
        return string.format(format, remainder_t)
    else
        return string.format('%d', num)
    end
end
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 活动规则
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbRuleActivity = class('OrbRuleActivity', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbRuleActivity:ctor(panel)
    print('OrbRuleActivity:ctor...')
    self.super.ctor(self, panel)

    panel:child('label_last'):setString( LANG{'ORB_RULE_ACTIVITY', app_name = cs.app.APP_NAME} )
end


return OrbRuleActivity
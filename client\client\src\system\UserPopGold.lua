-------------------------------------------------------------------------------
--  创世版1.0
--  金币场玩家悬浮信息
--  @date 2018-03-20
--  @auth woodoo
-------------------------------------------------------------------------------
local UserPopGold = class('UserPopGold', cc.Layer)
local HeadSprite = require(appdf.EXTERNAL_SRC .. "HeadSprite")
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")


-- 全局记录上一次动作时间
local last_act_time = 0
-- 最小动作间隔，秒
local ACT_INTERNAL = 5


-------------------------------------------------------------------------------
-- 静态方法
-------------------------------------------------------------------------------
function UserPopGold.popForUser(parent, user_item, pos)
    local instance = UserPopGold:create()
    helper.app.addToScene(instance, parent)

    instance:initInfo(user_item)
    instance:effectShow(pos)
    
    return instance
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function UserPopGold:ctor()
    print('UserPopGold:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('UserPopGold.csb')
    self.main_node = main_node
    self:addChild(main_node)

    helper.pop.addMask(self, 100, true)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function UserPopGold:onExit()
    print('UserPopGold:onExit...')
end


-------------------------------------------------------------------------------
-- 效果显示
-------------------------------------------------------------------------------
function UserPopGold:effectShow(pos)
    local bg = self.main_node:child('bg')
    local ax, ay = 0, 0
    if pos.x > display.cx then ax = 1 end
    if pos.y > display.cy then ay = 1 end
    bg:anchor(ax, ay):pos(pos):stop():scale(0):runMyAction( cc.Sequence:create(
        cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
    ) )
    self:show()
    self:child('mask'):effectShow()
end


-------------------------------------------------------------------------------
-- 显示信息
-------------------------------------------------------------------------------
function UserPopGold:initInfo(user_item)
    self.m_target_item = user_item
    local is_self = user_item.dwUserID == GlobalUserItem.dwUserID
    local bg = self.main_node:child('bg')

    -- 头像
    local panel = bg:child('panel_avator')
    panel:removeAllChildren()
    local size = panel:size()
	local head = HeadSprite:createNormal(user_item, size.width)
    head:pos(size.width/2, size.height/2):addTo(panel)

    bg:child('sp_sex'):texture( user_item.cbGender == 0 and 'common/icon_male.png' or 'common/icon_female.png' )
    bg:child('label_name'):setString( user_item.szNickName )
    bg:child('label_gold'):setString( helper.str.makeFormatNum(user_item.nTableScore, 1) )

    -- 动作按钮
    for i = 1, 4 do
        local btn = bg:child('act_' .. i)
        if btn then
            btn:setCascadeColorEnabled(true)
            if not is_self then
                btn.act_index = i
                btn:addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnAct) )
            else
                btn:setColor( cc.c3b(120, 120, 120) )
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 动作点击
-------------------------------------------------------------------------------
function UserPopGold:onBtnAct(sender)
    if os.time() - last_act_time < ACT_INTERNAL then
        helper.pop.message( LANG.ROOM_ACT_TIP )
        return
    end
    last_act_time = os.time()
    cs.app.room_frame:SendAction( self.m_target_item.dwUserID, sender.act_index )
    self:removeFromParent()
end


return UserPopGold

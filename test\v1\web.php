<?php
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Config.php');

class Web extends CI_Controller
{

    //游戏ID
    private $_game_id;

    //角色ID
    private $_role_id;

    //角色信息
    private $_role;

    private $_channel;

    private $_game;


    public function __construct()
    {
        header('Access-Control-Allow-Origin:*');

        parent::__construct();
        $this->load->model('server_model');
        $this->load->model('player_model');

        $key = 'Q7819FNAWERO329Y';//key

        $data = $this->input->post('init_data');//获取数据

        if (empty($data)) {
            show_response(0, '访问失败,请关闭网页重新尝试');
        }

        $role_id = empty($data[0]) ? 0 : $data[0];
        $channel_id = empty($data[1]) ? 0 : $data[1];
        $sign = empty($data[2]) ? '' : $data[2];

        $md5 = md5($channel_id . '-' . $role_id . '-' . $key);//md5验证加密
        $md5 = strtoupper($md5);//将加密后的字符串转换为大写

        if ($md5 != $sign) {//判断是否是游戏客服端发出的
            show_response(0, '签名失败');
        }

        $channel_info = $this->server_model->get_one_channel($channel_id);

        if (empty($channel_info) && empty($channel_info['game_id'])) {
            show_response(0, '渠道不存在');
        }

        $this->_channel = $channel_info;

        $this->_role_id = $role_id;
        $this->_game_id = $channel_info['game_id'];

        $game = $this->server_model->get_game_by_id($channel_info['game_id']);
        $this->_game = $game;

        if (empty($this->_game_id)) {
            show_response(0, '游戏ID不能为空');
        }

        $this->player_model->set_database($this->_game_id);

        if (empty($this->_role_id)) {
            show_response(0, '被推荐人不能为空');
        }

        $role = $this->player_model->get_role_info($this->_role_id);

        if (empty($role)) {
            show_response(0, '被推荐人不存在');
        }

        $this->_role = $role;
    }

    public function index()
    {
        //header('Access-Control-Allow-Origin:*');

        //今日数据
        $bind_num = $this->server_model->get_today_bind_role($this->_role_id);//新增绑定

        $this->_role['new_bind'] = empty(count($bind_num)) ? 0 : count($bind_num);//绑定玩家数

        $url = base_url() . 'down/invite?game_id=' . $this->_game_id . '&role_id=' . $this->_role_id;//二维码地址

        //角色信息
        $role_info = $this->player_model->get_role_score2($this->_role_id);

        $this->_role['spreader_num'] = $this->player_model->get_role_spreader_num($this->_role_id);//推荐玩家数
        $this->_role['ticket_num'] = $role_info['Ticket'];//红包券数量
        $this->_role['role_balance'] = $role_info['RedPacket'];//角色余额
        $data['role_info'] = $this->_role;
        $data['url'] = $url;

        show_response(1, '', $data);
    }

    //绑定推荐码
    public function bind()
    {
        //header('Access-Control-Allow-Origin:*');

        $SpreaderID = trim($this->input->post('SpreaderID'));

        if ($this->_role_id == $SpreaderID) {
            show_response(0, '推荐人不能为自己');
        }

        if (!empty($this->_role['SpreaderID'])) {
            show_response(0, '已绑定,请勿重复绑定');
        }

        if (empty($SpreaderID)) {
            show_response(0, '推荐码不能为空');
        }

        $referrer = $this->player_model->get_role_info($SpreaderID);

        if (empty($referrer)) {
            show_response(0, '推荐人不存在');
        }

        //判断传入玩家是否是被绑定玩家的下级
//        $judge = $this->_judge_superior($this->_role_id,$SpreaderID);
//
//        if(!$judge){
//            show_response(0,'当前用户为师傅,无法绑定到自己的徒弟下');
//        }

        // 获取
        $spreaders = $this->player_model->get_role_spreader($this->_role_id);

        if ($spreaders) {
            show_response(0, '当前用户为师傅,无法绑定');
        }

        $this->player_model->update_role_info($this->_role_id, array('SpreaderID' => $referrer['UserID']));

        $role = $this->player_model->get_role_info($this->_role_id);

        $data = array(
            'role_id' => $role['UserID'],
            'game_id' => $this->_game_id,
            'agent_id' => $referrer['UserID'],
            'is_play' => $role['PlayTimeCount'] > 0 ? 1 : 0,
            'bind_time' => time()
        );

        $this->db->insert('role_bind', $data);

//        $this->player_model->insert_redPacket($this->_role_id,1,3,'绑定奖励');
//        $this->player_model->insert_redPacket($SpreaderID,2,3,'绑定奖励');

        show_response(1, '绑定成功', $role);
    }

    //红包收益
    public function bonus()
    {
        //header('Access-Control-Allow-Origin:*');

        $role_info = $this->player_model->get_role_score2($this->_role_id);

        $log = $this->player_model->get_redPacket_logs(array('UserID' => $this->_role_id));

        $logs = $this->_deal_redPacket_logs($log);

        $data['logs'] = $logs;
        $data['role_info'] = $role_info;

        show_response(1, '', $data);
    }

    //查询收益
    public function search_bonus()
    {
        //header('Access-Control-Allow-Origin:*');

        $where = [];
        $begin_time = '';
        $end_time = '';
        if (!empty($this->input->post('begin_time'))) {
            $begin_time = $this->input->post('begin_time');
        }
        if (!empty($this->input->post('end_time'))) {
            $end_time = $this->input->post('end_time');
        }
        if (!empty($this->input->post('option'))) {
            $where['Type'] = $this->input->post('option');
        }
        if (!empty($begin_time) && !empty($end_time)) {
            if (strtotime($begin_time) > strtotime($end_time)) {
                show_response(0, '结束时间必须大于等于开始时间');
            }
        }

        $where['UserID'] = empty($this->input->post('search_id')) ? $this->_role_id : $this->input->post('search_id');

        $log = $this->player_model->get_redPacket_logs($where, null, $begin_time, $end_time);

        $logs = $this->_deal_redPacket_logs($log);

        show_response(1, '', $logs);
    }

    //红包券
    public function ticket()
    {
        //header('Access-Control-Allow-Origin:*');

        $role_info = $this->player_model->get_role_score2($this->_role_id);

        $log = $this->player_model->get_ticket_logs(array('UserID' => $this->_role_id));

        $logs = $this->_deal_Ticket_logs($log);

        $data['logs'] = $logs;
        $data['role_info'] = $role_info;

        show_response(1, '', $data);
    }

    //查询红包券记录
    public function search_ticket()
    {
        //header('Access-Control-Allow-Origin:*');

        $where = [];
        $begin_time = '';
        $end_time = '';
        if (!empty($this->input->post('begin_time'))) {
            $begin_time = $this->input->post('begin_time');
        }
        if (!empty($this->input->post('end_time'))) {
            $end_time = $this->input->post('end_time');
        }
        if (!empty($this->input->post('option'))) {
            $where['Type'] = $this->input->post('option');
        }
        if (!empty($begin_time) && !empty($end_time)) {
            if (strtotime($begin_time) > strtotime($end_time)) {
                show_response(0, '结束时间必须大于等于开始时间');
            }
        }

        $where['UserID'] = empty($this->input->post('search_id')) ? $this->_role_id : $this->input->post('search_id');

        $log = $this->player_model->get_ticket_logs($where, null, $begin_time, $end_time);

        $logs = $this->_deal_Ticket_logs($log);

        show_response(1, '', $logs);
    }

    //徒弟列表
    public function apprentice()
    {
        //header('Access-Control-Allow-Origin:*');

        $data['bind_role'] = $this->player_model->get_role_spreader($this->_role_id);//获取徒弟列表
        foreach ($data['bind_role'] as &$value) {
            $bind_info = $this->server_model->get_new_bind_time($value['UserID']);//获取绑定时间
            $value['bind_time'] = empty($bind_info['bind_time']) ? 0 : date('Y-m-d', $bind_info['bind_time']);
        }
        unset($value);
        $data['bind_count'] = empty(count($data['bind_role'])) ? 0 : count($data['bind_role']);//获取绑定数

        show_response(1, '', $data);
    }

    //查询徒弟
    public function search_apprentice()
    {
        //header('Access-Control-Allow-Origin:*');

        $where['UserID'] = trim($this->input->post('id'));
        $where['SpreaderID'] = $this->_role_id;

        if (empty($where['UserID']) && empty($where['SpreaderID'])) {
            show_response(0, 'ID不能为空 查询失败');
        }

        $bind_role = $this->player_model->get_role_info_by_where($where);

        foreach ($bind_role as &$value) {
            $bind_info = $this->server_model->get_new_bind_time($value['UserID']);
            $value['bind_time'] = empty($bind_role['bind_time']) ? 0 : date('Y-m-d', $bind_info['bind_time']);
        }
        unset($value);

        show_response(1, '', $bind_role);
    }

    //新增徒弟
    public function new_apprentice()
    {
        //header('Access-Control-Allow-Origin:*');

        $bind_role = $this->server_model->get_today_bind_role($this->_role_id);

        if (!empty($bind_role)) {
            foreach ($bind_role as $k => $value) {
                //$role_info = $this->player_model->get_role_info($value['role_id']);
                //$bind_role[$k]['role_name'] = $role_info['NickName'];
                $bind_role[$k]['bind_time'] = date('Y-m-d H:i:s', $value['bind_time']);
            }
        }

        show_response(1, '', $bind_role);
    }

    public function apprentice_ticket()
    {

        $role_info = $this->player_model->get_role_score2($this->_role_id);

        $log = $this->player_model->get_ticket_logs(array('UserID' => $this->_role_id, 'Type' => 3));

        $logs = $this->_deal_Ticket_logs($log);

        $data['logs'] = $logs;
        $data['role_info'] = $role_info;

        show_response(1, '', $data);
    }

    //签到
    public function sign()
    {
        //header('Access-Control-Allow-Origin:*');

        $logs = $this->player_model->get_ticket_logs(array('UserID' => $this->_role_id, 'Type' => 2), date('Y-m-d'));

        if (!empty($logs)) {
            show_response(0, '请勿重复签到');
        }

        $this->player_model->insert_Ticket($this->_role_id, 10, 2, '每日签到');

        show_response(0, '签到成功');
    }

    //提现
    public function withdraw()
    {
        //header('Access-Control-Allow-Origin:*');

//        show_response(0,'提现功能暂未开放,敬请期待');

        $money = intval($this->input->post('money'));

        // 判断今天是否已经提现
        $this->db->where('role_id', $this->_role_id);
        $this->db->where('game_id', $this->_game_id);
        $this->db->where('status', 1);
        $this->db->where('FROM_UNIXTIME(create_time,"%Y-%m-%d")', date('Y-m-d'));
        $query = $this->db->get('withdraw');
        $result = $query->row_array();

        if ($result) {
            show_response(1, '今日已提现');
        }

        if (!is_int($money)) {
            show_response(1, '提现金额必须为整数');
        }

        if ($money < 10) {
            show_response(1, '提现金额必须大于等于10元');
        }

        if ($money > 20000) {
            show_response(1, '提现金额必须小于20000元');
        }

        $role_score = $this->player_model->get_role_score2($this->_role_id);

        if ($money > $role_score['RedPacket']) {
            show_response(1, '提现金额必须小于当前余额');
        }

        // 获取该代理对应的微信用户
        $this->db->where('role_id', $this->_role_id);
        $this->db->where('game_id', $this->_game_id);
        $query = $this->db->get('mp_user');
        $mp_user = $query->row_array();

        if (empty($mp_user) || empty($mp_user['openid'])) {
            show_response(1, '未在公众号内注册过');
        }

        $this->load->model('mp_model');
        $mp_config = $this->mp_model->get_mp_config($this->_game_id);
        WxPayConfig::setConfig($mp_config);

        $this->load->helper('string');
        $order_no = date("YmdHis") . random_string('nozero', 3);

        $Amount = $money * 100 * 1;
        //if(in_array($this->_game['game_id'],array(32,37))){
        //$Amount = $money * 100 * 0.94;
        //}else{
        //$Amount = $money * 100 * 1;
        //}

        // 创建提现订单
        $data = array(
            'role_id' => $this->_role['UserID'],
            'role_name' => $this->_role['NickName'],
            'order_no' => $order_no,
            'total_fee' => $money,
            'create_time' => time(),
            'district_id' => $this->_role['CommonKindID']
        );

        $this->db->insert('withdraw', $data);

        $id = $this->db->insert_id();

        if ($id > 0) {
            $input = new WxPayTransfer();
            $input->SetAppid($mp_config['appid']);
            $input->SetPartnerTradeNo($order_no);
            $input->SetOpenid($mp_user['openid']);
            $input->SetAmount($Amount);
            $input->SetCheckName("NO_CHECK");
            $input->SetReUserName("");
            $input->SetDesc("红包提现");
            $order = WxPayApi::transferOrder($input);

            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS') {
                // 修改状态
                $this->db->where('id', $id);
                $this->db->update('withdraw', array('status' => 1, 'out_trade_no' => $order['payment_no']));

                $this->player_model->insert_redPacket($this->_role['UserID'], $money, 1, '提现');

                show_response(0, '提现成功');

            } else {
                // 修改状态
                $this->db->where('id', $id);
                $this->db->update('withdraw', array('status' => -1));

                log_message("error", '提现日志');
                log_message("error", var_export($order, TRUE));

                show_response(1, ' 提现失败：' . $order['err_code_des']);
            }
        } else {
            show_response(1, '插入记录失败');
        }
    }

    //防止玩家绑定到自己下级上面
    public function _judge_superior($children, $parent)
    {
        for ($i = 1; $i < 100; $i++) {
            $role = $this->player_model->get_children_role($children, $i);

            if ($role) {
                foreach ($role as $child) {
                    if ($child['UserID'] == $parent) {
                        return false;
                    } else {
                        continue;
                    }
                }
            } else {
                return true;
            }
        }
        return false;
    }

    //处理红包日志
    public function _deal_redPacket_logs($logs)
    {
        if (!empty($logs)) {
            foreach ($logs as &$value) {
                switch ($value['Type']) {
                    case 1:
                        $value['Type'] = '注册';
                        break;
                    case 2:
                        $value['Type'] = '邀请';
                        break;
                    case 3:
                        $value['Type'] = '绑定';
                        break;
                }
                $value['LogTime'] = date('Y-m-d', strtotime($value['LogTime']));
            }
            unset($value);

            return $logs;
        }
        return array();
    }

    //处理红包券日志
    public function _deal_Ticket_logs($logs)
    {
        if (!empty($logs)) {
            foreach ($logs as &$value) {
                switch ($value['Type']) {
                    case 1:
                        $value['Type'] = '新手任务';
                        break;
                    case 2:
                        $value['Type'] = '每日任务';
                        break;
                    case 3:
                        $value['Type'] = '徒弟贡献';
                        break;
                    case 4:
                        $value['Type'] = '邀请绑定';
                        break;
                }
                $value['LogTime'] = date('Y-m-d', strtotime($value['LogTime']));
            }
            unset($value);

            return $logs;
        }
        return array();
    }

    public function task()
    {
        // 获取
        $count1 = $this->player_model->get_role_gold_game_count($this->_role_id, date('Y-m-d'), 1);
        $count2 = $this->player_model->get_role_gold_game_count($this->_role_id, date('Y-m-d'), 2);
        $count3 = $this->player_model->get_role_gold_game_count($this->_role_id, date('Y-m-d'), 3);
        $count4 = $this->player_model->get_role_gold_game_count($this->_role_id, date('Y-m-d'), 4);

        $data = array(
            'game_count_1' => $count1,
            'game_count_2' => $count2,
            'game_count_3' => $count3,
            'game_count_4' => $count4,
        );

        show_response(1, '', $data);

    }

    public function reward_task()
    {

        $this->load->model('activity_model');

        // 获取该渠道下的活动配置
        $templates = [];

        $where = "FIND_IN_SET('" . $this->_channel['channel_id'] . "', channels)";
        $this->db->where($where);
        $this->db->where('begin_time <=', date('Y-m-d H:i:s'));
        $this->db->where('end_time >=', date('Y-m-d H:i:s'));

        $query = $this->db->get('h5_activities');
        $activity = $query->row_array();

//        echo $this->db->last_query();

        $types = array(
            'BIG_WIN_COUNT' => '大赢家局数',
            'WIN_COUNT' => '胜利局数',
            'SCORE_COST_COUNT' => '房卡消耗数',
            'PLAY_COUNT' => '游戏场次',
            'TOTAL_PAY' => '累计充值金额'
        );

        if ($activity) {
            $template_ids = explode(",", $activity['templates']);

            foreach ($template_ids as $k => $template_id) {
                $template = $this->activity_model->get_h5_template_by_id($template_id);

                if ($template) {
                    $templates[$k]['activity_id'] = $activity['id'];
                    $templates[$k]['template_id'] = $template['id'];

                    $templates[$k]['type'] = $template['type'];
                    $templates[$k]['type_name'] = $template['name'];
                    $templates[$k]['target_time'] = $template['target_time'];
                    $templates[$k]['target_num'] = $template['target_num'];

                    $templates[$k]['reward_item_id'] = $template['reward_item_id'];
                    $templates[$k]['reward_item_num'] = $template['reward_item_num'];

                    $item = $this->server_model->get_one_items($template['reward_item_id']);

                    if($template['reward_item_id'] == 9999) {
                        $templates[$k]['reward_item_name'] = '房卡 x '.$template['reward_item_num'];
                    } else if ($item) {
                        $templates[$k]['reward_item_name'] = $item['Name'].' x '.$template['reward_item_num'];
                        $templates[$k]['reward_item_icon'] = $item['Icon'];
                    }

                   $current_num = $this->_get_current_num($template,$activity);

                    $templates[$k]['current_num'] = $current_num;

                    if($current_num<$template['target_num']) {
                        $templates[$k]['receive_status'] = 0;
                    } else {
                        if($this->_check_receive($template,$activity)) {
                            $templates[$k]['receive_status'] = 2;
                        } else {
                            $templates[$k]['receive_status'] = 1;
                        }
                    }
                }
            }
        }

        show_response(1, '', compact('activity','templates'));
    }

    private function _get_current_num($template,$activity)
    {
        $current_num = 0;

        if ($template['target_time'] == 'SINGLE_DAY') {
            $begin_time = strtotime(date('Y-m-d'));
            $end_time = time();
        } else {
            $begin_time = strtotime($activity['begin_time']);
            $end_time = strtotime($activity['end_time']);
        }

        switch ($template['type']) {
            case 'BIG_WIN_COUNT':
                $current_num = $this->player_model->get_role_big_win_nums($this->_role['UserID'], $begin_time, $end_time);
                break;
            case 'WIN_COUNT':
                $current_num = $this->player_model->get_role_win_nums($this->_role['UserID'], $begin_time, $end_time);
                break;
            case 'SCORE_COST_COUNT':
                $current_num = $this->player_model->get_role_cost_count($this->_role['UserID'], $begin_time, $end_time);
                break;
            case 'PLAY_COUNT':
                $current_num = $this->player_model->get_role_game_nums($this->_role['UserID'], $begin_time, $end_time);
                break;
            case 'TOTAL_PAY':
                $current_num = $this->player_model->get_role_recharge_cost($this->_role['UserID'],$this->_channel['game_id'], $begin_time, $end_time);
                break;
            default:
                break;
        }

        return $current_num;
    }

    private function _check_receive($template,$activity)
    {
        if($template['target_time'] == 'SINGLE_DAY') {
            $this->db->where('role_id',$this->_role['UserID']);
            $this->db->where('game_id',$this->_game_id);
            $this->db->where('activity_id',$activity['id']);
            $this->db->where('template_id',$template['id']);
            $this->db->where('FROM_UNIXTIME(receive_time,"%Y-%m-%d")',date('Y-m-d'));

            $query = $this->db->get('h5_reward_logs');

            return $query->num_rows()>0 ;
        } else {
            $this->db->where('role_id',$this->_role['UserID']);
            $this->db->where('game_id',$this->_game_id);
            $this->db->where('activity_id',$activity['id']);
            $this->db->where('template_id',$template['id']);
            $this->db->where('FROM_UNIXTIME(receive_time,"%Y-%m-%d")',date('Y-m-d'));

            $query = $this->db->get('h5_reward_logs');

            return $query->num_rows()>0 ;
        }
    }

    public function reward_receive() {
        $activity_id = $this->input->post('activity_id');
        $template_id = $this->input->post('template_id');

        $this->load->model('activity_model');

        $activity = $this->activity_model->get_h5_activity_by_id($activity_id);

        if($activity) {

            if($activity['begin_time']>date('Y-m-d H:i:s')) {
                show_response(0, '活动未开始');
            }

            if($activity['begin_time']>date('Y-m-d H:i:s')) {
                show_response(0, '活动已结束');
            }

            // 获取模版ID
            $template = $this->activity_model->get_h5_template_by_id($template_id);

            if($template) {

                // 判断是否可以领取
                $current_num = $this->_get_current_num($template,$activity);

                if($current_num < $template['target_num']) {
                    show_response(0, '未满足领取条件');
                }

                if($this->_check_receive($template,$activity)) {
                     show_response(0, '奖励已领取');
                }

                // 发放奖励

                if($template['reward_item_id'] == 9999) {
                    $this->player_model->update_role_score($this->_role['UserID'], $template['reward_item_num']);
                } else {
                    $this->player_model->proc_role_item($this->_role['UserID'], $template['reward_item_id'], $template['reward_item_num'], 'H5');
                }

                $item_name = '';

                if($template['reward_item_id'] == 9999) {
                    $item_name = '房卡';
                } else {
                   $item = $this->server_model->get_one_items($template['reward_item_id']);
                    if($item) {
                        $item_name = $item['Name'];
                    }
                }

                $data = array(
                    'activity_id'=>$activity_id,
                    'template_id'=>$template_id,
                    'role_id'=>$this->_role['UserID'],
                    'role_name'=>$this->_role['NickName'],
                    'game_id'=>$this->_game_id,
                    'game_name'=>$this->_game['game_name'],
                    'item_id'=>$template['reward_item_id'],
                    'item_name'=>$item_name,
                    'item_num'=>$template['reward_item_num'],
                    'receive_time'=>time()
                );

                $this->db->insert('h5_reward_logs',$data);

                show_response(0, '领取成功');

            } else {
                show_response(0, '模版不存在');
            }
        } else {
            show_response(0, '活动不存在');
        }


    }
}
-------------------------------------------------------------------------------
--  创世版1.0
--  房间创建
--      父类：RoomCreateBase
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local RoomCreateBase = cs.app.client('system.RoomCreateBase')
local RoomCreateLayer = class('RoomCreateLayer', RoomCreateBase)


-------------------------------------------------------------------------------
-- 构造方法
--  create_params: 初始参数，是否用于创建玩法（只是生成房间参数，不直接创建并进入房间）
--  save_callback：参数模式下的保存回调
-------------------------------------------------------------------------------
function RoomCreateLayer:ctor(kind, create_params, save_callback)
    self.super.ctor(self)
    self.m_init_kind = kind
    self.m_is_create_kind = create_params and true or false
    self.m_create_params = create_params
    self.m_save_callback = save_callback

    if self.m_create_params and self.m_create_params.kind then
        self.m_init_kind = self.m_create_params.kind
    end

    -- 如果初始kind，创建模式下取本地记录，除非从未弹出过上拉菜单
    if not self.m_init_kind then
        local has_drop = cc.UserDefault:getInstance():getStringForKey('HasDropKindList', '0') == '1'
        if has_drop then
            local local_kind = cc.UserDefault:getInstance():getStringForKey('GameKind', '')
            if local_kind ~= '' then
                self.m_init_kind = tonumber(local_kind)
            end
        else
            cc.UserDefault:getInstance():setStringForKey('HasDropKindList', '1')
        end
    end

    -- 绑定视图
    PassRoom:getInstance():setViewFrame(self)

    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomCreateLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self)

    if self.m_is_create_kind then
        main_node:child('sp_icon'):hide()
        main_node:child('btn_pop_kinds'):show():addTouchEventListener( helper.app.commClickHandler(self, self.onBtnToggleKind) )
    else
        main_node:child('btn_pop_kinds'):hide()
    end

    local panel_content = main_node:child('panel_content'):hide()
    self.panel_content = panel_content
    if main_node:child('group_template') then
        main_node:child('group_template'):hide()
    end
    if main_node:child('tab_template') then
        main_node:child('tab_template'):hide()
    end
    panel_content:child('radio_template'):hide()
    panel_content:child('check_template'):hide()
    panel_content:child('line_template'):hide()
    panel_content:child('fen_template'):hide()
    panel_content:child('btn_ok'):addTouchEventListener( handler(self, self.onBtnOk) )

    panel_content:child('panel_continue/check_continue'):addTouchEventListener( handler(self, self.onBtnCanContinue) )
    panel_content:child('panel_continue/text_continue'):addTouchEventListener( handler(self, self.onBtnCanContinue) )
    panel_content:child('panel_continue/text_continue_tip'):addTouchEventListener( handler(self, self.onBtnCanContinue) )

    self.m_panel_wanfa = panel_content:child('panel_wanfa')
    self.m_panel_renshu = panel_content:child('panel_renshu')
    self.m_panel_zhifu = panel_content:child("panel_pay")

    panel_content:child('listview_wanfa'):onScroll( handler(self, self.onWanfaScroll) )

    self.m_player_count_index = 0
    self.m_game_count_index = 0
    self.m_game_pay_index = 0

    local panel_desc = self.main_node:child('panel_desc')
    panel_desc:addTouchEventListener( handler(self, self.onBtnCloseHelp) )
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function RoomCreateLayer:onEnterTransitionFinish()
    print('RoomCreateLayer:onEnterTransitionFinish...')
    if cs.app.CREATE_ROOM_TAB then
        self:createGroupTabs()
    elseif self.m_init_kind then
        local listview = self.main_node:child('listview')
        if listview then listview:hide() end
        local panel_group = self.main_node:child('panel_group')
        if panel_group then panel_group:hide() end

        local kind_cfg = self:getKindConfig(self.m_init_kind)
        self:initKind(kind_cfg)
    elseif self.m_is_create_kind then
        self:onBtnToggleKind(self.main_node:child('btn_pop_kinds'))
    end


    -- 请求私人房配置
    if not self.m_is_create_kind then
        self:perform(function()
            helper.pop.waiting()
            PassRoom:getInstance():getNetFrame():onGetRoomParameter()
        end, 0.3)
    end
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RoomCreateLayer:onExit()
    print('RoomCreateLayer:onExit...')
    if PassRoom:getInstance()._viewFrame == self then
        PassRoom:getInstance():setViewFrame(nil)
    end
end

-------------------------------------------------------------------------------
-- 显示帮助信息 
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnHelp( sender )
    local panel_desc = self.main_node:child('panel_desc')
    self.main_node:child('panel_desc'):show()
    local posx, posy = sender:pos()
    local pos = sender:getParent():convertToWorldSpace( cc.p(posx, posy) )
    panel_desc:child('img_desc'):pos( pos )
    panel_desc:child('img_desc/label_desc'):setText( sender.desc )
end

-------------------------------------------------------------------------------
-- 关闭帮助信息
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnCloseHelp( sender )
    sender:hide()
end


-------------------------------------------------------------------------------
-- 切换玩法
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnToggleKind(sender)
    if not self.m_pop_kinds then
        local values = {}
        for i, v in ipairs(cs.app.plugins) do
            if v.kind then
                table.insert(values, v)
            end
        end
        local onRemove = function()
            self.m_pop_kinds:hide()
        end
        local pos = cc.p(sender:px(), sender:py() + sender:size().height/2)
        self.m_pop_kinds = helper.pop.drop(values, nil, pos,
            handler(self, self.onKindPopClick), onRemove, 'up', self, 'DropListKind.csb'):zorder(100)

        -- 如果只有一种玩法，不弹，直接显示
        if #values == 1 then
            self.m_pop_kinds:hide()
            self:onBtnKind(values[1])
        end
    else
        self.m_pop_kinds:show():showDrop()
    end
end


-------------------------------------------------------------------------------
-- 玩法弹出点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onKindPopClick(kind)
    self:onBtnKind(kind)
end


-------------------------------------------------------------------------------
-- 创建按钮点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnOk(sender, event)
    helper.app.commClickEffect(sender, event)
    if event ~= cc.EventCode.ENDED then return end
    if not self:checkForm() then return end

    if self.m_is_create_kind then
        self:saveToLocal()

        local params = clone(self.m_create_params) or {}
        params.kind        = self.m_now_kind
        params.jushu_index = self.m_game_count_index
        params.jushu       = self.m_game_count_value
        params.renshu      = self.m_player_count_index
        params.pay_type    = self.m_game_pay_index
        params.fangka      = self.m_game_pay_fangka
        params.rule        = bit:_lshift(self.m_play_rule, 2) + 1 -- 加1是将第1位设1
        params.difen       = self.m_difen
        params.room_desc   = helper.logic.makeRoomDesc(self.m_game_cfg, params)
        params.continue    = self.m_can_continue

        xpcall(function() self.m_save_callback(params) end, __G__TRACKBACK__)
        
        self:removeFromParent()
    else
        helper.app.checkRoom(handler(self, self.doCreate), self.m_now_kind)
    end
end


-------------------------------------------------------------------------------
-- 执行创建
-------------------------------------------------------------------------------
function RoomCreateLayer:doCreate()
    if tolua.isnull(self) then return end

    -- 开始创建后禁用创建按钮一段时间
    local btn_ok = self.panel_content:child('btn_ok')
    btn_ok:setTouchEnabled(false)
    btn_ok:perform(function()
        btn_ok:setTouchEnabled(true)
    end, 3)

    PassRoom:getInstance():showPopWait()
    PassRoom:getInstance():getNetFrame():onCreateRoom()
end


-------------------------------------------------------------------------------
-- 获取kind配置
-------------------------------------------------------------------------------
function RoomCreateLayer:getKindConfig(kind)
    for i, v in ipairs(cs.app.plugins) do
        if v.kind and v.kind == kind then
            return v
        end
    end
end


-------------------------------------------------------------------------------
-- 创建分组tab
-------------------------------------------------------------------------------
function RoomCreateLayer:createGroupTabs()
    local init_group = nil

    -- 整理
    local groups = {}
    local names = {}
    for i, v in ipairs(cs.app.plugins) do
        if v.kind then
            local group = v.group
            if not groups[group] then
                groups[group] = {}
                table.insert(names, group)
            end
            table.insert(groups[group], v)

            if self.m_init_kind and self.m_init_kind == v.kind then
                init_group = group
            end
        end
    end
    self.m_groups = groups

    local panel_group = self.main_node:child('panel_group')
    local template = self.main_node:child('group_template')
	local last_group = cc.UserDefault:getInstance():getStringForKey('GameGroup', '')
	local group_first
    for i, group in ipairs(names) do
        local tab = template:clone():show()
        tab.group = 1
        tab.game_group = group
        tab:child('text'):setString(group)
        helper.logic.initImageCheck(tab, false, 'common/btn_tab_top_s.png', 'common/btn_tab_top_n.png', 
            handler(self, self.onBtnGroup))
        tab:pos((2*i-1) * tab:size().width/2 + (i-1) * 10, tab:size().height/2):addTo(panel_group)

        if init_group then
            if init_group == group then
                group_first = item
            end
        else
    	    if last_group ~= '' and group == last_group then
			    group_first = item
            elseif not group_first then
                group_first = tab
            end
        end
    end

    template:removeFromParent()

    if group_first then
        group_first:toggle()
        self:onBtnGroup(group_first)
    end
end


-------------------------------------------------------------------------------
-- 按配置创建界面
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnGroup(sender)
    local kinds = self.m_groups[sender.game_group]

    self:createKindList(kinds)
end


-------------------------------------------------------------------------------
-- 游戏列表生成
-------------------------------------------------------------------------------
function RoomCreateLayer:createKindList(kinds)
    local listview = self.main_node:child('listview')
    listview:removeAllItems()

    local template = self.main_node:child('tab_template')
    for i, kind in ipairs(kinds) do
        local tab = template:clone():show()
        tab.kind = kind
        tab.group = 1
        tab:child('text'):setString(kind.name)
        helper.layout.scaleToWidth(tab:child('text'), template:size().width * 0.7)
        helper.logic.initImageCheck(tab, false, 'common/btn_tab_s.png', 'common/btn_tab_n.png', 
            handler(self, self.onBtnKind))
        listview:pushBackCustomItem(tab)
    end
	local kind_first = listview:getItem(0)
	local last_kind = tostring(self.m_init_kind) or cc.UserDefault:getInstance():getStringForKey('GameKind', '')
    self.m_init_kind = nil
	if last_kind ~= '' then
		for i, item in ipairs( listview:getItems() ) do
			if tostring(item.kind.kind) == last_kind then
				kind_first = item
                helper.layout.scrollToItem(listview, kind_first)
				break
			end
		end
	end

    kind_first:toggle()
    self:onBtnKind(kind_first)
end


-------------------------------------------------------------------------------
-- 游戏类型点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnKind(sender)
    local kind = type(sender) == 'table' and sender or sender.kind
    helper.app.checkGameUpdate(kind.game, kind.kind, function()
        self:initKind(kind)
    end)
end


-------------------------------------------------------------------------------
-- 网络就绪，开始创建
-------------------------------------------------------------------------------
function RoomCreateLayer:onLoginPriRoomFinish()
    local meUser = PassRoom:getInstance():getMeUserItem()
    if nil == meUser then
        return false
    end
    -- 发送创建桌子
    if ((meUser.cbUserStatus == yl.US_FREE or meUser.cbUserStatus == yl.US_NULL or meUser.cbUserStatus == yl.US_PLAYING)) then
        if PassRoom:getInstance().m_nLoginAction == PassRoom.L_ACTION.ACT_CREATEROOM then
            local cmd_private = cs.app.client('header.CMD_Private')

            -- 创建登陆
            local buffer = ExternalFun.create_netdata(cmd_private.game.CMD_GR_CreateTable)
            buffer:setcmdinfo(cmd_private.game.MDM_GR_PERSONAL_TABLE, cmd_private.game.SUB_GR_CREATE_TABLE)
            buffer:pushscore(self.m_difen)
            buffer:pushbyte(self.m_game_count_index)
            buffer:pushword(self.m_player_count_index)
            buffer:pushdword(0)
            buffer:pushstring("", yl.LEN_PASSWORD)
            buffer:pushbyte( self.m_play_rule > 0 and 1 or 0 )
            if GlobalUserItem.nCurGameKind == 300 then
                buffer:pushbyte(6)
            else
                buffer:pushbyte(0)
            end
            --单个游戏规则(额外规则)
            for i = 1, 100 - 2 do   -- todo: for test, tobe "- 1"
                if i <= 32 then
                    buffer:pushbyte( bit:_and(self.m_play_rule, bit:_lshift(1, i-1)) > 0 and 1 or 0)
                else
                    buffer:pushbyte(0)
                end
            end
            buffer:pushbyte(self.m_game_pay_index)  -- todo: 按用户选择
            buffer:pushbyte(self.m_can_continue)
            PassRoom:getInstance():getNetFrame():sendGameServerMsg(buffer)
            return true
        end
    end
    return false
end


-------------------------------------------------------------------------------
-- 保存配置
-------------------------------------------------------------------------------
function RoomCreateLayer:saveToLocal()
    local kind = self.m_now_kind
	cc.UserDefault:getInstance():setIntegerForKey('CreateGameCount' .. kind, self.m_game_count_index)
	cc.UserDefault:getInstance():setIntegerForKey('CreatePlayerCount' .. kind, self.m_player_count_index)
	cc.UserDefault:getInstance():setIntegerForKey('CreateBaseScore' .. kind, self.m_difen)
    cc.UserDefault:getInstance():setIntegerForKey('CreateGamePay' .. kind, self.m_game_pay_index)
    cc.UserDefault:getInstance():setStringForKey('CreateGameRule' .. kind, self.m_play_rule)
    cc.UserDefault:getInstance():setStringForKey('CreateCanContinue' .. kind, self.m_can_continue)
    cc.UserDefault:getInstance():setStringForKey('GameKind', kind)
end


-------------------------------------------------------------------------------
-- 创建成功
-------------------------------------------------------------------------------
function RoomCreateLayer:onRoomCreateSuccess()
	self:saveToLocal()

    PassRoom:getInstance():setViewFrame(nil)

    if 0 == PassRoom:getInstance().m_tabRoomOption.cbIsJoinGame then
        -- 非必须加入
        --PassRoom:getInstance():getTagLayer(PassRoom.LAYTAG.LAYER_CREATERESULT, nil, self._scene)
        PassRoom:getInstance():showPopWait()
        PassRoom:getInstance():getNetFrame():onSearchRoom(PassRoom:getInstance().m_tabPriData.szServerID, yl.SEARCH_ROOM_TYPE_CREATE)
    end
end


-------------------------------------------------------------------------------
-- 设置局数
-------------------------------------------------------------------------------
function RoomCreateLayer:setGameCountIndex(index)
	local panel = self.panel_content:child('panel_jushu')
    for i, child in ipairs( panel:getChildren() ) do
		local check = child:child('checkbox')
        check:setSelected( index == check.index )
        if index == check.index then
            self.m_game_count_value = check.jushu
        end
	end
	self.m_game_count_index = index
    -- self:updatePayInfo()
    self:modifyRenshuByJuShu()
end


-------------------------------------------------------------------------------
-- 设置人数
-------------------------------------------------------------------------------
function RoomCreateLayer:setPlayerCountIndex(index)
	local panel = self.panel_content:child('panel_renshu')
    for i, child in ipairs( panel:getChildren() ) do
		local check = child:child('checkbox')
        check:setSelected( index == check.index )
	end
	self.m_player_count_index = index
    self:updatePayInfo()
    self:modifyWanfaByRenshu(index) -- index是人数
end


-------------------------------------------------------------------------------
-- 设置是否可续打点击
-------------------------------------------------------------------------------
function RoomCreateLayer:setCanContinue(can_continue)
	local check = self.panel_content:child('panel_continue/check_continue')
    check:setSelected( can_continue == 1 )
    self.m_can_continue = can_continue
end


-------------------------------------------------------------------------------
-- 是否可续打点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnCanContinue(sender, event)
    if event == cc.EventCode.MOVED then return end

    local check = self.panel_content:child('panel_continue/check_continue')
    helper.app.commClickEffect(check, event)

    if sender ~= check and event == cc.EventCode.ENDED then -- 点中文字上
        check:setSelected(not check:isSelected())
    end

	self.m_can_continue = check:isSelected() and 1 or 0
end


-------------------------------------------------------------------------------
-- 局数点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnJushu(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)
	self:setGameCountIndex(sender.index)
end


-------------------------------------------------------------------------------
-- 支付点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnPay(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)
    self:setGamePayIndex(sender.index)
end


-------------------------------------------------------------------------------
-- 设置支付
-------------------------------------------------------------------------------
function RoomCreateLayer:setGamePayIndex(index)
    local panel = self.panel_content:child('panel_pay')
    for i, child in ipairs( panel:getChildren() ) do
		local check = child:child('checkbox')
        check:setSelected( index == check.index )
        if check:isSelected() then
            self.m_game_pay_fangka = check.fangka
        end
	end
	self.m_game_pay_index = index
end


-------------------------------------------------------------------------------
-- 刷新支付信息
-------------------------------------------------------------------------------
function RoomCreateLayer:updatePayInfo()
    local panel_zhifu = self.m_panel_zhifu
    local children = panel_zhifu:getChildren()
    local fanka_index = self.m_game_count_index + 1
    local totalPlayer = self.m_player_count_index
    local totalFanka = self.totalPay[fanka_index]
    local config = self.m_game_cfg[self.m_now_kind]

    for i, node in ipairs(children) do
		local check = node:child('checkbox')
        local cost = totalFanka / 4 -- todo: 数量配置
        local pay_type = check.index
        --房主支付
        if pay_type == 0 then
           cost = cost * totalPlayer
        --AA支付 
        elseif pay_type == 1 then
        --2房主不关心参与人数
        elseif pay_type == 2 then
           cost = totalFanka
        --3大赢家
        elseif pay_type == 3 then
           cost = cost * totalPlayer
        --4大赢家不关心参与人数
        elseif pay_type == 4 then 
           cost = totalFanka
        end
        check.fangka = math.ceil(cost)
        local text = node:child('text')
		text:setRich( LANG{'CREATE_ZHIFU_'..pay_type, fangka = check.fangka} )
        
        local text_desc = node:child('text_desc')
        local desc = config['ZHIFU_DESC']
        if desc and desc ~= '' then
           local arr_desc = desc:split(';')
           local desc_str = arr_desc[1]
           local limit_array = arr_desc[2]:split(',')
           local count = #limit_array
		   local isShow = false
           for li = 1, count do
               if tonumber(limit_array[li]) == totalPlayer then
                    isShow = true
               end
           end
           if isShow then
               text_desc:show()
               text_desc:setRich(desc_str)
               text_desc:px(text:px() + text:getContentSize().width)   
           else
               text_desc:hide()
           end
        end
        
        local isCurLock = self.jushu_config[self.m_game_count_index + 1][totalPlayer][pay_type + 1].isPayLock
        if isCurLock then
            node:hide()
        else
            node:show()
        end 
	end
    local isLock = self.jushu_config[self.m_game_count_index + 1][totalPlayer][self.m_game_pay_index + 1].isPayLock
    if isLock then
        for i, node in ipairs(children) do
            local check = node:child('checkbox')
            local pay_type = check.index
            local isCurLock = self.jushu_config[self.m_game_count_index + 1][totalPlayer][pay_type + 1].isPayLock
            if not isCurLock then
                self:setGamePayIndex(pay_type)
                break
            end
        end
    end

    for i, node in ipairs(children) do
        local check = node:child('checkbox')
        if check:isSelected() then
            self.m_game_pay_fangka = check.fangka
        end
    end
end


-------------------------------------------------------------------------------
-- 人数点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnRenshu(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)
	self:setPlayerCountIndex(sender.index)
end


-------------------------------------------------------------------------------
-- 玩法点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnWanfa(sender, event, is_text_click)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)

	local group_count = sender.group_count
	local group = sender.group

	-- 同组的互斥，组内只有一个的可勾选，总共只有一个选项，总是选中
	local panel = self.m_panel_wanfa
	local children = panel:getChildren()
	if group_count > 1 then
		for i, node in ipairs(children) do
			local check = node:child('checkbox')
			local g = check.group
			if g == group and sender ~= check then
				check:setSelected(false)
            end
		end
		sender:setSelected(true)
    else
        -- 复选框时，如果是文本点击，需要手动切换复选框状态
        if is_text_click then
            sender:setSelected( not sender:isSelected() )
        end
	end
	self:makeRuleValue()
end


-------------------------------------------------------------------------------
-- 生成玩法值
-------------------------------------------------------------------------------
function RoomCreateLayer:makeRuleValue()
	self.m_play_rule = 0
	local panel = self.m_panel_wanfa
	local children = panel:getChildren()
	for i, node in ipairs(children) do
		local check = node:child('checkbox')
		if check:isSelected() then
			local digit = check.digit
			local value = check.set_value
			if value == 0 then
				self.m_play_rule = bit:_and(self.m_play_rule, bit:_not(bit:_lshift(1, digit)))
			else
				self.m_play_rule = bit:_or(self.m_play_rule, bit:_lshift(1, digit))
            end
		end
	end
end


-------------------------------------------------------------------------------
-- 检查表单
-------------------------------------------------------------------------------
function RoomCreateLayer:checkForm()
    -- 检查玩法选项（互斥选项有且只能选其一）
    local group_map = {}
    local group_names = {}
	local panel = self.m_panel_wanfa
	local children = panel:getChildren()
	for i, node in ipairs(children) do
		local check = node:child('checkbox')
        if check.group_count > 1 then   -- 多选项
            local group = check.group
            if not group_map[group] then
                group_map[group] = 0
                group_names[group] = {}
            end
    		if check:isSelected() then
                group_map[group] = group_map[group] + 1
            end
            table.insert(group_names[group], node:child('text'):getString())
        end
	end
    for group, v in pairs(group_map) do
        if v ~= 1 then
            local desc = table.concat(group_names[group], ', ')
            helper.pop.message( LANG{'CREATE_WANFA_CHECK', desc=desc} )
            return false
        end
    end
    return true
end


-------------------------------------------------------------------------------
-- 底分点击
-------------------------------------------------------------------------------
function RoomCreateLayer:onBtnDifen(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)

	local panel = self.panel_content:child('panel_difen')
	for i, check in ipairs(panel:getChildren()) do
		check:setSelected( check == sender )
	end
	self.m_difen = sender.score
end


-------------------------------------------------------------------------------
-- 按人数调整玩法
-------------------------------------------------------------------------------
function RoomCreateLayer:modifyWanfaByRenshu(renshu)
    local panel_wanfa = self.m_panel_wanfa
    for i, node in ipairs(panel_wanfa:getChildren()) do
        local check = node:child('checkbox')
        if check.renshu_dict and check.renshu_dict[renshu] then
            check:setSelected( check.renshu_dict[renshu] == 1 )
            check:setTouchEnabled(false)
            node:child('text'):setTouchEnabled(false)
            node:opacity(120)
        else
            check:setTouchEnabled(true)
            node:child('text'):setTouchEnabled(true)
            node:opacity(255)
        end
    end
	self:makeRuleValue()
end

-------------------------------------------------------------------------------
-- 按局数调整人数
-------------------------------------------------------------------------------
function RoomCreateLayer:modifyRenshuByJuShu( )
    local panel_renshu = self.m_panel_renshu
    --dump(panel_renshu.jushu_dict, 'panel_renshu.jushu_dict', 9)
    for i, node in ipairs(panel_renshu:getChildren()) do
        local check = node:child('checkbox')
        local isCurLock = self.jushu_config[self.m_game_count_index + 1][check.index].isRenLock
        -- print('islock', self.m_game_count_index + 1, check.index, isLock)
        if isCurLock then
            node:hide()
        else
            node:show()
        end
    end
    --dump(self.jushu_config, 'self.jushu_config', 9)
    --print(self.m_game_count_index + 1, self.m_player_count_index)
    if self.m_player_count_index <= 0 then
        return
    end
    local cfg = self.jushu_config[self.m_game_count_index + 1][self.m_player_count_index]
    if not cfg then
        return
    end
    local isLock = cfg.isRenLock
    if isLock then
        for i, node in ipairs(panel_renshu:getChildren()) do
            local check = node:child('checkbox')
            local isCurLock = self.jushu_config[self.m_game_count_index + 1][check.index].isRenLock
            if not isCurLock then
                self:setPlayerCountIndex(check.index)
                break
            end
        end
    end
    self:updatePayInfo()
end

-------------------------------------------------------------------------------
-------------------------------------------------------------------------------
function RoomCreateLayer:onWanfaScroll(event)
    if event.name == 'BOUNCE_BOTTOM' and event.target._has_arrow then
        event.target:removeProtectedChildByTag(event.target._arrow_tag, true)
		event.target._has_arrow = nil
    end
end


-------------------------------------------------------------------------------
-- 按配置创建界面
-------------------------------------------------------------------------------
function RoomCreateLayer:initKind(kind_cfg)
    local kind = kind_cfg.kind
    GlobalUserItem.nCurGameKind = kind

    self.main_node:child('sp_icon'):show():texture('common/icon_kind_' .. kind .. '.png')

    self.m_now_kind = kind
    local panel_content = self.panel_content:show()
    local radio_template = panel_content:child('radio_template')
    local check_template = panel_content:child('check_template')
    local line_template = panel_content:child('line_template')

    -- 载入游戏配置
    local game_cfg = cs.app.req(cs.app.GAME_ROOT .. '.' .. kind_cfg.game .. '.src.game')
    self.m_game_cfg = game_cfg
    local config = game_cfg[kind]
    if not config then
        panel_content:hide()
        return
    end
    helper.app.cleanPackages('game.' .. kind_cfg.game .. '.src.')
    helper.app.removeGameSearchPath(kind_cfg.game)
	-- 清理界面 -----------------------------------------
	local panel_renshu = self.m_panel_renshu
	local panel_wanfa = self.m_panel_wanfa
    local panel_zhifu = self.m_panel_zhifu
	local panel_jushu = panel_content:child("panel_jushu")
	local panel_difen = panel_content:child("panel_difen")
	local panel_line = panel_content:child("panel_line")
    local listview_wanfa = panel_content:child('listview_wanfa'):hide()

    panel_jushu:removeAllChildren()
    panel_renshu:removeAllChildren()
    panel_wanfa:removeAllChildren()
    panel_difen:removeAllChildren()
    panel_line:removeAllChildren()
    panel_zhifu:removeAllChildren()

    if panel_wanfa:getParent() ~= panel_jushu:getParent() then
        panel_wanfa:retain()
        listview_wanfa:removeAllItems()
        panel_wanfa:pos(panel_jushu:pos()):size(panel_jushu:size()):addTo(panel_jushu:getParent())
        panel_wanfa:release()
    end

    if listview_wanfa._has_arrow then
        listview_wanfa:removeProtectedChildByTag(listview_wanfa._arrow_tag, true)
		listview_wanfa._has_arrow = nil
    end

    local line_top = 33
    local line_btm = 33
    local v_gap = 46
    local h_gap = 340
    local offset_y = 384

    local addLine = function()
	    offset_y = offset_y - line_top
	    local line = line_template:clone():show()
	    line:pos(414, offset_y):addTo(panel_line)
	    offset_y = offset_y - line_btm
    end
    
	-- 生成局数 ------------------------------------------------
	self.totalPay = {}
    panel_content:child('panel_label/label_jushu'):py(offset_y)
    local arr_jushu = config.JUSHU:split(';')
    local jushu_count = #arr_jushu
    local col_gap = ({h_gap, h_gap, h_gap/2, h_gap/2})[jushu_count] or h_gap/2
    local jushu_dict = {}
    self.jushu_config = jushu_dict
	for i = 1, jushu_count do
		local one_arr = arr_jushu[i]:split(',')
		local node = radio_template:clone():show():addTo(panel_jushu)
		node:pos(120 + (i-1) % 4 * col_gap, offset_y)

		local check = node:child('checkbox')
        check.index = i - 1
        check.jushu = one_arr[1]
        check:addTouchEventListener( handler(self, self.onBtnJushu) )

        local text = node:child('text')
		text:setRich( LANG{'CREATE_JUSHU', person=one_arr[1]} )
        text:addTouchEventListener( handler(self, function(a, event) a:onBtnJushu(check, event) end) )
        self.totalPay[i] = one_arr[2]
		if i % 4 == 0 and i < jushu_count then
			offset_y = offset_y - v_gap
        end
        jushu_dict[i] = {} 
	end
	addLine()

	-- 生成人数 ------------------------------------------------
    self.totalPlayer = {}
	panel_content:child('panel_label/label_renshu'):py(offset_y)
    local arr_renshu = config.RENSHU:split(';')
	local renshu_count = #arr_renshu
	local col_gap = renshu_count == 2 and h_gap or h_gap / 2
    
    for i = 1, renshu_count do
		local node = radio_template:clone():show():addTo(panel_renshu)
		node:pos(120 + (i - 1) % renshu_count * col_gap, offset_y)
        local one_arr = arr_renshu[i]:split(',')
        local renshu = tonumber(one_arr[1])
        for j = 1, #jushu_dict do
            local jushu_tab = jushu_dict[j]
            jushu_tab[renshu] = {}
            jushu_tab.isRenLock = false
        end
        
        -- 局数 控制 分析
        if one_arr[2] then
           local jushu_array = one_arr[2]:split('|')
           for j = 1, #jushu_array do
                local jushu = jushu_array[j]
                local tab = jushu_dict[tonumber(jushu)]
                tab[renshu].isRenLock = true
           end
        end

		local check = node:child('checkbox')
        check.index = renshu  -- note: 如果用索引，改成：i - 1
        check:addTouchEventListener( handler(self, self.onBtnRenshu) )

        local text = node:child('text')
		text:setString( LANG{'CREATE_RENSHU', num = renshu} )
        text:addTouchEventListener( handler(self, function(a, event) a:onBtnRenshu(check, event) end) )

        self.totalPlayer[i] = renshu
	end
    panel_renshu.jushu_dict = jushu_dict
    addLine()

    -- 生成玩法 ------------------------------------------------
    local game_rule_default = 0
    if config.WANFA == '' then
        panel_content:child('panel_label/label_wanfa'):hide()
    else
        local is_scroll = config.WANFA_HEIGHT and true or false
        local wanfa_start_y = offset_y
        local wanfa_y = is_scroll and panel_wanfa:size().height - 15 or offset_y
        local wanfa_base_x = is_scroll and 0 or 120

	    panel_content:child('panel_label/label_wanfa'):py(offset_y)
	    
        local arr_wanfa = config.WANFA:split(';')
	    local wanfa_col = config.WANFA_COL
	    local col_gap = wanfa_col == 2 and h_gap or h_gap / 2
	    local row_count = 0
        local wanfa_rows = 1
	    local wanfa_count = #arr_wanfa
	    for i = 1, wanfa_count do
		    local arr_group = arr_wanfa[i]:split('|')
		    local count = #arr_group
		    for gi = 1, count do
			    local arr_one = arr_group[gi]:split(',')
		        local node = (count == 1 and check_template or radio_template):clone():show():addTo(panel_wanfa)
			    local col_num = row_count % wanfa_col
			    node:pos(wanfa_base_x + col_num * col_gap, wanfa_y)
			    row_count = row_count + 1

			    local group = i - 1
			    local digit = tonumber(arr_one[1])
			    local set_value = tonumber(arr_one[2])

		        local check = node:child('checkbox')
			    check.group_count = count
			    check.group = group
			    check.digit = digit
			    check.set_value = set_value
                check:addTouchEventListener( handler(self, self.onBtnWanfa) )

                local renshu_str = arr_one[5]
                if renshu_str then
                    local renshu_dict = {}
                    for i = 1, #renshu_str, 2 do
                        renshu_dict[tonumber(renshu_str:sub(i, i))] = tonumber(renshu_str:sub(i+1, i+1))
                    end
                    check.renshu_dict = renshu_dict
                    node:setCascadeOpacityEnabled(true)
                end

            local rule_key = 'RULE' .. digit .. (set_value == 1 and '' or '_NONE')
			local show = config[rule_key]
            local text = node:child('text')
	    	text:setString(show)

            local icon_help = node:child('icon_help')
            rule_key = 'RULE' .. digit .. '_DESC'
            local rule_desc = config[rule_key]
            if rule_desc then
                icon_help:show()
                icon_help:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnHelp) )
                icon_help:px(text:px() + text:size().width + icon_help:size().width/2 + 5)
                icon_help.desc = rule_desc
            end

            if not is_scroll then
                text:addTouchEventListener( handler(self, function(a, event) a:onBtnWanfa(check, event, true) end) )
            end

			    if wanfa_col - col_num == 1 or i == wanfa_count then

			    else
				    local width = text:size().width
				    local dis = col_gap - 40
				    if width > dis then
					    text:scale(dis/width)
				    end
			    end

			    if #arr_one > 2 then
				    if arr_one[2] == '1' and arr_one[3] == '1' then
					    game_rule_default = bit:_or(game_rule_default, bit:_lshift(1, digit))
                    end
			    end
			    if (row_count == wanfa_col and (gi < count or i < wanfa_count)) or 
                                                #arr_one > 3 and arr_one[4] == '1' then
				    wanfa_y = wanfa_y - v_gap
				    row_count = 0
                    wanfa_rows = wanfa_rows + 1
			    end
		    end
	    end

        offset_y = wanfa_y

        -- 有玩法滚动框时做些修正
        if is_scroll then
            local pos = panel_wanfa:convertToWorldSpace(cc.p(0, wanfa_start_y - config.WANFA_HEIGHT))
            pos = listview_wanfa:getParent():convertToNodeSpace(pos)
            local height = wanfa_rows * v_gap
            local size = panel_wanfa:size()
            panel_wanfa:size(size.width, height)
            for i, child in ipairs(panel_wanfa:getChildren()) do
                child:py(child:py() + height - size.height)
            end
            panel_wanfa:retain()
            panel_wanfa:removeFromParent()
            listview_wanfa:size(listview_wanfa:size().width, config.WANFA_HEIGHT)
            listview_wanfa:pushBackCustomItem(panel_wanfa)
            listview_wanfa:py(pos.y + v_gap / 2 - 5):show()
            if config.WANFA_HEIGHT < height then
                listview_wanfa._arrow_tag = 1234
                listview_wanfa._has_arrow = true
                local arrow = display.newSprite('common/icon_list_arrow.png')
                arrow:pos(listview_wanfa:size().width/2 - 31, 20)
                listview_wanfa:addProtectedChild(arrow, 9999, listview_wanfa._arrow_tag)
                arrow:runAction( cc.RepeatForever:create( cc.Sequence:create(
                    cc.MoveBy:create(0.3, cc.p(0, -5)),
                    cc.MoveBy:create(0.3, cc.p(0, 5))
                )))
            end
            panel_wanfa:release()
            offset_y = wanfa_start_y - config.WANFA_HEIGHT + v_gap
        end

	    addLine()
    end

    -- 生成支付类型 ------------------------------------------------
	panel_content:child('panel_label/label_pay'):py(offset_y)
    local default_game_pay = nil
    local arr_zhifu = config.ZHIFU:split(';')
    local zhifu_count = #arr_zhifu
    local zhifu_col = 3
    local zhifu_gap = zhifu_col == 2 and h_gap or h_gap / 2
    local zhifu_types = {}
	for i = 1, zhifu_count do
        arr_pay = arr_zhifu[i]:split(',')
		local pay_type = arr_pay[1]
        table.insert(zhifu_types, tonumber(pay_type))
		local node = radio_template:clone():show():addTo(panel_zhifu)
		node:pos(120 + (i-1) % 2 * h_gap, offset_y)
        local index = i - 1
		local check = node:child('checkbox')
        check.index = tonumber( pay_type ) 
        if not default_game_pay then default_game_pay = check.index end -- 第一个值
        check:addTouchEventListener( handler(self, self.onBtnPay) )
        local icon_help = node:child('icon_help')

        local text = node:child('text')
		text:setRich( LANG{'CREATE_ZHIFU_'..pay_type, fangka = 0} )
        text:addTouchEventListener( handler(self, function(a, event) a:onBtnPay(check, event) end) )

        if pay_type == '3' then
            icon_help:show()
            icon_help:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnHelp) )
            icon_help:px(text:px() + text:size().width + icon_help:size().width/2 + 5)
            icon_help.desc = LANG.ZHIFU_3_DESC
        end
		local col_num = math.floor(index/zhifu_col)
        if col_num > 0 and index % zhifu_col == 0 and index < zhifu_count then
			offset_y = offset_y - v_gap
        end

		node:pos(120 + (index % zhifu_col) * zhifu_gap, offset_y )

        for k, v in pairs(jushu_dict) do
            for k1, v1 in pairs(v) do
                if type(k1) == "number" and type(k) == "number" then
                    jushu_dict[k][k1][pay_type + 1] = {}
                    jushu_dict[k][k1][pay_type + 1].isPayLock = false
                end
            end
        end
        -- 人数 控制 分析
        if arr_pay[2] then
           local cfg_array = arr_pay[2]:split('|')
           for j = 1, #cfg_array do
               local jushu_array = cfg_array[j]:split('#')
               local jushu = tonumber(jushu_array[1])
               local renshu_array = jushu_array[2]:split('@')
               for r = 1, #renshu_array do
                    local renshu = tonumber(renshu_array[r])
                    jushu_dict[jushu][renshu][pay_type + 1].isPayLock = true
               end
           end
        end
	end
	
    panel_zhifu.renshu_dict = renshu_dict

    -- 生成底分 ------------------------------------------------
    local fen_template = panel_content:child('fen_template')
    local arr_difen = config.DIFEN:split(';')
	local wanfa_col = config.WANFA_COL
	local col_gap = 100
	for i = 1, #arr_difen do
		local node = fen_template:clone():show():addTo(panel_difen)
		node:pos(166 + (i - 1) * col_gap, 36)
		node.score = tonumber(arr_difen[i])
        node:child('num'):setString(arr_difen[i])
        node:addTouchEventListener( handler(self, self.onBtnDifen) )
	end
	-- 只有一个底分认为不显示
	if #arr_difen == 1 then
        panel_content:child('panel_label/label_difen'):hide()
		panel_difen:hide()
    else
        panel_content:child('panel_label/label_difen'):show()
		panel_difen:show()
	end

	-- 恢复局数、人数、底分设置 ----------------------------------------------------
	local game_count_idx = cc.UserDefault:getInstance():getIntegerForKey('CreateGameCount' .. kind, 0)
    local default_player_num = tonumber(arr_renshu[1]:split(',')[1])
	local player_count_idx = cc.UserDefault:getInstance():getIntegerForKey('CreatePlayerCount' .. kind, default_player_num)  -- note: 若用索引改成 0
	local base_score = cc.UserDefault:getInstance():getIntegerForKey('CreateBaseScore' .. kind, 1)
    local game_pay = cc.UserDefault:getInstance():getIntegerForKey('CreateGamePay' .. kind, default_game_pay or 0)
    if not table.indexof(zhifu_types, game_pay) then
        game_pay = zhifu_types[1]
    end
    
    if self.m_create_params then
        local p = self.m_create_params                                                                                    
        if p.jushu_index then game_count_idx = p.jushu_index end
        if p.renshu then player_count_idx = p.renshu end
        if p.difen then base_score = p.difen end
        if p.pay_type then game_pay = p.pay_type end
    end

	self:setGameCountIndex(game_count_idx)
    self:setGamePayIndex(game_pay)

	local children = panel_difen:getChildren()
	local difen_first = children[1]
	self:onBtnDifen(difen_first)
	for i, check in ipairs(children) do
		if tonumber(check.score) == base_score then
			self:onBtnDifen(check)
		end
	end

	-- 恢复玩法设置 ------------------------------------------------
	self.m_play_rule = tonumber(cc.UserDefault:getInstance():getStringForKey('CreateGameRule' .. kind, 
        tostring(game_rule_default))) -- 不用integer，位数不够
    
    if self.m_create_params then
        local p = self.m_create_params                                                                                    
        if p.rule then
            self.m_play_rule = bit:_rshift(p.rule, 2)
        end
    end

	local rules = panel_wanfa:getChildren()
	for i, node in ipairs(rules) do
		local check = node:child('checkbox')
        local digit = tonumber(check.digit)
        local value = tonumber(check.set_value)
		-- 只有一个选项，或设1且为1，或设0且为0
		if #rules == 1 or value == 1 and bit:_and(self.m_play_rule, bit:_lshift(1, digit)) > 0 or
								value == 0 and bit:_and(self.m_play_rule, bit:_lshift(1, digit)) == 0 then
			check:setSelected(true)
        else
            check:setSelected(false)
        end
	end

	-- 恢复是否可续打 ------------------------------------------------
    if self.m_game_cfg.CAN_CONTINUE then
	    local can_continue = tonumber(cc.UserDefault:getInstance():getStringForKey('CreateCanContinue' .. kind, '1'))
        if m_create_params and m_create_params.continue then
            can_continue = m_create_params.continue
        end
        self:setCanContinue(can_continue)
    else
        self:setCanContinue(0)
        panel_content:child('panel_continue'):hide()
    end

    -- 人数需要在玩法后面
	self:setPlayerCountIndex(player_count_idx)
    self:modifyRenshuByJuShu()
end


return RoomCreateLayer
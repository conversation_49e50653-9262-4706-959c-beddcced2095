-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部导航
--  @date 2018-01-29
--  @auth woodoo
-------------------------------------------------------------------------------
local ClubBrowser = class("ClubBrowser")


local cache_list = {}


-------------------------------------------------------------------------------
-- 清除缓存
-------------------------------------------------------------------------------
function <PERSON><PERSON>rowser.clean()
    cache_list = {}
end


-------------------------------------------------------------------------------
-- 后退
-------------------------------------------------------------------------------
function ClubBrowser.back(this)
    local len = #cache_list
    local node = cache_list[len]
    if len > 0 then
        table.remove(cache_list, len)
    end
    if node and not tolua.isnull(node) then
        node:show()
        if node.onActive then
            node:onActive(this)
        end
    end
    return node
end


-------------------------------------------------------------------------------
-- 打开窗口
-- 后面的参数是复制helper.pop.popLayer
-------------------------------------------------------------------------------
function ClubBrowser.open(this, class_name, parent, layer_params, mask_opacity, mask_closable, no_blur)
    table.insert(cache_list, this)
    this:hide()
    if this.onDeactive then
        this:onDeactive()
    end
    helper.pop.popLayer( cs.app.CLIENT_SRC .. class_name, parent, layer_params, mask_opacity, mask_closable, no_blur )
end


return ClubBrowser

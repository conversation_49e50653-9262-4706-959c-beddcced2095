-------------------------------------------------------------------------------
--  创世版1.0
--  通用下拉列表
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local PopupMask	= cs.app.client('system.PopupMask')


local DropList = class('DropList', PopupMask)


-------------------------------------------------------------------------------
-- 构造方法
--  values：显示项目{{text=...}, {text=...}} or {name=...}, {name=...}} or {text1, text2}
--  size: 下拉框尺寸
--  pos：显示位置，相对于屏幕
--  callback：点击项目回调
--  remove_callback: 可选，移除时触发，nil表示直接移除，否则被接管
--  dir: 下拉方向，可选，默认向下
--  csb: 自定义csb
-------------------------------------------------------------------------------
function DropList:ctor(values, size, pos, callback, remove_callback, dir, csb)
    self.super.ctor(self, 0)
    self:zorder(0)  -- 因为PopupMask的默认zorder是负的
    self.m_callback = callback
    self.m_remove_callback = remove_callback
    self.m_dir = dir or 'down'

    local main_node = helper.app.loadCSB(csb or 'DropListLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    local mask, template = main_node:child('panel_mask, template')
    local drop = mask:child('panel_drop')
    local bg, listview = drop:child('bg, listview')
    self.m_drop = drop
    template:hide()

    if self.m_dir == 'up' then
        mask:anchor(0.5, 0)
    end
    mask:pos(pos)

    if size then
        local o_size = drop:size()
        local l_size = listview:size()
        local offset_h = o_size.height - l_size.height

        mask:size(size)
        drop:size(size):pos(size.width/2, size.height)
        bg:size(size)
        listview:size(size.width, size.height - offset_h)
        template:size(size.width - 20, template:size().height)
    end

    self:makeDrop(values)
    self:showDrop()
end


-------------------------------------------------------------------------------
-- @override 蒙版收到点击事件
-------------------------------------------------------------------------------
function DropList:onMaskClick(sender, event)
	if event == cc.EventCode.BEGAN then
        local size = self.m_drop:getParent():size()
        local y = self.m_dir == 'down' and size.height * 2 or 0
        self.m_drop:stop():runAction( cc.Sequence:create(
            cc.EaseIn:create(cc.MoveTo:create(0.1, cc.p(size.width/2, y)), 5),
            cc.CallFunc:create(function()
                if self.m_remove_callback then
                    self.m_remove_callback()
                else
                    self:removeFromParent()
                end
            end)
        ) )
	end
end


-------------------------------------------------------------------------------
-- 创建UI
-------------------------------------------------------------------------------
function DropList:makeDrop(values)
    local template = self.main_node:child('template')
    local size = template:size()
    local listview = self.m_drop:child('listview')
    listview:removeAllItems()
    for i, value in ipairs(values) do
        local item = template:clone():show()
        item.data = value
        item:child('text'):setString(value.text or value.name or value)
        local d = item:child('text'):size()
        helper.layout.scaleToWidth(item:child('text'), size.width - 8)
        item:setCascadeColorEnabled(true)
        item:addTouchEventListener( helper.app.tintClickHandler(self, self.onDropClick) )
        listview:pushBackCustomItem(item)
    end
    template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 下拉
-------------------------------------------------------------------------------
function DropList:showDrop()
    local size = self.m_drop:getParent():size()
    local y = self.m_dir == 'down' and size.height * 2 or 0
    self.m_drop:stop():show():pos(size.width/2, y):runAction(
        cc.EaseOut:create(cc.MoveTo:create(0.1, cc.p(size.width/2, size.height)), 5)
    )
end


-------------------------------------------------------------------------------
-- 下拉选项点击
-------------------------------------------------------------------------------
function DropList:onDropClick(sender)
    self.m_callback(sender.data)
    self:onMaskClick(nil, cc.EventCode.BEGAN)
end


return DropList
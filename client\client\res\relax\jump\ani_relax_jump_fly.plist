<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_relax_jump_fly_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,264},{221,116}}</string>
                <key>offset</key>
                <string>{0,30}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,2},{221,116}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,494},{221,106}}</string>
                <key>offset</key>
                <string>{0,-7}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,106}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,262},{221,120}}</string>
                <key>offset</key>
                <string>{0,-15}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,45},{221,120}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,132},{221,130}}</string>
                <key>offset</key>
                <string>{0,-19}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,130}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{221,136}}</string>
                <key>offset</key>
                <string>{0,-22}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,136}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,0},{221,130}}</string>
                <key>offset</key>
                <string>{0,-19}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,130}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_15.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,138},{221,122}}</string>
                <key>offset</key>
                <string>{0,-15}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,122}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_16.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,492},{221,106}}</string>
                <key>offset</key>
                <string>{0,-7}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,106}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_17.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,798},{221,88}}</string>
                <key>offset</key>
                <string>{0,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,88}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_18.png</key>
            <dict>
                <key>frame</key>
                <string>{{446,669},{221,74}}</string>
                <key>offset</key>
                <string>{0,9}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,74}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_19.png</key>
            <dict>
                <key>frame</key>
                <string>{{446,446},{221,74}}</string>
                <key>offset</key>
                <string>{0,9}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,74}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,384},{221,108}}</string>
                <key>offset</key>
                <string>{0,26}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,10},{221,108}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_20.png</key>
            <dict>
                <key>frame</key>
                <string>{{342,798},{221,78}}</string>
                <key>offset</key>
                <string>{0,11}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,40},{221,78}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_21.png</key>
            <dict>
                <key>frame</key>
                <string>{{176,798},{221,84}}</string>
                <key>offset</key>
                <string>{0,14}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,34},{221,84}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_22.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,702},{221,94}}</string>
                <key>offset</key>
                <string>{0,19}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,24},{221,94}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_23.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,602},{221,100}}</string>
                <key>offset</key>
                <string>{0,22}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,18},{221,100}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_24.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,382},{221,108}}</string>
                <key>offset</key>
                <string>{0,26}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,10},{221,108}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_25.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,264},{221,116}}</string>
                <key>offset</key>
                <string>{0,30}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,2},{221,116}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{223,600},{221,100}}</string>
                <key>offset</key>
                <string>{0,22}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,18},{221,100}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,704},{221,92}}</string>
                <key>offset</key>
                <string>{0,18}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,26},{221,92}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{90,798},{221,84}}</string>
                <key>offset</key>
                <string>{0,14}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,34},{221,84}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{262,798},{221,78}}</string>
                <key>offset</key>
                <string>{0,11}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,40},{221,78}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{446,223},{221,74}}</string>
                <key>offset</key>
                <string>{0,9}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,74}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{446,0},{221,74}}</string>
                <key>offset</key>
                <string>{0,9}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,74}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
            <key>ani_relax_jump_fly_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,798},{221,88}}</string>
                <key>offset</key>
                <string>{0,2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,44},{221,88}}</string>
                <key>sourceSize</key>
                <string>{221,180}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_relax_jump_fly.png</string>
            <key>size</key>
            <string>{520,1019}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:eb68907d27406454cf6e37b8e149ddc7:46e0b218bbda82a51ba6a6476f3fd3e7:e00f3ad960aef671228cf69f446e504f$</string>
            <key>textureFileName</key>
            <string>ani_relax_jump_fly.png</string>
        </dict>
    </dict>
</plist>

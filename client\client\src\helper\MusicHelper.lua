-------------------------------------------------------------------------------
--  创世版1.0
--  音乐音效辅助方法类
--      访问方式：helper.music.
--  @date 2017-06-26
--  @auth woodoo
-------------------------------------------------------------------------------
local MusicHelper = {}
helper = helper or {}
helper.music = MusicHelper


-- 配音索引缓存，比如：{'301/b_3wan':{'', '_1'}}，表示有b_3wan.mp3, b_3wan_1.mp3
local peiyin_index_caches = {}


-------------------------------------------------------------------------------
-- 播放背景音乐
-------------------------------------------------------------------------------
function MusicHelper.playMusic(file_name)
	if GlobalUserItem.bMusicAble then
		AudioEngine.playMusic( cc.FileUtils:getInstance():fullPathForFilename(file_name), true )
        -- ios下切换背景音乐需要重新设置音量
        -- AudioEngine.playMusic中已经做过了，但是热更可能无效，这里重复设置一下确保热更有效
        if device.platform == 'ios' then
	        AudioEngine.setMusicVolume((GlobalUserItem and GlobalUserItem.nMusic or 100)/100.0)
        end
	end
end


-------------------------------------------------------------------------------
-- 播放登录界面背景音乐
-------------------------------------------------------------------------------
function MusicHelper.playLogin()
	MusicHelper.playMusic('sound/backgroud01.mp3')
end


-------------------------------------------------------------------------------
-- 播放大厅背景音乐
-------------------------------------------------------------------------------
function MusicHelper.playPlaza()
	MusicHelper.playMusic('sound/backgroud01.mp3')
end


-------------------------------------------------------------------------------
-- 播放房间背景音乐
-------------------------------------------------------------------------------
function MusicHelper.playRoom()
    if cc.FileUtils:getInstance():isFileExist('sound/backgroud_room.mp3') then
        MusicHelper.playMusic('sound/backgroud_room.mp3')
    else
	    MusicHelper.playMusic('sound/backgroud01.mp3')
    end
end


-------------------------------------------------------------------------------
-- 播放配音
-------------------------------------------------------------------------------
function MusicHelper.playPeiyin(peiyin, file)
    if GlobalUserItem.bVoiceAble then
        local relative_path = helper.app.getPeiyinPath(peiyin) .. file
        local indexes = peiyin_index_caches[relative_path]
        if not indexes then
            indexes = {''}
            peiyin_index_caches[relative_path] = indexes
            for i = 1, 5 do
                local path = string.format('sound/%s_%s.mp3', relative_path, i)
                if cc.FileUtils:getInstance():isFileExist(path) then
                    table.insert(indexes, '_' .. i)
                end
            end
        end
        local index = #indexes > 1 and indexes[math.random(1, #indexes)] or indexes[1]
        local path = string.format('sound/%s%s.mp3', relative_path, index)
        AudioEngine.playEffect(path)
    end
end


-------------------------------------------------------------------------------
-- 播放聊天配音
-------------------------------------------------------------------------------
function MusicHelper.playPeiyinChat(peiyin, file)
    if GlobalUserItem.bVoiceAble then
        local peiyin_path = helper.app.getPeiyinPath(peiyin)
        local path = 'sound/' .. peiyin_path .. file .. '.wav'
        if not cc.FileUtils:getInstance():isFileExist(path) then    -- 如果不存在用client下通用的普通话
            local arr = peiyin_path:split('/')
            peiyin_path = arr[2]
            path = 'sound/' .. peiyin_path .. file .. '.wav'
        end
        AudioEngine.playEffect(path)
    end
end


-------------------------------------------------------------------------------
-- 播放通用点击音效s
-------------------------------------------------------------------------------
function MusicHelper.playClickSound()
    if GlobalUserItem.bSoundAble then
        local path = 'sound/click.wav'
        AudioEngine.playEffect(path)
    end
end

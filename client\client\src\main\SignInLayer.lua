-------------------------------------------------------------------------------
--  创世版1.0
--  地区选择
--  @date 2018-04-23
--  @auth woodoo
-------------------------------------------------------------------------------
local SignInLayer = class("SignInLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function SignInLayer:ctor()
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('SignInLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )

    self:initUI()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function SignInLayer:onEnter()
    print('SignInLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function SignInLayer:onExit()
    print('SignInLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 初始化UI
-------------------------------------------------------------------------------
function SignInLayer:initUI()
    local days = GlobalUserItem.nSignDays
    local main_node = self.main_node
    local descs = LANG.SIGN_IN_DESCS:split('|');
    for i = 1, 7 do
        local item = main_node:child('day' .. i)
        item:child('desc'):setString(descs[i] or '')
        if i <= days then
            local mask = display.newSprite('common/bg_sign_in_item_mask.png')
            helper.layout.addCenter(item, mask)
            local flag = display.newSprite('word/font_has_draw_circle.png')
            helper.layout.addCenter(item, flag)
        elseif i == days + 1 then
            item:texture('common/bg_sign_in_item_current.png')
        end
    end
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function SignInLayer:onBtnShare()
    local image_path = 'common/share_image.jpg'
    if cs.app.IS_GOLD_HALL then
        image_path = 'myqrshare.jpg'

        -- 所在地区
        local label_where = ccui.Text:create('', cs.app.FONT_NAME, 22)
        label_where:setTextHorizontalAlignment( cc.TEXT_ALIGNMENT_CENTER )
        local main_scene = helper.app.getFromScene('main_scene')
        if not main_scene then return end
        main_scene:setDistrictName(label_where, 136)

        -- 玩法列表
        local kinds = {}
        local y = 219
        for i, plugin in ipairs(cs.app.plugins) do repeat
            if not plugin.kind then break end
            if yl.is_reviewing and plugin.review then break end
            local index = #kinds + 1
            local btn = display.newSprite('common/bg_gold_hall_share_item.png')
            local label = ccui.Text:create(plugin.name, cs.app.FONT_NAME, 28)
            label:pos(btn:size().width/2, btn:size().height/2):addTo(btn)
            local x = index % 2 == 1 and 114 or 319
            btn:pos(x, y)
            if index % 2 == 0 then
                y = y - 83
            end
            table.insert(kinds, btn)
        until true end

        local bg = display.newSprite('common/bg_gold_hall_share.jpg')
        local qr = helper.comp.createQr( GlobalUserItem.qrcode_url, 196 ):anchor(0.5, 0.5)
        local size = bg:size()
        local w, h = size.width, size.height
        local render = cc.RenderTexture:create(w, h)
        render:begin()
        
        bg:pos(w * 0.5, h * 0.5)
        bg:visit()
        
        qr:pos(538, 150)
        qr:visit()
        
        label_where:pos(259, 294)
        label_where:visit()

        for i, btn in ipairs(kinds) do
            btn:visit()
        end

        render:endToLua()
        render:saveToFile(image_path, cc.IMAGE_FORMAT_JPEG)
    end

    -- 在saveToFile模式下，需要下一帧文件才会生成
    self:perform(function()
        local layer = helper.pop.shareImage(image_path, false, LANG.SHARE_GOLD, 'share_sign_in')
        layer:showButtons('pyq')
        layer:hide():doShare(yl.ThirdParty.WECHAT_CIRCLE)
        self:removeFromParent()
    end, 0.1)
end


return SignInLayer

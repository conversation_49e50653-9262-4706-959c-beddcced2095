-------------------------------------------------------------------------------
--  创世版1.0
--  比赛场即将开始等待界面
--  @date 2018-04-12
--  @auth woodoo
-------------------------------------------------------------------------------
local ArenaWaitLayer = class("ArenaWaitLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ArenaWaitLayer:ctor(results)
    self:enableNodeEvents()
    self:setName('arena_wait_layer')

    -- 载入主UI
    local main_node = helper.app.loadCSB('ArenaWaitLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('btn_back'):hide()

    local bar = main_node:child('loading/bar')
    local progress = 0
    self:perform(function()
        progress = progress + 1
        bar:setPercent(progress)
    end, 0.05, 200)

    self:perform(function()
        main_node:child('btn_back'):show():addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    end, 10)
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ArenaWaitLayer:onEnter()
    print('ArenaWaitLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ArenaWaitLayer:onExit()
    print('ArenaWaitLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function ArenaWaitLayer:onBtnBack(sender)
    self:removeFromParent()
end


return ArenaWaitLayer

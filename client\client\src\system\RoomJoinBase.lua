-------------------------------------------------------------------------------
--  创世版1.0
--  所有游戏房间加入基类
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local RoomJoinBase = class("RoomJoinBase", cc.Layer)

local ROOM_NUMS = 6

-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomJoinBase:ctor()
    print('RoomJoinBase:ctor...')
    self:enableNodeEvents()

    -- 绑定试图
    PassRoom:getInstance():setViewFrame(self)

    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomJoinLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self)

    for i = 1, ROOM_NUMS do
        main_node:child('label_num' .. i):setString('')
    end
    helper.logic.initKeyboard( main_node:child('keyboard'), handler(self, self.showNums), ROOM_NUMS )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RoomJoinBase:onExit()
    print('RoomJoinBase:onExit...')
    PassRoom:getInstance():setViewFrame(nil)
end


-------------------------------------------------------------------------------
-- 显示数值
-------------------------------------------------------------------------------
function RoomJoinBase:showNums(nums, opts)
    local main_node = self.main_node
    local len = #nums
    for i = 1, ROOM_NUMS do
        local label = main_node:child('label_num' .. i)
        local old = label._value
        local new = i > len and '' or nums:sub(i, i)
        label._value = new

        if old and old ~= '' and old ~= new then -- delete
            local child = label:child('use_node')
            if child then
                child:setName('')
                child:setTextColor( label:getTextColor() )
                child:stop():scale(1):runMyAction( cc.Sequence:create( 
                    cc.Spawn:create(
                        cc.FadeOut:create(0.15),
                        cc.ScaleTo:create(0.15, 0, 1),
                        cc.MoveBy:create(0.15, cc.p(0, 30))
                    ),
                    cc.RemoveSelf:create(true)
                ) )
            end
        end

        if old ~= new and new ~= '' then
            local child = label:clone() -- 主要是方便设置字体参数
            child:removeAllChildren()
            child:setName('use_node')
            child:setString(new)
            local effect = child:clone():pos(child:size().width/2, child:size().height/2)
                :hide():addTo(child):setTextColor( cc.c3b(255, 255, 255) )
            child:pos(0, 0):scale(4):opacity(0):runMyAction( cc.Sequence:create(
                cc.Spawn:create( 
                    cc.FadeIn:create(0.15),
                    cc.EaseBackOut:create( cc.ScaleTo:create(0.15, 1) )
                ),
                cc.CallFunc:create( function(sender)
                    effect:show():runMyAction( cc.Spawn:create(
                        cc.ScaleTo:create(0.3, 3),
                        cc.FadeOut:create(0.3)
                    ) )
                end )
            ) )
            child:addTo(label)
        end
    end

    if len == ROOM_NUMS then
        helper.app.checkRoom(function()
            helper.pop.waiting()
            PassRoom:getInstance():getNetFrame():onSearchRoom(nums, yl.SEARCH_ROOM_TYPE_JOIN)
        end, nil, nums)
    end
end


return RoomJoinBase
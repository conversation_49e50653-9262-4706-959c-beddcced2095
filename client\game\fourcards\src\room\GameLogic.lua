local GameLogic = {}


--**************    扑克类型    ******************--
--混合牌型
GameLogic.OX_VALUEO				= 100

GameLogic.USER_OPERATE_PASS     = 200
GameLogic.USER_OPERATE_HINT     = 201
GameLogic.USER_OPERATE_OUTCARD  = 202
GameLogic.USER_OPERATE_REPUSH 	= 203


--最大手牌数目
GameLogic.MAX_CARDCOUNT			= 5
--牌库数目
GameLogic.FULL_COUNT			= 52
--正常手牌数目
GameLogic.NORMAL_COUNT			= 5


--取模
function GameLogic:mod(a,b)
    return a - math.floor(a/b)*b
end
--获得牌的数值
function GameLogic:getCardValue(cbCardData)
    return self:mod(cbCardData, 16)
end

--获得牌的颜色（0 -- 4）
function GameLogic:getCardColor(cbCardData)
    return math.floor(cbCardData/16)
end

--拷贝表
function GameLogic:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
end

--排序
function GameLogic:sort(cbCardData, sort_card_num)
	table.sort(cbCardData, function(a, b) 
		local a_v = self:getCardValue(a)
		local b_v = self:getCardValue(b)
		if a >= 0x40 then
			a_v = a
		end
		if b >= 0x40 then
			b_v = b
		end
		if a_v < 0x03 then
			a_v = a_v + 0x0D
		end
		if b_v < 0x03 then
			b_v = b_v + 0x0D
		end
		if a_v > b_v then
			return true
		elseif a_v < b_v then
			return false
		else
			return a > b
		end
	end)

	
end

function GameLogic:concat(a, b)
	local result = {}
	for _, v in ipairs(a) do
		table.insert( result, v)
	end
	for _, v in ipairs(b) do
		table.insert( result, v)
	end

	return result
end

-- 获取相同的牌
function GameLogic:anayseCard(cbCardData, card_count, is_sort_card_value, is_out_card)
	is_sort_card_value = is_sort_card_value or false
	is_out_card = is_out_card or false
	local max_king_card = {}
	local dian_king_card = {}
	local min_king_card = {}
	local vec_cards = {}
	local cards = {}
	local pre_card = 0
	local king_is_ok = false
	self:sort(cbCardData)
	--dump(cbCardData)
	for i = 1, card_count do repeat
		if cbCardData[i] == 0 then
			break
		end
		if cbCardData[i] == 0x44 then
			table.insert(dian_king_card, cbCardData[i])
			break
		end
		if cbCardData[i] == 0x42 then
			table.insert(max_king_card, cbCardData[i])
			break
		end
		if cbCardData[i] == 0x41 then
			table.insert(min_king_card, cbCardData[i])
			break
		end
		if not king_is_ok then
			pre_card = cbCardData[i];
			king_is_ok = true;
			table.insert(cards, cbCardData[i]);
			--print('first not king ', self:getCardValue(cbCardData[i]))
			break
		end

		if self:getCardValue(pre_card) == self:getCardValue(cbCardData[i]) then
			table.insert(cards, cbCardData[i])
			break
		end
		--print('current card data ', self:getCardValue(cbCardData[i]))
		--dump(cards)
		table.insert(vec_cards, cards)
		cards = {}
		pre_card = cbCardData[i]
		table.insert(cards, cbCardData[i])
	until true
	end

	if #cards > 0 then
		table.insert(vec_cards, cards)
	end

	local king_num = #max_king_card + #min_king_card + #dian_king_card
	if king_num >= 4 then
		local king_cards = self:concat(dian_king_card, self:concat(max_king_card, min_king_card))
		table.insert(vec_cards, 1, king_cards)
	else
		if #min_king_card > 0 then
			table.insert(vec_cards, 1, min_king_card)
		end

		if #max_king_card > 0 then
			table.insert(vec_cards, 1, max_king_card)
		end

		if #dian_king_card > 0 then
			table.insert(vec_cards, 1, dian_king_card)
		end
	end

	if not is_sort_card_value then
		if is_out_card then
			table.sort(vec_cards, function (list_a, list_b) 
				local a_d = list_a[1]
				local b_d = list_b[1]
				if #list_a < #list_b then
					return true
				elseif #list_a > #list_b then
					return false
				else
					return self:compareTwoCard(a_d, b_d)
				end

			end)
		else
			table.sort(vec_cards, function (list_a, list_b) 
				local a_d = list_a[1]
				local b_d = list_b[1]
				if a_d > 0x40 and b_d < 0x40 then
					return true
				end
				if  a_d < 0x40 and b_d > 0x40 then
					return false
				end
				if a_d > 0x40 and b_d > 0x40 then
					return a_d > b_d
				end
				if a_d < 0x40 and b_d < 0x40 then
					if #list_a > #list_b then
						return true
					elseif #list_a < #list_b then
						return false
					else
						return self:compareTwoCard(b_d, a_d)
					end
				end
			end)
		end
	end

	return vec_cards
end

function GameLogic:getCardXian(vec_cards, is_zhuaneight)
	is_zhuaneight = is_zhuaneight or false
	local result = {}
	for _, v in ipairs(vec_cards) do repeat
		local card_info = {card = {}, card_num = #v, card_v = 0, isSingleColor = false}
		card_info.is_single_king = false
			
		for i = 1, #v do
			table.insert(card_info.card, v[i])
		end

		print('is_zhuaneight and card_num is', is_zhuaneight, card_info.card_num)
		if is_zhuaneight and card_info.card_num == 4 then
			if card_info.card[1] == card_info.card[4] then
				card_info.card_num = 8
				card_info.isSingleColor = true
			 end 
		end		

		card_info.card_v = card_info.card[1]

		if v[1] > 0x40 then
			local max_king = 0
			local min_king = 0
			local dian_king = 0
			for k, val in ipairs(v) do
				if val == 0x44 then
					dian_king = dian_king + 1
				elseif val == 0x42 then
					max_king = max_king + 1
				elseif val == 0x41 then
					min_king = min_king + 1
				end
			end

			local card_num = 0
			if dian_king == 4 then
				card_num = 8 + (min_king + max_king) * 2
				card_info.card_v = 0x44
				card_info.is_single_king = true
				if is_zhuaneight and card_num == 8 then
					card_info.isSingleColor = true
				end
			elseif max_king == 4 then
				card_num = 8 + (min_king + dian_king) * 2
				card_info.card_v = 0x42
				card_info.is_single_king = true
				if is_zhuaneight and card_num == 8 then
					card_info.isSingleColor = true
				end
			elseif min_king == 4 then
				card_num = 8 + (dian_king + max_king) * 2
				card_info.card_v = 0x41
				card_info.is_single_king = true
				if is_zhuaneight and card_num == 8 then
					card_info.isSingleColor = true
				end
			else
				if dian_king + max_king + min_king >= 4 then
					card_num = 7 + (dian_king + max_king + min_king - 4)
					card_info.card_v = 0x42
				else
					card_num = min_king + max_king + dian_king
					if min_king > 0 then
						card_info.card_v = 0x41
					elseif max_king > 0 then
						card_info.card_v = 0x42
					elseif dian_king > 0 then
						card_info.card_v = 0x44
					end
				end				
			end
			card_info.card_num = card_num
		end
		
		table.insert(result, card_info)
	until true
	end

	table.sort(result, function (list_a, list_b) 
		local a_d = list_a.card_v
		local b_d = list_b.card_v
		--if a_d < 0x40 and b_d < 0x40 then
			if list_a.card_num < list_b.card_num then
				return true
			elseif list_a.card_num > list_b.card_num then
				return false
			else
				return self:compareTwoCard(a_d, b_d)
			end
		--end
	end)

	return result
end

function GameLogic:compareTwoCard(card_a, card_b)
	local a_v = self:getCardValue(card_a)
	if card_a > 0x40 then
		a_v = card_a
	elseif a_v <= 0x02 then
		a_v = a_v + 0x0D
	end
	local b_v = self:getCardValue(card_b)
	if card_b > 0x40 then
		b_v = card_b
	elseif b_v <= 0x02 then
		b_v = b_v + 0x0D
	end
	return a_v < b_v
end

function GameLogic:GuanCard(vec_cards, out_card, can_spilt_card, is_zhuaneight)
	is_zhuaneight = is_zhuaneight or false
	can_spilt_card = can_spilt_card or false
	local result = {}
	local is_has_zha = false
	--dump(out_card)
	for _, v in ipairs(vec_cards) do repeat
		if out_card.card_num == 0 then
			table.insert(result, v.card)
			break
		end
		if not is_has_zha then
			if v.card_num >= 4 then
				is_has_zha = true
			end
		end
		if out_card.card_num >= 4 then
			if out_card.card_num < v.card_num then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				print('is_zhuaneight and card_num is ', is_zhuaneight, out_card.card_num)
				if is_zhuaneight and out_card.card_num == 8 then
					if v.isSingleColor and not out_card.isSingleColor then
						table.insert(result, v.card)
						break
					elseif v.isSingleColor and out_card.isSingleColor then
						if self:compareTwoCard(out_card.card_real_v, v.card_v) then
							table.insert(result, v.card)
							break
						end
					elseif not v.isSingleColor and not out_card.isSingleColor then
						if self:compareTwoCard(out_card.card_real_v, v.card_v) then
							table.insert(result, v.card)
							break
						end
					else
						break
					end
				end

				if v.is_single_king and not out_card.is_single_king then
					table.insert(result, v.card)
					break
				end
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					dump(out_card)
					dump(v)
					table.insert(result, v.card)
					break
				end
			end
			
		end
		if out_card.card_num < 4 then
			if v.card_num >= 4 then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end 
			end
		end

	until true
	end

	if can_spilt_card == false then
		return result
	end

	if #result == 0 and not is_has_zha then
		for _, v in ipairs(vec_cards) do
			if self:compareTwoCard(out_card.card_real_v, v.card_v) and out_card.card_num <= #v.card then
				local ret_vec = {}
				for i = 1, out_card.card_num do
					table.insert(ret_vec, v.card[i])
				end
				table.insert(result, ret_vec)
			end
		end
	end
	return result
end


return GameLogic
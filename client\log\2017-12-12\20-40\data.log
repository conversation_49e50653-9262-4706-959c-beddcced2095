[{"logtime": "2017/12/12 20:40:12", "logdata": "[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1495: cc.ScaleTo:create has wrong number of arguments: 1, was expecting 4\nstack traceback:\n\t[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1495: in function 'showCardPlate'\n\t[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1070: in function 'gameOutCard'\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:674: in function 'onEventGameMessage'\n\t[string \"client/src/system/RoomFrame.lua\"]:124: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}, {"logtime": "2017/12/12 20:40:30", "logdata": "[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1495: cc.ScaleTo:create has wrong number of arguments: 1, was expecting 4\nstack traceback:\n\t[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1495: in function 'showCardPlate'\n\t[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1070: in function 'gameOutCard'\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:674: in function 'onEventGameMessage'\n\t[string \"client/src/system/RoomFrame.lua\"]:124: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}, {"logtime": "2017/12/12 20:40:30", "logdata": "[string \"cocos/init.lua\"]:57: attempt to call global 'buglyReportLuaException' (a nil value)\nstack traceback:\n\t[C]: in function 'create'\n\t[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1495: in function 'showCardPlate'\n\t[string \"game/mahjong/src/room/GameViewLayer.lua\"]:1070: in function 'gameOutCard'\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:674: in function 'onEventGameMessage'\n\t[string \"client/src/system/RoomFrame.lua\"]:124: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}, {"logtime": "2017/12/12 20:40:35", "logdata": [0, 0, 0, 0, 0, 0, 0, 0]}, {"logtime": "2017/12/12 20:40:35", "logdata": [0, 0, 0, 0, 0, 0, 0, 0]}]
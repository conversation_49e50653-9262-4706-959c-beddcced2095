#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess

def check_apache_logs():
    """检查 Apache 错误日志"""
    print("=== 检查 Apache 错误日志 ===")
    
    # 常见的 XAMPP Apache 日志路径
    log_paths = [
        r"E:\xampp\apache\logs\error.log",
        r"C:\xampp\apache\logs\error.log",
        r"D:\xampp\apache\logs\error.log",
    ]
    
    error_log_found = False
    
    for log_path in log_paths:
        if os.path.exists(log_path):
            print(f"找到错误日志: {log_path}")
            error_log_found = True
            
            try:
                # 读取最后 20 行
                with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    last_lines = lines[-20:] if len(lines) > 20 else lines
                
                print(f"\n最后 {len(last_lines)} 行错误日志:")
                print("-" * 60)
                for line in last_lines:
                    print(line.rstrip())
                print("-" * 60)
                
            except Exception as e:
                print(f"读取日志文件时出错: {e}")
            
            break
    
    if not error_log_found:
        print("❌ 未找到 Apache 错误日志文件")
        print("可能的原因:")
        print("1. XAMPP 安装在不同的路径")
        print("2. Apache 从未启动过")
        print("3. 权限问题")
    
    # 检查 XAMPP 安装路径
    print("\n=== 检查 XAMPP 安装路径 ===")
    possible_paths = [
        r"E:\xampp",
        r"C:\xampp", 
        r"D:\xampp",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到 XAMPP 安装: {path}")
            
            # 检查 Apache 可执行文件
            apache_exe = os.path.join(path, "apache", "bin", "httpd.exe")
            if os.path.exists(apache_exe):
                print(f"✅ Apache 可执行文件: {apache_exe}")
                
                # 尝试测试 Apache 配置
                try:
                    print("\n测试 Apache 配置...")
                    result = subprocess.run([apache_exe, '-t'], 
                                          capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        print("✅ Apache 配置文件语法正确")
                        print("输出:", result.stdout)
                    else:
                        print("❌ Apache 配置文件有错误:")
                        print("错误:", result.stderr)
                        print("输出:", result.stdout)
                        
                except subprocess.TimeoutExpired:
                    print("⚠️ 配置测试超时")
                except Exception as e:
                    print(f"❌ 测试配置时出错: {e}")
            else:
                print(f"❌ Apache 可执行文件不存在: {apache_exe}")
        else:
            print(f"❌ 路径不存在: {path}")

def check_system_info():
    """检查系统信息"""
    print("\n=== 系统信息 ===")
    
    try:
        # 检查是否以管理员身份运行
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            print("✅ 以管理员身份运行")
        else:
            print("❌ 未以管理员身份运行")
            print("💡 建议以管理员身份运行 XAMPP")
    except:
        print("⚠️ 无法检查管理员权限")
    
    # 检查防火墙状态
    try:
        result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                              capture_output=True, text=True, timeout=5)
        print(f"\n防火墙状态:")
        print(result.stdout)
    except:
        print("⚠️ 无法检查防火墙状态")

if __name__ == '__main__':
    check_apache_logs()
    check_system_info()
    
    print("\n=== 建议的解决步骤 ===")
    print("1. 以管理员身份重新运行 XAMPP")
    print("2. 检查上面显示的 Apache 配置错误")
    print("3. 如果有端口冲突，停止冲突的服务")
    print("4. 如果有权限问题，检查文件夹权限")
    print("5. 考虑重新安装 XAMPP")

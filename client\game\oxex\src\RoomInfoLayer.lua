-------------------------------------------------------------------------------
--  创世版1.0
--  私人房游戏顶层
--  @date 2018-01-02
--  @auth woodoo
-------------------------------------------------------------------------------
local RoomInfoBase = cs.app.client('system.RoomInfoBase')


local RoomInfoLayer = class('RoomInfoLayer', RoomInfoBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomInfoLayer:ctor( game_layer )
    print('RoomInfoLayer:ctor...')
    self.super.ctor(self, game_layer)
end


-------------------------------------------------------------------------------
-- 生成邀请信息
-------------------------------------------------------------------------------
function RoomInfoLayer:makeShareInfo()

    local data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[GlobalUserItem.nCurGameKind]

    local name = config.NAME
    local brand = cs.game.BRAND
    local room_id = data.szServerID
    local jushu = data.nPlayCount
    local cost = data.nFee
    local pay_type = data.cbPayType
    local zhifu = LANG{'CREATE_ZHIFU_'..pay_type, fangka = cost}
    if jushu < 0 then
        jushu = LANG{'ROOM_KUN', kun=-jushu}
    end
    local renshu = PassRoom:getInstance():getChairCount()
	local difen = data.lCellScore
	local wanfa = self:makeRuleStr()

	--local url = yl.INVITE_URL .. '?roomId=' .. room_id
    local url = helper.app.makeInviteUrl(room_id)
    local title = LANG{'ROOM_INVITE_TITLE', brand = brand, name = name, room = room_id}
    local desc = LANG{'ROOM_INVITE_CONTENT', jushu = jushu, renshu = renshu, wanfa = wanfa, zhifu = zhifu}
    
    return url, title, desc
end


-------------------------------------------------------------------------------
-- 生成玩法字符串
-------------------------------------------------------------------------------
function RoomInfoLayer:makeRuleStr()
	local room_data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[ GlobalUserItem.nCurGameKind ]
    local t = {}
	self.m_rule_arr = self.m_rule_arr or {}
	self.m_rule_arr = room_data.cbGameRule[1]
    for i, v in ipairs(self.m_rule_arr) do
        if i > 2 then   -- 1是标志位
            local rule_key = 'RULE' .. (i - 3) .. (v == 1 and '' or '_NONE')
            local str = config[rule_key]
            if str then 
                t[#t + 1] = str
            end
        end
    end
    return table.concat(t, '/')
end



return RoomInfoLayer
<?php
//活动接口
require(APPPATH.'/libraries/REST_Controller.php');

class Activity extends REST_Controller{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('server_model');
        $this->load->model('activity_model');
        $this->load->model('mp_model');
    }

    /*
    * 获取某个游戏渠道的轮播图
    * @return array 游戏活动
    */
    public function slides_post()
    {
        $c_version = $this->input->post('c_version');
        $res_version = $this->input->post('res_version');

        $version_no = (int)$c_version . '.' . (int)$res_version;

        if($this->_channel['is_audit'] == 1) {
            if ($this->_channel['audit_version'] == $version_no) {
//                $this->response(array('code' => 0, 'msg' => '', 'slides' => []));
            }
        }

//        $template = $this->server_model->get_channel_template_by_type($this->_channel['channel_id'],1);
        $slide = $this->activity_model->get_channel_slide($this->_channel['channel_id']);
        $role_id = $this->input->post('uid');

        $ret = array();
//        $slide = array();

        if($slide) {

            $templates = $this->activity_model->get_slides_templates($slide['slide_id']);

            if($templates) {

                foreach($templates as $k=>$v) {
                    $template = $this->activity_model->get_slide_template_by_id($v['template_id']);

                    if($template) {

                        if($template['show_begin_time']>time() || $template['show_end_time']<time()) {
                            continue;
                        }

                        $ret[$k]['id'] = $v['id']*1;
                        $ret[$k]['category'] = $template['category']*1;
                        $ret[$k]['title'] = $template['title'];
                        $ret[$k]['image'] = str_replace('https://','http://',$template['small_image']);
                        $ret[$k]['res'] = str_replace('https://','http://',$template['big_image']);
                        $ret[$k]['btn'] = str_replace('https://','http://',$template['btn_image']);
                        $ret[$k]['skip_type'] = $template['skip_type'];
                        $ret[$k]['skip_param'] = $this->_get_skip_param($template['skip_type'],$template['skip_param'],$role_id);
                    }

                }
                if (!empty($ret)){
                    //重置索引 防止json格式错误
                    $ret = array_values($ret);
                }
            }
        }

        $url = base_url() . 'down/invite?game_id=' . $this->_channel['game_id'] . '&role_id=' . $role_id;


        $this->response(array('code'=>0,'msg'=>'','slides'=>$ret,'qrcode_url'=>$url));
    }

    /*
     * 获取弹窗
     */
    public function popup_post() {
        $role_id = $this->input->post('uid');

        if($this->_channel['is_audit'] == 1) {
            $this->response(array('code'=>0,'msg'=>'','data'=>[]));
        }

        $data = array();

        $this->load->model('player_model');

        $this->player_model->set_database($this->_channel['game_id']);

        $role = $this->player_model->get_role_info($role_id);

        if(!$role) {
            show_response(1,'角色不存在');
        }

        // 更新渠道号
        if(isset($role['ChannelID'])&&$role['ChannelID'] == "") {
            $this->player_model->update_role_info($role['UserID'],array('ChannelID'=>$this->_channel['channel_id']));
        }

        $popup = $this->activity_model->get_channel_popup($this->_channel['channel_id']);

        if($popup) {
           // 查询弹窗是否在时间范围内
            if($popup['begin_time']<=time()&&$popup['end_time']>=time()) {
                // 查询是否在人群范围内的
                // 查寻注册时间
                $reg_diff_days = round((time()-strtotime(date('Y-m-d',strtotime($role['RegisterDate']))))/86400);
                // 查询最后登录时间差
                $is_return_role = $this->player_model->check_return_role(date('Y-m-d'),$role_id);

                if($popup['target'] == 1 || ($popup['target']==2&&$reg_diff_days<=7) || ($popup['target']==3 && $is_return_role)) {
                    // 判断角色今天登录的次数
                    $count = $this->player_model->get_login_nums2(date('Y-m-d'),$role_id);
                    $login_count = isset($count['Logins'])?$count['Logins']:0;
                    // 判断弹窗频率
                    if($popup['freq'] == 1 || ($popup['freq'] == 2 && $login_count == 1)) {

                        $popup['skip_param'] = $this->_get_skip_param($popup['skip_type'],$popup['skip_param'],$role_id);

                        $data = $popup;
                    }
                }

            }
        }

        $this->response(array('code'=>0,'msg'=>'','data'=>$data));
    }

    private function _get_skip_param($skip_type,$skip_param,$role_id) {
        $ret = array();

        if($skip_type == 'MALL') {
            $ret = array('name'=>$skip_param);
        }

        if($skip_type == 'PAY') {
            if($skip_param) {
                $params = explode(',',$skip_param);

                if(is_array($params)&&count($params)==3) {
                    // 获取对应条件下的商品
                    $this->load->model('server_model');
                    $this->server_model->set_database($this->_channel['game_id']);

                    $malls  = $this->server_model->get_shelves_by_channel($this->_channel['channel_id']);

                    if($malls) {
                        foreach($malls as $mall) {
                            if($mall['name'] == $params[0]) {

                                $is_first_order = $this->mp_model->get_role_is_first_order($role_id,$this->_channel['game_id']);

                                // 获取该商城下的商品
                                $good = $this->server_model->get_shelves_goods2($mall['mall_id'],1,$is_first_order,$params[1],$params[2]);

                                if($good) {
                                    $ret = array('mall_id'=>$mall['mall_id'],'good_id'=>$good['id']);
                                }
                            }
                        }
                    }

                }
            }
        }

        if($skip_type == 'LINK') {
            $ret = array('url'=>$skip_param);
        }

        return $ret;
    }
}
?>
-------------------------------------------------------------------------------
--  创世版3.0
--  网络消息监听
--  @date 2018-01-11
--  @auth woodoo
-------------------------------------------------------------------------------
local MsgListener = class('MsgListener')


local cmdCombine = function(main, sub)
	return main .. '-' .. sub
end
local cmdSplit = function(cmd)
    local arr = cmd:split('-')
	return tonumber(arr[1]), tonumber(arr[2])
end


function MsgListener:ctor()
	self.cmd_map = {}
	self.obj_map = {}
	self.remove_cmds = {}
	self.global_listen = nil
end


--------------------------------------------------------------------------------
-- 设置全局监听
--------------------------------------------------------------------------------
function MsgListener:setGlobalListen(obj, func)
	if not obj or not func then
		self.global_listen = nil
	end

	self.global_listen = {obj=obj, func=func}
end


--------------------------------------------------------------------------------
-- 监听
--------------------------------------------------------------------------------
function MsgListener:addListen(main, sub, obj, func)
	assert(obj, 'listen object must exists!')
	assert(func, 'listen callback function must exists!')
	local entity = {obj=obj, func=func, removed=false}
	local cmd = cmdCombine(main, sub)

	-- 该命令的监听列表是否已经存在
	local cmd_entities = self.cmd_map[cmd]
	if not cmd_entities then
		cmd_entities = {}
		self.cmd_map[cmd] = cmd_entities
	end

	-- 该监听人的命令列表是否已经存在
	local obj_cmds = self.obj_map[obj]
	if not obj_cmds then
		obj_cmds = {}
		self.obj_map[obj] = obj_cmds
	end

	-- 如果已经存在，并且设为移除了，重设为正常
	for i, e in ipairs(cmd_entities) do
		if e.obj == obj and e.func == func then
			if e.removed then
				e.removed = false
			end
			return
		end
	end

	cmd_entities[#cmd_entities+1] = entity
	obj_cmds[#obj_cmds+1] = cmd
end


--------------------------------------------------------------------------------
-- 按监听者移除其所有监听
--------------------------------------------------------------------------------
function MsgListener:removeListenByObj(obj)
	local obj_cmds = self.obj_map[obj]
	if not obj_cmds then return end

	for i, cmd in ipairs(obj_cmds) do
        local main, sub = cmdSplit(cmd)
		self:removeListen(main, sub, obj)
	end

	self.obj_map[obj] = nil
end


--------------------------------------------------------------------------------
-- 移除监听者的某个命令监听
--	remove_obj：单独调用时需要传true
--------------------------------------------------------------------------------
function MsgListener:removeListen(main, sub, obj, remove_obj)
    local cmd = cmdCombine(main, sub)
	local cmd_entities = self.cmd_map[cmd]
	if not cmd_entities then return end

	self.remove_cmds[#self.remove_cmds+1] = cmd
	for i, entity in ipairs(cmd_entities) do
		if entity.obj == obj then
			entity.removed = true	-- 标记成被删除，不直接删除，避免迭代遍历问题
		end
	end

	if remove_obj then
		local obj_cmds = self.obj_map[obj]
		if obj_cmds then
			for i=#obj_cmds, 1, -1 do
				if obj_cmds[i] == cmd then
					table.remove(obj_cmds, i)
				end
			end
			if #obj_cmds == 0 then
				self.obj_map[obj] = nil
			end
		end
	end
end


--------------------------------------------------------------------------------
-- 清理被移除的监听
--------------------------------------------------------------------------------
function MsgListener:clearRemovedListens()
	local t = table.unique(self.remove_cmds, true)
	for _, cmd in ipairs(t) do
		local cmd_entities = self.cmd_map[cmd]
		if cmd_entities then
			for i=#cmd_entities, 1, -1 do
				local entity = cmd_entities[i]
				if entity.removed then
					table.remove(cmd_entities, i)
				end
			end
			if #cmd_entities == 0 then
				self.cmd_map[cmd] = nil
			end
		end
	end
	self.remove_cmds = {}
end


--------------------------------------------------------------------------------
-- 触发监听
--------------------------------------------------------------------------------
function MsgListener:trigger(main, sub, data, error_code)
    self:clearRemovedListens()

    local cmd = cmdCombine(main, sub)

	if self.global_listen then
		self.global_listen.func(self.global_listen.obj, main, sub, data, error_code)
	end

	local cmd_entities = self.cmd_map[cmd]
	if cmd_entities then
		local count = #cmd_entities	-- 防止事件处理中cmd_entities被修改(增加)
		for i=1, count do
            data:resetread()    -- 必须复位
			local entity = cmd_entities[i]
			if not entity.removed and not tolua.isnull(entity.obj) then
				-- 防止数据包的处理被打断，需要用xpcall捕获错误
				xpcall(function() entity.func(entity.obj, data, error_code) end, __G__TRACKBACK__)
			end
		end
	end
end


return MsgListener
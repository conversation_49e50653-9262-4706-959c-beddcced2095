local GameViewLayer = class("GameViewLayer",function(scene)
		local gameViewLayer =  display.newLayer()
    return gameViewLayer
end)

cs.app.client('system.GameViewLayerEx').assign(GameViewLayer)

local cmd = appdf.req(appdf.GAME_SRC.."fourcards.src.room.CMD_Game")
local PopupHead = appdf.req("client.src.system.PopupHead")
local ExternalFun = require(appdf.EXTERNAL_SRC .. "ExternalFun")
local GameLogic = appdf.req(appdf.GAME_SRC.."fourcards.src.room.GameLogic")

GameViewLayer.BT_PROMPT 			= 2
GameViewLayer.BT_OPENCARD 			= 3
GameViewLayer.BT_START 				= 'btn_start'


GameViewLayer.BT_SWITCH 			= 'btn_switch'
GameViewLayer.BT_EXIT 				= 'btn_exit'
GameViewLayer.BT_CHAT 				= 'img_chat'
GameViewLayer.BT_SETTING			= 'btn_setting'
--GameViewLayer.BT_TAKEBACK 			= 16

GameViewLayer.BT_YQHY 				= 'btn_yqhy'
GameViewLayer.BT_VOL_START			= 'img_vol_start'
GameViewLayer.BT_VOL_END			= 'img_vol_end'
GameViewLayer.BT_VOL				= 'img_vol'
GameViewLayer.BT_VOL_CANCEL			= 'img_vol_cancel'
GameViewLayer.BT_NOT_OUT			= 'btn_not_out_card'
GameViewLayer.BT_HINT				= 'btn_hint'
GameViewLayer.BT_OUT_CARD			= 'btn_out_card'


GameViewLayer.FRAME 				= 1
GameViewLayer.NICKNAME 				= 2
GameViewLayer.SCORE 				= 3
GameViewLayer.FACE 					= 7

GameViewLayer.TIMENUM   			= 1
GameViewLayer.CHIPNUM 				= 1

--牌间距
GameViewLayer.CARDSPACING 			= 40

GameViewLayer.VIEWID_CENTER 		= 5

GameViewLayer.RES_PATH 				= "game/fourcards/res/"

local ZORDER_HEAD_INFO = 10
local XIAZHU_NUM = 4
local ZORDER_CHAT_LAYER = 10

local pointPlayer = {cc.p(170, 115), cc.p(897, 625)}
local pointCard = {cc.p(display.cx, 100), cc.p(display.cx, 617)}
local pointClock = {cc.p(157, 275), cc.p(1037, 640)}
local pointOpenCard = {cc.p(display.size.width - 350, 115), cc.p(display.cx - 200, display.cy + 250)}
local pointTableScore = {cc.p(display.cx, 342), cc.p(display.cx, 505)}
local pointBankerFlag = {cc.p(243, 208), cc.p(965, 715)}
local pointChat = {cc.p(230, 250), cc.p(767, 690)}
local ptWinLoseAnimate = {cc.p(320, 60), cc.p(1065, 500)}
local pointUserInfo = {cc.p(205, 170), cc.p(445, 240)}
local anchorPoint = {cc.p(0, 0), cc.p(1, 1)}


function GameViewLayer:onInitData()
	
	self.chatDetails = {}
	self.gamePlayStatus = {}
	self.bCanMoveCard = false
	self.player_num = 0
	self.bCanNextReplay = true
	self.cbPreOutCard = {}
end

function GameViewLayer:onExit()
	print("GameViewLayer onExit")
	self:onExitEx()
	yl.IS_REPLAY_MODEL = false
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
    cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
end

local this
function GameViewLayer:ctor(scene)
	this = self
	self._scene = scene

	local main_node = helper.app.loadCSB('GameLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

	--房卡需要
	self.m_sparrowUserItem = {}
	self:onInitDataEx()

	self:onInitData()

	--节点事件
	local function onNodeEvent(event)
		if event == "exit" then
			self:onExit()
		end
	end
	self:registerScriptHandler(onNodeEvent)


	local bAble = GlobalUserItem.bSoundAble or GlobalUserItem.bVoiceAble					--声音
	if GlobalUserItem.bVoiceAble then
		--AudioEngine.playMusic(GameViewLayer.RES_PATH.."sound/backMusic.mp3", true)
	end
	
	local  btcallback = function(ref, type)
		helper.app.tintClickEffect(ref, eventType)
        if type == ccui.TouchEventType.ended then
			if GlobalUserItem.bVoiceAble then
				AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/clickcard.mp3")
				--helper.music.playPeiyin(self._scene.peiyin[MY_VIEWID], GameViewLayer.RES_PATH.."sound/clickcard.mp3")
			end
         	this:onButtonClickedEvent(ref:getName(),ref)
		elseif type == ccui.TouchEventType.began and ref:getName() == GameViewLayer.BT_VOL then
			self:onButtonClickedEvent(GameViewLayer.BT_VOL_START, ref)
		elseif type == ccui.TouchEventType.canceled and ref:getName() == GameViewLayer.BT_VOL then
			self:onButtonClickedEvent(GameViewLayer.BT_VOL_CANCEL, ref)
		end
    end

	self.game_is_start = false
	self.daqiang_ani_play = false

    --特殊按钮
    local pointBtSwitch = cc.p(display.size.width - 60, display.size.height - 60)

	self:showJuShu()	

	self.btChat = self.main_node:child('img_chat')
	--self.btChat:setTag(GameViewLayer.BT_CHAT)
	self.btChat:addTouchEventListener(btcallback)

	self.btVol = self.main_node:child('img_vol')
	--self.btVol:setTag(GameViewLayer.BT_VOL)
	self.btVol:addTouchEventListener(btcallback)

	self.btExit = ccui.Button:create("room/fanhui.png", "room/fanhui.png", "")
		:move(pointBtSwitch)
		:setName(GameViewLayer.BT_EXIT)
		:setTouchEnabled(false)
		:addTo(self)
	self.btExit:addTouchEventListener(btcallback)

	self.btExit = ccui.Button:create("room/set.png", "room/set.png", "")
		:move(pointBtSwitch)
		:setName(GameViewLayer.BT_SETTING)
		:setTouchEnabled(false)
		:addTo(self)
	self.btExit:addTouchEventListener(btcallback)


	self.btSwitch = ccui.Button:create("room/shez.png", "room/shez.png", "")
		:move(pointBtSwitch)
		:setName(GameViewLayer.BT_SWITCH)
		:addTo(self)
	self.btSwitch:addTouchEventListener(btcallback)

	self.btStart = self.main_node:child('btn_start')
	self.btStart:addTouchEventListener(btcallback)

	--[[
	self.btYqhy = self.main_node:child('btn_yqhy')
	self.btYqhy:addTouchEventListener(btcallback)
	--]]
	

	self.chatBubble = {}
	for i = 1 , cmd.GAME_PLAYER do
		local bubble = self.main_node:child('chat_bubble_' .. i):hide()
        bubble:child('place'):hide()
		self.chatBubble[i] = bubble
		self.chatBubble[i]:zorder(ZORDER_CHAT_LAYER)
	end


	self.btNotOut = self.main_node:child('btn_not_out_card')
	self.btNotOut:addTouchEventListener(btcallback)

	self.btHint = self.main_node:child('btn_hint')
	self.btHint:addTouchEventListener(btcallback)

	self.btOutCard = self.main_node:child('btn_out_card')
	self.btOutCard:addTouchEventListener(btcallback)


	self.txt_CellScore = cc.Label:createWithTTF("底注：0","common/round_body.ttf",24)
		:move(1040, display.size.height - 20)
		:setVisible(false)
		:addTo(self)
	self.txt_TableID = cc.Label:createWithTTF(LANG.DESKTOP_NUM,"common/round_body.ttf",24)
		:move(100, display.size.height - 20)
		:addTo(self)
	
	self.txt_TableID:hide()

	
	

	

	--local labAtTime = self.spriteClock:child('num')
	--labAtTime:setTag(GameViewLayer.TIMENUM)

	--用于发牌动作的那张牌
	self.animateCard = self.main_node:child('animateCard')
	local animateCard_x, animateCard_y = self.animateCard:pos()
	self.animateCardPos = cc.p(animateCard_x, animateCard_y)
	--记录开始位置和结束位置
	self.start_card = self.main_node:child('card_start');
	self.end_card = self.main_node:child('card_end');
	

	--四个玩家
	self.nodePlayer = {}
	self.clocks = {}
	self.clock_times = {}
	self.gong_score = {}
	self.zhua_score = {}
	self.order_num = {}
	self.left_card_num = {}
	self.animationPos = {}
	self.outCardPosNode = {}
	self.outCardNode = {{},{},{},{}}
	for i = 1 ,cmd.GAME_PLAYER do
		--玩家结点
		self.nodePlayer[i] = self.main_node:child('face_bg_' .. i)
		print('....', self.nodePlayer[i])
		self.nodePlayer[i]:hide()

		--昵称
		--self.nicknameConfig = string.getConfig("public/round_body.ttf", 18)
		local name = self.nodePlayer[i]:child('name')
		--name:setTag(GameViewLayer.NICKNAME)

		--score:hide()
		--时钟
		self.clocks[i] = self.nodePlayer[i]:child('clock')
		self.clock_times[i] = self.clocks[i]:child('txt_clock')

		--score
		self.gong_score[i] = self.nodePlayer[i]:child('gong'):child('txt_score')
		self.zhua_score[i] = self.nodePlayer[i]:child('zhua'):child('txt_score')

		-- order
		self.order_num[i] = self.nodePlayer[i]:child('img_order')

		self.nodePlayer[i]:child('left_card'):hide()
		-- left card
		self.left_card_num[i] = self.nodePlayer[i]:child('left_card'):child('txt_left_card')
		self.left_card_num[i]:setProperty('54', 'room/n2.png', 39, 63, '0')

		local show_card = self.main_node:child('show_card_' .. i)
		self.outCardPosNode[i] = show_card
		show_card:child('xian'):hide()
		local show_x,show_y = show_card:pos()
		self.animationPos[i] = cc.p(show_x, show_y)
	end

	self.m_node_player = {}
	for i=1, cmd.GAME_PLAYER do
		self.m_node_player[i] = self.nodePlayer[i]
	end


	--牌节点
	self.nodeCard = {}
	
	--下注的分数
	self.tableScore = {}

	self.runNode = {}
		
	--准备标志
	self.flag_ready = {}
	

	--摊牌标志
	self.flag_openCard = {}
	for i = 1, cmd.GAME_PLAYER do

		--桌面金币
		local score = self.nodePlayer[i]:child('txt_difen')
		self.tableScore[i] = score

		--准备
		self.flag_ready[i] = self.main_node:child('img_state_' .. i)
		self.flag_ready[i]:ignoreContentAdaptWithSize(true)
		self.flag_ready[i]:setLocalZOrder(5)

	end

	self.txt_cur_score = self.main_node:child('txt_cur_score');


	self:resetTablecloth()


	if yl.IS_REPLAY_MODEL then
		self.btStart:hide()
		--self.btYqhy:hide()
	else
		self.btStart:show()
		--self.btYqhy:show()
	end

	self:onResetView()

--[[
	began = 0,
    moved = 1,
	--]]
	--点击事件
	self:setTouchEnabled(true)
	self:registerScriptTouchHandler(function(eventType, x, y)
		if eventType == "ended" then
			self:onEventTouchCallback(x, y)
		end
		return true
	end)


end

function GameViewLayer:setBtnOutCardState(is_can_touch)
	if is_can_touch == false then
		self.btOutCard:texture('room/huise.png')
		self.btOutCard:setTouchEnabled(false)
	elseif is_can_touch == true then
		self.btOutCard:texture('room/btn_inroom_yellow.png')
		self.btOutCard:setTouchEnabled(true)
	end
end

function GameViewLayer:setBtnState(is_show, is_can_touch)
	--[[
	if yl.IS_REPLAY_MODEL then
		is_show = false
	end
	
	if is_can_touch and is_can_touch == 0 then
		self:perform(handler(self, self.btnNotOutCard), 0.8, 1)
		self.bCanMoveCard = false
		--self:btnNotOutCard()
		return
	end
	--]]	

	self.btHint:setVisible(is_show)
	self.btOutCard:setVisible(is_show)
	self.btNotOut:setVisible(is_show)
	
	if is_show then
		if is_can_touch == 1 then
			self.btNotOut:texture('room/huise.png')
			self.btNotOut:setTouchEnabled(false)
		elseif is_can_touch == 0 then
			self.btNotOut:texture('room/btn_inroom_yellow.png')
			self.btNotOut:setTouchEnabled(true)
		end
	end
	
end

function GameViewLayer:createSelfCard(card_num, is_show)

	if self.nodeCard[cmd.MY_VIEWID] then
		for i = 1, #self.nodeCard[cmd.MY_VIEWID] do
			self.nodeCard[cmd.MY_VIEWID][i]:removeFromParent()
		end
	end
	print('createSelfCard createSelfCard', card_num)
	self.nodeCard[cmd.MY_VIEWID] = {}

	is_show = is_show or false
	--local index = self._scene:GetMeChairID() + 1
	local vec = self._scene.anayseCard
	local start_x, start_y, diff_x, zha_x = self:calcStartPosAndDiff(card_num, vec)
	print('start_x, start_y ', start_x, start_y, diff_x, zha_x)
	local index = 1
	local cur_x = start_x - diff_x
	--打开自己的牌
    for i = 1, #vec do
		local cards = vec[i]
		local cards_len = #cards
		for j = 1, cards_len do
			local data = cards[j]
			
			local value = GameLogic:getCardValue(data)
			local color = GameLogic:getCardColor(data)
			local card = self.start_card:clone()
			self.nodeCard[cmd.MY_VIEWID][index] = card
			self.main_node:addChild(card)
			card.data = data
			card:setVisible(is_show)
			local x = cur_x + diff_x
			if j > 1 then
				x = cur_x + zha_x
			end
			card:pos(x, start_y)
			cur_x = x
			local card_name_v = value
			if data > 0x40 and cards_len >= 4 then
				card_name_v = 0x40
			elseif data > 0x40 and cards_len < 4 then
				card_name_v = data
			end
			--print('card_name_v is ', card_name_v, data, cards_len, (value > 0x40 and cards_len < 4))

			card:setName('card_' .. tostring(card_name_v) .. '-' .. tostring(j))
			local txt_num = card:child('txt_num')
			if data > 0x40 and cards_len >= 4 then
				local tmp_vec_card = {}
				table.insert(tmp_vec_card, cards)
				local result = GameLogic:getCardXian(tmp_vec_card, false)
				local xian_num = result[1].card_num
				txt_num:setString(tostring(cards_len))
				--txt_num:hide()
				local copy_num = txt_num:clone()
				copy_num:addTo(txt_num:getParent()):pos(txt_num:pos())
				copy_num:setString(tostring(xian_num))
				if j == cards_len then
					copy_num:show()
				end
				
			else
				if self._scene.is_zhuaneight then
					local tmp_vec_card = {}
					table.insert(tmp_vec_card, cards)
					local result = GameLogic:getCardXian(tmp_vec_card, self._scene.is_zhuaneight)
					local xian_num = result[1].card_num

					local copy_num = txt_num:clone()
					copy_num:addTo(txt_num:getParent()):pos(txt_num:pos())
					copy_num:setString(tostring(xian_num))
					if j == cards_len then
						copy_num:show()
					end
				else
					if j == cards_len then	
						txt_num:show()
					end
				end
				
				txt_num:setString(tostring(cards_len))
			end
			
			self:setCardTextureRect(cmd.MY_VIEWID, index, value, color)
			index = index + 1
		end
	end
	
end

function GameViewLayer:calcStartPosAndDiff(card_num, vec)
	
	local max_diff_card = 32 --self.start_card:size().width / 2
	local zha_x = 20
	local start_x,start_y = self.start_card:pos()
	local end_x, end_y = self.end_card:pos()
	start_x = 60
	end_x = display.size.width - 60
	local total_width = end_x - start_x
	local card_sub_num = 0
	--local card_tmp_diff = 25

	local diff_x = (total_width) / (card_num - 1)
	--print('zha_x is  ', zha_x, diff_x)

	if max_diff_card <= diff_x then
		start_x = display.cx - (max_diff_card * (card_num - 1) / 2)
		
		return start_x, start_y, max_diff_card, max_diff_card
	else
		if vec then
			if diff_x < zha_x then
				start_x = display.cx - (diff_x * (card_num - 1) / 2)
				return start_x, start_y, diff_x, diff_x
			end
			for i = 1, #vec do
				local cards = vec[i]
				if #cards > 1 then
					total_width = total_width - (#cards - 1) * zha_x
					card_sub_num = card_sub_num + #cards -1
				end
			end
			
			--local width_tmp = card_sub_num * zha_x
			--local width_tmp1 = diff_x * (card_num - 1 - card_sub_num)
			local left_card_num = (card_num - 1 - card_sub_num)
			diff_x = total_width / left_card_num
			if diff_x > max_diff_card then
				diff_x = max_diff_card
				start_x = display.cx - (diff_x * left_card_num + card_sub_num * zha_x) / 2
			end

			return start_x, start_y, diff_x, zha_x
		end
	end
	
	

end

function GameViewLayer:checkJieSuan()

	if self.is_not_check then
		return
	end
end


-------------------------------------------------------------------------------
-- 玩家聊天
-------------------------------------------------------------------------------
function GameViewLayer:userChat(wViewChairId, chatString)
	if not chatString or #chatString == 0 then return end

    local bubble = self.chatBubble[wViewChairId]
	local label = self.chatDetails[wViewChairId]

	self:addChatHistory(wViewChairId, chatString)

    -- 取消上次	
    if label then
		label:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	-- 创建label
	local limWidth = 24*12
	local labCountLength = cc.Label:createWithTTF(chatString,"common/round_body.ttf", 24)  
	if labCountLength:getContentSize().width > limWidth then
		label = cc.Label:createWithTTF(chatString,"common/round_body.ttf", 24, cc.size(limWidth, 0))
	else
		label = cc.Label:createWithTTF(chatString,"common/round_body.ttf", 24)
	end
    self.chatDetails[wViewChairId] = label
	label:setColor(cc.c3b(0, 0, 0))
	label:addTo(bubble)

	self:showBubble(wViewChairId, cc.size(label:size().width + 58, label:size().height + 40))
end


-------------------------------------------------------------------------------
-- 显示气泡
-------------------------------------------------------------------------------
function GameViewLayer:showBubble(wViewChairId, size, no_remove)
    local node = self.chatDetails[wViewChairId]
	--print('showBubble 11111111111111111111', node)
    if not node then return end

	--print('showBubble 11111111111111111111')
    local bubble = self.chatBubble[wViewChairId]
    local place = bubble:child('place')
    if not place._offset_right then
        place._offset_right = bubble:size().width - place:px()
    end
	bubble:size(size):stop():show():scale(0):runAction( cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) ) )

    -- 锚点在右侧的框size改变后子节点需要调整坐标，place在编辑器中设了右侧绑定，但无效，暂时先在这里修正
    if bubble:anchor().x >= 1  then
        place:px( size.width - place._offset_right )
    end

    local anchor = place:anchor()
    if node:getRotation() ~= 0 then -- 水平翻转问题修正
        anchor.x = 1 - anchor.x
    end
    node:anchor(anchor):pos( place:px(), size.height/2 )

    if not no_remove then
        node:runAction( cc.Sequence:create(
	        cc.DelayTime:create(3),
	        cc.CallFunc:create(function(ref)
                bubble:stop():runAction( cc.Sequence:create(
                    cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
                    cc.Hide:create(),
                    cc.CallFunc:create(function(sender)
                        if self.chatDetails[wViewChairId] then
    	    	            self.chatDetails[wViewChairId]:removeFromParent()
	    		            self.chatDetails[wViewChairId] = nil
                        end
                    end)
                ) )
	        end)
        ))
    end
end


-------------------------------------------------------------------------------
-- 玩家表情
-------------------------------------------------------------------------------
function GameViewLayer:userExpression(wViewChairId, wItemIndex)
	if not wItemIndex or wItemIndex < 1 then return end

	self:addChatHistory(wViewChairId, wItemIndex)

    local bubble = self.chatBubble[wViewChairId]
	local icon = self.chatDetails[wViewChairId]

	-- 取消上次
	if icon then
		icon:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	local strName = string.format("room/face_%02d.png", wItemIndex)
	icon = display.newSprite(strName)
	icon:addTo(bubble)
    self.chatDetails[wViewChairId] = icon

	self:showBubble(wViewChairId, cc.size(130, 90))
end


-------------------------------------------------------------------------------
-- 玩家语音
-------------------------------------------------------------------------------
function GameViewLayer:onUserVoiceStart(wViewChairId)
	--print('onUserVoiceStartonUserVoiceStartonUserVoiceStart')
    local bubble = self.chatBubble[wViewChairId]
	local sprite = self.chatDetails[wViewChairId]

	-- 取消上次
	if sprite then
		sprite:stop():removeFromParent()
		sprite = nil
	end

    -- 语音动画
    local sprite = helper.app.createAnimation('room/icon_voice_play', 4, 0.4, true, nil, ccui.TextureResType.localType)
	sprite:addTo(bubble)

	if wViewChairId == 4 or wViewChairId == 1 then
		sprite:setRotation(180)
	end

    self.chatDetails[wViewChairId] = sprite
	self:showBubble(wViewChairId, cc.size(130, 90), true)
end


function GameViewLayer:onUserVoiceEnded(viewId)
	--print('voice end ', viewId, self.chatDetails[viewId])
	if self.chatDetails[viewId] then
	    self.chatDetails[viewId]:removeFromParent()
	    self.chatDetails[viewId] = nil
	    self.chatBubble[viewId]:hide()
	end
end

function GameViewLayer:onResetView()
		
	self.gamePlayStatus = {}
	self.cbPreOutCard = {}
	self.out_card = {card = {}, card_num = 0, card_real_v = 0, is_single_king = false, isSingleColor = false}

	for i = 1, cmd.GAME_PLAYER do
		self.gong_score[i]:setString('0')
		self.zhua_score[i]:setString('0')
		self.left_card_num[i]:setString(tostring(cmd.GAME_CARD_NUM))
		self.order_num[i]:hide()
		--print('left card is ', cmd.GAME_CARD_NUM)
	end

	self.main_node:child('img_cur_score'):hide()
	self.txt_cur_score:hide()
	self.start_index = 0
	

	if self.runNode then
		for i = 1, #self.runNode do
			self.runNode[i]:removeFromParent()
		end
	end
	
	self.runNode = {}

	if yl.IS_REPLAY_MODEL then
		self.btStart:hide()
		--self.btYqhy:hide()
	end
	self:setBtnOutCardState(false)

	for i = 1, #self.outCardNode do
		for j = 1, #self.outCardNode[i] do
			self.outCardNode[i][j]:removeFromParent()
		end
		self.outCardNode[i] = {}
	end
	
end

function GameViewLayer:startGame()
	self.main_node:child('img_cur_score'):show()
	self.txt_cur_score:show()
end

function GameViewLayer:showJuShu()
	local room_data = PassRoom:getInstance().m_tabPriData
    -- 局数
    --local count = room_data.dwPlayCount + 1
    local limit = room_data.nPlayCount
	--print('count, limit, ', self._scene.jushu, limit)
	local cur_jushu = self._scene.jushu or 1
	self.main_node:child('title'):setString(LANG{'ROOM_JUSHU_TITLE', count = cur_jushu, limit=limit})
end

--更新用户显示
function GameViewLayer:OnUpdateUser(viewId, userItem)
	if not viewId or viewId == yl.INVALID_CHAIR then
		print("OnUpdateUser viewId is nil")
		return
	end

	print('更新用户显示', viewId)
	--[[
	if self._scene.game_end then
		return
	end
	--]]
	
	self.m_sparrowUserItem[viewId] = userItem

	
	--头像
	local head = self.nodePlayer[viewId]:child('sp_head')
	if not userItem then
		
		self.nodePlayer[viewId]:setVisible(false)
		if self.flag_ready[viewId] then
			self.flag_ready[viewId]:setVisible(false)
		end
		
		if head then
			head:setVisible(false)
		end
		
	else
		self.nodePlayer[viewId]:setVisible(true)


		local touxiang = self.nodePlayer[viewId]:child('head')

		--头像
		if not head then
			head = PopupHead:create(self, userItem, 75, ZORDER_HEAD_INFO)
			head:pos(touxiang:pos())			--初始位置
			head:setName('sp_head')
			head:addTo( self.nodePlayer[viewId] )
		else
			head:updateHead(userItem)
			--掉线头像变灰
			print('userItem.cbUserStatus == yl.US_OFFLINE', userItem.cbUserStatus == yl.US_OFFLINE)
			if userItem.cbUserStatus == yl.US_OFFLINE then
                head:runAction(cc.Sequence:create(
				        cc.DelayTime:create(5),
				        cc.CallFunc:create(function()     
							head:removeChildByName('offline')       
                            local icon = display.newSprite('word/font_offline.png'):setName('offline')
                            helper.layout.addCenter(head, icon)
				        end)
		        ))
			else
                head:removeChildByName('offline')
			end
		end
		head:show()


		--local strNickname = string.EllipsisByConfig(userItem.szNickName, 90, string.getConfig("public/round_body.ttf", 14))
		self:setNickname(viewId, userItem.szNickName)
		--print('nick name is ', userItem.szNickName)
		if self.flag_ready[viewId] then
			self.flag_ready[viewId]:setVisible(yl.US_READY == userItem.cbUserStatus)
			self.flag_ready[viewId]:texture('room/icon_room_ready.png')
		end

		local is_fangzhu = userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID
		self.nodePlayer[viewId]:child('fangzhu'):setVisible(is_fangzhu)
		self.nodePlayer[viewId]:child('fangzhu'):setLocalZOrder(10)

		if is_fangzhu == true and viewId == cmd.MY_VIEWID then
			self.game_is_start = true
		end
		--[[
		if not is_fangzhu then
			self.btYqhy:hide()
		else
			self.btYqhy:show()
		end
		--]]
		
	end
	
end

--****************************      计时器        *****************************--
function GameViewLayer:OnUpdataClockView(viewId, time)

	for i = 1, 4 do
		if not viewId or viewId == yl.INVALID_CHAIR or not time or viewId ~= i then
			self.clocks[i]:hide()
		else
			self.clocks[i]:show()
			self.clock_times[i]:setString(tostring(time))
		end
	end
	
end


--**************************      点击事件        ****************************--
--用于触发手牌的点击事件
function GameViewLayer:onEventTouchCallback(x, y)
	--按钮滚回
	if self.bBtnInOutside then
		self:onButtonSwitchAnimate(true)
	end

	-- --聊天框
	-- if self._chatLayer:isVisible() then
	-- 	self._chatLayer:showGameChat(false)
	-- end

	self.is_select = false

	--牌可点击
	if self.bCanMoveCard == true then
		for i = #self.nodeCard[cmd.MY_VIEWID], 1, -1 do
			local card = self.nodeCard[cmd.MY_VIEWID][i]
			local x2, y2 = card:getPosition()
			local size2 = card:getContentSize()
			local rect = card:getBoundingBox()
			rect.width = rect.width
			rect.height = rect.height
			--print('rect is ', rect.x, rect.y, rect.width, rect.height, x, y)
			if cc.rectContainsPoint(rect, cc.p(x, y))  then
				local is_out = false
				--local cur_card_data = GameLogic:getCardValue(card.data)
				for j = 1, #self.cbPreOutCard do
					if card.data == self.cbPreOutCard[j].data then
						is_out = true
						break
					end
				end
				
				self.cbPreOutCard = {}
				if is_out then
					break
				end
				local card_num = tonumber(card:child('txt_num'):getString())
				local card_name = card:getName()
				local card_index = string.find(card_name, '-')
				--local card_index_1 = string.find(card_name, '_')
				local card_pre_name = string.sub(card_name, 1, card_index)
				
			--	print('card_name is ', card_name, card_num)
				for i = 1, card_num do
					local out_card = self.main_node:child(card_pre_name .. tostring(i))
					table.insert(self.cbPreOutCard, out_card)
				end

				self.is_select = true
				break
			end
		end

		local tmp_data = {}
		for i = 1, #self.cbPreOutCard do
			table.insert(tmp_data, self.cbPreOutCard[i].data)
			--print('cbPreOutCard is ', self.cbPreOutCard[i].data)
		end
		local anayseCard = GameLogic:anayseCard(tmp_data, #tmp_data, false, true)
		local tmpCards = GameLogic:getCardXian(anayseCard, self._scene.is_zhuaneight)
		local guan_cards = GameLogic:GuanCard(tmpCards, self.out_card, false, self._scene.is_zhuaneight)
		--print('guan_cards vec count is ', #anayseCard, #guan_cards, #tmpCards)
		if #guan_cards <= 0 then
			self.cbPreOutCard = {}
		else
			local card_num = #guan_cards[1]
			for i = 1, #self.cbPreOutCard do
				if i > card_num then
					self.cbPreOutCard[i] = nil
				end
			end
		end

		self:updateCardPos()
	end

end

function GameViewLayer:updateCardPos()
	local card_num = #self.nodeCard[cmd.MY_VIEWID]
	--local start_x, start_y, diff_x, zha_x = self:calcStartPosAndDiff(card_num)
	local start_x,start_y = self.start_card:pos()
	local index = 1
	--打开自己的牌
	for i = 1, card_num do
		local card = self.nodeCard[cmd.MY_VIEWID][i]
		local x = self.nodeCard[cmd.MY_VIEWID][i]:pos()
		card:pos(x, start_y)
	end

	for i = 1, #self.cbPreOutCard do
		local x, y = self.cbPreOutCard[i]:pos()
		y = y + 20
		self.cbPreOutCard[i]:pos(x, y)
	end		

	self:setBtnOutCardState(#self.cbPreOutCard > 0 and true or false)

	--[[
	if #self.cbPreOutCard > 0 then
		self.btOutCard:show()
		self.btNotOut:show()
		self.btHint:show()
	else
		self.btOutCard:hide()
		self.btNotOut:hide()
		self.btHint:hide()
	end
	--]]
end



--按钮点击事件
function GameViewLayer:onButtonClickedEvent(tag,ref)
	if tag == GameViewLayer.BT_EXIT then
		if not yl.IS_REPLAY_MODEL then
			self._scene:onQueryExitGame()
		else
			self._scene:onExitRoom()
		end
	elseif tag == GameViewLayer.BT_NOT_OUT then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self:btnNotOutCard()
	elseif tag == GameViewLayer.BT_HINT then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self:btnHint()
	elseif tag == GameViewLayer.BT_OUT_CARD then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self:btnOutCard()
	elseif tag == GameViewLayer.BT_START then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self.btStart:setVisible(false)
		--self.btYqhy:setVisible(false)
		self._scene:onStartGame()
	elseif tag == GameViewLayer.BT_CHAT then
		self:showChatLayer()
	elseif tag == GameViewLayer.BT_VOL then
		print("录音结束！")
		helper.voice.stopRecord()
	elseif tag == GameViewLayer.BT_VOL_START then
		print("录音开始！")
        helper.voice.startRecord( self._scene._gameFrame )
	elseif tag == GameViewLayer.BT_VOL_CANCEL then
		print("录音取消！")
        helper.voice.cancelRecord()
	elseif tag == GameViewLayer.BT_SWITCH then
		print("BT_SWITCH")
		self:onButtonSwitchAnimate()
		--[[
	elseif tag == GameViewLayer.BT_YQHY then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self:InviteHY()
		--]]

	elseif tag == GameViewLayer.BT_SETTING then
		helper.link.toSetting( cs.game.SRC .. 'RoomSetting')
		--helper.link.toSetting()
	else
		showToast(self, LANG.FUNC_NOT_OPEN, 1)
	end
end

function GameViewLayer:btnOutCard()
	self:setBtnState(false)
	self._scene:sendOp(GameLogic.USER_OPERATE_OUTCARD, 0)
	self.cbPreOutCard = {}
	self:updateCardPos()
	self.start_index = 0
end

function GameViewLayer:clearOneOutCard(wChairID)
	local wViewChairId = self._scene:SwitchViewChairID(wChairID)
	print('show clearOneOutCard user is ', wViewChairId)
	if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
		return
	end 
	if self.outCardNode[wViewChairId] then
		for i = 1, #self.outCardNode[wViewChairId] do
			self.outCardNode[wViewChairId][i]:removeFromParent()
		end
		self.outCardNode[wViewChairId] = {}
	end
end

function GameViewLayer:gameEndShowLeftCard(wChairID, cards, card_num)
	--local anayse_card = GameLogic:anayseCard(cards, card_num)
	local wViewChairId = self._scene:SwitchViewChairID(wChairID)
	if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
		return
	end

	if card_num > 0 then
		self.flag_ready[wViewChairId]:hide()
	end
	

	local diff = 32
	
	for i = 1, #self.outCardNode[wViewChairId] do
		self.outCardNode[wViewChairId][i]:removeFromParent()
	end
	
	self.outCardNode[wViewChairId] = {}
	local start_x, start_y = self.outCardPosNode[wViewChairId]:pos()
	local zha_pos_x = 0 
	if wViewChairId == 1 or wViewChairId == 4 then
		print('out 1111111', wViewChairId)
		for i = card_num, 1, -1 do
			local node = self.outCardPosNode[wViewChairId]:clone()
			node:show()
			node:zorder(i)
			table.insert(self.outCardNode[wViewChairId], node)
			local x = start_x - (card_num - i) * diff
			node:pos(x, start_y)
			self.main_node:addChild(node)
			local data = cards[i]
			local value = GameLogic:getCardValue(data)
			local color = GameLogic:getCardColor(data)
			local png = string.format('card/card%d%02d.png', color, value)
			node:texture(png)
			
		end
	else
		print('out 2222222', wViewChairId)
		if wViewChairId == 3 then
			start_x = start_x - card_num / 2 * diff 
		end
		for i = 1, card_num do
			local node = self.outCardPosNode[wViewChairId]:clone()
			node:show()
			node:zorder(card_num - i)
			table.insert(self.outCardNode[wViewChairId], node)
			local x = start_x + (card_num - i) * diff
			node:pos(x, start_y)
			self.main_node:addChild(node)
			local data = cards[i]
			local value = GameLogic:getCardValue(data)
			local color = GameLogic:getCardColor(data)
			local png = string.format('card/card%d%02d.png', color, value)
			node:texture(png)
		end
	end
	self:showWinUser()
end

function GameViewLayer:gameEnd(score)
	for i = 1, cmd.GAME_PLAYER do repeat
		local wViewChairId = self._scene:SwitchViewChairID(i - 1)
		if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
			break
		end
		self:setUserScore(wViewChairId, score[1][i])
	until true
	end

	for i = 1, #self.nodeCard[cmd.MY_VIEWID] do
		self.nodeCard[cmd.MY_VIEWID][i]:removeFromParent()
	end
	for i = 1, #self.outCardNode do
		for j = 1, #self.outCardNode[i] do
			self.outCardNode[i][j]:removeFromParent()
		end
		self.outCardNode[i] = {}
	end
	self.nodeCard[cmd.MY_VIEWID] = {}
	--self.outCardNode = {}
	self.btHint:hide()
	self.btOutCard:hide()
	self.btNotOut:hide()
	self.txt_cur_score:setString(0)
	self.txt_cur_score:hide()
	self:onResetView()
end

function GameViewLayer:btnHint()
	--local anayseCard = GameLogic:anayseCard(self._scene.cbCardData, #tmp_data)
	local cards = GameLogic:copyTab(self._scene.cbCardData)
	local anayseCard = GameLogic:anayseCard(cards, #cards, false, true)
	--local tmpCards = GameLogic:getCardXian(self._scene.anayseCard)
	local tmpCards = GameLogic:getCardXian(anayseCard, self._scene.is_zhuaneight)
	local guan_cards = GameLogic:GuanCard(tmpCards, self.out_card, false, self._scene.is_zhuaneight)
	--dump(self.out_card)
	if #guan_cards == 0 then
		self._scene:sendOp(GameLogic.USER_OPERATE_HINT, self.start_index)
		self.cbPreOutCard = {}
		self:updateCardPos()
	else
		self.start_index = self.start_index + 1
		if self.start_index > #guan_cards then
			self.start_index = 1
		end
		local cards = guan_cards[self.start_index]
		local cards_len = #cards
		self.cbPreOutCard = {}
		for i = 1, cards_len do
			local data = cards[i]
			local value = GameLogic:getCardValue(data)
			local color = GameLogic:getCardColor(data)
			
			local card_name_v = value
			if data > 0x40 and cards_len >= 4 then
				card_name_v = 0x40
			elseif data > 0x40 and cards_len < 4 then
				card_name_v = data
			end
			local out_card = self.main_node:child('card_' .. tostring(card_name_v) .. '-' .. tostring(i))
			table.insert(self.cbPreOutCard, out_card)
		end
		self:updateCardPos()
	end
	--self._scene:sendOp(GameLogic.USER_OPERATE_HINT, self.start_index)
end

function GameViewLayer:btnNotOutCard()
	self._scene:sendOp(GameLogic.USER_OPERATE_PASS, 0)
	self.cbPreOutCard = {}
	self:updateCardPos()
	self.start_index = 0
end

function GameViewLayer:clearOutCard()
	if self.out_card then
		self.out_card.card = {}
		self.out_card.card_num = 0
		self.out_card.card_real_v = 0
		self.out_card.isSingleColor = false
		self.txt_cur_score:setString(0)
		for i = 1, #self.outCardNode do
			for j = 1, #self.outCardNode[i] do
				self.outCardNode[i][j]:removeFromParent()
			end
			self.outCardNode[i] = {}			
		end
		--self.outCardNode = {}
	end
	
end

function GameViewLayer:showOutCard(wChairID, cards, card_num, is_zha, card_xian, peiyin)
	peiyin = peiyin or GlobalUserItem.wPeiyin
	self.out_card.card = {}
	self.out_card.card_num = card_xian
	self.out_card.card_real_v = 0
	for i = 1, card_num do
		table.insert(self.out_card.card, cards[i])
	end
	
	if #self.out_card.card > 0 then
		local tmp_vec_card = {}
		table.insert(tmp_vec_card, self.out_card.card)
		local result = GameLogic:getCardXian(tmp_vec_card, self._scene.is_zhuaneight)
		self.out_card.card_real_v = result[1].card_v
		self.out_card.is_single_king = result[1].is_single_king
		self.out_card.isSingleColor = result[1].isSingleColor
	end
	


	print('card_num is ', card_num, is_zha, card_xian, wChairID)

	local wViewChairId = self._scene:SwitchViewChairID(wChairID)
	if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
		return
	end

	
	if card_num > 0 then
		--for i = 1, cmd.GAME_PLAYER do
			self.flag_ready[wViewChairId]:hide()
		--end
	end
	

	local diff = 32
	
	
	for i = 1, #self.outCardNode[wViewChairId] do
		self.outCardNode[wViewChairId][i]:removeFromParent()
	end
	
	self.outCardNode[wViewChairId] = {}
	local start_x, start_y = self.outCardPosNode[wViewChairId]:pos()
	local zha_pos_x = 0 
	if wViewChairId == 1 or wViewChairId == 4 then
		--print('out 1111111', wViewChairId)
		for i = card_num, 1, -1 do
			local node = self.outCardPosNode[wViewChairId]:clone()
			node:show()
			node:zorder(i)
			table.insert(self.outCardNode[wViewChairId], node)
			local x = start_x - (card_num - i) * diff
			node:pos(x, start_y)
			self.main_node:addChild(node)
			local data = self.out_card.card[i]
			local value = GameLogic:getCardValue(data)
			local color = GameLogic:getCardColor(data)
			local png = string.format('card/card%d%02d.png', color, value)
			node:texture(png)

			if i == card_num then
				if card_num >= 4 then
					node:child('xian'):ignoreContentAdaptWithSize(true)
					node:child('xian'):show()
					node:child('xian'):texture('word/' .. tostring(card_xian) .. 'xian.png')
				end
			end
			if i == math.floor(card_num / 2) then
				zha_pos_x = x
			end

			
		end
	else
		--print('out 2222222', wViewChairId)
		if wViewChairId == 3 then
			start_x = start_x - card_num / 2 * diff 
		end
		for i = 1, card_num do
			local node = self.outCardPosNode[wViewChairId]:clone()
			node:show()
			node:zorder(card_num - i)
			table.insert(self.outCardNode[wViewChairId], node)
			local x = start_x + (card_num - i) * diff
			node:pos(x, start_y)
			self.main_node:addChild(node)
			local data = self.out_card.card[i]
			local value = GameLogic:getCardValue(data)
			local color = GameLogic:getCardColor(data)
			local png = string.format('card/card%d%02d.png', color, value)
			node:texture(png)

			if i == 1 then
				if card_num >= 4 then
					node:child('xian'):ignoreContentAdaptWithSize(true)
					node:child('xian'):show()
					node:child('xian'):texture('word/' .. tostring(card_xian) .. 'xian.png')
				end
			end
			if i == math.floor(card_num / 2) then
				zha_pos_x = x
			end
		end
	end
	self.start_index = 0
	
	if is_zha == 1 then
		local file_name = 'room/anim/bombAni/bombAni'
		local frame_num = 19
		local action_time = 0.1
		local callback = cc.RemoveSelf:create(true)
		local sprite = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, callback, 	ccui.TextureResType.localType)
		self.main_node:addChild(sprite, 20)
		sprite:pos(zha_pos_x, start_y)
		if GlobalUserItem.bVoiceAble then
			AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/bomb.mp3")
			--helper.music.playPeiyin(self._scene.peiyin[wViewChairId], GameViewLayer.RES_PATH.."sound/bomb.mp3")
		end
	end
	
	
	--[[
	if self._scene:GetMeChairID() ~= wChairID then
		for i = 1, #self.cbPreOutCard do
			self.cbPreOutCard[i]:removeFromParent()
		end
	end
	self.cbPreOutCard = {}
	--]]

	self.bCanNextReplay = true
end

function GameViewLayer:showGongScore(score, is_add)
	is_add = is_add or true
	for i = 1, cmd.GAME_PLAYER do repeat
		local wViewChairId = self._scene:SwitchViewChairID(i - 1)
		if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
			break
		end
		if score[i] == 0 then
			break
		end
		local cur_score = tonumber(self.gong_score[wViewChairId]:getString())
		if is_add == true then
			self.gong_score[wViewChairId]:setString(tostring(cur_score + score[i]))
			local parent_node = self.gong_score[wViewChairId]:getParent()
			local x, y = self.gong_score[wViewChairId]:pos()
			y = y - 100 
			local show_score = nil
			local png_path = 'room/n5.png'
			if score[i] > 0 then
				show_score = '/' .. tostring(score[i])
			else
				png_path = 'room/n4.png'
				show_score = '/' .. tostring(-score[i])
			end
			local labAtNum = cc.LabelAtlas:_create(show_score, png_path, 25, 38, string.byte('/'))		--数字
			:setAnchorPoint(cc.p(0.5, 0.5))
			:addTo(parent_node, 20)
			:pos(x, y)
			labAtNum:runAction(cc.Sequence:create(
				cc.MoveTo:create(1.0, cc.p(x, y + 100)),
				cc.RemoveSelf:create(true)
			))
		else
			self.gong_score[wViewChairId]:setString(tostring(score[i]))
		end

	until true
	end
end

function GameViewLayer:showCardScore(cur_score, score)
	self.txt_cur_score:setString(tostring(cur_score))
	self.txt_cur_score:show()
	for i = 1, cmd.GAME_PLAYER do repeat
		local wViewChairId = self._scene:SwitchViewChairID(i - 1)
		if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
			break
		end
		local cur_score = tonumber(self.zhua_score[wViewChairId]:getString())
		local diff_score = score[i] - cur_score
		if diff_score == 0 then
			break
		end

		local parent_node = self.zhua_score[wViewChairId]:getParent()
		local x, y = parent_node:pos()
		y = y - 100 
		local show_score = nil
		local png_path = 'room/n5.png'
		if diff_score > 0 then
			show_score = '/' .. tostring(diff_score)
		else
			png_path = 'room/n4.png'
			show_score = '/' .. tostring(-diff_score)
		end
		local labAtNum = cc.LabelAtlas:_create(show_score, png_path, 25, 38, string.byte('/'))		--数字
		:setAnchorPoint(cc.p(0.5, 0.5))
		:addTo(parent_node:getParent(), 20)
		:pos(x, y)

		labAtNum:runAction(cc.Sequence:create(
			cc.MoveTo:create(1, cc.p(x, y + 100)),
			cc.RemoveSelf:create(true)
		))
		
		--local cur_score = tonumber(self.zhua_score[wViewChairId]:getString())
		self.zhua_score[wViewChairId]:setString(tostring(score[i]))
	until true
	end
end

function GameViewLayer:showWinUser()
	for i = 1, cmd.GAME_PLAYER do repeat
		if self._scene.cbWinIndex[i] > 0 then
			local wViewChairId = self._scene:SwitchViewChairID(i - 1)
			if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
				break
			end
			local img_order = self.order_num[wViewChairId]
			img_order:show()
			img_order:texture('word/' .. tostring(self._scene.cbWinIndex[i]) .. 'ming.png')
		end 
	until true
	end
end

function GameViewLayer:showZhaAnimation()
end



function GameViewLayer:openJieSuan()
	--print('open jiesuan ui')
	local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
    if room_result_layer then
		--print('open jiesuan ui', room_result_layer)
        room_result_layer:show()
    end
end

function GameViewLayer:showChatLayer()
	--print('main.ChatLayermain.ChatLayermain.ChatLayer')
	if not self._chatLayer then
		local max_history = 30
        local path = cs.app.CLIENT_SRC .. 'main.ChatLayer'
        self._chatLayer = helper.pop.popLayer(path, self, {self._scene._gameFrame, max_history}, 100, true)
        self._chatLayer:zorder(ZORDER_CHAT_LAYER)
		self._chatLayer:hide()
		
		if self._chat_histories then
            local index = 1
            if #self._chat_histories > max_history then
                index = #self._chat_histories - max_history + 1
            end
            for i=index, #self._chat_histories do
                local v = self._chat_histories[i]
                self._chatLayer:addHistory(v[1], v[2])
            end
		end
        
	end

	self._chatLayer:effectShow()
end

-------------------------------------------------------------------------------
-- 增加聊天历史记录
-------------------------------------------------------------------------------
function GameViewLayer:addChatHistory(wViewChairId, chatString)
    local useritem = self.m_sparrowUserItem[wViewChairId]
    if not useritem then return end

    if self._chatLayer and not tolua.isnull(self._chatLayer) then
        self._chatLayer:addHistory(useritem.szNickName, chatString)
    else
        if not self._chat_histories then
            self._chat_histories = {}
        end
        table.insert(self._chat_histories, {useritem.szNickName, chatString})
    end
end

-- 初始化玩家
function GameViewLayer:initPlayers()
	-- 房间人数对应的座位显示
    local player_visibles = {true, true, true, true}
    local renshu = PassRoom:getInstance():getChairCount()
    print( '人数:', renshu)
    if renshu == 3 then
        if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false then  -- true or nil
            player_visibles[1] = false
        else
            local chair_id = self._scene:GetMeChairID()
            --print('椅子chair_id:', chair_id)
            if chair_id == 0 then
                player_visibles[2] = false
            elseif chair_id == 1 then
                player_visibles[1] = false
            elseif chair_id == 2 then
                player_visibles[4] = false
            else
                player_visibles[2] = false
            end
        end
    elseif renshu == 2 then
        player_visibles[2] = false
        player_visibles[4] = false
    end
    for i = 1, cmd.GAME_PLAYER do
		self.nodePlayer[i]:setVisible( player_visibles[i])
		self.nodePlayer[i]:child('head'):hide()
		self.nodePlayer[i]:child('name'):setString('')
	end
end


function GameViewLayer:onButtonSwitchAnimate(bTakeBack)
	local fInterval = 0.15
	local spacing = 80
	local originX, originY = self.btSwitch:getPosition()
	local tag_enum = {
		GameViewLayer.BT_EXIT,
		GameViewLayer.BT_SETTING,
	}
	for i = 1, #tag_enum do
		local nCount = i
		local button = self:getChildByName(tag_enum[i])
		if not button  then break end
		button:setTouchEnabled(false)
		--算时间和距离
		local time = fInterval*nCount
		local pointTarget = cc.p(0, spacing*nCount)

		local fRotate = 720
		if not bTakeBack then 			--按钮滚出(否则滚回)
			fRotate = -fRotate
			pointTarget = cc.p(-pointTarget.x, -pointTarget.y)
		end

		button:runAction(cc.Sequence:create(
			cc.Spawn:create(cc.MoveBy:create(time, pointTarget), cc.RotateBy:create(time, fRotate)),
			cc.CallFunc:create(function()
				if not bTakeBack then
					button:setTouchEnabled(true)
					self.bBtnInOutside = true
				else
					self.bBtnInOutside = false
				end
			end)))
	end
	if not bTakeBack then
		self.btSwitch:setTouchEnabled(false)
	else
		self.btSwitch:setTouchEnabled(true)
	end
end

function GameViewLayer:gameCallBanker(callBankerViewId, bFirstTimes)
	if callBankerViewId == cmd.MY_VIEWID then
        self.btCallBanker:setVisible(true)
        self.btCancel:setVisible(true)
    end

    if bFirstTimes then
		display.newSprite()
			:move(display.center)
			:addTo(self)
			:runAction(self:getAnimate("start", true))
    	AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_START.WAV")
    end
end


function GameViewLayer:gameSendCard(firstViewId, totalCount)
	--开始发牌
	--self.spriteCardBG:setVisible(true)
	self.bCanNextReplay = false
	self.animateCard:show()
	-- 设置默认的其它玩家出牌的数据
	self.out_card = {card = {}, card_num = 0, card_real_v = 0, is_single_king = false, isSingleColor = false}
	--dump(self.nodeCard[cmd.MY_VIEWID])
	self:runSendCardAnimate(firstViewId, totalCount, totalCount)
	self.game_is_start = true
end


-------------------------------------------------------------------------------
-- 重新设置桌布
-------------------------------------------------------------------------------
function GameViewLayer:resetTablecloth()
    if GlobalUserItem.nTablecloth >= 0 and GlobalUserItem.nTablecloth <= 1 then
        self.main_node:child('bg'):texture('room/bg_room' .. GlobalUserItem.nTablecloth .. '.jpg')
    end
end

function GameViewLayer:onSettingChange()
	self:resetTablecloth()
end



function GameViewLayer:gameScenePlaying(wChairID, out_cards, real_card_num, card_xian, card_score, gong_score, table_score, zhaUser)
	--self.btYqhy:hide()
	self.btStart:hide()
	self:clearOutCard()
	
	for i = 1, cmd.GAME_PLAYER do repeat
		local wViewChairId = self._scene:SwitchViewChairID(i - 1)
		if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
			break
		end
		if i == wChairID + 1 then
			self.flag_ready[wViewChairId]:hide()
			self:clearOneOutCard(i - 1)
			break
		end
		if real_card_num[i] == 0 then
			self.flag_ready[wViewChairId]:hide()
			break
		end
		self:showOutCard(i - 1, out_cards[i], real_card_num[i], false, card_xian[i])
	until true
	end
	self.bCanMoveCard = false
	
	self:showCardScore(table_score, card_score)
	self:showGongScore(gong_score, false)
	self.out_card.card = {}
	self.out_card.card_num = card_xian[zhaUser + 1]
	for i = 1, real_card_num[zhaUser + 1] do
		table.insert(self.out_card.card, out_cards[zhaUser + 1][i])
	end

	if #self.out_card.card > 0 then
		local tmp_vec_card = {}
		table.insert(tmp_vec_card, self.out_card.card)
		local result = GameLogic:getCardXian(tmp_vec_card, self._scene.is_zhuaneight)
		self.out_card.card_real_v = result[1].card_v
		self.out_card.is_single_king = result[1].is_single_king
		self.out_card.isSingleColor = result[1].isSingleColor
	end

	

	if wChairID == self._scene:GetMeChairID() then
		self.bCanMoveCard = true
		local cards = GameLogic:copyTab(self._scene.cbCardData)
		local anayseCard = GameLogic:anayseCard(cards, #cards, false, true)
		--local tmpCards = GameLogic:getCardXian(self._scene.anayseCard)
		local tmpCards = GameLogic:getCardXian(anayseCard, self._scene.is_zhuaneight)
		local guan_cards = GameLogic:GuanCard(tmpCards, self.out_card, false, self._scene.is_zhuaneight)

		local bCanOut = 1
		if #guan_cards > 0 then
			bCanOut = 0
		end
        self:setBtnState(true, bCanOut)
	end
	self:showJuShu()
	self:startGame()
end

function GameViewLayer:setCellScore(cellscore)
	if not cellscore then
		self.txt_CellScore:setString("底注：")
	else
		self.txt_CellScore:setString("底注："..cellscore)
	end
end

function GameViewLayer:setTableID(id)
	if not id or id == yl.INVALID_TABLE then
		self.txt_TableID:setString(LANG.DESKTOP_NUM)
	else
		self.txt_TableID:setString(LANG{'DESKTOP_NUM_1', num = (id + 1) })
	end
end

function GameViewLayer:setCardTextureRect(viewId, tag, cardValue, cardColor)
	if viewId < 1 or viewId > 5 then
		print("card texture rect error!")
		return
	end
	
	local card = self.nodeCard[viewId][tag]
	local png = string.format('card/card%d%02d.png', cardColor, cardValue)
	
	card:texture(png)
	--local rectCard = card:getTextureRect()
	--rectCard.x = rectCard.width*(cardValue - 1)
	--rectCard.y = rectCard.height*cardColor
	--card:setTextureRect(rectCard)
end

function GameViewLayer:setNickname(viewId, strName)
	local name = string.EllipsisByConfig(strName, 133, self.nicknameConfig)
	local labelNickname = self.nodePlayer[viewId]:getChildByName('name')
	labelNickname:setString(name)

	-- local labelWidth = labelNickname:getContentSize().width
	-- if labelWidth > 113 then
	-- 	labelNickname:setScaleX(113/labelWidth)
	-- elseif labelNickname:getScaleX() ~= 1 then
	-- 	labelNickname:setScaleX(1)
	-- end
end

function GameViewLayer:setScore(viewId, lScore)
	local labelScore = self.nodePlayer[viewId]:child('txt_difen')
	labelScore:setString(lScore)

	local labelWidth = labelScore:getContentSize().width
	if labelWidth > 98 then
		labelScore:setScaleX(98/labelWidth)
	elseif labelScore:getScaleX() ~= 1 then
		labelScore:setScaleX(1)
	end
end

function GameViewLayer:recoverUserScore(wViewChairId, lScore)
	local txt_score = self.nodePlayer[wViewChairId]:child('txt_difen')
	txt_score:setString(lScore)
end

function GameViewLayer:setUserScore(wViewChairId, lScore)
	local txt_score = self.nodePlayer[wViewChairId]:child('txt_difen')
	local cur_score = tonumber(txt_score:getString())
	txt_score:setString(lScore + cur_score)
end

function GameViewLayer:setReadyVisible(wViewChairId, isVisible)
	if self.flag_ready[wViewChairId] then
		self.flag_ready[wViewChairId]:setVisible(isVisible)
	end
end

function GameViewLayer:setSpecialTypeVisible(wViewChairId, type)
	if self.flag_ready[wViewChairId] then
		self.flag_ready[wViewChairId]:setVisible(isVisible)
	end
end

function GameViewLayer:setOpenCardVisible(wViewChairId, isVisible)
	if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
		return
	end
	self.flag_ready[wViewChairId]:setVisible(isVisible)
	self.flag_ready[wViewChairId]:texture('word/complete.png')

	local head = self.nodePlayer[wViewChairId]:child('sp_head')
    head:removeChildByName('offline')
				
end

function GameViewLayer:setTurnMaxScore(lTurnMaxScore)

end

function GameViewLayer:setBankerUser(wViewChairId)
	if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
		return
	end
	
	local fangzhu = self.nodePlayer[wViewChairId]:child('fangzhu')

	local img = self.nodePlayer[wViewChairId]:child('img_zhuang')
	--print('bank user is ', wViewChairId, self.nodePlayer[wViewChairId], img)
	if img then
		img:setVisible(true)
		img:setLocalZOrder(10)
	end
	
end

function GameViewLayer:setUserTableScore(wViewChairId, lScore)
	if wViewChairId == yl.INVALID_CHAIR or wViewChairId == nil then
		return
	end
	local cur_fen = tonumber(self.tableScore[wViewChairId]:getString())
	self.tableScore[wViewChairId]:setString(cur_fen + lScore)
end

function GameViewLayer:showAllcards()
	for i = 1, #self.nodeCard[cmd.MY_VIEWID] do
		self.nodeCard[cmd.MY_VIEWID][i]:show()
	end
end

--发牌动作
function GameViewLayer:runSendCardAnimate(wViewChairId, nCount, totalCount)
	local run_action_time = 0.02
	--print('wViewChairId and nCount', wViewChairId, nCount)
	--[[
	for i = 1, #self.nodeCard[cmd.MY_VIEWID] do
		self.nodeCard[cmd.MY_VIEWID][i]:show()
	end
	nCount = 0
	--]]
	if nCount < 1 then
		self.bCanNextReplay = true
		self.animateCard:hide()
		if self._scene.wBankerUser == self._scene:GetMeChairID() then
			self.bCanMoveCard = true
			self:setBtnState(true, 1)
		end
		self._scene:SetGameClock(self._scene.wCurrentUser, cmd.IDI_TIME_OUT_CARD, cmd.TIME_USER_CALL_BANKER)
		return
	end
	local total_num = totalCount
	
	
	--index_card = GameLogic:mod(index_card, 4)
	
	self.animateCard:runAction(cc.Sequence:create(
		cc.MoveTo:create(run_action_time, self.animationPos[wViewChairId]),
		cc.CallFunc:create(function(ref)
			ref:move(self.animateCardPos)
			--print('index_card is ', nCount, wViewChairId)
			if wViewChairId == cmd.MY_VIEWID then
				local index_card = total_num - nCount
				index_card = math.floor(index_card / cmd.GAME_PLAYER) + 1
				--print('MY_VIEWID index_card is ', index_card, self.nodeCard[cmd.MY_VIEWID][index_card])
				self.nodeCard[cmd.MY_VIEWID][index_card]:show()
			end
			wViewChairId = wViewChairId + 1
			if wViewChairId > cmd.GAME_PLAYER then
				wViewChairId = 1
			end
			
			self:runSendCardAnimate(wViewChairId, nCount - 1, totalCount)
		end
		)
	))


	--[[
	if GlobalUserItem.bVoiceAble then
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/fapai.mp3")
	end
	--]]
end


function GameViewLayer:showBtn(type, is_show)
	--print('btn tag is ... ', type, is_show)
	local btn = self.panel_btn:getChildByTag(type)
	--btn:setBright(is_show)
	if is_show then
		btn:texture('room/paixingann.png')
	else
		btn:texture('room/paixingann02.png')
	end
end


function GameViewLayer:getAnimate(name, bEndRemove)
	local animation = cc.AnimationCache:getInstance():getAnimation(name)
	local animate = cc.Animate:create(animation)

	if bEndRemove then
		animate = cc.Sequence:create(animate, cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end))
	end

	return animate
end

--拷贝表
function GameViewLayer:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
 end

--取模
function GameViewLayer:mod(a,b)
    return a - math.floor(a/b)*b
end

--运行输赢动画
function GameViewLayer:runWinLoseAnimate(viewid, score)
	local strAnimate
	local strSymbol
	local strNum
	--[[
	if score > 0 then
		
		strSymbol = GameViewLayer.RES_PATH.."symbol_add.png"
		strNum = GameViewLayer.RES_PATH.."num_add.png"
	else
		score = -score
		
		strSymbol = GameViewLayer.RES_PATH.."symbol_reduce.png"
		strNum = GameViewLayer.RES_PATH.."num_reduce.png"
	end

	--加减
	local node = cc.Node:create()
		:move(self.nodeCardPos[viewid])
		:setAnchorPoint(cc.p(0.5, 0.5))
		:setOpacity(0)
		:setCascadeOpacityEnabled(true)
		:addTo(self, 4)

	local spriteSymbol = display.newSprite(strSymbol)		--符号
		:addTo(node)
	local sizeSymbol = spriteSymbol:getContentSize()
	spriteSymbol:move(sizeSymbol.width/2, sizeSymbol.height/2)
--]]
	local x, y = self.nodePlayer[viewid]:pos()
	local size = self.nodePlayer[viewid]:size()
	y = y - size.height / 2
	local word_pos = self.nodePlayer[viewid]:getParent():convertToWorldSpace(cc.p(x, y))
	local node = cc.Node:create()
		:move(word_pos)
		:setAnchorPoint(cc.p(0.5, 0.5))
		:setOpacity(0)
		:setCascadeOpacityEnabled(true)
		:addTo(self, 4)

	local is_win = false
	local png_path = 'room/n1.png'
	local start_char = '/'
	if score > 0 then
		score = '/' .. tostring(score)
		strAnimate = "yellow"
		is_win = true
	else
		if score == 0 then
			score = tostring(score)
		else
			score = '/' .. tostring(-score)
		end
		
		strAnimate = "blue"
		png_path = 'room/n2.png'
		start_char = '/'
	end

--[[
	for i = 1, self._scene:getPlayNum() do
		if i == self._scene:GetMeChairID() then
			self.cur_score[i] = self.cur_score[i] + score
		else
			self.cur_score[i] = self.cur_score[i] - score
		end
	end
	--]]
	local labAtNum = cc.LabelAtlas:_create(score, png_path, 21, 31, string.byte(start_char))		--数字
		:setAnchorPoint(cc.p(0.5, 0.5))
		:addTo(node)
	local sizeNum = labAtNum:getContentSize()
	
	--local num_pos = node:convertToNodeSpace(word_pos)
	--labAtNum:move(num_pos)
	--labAtNum:move(self.nodeCardPos[viewid])

	--node:setContentSize(sizeNum.width, sizeNum.height)

	--底部动画
	local nTime = 1.5
	local spriteAnimate = display.newSprite()
		:move(self.nodeCardPos[viewid])
		:addTo(self, 3)
	spriteAnimate:runAction(cc.Sequence:create(
		cc.Spawn:create(
			cc.MoveBy:create(nTime, cc.p(0, 100)),
			self:getAnimate(strAnimate)
		),
		cc.DelayTime:create(2),
		cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end)
	))

	node:runAction(cc.Sequence:create(
		cc.Spawn:create(
			cc.MoveBy:create(nTime, cc.p(0, 100)), 
			cc.FadeIn:create(nTime)
		),
		cc.DelayTime:create(2))
	)
	table.insert(self.runNode, node)
end


return GameViewLayer
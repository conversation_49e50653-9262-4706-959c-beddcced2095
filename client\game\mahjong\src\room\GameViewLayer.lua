local PopupHead = cs.app.client('system.PopupHead')
local extend = cs.app.client('system.extend')
local ExternalFun = cs.app.client('external.ExternalFun')
local GameLogic = cs.app.game('room.GameLogic')
local CardLayer = cs.app.game('room.CardLayer')
local cmd = cs.app.game('room.CMD_Game')

local CARD_PATH = 'card'

local GameViewLayer = class("GameViewLayer",function(scene)
	local gameViewLayer =  helper.app.loadCSB('RoomLayer.csb')
    return gameViewLayer
end)

-- 通用扩展导入
cs.app.client('system.GameViewLayerEx').assign(GameViewLayer)

local posPlate = {cc.p(568, 500), cc.p(227, 354), cc.p(568, 230), cc.p(910, 354)}

local HIDE_PLATE_TAG = 9001
local OUT_CARD_DISPLAY_TIME = 0.7
local ZORDER_ROOM_INFO = -1
local ZORDER_ROOM_BG = -2
local ZORDER_CHOICE = 4
local ZORDER_CHAT_BUBBLE = 3
local ZORDER_CHAT_LAYER = 10
local ZORDER_HEAD_INFO = 10
local ZORDER_ROOM_CONTINUE = 11

local MAX_CONTINUE_TIMES = 4 -- 最大连打场数，总场数是该值加1

function GameViewLayer:onInitData()
	self.cbActionCard = 0
	self.cbOutCardTemp = 0
	self.chatDetails = {}
	self.cbAppearCardIndex = {}
	self.m_bNormalState = {}
	--房卡需要
	self.m_sparrowUserItem = {}

    self:onInitDataEx() -- 需放最后
end

function GameViewLayer:onResetData()
	self._cardLayer:onResetData()
    if self._panel_continue then
        self._panel_continue:resetUsers()
    end

	self.m_sp_ting:removeAllChildren()
	self.m_sp_ting:hide()
	self.cbOutCardTemp = 0
	self.cbAppearCardIndex = {}
	local sp_flag = self:child('sp_operateflag')
	if sp_flag then
		sp_flag:removeFromParent()
	end
	self.m_card_plate:hide()
	self.m_sp_trustee_cover:hide()
    self.m_sp_trustee_panel:hide()
	for i = 1, cmd.GAME_PLAYER do
		self.m_node_player[i]:child('sp_trustee'):hide()
		self.m_node_player[i]:child('sp_zhuang'):hide()
        self.m_node_player[i]:child('sp_pos'):hide()
        self.m_node_player[i]:child('hua_node'):hide()
	end
	self:setRemainCardNum(0)
    self:setShengPaiVisible(false)
    self.sp_game_btn:child('btn_guo'):show()
    self:HideGameBtn()
end

function GameViewLayer:onExit()
	print("GameViewLayer onExit")
    self:onExitEx()
    --printf( "yl.IS_REPLAY_MODEL false" )
    yl.IS_REPLAY_MODEL = false
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
    cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
end

local this
function GameViewLayer:ctor(scene)
    print('GameViewLayer:ctor............')
    CARD_PATH = GlobalUserItem.getCardPath()    -- 重置为当前玩法的资源路径

    self:child('bg'):zorder(ZORDER_ROOM_BG)
    self:resetTablecloth()

    self:child('sp_cards_ting'):zorder(ZORDER_HEAD_INFO)

	this = self
	self._scene = scene
	self:onInitData()
	self:preloadUI()
	self:initButtons()
	self._cardLayer = CardLayer:create(self):addTo(self)							--牌图层

	--聊天泡泡
	self.chatBubble = {}
	for i = 1 , cmd.GAME_PLAYER do
		local bubble = self:child('chat_bubble_' .. i):hide():zorder(ZORDER_CHAT_BUBBLE)
        bubble:child('place'):hide()
		self.chatBubble[i] = bubble
	end

	--节点事件
	local function onNodeEvent(event)
		if event == "exit" then
			self:onExit()
		end
	end
	self:registerScriptHandler(onNodeEvent)

	self.m_node_player = {}
	for i = 1, cmd.GAME_PLAYER do
		self.m_node_player[i] = self:child('player' .. i)
		--self.m_node_player[i]:zorder(zorder):setVisible( player_visibles[i] )
		self.m_node_player[i]:child('sp_cover'):zorder(2)
		self.m_node_player[i]:child('label_name'):zorder(2)
		self.m_node_player[i]:child('sp_trustee'):zorder(1):hide()
		self.m_node_player[i]:child('sp_box'):zorder(2)
		self.m_node_player[i]:child('sp_zhuang'):zorder(2):hide()
        self.m_node_player[i]:child('sp_pos'):zorder(2):hide()
        self.m_node_player[i]:child('hua_node'):zorder(2):hide()
        self:onInitUserEx(self.m_node_player[i])
	end

	self.m_sp_ting = self:child('sp_ting'):scale(0.7):zorder(3):hide()

    --托管覆盖层
    self.m_sp_trustee_panel = self:child('panel_tuoguan'):zorder(2):hide()
    local tuoguan_ani = self.m_sp_trustee_panel:child('tuoguan_ani')
    helper.app.createAnimation('room/tuoguan_ani', 4, 1, true):addTo(self.m_sp_trustee_panel):pos(tuoguan_ani:pos())
    
    self.m_sp_trustee_cover = cc.Layer:create():hide():addTo(self, 4)
	display.newSprite(cmd.RES_PATH.."room/trustee_cover.png")
		:move(568, 105)
		:setName('sp_trustee_bg')
		:addTo(self.m_sp_trustee_cover)
    --[[
	display.newSprite(cmd.RES_PATH.."room/trustee_robot.png")
		:move(568, 108)
		:addTo(self.m_sp_trustee_cover)
	--]]
    self.m_sp_trustee_cover:setTouchEnabled(true)
	self.m_sp_trustee_cover:registerScriptTouchHandler(function(eventType, x, y)
		return self:onTrusteeTouchCallback(eventType, x, y)
	end)

	--牌盘
	self.m_card_plate = self:child('sp_plate'):zorder(3):hide():opacity(0)
    self.m_card_plate:setCascadeOpacityEnabled(false)
    local plate_size = self.m_card_plate:size()
	local card_bg = display.newSprite(CARD_PATH .. "/font_big/card_down.png")
	card_bg:pos(plate_size.width/2, plate_size.height/2):addTo(self.m_card_plate)
	local card_font = display.newSprite(CARD_PATH .. "/font_big/card_down.png")
	card_font:pos(plate_size.width/2, plate_size.height/2+12):addTo(self.m_card_plate):setName('sp_card')
    local card_cai = display.newSprite('room/icon_cai.png')
    card_cai:anchor(0,0):pos(plate_size.width/2-card_bg:size().width/2-3, plate_size.height/2-card_bg:size().height/2+24):zorder(1):addTo(self.m_card_plate):setName('sp_caishen')
	
    self.spClock = self:child('sp_clock'):scale(0.8)
	self.asLabTime = self.spClock:child('label_num'):setString("0")

    if yl.IS_LOCATION_OPEN and GlobalUserItem.bPrivateRoom then
        self:showLBSLayer()
        self:runMyAction(cc.Sequence:create(cc.DelayTime:create(1.0),
                   cc.CallFunc:create(function()
				   self:updateLabsInfo( true )
				   end)))
    end 
    self.disCD = 0
end

-- 初始化玩家
function GameViewLayer:initPlayers()
    -- 房间人数对应的座位显示
    local player_visibles = {true, true, true, true}
    local renshu = PassRoom:getInstance():getChairCount()
    --print( '人数:', renshu)
    if renshu == 3 then
        if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false then  -- true or nil
            player_visibles[1] = false
        else
            local chair_id = self._scene:GetMeChairID()
            --print('椅子chair_id:', chair_id)
            if chair_id == 0 then
                player_visibles[2] = false
            elseif chair_id == 1 then
                player_visibles[1] = false
            elseif chair_id == 2 then
                player_visibles[4] = false
            else
                player_visibles[2] = false
            end
        end
    elseif renshu == 2 then
        player_visibles[2] = false
        player_visibles[4] = false
    end
    for i = 1, cmd.GAME_PLAYER do
        self.m_node_player[i] = self:child('player' .. i)
		self.m_node_player[i]:zorder(1):setVisible( player_visibles[i])
	end
end

function GameViewLayer:preloadUI()
    --print("欢迎来到我的酒馆！")
    --导入动画
    local animationCache = cc.AnimationCache:getInstance()
    for i = 1, 12 do
    	local strColor = ""
    	local index = 0
    	if i <= 6 then
    		strColor = "white"
    		index = i
    	else
    		strColor = "red"
    		index = i - 6
    	end
		local animation = cc.Animation:create()
		animation:setDelayPerUnit(0.1)
		animation:setLoops(1)
		for j = 1, 9 do
			local file_name = cmd.RES_PATH.."Animate_sice_"..strColor..string.format("/sice_%d.png", index)
			local spFrame = cc.SpriteFrame:create(file_name, cc.rect(133*(j - 1), 0, 133, 207))
			animation:addSpriteFrame(spFrame)
		end

		local strName = "sice_"..strColor..string.format("_%d", index)
		animationCache:addAnimation(animation, strName)
	end
end

function GameViewLayer:initButtons()
	-- 按钮回调
	local btnCallback = function(ref, eventType)
        helper.app.tintClickEffect(ref, eventType)
		if eventType == ccui.TouchEventType.ended then
			self:onButtonClickedEvent(ref:getName(), ref)
		elseif eventType == ccui.TouchEventType.began and ref:getName() == 'btn_voice' then
			self:onButtonClickedEvent('btn_voice_open', ref)
		elseif eventType == ccui.TouchEventType.canceled and ref:getName() == 'btn_voice' then
			self:onButtonClickedEvent('btn_voice_cancel', ref)
		end
	end

    local holdOnBtnCallback = function(ref, eventType)
        helper.app.tintClickEffect(ref, eventType)
		if eventType == ccui.TouchEventType.began then
			self:onButtonClickedEvent(ref:getName(), ref)
		elseif eventType == ccui.TouchEventType.ended or eventType == ccui.TouchEventType.canceled then
			self:onButtonClickedEvent(ref:getName(), ref)
		end
	end

    --是提示按钮
    if yl.IS_LOCATION_OPEN and GlobalUserItem.bPrivateRoom then
        self:child('img_warning_icon'):show():addTouchEventListener(btnCallback)
    else
        self:child('img_warning_icon'):hide()
    end

	self.panel_menu = self:child('panel_menu'):zorder(5):hide() -- 先隐藏，开始后会在changeToPlaying中开启
    self.panel_menu._is_open = true
    self.panel_menu._offset_y = 255
    self:toggleMenu( self.panel_menu )

	self:child('panel_menu/btn_drop'):addTouchEventListener(btnCallback)    -- 菜单
	self:child('panel_menu/btn_setting'):addTouchEventListener(btnCallback) -- 设置
	self:child('panel_menu/btn_dismiss'):addTouchEventListener(btnCallback) -- 退出
	self:child('panel_menu/btn_rule'):addTouchEventListener(btnCallback)    -- 玩法
	
    self:child('btn_ting'):addTouchEventListener( holdOnBtnCallback )               -- 听牌按钮

	self:child('panel_menu/btn_tuoguan'):addTouchEventListener(btnCallback)   -- 托管
    self:child('panel_menu/btn_rule'):setVisible(GlobalUserItem.bPrivateRoom)
    self:child('panel_menu/btn_tuoguan'):setVisible(not GlobalUserItem.bPrivateRoom)

    --取消已经加至屏蔽层事件中，不用单独加按钮了
    --self:child('panel_tuoguan/btn_no_tuoguan'):addTouchEventListener(btnCallback) -- 托管取消按钮
    
	
    -- 回放的ui
    self.panel_replay = self:child('panel_replay'):zorder(5):hide() -- 先隐藏，开始后会在replay中开启
    self.panel_replay._is_open = true
    self.panel_replay._offset_y = 395
    self:toggleMenu( self.panel_replay )
    self:child('panel_replay/btn_replay_restart'):addTouchEventListener(btnCallback)    -- 重新开始
    self:child('panel_replay/btn_replay_play'):addTouchEventListener(btnCallback)       -- 开始
    self:child('panel_replay/btn_back'):addTouchEventListener(btnCallback)              -- 暂停
    self:child('panel_replay/btn_replay_next'):addTouchEventListener(btnCallback)       -- 下一步
    self:child('panel_replay/btn_replay_speed'):addTouchEventListener(btnCallback)      -- 加速
    self:child('panel_replay/btn_drop'):addTouchEventListener(btnCallback)              -- 菜单
    
    -- 回放界面调整
    if yl.IS_REPLAY_MODEL then
        self.panel_replay:show()
        self:updateReplayPauseStatus( self:child('panel_replay/btn_replay_play') )
        self:updateReplaySpeed( self:child('panel_replay/btn_replay_speed') )
        self.panel_replay:zorder( 1002 )
        if not self:child('touch_mask') then
            local mask = helper.pop.addMask(self, 0, false)
            mask:setName('touch_mask')
            mask:zorder( 1000 )
        end
    end

    self:child('btn_chat'):addTouchEventListener(btnCallback)               -- 聊天
    self:child('btn_continue'):hide():addTouchEventListener(btnCallback)               -- 再开一局
    self:child('btn_score'):hide():addTouchEventListener(btnCallback)               -- 再开一局

    self:child('panel_choice'):zorder(ZORDER_CHOICE):hide()
    self:child('panel_choice/template'):hide()
    self:child('panel_choice/btn_back'):addTouchEventListener( helper.app.tintClickHandler(self, self.onChoiceBack) )

	--开始
	self.btStart = self:child('btn_start')
		:zorder(2)
		:hide()
	self.btStart:addTouchEventListener(btnCallback)
	--语音
	local btVoice = self:child('btn_voice')
	btVoice:zorder(2)
	btVoice:addTouchEventListener(btnCallback)

	--游戏操作按钮
	self.sp_game_btn = self:child('sp_game_opt'):zorder(3):hide()
    self.sp_game_btn:child('btn_chou'):addTouchEventListener(btnCallback) 	--抽
	self.sp_game_btn:child('btn_chi'):addTouchEventListener(btnCallback) 	--吃
	self.sp_game_btn:child('btn_peng'):addTouchEventListener(btnCallback) 	--碰
	self.sp_game_btn:child('btn_gang'):addTouchEventListener(btnCallback) 	--杠
	self.sp_game_btn:child('btn_hu'):addTouchEventListener(btnCallback)		--胡
	self.sp_game_btn:child('btn_guo'):addTouchEventListener(btnCallback)	--过
    local btn_guo = self.sp_game_btn:child('btn_guo')
    btn_guo:px(btn_guo:px() - 30)
    self:HideGameBtn()

    -- 金币场处理
    if not GlobalUserItem.bPrivateRoom then
        self:child('panel_menu/btn_dismiss'):texture('room/btn_menu_exit_room_gray.png')
    end
end


-------------------------------------------------------------------------------
-- 重新设置桌布
-------------------------------------------------------------------------------
function GameViewLayer:resetTablecloth()
    if GlobalUserItem.nTablecloth >= 0 and GlobalUserItem.nTablecloth <= 1 then
        local good_file = string.format('room/bg_room%d_%d.jpg', tonumber(GlobalUserItem.nCurGameKind), tonumber(GlobalUserItem.nTablecloth))
        if not cc.FileUtils:getInstance():isFileExist(good_file) then
            good_file = 'room/bg_room' .. GlobalUserItem.nTablecloth .. '.jpg'
        end
        self:child('bg'):texture(good_file)
    end
end


-------------------------------------------------------------------------------
-- 设置改变,重新设置牌面缩放
-------------------------------------------------------------------------------
function GameViewLayer:onSettingChange()
    self:resetTablecloth()
    self._cardLayer:onSettingChange()
    self._priView:onSettingChange()
end


-------------------------------------------------------------------------------
-- 房间信息层zorder
-------------------------------------------------------------------------------
function GameViewLayer:getRoomInfoZOrder()
    return ZORDER_ROOM_INFO
end


-------------------------------------------------------------------------------
-- 显示为进行中状态
-------------------------------------------------------------------------------
function GameViewLayer:changeToPlaying()
    -- 显示菜单
    if yl.IS_REPLAY_MODEL then
        self.panel_replay:show()
    else
        self.panel_menu:show()
        self:onRefreshInfo()
    end
end


-------------------------------------------------------------------------------
-- table tip
-------------------------------------------------------------------------------
function GameViewLayer:onRefreshInfo()
    if not GlobalUserItem.bPrivateRoom or not self.panel_menu:isVisible() then return end

    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbCanContinue ~= 1 then return end
    
    self:child('btn_continue'):stop():hide()
    self:child('btn_score'):hide()

    local count = room_data.dwPlayCount
    local limit = room_data.nPlayCount
    if not limit then return end

    if count and count > 1 then
        self:child('btn_score'):show()
        self:child('btn_score/label_ba'):setString(LANG{'SCORE_BA', num=math.ceil(count / limit)})
        if room_data.m_nContinue < MAX_CONTINUE_TIMES and ((count - 1) % limit) == (limit - 1) then -- 总场数范围内，最后一局
            self:child('btn_continue'):stop():show():runAction(cc.RepeatForever:create(cc.Sequence:create(
                cc.ScaleTo:create(0.3, 1.1),
                cc.ScaleTo:create(0.3, 1.0)
            )))
            self:showContinue()
        else
            if self._panel_continue then
                self._panel_continue:effectClose()
            end
        end
    end
end


function GameViewLayer:toggleMenu( node )
    local panel = node
    panel._is_open = not panel._is_open
	local offset_y = panel._offset_y
    if not panel._origin_y then panel._origin_y = panel:py() end

    local btn_drop = panel:child('btn_drop')
    local target_pos = cc.p(panel:px(), panel._origin_y + (panel._is_open and 0 or offset_y))
    local target_pic = string.format('room/btn_drop_%s.png', panel._is_open and 'up' or 'down')
	panel:stop():runMyAction( cc.Sequence:create(
        cc.EaseBackOut:create( cc.MoveTo:create(0.3, target_pos) ),
        cc.CallFunc:create(function(sender) btn_drop:texture(target_pic) end)
    ) )

	return true
end

--更新用户显示
function GameViewLayer:OnUpdateUser(viewId, userItem)
	if not viewId or viewId == yl.INVALID_CHAIR then
		--print("OnUpdateUser viewId is nil")
		return
	end

	self.m_sparrowUserItem[viewId] = userItem
	--头像
	local head = self.m_node_player[viewId]:child('sp_head')
    if head then
        head:stopAllActions()
    end
    if not userItem then
		--self.m_node_player[viewId]:hide()
        self.m_node_player[viewId]:child('sp_ready'):hide()
        self.m_node_player[viewId]:child('label_score'):setString('')
        self.m_node_player[viewId]:child('label_name'):setString('')
		if head then
			head:hide()
		end
	else
		self.m_node_player[viewId]:show()
		self.m_node_player[viewId]:child('sp_ready'):setVisible( userItem.cbUserStatus == yl.US_READY )
		--头像
		if not head then
			head = PopupHead:create(self, userItem, 68, ZORDER_HEAD_INFO)
			head:setPosition(0, 18)			--初始位置
			head:setName('sp_head')
			head:addTo( self.m_node_player[viewId] )
            
            -- 月卡
            if helper.app.addAvatorCard(self.m_node_player[viewId], 0.5, cc.p(0, 18), userItem.nMonthTicketType) then
                self.m_node_player[viewId]:child('sp_box'):hide()
            end
		else
			head:updateHead(userItem)
			--掉线头像变灰
			if userItem.cbUserStatus == yl.US_OFFLINE then
				if self.m_bNormalState[viewId] then
					--convertToGraySprite(head:child('head').m_spRender)
                    head:runMyAction(cc.Sequence:create(
				            cc.DelayTime:create(10),
				            cc.CallFunc:create(function()            
                                local icon = display.newSprite('word/font_offline.png'):setName('offline')
                                helper.layout.addCenter(head, icon)
				            end)
		            ))
				end
				self.m_bNormalState[viewId] = false
			else
				if not self.m_bNormalState[viewId] then
					--convertToNormalSprite(head:child('head').m_spRender)
                    head:removeChildByName('offline')
				end
				self.m_bNormalState[viewId] = true
			end
		end
		head:show()
		--金币
		self.m_node_player[viewId]:child('label_score'):setString(userItem.nTableScore)
        self.m_node_player[viewId]:child('label_score'):setTextColor(userItem.nTableScore > 0 and cc.c3b(0,255,0) or 
                                                    (userItem.nTableScore < 0 and display.COLOR_YELLOW or cc.c3b(255,255,255)))
        --金币变化原因
        if userItem.wScoreReason == yl.SCORE_REASON_READY then
            userItem.wScoreReason = yl.SCORE_REASON_NONE
            local who = userItem.dwUserID == GlobalUserItem.dwUserID and LANG.SELF or userItem.szNickName
            helper.pop.message( LANG{'ROOM_SCORE_READY', who=who, score=userItem.nTableScore} )
        end

		--昵称
		local strNickname = string.EllipsisByConfig(userItem.szNickName, 90, string.getConfig(FONT_TTF, 14))
		self.m_node_player[viewId]:child('label_name'):setString(strNickname)

	end

    self:onUpdateUserEx(self.m_node_player[viewId], userItem)
end

function GameViewLayer:showChatLayer()
    if not self._chatLayer then
        local max_history = 30
        local path = cs.app.CLIENT_SRC .. 'main.ChatLayer'
        self._chatLayer = helper.pop.popLayer(path, self, {self._scene._gameFrame, max_history}, 100, true, true)
        self._chatLayer:zorder(ZORDER_CHAT_LAYER)
        self._chatLayer:hide()
        if self._chat_histories then
            local index = 1
            if #self._chat_histories > max_history then
                index = #self._chat_histories - max_history + 1
            end
            for i=index, #self._chat_histories do
                local v = self._chat_histories[i]
                self._chatLayer:addHistory(v[1], v[2])
            end
        end
    end

    self._chatLayer:effectShow()
end

function GameViewLayer:showContinue()
    if not self._panel_continue then
        local cls = require(cs.game.SRC .. 'room.RoomContinue')
        local layer = cls:create(self, self.m_sparrowUserItem)
        self:addChild(layer)
        self._panel_continue = layer
        layer:zorder(ZORDER_ROOM_CONTINUE)
        layer:hide()
        layer:pos(0, 0)
        layer.main_node:child('bg'):pos(self:child('btn_continue'):pos())
    end

    self._panel_continue:effectShow()
end

function GameViewLayer:updateRoomContinue(user_item, is_agree)
    self:showContinue()
    self._panel_continue:updateUser(user_item, is_agree)
end

function GameViewLayer:showLBSLayer()
    local renshu = PassRoom:getInstance():getChairCount()
    if not self._lbsLayer then
        local path = cs.app.CLIENT_SRC .. 'main.LBSLayer'
        self._lbsLayer = helper.pop.popLayer(path, self, {renshu}, 100, true, true)
        self._lbsLayer:zorder(ZORDER_CHAT_LAYER)
        self._lbsLayer:hide()
    else
        self._lbsLayer.player_num = renshu
        self._lbsLayer:effectShow()
    end
end

-------------------------------------------------------------------------------
-- 显示气泡
-------------------------------------------------------------------------------
function GameViewLayer:showBubble(wViewChairId, size, no_remove)
    local node = self.chatDetails[wViewChairId]
    if not node then return end

    local bubble = self.chatBubble[wViewChairId]
    local place = bubble:child('place')
    if not place._offset_right then
        place._offset_right = bubble:size().width - place:px()
    end
	bubble:size(size):stop():show():scale(0):runMyAction( cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) ) )

    -- 锚点在右侧的框size改变后子节点需要调整坐标，place在编辑器中设了右侧绑定，但无效，暂时先在这里修正
    if bubble:anchor().x >= 1  then
        place:px( size.width - place._offset_right )
    end

    local anchor = place:anchor()
    if node:getRotation() ~= 0 then -- 水平翻转问题修正
        anchor.x = 1 - anchor.x
    end
    node:anchor(anchor):pos( place:px(), size.height/2 )

    if not no_remove then
        node:runMyAction( cc.Sequence:create(
	        cc.DelayTime:create(3),
	        cc.CallFunc:create(function(ref)
                bubble:stop():runMyAction( cc.Sequence:create(
                    cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
                    cc.Hide:create(),
                    cc.CallFunc:create(function(sender)
                        if self.chatDetails[wViewChairId] then
    	    	            self.chatDetails[wViewChairId]:removeFromParent()
	    		            self.chatDetails[wViewChairId] = nil
                        end
                    end)
                ) )
	        end)
        ))
    end
end


-------------------------------------------------------------------------------
-- 增加聊天历史记录
-------------------------------------------------------------------------------
function GameViewLayer:addChatHistory(wViewChairId, chatString)
    local useritem = self.m_sparrowUserItem[wViewChairId]
    if not useritem then return end

    if self._chatLayer and not tolua.isnull(self._chatLayer) then
        self._chatLayer:addHistory(useritem.szNickName, chatString)
    else
        if not self._chat_histories then
            self._chat_histories = {}
        end
        table.insert(self._chat_histories, {useritem.szNickName, chatString})
    end
end


-------------------------------------------------------------------------------
-- 玩家聊天
-------------------------------------------------------------------------------
function GameViewLayer:userChat(wViewChairId, chatString)
	if not chatString or #chatString == 0 then return end

    self:addChatHistory(wViewChairId, chatString)

    local bubble = self.chatBubble[wViewChairId]
	local label = self.chatDetails[wViewChairId]

    -- 取消上次	
    if label then
		label:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	-- 创建label
	local limWidth = 24*12
	local label = cc.Label:createWithSystemFont(chatString, cs.game.FONT_NAME, 24)
	if label:size().width > limWidth then
		label = cc.Label:createWithSystemFont(chatString, cs.game.FONT_NAME, 24, cc.size(limWidth, 0))
	end
    self.chatDetails[wViewChairId] = label
	label:setColor(cc.c3b(0, 0, 0))
	label:addTo(bubble)

	self:showBubble(wViewChairId, cc.size(label:size().width + 58, label:size().height + 40))
end


-------------------------------------------------------------------------------
-- 玩家表情
-------------------------------------------------------------------------------
function GameViewLayer:userExpression(wViewChairId, wItemIndex)
	if not wItemIndex or wItemIndex < 1 then return end

    self:addChatHistory(wViewChairId, wItemIndex)

    local bubble = self.chatBubble[wViewChairId]
	local icon = self.chatDetails[wViewChairId]

	-- 取消上次
	if icon then
		icon:stop():removeFromParent()
		self.chatDetails[wViewChairId] = nil
	end

	local strName = string.format("room/face_%02d.png", wItemIndex)
	icon = display.newSprite(strName)
	icon:addTo(bubble)
    self.chatDetails[wViewChairId] = icon

	self:showBubble(wViewChairId, cc.size(130, 90))
end


-------------------------------------------------------------------------------
-- 玩家语音
-------------------------------------------------------------------------------
function GameViewLayer:onUserVoiceStart(wViewChairId)
    local bubble = self.chatBubble[wViewChairId]
	local sprite = self.chatDetails[wViewChairId]

	-- 取消上次
	if sprite then
		sprite:stop():removeFromParent()
		sprite = nil
	end

    -- 语音动画
    local sprite = helper.app.createAnimation('room/icon_voice_play', 4, 0.4, true, nil, ccui.TextureResType.localType)
	sprite:addTo(bubble)

	if wViewChairId == 4 then
		sprite:setRotation(180)
	end

    self.chatDetails[wViewChairId] = sprite
	self:showBubble(wViewChairId, cc.size(130, 90), true)
end


function GameViewLayer:onUserVoiceEnded(viewId)
	if self.chatDetails[viewId] then
	    self.chatDetails[viewId]:removeFromParent()
	    self.chatDetails[viewId] = nil
	    self.chatBubble[viewId]:hide()
	end
end


function GameViewLayer:onButtonClickedEvent(tag, ref)
    local msg = '按钮点击:' .. (tag or 'empty')
    if ref then
        msg = msg .. '位置:' ..  math.ceil(ref:px() )
    end
    self._scene:AddUserLog( msg, true )
    self._scene:sendUserLog()
    if tag == 'btn_start' then
		--print("红中麻将开始！")
		self.btStart:hide()
        if self.m_cbGameStatus ~= cmd.GAME_SCENE_PLAY then
            self._scene:sendGameStart()
        end
	elseif tag == 'btn_drop' then
		--print("菜单开关")
        if yl.IS_REPLAY_MODEL then
            self:toggleMenu( self.panel_replay )
        else
            self:toggleMenu( self.panel_menu )
        end
	elseif tag == 'btn_chat' then
		--print("聊天！")
		self:showChatLayer(self.panel_menu)
	elseif tag == 'btn_continue' then
		--print("再开一局！")
		self:showContinue()
	elseif tag == 'btn_score' then
		--print("战绩！")
		helper.link.toGroupScore()
	elseif tag == 'btn_setting' then
		--print("设置开关")
        self:toggleMenu( self.panel_menu )
		helper.link.toSetting( cs.game.SRC .. 'RoomSetting')
	elseif tag == 'btn_rule' then
		--print("玩法！")
        self:toggleMenu( self.panel_menu )
        helper.link.toRule()
	elseif tag == 'btn_dismiss' then
        self:toggleMenu( self.panel_menu )
		--print("退出！")
        if GlobalUserItem.bPrivateRoom then
            PassRoom:getInstance():queryDismissRoom()
        elseif GlobalUserItem.bCommonGold then
            helper.pop.alert(LANG.ROOM_EXIT_GOLD)
        else
            helper.pop.alert(LANG.ROOM_EXIT_DENIED)
        end
	elseif tag == 'btn_tuoguan' then
		--print("托管")
        self:toggleMenu( self.panel_menu )
		self._scene:sendUserTrustee( true ) 
    elseif tag == 'btn_no_tuoguan' then
		--print("取消托管")
		self._scene:sendUserTrustee( false )
	elseif tag == 'btn_voice' then
		--print("录音结束！")
		helper.voice.stopRecord()
	elseif tag == 'btn_voice_open' then
		--print("录音开始！")
        helper.voice.startRecord( self._scene._gameFrame )
	elseif tag == 'btn_voice_cancel' then
		--print("录音取消！")
        helper.voice.cancelRecord()
	elseif tag == 'btn_peng' then
        -- self:onButtonEventPeng()
        self:onButtonEventCheckOperate( tag, true )
	elseif tag == 'btn_gang' then
        -- self:onButtonEventGang()
        self:onButtonEventCheckOperate( tag, true )
    elseif tag == 'btn_chi' then
        -- self:onButtonEventChi()
        self:onButtonEventCheckOperate( tag, true )
    elseif tag == 'btn_chou' then
        self:onButtonEventChou()
	elseif tag == 'btn_hu' then
        self:onButtonEventHu()
	elseif tag == 'btn_guo' then
        -- self:onButtonEventGuo()
        self:onButtonEventCheckOperate( tag, true )
    elseif tag == 'btn_replay_restart' then    -- 重新开始
        --print("重新开始1！")
        PassRoom:getInstance():getNetFrame():doStartReplay()
        --print("重新开始2！")
    elseif tag == 'btn_replay_play' then    -- 开始
        self:updateReplayPauseStatus( ref, true )
    elseif tag == 'btn_replay_next' then    -- 下一步
        PassRoom:getInstance():getNetFrame():doNextReplay( nil, true )
        local replayIsPause = PassRoom:getInstance():getNetFrame().replayIsPause
        if not replayIsPause then
            self:updateReplayPauseStatus( self:child('panel_replay/btn_replay_play'), true )
        end
        --print("下一步！")
    elseif tag == 'btn_replay_speed' then   -- 加速
        self:updateReplaySpeed( ref, true )
    elseif tag == 'btn_back' then           -- 返回
        --print("返回！")
        helper.app.getFromScene('game_room_layer'):onExitRoom()
    elseif tag == 'img_warning_icon' then
        --print("定位界面")
        self:showLBSLayer()
    elseif tag == 'btn_ting' then
        --print('听牌.....')
        self:showTingCards()
	else
		--print("default")
	end
end

-- 更新回放速度
function GameViewLayer:updateReplaySpeed( node, isChange )
   local speed = PassRoom:getInstance():getNetFrame().replaySpeed
   if isChange then
        if speed == 1 then
            speed = 2
        elseif speed == 2 then
            speed = 4
        elseif speed == 4 then
            speed = 1
        end
        PassRoom:getInstance():getNetFrame().replaySpeed = speed
        --print("加速！", speed)
   end
   node:texture('room/btn_replay_speed_' .. speed .. '.png')
end

--碰事件
function GameViewLayer:onButtonEventPeng()
   --print("碰！")
   if extend.execute(self, 'onButtonEventPeng') then return end

   local cbOperateCard = {self.cbActionCard, self.cbActionCard, self.cbActionCard, 0}
   self._scene:sendOperateCard(GameLogic.WIK_PENG, cbOperateCard)
   self:HideGameBtn()
end

--杠事件
function GameViewLayer:onButtonEventGang()
	--print("杠！")
    if extend.execute(self, 'onButtonEventGang') then return end

    if self.m_gang_cards and #self.m_gang_cards > 1 then
       self.sp_game_btn:hide() -- 不能用self:HideGameBtn()
       self:showGangChoices()
    elseif #self.m_gang_cards == 1 then
	   --local cbGangCard = self._cardLayer:getGangCard(self.cbActionCard)
       local card = self.m_gang_cards[1]
	   local cbOperateCard = {card, card, card, card}
	   self._scene:sendOperateCard(GameLogic.WIK_GANG, cbOperateCard)
	   self:HideGameBtn()
    end    
end

-- 操作 事件
function GameViewLayer:onButtonEventCheckOperate( name, isCheck )
    if isCheck and not cs.game.IS_NOT_CHECK_OPERATE then
        if self.sp_game_btn:child('btn_hu'):isVisible() then
           helper.pop.alert( LANG.PASS_HU_NOTICE, function()
                self:onButtonEventCheckOperate(name, false)
           end, true )
           return
        end
    end
    if name == 'btn_peng' then
        self:onButtonEventPeng()
	elseif name == 'btn_gang' then
        self:onButtonEventGang()
    elseif name == 'btn_chi' then
        self:onButtonEventChi()
    elseif name == 'btn_guo' then
        self:onButtonEventGuo()
    end
end

-- 胡牌 事件
function GameViewLayer:onButtonEventHu()
    --print("胡！")
	local cbOperateCard = {self.cbActionCard, 0, 0, 0}
	self._scene:sendOperateCard(GameLogic.WIK_CHI_HU, cbOperateCard)
	self:HideGameBtn()
end

-- 过牌 事件
function GameViewLayer:onButtonEventGuo()
    --print("过！")
    local cbOperateCard = {0, 0, 0, 0}
	self._scene:sendOperateCard(GameLogic.WIK_NULL, cbOperateCard)
	self:HideGameBtn()
end

-- 抽牌 事件
function GameViewLayer:onButtonEventChou()
    --print('抽牌')
    local children = self._cardLayer.nodeBpBgCard[ cmd.MY_VIEWID ]:getChildren()
    local weaves = {}
    for index, child in pairs( children ) do
        table.insert(weaves, child.cardInfo)
    end
    --dump(weaves, '抽牌',  9)
    local cardsWaves = GameLogic.AnyseChouCards( self._cardLayer.cbCardData, weaves )
    if #cardsWaves > 1 then
       self.sp_game_btn:hide() -- 不能用self:HideGameBtn()
       self:showAllChoices( cardsWaves )
    elseif #cardsWaves == 1 then
       self._scene:sendOperateCard(cardsWaves[1].operate, cardsWaves[1].cards)
       self:HideGameBtn()  
    end
end

--吃事件
function GameViewLayer:onButtonEventChi()
	--print("吃！")
    if extend.execute(self, 'onButtonEventChi') then return end

    --发送碰牌
    self.m_chi_card = self.cbActionCard
    local multiple = {}
    local index = 0
    if bit:_and(self.cbActionMask, GameLogic.WIK_RIGHT) > 0 then      --右吃
	    index = index + 1
        multiple[index] = GameLogic.WIK_RIGHT
	end
    if bit:_and(self.cbActionMask, GameLogic.WIK_CENTER) > 0 then     --中吃
        index = index + 1
        multiple[index] = GameLogic.WIK_CENTER
	end
	if bit:_and(self.cbActionMask, GameLogic.WIK_LEFT) > 0 then       --左吃
        index = index + 1
        multiple[index] = GameLogic.WIK_LEFT
	end
    if index > 1 then
        self.sp_game_btn:hide() -- 不能用self:HideGameBtn()
        self:showChiChoices( multiple )
    else
        -- 数值操作的牌
	    local chi_card = GameLogic.getUseCardData(self.m_chi_card, self.m_caishen)
        -- 真实的显示的牌
        local show_chi_card = self.m_chi_card
        local chi_operate = multiple[1]
        local a, b = GameLogic.getChiOthers( chi_card, chi_operate, self.m_caishen )
        local cbOperateCard = { show_chi_card, a, b }
        if chi_operate == GameLogic.WIK_CENTER then
            cbOperateCard = { a, show_chi_card, b }
		elseif chi_operate == GameLogic.WIK_RIGHT then
            cbOperateCard = { a, b, show_chi_card } 
        end
        self._scene:sendOperateCard(chi_operate, cbOperateCard)
        self:HideGameBtn()
    end 
end

-- 更新回放状态
function GameViewLayer:updateReplayPauseStatus( node, isChange )
   local replayIsPause = PassRoom:getInstance():getNetFrame().replayIsPause
   if replayIsPause then
      if isChange then
          PassRoom:getInstance():getNetFrame().replayIsPause = false
          --print("开始！")    
      end
   else    
       if isChange then
           PassRoom:getInstance():getNetFrame().replayIsPause = true
           --print("暂停！")       
       end  
   end

   if PassRoom:getInstance():getNetFrame().replayIsPause then
        node:texture('room/btn_replay_play.png')
   else
        node:texture('room/btn_replay_pause.png')
   end
end

-------------------------------------------------------------------------------
-- 显示吃选
-------------------------------------------------------------------------------
function GameViewLayer:showChiChoices( multiple )
    local callback = function(owner, sender)
		owner._scene:sendOperateCard(sender.chi_operate, sender.cards)
		owner:HideGameBtn()
        owner:child('panel_choice'):hide()
    end
    local panel_choice = self:child('panel_choice'):show()
    local template = panel_choice:child('template')
    local container = panel_choice:child('container')
    local size = container:size()
    container:removeAllChildren()

    local addCards = function(cards, item)
        for j, card_data in ipairs(cards) do
            local card = cs.game.util.createCard(card_data, cmd.MY_VIEWID, 'pile')
            local x, y = helper.layout.calcEvenPos(item, card, j, 3, 0)
            card:pos(x, item:size().height / 2):addTo(item):setTag(j)
        end
    end

    local last_item = nil
    -- 数值操作的牌
	local chi_card = GameLogic.getUseCardData(self.m_chi_card, self.m_caishen)
    -- 真实的显示的牌
    local show_chi_card = self.m_chi_card
    for index, chi_operate in ipairs(multiple) do
        local item = template:clone():show()
        item:setCascadeColorEnabled(true)
        item:addTouchEventListener( helper.app.tintClickHandler(self, callback) )
        local x, y = helper.layout.calcEvenPos(container, item, index, #multiple, -50)
        item:pos(x, size.height/2):addTo(container)
        item:scale(0):runMyAction( cc.Sequence:create(
            cc.DelayTime:create(0.1 * (index - 1)),
            cc.ScaleTo:create(0.2, 1)
        ) )
		item.chi_operate = chi_operate
        last_item = item

		local a, b = GameLogic.getChiOthers(chi_card, chi_operate, self.m_caishen)
		local flag_card = nil
		if chi_operate == GameLogic.WIK_LEFT then
            addCards({show_chi_card, a, b}, item)
			flag_card = item:getChildByTag(1)
            local cards = { show_chi_card, a, b }
            item.cards = cards
		elseif chi_operate == GameLogic.WIK_CENTER then
            addCards({a, show_chi_card, b}, item)
			flag_card = item:getChildByTag(2)
            local cards = { a, show_chi_card, b }
            item.cards = cards
		elseif chi_operate == GameLogic.WIK_RIGHT then
            addCards({a, b, show_chi_card}, item)
			flag_card = item:getChildByTag(3)
            local cards = { a, b, show_chi_card }
            item.cards = cards
        end
		flag_card:setColor(cc.c3b(200, 200, 200))
    end

    panel_choice:child('btn_back'):px(last_item:px() + last_item:size().width * 0.75 )
end

-------------------------------------------------------------------------------
-- 显示杠选
-------------------------------------------------------------------------------
function GameViewLayer:showGangChoices()
    local callback = function(owner, sender)
		local cards = {sender.card, sender.card, sender.card, sender.card}
		owner._scene:sendOperateCard(GameLogic.WIK_GANG, cards)
		owner:HideGameBtn()
        owner:child('panel_choice'):hide()
    end
    local panel_choice = self:child('panel_choice'):show()
    local template = panel_choice:child('template')
    local container = panel_choice:child('container')
    local size = container:size()
    container:removeAllChildren()
    local last_item = nil
    for i, card_data in ipairs(self.m_gang_cards) do
        local item = template:clone():show()
        item.card = card_data
        item:setCascadeColorEnabled(true)
        item:addTouchEventListener( helper.app.tintClickHandler(self, callback) )
        local card = cs.game.util.createCard(card_data, cmd.MY_VIEWID, 'pile')
        card:pos(item:size().width / 2, item:size().height / 2):addTo(item)
        local x, y = helper.layout.calcEvenPos(container, item, i, #self.m_gang_cards, -50)
        item:pos(x, size.height/2):addTo(container)
        item:scale(0):runMyAction( cc.Sequence:create(
            cc.DelayTime:create(0.1 * (i - 1)),
            cc.ScaleTo:create(0.2, 1)
        ) )
        last_item = item
    end

    panel_choice:child('btn_back'):px(last_item:px() + last_item:size().width/2 + 50)
end

-------------------------------------------------------------------------------
-- 显示碰选
-------------------------------------------------------------------------------
function GameViewLayer:showAllChoices( cardsWaves )
    local callback = function(owner, sender)
		owner._scene:sendOperateCard(sender.operate, sender.cards)
		owner:HideGameBtn()
        owner:child('panel_choice'):hide()
    end
    local panel_choice = self:child('panel_choice'):show()
    local template = panel_choice:child('template')
    local container = panel_choice:child('container')
    local size = container:size()
    container:removeAllChildren()

    local addCards = function(cards, item)
        for j, card_data in ipairs(cards) do
            if card_data > 0 then
                local card = cs.game.util.createCard(card_data, cmd.MY_VIEWID, 'pile')
                local x, y = helper.layout.calcEvenPos(item, card, j, 3, 0)
                card:pos(x, item:size().height / 2):addTo(item):setTag(j)
            end
        end
    end
    --dump(cardsWaves, '显示碰选', 9)
    local last_item = nil
    -- 数值操作的牌
	--local chi_card = GameLogic.getUseCardData(self.m_chi_card, self.m_caishen)
    -- 真实的显示的牌
    --local show_chi_card = self.m_chi_card
    for index, cardsWave in ipairs(cardsWaves) do
        local item = template:clone():show()
        item:setCascadeColorEnabled(true)
        item:addTouchEventListener( helper.app.tintClickHandler(self, callback) )
        local curIndex = index - 1
        local col = math.mod(curIndex, 3) + 1
        local row = math.floor(curIndex/3)
        local max = math.min(#cardsWaves, 3) 
        local x, y = helper.layout.calcEvenPos(container, item, col, max, -50)
        item:pos(x, size.height/2 + row * (item:size().height + 10) ):addTo(container)
        item:scale(0):runMyAction( cc.Sequence:create(
            cc.DelayTime:create(0.1 * (curIndex)),
            cc.ScaleTo:create(0.2, 1)
        ))
        if last_item == nil or last_item:px() < item:px() then
            last_item = item
        end
        addCards(cardsWave.cards, item)
	    flag_card = item:getChildByTag(1)
		item.cards = cardsWave.cards
        item.operate = cardsWave.operate    
    end
    panel_choice:child('btn_back'):px(last_item:px() + last_item:size().width/2 )
end

-------------------------------------------------------------------------------
-- 吃杠选择返回
-------------------------------------------------------------------------------
function GameViewLayer:onChoiceBack()
    self:child('panel_choice'):hide()
    self.sp_game_btn:show()
end


--计时器刷新
function GameViewLayer:OnUpdataClockView(viewId, time)
	local res = string.format("room/icon_clock_%d.png", (not viewId or viewId == yl.INVALID_CHAIR) and 0 or viewId)
	self.spClock:texture(res)
	self.asLabTime:setString(time or 0)
end

--设置全部的手牌
function GameViewLayer:setAllCardData( data )
    self._cardLayer:setAllCardData( data )
end

--开始
function GameViewLayer:gameStart( cmd_data, cbCardData, cbCardCount )
    local sice1 = cmd_data.cbCurSice[1][1]
    local sice2 = cmd_data.cbCurSice[1][2]
    self._priView:updateSice( false )
    if sice1 > 0 and sice2 > 0 then
        self:runSiceAnimate(sice1, sice2, function()
            self._priView:updateSice( true, sice1, sice2 )
        end) 
    end
    self:showShuffleEffect() 
    self._cardLayer:sendCard( cmd_data, cbCardData, cbCardCount)
end
--用户出牌
function GameViewLayer:gameOutCard(viewId, card)
	self:showCardPlate(viewId, card)
	self._cardLayer:removeHandCard(viewId, {card}, true)
	self.cbOutCardTemp = card
	self.cbOutUserTemp = viewId
end

--用户抓牌
function GameViewLayer:gameSendCard( viewId, cmd_data )
    if not cs.game.DISCARD_IMMEDIATELY then
	    --把上一个人打出的牌丢入弃牌堆
	    if self.cbOutCardTemp ~= 0 then
		    self._cardLayer:discard(self.cbOutUserTemp, self.cbOutCardTemp)
	    end

	    --清理之前的出牌(如果是立即出现弃牌，则不需处理，因为showCardPlate内部会自动隐藏)
        self.m_card_plate:perform(function()
		    self:showCardPlate(nil)
		    --self:showOperateFlag(nil) 自己会消失
	    end, 0.5, nil, HIDE_PLATE_TAG)
    end
	self.cbOutUserTemp = nil
	self.cbOutCardTemp = 0

	--当前的人抓牌
	self._cardLayer:catchCard(viewId, cmd_data)
end

--摇骰子
function GameViewLayer:runSiceAnimate(cbSiceCount1, cbSiceCount2, callback)
    if cbSiceCount1 <= 0 or cbSiceCount2 <= 0 then
        return 
    end
	local str1 = string.format("sice%d", cbSiceCount1)
	local str2 = string.format("sice%d", cbSiceCount2)
	local siceX1 = 568 - 320 + math.random(640) - 35
	local siceY1 = 375 - 120 + math.random(240) + 43
	local siceX2 = 568 - 320 + math.random(640) - 35
	local siceY2 = 375 - 120 + math.random(240) + 43
    local sice = helper.app.createAnimation('common/shaizi/'..str1, 31, 1.55, false)
	sice:move(siceX1, siceY1)
		:setName('sp_sice1')
		:addTo(self, 0)
        :zorder(1000)
		:runMyAction(cc.Sequence:create(
			--self:getAnimate(str1),
			cc.DelayTime:create(3.1),
			cc.CallFunc:create(function(ref)
				ref:removeFromParent()
			end)))
    sice = helper.app.createAnimation('common/shaizi/'..str2, 31, 1.55, false)
	sice:move(siceX2, siceY2)
		:setName('sp_sice2')
		:addTo(self, 0)
        :zorder(1000)
		:runMyAction(cc.Sequence:create(
			--self:getAnimate(str2),
			cc.DelayTime:create(3.1),
			cc.CallFunc:create(function(ref)
				ref:removeFromParent()
				if callback then
					callback()
				end
			end)))
	self._scene:PlaySound(cmd.RES_PATH.."sound/DRAW_SICE.mp3")
end

function GameViewLayer:sendCardFinish()
	local spSice1 = self:child('sp_sice1')
	if spSice1 then
		spSice1:removeFromParent()
	end
	local spSice2 = self:child('sp_sice2')
	if spSice2 then
		spSice2:removeFromParent()
	end	
	self._scene:sendCardFinish()
end

function GameViewLayer:gameConclude()
    self.m_shuffle_player = '' -- 本局洗牌人重置

    for i = 1, cmd.GAME_PLAYER do
		self:setUserTrustee(i, false)
	end
	self._cardLayer:gameEnded()

    -- 金币场处理
    if not GlobalUserItem.bPrivateRoom then 
        self.panel_menu:hide()
        --self._priView:onGameConclude()
    end
end


-------------------------------------------------------------------------------
-- 显示操作按钮
-------------------------------------------------------------------------------
function GameViewLayer:showGameBtn(show_buttons)
    -- 排序
    local buttons = {}
    for btn, order in pairs(show_buttons) do
        table.insert(buttons, {btn, order})
    end
    table.sort(buttons, function(a, b) return a[2] < b[2] end)

    local gap = 30
    local btn_guo = self.sp_game_btn:child('btn_guo')
    local start_x = btn_guo:px() - btn_guo:size().width/2 - 85  -- -85是让“过”和其它按钮空开一点

    -- 从“过”位置往左依次显示，计算合适的位置
    local count = #buttons
    for index, v in ipairs(buttons) do
        local btn = v[1]:show()
        --local x = helper.layout.calcEvenPos(self.sp_game_btn, btn, index, count, 5)
        start_x = start_x - btn:size().width/2 - gap
        btn:px(start_x)
        start_x = start_x - btn:size().width/2
        self._scene:AddUserLog('按钮:' .. btn:getName()  .. math.ceil(start_x) )
    end
    self._scene:sendUserLog()
    self.sp_game_btn:show()
    self.b_is_game_btn_ok = true
end


-------------------------------------------------------------------------------
-- 隐藏操作按钮
-------------------------------------------------------------------------------
function GameViewLayer:HideGameBtn()
	for _, bt in ipairs{ self.sp_game_btn:child('btn_chi, btn_peng, btn_gang, btn_hu, btn_chou') } do
		bt:hide()
	end
	self.sp_game_btn:hide()
end


-------------------------------------------------------------------------------
-- 识别动作掩码
-------------------------------------------------------------------------------
function GameViewLayer:recognizecbActionMask(cbActionMask, cbCardData, cbGangCards)
	--print("收到提示操作：", cbActionMask, cbCardData)
    self._scene:AddUserLog('显示操作:' .. cbActionMask .. '牌:', cbCardData, true)
    self.cbActionMask = cbActionMask
    -- 杠是否有多选
    self.m_gang_cards = cbGangCards
    --dump(self.m_gang_cards, '杠1', 9)
    if self.m_gang_cards then
        for i=#self.m_gang_cards, 1, -1 do
            if self.m_gang_cards[i] == 0 then
                table.remove(self.m_gang_cards, i)
            end
        end
        if #self.m_gang_cards == 0 then 
            self.m_gang_cards = nil
        end
    end
    --dump(self.m_gang_cards, '杠2', 9)
	if cbActionMask == GameLogic.WIK_NULL or cbActionMask == 32 then
		assert("false")
		return false
	end

	if self._cardLayer:isUserMustWin() then
		--必须胡牌的情况
		self.sp_game_btn:child('btn_guo'):hide()
	end

	if cbCardData then
		self.cbActionCard = cbCardData
	end

    -- 需显示的按钮列表
    local show_buttons = {}
    --print('抽牌', bit:_and(cbActionMask, GameLogic.WIK_CHOU_PAI), cbActionMask, GameLogic.WIK_CHOU_PAI)
	if bit:_and(cbActionMask, GameLogic.WIK_CHOU_PAI) > 0 then   --抽牌
		show_buttons[ self.sp_game_btn:child('btn_chou') ] = 4
	end
	if bit:_and(cbActionMask, GameLogic.WIK_CHI_HU) > 0 then     --胡
		show_buttons[ self.sp_game_btn:child('btn_hu') ] = 5
	end
	if bit:_and(cbActionMask, GameLogic.WIK_LISTEN) > 0 then     --听

	end
	if bit:_and(cbActionMask, GameLogic.WIK_GANG) > 0 then       --杠
        if not self.m_gang_cards then
            --print('no gang card')
            return false
        end
		show_buttons[ self.sp_game_btn:child('btn_gang') ] = 3
	end
	if bit:_and(cbActionMask, GameLogic.WIK_PENG) > 0 then       --碰
		if self._cardLayer:isUserCanBump() then
		    show_buttons[ self.sp_game_btn:child('btn_peng') ] = 2
		end
	end
	if bit:_and(cbActionMask, GameLogic.WIK_RIGHT) > 0 then      --右 吃
		show_buttons[ self.sp_game_btn:child('btn_chi') ] = 1
	end
	if bit:_and(cbActionMask, GameLogic.WIK_CENTER) > 0 then     --中吃
        show_buttons[ self.sp_game_btn:child('btn_chi') ] = 1
	end
	if bit:_and(cbActionMask, GameLogic.WIK_LEFT) > 0 then       --左吃
        show_buttons[ self.sp_game_btn:child('btn_chi') ] = 1
	end
	self:showGameBtn( show_buttons )
	self._scene:SetGameOperateClock()
	return true
end


function GameViewLayer:getAnimate(name, bEndRemove)
	local animation = cc.AnimationCache:getInstance():getAnimation(name)
	local animate = cc.Animate:create(animation)

	if bEndRemove then
		animate = cc.Sequence:create(animate, cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end))
	end

	return animate
end
--设置听牌提示
function GameViewLayer:setListeningCard(cbCardData)
    if not cs.game.TING_PROMPT then return end

	if cbCardData == nil then
		self.m_sp_ting:hide()
		return
	end
	assert(type(cbCardData) == "table")
	self.m_sp_ting:removeAllChildren()
	self.m_sp_ting:show()

	local cbCardCount = #cbCardData
	local bTooMany = (cbCardCount >= 16)
	--拼接块
	local width = 44
	local height = 67
	local posX = 327
	local fSpacing = 100
	if not bTooMany then
		for i = 1, fSpacing*cbCardCount do
			display.newSprite("#sp_listenBg_2.png")
				:move(posX, 46.5)
				:setAnchorPoint(cc.p(0, 0.5))
				:addTo(self.m_sp_ting)
			posX = posX + 1
			if i > 700 then
				break
			end
		end
	end
	--尾块
	display.newSprite("#sp_listenBg_3.png")
		:move(posX, 46.5)
		:setAnchorPoint(cc.p(0, 0.5))
		:addTo(self.m_sp_ting)
	--可胡牌过多，屏幕摆不下
	if bTooMany then
		local cardBack = cs.game.util.createCard(53, 5)
			:move(183 + 40, 46)
			:addTo(self.m_sp_ting)

		local strFilePrompt = ""
		local spListenCount = nil
		if cbCardCount == 28 then 		--所有牌
			strFilePrompt = "#389_sp_listen_anyCard.png"
		else
			strFilePrompt = "#389_sp_listen_manyCard.png"
			spListenCount = cc.Label:createWithTTF(cbCardCount.."", FONT_TTF, 30)
		end

		local spPrompt = display.newSprite(strFilePrompt)
			:move(183 + 110, 46)
			:setAnchorPoint(cc.p(0, 0.5))
			:addTo(self.m_sp_ting)
		if spListenCount then
			spListenCount:move(70, 12):addTo(spPrompt)
		end

		-- cc.Label:createWithTTF("厉害了word哥！你可以胡的牌太多，摆不下了....", FONT_TTF, 50)
		-- 	:move(260, 40)
		-- 	:setAnchorPoint(cc.p(0, 0.5))
		-- 	:setColor(cc.c3b(0, 0, 0))
		-- 	:addTo(self.m_sp_ting, 1)
	end
	--牌、番、数
	self.cbAppearCardIndex = GameLogic.DataToCardIndex(self._scene.cbAppearCardData)
	for i = 1, cbCardCount do
		if bTooMany then
			break
		end
		local tempX = fSpacing*(i - 1)
		--local rectX = self._cardLayer:switchToCardRectX(cbCardData[i])
		local cbCardIndex = GameLogic.SwitchToCardIndex(cbCardData[i])
		local nLeaveCardNum = 4 - self.cbAppearCardIndex[cbCardIndex]
		--牌底
		local card = cs.game.util.createCard( cbCardData[i], 5 )
			:move(183 + tempX, 46)
			:addTo(self.m_sp_ting)
		cc.Label:createWithTTF("1", FONT_TTF, 16)		--番数
			:move(220 + tempX, 61)
			:setColor(cc.c3b(254, 246, 165))
			:addTo(self.m_sp_ting)
		display.newSprite("#sp_listenTimes.png")
			:move(244 + tempX, 61)
			:addTo(self.m_sp_ting)
		cc.Label:createWithTTF(nLeaveCardNum.."", FONT_TTF, 16) 		--剩几张
			:move(220 + tempX, 31)
			:setColor(cc.c3b(254, 246, 165))
			:setTag(cbCardIndex)
			:addTo(self.m_sp_ting)
		display.newSprite("#sp_listenNum.png")
			:move(244 + tempX, 31)
			:addTo(self.m_sp_ting)
	end
end

--减少可听牌数
function GameViewLayer:reduceListenCardNum(cbCardData)
    if not cs.game.TING_PROMPT then return end

	local cbCardIndex = GameLogic.SwitchToCardIndex(cbCardData)
	if #self.cbAppearCardIndex == 0 then
		self.cbAppearCardIndex = GameLogic.DataToCardIndex(self._scene.cbAppearCardData)
	end
	self.cbAppearCardIndex[cbCardIndex] = self.cbAppearCardIndex[cbCardIndex] + 1
	local labelLeaveNum = self.m_sp_ting:getChildByTag(cbCardIndex)
	if labelLeaveNum then
		local nLeaveCardNum = 4 - self.cbAppearCardIndex[cbCardIndex]
		labelLeaveNum:setString(nLeaveCardNum.."")
	end
end

function GameViewLayer:switchChairViewID(view_id)
    for chair = 0, cmd.GAME_PLAYER - 1 do
        if self._scene:SwitchViewChairID(chair) == view_id then
            return chair
        end
    end
end

function GameViewLayer:setBanker(view_id, lian_zhuang, pos)
	if view_id < 1 or view_id > cmd.GAME_PLAYER then
		--print("chair id is error!")
		return false
	end

    local zhuang_chair_id = self:switchChairViewID(view_id)

    -- 游戏配置
    local renshu = PassRoom:getInstance():getChairCount()
    for i = 1, cmd.GAME_PLAYER do
    	local sp_zhuang = self.m_node_player[i]:child('sp_zhuang')
        local sp_pos = self.m_node_player[i]:child('sp_pos')
        local chair_id = self:switchChairViewID(i)
        if chair_id then
            cs.game.util.setZhuang(sp_zhuang, zhuang_chair_id, chair_id, lian_zhuang)
            cs.game.util.setFeng(sp_pos, pos[chair_id + 1])
        end
    end

	return true
end

-- 刷新分数
function GameViewLayer:updateOtherScore(score_type, player_score, player_desc)
    local renshu = PassRoom:getInstance():getChairCount()
    for i = 1, cmd.GAME_PLAYER do
        local update_score = player_score[i]
        local player = self.m_node_player[i]
        if score_type == yl.ScoreType.HUA then
    	    --self.m_node_player[i]:child('hua_node'):show()
            --self.m_node_player[i]:child('hua_node/label_hua'):setString(score)
        elseif score_type == yl.ScoreType.GANG or score_type == yl.ScoreType.FOLLOW then
            if update_score ~= 0 and update_score then
                local score = player:child('label_score'):getString()
                score = tonumber(score) + update_score
                player:child('label_score'):setString(score)
                player:child('label_score'):setTextColor(score > 0 and cc.c3b(0,255,0) or 
                                                    (score < 0 and cc.c3b(255,0,0) or cc.c3b(255,255,255)))
                self:popPlayerScore(i, update_score)
            end
        end
    end
end

function GameViewLayer:setUserTrustee(viewId, bTrustee)
	self.m_node_player[viewId]:child('sp_trustee'):setVisible(bTrustee)
	if viewId == cmd.MY_VIEWID then
		self.m_sp_trustee_cover:setVisible(bTrustee)
        self.m_sp_trustee_panel:setVisible(bTrustee)
	end
end

--设置房间信息
function GameViewLayer:setRoomInfo(tableId, chairId)
end

function GameViewLayer:onTrusteeTouchCallback(event, x, y)
	if not self.m_sp_trustee_cover:isVisible() then return false end
    if event ~= 'began' then return true end -- 防止因网络延时未解除托管，而后续move，end等事件持续触发

    self._scene:sendUserTrustee( false )
    return true -- 返回true表示吞噬这个点
    --[[
	local rect = self.m_sp_trustee_cover:child('sp_trustee_bg'):getBoundingBox()
	if cc.rectContainsPoint(rect, cc.p(x, y)) then
		return true
	else
		return false
	end
    --]]
end
--设置剩余牌
function GameViewLayer:setRemainCardNum(num)
    if self._priView then
        self._priView:setRemainCardNum(num)
    end
end

function GameViewLayer:setShengPaiVisible(num)
    if self._priView then
        self._priView:setShengPaiVisible(num)
    end
end

--牌托
function GameViewLayer:showCardPlate(viewId, cbCardData, no_discard)
	if nil == viewId then
		self.m_card_plate:hide()
		return
	end 

    if not cs.game.DISCARD_IMMEDIATELY then
        self.m_card_plate:stop(HIDE_PLATE_TAG)
        --print('no DISCARD_IMMEDIATELY')
	    local color, value = cs.game.util.splitCardValue(cbCardData)
	    local file_name = CARD_PATH .. "/font_big/font_" .. color .. "_" .. value .. ".png"
        self.m_card_plate:child('sp_card'):texture(file_name)
        self.m_card_plate:child('sp_caishen'):setVisible( GameLogic.IsMagicCard( cbCardData ) )
	    self.m_card_plate:move(posPlate[viewId]):show()

    else
        --print('DISCARD_IMMEDIATELY')
        if cs.game.IS_FAST_OUT_CARD then
            local sp_flag = self:child('sp_operateflag')
	        if sp_flag then
		        sp_flag:removeFromParent()
	        end
            local card_node, is_hua = nil, false
            card_node, is_hua = self._cardLayer:discard(viewId, cbCardData, nil, true)  -- true不直接显示当前牌标记
            if not is_hua then
                self._cardLayer:flagDiscard(true, card_node)
            end
            local parent = self.m_card_plate:getParent()
            local show_node = cs.game.util.createCard(cbCardData, 3, 'hand')    -- 用于展示，自己删除
            show_node:scale(1):zorder(self.m_card_plate:getLocalZOrder()):pos(posPlate[viewId]):addTo(parent)
            show_node:runAction(cc.Sequence:create(
                --cc.ScaleTo:create(0.1, 2.0),
                cc.DelayTime:create(0.5),
                cc.RemoveSelf:create(true)
            ))
        else
            if not no_discard then
	            card_node, is_hua = self._cardLayer:discard(viewId, cbCardData, nil, true)  -- true不直接显示当前牌标记
                local target_scale = card_node:getScale()
                card_node:scale(0):runMyAction(cc.Sequence:create(
                    cc.DelayTime:create(OUT_CARD_DISPLAY_TIME + 0.15),
                    cc.ScaleTo:create(0.1, target_scale),
                    cc.CallFunc:create(function(sender)
                        if not is_hua then
                            self._cardLayer:flagDiscard(true, sender)
                        end
                    end)
                ))
            end

            local parent = self.m_card_plate:getParent()
            local show_node = cs.game.util.createCard(cbCardData, 3, 'pile')    -- 用于展示，自己删除
            show_node:scale(1.4):zorder(self.m_card_plate:getLocalZOrder()):pos(posPlate[viewId]):addTo(parent)
            local action = nil
            if card_node then
                local pos = card_node:getParent():convertToWorldSpace(cc.p(card_node:pos()))
                pos = parent:convertToNodeSpace(pos)
                action = cc.Spawn:create(
                    cc.EaseOut:create( cc.MoveTo:create(0.1, pos), 3 ),
                    cc.ScaleTo:create(0.1, 0.5),
                    cc.FadeOut:create(0.1)
                )
            else
                action = cc.ScaleTo:create(0.1, 0)
            end
            show_node:runAction(cc.Sequence:create(
                cc.DelayTime:create(OUT_CARD_DISPLAY_TIME),
                action,
                cc.RemoveSelf:create(true)
            ))                
        end
    end
end
--操作效果
function GameViewLayer:showOperateFlag(viewId, operateCode, path, delayTime )
	local sp_flag = self:child('sp_operateflag')
	if sp_flag then
		sp_flag:removeFromParent()
	end
	if nil == viewId then
		return false
	end
	local file_name = nil
    if not path then
	    if operateCode == GameLogic.WIK_NULL then
		    return false
	    elseif operateCode == GameLogic.WIK_BU_HUA then
		    file_name = "word/font_operate_buhua.png"
        elseif operateCode == GameLogic.WIK_ZIMO then
		    file_name = "word/font_operate_zimo.png"
	    elseif operateCode == GameLogic.WIK_CHI_HU then
		    file_name = "word/font_operate_hu.png"
	    elseif operateCode == GameLogic.WIK_LISTEN then
		    file_name = "word/font_operate_ting.png"
	    elseif operateCode == GameLogic.WIK_GANG then
		    file_name = "word/font_operate_gang.png"
	    elseif operateCode == GameLogic.WIK_PENG then
		    file_name = "word/font_operate_peng.png"
	    elseif operateCode <= GameLogic.WIK_RIGHT then
		    file_name = "word/font_operate_chi.png"
        elseif operateCode == GameLogic.WIK_CHENG_BAO then
	        file_name = "word/font_operate_santan.png"
        elseif operateCode == GameLogic.WIK_CHOU_PAI then
	        file_name = "word/font_operate_choupai.png"
        elseif operateCode == GameLogic.WIK_PIAO_CAI then
            file_name = "word/font_operate_piaocai.png"
        elseif operateCode == GameLogic.WIK_SHENG_PAI then
            file_name = "word/font_shengpaijieduan.png"
	    end    
    else
        file_name = path
    end
    if not file_name then return end
    local pos = cc.p(0, 0) 
	
    local sprite = display.newSprite(file_name)
		:setName('sp_operateflag')
        :scale(0)
        :zorder(4)
    if viewId ~= yl.INVALID_CHAIR then
        pos = posPlate[viewId]
        sprite:move(pos)
        self:addChild(sprite)
    else
        helper.layout.addCenter(self, sprite)
    end
    local cd = 1.0
    if delayTime then
        cd = delayTime
    end
    sprite.operate_code = operateCode
    sprite:runMyAction( cc.Sequence:create(
            cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) ),
            cc.DelayTime:create( cd ),
            cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
            cc.RemoveSelf:create(true)
    ) )

    if operateCode == GameLogic.WIK_SHENG_PAI then
        self:setShengPaiVisible(true)    
    end
	return true
end

-- 刷新玩家的分数
function GameViewLayer:popPlayerScore( viewID, score )
    if GlobalUserItem.bIsRedArena then return end
    if score == 0 then
       return
    end
    --print('popPlayerScore', score)
    local node = self.m_node_player[viewID]
    local label = ccui.Text:create(score, cs.game.FONT_NAME, 32)
    if score > 0 then
        label:setString( '+' .. score )
        label:setColor(cc.c3b(0, 255, 0))
    elseif score < 0 then
        label:setColor(cc.c3b(255, 0, 0))
    end
    label:zorder(999):stroke():pos(node:size().width/2, 0):addTo(node)
    local pos_y = label:py()+ 100
    local pos_x = label:px()
    label:runMyAction(
    cc.Sequence:create(
        cc.MoveTo:create(1.0, cc.p(pos_x, pos_y)),
        cc.RemoveSelf:create(true)
        ))
end

-- 刷新定位信息
function GameViewLayer:updateLabsInfo( isWarning )
    -- 刷新界面
    local time = yl.time()
    if time - self.disCD > 1.0 then
         self.disCD = time
    else
         return
    end
    --print('-- 刷新定位信息')
    if self._lbsLayer then
       self._lbsLayer:initUI()
       local status = self._lbsLayer:isNeedWarning( isWarning )
       local img_warning_icon = self:child('img_warning_icon')
       if status == yl.WarningTypes.IP_SAME then
            img_warning_icon:texture('room/warnning_icon_status_1.png')
       elseif status == yl.WarningTypes.LOCATION_DIS then
            img_warning_icon:texture('room/warnning_icon_status_2.png')
       elseif status == yl.WarningTypes.LOCATION_FAIL then
            img_warning_icon:texture('room/warnning_icon_status_2.png')
       else
            img_warning_icon:texture('room/warnning_icon_status_0.png')
       end
    end
end

-------------------------------------------------------------------------------
-- 
-------------------------------------------------------------------------------
function GameViewLayer:updateCardsTingInfo( isShow, tingInfo)
    local sp_cards_ting = self:child('sp_cards_ting')
    if not tingInfo or tingInfo.tingCardsNum == 0 then
       isShow = false
    end
    sp_cards_ting:setVisible( isShow )
    if isShow then
        local panel_cards = sp_cards_ting:child('img_bg/panel_cards')
        panel_cards:removeAllChildren()
        local count = 0
        local card_small = nil
        for index, num in ipairs( tingInfo.tingCards[1] ) do
            if num > 0 then
               local dataTemp = GameLogic.SwitchToCardData(index)
               card_small = cs.game.util.createCard(dataTemp, 3, 'pile')
               local cardSize = card_small:getContentSize()
               local label_num = ccui.Text:create(num, cs.game.FONT_NAME, 20)
               label_num:anchor(0.5, 0):pos( cc.p(cardSize.width/2, 2 ) ):stroke():addTo( card_small )
               card_small:pos(cc.p(cardSize.width  + cardSize.width * count , cardSize.height/2)):addTo( panel_cards ) 
               count = count + 1 
            end
        end
        if card_small then
            local max_x = card_small:px() + card_small:getContentSize().width
            local img_bg = sp_cards_ting:child('img_bg')
            img_bg:setContentSize( cc.size( max_x + 10, img_bg:getContentSize().height ))
            panel_cards:px( -max_x/2 + img_bg:getContentSize().width/2 )
        end
    end
end

-- 显示 听牌 内容
function GameViewLayer:showTingCards()
    if self:child('sp_cards_ting'):isVisible() then
        self:updateCardsTingInfo(false)
    else
        self:updateCardsTingInfo(true ,self.cardsTingInfo.curTingCards )
    end
end

-- 刷新 按钮 显示
function GameViewLayer:updateTingBtn( isHide )
    if not self.cardsTingInfo then
        return
    end
    if isHide then
        self.cardsTingInfo.bIsCurTing = false
    end
    if self.cardsTingInfo.bIsCurTing and self.cardsTingInfo.curTingCards then
        self:child('btn_ting'):show()
    else
        self:updateCardsTingInfo(false)
        self:child('btn_ting'):hide()
    end
end


--用户洗牌通知
function GameViewLayer:onUserShuffle(player_name)
    self.m_shuffle_player = player_name
end


--用户洗牌效果
function GameViewLayer:showShuffleEffect()
    if not self.m_shuffle_player or self.m_shuffle_player == '' then return end

    --helper.pop.message(LANG{'ROOM_SHUFFLE_HINT', name = self.m_shuffle_player})

    local params = {
        {-- 1号位
            path = CARD_PATH .. "/font_small/card_back.png",
            pos = cc.p(283, 497),
            add_h = cc.p(44, 0),
            add_v = cc.p(0, 14),
            move = cc.p(0, 100),
            order = 1,
            pile = 12,
        },
        {-- 4号位
            path = CARD_PATH .. "/font_small_side/card_back.png",
            pos = cc.p(859, 541),
            add_h = cc.p(0, -34),
            add_v = cc.p(0, 14),
            move = cc.p(100, 0),
            order = 1,
            pile = 10,
        },
        {-- 3号位
            path = CARD_PATH .. "/font_small/card_back.png",
            pos = cc.p(855, 211),
            add_h = cc.p(-44, 0),
            add_v = cc.p(0, 14),
            move = cc.p(0, -100),
            order = 1,
            pile = 12,
        },
        {-- 2号位
            path = CARD_PATH .. "/font_small_side/card_back.png",
            pos = cc.p(279, 167),
            add_h = cc.p(0, 34),
            add_v = cc.p(0, 14),
            move = cc.p(-100, 0),
            order = -1,
            pile = 10,
        },
    }

    local index = 0
    local cards = {}
    local map_real = {}
    local parent = cc.Node:create():addTo(self)
    for view_index, param in ipairs(params) do
        local order = 1
        for i = 1, param.pile do -- 两块一堆，几堆
            index = index + 1
            order = order + param.order
            for j = 1, 2 do -- 上下两块
                local zorder = order
                local tag = view_index * 100 + i * 10 + j;  -- 122表示1号位第2堆第2块
                local pos = cc.pAdd(param.pos, cc.p(param.add_h.x * i, param.add_h.y * i))
                if j == 2 then
                    pos = cc.pAdd(pos, param.add_v)
                    zorder = order + 1
                end
                map_real[#map_real + 1] = pos
                local card = display.newSprite(param.path)
				:move(cc.p(display.cx, display.cy))
				:setTag(tag)
                :opacity(0)
                :zorder(zorder)
				:addTo(parent)
                cards[#cards + 1] = card
            end
        end
    end

    local rand11 = function() return math.random(1, 100) <= 50 and 1 or -1 end

    for i, card in ipairs(cards) do
        local is_last = i == #cards
        local pos = map_real[i]
        local pos_1 = cc.p(display.cx + rand11() * math.random(1, 300), display.cy + rand11() * math.random(1, 160))
        local pos_2 = cc.p(display.cx + rand11() * math.random(1, 300), display.cy + rand11() * math.random(1, 160))
        local pos_3 = cc.p(display.cx + rand11() * math.random(1, 300), display.cy + rand11() * math.random(1, 160))
        card:runMyAction(cc.Sequence:create(
            cc.Spawn:create(
                cc.FadeIn:create(0.3),
                cc.MoveTo:create(0.3, pos_1)
            ),
            cc.MoveTo:create(0.3, pos_2),
            cc.MoveTo:create(0.3, pos_3),
            cc.MoveTo:create(0.3, pos),
            cc.DelayTime:create(0.3),
            cc.FadeOut:create(0.1),
            cc.CallFunc:create(function()
                if is_last then
                    parent:removeFromParent()
                end
            end)
        ))
    end

    self._scene:PlaySound(cmd.RES_PATH.."sound/SHUFFLE_CARD.mp3")
end


return GameViewLayer
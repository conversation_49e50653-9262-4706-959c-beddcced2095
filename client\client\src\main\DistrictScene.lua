-------------------------------------------------------------------------------
--  创世版1.0
--  地区选择场景
--  @date 2018-04-23
--  @auth woodoo
-------------------------------------------------------------------------------
local DistrictScene = class("DistrictScene", cc.load("mvc").ViewBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function DistrictScene:ctor(app, name)
    self.super.ctor(self, app, name)
    print('DistrictScene:ctor...')
    self:setName('district_scene')

    local cls = require(cs.app.CLIENT_SRC .. 'main.DistrictSelectLayer')
    local layer = cls:create(function()
        self:getApp():enterSceneEx(cs.app.CLIENT_SRC .. 'main.MainScene', 'FADE', 0.5)
    end)
    layer.m_is_scene = true
    layer:addTo(self)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function DistrictScene:onEnterTransitionFinish()
    print('DistrictScene:onEnterTransitionFinish...')

end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function DistrictScene:onExit()
    print('DistrictScene:onExit...')

end


return DistrictScene
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_relax_jump_run_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{220,124}}</string>
                <key>offset</key>
                <string>{9,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,6},{220,124}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,244},{214,118}}</string>
                <key>offset</key>
                <string>{9,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,13},{214,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{214,720},{212,120}}</string>
                <key>offset</key>
                <string>{9,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{26,11},{212,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{440,122},{214,120}}</string>
                <key>offset</key>
                <string>{8,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{24,10},{214,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{430,712},{212,120}}</string>
                <key>offset</key>
                <string>{8,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,10},{212,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,600},{212,120}}</string>
                <key>offset</key>
                <string>{9,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{26,12},{212,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_15.png</key>
            <dict>
                <key>frame</key>
                <string>{{216,598},{212,120}}</string>
                <key>offset</key>
                <string>{10,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,14},{212,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_16.png</key>
            <dict>
                <key>frame</key>
                <string>{{428,834},{212,118}}</string>
                <key>offset</key>
                <string>{11,-4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{28,17},{212,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_17.png</key>
            <dict>
                <key>frame</key>
                <string>{{214,842},{212,118}}</string>
                <key>offset</key>
                <string>{12,-5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{29,18},{212,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_18.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,482},{214,116}}</string>
                <key>offset</key>
                <string>{11,-4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,18},{214,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_19.png</key>
            <dict>
                <key>frame</key>
                <string>{{216,480},{214,116}}</string>
                <key>offset</key>
                <string>{10,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{26,15},{214,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{222,0},{218,120}}</string>
                <key>offset</key>
                <string>{10,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{24,8},{218,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_20.png</key>
            <dict>
                <key>frame</key>
                <string>{{432,596},{214,114}}</string>
                <key>offset</key>
                <string>{10,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{26,15},{214,114}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_21.png</key>
            <dict>
                <key>frame</key>
                <string>{{432,480},{214,114}}</string>
                <key>offset</key>
                <string>{9,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,14},{214,114}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_22.png</key>
            <dict>
                <key>frame</key>
                <string>{{432,362},{214,116}}</string>
                <key>offset</key>
                <string>{9,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,12},{214,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_23.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,364},{214,116}}</string>
                <key>offset</key>
                <string>{8,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{24,12},{214,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_24.png</key>
            <dict>
                <key>frame</key>
                <string>{{442,0},{212,120}}</string>
                <key>offset</key>
                <string>{8,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,9},{212,120}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{222,122},{216,118}}</string>
                <key>offset</key>
                <string>{10,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,12},{216,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,126},{216,116}}</string>
                <key>offset</key>
                <string>{10,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{25,15},{216,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{216,362},{214,116}}</string>
                <key>offset</key>
                <string>{11,-4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,18},{214,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{434,244},{214,116}}</string>
                <key>offset</key>
                <string>{11,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,17},{214,116}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,842},{212,118}}</string>
                <key>offset</key>
                <string>{11,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{28,15},{212,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{218,242},{214,118}}</string>
                <key>offset</key>
                <string>{10,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{26,15},{214,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
            <key>ani_relax_jump_run_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,722},{212,118}}</string>
                <key>offset</key>
                <string>{10,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{27,14},{212,118}}</string>
                <key>sourceSize</key>
                <string>{246,144}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_relax_jump_run.png</string>
            <key>size</key>
            <string>{654,960}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:5e4fea22a6fd4950093bbf57d0347734:2ab3b89551c1619cf3690cffa28ed935:394f21841ff6b444ed5f3b34d3362a65$</string>
            <key>textureFileName</key>
            <string>ani_relax_jump_run.png</string>
        </dict>
    </dict>
</plist>

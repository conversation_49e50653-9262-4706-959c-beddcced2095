-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部成员列表
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')
local ClubPager = cs.app.client('club.ClubPager')


local ClubMemberLayer = class("ClubMemberLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubMemberLayer:ctor(club)
    print('ClubMemberLayer:ctor...')
    self.m_club = club
    local main_node = ClubUtil.initUI(self, 'ClubMemberLayer.csb')
    main_node:child('row_template'):hide()
    self.m_pager = ClubPager.new(main_node:child('pager'), self.m_club.dwClubID, cmd.SUB_CLUB_MEMBERS)
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubMemberLayer:onEnter()
    print('ClubMemberLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_MEMBERS, self, self.onMemberListResp)
    ClubUtil.listen(cmd.SUB_CLUB_MEMBER_KICK, self, self.onMemberKickResp)
    ClubUtil.listen(cmd.SUB_CLUB_SET_MANAGER, self, self.onSetManagerResp)
    
    self.m_pager:stepPage(1)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubMemberLayer:onExit()
    print('ClubMemberLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 成员列表生成
-------------------------------------------------------------------------------
function ClubMemberLayer:onMemberListResp(data)
    local members = LiveFrame:getInstance():resp(data, cmd.tagClubMember, true)
    if not members then return end

    -- 更新翻页
    if #members > 0 then
        self.m_pager:updateParams(members[1].wTotal, members[1].wIndex, members[#members].wIndex)
    else
        self.m_pager:updateParams(0, 0, 0)
    end

    self.m_members = members

    local is_join = self.m_club.is_join

    local listview = self.main_node:child('listview')
    listview:removeAllItems()
    local template = self.main_node:child('row_template')
    helper.layout.pushEmpty(listview, {10, 10})
    for i, member in ipairs(members) do
        local item = template:clone():show()
        item:child('btn_kick').member = member
        item:child('btn_set_manager').member = member

        ClubUtil.createPlayerHead(item:child('panel_head'), member.dwUserID, member.szHeadHttp)

        item:child('label_name'):setString(member.szName)
        item:child('label_id'):setString( 'ID: ' .. helper.str.formatUserID(member.dwUserID) )
        item:child('img_status'):texture( ClubUtil.MEMBER_STATUS[member.cbStatus] )
        item:child('label_status'):setString( LANG['CLUB_MEMBER_STATUS' .. member.cbStatus] )
        item:child('label_join_time'):setString( ClubUtil.convertTime(member.sysJoinTime) )
        item:child('label_login_time'):setString( ClubUtil.convertTime(member.sysLastLogonDate) )

        local is_self = member.dwUserID == GlobalUserItem.dwUserID
        local is_president = member.dwUserID == self.m_club.dwPresidentID
        local is_manager = member.cbIsManager == 1

        -- 目标不是自己，且不是会长，且不是管理员或者当前是会长
        if not is_self and not is_president and (not is_manager or not is_join) and (not is_join or self.m_club.cbIsManager == 1) then -- 会长或管理员
            item:child('btn_kick'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnKick) )
        else
            item:child('btn_kick'):removeFromParent()
        end

        if not is_self and not is_join then -- 会长
            item:child('btn_set_manager/font'):texture('word/font_btn_' .. (member.cbIsManager == 0 and '' or 'un') .. 'setmanager.png' )
            item:child('btn_set_manager'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnSetManager) )
        else
            item:child('btn_set_manager'):removeFromParent()
        end

        if item:child('btn_kick') and not item:child('btn_set_manager') then -- 只有踢出按钮，位置居中
            item:child('btn_kick'):py(75)
        end

        listview:pushBackCustomItem(item)
    end
    helper.layout.pushEmpty(listview, {10, 10})
end


-------------------------------------------------------------------------------
-- 拒绝按钮点击
-------------------------------------------------------------------------------
function ClubMemberLayer:onBtnKick(sender)
    local member = sender.member
    helper.pop.alert( LANG.CLUB_MEMBER_KICK, function()
        local values = {dwID=self.m_club.dwClubID, nValue=member.dwUserID, nValue2=0}
        ClubUtil.send(cmd.SUB_CLUB_MEMBER_KICK, cmd.CMD_GR_IDValue, values)
    end, true)
end


-------------------------------------------------------------------------------
-- 踢出返回
-------------------------------------------------------------------------------
function ClubMemberLayer:onMemberKickResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_KICK_SUCC )
        if tolua.isnull(self) then return end
        -- 重新请求该页
        self.m_pager:stepPage(#self.m_members == 1 and -1 or 0)
    end)
end


-------------------------------------------------------------------------------
-- 设置管理员按钮点击
-------------------------------------------------------------------------------
function ClubMemberLayer:onBtnSetManager(sender)
    local member = sender.member
    local value = member.cbIsManager == 1 and 0 or 1
    helper.pop.alert( (member.cbIsManager == 0 and LANG.CLUB_SET_MANAGER or LANG.CLUB_UNSET_MANAGER), function()
        local values = {dwID=self.m_club.dwClubID, nValue=member.dwUserID, nValue2=value}
        ClubUtil.send(cmd.SUB_CLUB_SET_MANAGER, cmd.CMD_GR_IDValue, values)
    end, true)
end


-------------------------------------------------------------------------------
-- 设置管理员返回
-------------------------------------------------------------------------------
function ClubMemberLayer:onSetManagerResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_SET_SUCC )
        if tolua.isnull(self) then return end
        -- 重新请求该页
        self.m_pager:stepPage(0)
    end)
end


return ClubMemberLayer

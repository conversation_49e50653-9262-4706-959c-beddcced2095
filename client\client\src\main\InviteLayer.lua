-------------------------------------------------------------------------------
--  创世版1.0
--  邀请
--  @date 2017-09-14
--  @auth woodoo
-------------------------------------------------------------------------------
local URL_INDEX = '/api/v1/invite/index'    -- 加载邀请界面
local URL_LOGS  = '/api/v1/invite/logs'     -- 邀请记录
local URL_DRAW  = '/api/v1/invite/receive'  -- 领取    id ：领奖id  type：领奖类型 prize 宝箱类  log 日志类


local InviteLayer = class("InviteLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function InviteLayer:ctor()
    print('InviteLayer:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('InviteLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.m_panel_main = main_node:child('panel_main'):hide()
    self.m_panel_main:child('template_box'):hide()
    self.m_panel_main:child('listview_record'):hide()
    self.m_panel_main:child('template_record'):hide()
    self.m_panel_main:child('btn_record/text_record'):show()
    self.m_panel_main:child('btn_record/text_back'):hide()

    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    helper.logic.addListenerByName(self, {self.m_panel_main:child('btn_share, btn_record')})
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function InviteLayer:onEnterTransitionFinish()
    print('InviteLayer:onEnterTransitionFinish...')
    self:requestInvites()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function InviteLayer:onExit()
    print('InviteLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 请求
-------------------------------------------------------------------------------
function InviteLayer:doRequest(url, func, params)
    helper.pop.waiting()
    local all_params = {uid=GlobalUserItem.dwUserID}
    if params then
        for k, v in pairs(params) do
            all_params[k] = v
        end
    end
    yl.GetUrl(url, 'post', all_params, handler(self, func) )
end


-------------------------------------------------------------------------------
-- 请求基本信息
-------------------------------------------------------------------------------
function InviteLayer:requestInvites()
    self:doRequest(URL_INDEX, self.onInvitesRespose)
end


-------------------------------------------------------------------------------
-- 请求邀请记录
-------------------------------------------------------------------------------
function InviteLayer:requestRecords()
    self:doRequest(URL_LOGS, self.onRecordsRespose)
end


-------------------------------------------------------------------------------
-- 领取请求
-------------------------------------------------------------------------------
function InviteLayer:requestDraw(params)
    self:doRequest(URL_DRAW, self.onDrawRespose, params)
end


-------------------------------------------------------------------------------
-- 基本信息返回
-------------------------------------------------------------------------------
function InviteLayer:onInvitesRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    local panel_main = self.m_panel_main:show()
    data = data.data
    self.m_data = data

    -- 统计
    panel_main:child('label_renshu'):setString(LANG{'INVITE_TOTAL_RENSHU', num=data.total_num})
    panel_main:child('label_fangka'):setString(LANG{'INVITE_TOTAL_FANGKA', num=data.total_score})

    -- 奖励列表
    if #data.prizes > 0 then
        local count = #data.prizes
        local percent_gap = 100 / count
        local loading = panel_main:child('loading')        
        local percent = 0   -- 因为节点均匀分布但数量非均匀，所以得hack一下
        local is_first_cannot_draw = true
        local gap = loading:size().width / #data.prizes
        local template = panel_main:child('template_box')
        for i, prize in ipairs(data.prizes) do
            local item = template:clone():show()
            item:setName('prize' .. i)
            item:child('num'):setString(LANG{'INVITE_RENSHU', num=prize.num})
            item:child('fangka'):setString(LANG{'INVITE_FANGKA', num=prize.score})
            local can_draw = prize.num <= data.total_num    -- 是否达成
            if can_draw then
                percent = percent + percent_gap
            elseif is_first_cannot_draw then
                is_first_cannot_draw = false
                local last_num = i == 1 and 0 or data.prizes[i - 1].num
                local sub_percent = percent_gap * (data.total_num - last_num) / (prize.num - last_num)  -- 本段已完成数量 / 本段总数
                percent = percent + sub_percent
            end
            local has_draw = prize.is_receive == 1          -- 是否已领
            item.can_draw = can_draw
            if not has_draw then
                item:child('box').prize_id = prize.num
                item:child('box'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBoxDraw) )
            end
            item:child('has_draw'):setVisible(has_draw)
            if not can_draw then
                item:child('box'):setTouchEnabled(false)
                item:child('box'):texture('#invite_box_gray.png')
            end
            local x, y = i * gap, loading:size().height/2
            item:pos(x, y):addTo(loading)
            item:scale(0):runAction(cc.Sequence:create(
                cc.DelayTime:create((i-1) * 0.1),
                cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
            ))
        end
        loading:setPercent(percent)
    end

    -- 显示二维码
    local parent = panel_main:child('panel_qr')
    local node = helper.comp.createQr(data.qrcode_url, parent:size().width)
    helper.layout.addCenter(parent, node)

    -- 规则内容
    local listview_rule = panel_main:child('listview_rule')
    local rich = cs.app.client('system.RichText').new(data.content, 22, 
                    cc.size(listview_rule:size().width - 30, 0), cs.app.FONT_NAME, {['='] = cc.c3b(92,41,22)})
    listview_rule:pushBackCustomItem(rich)
end


-------------------------------------------------------------------------------
-- 基本信息返回
-------------------------------------------------------------------------------
function InviteLayer:onRecordsRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    local listview_record = self.m_panel_main:child('listview_record')
    listview_record.is_inited = true

    local template = self.m_panel_main:child('template_record')
    for i, v in ipairs(data.data) do
        local item = template:clone():show()
        item:child('name'):setString(v.role_name)
        item:child('id'):setString( string.format('ID:%06d', v.role_id) )
        item:child('time'):setString( helper.time.format(v.create_time, '%Y-%m-%d') )

        local btn = item:child('btn')
        if v.is_receive == 1 then
            btn:child('text'):removeFromParent()
            btn:ignoreContentAdaptWithSize(true)
            btn:texture('#invite_has_draw.png')
        else
            btn.record_id = v.id
            btn:setTouchEnabled(true)
            btn:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRecordDraw) )
        end
        listview_record:pushBackCustomItem(item)
    end
end


-------------------------------------------------------------------------------
-- 领取返回
-------------------------------------------------------------------------------
function InviteLayer:onDrawRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    data = data.data

    helper.pop.message( LANG.INVITE_DRAW_SUCCESS )

    -- 更新邀请房卡总数和主界面房卡总数
    if data.total_score then
        PassRoom:getInstance():getPlazaScene():updateFangka( tonumber(data.total_score) )
    end
    if data.invite_score then
        self.m_panel_main:child('label_fangka'):setString(LANG{'INVITE_TOTAL_FANGKA', num=data.invite_score})
    end

    -- 更新状态
    local btn = self.m_last_draw
    btn:setTouchEnabled(false)
    if btn.record_id then
        btn:child('text'):removeFromParent()
        btn:ignoreContentAdaptWithSize(true)
        btn:texture('#invite_has_draw.png')
    elseif btn.prize_id then
        btn:getParent():child('has_draw'):show()
    end
end


-------------------------------------------------------------------------------
-- 记录领取点击
-------------------------------------------------------------------------------
function InviteLayer:onBtnRecordDraw(sender)
    self.m_last_draw = sender
    self:doRequest(URL_DRAW, self.onDrawRespose, {type='log', id=sender.record_id})
end


-------------------------------------------------------------------------------
-- 宝箱点击
-------------------------------------------------------------------------------
function InviteLayer:onBtnBoxDraw(sender)
    self.m_last_draw = sender
    self:doRequest(URL_DRAW, self.onDrawRespose, {type='prize', id=sender.prize_id})
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function InviteLayer:onBtnShare(sender)
    local bg = display.newSprite('common/bg_myqr.jpg')
    local qr = helper.comp.createQr(self.m_data.qrcode_url, 190):anchor(0.5, 0.5)
    local size = bg:size()
    local w, h = size.width, size.height
    local render = cc.RenderTexture:create(w, h)
    render:begin()
    bg:pos(w * 0.5, h * 0.5)
    bg:visit()
    qr:pos(w * 0.5 + 4, 45 + 190/2)
    qr:visit()
    render:endToLua()
    render:saveToFile('myqr.jpg', cc.IMAGE_FORMAT_JPEG)
    helper.pop.shareImage('myqr.jpg')
end


-------------------------------------------------------------------------------
-- 记录按钮点击
-------------------------------------------------------------------------------
function InviteLayer:onBtnRecord(sender)
    local listview_rule, listview_record = self.m_panel_main:child('listview_rule, listview_record')
    listview_rule:setVisible(not listview_rule:isVisible())
    listview_record:setVisible(not listview_record:isVisible())
    if listview_record:isVisible() and not listview_record.is_inited then
        self:requestRecords()
    end
    sender:child('text_record'):setVisible(listview_rule:isVisible())
    sender:child('text_back'):setVisible(listview_record:isVisible())
end


return InviteLayer
-------------------------------------------------------------------------------
--  创世版1.0
--  手机绑定
--  @date 2017-06-22
--  @auth woodoo
-------------------------------------------------------------------------------
local EditBox = cs.app.client('system.EditBox')


local URL_GET   = '/api/v1/user/get_verify_code'    -- 获取验证码
local URL_BIND  = '/api/v1/user/bind_phone'         -- 绑定手机号


local MobileBindLayer = class("MobileBindLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function MobileBindLayer:ctor(callback)
    print('MobileBindLayer:ctor...')
    self:enableNodeEvents()
    self.m_callback = callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('MobileBindLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('bg_time'):hide()
    local edit1 = EditBox.convertTextField( main_node:child('input_mobile'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    local edit2 = EditBox.convertTextField( main_node:child('input_code'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    edit1:setPlaceholderFontColor(cc.c3b(220, 220, 220))
    edit2:setPlaceholderFontColor(cc.c3b(220, 220, 220))

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    main_node:child('btn_cancel'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    main_node:child('btn_get'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnGet) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function MobileBindLayer:onExit()
    print('MobileBindLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 遮罩点击
-------------------------------------------------------------------------------
function MobileBindLayer:onPopClose()
    self:doClose(false)
end


-------------------------------------------------------------------------------
-- 关闭
-------------------------------------------------------------------------------
function MobileBindLayer:onBtnClose()
    self:doClose(false)
end


-------------------------------------------------------------------------------
-- 获取验证码按钮点击
-------------------------------------------------------------------------------
function MobileBindLayer:onBtnGet(sender)
    local phone = self.main_node:child('input_mobile'):getString():trim()
    if phone == '' then
        helper.pop.message(LANG.MOBILE_BIND_BAD)
        return
    end

    helper.pop.waiting()
    local params = {uid=GlobalUserItem.dwUserID, phone=phone}

    -- 添加调试日志
    print("=== 短信验证码测试 ===")
    print("请求URL:", yl.ADMIN_URL .. URL_GET)
    print("请求参数:", dump(params))
    print("用户ID:", GlobalUserItem.dwUserID)
    print("手机号:", phone)

    yl.GetUrl(URL_GET, 'post', params, handler(self, self.onGetVcodeResponse) )
end


-------------------------------------------------------------------------------
-- 确定按钮点击
-------------------------------------------------------------------------------
function MobileBindLayer:onBtnOk(sender)
    local phone = self.main_node:child('input_mobile'):getString():trim()
    if phone == '' then return end
    if not phone:match('^%d+$') then
        helper.pop.message(LANG.MOBILE_BIND_BAD)
        return
    end
    local vcode = self.main_node:child('input_code'):getString():trim()
    if vcode == '' then return end

    helper.pop.waiting()
    local params = {uid=GlobalUserItem.dwUserID, phone=phone, code=vcode}
    yl.GetUrl(URL_BIND, 'post', params, handler(self, self.onBindResponse) )
end


-------------------------------------------------------------------------------
-- 获取验证码返回
-------------------------------------------------------------------------------
function MobileBindLayer:onGetVcodeResponse(data, response, http_status)
    if tolua.isnull(self) then return end

    -- 添加详细的调试日志
    print("=== 短信验证码响应 ===")
    print("HTTP状态码:", http_status)
    print("响应数据:", dump(data))
    print("响应原始数据:", response)

    if not helper.app.urlErrorCheck(data, response, http_status) then
        print("URL错误检查失败")
        return
    end
    helper.pop.message(LANG.MOBILE_BIND_SENT)

    self.main_node:child('bg_time'):show()
    self.main_node:child('btn_get'):hide()
    local tag = 213
    local num = 60
    self:perform(function()
        self.main_node:child('bg_time/label_time'):setString( LANG{'MOBILE_BIND_TIME', seconds=num} )
        num = num - 1
        if num == -1 then
            self:stop(action)
            self.main_node:child('bg_time'):hide()
            self.main_node:child('btn_get'):show()
        end
    end, 1, -1, tag, true)
end


-------------------------------------------------------------------------------
-- 绑定返回
-------------------------------------------------------------------------------
function MobileBindLayer:onBindResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    data = data.data

    -- 更新绑定手机
    GlobalUserItem.szMobilePhone = data.phone

    -- 更新全局房卡
    if data.total_fangka then
        PassRoom:getInstance():getPlazaScene():updateFangka( tonumber(data.total_fangka) )
    end

    -- 更新全局房卡
    if data.add_fangka and tonumber(data.add_fangka) > 0 then
        helper.pop.message(LANG{'MOBILE_BIND_SUCC1', num=data.add_fangka})
    else
        helper.pop.message(LANG.MOBILE_BIND_SUCC)
    end

    self:doClose(true)
end


-------------------------------------------------------------------------------
-- 关闭
-------------------------------------------------------------------------------
function MobileBindLayer:doClose(is_bind)
    if self.m_callback then
        self.m_callback(is_bind)
    end
    self:removeFromParent()
end


return MobileBindLayer
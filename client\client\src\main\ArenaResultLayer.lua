-------------------------------------------------------------------------------
--  创世版1.0
--  比赛场结果
--  @date 2018-03-27
--  @auth woodoo
-------------------------------------------------------------------------------
local ArenaResultLayer = class("ArenaResultLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function Arena<PERSON><PERSON>ultLayer:ctor(results)
    self:enableNodeEvents()
    self.m_file_images = {}
    self.results = results

    -- 载入主UI
    local main_node = helper.app.loadCSB('ArenaResultLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('rwd_template'):hide()

    main_node:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )
    main_node:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )

    self:initUI(self.results[1])
    table.remove(self.results, 1)
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ArenaResultLayer:onEnter()
    print('ArenaResultLayer:onEnter...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ArenaResultLayer:onExit()
    print('ArenaResultLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 初始化UI
-------------------------------------------------------------------------------
function ArenaResultLayer:initUI(data)
    local main_node = self.main_node
    local title = LANG{'ARENA_RESULT_TITLE', name=GlobalUserItem.szNickName, arena=data.szMatchName}
    main_node:child('label_title'):setRich(title)
    main_node:child('label_time'):setString( helper.time.format() )

    -- 第几名
    local atlas_rank, sp_di, sp_ming = main_node:child('atlas_rank, sp_di, sp_ming')
    atlas_rank:setString(data.nRank)
    sp_di:px(atlas_rank:px() - atlas_rank:size().width/2 - sp_di:size().width/2 - 10)
    sp_ming:px(atlas_rank:px() + atlas_rank:size().width/2 + sp_ming:size().width/2 + 10)

    -- 奖励（从中间往两边散开）
    local file_caches = {}
    local listview = main_node:child('listview')
    local template = main_node:child('rwd_template')
    template:setBackGroundColorType(ccui.LayoutBackGroundColorType.none)
    local rwd_count = 0
    for i = 1, 5 do
        if data.nRewardTypes[1][i] > 0 then
            rwd_count = rwd_count + 1
            local item = template:clone():show()
            item:child('label_num'):setString('x' .. data.nRewardNums[1][i])
            self:downloadIcon(item, data.szIcon[1][i], file_caches)
            listview:pushBackCustomItem(item)
        end
    end
    if rwd_count < 5 then
        listview:size(rwd_count * (template:size().width + 20), listview:size().height)
    end

    template:removeFromParent()
end


-------------------------------------------------------------------------------
-- 下载图标
-------------------------------------------------------------------------------
function ArenaResultLayer:downloadIcon(node, path, cache_table)
    node:child('img_icon'):hide()
    if not path or path == '' then return end

    local function add(node, image)
        image:pos(node:child('img_icon'):pos()):addTo(node)
        node:child('img_icon'):removeFromParent()
        image:setName('img_icon')
    end
    helper.app.addURLImage(self, cache_table or {}, 'itemicon', path, node, add)
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function ArenaResultLayer:onBtnBack(sender)
    if #self.results > 0 then
        local path = cs.app.CLIENT_SRC .. 'main.ArenaResultLayer'
        helper.pop.popLayer(path, nil, {self.results})
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 比赛记录按钮点击
-------------------------------------------------------------------------------
function ArenaResultLayer:onBtnShare(sender)
    local btn_back, btn_share = self.main_node:child('btn_back, btn_share')
    btn_back:hide()
    btn_share:hide()
    local framesize = cc.Director:getInstance():getOpenGLView():getFrameSize()
    local area = cc.rect(0, 0, framesize.width, framesize.height)
    captureScreenWithArea(area, 'screenshot.jpg', function(ok, save_path)
        btn_back:show()
        btn_share:show()
        if not ok then return end

        local bg = display.newSprite(save_path)
        local bg_qr = display.newSprite('common/bg_arena_result_qr.png'):anchor(0, 0)
        local qr = helper.comp.createQr(yl.INVITE_URL, 131):anchor(0.5, 0.5)
        local size = bg:size()
        local w, h = size.width, size.height
        local render = cc.RenderTexture:create(w, h)
        render:begin()
        bg:pos(w * 0.5, h * 0.5)
        bg:visit()
        bg_qr:pos(0, h/2 - 164)
        bg_qr:visit()
        qr:pos(101, h/2 - 31)
        qr:visit()
        render:endToLua()
        render:saveToFile('arenashare.jpg', cc.IMAGE_FORMAT_JPEG)
        helper.pop.shareImage('arenashare.jpg')
    end)
end


return ArenaResultLayer

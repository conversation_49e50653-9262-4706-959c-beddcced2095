-------------------------------------------------------------------------------
--  创世版1.0
--  商城
--  @date 2017-06-12
--  @auth woodoo
--[[
    1、购买支付会使游戏进入后台
    2、回到前台后开启定时器刷新购买记录，1秒，3秒，3秒。。。
    3、如果有新的购买记录，更新商品列表（当前显示tab）
    4、更新商品列表时，重新定位列表
--]]
-------------------------------------------------------------------------------
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")


local URL_GOODS  = '/api/v1/store/goods'
local URL_ORDER  = '/api/v1/store/order'
local URL_QUERY  = '/api/v1/store/query' -- 查询订单   orders  查询的订单号

local QUERY_TAG = 3462  -- 查询订单状态定时器tag

local promotion_types = {
    HOT         = 1,    -- 热卖
    LIMIT       = 2,    -- 限时
    DISCOUNT    = 3,    -- 特惠
    MAX         = 4,
}

local PRICE_TYPE_CASH   = 1
local PRICE_TYPE_GOLD   = 2
local PRICE_TYPE_QUAN   = 3
local PRICE_TYPE_FANGKA = 4
local PRICE_TYPE_GUESS  = 5


local MallLayer = class("MallLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function MallLayer:ctor(tab_name)
    self:enableNodeEvents()
    self.m_orders = {}
    self.m_goods = {}
    self.m_init_tab_name = tab_name

    -- 载入主UI
    local main_node = helper.app.loadCSB('MallLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('tab_template'):hide()
    main_node:child('item_template'):hide()

    -- 初始化TopBar
    local top_bar = main_node:child('top_bar')
    top_bar:child('bg_guess/label'):setString( GlobalUserItem.nGuessCoin )
    top_bar:child('bg_gold/label'):setString( helper.str.makeFormatNum(GlobalUserItem.lUserScore, 1) )
    top_bar:child('bg_fangka/label'):setString( GlobalUserItem.lRoomCard )
    top_bar:child('btn_back'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBack) )

    helper.app.checkFangkDiamond(top_bar:child('bg_fangka/icon'))
    helper.app.addGoldLabelEvent(top_bar:child('bg_gold/label'))

    -- 暂时按开关控制
    local server_config = yl.app:getServerConfig()
    if server_config.is_open_guess == 0 then
        top_bar:child('bg_guess'):hide()
    end

    self:perform(handler(self, self.requestGoods), 0.3)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function MallLayer:onEnterTransitionFinish()
    print('MallLayer:onEnterTransitionFinish...')
    --helper.app.addBackgroundCallback(self, self.onBackground)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function MallLayer:onExit()
    print('MallLayer:onExit...')
    --helper.app.removeBackgroundCallback(self)
end


-------------------------------------------------------------------------------
-- onBackground
-------------------------------------------------------------------------------
function MallLayer:onBackground(is_foreground)
    print('MallLayer:onBackground...', is_foreground)
    if is_foreground then
        self:stop(QUERY_TAG):perform(function()
            self:requestQuery()
        end, 2, 10, QUERY_TAG)
    end
end


-------------------------------------------------------------------------------
-- 开启刷新订单定时器
-------------------------------------------------------------------------------
function MallLayer:startQueryTimer()
    self:stop(QUERY_TAG):perform(function()
        self:requestQuery()
    end, 2, 30, QUERY_TAG)
end


-------------------------------------------------------------------------------
-- 更新房卡
-------------------------------------------------------------------------------
function MallLayer:updateFangka(fangka, jiangquan, gold, guess)
    if fangka then
        self.main_node:child('top_bar/bg_fangka/label'):setString(fangka)
    end
    if jiangquan then
        self.main_node:child('top_bar/bg_quan/label'):setString(jiangquan)
    end
    if gold then
        self.main_node:child('top_bar/bg_gold/label'):setString( helper.str.makeFormatNum(gold, 1) )
    end
    if guess then
        GlobalUserItem.nGuessCoin = guess
        self.main_node:child('top_bar/bg_guess/label'):setString(guess)
    end

    -- 更新全局房卡、金币
    if fangka or gold then
        PassRoom:getInstance():getPlazaScene():updateFangka( tonumber(fangka), tonumber(gold) )
    end
end


-------------------------------------------------------------------------------
-- 请求
-------------------------------------------------------------------------------
function MallLayer:doRequest(url, func, params)
    helper.pop.waiting()
    local all_params = {uid=GlobalUserItem.dwUserID}
    if params then
        for k, v in pairs(params) do
            all_params[k] = v
        end
    end
    yl.GetUrl(url, 'post', all_params, handler(self, func) )
end


-------------------------------------------------------------------------------
-- 请求基本信息
-------------------------------------------------------------------------------
function MallLayer:requestGoods()
    self:doRequest(URL_GOODS, self.onGoodsResponse)
end


-------------------------------------------------------------------------------
-- 基本信息返回
-------------------------------------------------------------------------------
function MallLayer:onGoodsResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    local is_first = not self.m_cur_tab

    if not data.data or not data.data.goods then return end

    self.m_goods = data.data.goods

    self:updateFangka(data.data.fangka, data.data.jiangquan, data.data.gold, data.data.guess)

    if is_first then
        self:initTabs(self.m_goods)

        local tab_first
        local listview = self.main_node:child('panel_left/listview')
        if not self.m_init_tab_name then
	        tab_first = listview:getItem(0)
        else
            for i, item in ipairs(listview:getItems()) do
                if item.tab_name == self.m_init_tab_name then
                    tab_first = item
                    break
                end
            end
        end
        if tab_first then
            tab_first:toggle()
            self:onBtnTab(tab_first)
        end
    else
        -- 检查是否有差异
        self:createItems(self.m_cur_tab)
    end
end


-------------------------------------------------------------------------------
-- 请求基本信息
-------------------------------------------------------------------------------
function MallLayer:requestQuery()
    if #self.m_orders == 0 then
        self:stop(QUERY_TAG)
        return
    end
    self:doRequest(URL_QUERY, self.onQueryResponse, {orders=table.concat(self.m_orders, ',')})
end


-------------------------------------------------------------------------------
-- 订单状态查询返回
-------------------------------------------------------------------------------
function MallLayer:onQueryResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    if not data.data then return end

    local data = data.data
    if data.success and type(data.success) == 'table' then
        for i, id in ipairs(data.success) do
            for idx, order_id in ipairs(self.m_orders) do
                if id == order_id then
                    helper.pop.message( LANG.MALL_BUY_SUCCESS )
                    table.remove(self.m_orders, idx)
                    break
                end
            end
        end
    end

    -- 更新房卡
    self:updateFangka(data.fangka, data.jiangquan, data.gold, data.guess)

    -- 重新请求goods
    self:requestGoods()
end


-------------------------------------------------------------------------------
-- 初始化Tab
-------------------------------------------------------------------------------
function MallLayer:initTabs(goods)
    local main_node = self.main_node
    local listview = main_node:child('panel_left/listview')
    local template = main_node:child('tab_template')
    for i, category in ipairs(goods) do
        local tab = template:clone():show()
        tab.tab_type = i
        tab.tab_name = category.name
        tab.group = 1
        tab:child('text'):setString(category.name)
        helper.logic.initImageCheck(tab, false, 'common/btn_tab_s.png', 'common/btn_tab_n.png', 
            handler(self, self.onBtnTab))
        listview:pushBackCustomItem(tab)
    end
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function MallLayer:onBtnBack(sender)
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 商城类型点击
-------------------------------------------------------------------------------
function MallLayer:onBtnTab(sender)
    local tab_type = sender.tab_type
    self:createItems(tab_type)
    self.m_cur_tab = tab_type
end


-------------------------------------------------------------------------------
-- 购买点击
-------------------------------------------------------------------------------
function MallLayer:onBtnBuy(sender)
    local data = sender:getParent().data
    local mall_id = self.m_goods[self.m_cur_tab].mall_id
    self:doRequest(URL_ORDER, self.onOrderResponse, {mall_id=mall_id, good_id=data.id})
end


-------------------------------------------------------------------------------
-- 购买返回
-------------------------------------------------------------------------------
function MallLayer:onOrderResponse(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end
    if not data.data then return end

    if data.data.order_id then
        table.insert(self.m_orders, data.data.order_id)
        self:startQueryTimer()
    end

    if data.data.url then -- 调用平台跳转
        MultiPlatform:getInstance():openBrowser(data.data.url)
    elseif data.data.params then
        MultiPlatform:getInstance():thirdPartyPay(yl.ThirdParty.WECHAT, data.data.params, function()
            -- 支付成功，目前没什么需要处理
        end)
    else    -- 普通购买
        helper.pop.message( LANG.MALL_BUY_SUCCESS )

        -- 更新房卡
        self:updateFangka(data.fangka, data.jiangquan, data.gold, data.guess)

        -- 重新请求goods
        self:requestGoods()
    end
end


-------------------------------------------------------------------------------
-- 创建商品
-------------------------------------------------------------------------------
function MallLayer:createItems(tab_type)
    self.m_file_images = {}
    local items = {}
    local listview = self.main_node:child('listview_items')
    local template = self.main_node:child('item_template')
    local l_size = listview:size()
    local i_size = template:size()
    listview:removeAllItems()
    local row
    local datas = self.m_goods[tab_type] or {}
    for i, data in ipairs(datas.good or {}) do
        if i % 3 == 1 then
            row = ccui.Layout:create()
            row:size(l_size.width, i_size.height)
            listview:pushBackCustomItem(row)
        end
        item = template:clone():show()
        item.data = data
        local col = (i - 1) % 3 + 1
        item:pos((col - 1) * i_size.width, 0)
        item:addTo(row)

        item:child('btn_buy'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnBuy) )
        
        local lang_key = 'MALL_PRICE_' .. (data.price_type or 1)
        local fmt = PRICE_TYPE_CASH == data.price_type and '%.2f' or '%d'
        item:child('btn_buy/label_price'):setRich( LANG{lang_key, price=string.format(fmt, data.price)} )

        item:child('label_name'):setString(data.name)
        if data.stock <= -99 then
            item:child('label_left'):hide()
            item:child('img_left'):hide()
        else
            item:child('label_left'):setString( LANG{'MALL_LEFT', num=data.stock} )
        end
        item:child('img_promotion'):zorder(1)   -- 提高zorder以覆盖将要添加的icon
        item:child('label_promotion'):zorder(1):setString(data.favorable_desc)
        item:child('label_desc'):setString(data.desc)
        if data.is_first == 1 then
            item:child('img_hot'):texture('#mall_icon_first_charge.png')
        elseif data.promotion_type > 0 and data.promotion_type < promotion_types.MAX then
            item:child('img_hot'):texture('#mall_icon_promotion_' .. data.promotion_type .. '.png')
        else
            item:child('img_hot'):hide()
        end

        items[data.id] = item

        -- 图标下载处理 ---------------------------------------------------------------------------
        local function add(node, image)
            image:pos(node:size().width/2, 173):addTo(node)
        end
        helper.app.addURLImage(self, self.m_file_images, 'mallicon', data.icon, item, add)
    end

    listview:jumpToTop()
    self.m_items = items
end


return MallLayer
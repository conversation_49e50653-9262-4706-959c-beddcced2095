-------------------------------------------------------------------------------
--  创世版3.0
--  玩法管理
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')


local MAX_NUM = ClubUtil.MAX_CLUB_KINDS


local ClubKindLayer = class("ClubKindLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubKindLayer:ctor(club)
    print('ClubKindLayer:ctor...')
    self.m_club = club
    local main_node = ClubUtil.initUI(self, 'ClubKindLayer.csb')
    main_node:child('template'):hide()
    self.panel_add = self.main_node:child('panel_add')

    if MAX_NUM <= 3 then
        self.main_node:child('listview'):setTouchEnabled(false) -- 最多3项不出现滚动
    end
    main_node:child('panel_add/btn_add'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAdd) )

    local names = {}
    for i, plugin in ipairs(cs.app.plugins) do
        if plugin.kind then
            names[plugin.kind] = plugin.name
        end
    end
    self.m_names = names
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubKindLayer:onEnter()
    print('ClubKindLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_RULE_LIST, self, self.onListResp)
    ClubUtil.listen(cmd.SUB_CLUB_RULE_ADD, self, self.onAddResp)
    ClubUtil.listen(cmd.SUB_CLUB_RULE_EDIT, self, self.onEditResp)
    ClubUtil.listen(cmd.SUB_CLUB_RULE_REMOVE, self, self.onRemoveResp)

    ClubUtil.send(cmd.SUB_CLUB_RULE_LIST, cmd.CMD_GR_ID, {dwID=self.m_club.dwClubID})
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubKindLayer:onExit()
    print('ClubKindLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 单个玩法生成
-------------------------------------------------------------------------------
function ClubKindLayer:createOne(kind)
    local listview = self.main_node:child('listview')
    local template = self.main_node:child('template')
    local item = template:clone():show()
    item.kind = kind

    --item:child('icon'):texture('common/icon_kind_' .. kinds.wKindID .. '.png')
    item:child('label_title'):setString(self.m_names[kind.wKindID] or '')
    item:child('label_rule'):setString((string.gsub(kind.szRule, ', ', '\n')))
    item:child('btn_edit'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnEdit) )
    item:child('btn_delete'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDelete) )
    listview:pushBackCustomItem(item)

    return item
end


-------------------------------------------------------------------------------
-- 玩法列表生成
-------------------------------------------------------------------------------
function ClubKindLayer:createList(kinds)
    local listview = self.main_node:child('listview')
    local panel_add = self.panel_add
    if #kinds < MAX_NUM then
        panel_add:child('label_num'):setString(#kinds .. '/' .. MAX_NUM)
        panel_add:retain()
        panel_add:removeFromParent()
        listview:pushBackCustomItem(panel_add)
        panel_add:release()
    else
        panel_add:hide()
    end

    for i, kind in ipairs(kinds) do             
        self:createOne(kind)
    end
end


-------------------------------------------------------------------------------
-- 编辑按钮点击
-------------------------------------------------------------------------------
function ClubKindLayer:onBtnEdit(sender)
    self.m_cur_kind = sender:getParent().kind
    self.m_cur_act = 'edit'

    -- 调用创建房间UI
    local kind = self.m_cur_kind
    local params = {
        dwRuleID    = kind.dwRuleID,
        kind        = kind.wKindID,
        jushu_index = kind.cbPlayCountIndex,
        renshu      = kind.wJoinGamePeopleCount,
        pay_type    = kind.cbPayType,
        rule        = kind.llGameRule,
        difen       = kind.nBaseScore,
        continue    = kind.cbCanContinue,
    }
    ClubUtil.showKindCreator(params, handler(self, self.onEditSave))
end


-------------------------------------------------------------------------------
-- 删除按钮点击
-------------------------------------------------------------------------------
function ClubKindLayer:onBtnDelete(sender)
    self.m_cur_kind = sender:getParent().kind
    helper.pop.alert( LANG.CLUB_KIND_DELETE, function()
        ClubUtil.send(cmd.SUB_CLUB_RULE_REMOVE, cmd.CMD_GR_ID, {dwID=self.m_cur_kind.dwRuleID})
    end, true)
end


-------------------------------------------------------------------------------
-- 添加按钮点击
-------------------------------------------------------------------------------
function ClubKindLayer:onBtnAdd(sender)
    self.m_cur_kind = nil
    self.m_cur_act = 'add'
    ClubUtil.showKindCreator({}, handler(self, self.onEditSave))
end


-------------------------------------------------------------------------------
-- 编辑结果
-------------------------------------------------------------------------------
function ClubKindLayer:onEditSave(param)
    local commond = self.m_cur_act == 'add' and cmd.SUB_CLUB_RULE_ADD or cmd.SUB_CLUB_RULE_EDIT
    ClubUtil.sendKindCmd(commond, param.dwRuleID or 0, self.m_club.dwClubID, param)
end


-------------------------------------------------------------------------------
-- 列表返回
-------------------------------------------------------------------------------
function ClubKindLayer:onListResp(data)
    local kinds = LiveFrame:getInstance():resp(data, cmd.tagClubRule, true)
    if not kinds then return end

    self.m_kinds = kinds
    self:createList(kinds)
end


-------------------------------------------------------------------------------
-- 新增玩法返回
-------------------------------------------------------------------------------
function ClubKindLayer:onAddResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd.tagClubRuleResult)
    if not ret then return end
    if ret.szMsg ~= '' then
        --此处不需要了，在ClubMainLayer中已有提示。helper.pop.message(ret.szMsg)
        return
    end

    table.insert(self.m_kinds, ret.rule)
    self:createOne(ret.rule)
    self.panel_add:child('label_num'):setString(#self.m_kinds .. '/' .. MAX_NUM)
    if #self.m_kinds >= MAX_NUM then
        local panel_add = self.panel_add
        panel_add:retain()
        panel_add:removeFromParent()
        panel_add:hide():addTo(self)
        panel_add:release()
    end
end


-------------------------------------------------------------------------------
-- 编辑玩法返回
-------------------------------------------------------------------------------
function ClubKindLayer:onEditResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd.tagClubRuleResult)
    if not ret or not self.m_cur_kind then return end
    if ret.szMsg ~= '' then
        helper.pop.message(ret.szMsg)
        return
    end

    for i, kind in ipairs(self.m_kinds) do
        if kind.dwRuleID == ret.rule.dwRuleID then
            self.m_kinds[i] = ret.rule
            break
        end
    end

    local listview = self.main_node:child('listview')
    for i, item in ipairs(listview:getItems()) do
        if item.kind and item.kind.dwRuleID == ret.rule.dwRuleID then
            item.kind = ret.rule
            item:child('label_title'):setString(self.m_names[ret.rule.wKindID] or '')
            item:child('label_rule'):setString((string.gsub(ret.rule.szRule, ', ', '\n')))
            break
        end
    end
end


-------------------------------------------------------------------------------
-- 删除玩法返回
-------------------------------------------------------------------------------
function ClubKindLayer:onRemoveResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDValueMsg, function(ret)
        helper.pop.message( LANG.CLUB_SUCC )

        local rule_id = ret.nValue

        for i, kind in ipairs(self.m_kinds) do
            if kind.dwRuleID == rule_id then
                table.remove(self.m_kinds, i)
                break
            end
        end

        local listview = self.main_node:child('listview')
        for i, item in ipairs(listview:getItems()) do
            if item.kind and item.kind.dwRuleID == rule_id then
                listview:removeItem(i - 1)
                break
            end
        end

        self.panel_add:child('label_num'):setString(#self.m_kinds .. '/' .. MAX_NUM)
        if #self.m_kinds < MAX_NUM then
            local panel_add = self.panel_add:show()
            panel_add:retain()
            panel_add:removeFromParent()
            listview:insertCustomItem(panel_add, 0)
            panel_add:release()
        end

        self.m_cur_kind = nil
    end)
end


return ClubKindLayer

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_circle_light_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{360,342},{85,90}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,0},{85,90}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,256},{83,84}}</string>
                <key>offset</key>
                <string>{-2,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,2},{83,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{95,342},{85,86}}</string>
                <key>offset</key>
                <string>{-2,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,4},{85,86}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{97,256},{91,84}}</string>
                <key>offset</key>
                <string>{-3,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,5},{91,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{360,170},{93,84}}</string>
                <key>offset</key>
                <string>{-2,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,5},{93,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_14.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,256},{93,84}}</string>
                <key>offset</key>
                <string>{-2,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,4},{93,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_15.png</key>
            <dict>
                <key>frame</key>
                <string>{{275,256},{91,84}}</string>
                <key>offset</key>
                <string>{-3,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,4},{91,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_16.png</key>
            <dict>
                <key>frame</key>
                <string>{{85,84},{91,82}}</string>
                <key>offset</key>
                <string>{-3,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,2},{91,82}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_17.png</key>
            <dict>
                <key>frame</key>
                <string>{{283,2},{89,78}}</string>
                <key>offset</key>
                <string>{-3,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,3},{89,78}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_18.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{89,78}}</string>
                <key>offset</key>
                <string>{-3,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,3},{89,78}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_19.png</key>
            <dict>
                <key>frame</key>
                <string>{{368,256},{87,84}}</string>
                <key>offset</key>
                <string>{-3,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{2,2},{87,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{273,342},{85,90}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,0},{85,90}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_20.png</key>
            <dict>
                <key>frame</key>
                <string>{{180,170},{89,84}}</string>
                <key>offset</key>
                <string>{-4,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,1},{89,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_21.png</key>
            <dict>
                <key>frame</key>
                <string>{{95,170},{83,84}}</string>
                <key>offset</key>
                <string>{-2,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,2},{83,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_22.png</key>
            <dict>
                <key>frame</key>
                <string>{{269,84},{87,84}}</string>
                <key>offset</key>
                <string>{-5,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,1},{87,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_23.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,84},{81,80}}</string>
                <key>offset</key>
                <string>{-8,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,4},{81,80}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_24.png</key>
            <dict>
                <key>frame</key>
                <string>{{93,2},{91,78}}</string>
                <key>offset</key>
                <string>{2,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,6},{91,78}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_25.png</key>
            <dict>
                <key>frame</key>
                <string>{{186,2},{95,78}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,5},{95,78}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,170},{91,84}}</string>
                <key>offset</key>
                <string>{3,-2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,5},{91,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,342},{91,86}}</string>
                <key>offset</key>
                <string>{3,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,2},{91,86}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{182,342},{89,86}}</string>
                <key>offset</key>
                <string>{3,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,0},{89,86}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{178,84},{89,82}}</string>
                <key>offset</key>
                <string>{3,4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,0},{89,82}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{374,2},{95,80}}</string>
                <key>offset</key>
                <string>{-1,3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,2},{95,80}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{358,84},{95,84}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,2},{95,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
            <key>ani_circle_light_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{271,170},{87,84}}</string>
                <key>offset</key>
                <string>{2,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,3},{87,84}}</string>
                <key>sourceSize</key>
                <string>{97,90}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_circle_light.png</string>
            <key>size</key>
            <string>{471,434}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:69c614b6dfb7568f5fe86b3064e9da4e:5ec40ec271d89d181baeadb2b1bffeb8:cef09eb4acbedc7f2c597fd6531a2f5c$</string>
            <key>textureFileName</key>
            <string>ani_circle_light.png</string>
        </dict>
    </dict>
</plist>

-------------------------------------------------------------------------------
--  创世版1.0
--  窗口跳转辅助方法类
--      一些二级弹窗打开
--      访问方式：helper.link.
--  @date 2017-06-06
--  @auth woodoo
-------------------------------------------------------------------------------
local LinkHelper = {}
helper = helper or {}
helper.link = LinkHelper


-------------------------------------------------------------------------------
-- 加房卡
-------------------------------------------------------------------------------
function LinkHelper.toFangka()
    local path = cs.app.CLIENT_SRC .. 'main.FangkaLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 设置
-------------------------------------------------------------------------------
function LinkHelper.toSetting(external_class)
    -- 检查是否有子类
    local path = cs.app.CLIENT_SRC .. 'main.SettingLayer'
    helper.pop.popLayer(path, nil, external_class, nil, true)
end


-------------------------------------------------------------------------------
-- 规则
-------------------------------------------------------------------------------
function LinkHelper.toRule(kind)
    local path = cs.app.CLIENT_SRC .. 'main.RuleLayer'
    helper.pop.popLayer(path, nil, kind, nil, true)
end


-------------------------------------------------------------------------------
-- 绑定
-------------------------------------------------------------------------------
function LinkHelper.toBind()
    local path = cs.app.CLIENT_SRC .. 'main.BindLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 实名认证
-------------------------------------------------------------------------------
function LinkHelper.toMobileBind(callback, parent)
    local path = cs.app.CLIENT_SRC .. 'main.MobileBindLayer'
    helper.pop.popLayer(path, parent, {callback}, nil, true)
end


-------------------------------------------------------------------------------
-- 游戏记录
-------------------------------------------------------------------------------
function LinkHelper.toScore()
    local path = cs.app.CLIENT_SRC .. 'main.ScoreLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 玩家信息
-------------------------------------------------------------------------------
function LinkHelper.toUser()
    local path = cs.app.CLIENT_SRC .. 'main.UserLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 个人中心
-------------------------------------------------------------------------------
function LinkHelper.toPersonalCenter()
    local path = cs.app.CLIENT_SRC .. 'main.PersonalCenterLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 商城
-------------------------------------------------------------------------------
function LinkHelper.toMall(tab_name)
    local path = cs.app.CLIENT_SRC .. 'main.MallLayer'
    helper.pop.popLayer(path, nil, {tab_name})
end


-------------------------------------------------------------------------------
-- 活动
-------------------------------------------------------------------------------
function LinkHelper.toActivity(data, default_index)
    local path = cs.app.CLIENT_SRC .. 'main.ActivityLayer'
    helper.pop.popLayer(path, nil, {data, default_index})
end


-------------------------------------------------------------------------------
-- 大转盘
-------------------------------------------------------------------------------
function LinkHelper.toTurntable()
    local path = cs.app.CLIENT_SRC .. 'main.TurntableLayer'
    helper.pop.popLayer(path)
end


-------------------------------------------------------------------------------
-- 邀请
-------------------------------------------------------------------------------
function LinkHelper.toInvite()
    local path = cs.app.CLIENT_SRC .. 'main.InviteLayer'
    helper.pop.popLayer(path)
end


-------------------------------------------------------------------------------
-- 我的红包
-------------------------------------------------------------------------------
function LinkHelper.toMyRedBag()
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    local channel = MultiPlatform:getInstance():getChannel()
    local uid = GlobalUserItem.dwUserID
    local sign = md5(channel .. '-' .. uid .. '-' .. yl.MY_RED_BAG_URL_KEY)
    local params = string.format('?channel_id=%s&role_id=%d&sign=%s', channel, uid, sign)
    print(yl.MY_RED_BAG_URL .. params)
    MultiPlatform:getInstance():openBrowser(yl.MY_RED_BAG_URL .. params)
end


-------------------------------------------------------------------------------
-- 红包
-------------------------------------------------------------------------------
function LinkHelper.toRedBag()
    if not helper.app.getFromScene( 'subRedBagLayer' ) then
        local path = cs.app.CLIENT_SRC .. 'main.RedBagLayer'
        helper.pop.popLayer(path)
    end
end


-------------------------------------------------------------------------------
-- 世界杯竞猜
-------------------------------------------------------------------------------
function LinkHelper.toWorldCup()
    local path = cs.app.CLIENT_SRC .. 'main.WorldCupGuess'
    helper.pop.popLayer(path)
end


-------------------------------------------------------------------------------
-- 拆红包
-------------------------------------------------------------------------------
function LinkHelper.toOrb()
    local path = cs.app.CLIENT_SRC .. 'orb.OrbFrame'
    helper.pop.popLayer(path)
end


-------------------------------------------------------------------------------
-- 首次登陆广告
-------------------------------------------------------------------------------
function LinkHelper.toFirstAD( data )
    if not helper.app.getFromScene( 'subFirstPopupLayer' ) then
        local path = cs.app.CLIENT_SRC .. 'main.FirstPopupLayer'
        helper.pop.popLayer(path, nil, {data})
    end
end


-------------------------------------------------------------------------------
-- 俱乐部
-------------------------------------------------------------------------------
function LinkHelper.toClub(target_name, params)
    local path = cs.app.CLIENT_SRC .. 'club.ClubMainLayer'
    helper.pop.popLayer(path, nil, {target_name, params})
end


-------------------------------------------------------------------------------
-- 俱乐部邀请
-------------------------------------------------------------------------------
function LinkHelper.toClubInvite(club_id, rule_id, owner_id, parent, zorder)
    local path = cs.app.CLIENT_SRC .. 'club.ClubRoomInviteLayer'
    helper.pop.popLayer(path, parent, {club_id, rule_id, owner_id}, nil, true):zorder(zorder)
end


-------------------------------------------------------------------------------
-- 系统消息
-------------------------------------------------------------------------------
function LinkHelper.toMessage()
    local path = cs.app.CLIENT_SRC .. 'main.SysMessageLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 背包
-------------------------------------------------------------------------------
function LinkHelper.toBag()
    local path = cs.app.CLIENT_SRC .. 'main.BagLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 比赛场
-------------------------------------------------------------------------------
function LinkHelper.toArena()
    local path = cs.app.CLIENT_SRC .. 'main.ArenaLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 签到
-------------------------------------------------------------------------------
function LinkHelper.toSignIn(parent)
    local path = cs.app.CLIENT_SRC .. 'main.SignInLayer'
    return helper.pop.popLayer(path, parent, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 连场战绩
-------------------------------------------------------------------------------
function LinkHelper.toGroupScore()
    local path = cs.app.CLIENT_SRC .. 'main.GroupScoreLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
end

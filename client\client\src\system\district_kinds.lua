-------------------------------------------------------------------------------
--  创世版1.0
--  地区玩法配置
--      支持全国玩法、省、市、地区各级配置
--  @date 2017-06-05
--  @auth woodoo
-------------------------------------------------------------------------------

--[[
字段说明：
group: 中文，同一类游戏相同，主要是分组配子游戏时使用
game:
    mahjong (麻将)
    fourcards (四副牌)
    land (斗地主)
    shuangkou (双扣)
    thirteen (十三水)
kind: 玩法ID
name: 中文名称
sub: 1，表示是子节点
pos: 数值，若配置了表示固定位置，位置太大自动前移
empty: true, 表示空节点，仅此一个字段即可
review: true, 表示受审核控制
no_private: true, 表示没有私人房
--]]

local kinds = {
    -----------------------------------------------
    -- 全国通用玩法此处定义
    --[[
    {
        group       = '双扣',
        game        = 'shuangkou',
        kind        = 201,
        name        = '双扣',
        review      = true,
    },
    --]]
    -----------------------------------------------
    {
        group       = '斗地主',
        game        = 'land',
        kind        = 200,
        name        = '斗地主',
        review      = true,
    },
    --[[
    {
        group       = '麻将',
        game        = 'mahjong',
        kind        = 431,
        name        = '血战到底',
        review      = true,
    },
    --]]
    {
        group       = '麻将',
        game        = 'mahjong',
        kind        = 300,
        name        = '百搭麻将',
    },
    --[[{
        group       = '麻将',
        game        = 'mahjong',
        kind        = 302,
        name        = '广东麻将',
        review      = true,
    },--]]
    {
        group       = '跑得快',
        game        = 'paodekuai',
        kind        = 202,
        name        = '跑得快',
    },
    {
        group       = 'function',
        game        = 'arena',
        name        = '比赛场',
        review      = true,
    },
    --[[
    {
        group       = '双扣',
        game        = 'shuangkou',
        kind        = 201,
        name        = '双扣',
    },
    --]]
    {
        group       = 'function',
        game        = 'join',
        name        = '加入房间',
        pos         = 7,
    },
    --[[
    {
        group       = 'function',
        game        = 'club',
        name        = '亲友圈',
        review      = true,
    },
    {
        group       = 'function',
        game        = 'district',
        name        = '地区选择',
        review      = true,
    },
    --]]




    ['浙江省'] = {
        -----------------------------------------------
        -- 本省通用玩法此处定义
        -----------------------------------------------
        --[[
        {
            group       = '双扣',
            game        = 'shuangkou',
            kind        = 201,
            name        = '双扣',
        },
        --]]
        ['台州市'] = {
            -----------------------------------------------
            -- 本市通用玩法此处定义
            -----------------------------------------------
            --[[
            {
                group       = '十三水',
                game        = 'thirteen',
                kind        = 104,
                name        = '台州大菠萝',
            },
            --]]
            ['临海市'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 403,
                    name        = '临海麻将',
                },
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 402,
                    name        = '杜桥麻将',
                },
            },
            ['三门县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 301,
                    name        = '三门麻将',
                },
                --[[
                {
                    group       = '十三水',
                    game        = 'thirteen',
                    kind        = 105,
                    name        = '三门大菠萝',
                },
                --]]
            },
            ['天台县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 304,
                    name        = '天台三阿磨',
                },
            },
            ['椒江区'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 305,
                    name        = '台州麻将',
                },
            },
            ['路桥区'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 305,
                    name        = '台州麻将',
                },
            },
            ['黄岩区'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 305,
                    name        = '台州麻将',
                },
            },
            ['温岭市'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 401,
                    name        = '温岭麻将',
                },
            },
            ['仙居县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 303,
                    name        = '仙居麻将',
                },
            },
            ['玉环县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 405,
                    name        = '玉环麻将',
                },
            },
        },
        ['金华市'] = {
            -----------------------------------------------
            -- 本市通用玩法此处定义
            -----------------------------------------------

            ['磐安县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 404,
                    name        = '磐安碰碰胡',
                },
                --[[
                {
                    group       = '四副牌',
                    game        = 'fourcards',
                    kind        = 101,
                    name        = '四副牌',
                },
                {
                    group       = '十三水',
                    game        = 'thirteen',
                    kind        = 100,
                    name        = '磐安十三水',
                },
                --]]
            },
        },
        ['湖州市'] = {
            -----------------------------------------------
            -- 本市通用玩法此处定义
            -----------------------------------------------
            --[[
            {
                group       = '红十',
                game        = 'redten',
                kind        = 110,
                name        = '红十',
            },
            --]]
            ['吴兴区'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 412,
                    name        = '湖州推倒胡',
                },
            },
            ['南浔区'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 412,
                    name        = '湖州推倒胡',
                },
            },
            ['德清县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 411,
                    name        = '德清麻将',
                },
            },
            ['长兴县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 414,
                    name        = '长兴麻将',
                },
            },
            ['安吉县'] = { -- 各县区玩法定义
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 413,
                    name        = '安吉麻将',
                },
            },
        },
    },


    ['安徽省'] = {
        -----------------------------------------------
        -- 本省通用玩法此处定义
        -----------------------------------------------

        ['六安市'] = {
            -----------------------------------------------
            -- 本市通用玩法此处定义
            -----------------------------------------------

            ['霍邱县'] = {
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 308,
                    name        = '霍邱麻将',
                },
            },
            ['寿县'] = {
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 306,
                    name        = '寿县麻将',
                },
            },
        },
    },


    ['江苏省'] = {
        -----------------------------------------------
        -- 本省通用玩法此处定义
        -----------------------------------------------
        {
            group       = '麻将',
            game        = 'mahjong',
            kind        = 302,
            name        = '广麻',
        },

        ['苏州市'] = {
            -----------------------------------------------
            -- 本市通用玩法此处定义
            -----------------------------------------------

            ['张家港市'] = {
                {
                    group       = '麻将',
                    game        = 'mahjong',
                    kind        = 309,
                    name        = '花麻',
                },
            },
        },
    },
}

if cs and cs.app and cs.app.district_kinds then
    kinds = cs.app.district_kinds
end


local M = {}


-------------------------------------------------------------------------------
-- 获取所有玩法
--  外部调用只需：local t = ...getAllKinds()
-------------------------------------------------------------------------------
function M.getAllKinds(in_kinds, out_t)
    local arr = in_kinds or kinds
    local t = out_t or {}
    for _, v in pairs(arr) do
        if type(v) == 'table' then
            if v.kind then
                table.insert(t, v)
            else
                M.getAllKinds(v, t)
            end
        end
    end
    return t
end


-------------------------------------------------------------------------------
-- 按省、市、区获取所有玩法
--  顺序：地区，市，省，全国
-------------------------------------------------------------------------------
function M.getKinds(province, city, district)
    local t = {}
    local add = function(configs)
        if configs then
            for i, cfg in ipairs(configs) do
                table.insert(t, i, clone(cfg))
            end
        end
    end

    if cs and cs.app and cs.app.plugins_ext then
        add(cs.app.plugins_ext) -- 加app_ext中的额外配置
    end

    add(kinds)  -- 加全国
    local province_t = kinds[province]
    if province_t then
        add(province_t) -- 加省
        local city_t = province_t[city]
        if city_t then
            add(city_t) -- 加市
            local district_t = city_t[district]
            if district_t then
                add(district_t) -- 加地区
            end
        end
    end

    -- 固定位置实现
    local plugins = {}
    local max_pos = #t
    for i, plugin in ipairs(t) do    -- 先防止所有固定位置项目
        if plugin.pos and type(plugin.pos) == 'number' then
            plugins[plugin.pos] = plugin
            max_pos = math.max(max_pos, plugin.pos)
        end
    end
    local index = 1
    for i, plugin in ipairs(t) do    -- 其它非固定项目依次插入空隙
        if not plugin.pos or type(plugin.pos) ~= 'number' then
            while plugins[index] do
                index = index + 1
            end
            plugins[index] = plugin
        end
    end
    for i = 1, max_pos do   -- 必须把空位补成非nil，否则remove不生效
        if not plugins[i] then
            plugins[i] = 0
        end
    end
    for i = max_pos, 1, -1 do   -- 去掉空位
        if plugins[i] == 0 then
            table.remove(plugins, i)
        end
    end

    return plugins
end


return M

-- This is the DeprecatedClass

DeprecatedClass = {} or DeprecatedClass

--tip
local function deprecatedTip(old_name,new_name)
    print("\n********** \n"..old_name.." was deprecated please use ".. new_name .. " instead.\n**********")
end

--CCProgressTo class will be Deprecated,begin
function DeprecatedClass.CCProgressTo()
    deprecatedTip("CCProgressTo","cc.ProgressTo")
    return cc.ProgressTo
end
_G["CCProgressTo"] = DeprecatedClass.CCProgressTo()
--CCProgressTo class will be Deprecated,end

--CCHide class will be Deprecated,begin
function DeprecatedClass.CCHide()
    deprecatedTip("CCHide","cc.Hide")
    return cc.Hide
end
_G["CCHide"] = DeprecatedClass.CCHide()
--CCHide class will be Deprecated,end

--CCTransitionMoveInB class will be Deprecated,begin
function DeprecatedClass.CCTransitionMoveInB()
    deprecatedTip("CCTransitionMoveInB","cc.TransitionMoveInB")
    return cc.TransitionMoveInB
end
_G["CCTransitionMoveInB"] = DeprecatedClass.CCTransitionMoveInB()
--CCTransitionMoveInB class will be Deprecated,end

--CCEaseSineIn class will be Deprecated,begin
function DeprecatedClass.CCEaseSineIn()
    deprecatedTip("CCEaseSineIn","cc.EaseSineIn")
    return cc.EaseSineIn
end
_G["CCEaseSineIn"] = DeprecatedClass.CCEaseSineIn()
--CCEaseSineIn class will be Deprecated,end

--CCTransitionMoveInL class will be Deprecated,begin
function DeprecatedClass.CCTransitionMoveInL()
    deprecatedTip("CCTransitionMoveInL","cc.TransitionMoveInL")
    return cc.TransitionMoveInL
end
_G["CCTransitionMoveInL"] = DeprecatedClass.CCTransitionMoveInL()
--CCTransitionMoveInL class will be Deprecated,end

--CCEaseInOut class will be Deprecated,begin
function DeprecatedClass.CCEaseInOut()
    deprecatedTip("CCEaseInOut","cc.EaseInOut")
    return cc.EaseInOut
end
_G["CCEaseInOut"] = DeprecatedClass.CCEaseInOut()
--CCEaseInOut class will be Deprecated,end

--CCTransitionMoveInT class will be Deprecated,begin
function DeprecatedClass.CCTransitionMoveInT()
    deprecatedTip("CCTransitionMoveInT","cc.TransitionMoveInT")
    return cc.TransitionMoveInT
end
_G["CCTransitionMoveInT"] = DeprecatedClass.CCTransitionMoveInT()
--CCTransitionMoveInT class will be Deprecated,end

--CCTransitionMoveInR class will be Deprecated,begin
function DeprecatedClass.CCTransitionMoveInR()
    deprecatedTip("CCTransitionMoveInR","cc.TransitionMoveInR")
    return cc.TransitionMoveInR
end
_G["CCTransitionMoveInR"] = DeprecatedClass.CCTransitionMoveInR()
--CCTransitionMoveInR class will be Deprecated,end

--CCParticleSnow class will be Deprecated,begin
function DeprecatedClass.CCParticleSnow()
    deprecatedTip("CCParticleSnow","cc.ParticleSnow")
    return cc.ParticleSnow
end
_G["CCParticleSnow"] = DeprecatedClass.CCParticleSnow()
--CCParticleSnow class will be Deprecated,end

--CCActionCamera class will be Deprecated,begin
function DeprecatedClass.CCActionCamera()
    deprecatedTip("CCActionCamera","cc.ActionCamera")
    return cc.ActionCamera
end
_G["CCActionCamera"] = DeprecatedClass.CCActionCamera()
--CCActionCamera class will be Deprecated,end

--CCProgressFromTo class will be Deprecated,begin
function DeprecatedClass.CCProgressFromTo()
    deprecatedTip("CCProgressFromTo","cc.ProgressFromTo")
    return cc.ProgressFromTo
end
_G["CCProgressFromTo"] = DeprecatedClass.CCProgressFromTo()
--CCProgressFromTo class will be Deprecated,end

--CCMoveTo class will be Deprecated,begin
function DeprecatedClass.CCMoveTo()
    deprecatedTip("CCMoveTo","cc.MoveTo")
    return cc.MoveTo
end
_G["CCMoveTo"] = DeprecatedClass.CCMoveTo()
--CCMoveTo class will be Deprecated,end

--CCJumpBy class will be Deprecated,begin
function DeprecatedClass.CCJumpBy()
    deprecatedTip("CCJumpBy","cc.JumpBy")
    return cc.JumpBy
end
_G["CCJumpBy"] = DeprecatedClass.CCJumpBy()
--CCJumpBy class will be Deprecated,end

--CCObject class will be Deprecated,begin
function DeprecatedClass.CCObject()
    deprecatedTip("CCObject","cc.Object")
    return cc.Object
end
_G["CCObject"] = DeprecatedClass.CCObject()
--CCObject class will be Deprecated,end

--CCTransitionRotoZoom class will be Deprecated,begin
function DeprecatedClass.CCTransitionRotoZoom()
    deprecatedTip("CCTransitionRotoZoom","cc.TransitionRotoZoom")
    return cc.TransitionRotoZoom
end
_G["CCTransitionRotoZoom"] = DeprecatedClass.CCTransitionRotoZoom()
--CCTransitionRotoZoom class will be Deprecated,end

--CCDirector class will be Deprecated,begin
function DeprecatedClass.CCDirector()
    deprecatedTip("CCDirector","cc.Director")
    return cc.Director
end
_G["CCDirector"] = DeprecatedClass.CCDirector()
--CCDirector class will be Deprecated,end

--CCScheduler class will be Deprecated,begin
function DeprecatedClass.CCScheduler()
    deprecatedTip("CCScheduler","cc.Scheduler")
    return cc.Scheduler
end
_G["CCScheduler"] = DeprecatedClass.CCScheduler()
--CCScheduler class will be Deprecated,end

--CCEaseElasticOut class will be Deprecated,begin
function DeprecatedClass.CCEaseElasticOut()
    deprecatedTip("CCEaseElasticOut","cc.EaseElasticOut")
    return cc.EaseElasticOut
end
_G["CCEaseElasticOut"] = DeprecatedClass.CCEaseElasticOut()
--CCEaseElasticOut class will be Deprecated,end

--CCTableViewCell class will be Deprecated,begin
function DeprecatedClass.CCTableViewCell()
    deprecatedTip("CCTableViewCell","cc.TableViewCell")
    return cc.TableViewCell
end
_G["CCTableViewCell"] = DeprecatedClass.CCTableViewCell()
--CCTableViewCell class will be Deprecated,end


--CCEaseBackOut class will be Deprecated,begin
function DeprecatedClass.CCEaseBackOut()
    deprecatedTip("CCEaseBackOut","cc.EaseBackOut")
    return cc.EaseBackOut
end
_G["CCEaseBackOut"] = DeprecatedClass.CCEaseBackOut()
--CCEaseBackOut class will be Deprecated,end

--CCParticleSystemQuad class will be Deprecated,begin
function DeprecatedClass.CCParticleSystemQuad()
    deprecatedTip("CCParticleSystemQuad","cc.ParticleSystemQuad")
    return cc.ParticleSystemQuad
end
_G["CCParticleSystemQuad"] = DeprecatedClass.CCParticleSystemQuad()
--CCParticleSystemQuad class will be Deprecated,end

--CCMenuItemToggle class will be Deprecated,begin
function DeprecatedClass.CCMenuItemToggle()
    deprecatedTip("CCMenuItemToggle","cc.MenuItemToggle")
    return cc.MenuItemToggle
end
_G["CCMenuItemToggle"] = DeprecatedClass.CCMenuItemToggle()
--CCMenuItemToggle class will be Deprecated,end

--CCStopGrid class will be Deprecated,begin
function DeprecatedClass.CCStopGrid()
    deprecatedTip("CCStopGrid","cc.StopGrid")
    return cc.StopGrid
end
_G["CCStopGrid"] = DeprecatedClass.CCStopGrid()
--CCStopGrid class will be Deprecated,end

--CCTransitionScene class will be Deprecated,begin
function DeprecatedClass.CCTransitionScene()
    deprecatedTip("CCTransitionScene","cc.TransitionScene")
    return cc.TransitionScene
end
_G["CCTransitionScene"] = DeprecatedClass.CCTransitionScene()
--CCTransitionScene class will be Deprecated,end

--CCSkewBy class will be Deprecated,begin
function DeprecatedClass.CCSkewBy()
    deprecatedTip("CCSkewBy","cc.SkewBy")
    return cc.SkewBy
end
_G["CCSkewBy"] = DeprecatedClass.CCSkewBy()
--CCSkewBy class will be Deprecated,end

--CCLayer class will be Deprecated,begin
function DeprecatedClass.CCLayer()
    deprecatedTip("CCLayer","cc.Layer")
    return cc.Layer
end
_G["CCLayer"] = DeprecatedClass.CCLayer()
--CCLayer class will be Deprecated,end

--CCEaseElastic class will be Deprecated,begin
function DeprecatedClass.CCEaseElastic()
    deprecatedTip("CCEaseElastic","cc.EaseElastic")
    return cc.EaseElastic
end
_G["CCEaseElastic"] = DeprecatedClass.CCEaseElastic()
--CCEaseElastic class will be Deprecated,end

--CCTMXTiledMap class will be Deprecated,begin
function DeprecatedClass.CCTMXTiledMap()
    deprecatedTip("CCTMXTiledMap","cc.TMXTiledMap")
    return cc.TMXTiledMap
end
_G["CCTMXTiledMap"] = DeprecatedClass.CCTMXTiledMap()
--CCTMXTiledMap class will be Deprecated,end

--CCGrid3DAction class will be Deprecated,begin
function DeprecatedClass.CCGrid3DAction()
    deprecatedTip("CCGrid3DAction","cc.Grid3DAction")
    return cc.Grid3DAction
end
_G["CCGrid3DAction"] = DeprecatedClass.CCGrid3DAction()
--CCGrid3DAction class will be Deprecated,end

--CCFadeIn class will be Deprecated,begin
function DeprecatedClass.CCFadeIn()
    deprecatedTip("CCFadeIn","cc.FadeIn")
    return cc.FadeIn
end
_G["CCFadeIn"] = DeprecatedClass.CCFadeIn()
--CCFadeIn class will be Deprecated,end

--CCNodeRGBA class will be Deprecated,begin
function DeprecatedClass.CCNodeRGBA()
    deprecatedTip("CCNodeRGBA","cc.Node")
    return cc.Node
end
_G["CCNodeRGBA"] = DeprecatedClass.CCNodeRGBA()
--CCNodeRGBA class will be Deprecated,end

--NodeRGBA class will be Deprecated,begin
function DeprecatedClass.NodeRGBA()
    deprecatedTip("cc.NodeRGBA","cc.Node")
    return cc.Node
end
_G["cc"]["NodeRGBA"] = DeprecatedClass.NodeRGBA()
--NodeRGBA class will be Deprecated,end

--CCAnimationCache class will be Deprecated,begin
function DeprecatedClass.CCAnimationCache()
    deprecatedTip("CCAnimationCache","cc.AnimationCache")
    return cc.AnimationCache
end
_G["CCAnimationCache"] = DeprecatedClass.CCAnimationCache()
--CCAnimationCache class will be Deprecated,end

--CCFlipY3D class will be Deprecated,begin
function DeprecatedClass.CCFlipY3D()
    deprecatedTip("CCFlipY3D","cc.FlipY3D")
    return cc.FlipY3D
end
_G["CCFlipY3D"] = DeprecatedClass.CCFlipY3D()
--CCFlipY3D class will be Deprecated,end

--CCEaseSineInOut class will be Deprecated,begin
function DeprecatedClass.CCEaseSineInOut()
    deprecatedTip("CCEaseSineInOut","cc.EaseSineInOut")
    return cc.EaseSineInOut
end
_G["CCEaseSineInOut"] = DeprecatedClass.CCEaseSineInOut()
--CCEaseSineInOut class will be Deprecated,end

--CCTransitionFlipAngular class will be Deprecated,begin
function DeprecatedClass.CCTransitionFlipAngular()
    deprecatedTip("CCTransitionFlipAngular","cc.TransitionFlipAngular")
    return cc.TransitionFlipAngular
end
_G["CCTransitionFlipAngular"] = DeprecatedClass.CCTransitionFlipAngular()
--CCTransitionFlipAngular class will be Deprecated,end

--CCEaseElasticInOut class will be Deprecated,begin
function DeprecatedClass.CCEaseElasticInOut()
    deprecatedTip("CCEaseElasticInOut","cc.EaseElasticInOut")
    return cc.EaseElasticInOut
end
_G["CCEaseElasticInOut"] = DeprecatedClass.CCEaseElasticInOut()
--CCEaseElasticInOut class will be Deprecated,end

--CCEaseBounce class will be Deprecated,begin
function DeprecatedClass.CCEaseBounce()
    deprecatedTip("CCEaseBounce","cc.EaseBounce")
    return cc.EaseBounce
end
_G["CCEaseBounce"] = DeprecatedClass.CCEaseBounce()
--CCEaseBounce class will be Deprecated,end

--CCShow class will be Deprecated,begin
function DeprecatedClass.CCShow()
    deprecatedTip("CCShow","cc.Show")
    return cc.Show
end
_G["CCShow"] = DeprecatedClass.CCShow()
--CCShow class will be Deprecated,end

--CCFadeOut class will be Deprecated,begin
function DeprecatedClass.CCFadeOut()
    deprecatedTip("CCFadeOut","cc.FadeOut")
    return cc.FadeOut
end
_G["CCFadeOut"] = DeprecatedClass.CCFadeOut()
--CCFadeOut class will be Deprecated,end

--CCCallFunc class will be Deprecated,begin
function DeprecatedClass.CCCallFunc()
    deprecatedTip("CCCallFunc","cc.CallFunc")
    return cc.CallFunc
end
_G["CCCallFunc"] = DeprecatedClass.CCCallFunc()
--CCCallFunc class will be Deprecated,end

--CCWaves3D class will be Deprecated,begin
function DeprecatedClass.CCWaves3D()
    deprecatedTip("CCWaves3D","cc.Waves3D")
    return cc.Waves3D
end
_G["CCWaves3D"] = DeprecatedClass.CCWaves3D()
--CCWaves3D class will be Deprecated,end

--CCFlipX3D class will be Deprecated,begin
function DeprecatedClass.CCFlipX3D()
    deprecatedTip("CCFlipX3D","cc.FlipX3D")
    return cc.FlipX3D
end
_G["CCFlipX3D"] = DeprecatedClass.CCFlipX3D()
--CCFlipX3D class will be Deprecated,end

--CCParticleFireworks class will be Deprecated,begin
function DeprecatedClass.CCParticleFireworks()
    deprecatedTip("CCParticleFireworks","cc.ParticleFireworks")
    return cc.ParticleFireworks
end
_G["CCParticleFireworks"] = DeprecatedClass.CCParticleFireworks()
--CCParticleFireworks class will be Deprecated,end

--CCMenuItemImage class will be Deprecated,begin
function DeprecatedClass.CCMenuItemImage()
    deprecatedTip("CCMenuItemImage","cc.MenuItemImage")
    return cc.MenuItemImage
end
_G["CCMenuItemImage"] = DeprecatedClass.CCMenuItemImage()
--CCMenuItemImage class will be Deprecated,end

--CCParticleFire class will be Deprecated,begin
function DeprecatedClass.CCParticleFire()
    deprecatedTip("CCParticleFire","cc.ParticleFire")
    return cc.ParticleFire
end
_G["CCParticleFire"] = DeprecatedClass.CCParticleFire()
--CCParticleFire class will be Deprecated,end

--CCMenuItem class will be Deprecated,begin
function DeprecatedClass.CCMenuItem()
    deprecatedTip("CCMenuItem","cc.MenuItem")
    return cc.MenuItem
end
_G["CCMenuItem"] = DeprecatedClass.CCMenuItem()
--CCMenuItem class will be Deprecated,end

--CCActionEase class will be Deprecated,begin
function DeprecatedClass.CCActionEase()
    deprecatedTip("CCActionEase","cc.ActionEase")
    return cc.ActionEase
end
_G["CCActionEase"] = DeprecatedClass.CCActionEase()
--CCActionEase class will be Deprecated,end

--CCTransitionSceneOriented class will be Deprecated,begin
function DeprecatedClass.CCTransitionSceneOriented()
    deprecatedTip("CCTransitionSceneOriented","cc.TransitionSceneOriented")
    return cc.TransitionSceneOriented
end
_G["CCTransitionSceneOriented"] = DeprecatedClass.CCTransitionSceneOriented()
--CCTransitionSceneOriented class will be Deprecated,end

--CCTransitionZoomFlipAngular class will be Deprecated,begin
function DeprecatedClass.CCTransitionZoomFlipAngular()
    deprecatedTip("CCTransitionZoomFlipAngular","cc.TransitionZoomFlipAngular")
    return cc.TransitionZoomFlipAngular
end
_G["CCTransitionZoomFlipAngular"] = DeprecatedClass.CCTransitionZoomFlipAngular()
--CCTransitionZoomFlipAngular class will be Deprecated,end

--CCEaseIn class will be Deprecated,begin
function DeprecatedClass.CCEaseIn()
    deprecatedTip("CCEaseIn","cc.EaseIn")
    return cc.EaseIn
end
_G["CCEaseIn"] = DeprecatedClass.CCEaseIn()
--CCEaseIn class will be Deprecated,end

--CCEaseExponentialInOut class will be Deprecated,begin
function DeprecatedClass.CCEaseExponentialInOut()
    deprecatedTip("CCEaseExponentialInOut","cc.EaseExponentialInOut")
    return cc.EaseExponentialInOut
end
_G["CCEaseExponentialInOut"] = DeprecatedClass.CCEaseExponentialInOut()
--CCEaseExponentialInOut class will be Deprecated,end

--CCTransitionFlipX class will be Deprecated,begin
function DeprecatedClass.CCTransitionFlipX()
    deprecatedTip("CCTransitionFlipX","cc.TransitionFlipX")
    return cc.TransitionFlipX
end
_G["CCTransitionFlipX"] = DeprecatedClass.CCTransitionFlipX()
--CCTransitionFlipX class will be Deprecated,end

--CCEaseExponentialOut class will be Deprecated,begin
function DeprecatedClass.CCEaseExponentialOut()
    deprecatedTip("CCEaseExponentialOut","cc.EaseExponentialOut")
    return cc.EaseExponentialOut
end
_G["CCEaseExponentialOut"] = DeprecatedClass.CCEaseExponentialOut()
--CCEaseExponentialOut class will be Deprecated,end

--CCLabel class will be Deprecated,begin
function DeprecatedClass.CCLabel()
    deprecatedTip("CCLabel","cc.Label")
    return cc.Label
end
_G["CCLabel"] = DeprecatedClass.CCLabel()
--CCLabel class will be Deprecated,end

--CCApplication class will be Deprecated,begin
function DeprecatedClass.CCApplication()
    deprecatedTip("CCApplication","cc.Application")
    return cc.Application
end
_G["CCApplication"] = DeprecatedClass.CCApplication()
--CCApplication class will be Deprecated,end

--CCDelayTime class will be Deprecated,begin
function DeprecatedClass.CCDelayTime()
    deprecatedTip("CCDelayTime","cc.DelayTime")
    return cc.DelayTime
end
_G["CCDelayTime"] = DeprecatedClass.CCDelayTime()
--CCDelayTime class will be Deprecated,end

--CCLabelAtlas class will be Deprecated,begin
function DeprecatedClass.CCLabelAtlas()
    deprecatedTip("CCLabelAtlas","cc.LabelAtlas")
    return cc.LabelAtlas
end
_G["CCLabelAtlas"] = DeprecatedClass.CCLabelAtlas()
--CCLabelAtlas class will be Deprecated,end

--CCLabelBMFont class will be Deprecated,begin
function DeprecatedClass.CCLabelBMFont()
    deprecatedTip("CCLabelBMFont","cc.LabelBMFont")
    return cc.LabelBMFont
end
_G["CCLabelBMFont"] = DeprecatedClass.CCLabelBMFont()
--CCLabelBMFont class will be Deprecated,end

--CCFadeOutTRTiles class will be Deprecated,begin
function DeprecatedClass.CCFadeOutTRTiles()
    deprecatedTip("CCFadeOutTRTiles","cc.FadeOutTRTiles")
    return cc.FadeOutTRTiles
end
_G["CCFadeOutTRTiles"] = DeprecatedClass.CCFadeOutTRTiles()
--CCFadeOutTRTiles class will be Deprecated,end

--CCEaseElasticIn class will be Deprecated,begin
function DeprecatedClass.CCEaseElasticIn()
    deprecatedTip("CCEaseElasticIn","cc.EaseElasticIn")
    return cc.EaseElasticIn
end
_G["CCEaseElasticIn"] = DeprecatedClass.CCEaseElasticIn()
--CCEaseElasticIn class will be Deprecated,end

--CCParticleSpiral class will be Deprecated,begin
function DeprecatedClass.CCParticleSpiral()
    deprecatedTip("CCParticleSpiral","cc.ParticleSpiral")
    return cc.ParticleSpiral
end
_G["CCParticleSpiral"] = DeprecatedClass.CCParticleSpiral()
--CCParticleSpiral class will be Deprecated,end

--CCFiniteTimeAction class will be Deprecated,begin
function DeprecatedClass.CCFiniteTimeAction()
    deprecatedTip("CCFiniteTimeAction","cc.FiniteTimeAction")
    return cc.FiniteTimeAction
end
_G["CCFiniteTimeAction"] = DeprecatedClass.CCFiniteTimeAction()
--CCFiniteTimeAction class will be Deprecated,end

--CCFadeOutDownTiles class will be Deprecated,begin
function DeprecatedClass.CCFadeOutDownTiles()
    deprecatedTip("CCFadeOutDownTiles","cc.FadeOutDownTiles")
    return cc.FadeOutDownTiles
end
_G["CCFadeOutDownTiles"] = DeprecatedClass.CCFadeOutDownTiles()
--CCFadeOutDownTiles class will be Deprecated,end

--CCJumpTiles3D class will be Deprecated,begin
function DeprecatedClass.CCJumpTiles3D()
    deprecatedTip("CCJumpTiles3D","cc.JumpTiles3D")
    return cc.JumpTiles3D
end
_G["CCJumpTiles3D"] = DeprecatedClass.CCJumpTiles3D()
--CCJumpTiles3D class will be Deprecated,end

--CCEaseBackIn class will be Deprecated,begin
function DeprecatedClass.CCEaseBackIn()
    deprecatedTip("CCEaseBackIn","cc.EaseBackIn")
    return cc.EaseBackIn
end
_G["CCEaseBackIn"] = DeprecatedClass.CCEaseBackIn()
--CCEaseBackIn class will be Deprecated,end

--CCSpriteBatchNode class will be Deprecated,begin
function DeprecatedClass.CCSpriteBatchNode()
    deprecatedTip("CCSpriteBatchNode","cc.SpriteBatchNode")
    return cc.SpriteBatchNode
end
_G["CCSpriteBatchNode"] = DeprecatedClass.CCSpriteBatchNode()
--CCSpriteBatchNode class will be Deprecated,end

--CCParticleSystem class will be Deprecated,begin
function DeprecatedClass.CCParticleSystem()
    deprecatedTip("CCParticleSystem","cc.ParticleSystem")
    return cc.ParticleSystem
end
_G["CCParticleSystem"] = DeprecatedClass.CCParticleSystem()
--CCParticleSystem class will be Deprecated,end

--CCActionTween class will be Deprecated,begin
function DeprecatedClass.CCActionTween()
    deprecatedTip("CCActionTween","cc.ActionTween")
    return cc.ActionTween
end
_G["CCActionTween"] = DeprecatedClass.CCActionTween()
--CCActionTween class will be Deprecated,end

--CCTransitionFadeDown class will be Deprecated,begin
function DeprecatedClass.CCTransitionFadeDown()
    deprecatedTip("CCTransitionFadeDown","cc.TransitionFadeDown")
    return cc.TransitionFadeDown
end
_G["CCTransitionFadeDown"] = DeprecatedClass.CCTransitionFadeDown()
--CCTransitionFadeDown class will be Deprecated,end

--CCParticleSun class will be Deprecated,begin
function DeprecatedClass.CCParticleSun()
    deprecatedTip("CCParticleSun","cc.ParticleSun")
    return cc.ParticleSun
end
_G["CCParticleSun"] = DeprecatedClass.CCParticleSun()
--CCParticleSun class will be Deprecated,end

--CCTransitionProgressHorizontal class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgressHorizontal()
    deprecatedTip("CCTransitionProgressHorizontal","cc.TransitionProgressHorizontal")
    return cc.TransitionProgressHorizontal
end
_G["CCTransitionProgressHorizontal"] = DeprecatedClass.CCTransitionProgressHorizontal()
--CCTransitionProgressHorizontal class will be Deprecated,end

--CCRipple3D class will be Deprecated,begin
function DeprecatedClass.CCRipple3D()
    deprecatedTip("CCRipple3D","cc.Ripple3D")
    return cc.Ripple3D
end
_G["CCRipple3D"] = DeprecatedClass.CCRipple3D()
--CCRipple3D class will be Deprecated,end

--CCTMXLayer class will be Deprecated,begin
function DeprecatedClass.CCTMXLayer()
    deprecatedTip("CCTMXLayer","cc.TMXLayer")
    return cc.TMXLayer
end
_G["CCTMXLayer"] = DeprecatedClass.CCTMXLayer()
--CCTMXLayer class will be Deprecated,end

--CCFlipX class will be Deprecated,begin
function DeprecatedClass.CCFlipX()
    deprecatedTip("CCFlipX","cc.FlipX")
    return cc.FlipX
end
_G["CCFlipX"] = DeprecatedClass.CCFlipX()
--CCFlipX class will be Deprecated,end

--CCFlipY class will be Deprecated,begin
function DeprecatedClass.CCFlipY()
    deprecatedTip("CCFlipY","cc.FlipY")
    return cc.FlipY
end
_G["CCFlipY"] = DeprecatedClass.CCFlipY()
--CCFlipY class will be Deprecated,end

--CCTransitionSplitCols class will be Deprecated,begin
function DeprecatedClass.CCTransitionSplitCols()
    deprecatedTip("CCTransitionSplitCols","cc.TransitionSplitCols")
    return cc.TransitionSplitCols
end
_G["CCTransitionSplitCols"] = DeprecatedClass.CCTransitionSplitCols()
--CCTransitionSplitCols class will be Deprecated,end

--CCTimer class will be Deprecated,begin
function DeprecatedClass.CCTimer()
    deprecatedTip("CCTimer","cc.Timer")
    return cc.Timer
end
_G["CCTimer"] = DeprecatedClass.CCTimer()
--CCTimer class will be Deprecated,end

--CCFadeTo class will be Deprecated,begin
function DeprecatedClass.CCFadeTo()
    deprecatedTip("CCFadeTo","cc.FadeTo")
    return cc.FadeTo
end
_G["CCFadeTo"] = DeprecatedClass.CCFadeTo()
--CCFadeTo class will be Deprecated,end

--CCRepeatForever class will be Deprecated,begin
function DeprecatedClass.CCRepeatForever()
    deprecatedTip("CCRepeatForever","cc.RepeatForever")
    return cc.RepeatForever
end
_G["CCRepeatForever"] = DeprecatedClass.CCRepeatForever()
--CCRepeatForever class will be Deprecated,end

--CCPlace class will be Deprecated,begin
function DeprecatedClass.CCPlace()
    deprecatedTip("CCPlace","cc.Place")
    return cc.Place
end
_G["CCPlace"] = DeprecatedClass.CCPlace()
--CCPlace class will be Deprecated,end


--CCGLProgram class will be Deprecated,begin
function DeprecatedClass.CCGLProgram()
    deprecatedTip("CCGLProgram","cc.GLProgram")
    return cc.GLProgram
end
_G["CCGLProgram"] = DeprecatedClass.CCGLProgram()
--CCGLProgram class will be Deprecated,end

--CCEaseBounceOut class will be Deprecated,begin
function DeprecatedClass.CCEaseBounceOut()
    deprecatedTip("CCEaseBounceOut","cc.EaseBounceOut")
    return cc.EaseBounceOut
end
_G["CCEaseBounceOut"] = DeprecatedClass.CCEaseBounceOut()
--CCEaseBounceOut class will be Deprecated,end

--CCCardinalSplineBy class will be Deprecated,begin
function DeprecatedClass.CCCardinalSplineBy()
    deprecatedTip("CCCardinalSplineBy","cc.CardinalSplineBy")
    return cc.CardinalSplineBy
end
_G["CCCardinalSplineBy"] = DeprecatedClass.CCCardinalSplineBy()
--CCCardinalSplineBy class will be Deprecated,end

--CCSpriteFrameCache class will be Deprecated,begin
function DeprecatedClass.CCSpriteFrameCache()
    deprecatedTip("CCSpriteFrameCache","cc.SpriteFrameCache")
    return cc.SpriteFrameCache
end
_G["CCSpriteFrameCache"] = DeprecatedClass.CCSpriteFrameCache()
--CCSpriteFrameCache class will be Deprecated,end

--CCTransitionShrinkGrow class will be Deprecated,begin
function DeprecatedClass.CCTransitionShrinkGrow()
    deprecatedTip("CCTransitionShrinkGrow","cc.TransitionShrinkGrow")
    return cc.TransitionShrinkGrow
end
_G["CCTransitionShrinkGrow"] = DeprecatedClass.CCTransitionShrinkGrow()
--CCTransitionShrinkGrow class will be Deprecated,end

--CCSplitCols class will be Deprecated,begin
function DeprecatedClass.CCSplitCols()
    deprecatedTip("CCSplitCols","cc.SplitCols")
    return cc.SplitCols
end
_G["CCSplitCols"] = DeprecatedClass.CCSplitCols()
--CCSplitCols class will be Deprecated,end

--CCClippingNode class will be Deprecated,begin
function DeprecatedClass.CCClippingNode()
    deprecatedTip("CCClippingNode","cc.ClippingNode")
    return cc.ClippingNode
end
_G["CCClippingNode"] = DeprecatedClass.CCClippingNode()
--CCClippingNode class will be Deprecated,end

--CCEaseBounceInOut class will be Deprecated,begin
function DeprecatedClass.CCEaseBounceInOut()
    deprecatedTip("CCEaseBounceInOut","cc.EaseBounceInOut")
    return cc.EaseBounceInOut
end
_G["CCEaseBounceInOut"] = DeprecatedClass.CCEaseBounceInOut()
--CCEaseBounceInOut class will be Deprecated,end

--CCLiquid class will be Deprecated,begin
function DeprecatedClass.CCLiquid()
    deprecatedTip("CCLiquid","cc.Liquid")
    return cc.Liquid
end
_G["CCLiquid"] = DeprecatedClass.CCLiquid()
--CCLiquid class will be Deprecated,end

--CCParticleFlower class will be Deprecated,begin
function DeprecatedClass.CCParticleFlower()
    deprecatedTip("CCParticleFlower","cc.ParticleFlower")
    return cc.ParticleFlower
end
_G["CCParticleFlower"] = DeprecatedClass.CCParticleFlower()
--CCParticleFlower class will be Deprecated,end

--CCParticleSmoke class will be Deprecated,begin
function DeprecatedClass.CCParticleSmoke()
    deprecatedTip("CCParticleSmoke","cc.ParticleSmoke")
    return cc.ParticleSmoke
end
_G["CCParticleSmoke"] = DeprecatedClass.CCParticleSmoke()
--CCParticleSmoke class will be Deprecated,end

--CCImage class will be Deprecated,begin
function DeprecatedClass.CCImage()
    deprecatedTip("CCImage","cc.Image")
    return cc.Image
end
_G["CCImage"] = DeprecatedClass.CCImage()
--CCImage class will be Deprecated,end

--CCTurnOffTiles class will be Deprecated,begin
function DeprecatedClass.CCTurnOffTiles()
    deprecatedTip("CCTurnOffTiles","cc.TurnOffTiles")
    return cc.TurnOffTiles
end
_G["CCTurnOffTiles"] = DeprecatedClass.CCTurnOffTiles()
--CCTurnOffTiles class will be Deprecated,end

--CCBlink class will be Deprecated,begin
function DeprecatedClass.CCBlink()
    deprecatedTip("CCBlink","cc.Blink")
    return cc.Blink
end
_G["CCBlink"] = DeprecatedClass.CCBlink()
--CCBlink class will be Deprecated,end

--CCShaderCache class will be Deprecated,begin
function DeprecatedClass.CCShaderCache()
    deprecatedTip("CCShaderCache","cc.ShaderCache")
    return cc.ShaderCache
end
_G["CCShaderCache"] = DeprecatedClass.CCShaderCache()
--CCShaderCache class will be Deprecated,end

--CCJumpTo class will be Deprecated,begin
function DeprecatedClass.CCJumpTo()
    deprecatedTip("CCJumpTo","cc.JumpTo")
    return cc.JumpTo
end
_G["CCJumpTo"] = DeprecatedClass.CCJumpTo()
--CCJumpTo class will be Deprecated,end

--CCAtlasNode class will be Deprecated,begin
function DeprecatedClass.CCAtlasNode()
    deprecatedTip("CCAtlasNode","cc.AtlasNode")
    return cc.AtlasNode
end
_G["CCAtlasNode"] = DeprecatedClass.CCAtlasNode()
--CCAtlasNode class will be Deprecated,end

--CCTransitionJumpZoom class will be Deprecated,begin
function DeprecatedClass.CCTransitionJumpZoom()
    deprecatedTip("CCTransitionJumpZoom","cc.TransitionJumpZoom")
    return cc.TransitionJumpZoom
end
_G["CCTransitionJumpZoom"] = DeprecatedClass.CCTransitionJumpZoom()
--CCTransitionJumpZoom class will be Deprecated,end

--CCTransitionProgressVertical class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgressVertical()
    deprecatedTip("CCTransitionProgressVertical","cc.TransitionProgressVertical")
    return cc.TransitionProgressVertical
end
_G["CCTransitionProgressVertical"] = DeprecatedClass.CCTransitionProgressVertical()
--CCTransitionProgressVertical class will be Deprecated,end

--CCAnimationFrame class will be Deprecated,begin
function DeprecatedClass.CCAnimationFrame()
    deprecatedTip("CCAnimationFrame","cc.AnimationFrame")
    return cc.AnimationFrame
end
_G["CCAnimationFrame"] = DeprecatedClass.CCAnimationFrame()
--CCAnimationFrame class will be Deprecated,end

--CCTintTo class will be Deprecated,begin
function DeprecatedClass.CCTintTo()
    deprecatedTip("CCTintTo","cc.TintTo")
    return cc.TintTo
end
_G["CCTintTo"] = DeprecatedClass.CCTintTo()
--CCTintTo class will be Deprecated,end

--CCTiledGrid3DAction class will be Deprecated,begin
function DeprecatedClass.CCTiledGrid3DAction()
    deprecatedTip("CCTiledGrid3DAction","cc.TiledGrid3DAction")
    return cc.TiledGrid3DAction
end
_G["CCTiledGrid3DAction"] = DeprecatedClass.CCTiledGrid3DAction()
--CCTiledGrid3DAction class will be Deprecated,end

--CCTMXTilesetInfo class will be Deprecated,begin
function DeprecatedClass.CCTMXTilesetInfo()
    deprecatedTip("CCTMXTilesetInfo","cc.TMXTilesetInfo")
    return cc.TMXTilesetInfo
end
_G["CCTMXTilesetInfo"] = DeprecatedClass.CCTMXTilesetInfo()
--CCTMXTilesetInfo class will be Deprecated,end

--CCTMXObjectGroup class will be Deprecated,begin
function DeprecatedClass.CCTMXObjectGroup()
    deprecatedTip("CCTMXObjectGroup","cc.TMXObjectGroup")
    return cc.TMXObjectGroup
end
_G["CCTMXObjectGroup"] = DeprecatedClass.CCTMXObjectGroup()
--CCTMXObjectGroup class will be Deprecated,end

--CCParticleGalaxy class will be Deprecated,begin
function DeprecatedClass.CCParticleGalaxy()
    deprecatedTip("CCParticleGalaxy","cc.ParticleGalaxy")
    return cc.ParticleGalaxy
end
_G["CCParticleGalaxy"] = DeprecatedClass.CCParticleGalaxy()
--CCParticleGalaxy class will be Deprecated,end

--CCTwirl class will be Deprecated,begin
function DeprecatedClass.CCTwirl()
    deprecatedTip("CCTwirl","cc.Twirl")
    return cc.Twirl
end
_G["CCTwirl"] = DeprecatedClass.CCTwirl()
--CCTwirl class will be Deprecated,end

--CCMenuItemLabel class will be Deprecated,begin
function DeprecatedClass.CCMenuItemLabel()
    deprecatedTip("CCMenuItemLabel","cc.MenuItemLabel")
    return cc.MenuItemLabel
end
_G["CCMenuItemLabel"] = DeprecatedClass.CCMenuItemLabel()
--CCMenuItemLabel class will be Deprecated,end

--CCLayerColor class will be Deprecated,begin
function DeprecatedClass.CCLayerColor()
    deprecatedTip("CCLayerColor","cc.LayerColor")
    return cc.LayerColor
end
_G["CCLayerColor"] = DeprecatedClass.CCLayerColor()
--CCLayerColor class will be Deprecated,end

--CCFadeOutBLTiles class will be Deprecated,begin
function DeprecatedClass.CCFadeOutBLTiles()
    deprecatedTip("CCFadeOutBLTiles","cc.FadeOutBLTiles")
    return cc.FadeOutBLTiles
end
_G["CCFadeOutBLTiles"] = DeprecatedClass.CCFadeOutBLTiles()
--CCFadeOutBLTiles class will be Deprecated,end

--CCTransitionProgress class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgress()
    deprecatedTip("CCTransitionProgress","cc.TransitionProgress")
    return cc.TransitionProgress
end
_G["CCTransitionProgress"] = DeprecatedClass.CCTransitionProgress()
--CCTransitionProgress class will be Deprecated,end

--CCEaseRateAction class will be Deprecated,begin
function DeprecatedClass.CCEaseRateAction()
    deprecatedTip("CCEaseRateAction","cc.EaseRateAction")
    return cc.EaseRateAction
end
_G["CCEaseRateAction"] = DeprecatedClass.CCEaseRateAction()
--CCEaseRateAction class will be Deprecated,end

--CCLayerGradient class will be Deprecated,begin
function DeprecatedClass.CCLayerGradient()
    deprecatedTip("CCLayerGradient","cc.LayerGradient")
    return cc.LayerGradient
end
_G["CCLayerGradient"] = DeprecatedClass.CCLayerGradient()
--CCLayerGradient class will be Deprecated,end

--CCMenuItemSprite class will be Deprecated,begin
function DeprecatedClass.CCMenuItemSprite()
    deprecatedTip("CCMenuItemSprite","cc.MenuItemSprite")
    return cc.MenuItemSprite
end
_G["CCMenuItemSprite"] = DeprecatedClass.CCMenuItemSprite()
--CCMenuItemSprite class will be Deprecated,end

--CCNode class will be Deprecated,begin
function DeprecatedClass.CCNode()
    deprecatedTip("CCNode","cc.Node")
    return cc.Node
end
_G["CCNode"] = DeprecatedClass.CCNode()
--CCNode class will be Deprecated,end

--CCToggleVisibility class will be Deprecated,begin
function DeprecatedClass.CCToggleVisibility()
    deprecatedTip("CCToggleVisibility","cc.ToggleVisibility")
    return cc.ToggleVisibility
end
_G["CCToggleVisibility"] = DeprecatedClass.CCToggleVisibility()
--CCToggleVisibility class will be Deprecated,end

--CCRepeat class will be Deprecated,begin
function DeprecatedClass.CCRepeat()
    deprecatedTip("CCRepeat","cc.Repeat")
    return cc.Repeat
end
_G["CCRepeat"] = DeprecatedClass.CCRepeat()
--CCRepeat class will be Deprecated,end

--CCRenderTexture class will be Deprecated,begin
function DeprecatedClass.CCRenderTexture()
    deprecatedTip("CCRenderTexture","cc.RenderTexture")
    return cc.RenderTexture
end
_G["CCRenderTexture"] = DeprecatedClass.CCRenderTexture()
--CCRenderTexture class will be Deprecated,end

--CCTransitionFlipY class will be Deprecated,begin
function DeprecatedClass.CCTransitionFlipY()
    deprecatedTip("CCTransitionFlipY","cc.TransitionFlipY")
    return cc.TransitionFlipY
end
_G["CCTransitionFlipY"] = DeprecatedClass.CCTransitionFlipY()
--CCTransitionFlipY class will be Deprecated,end

--CCLayerMultiplex class will be Deprecated,begin
function DeprecatedClass.CCLayerMultiplex()
    deprecatedTip("CCLayerMultiplex","cc.LayerMultiplex")
    return cc.LayerMultiplex
end
_G["CCLayerMultiplex"] = DeprecatedClass.CCLayerMultiplex()
--CCLayerMultiplex class will be Deprecated,end

--CCTMXLayerInfo class will be Deprecated,begin
function DeprecatedClass.CCTMXLayerInfo()
    deprecatedTip("CCTMXLayerInfo","cc.TMXLayerInfo")
    return cc.TMXLayerInfo
end
_G["CCTMXLayerInfo"] = DeprecatedClass.CCTMXLayerInfo()
--CCTMXLayerInfo class will be Deprecated,end

--CCEaseBackInOut class will be Deprecated,begin
function DeprecatedClass.CCEaseBackInOut()
    deprecatedTip("CCEaseBackInOut","cc.EaseBackInOut")
    return cc.EaseBackInOut
end
_G["CCEaseBackInOut"] = DeprecatedClass.CCEaseBackInOut()
--CCEaseBackInOut class will be Deprecated,end

--CCActionInstant class will be Deprecated,begin
function DeprecatedClass.CCActionInstant()
    deprecatedTip("CCActionInstant","cc.ActionInstant")
    return cc.ActionInstant
end
_G["CCActionInstant"] = DeprecatedClass.CCActionInstant()
--CCActionInstant class will be Deprecated,end

--CCTargetedAction class will be Deprecated,begin
function DeprecatedClass.CCTargetedAction()
    deprecatedTip("CCTargetedAction","cc.TargetedAction")
    return cc.TargetedAction
end
_G["CCTargetedAction"] = DeprecatedClass.CCTargetedAction()
--CCTargetedAction class will be Deprecated,end

--CCDrawNode class will be Deprecated,begin
function DeprecatedClass.CCDrawNode()
    deprecatedTip("CCDrawNode","cc.DrawNode")
    return cc.DrawNode
end
_G["CCDrawNode"] = DeprecatedClass.CCDrawNode()
--CCDrawNode class will be Deprecated,end

--CCTransitionTurnOffTiles class will be Deprecated,begin
function DeprecatedClass.CCTransitionTurnOffTiles()
    deprecatedTip("CCTransitionTurnOffTiles","cc.TransitionTurnOffTiles")
    return cc.TransitionTurnOffTiles
end
_G["CCTransitionTurnOffTiles"] = DeprecatedClass.CCTransitionTurnOffTiles()
--CCTransitionTurnOffTiles class will be Deprecated,end

--CCRotateTo class will be Deprecated,begin
function DeprecatedClass.CCRotateTo()
    deprecatedTip("CCRotateTo","cc.RotateTo")
    return cc.RotateTo
end
_G["CCRotateTo"] = DeprecatedClass.CCRotateTo()
--CCRotateTo class will be Deprecated,end

--CCTransitionSplitRows class will be Deprecated,begin
function DeprecatedClass.CCTransitionSplitRows()
    deprecatedTip("CCTransitionSplitRows","cc.TransitionSplitRows")
    return cc.TransitionSplitRows
end
_G["CCTransitionSplitRows"] = DeprecatedClass.CCTransitionSplitRows()
--CCTransitionSplitRows class will be Deprecated,end

--CCTransitionProgressRadialCCW class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgressRadialCCW()
    deprecatedTip("CCTransitionProgressRadialCCW","cc.TransitionProgressRadialCCW")
    return cc.TransitionProgressRadialCCW
end
_G["CCTransitionProgressRadialCCW"] = DeprecatedClass.CCTransitionProgressRadialCCW()
--CCTransitionProgressRadialCCW class will be Deprecated,end

--CCScaleTo class will be Deprecated,begin
function DeprecatedClass.CCScaleTo()
    deprecatedTip("CCScaleTo","cc.ScaleTo")
    return cc.ScaleTo
end
_G["CCScaleTo"] = DeprecatedClass.CCScaleTo()
--CCScaleTo class will be Deprecated,end

--CCTransitionPageTurn class will be Deprecated,begin
function DeprecatedClass.CCTransitionPageTurn()
    deprecatedTip("CCTransitionPageTurn","cc.TransitionPageTurn")
    return cc.TransitionPageTurn
end
_G["CCTransitionPageTurn"] = DeprecatedClass.CCTransitionPageTurn()
--CCTransitionPageTurn class will be Deprecated,end

--CCParticleExplosion class will be Deprecated,begin
function DeprecatedClass.CCParticleExplosion()
    deprecatedTip("CCParticleExplosion","cc.ParticleExplosion")
    return cc.ParticleExplosion
end
_G["CCParticleExplosion"] = DeprecatedClass.CCParticleExplosion()
--CCParticleExplosion class will be Deprecated,end

--CCMenu class will be Deprecated,begin
function DeprecatedClass.CCMenu()
    deprecatedTip("CCMenu","cc.Menu")
    return cc.Menu
end
_G["CCMenu"] = DeprecatedClass.CCMenu()
--CCMenu class will be Deprecated,end

--CCTexture2D class will be Deprecated,begin
function DeprecatedClass.CCTexture2D()
    deprecatedTip("CCTexture2D","cc.Texture2D")
    return cc.Texture2D
end
_G["CCTexture2D"] = DeprecatedClass.CCTexture2D()
--CCTexture2D class will be Deprecated,end

--CCActionManager class will be Deprecated,begin
function DeprecatedClass.CCActionManager()
    deprecatedTip("CCActionManager","cc.ActionManager")
    return cc.ActionManager
end
_G["CCActionManager"] = DeprecatedClass.CCActionManager()
--CCActionManager class will be Deprecated,end

--CCParticleBatchNode class will be Deprecated,begin
function DeprecatedClass.CCParticleBatchNode()
    deprecatedTip("CCParticleBatchNode","cc.ParticleBatchNode")
    return cc.ParticleBatchNode
end
_G["CCParticleBatchNode"] = DeprecatedClass.CCParticleBatchNode()
--CCParticleBatchNode class will be Deprecated,end

--CCTransitionZoomFlipX class will be Deprecated,begin
function DeprecatedClass.CCTransitionZoomFlipX()
    deprecatedTip("CCTransitionZoomFlipX","cc.TransitionZoomFlipX")
    return cc.TransitionZoomFlipX
end
_G["CCTransitionZoomFlipX"] = DeprecatedClass.CCTransitionZoomFlipX()
--CCTransitionZoomFlipX class will be Deprecated,end

--CCScaleBy class will be Deprecated,begin
function DeprecatedClass.CCScaleBy()
    deprecatedTip("CCScaleBy","cc.ScaleBy")
    return cc.ScaleBy
end
_G["CCScaleBy"] = DeprecatedClass.CCScaleBy()
--CCScaleBy class will be Deprecated,end

--CCTileMapAtlas class will be Deprecated,begin
function DeprecatedClass.CCTileMapAtlas()
    deprecatedTip("CCTileMapAtlas","cc.TileMapAtlas")
    return cc.TileMapAtlas
end
_G["CCTileMapAtlas"] = DeprecatedClass.CCTileMapAtlas()
--CCTileMapAtlas class will be Deprecated,end

--CCAction class will be Deprecated,begin
function DeprecatedClass.CCAction()
    deprecatedTip("CCAction","cc.Action")
    return cc.Action
end
_G["CCAction"] = DeprecatedClass.CCAction()
--CCAction class will be Deprecated,end

--CCLens3D class will be Deprecated,begin
function DeprecatedClass.CCLens3D()
    deprecatedTip("CCLens3D","cc.Lens3D")
    return cc.Lens3D
end
_G["CCLens3D"] = DeprecatedClass.CCLens3D()
--CCLens3D class will be Deprecated,end

--CCAnimation class will be Deprecated,begin
function DeprecatedClass.CCAnimation()
    deprecatedTip("CCAnimation","cc.Animation")
    return cc.Animation
end
_G["CCAnimation"] = DeprecatedClass.CCAnimation()
--CCAnimation class will be Deprecated,end

--CCTransitionSlideInT class will be Deprecated,begin
function DeprecatedClass.CCTransitionSlideInT()
    deprecatedTip("CCTransitionSlideInT","cc.TransitionSlideInT")
    return cc.TransitionSlideInT
end
_G["CCTransitionSlideInT"] = DeprecatedClass.CCTransitionSlideInT()
--CCTransitionSlideInT class will be Deprecated,end

--CCSpawn class will be Deprecated,begin
function DeprecatedClass.CCSpawn()
    deprecatedTip("CCSpawn","cc.Spawn")
    return cc.Spawn
end
_G["CCSpawn"] = DeprecatedClass.CCSpawn()
--CCSpawn class will be Deprecated,end

--CCSet class will be Deprecated,begin
function DeprecatedClass.CCSet()
    deprecatedTip("CCSet","cc.Set")
    return cc.Set
end
_G["CCSet"] = DeprecatedClass.CCSet()
--CCSet class will be Deprecated,end

--CCShakyTiles3D class will be Deprecated,begin
function DeprecatedClass.CCShakyTiles3D()
    deprecatedTip("CCShakyTiles3D","cc.ShakyTiles3D")
    return cc.ShakyTiles3D
end
_G["CCShakyTiles3D"] = DeprecatedClass.CCShakyTiles3D()
--CCShakyTiles3D class will be Deprecated,end

--CCPageTurn3D class will be Deprecated,begin
function DeprecatedClass.CCPageTurn3D()
    deprecatedTip("CCPageTurn3D","cc.PageTurn3D")
    return cc.PageTurn3D
end
_G["CCPageTurn3D"] = DeprecatedClass.CCPageTurn3D()
--CCPageTurn3D class will be Deprecated,end

--CCGrid3D class will be Deprecated,begin
function DeprecatedClass.CCGrid3D()
    deprecatedTip("CCGrid3D","cc.Grid3D")
    return cc.Grid3D
end
_G["CCGrid3D"] = DeprecatedClass.CCGrid3D()
--CCGrid3D class will be Deprecated,end

--CCTransitionProgressInOut class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgressInOut()
    deprecatedTip("CCTransitionProgressInOut","cc.TransitionProgressInOut")
    return cc.TransitionProgressInOut
end
_G["CCTransitionProgressInOut"] = DeprecatedClass.CCTransitionProgressInOut()
--CCTransitionProgressInOut class will be Deprecated,end

--CCTransitionFadeBL class will be Deprecated,begin
function DeprecatedClass.CCTransitionFadeBL()
    deprecatedTip("CCTransitionFadeBL","cc.TransitionFadeBL")
    return cc.TransitionFadeBL
end
_G["CCTransitionFadeBL"] = DeprecatedClass.CCTransitionFadeBL()
--CCTransitionFadeBL class will be Deprecated,end

--CCCamera class will be Deprecated,begin
function DeprecatedClass.CCCamera()
    deprecatedTip("CCCamera","cc.Camera")
    return cc.Camera
end
_G["CCCamera"] = DeprecatedClass.CCCamera()
--CCCamera class will be Deprecated,end

--CCLayerRGBA class will be Deprecated,begin
function DeprecatedClass.CCLayerRGBA()
    deprecatedTip("CCLayerRGBA","cc.Layer")
    return cc.Layer
end
_G["CCLayerRGBA"] = DeprecatedClass.CCLayerRGBA()
--CCLayerRGBA class will be Deprecated,end

--LayerRGBA class will be Deprecated,begin
function DeprecatedClass.LayerRGBA()
    deprecatedTip("cc.LayerRGBA","cc.Layer")
    return cc.Layer
end
_G["cc"]["LayerRGBA"] = DeprecatedClass.LayerRGBA()
--LayerRGBA class will be Deprecated,end

--CCBezierTo class will be Deprecated,begin
function DeprecatedClass.CCBezierTo()
    deprecatedTip("CCBezierTo","cc.BezierTo")
    return cc.BezierTo
end
_G["CCBezierTo"] = DeprecatedClass.CCBezierTo()
--CCBezierTo class will be Deprecated,end

--CCFollow class will be Deprecated,begin
function DeprecatedClass.CCFollow()
    deprecatedTip("CCFollow","cc.Follow")
    return cc.Follow
end
_G["CCFollow"] = DeprecatedClass.CCFollow()
--CCFollow class will be Deprecated,end

--CCTintBy class will be Deprecated,begin
function DeprecatedClass.CCTintBy()
    deprecatedTip("CCTintBy","cc.TintBy")
    return cc.TintBy
end
_G["CCTintBy"] = DeprecatedClass.CCTintBy()
--CCTintBy class will be Deprecated,end

--CCActionInterval class will be Deprecated,begin
function DeprecatedClass.CCActionInterval()
    deprecatedTip("CCActionInterval","cc.ActionInterval")
    return cc.ActionInterval
end
_G["CCActionInterval"] = DeprecatedClass.CCActionInterval()
--CCActionInterval class will be Deprecated,end

--CCAnimate class will be Deprecated,begin
function DeprecatedClass.CCAnimate()
    deprecatedTip("CCAnimate","cc.Animate")
    return cc.Animate
end
_G["CCAnimate"] = DeprecatedClass.CCAnimate()
--CCAnimate class will be Deprecated,end

--CCProgressTimer class will be Deprecated,begin
function DeprecatedClass.CCProgressTimer()
    deprecatedTip("CCProgressTimer","cc.ProgressTimer")
    return cc.ProgressTimer
end
_G["CCProgressTimer"] = DeprecatedClass.CCProgressTimer()
--CCProgressTimer class will be Deprecated,end

--CCParticleMeteor class will be Deprecated,begin
function DeprecatedClass.CCParticleMeteor()
    deprecatedTip("CCParticleMeteor","cc.ParticleMeteor")
    return cc.ParticleMeteor
end
_G["CCParticleMeteor"] = DeprecatedClass.CCParticleMeteor()
--CCParticleMeteor class will be Deprecated,end

--CCTransitionFadeTR class will be Deprecated,begin
function DeprecatedClass.CCTransitionFadeTR()
    deprecatedTip("CCTransitionFadeTR","cc.TransitionFadeTR")
    return cc.TransitionFadeTR
end
_G["CCTransitionFadeTR"] = DeprecatedClass.CCTransitionFadeTR()
--CCTransitionFadeTR class will be Deprecated,end

--CCCatmullRomTo class will be Deprecated,begin
function DeprecatedClass.CCCatmullRomTo()
    deprecatedTip("CCCatmullRomTo","cc.CatmullRomTo")
    return cc.CatmullRomTo
end
_G["CCCatmullRomTo"] = DeprecatedClass.CCCatmullRomTo()
--CCCatmullRomTo class will be Deprecated,end

--CCTransitionZoomFlipY class will be Deprecated,begin
function DeprecatedClass.CCTransitionZoomFlipY()
    deprecatedTip("CCTransitionZoomFlipY","cc.TransitionZoomFlipY")
    return cc.TransitionZoomFlipY
end
_G["CCTransitionZoomFlipY"] = DeprecatedClass.CCTransitionZoomFlipY()
--CCTransitionZoomFlipY class will be Deprecated,end

--CCTransitionCrossFade class will be Deprecated,begin
function DeprecatedClass.CCTransitionCrossFade()
    deprecatedTip("CCTransitionCrossFade","cc.TransitionCrossFade")
    return cc.TransitionCrossFade
end
_G["CCTransitionCrossFade"] = DeprecatedClass.CCTransitionCrossFade()
--CCTransitionCrossFade class will be Deprecated,end

--CCGridBase class will be Deprecated,begin
function DeprecatedClass.CCGridBase()
    deprecatedTip("CCGridBase","cc.GridBase")
    return cc.GridBase
end
_G["CCGridBase"] = DeprecatedClass.CCGridBase()
--CCGridBase class will be Deprecated,end

--CCSkewTo class will be Deprecated,begin
function DeprecatedClass.CCSkewTo()
    deprecatedTip("CCSkewTo","cc.SkewTo")
    return cc.SkewTo
end
_G["CCSkewTo"] = DeprecatedClass.CCSkewTo()
--CCSkewTo class will be Deprecated,end

--CCCardinalSplineTo class will be Deprecated,begin
function DeprecatedClass.CCCardinalSplineTo()
    deprecatedTip("CCCardinalSplineTo","cc.CardinalSplineTo")
    return cc.CardinalSplineTo
end
_G["CCCardinalSplineTo"] = DeprecatedClass.CCCardinalSplineTo()
--CCCardinalSplineTo class will be Deprecated,end

--CCTMXMapInfo class will be Deprecated,begin
function DeprecatedClass.CCTMXMapInfo()
    deprecatedTip("CCTMXMapInfo","cc.TMXMapInfo")
    return cc.TMXMapInfo
end
_G["CCTMXMapInfo"] = DeprecatedClass.CCTMXMapInfo()
--CCTMXMapInfo class will be Deprecated,end

--CCEaseExponentialIn class will be Deprecated,begin
function DeprecatedClass.CCEaseExponentialIn()
    deprecatedTip("CCEaseExponentialIn","cc.EaseExponentialIn")
    return cc.EaseExponentialIn
end
_G["CCEaseExponentialIn"] = DeprecatedClass.CCEaseExponentialIn()
--CCEaseExponentialIn class will be Deprecated,end

--CCReuseGrid class will be Deprecated,begin
function DeprecatedClass.CCReuseGrid()
    deprecatedTip("CCReuseGrid","cc.ReuseGrid")
    return cc.ReuseGrid
end
_G["CCReuseGrid"] = DeprecatedClass.CCReuseGrid()
--CCReuseGrid class will be Deprecated,end

--CCMenuItemAtlasFont class will be Deprecated,begin
function DeprecatedClass.CCMenuItemAtlasFont()
    deprecatedTip("CCMenuItemAtlasFont","cc.MenuItemAtlasFont")
    return cc.MenuItemAtlasFont
end
_G["CCMenuItemAtlasFont"] = DeprecatedClass.CCMenuItemAtlasFont()
--CCMenuItemAtlasFont class will be Deprecated,end

--CCSpriteFrame class will be Deprecated,begin
function DeprecatedClass.CCSpriteFrame()
    deprecatedTip("CCSpriteFrame","cc.SpriteFrame")
    return cc.SpriteFrame
end
_G["CCSpriteFrame"] = DeprecatedClass.CCSpriteFrame()
--CCSpriteFrame class will be Deprecated,end

--CCSplitRows class will be Deprecated,begin
function DeprecatedClass.CCSplitRows()
    deprecatedTip("CCSplitRows","cc.SplitRows")
    return cc.SplitRows
end
_G["CCSplitRows"] = DeprecatedClass.CCSplitRows()
--CCSplitRows class will be Deprecated,end

--CCSprite class will be Deprecated,begin
function DeprecatedClass.CCSprite()
    deprecatedTip("CCSprite","cc.Sprite")
    return cc.Sprite
end
_G["CCSprite"] = DeprecatedClass.CCSprite()
--CCSprite class will be Deprecated,end

--CCOrbitCamera class will be Deprecated,begin
function DeprecatedClass.CCOrbitCamera()
    deprecatedTip("CCOrbitCamera","cc.OrbitCamera")
    return cc.OrbitCamera
end
_G["CCOrbitCamera"] = DeprecatedClass.CCOrbitCamera()
--CCOrbitCamera class will be Deprecated,end

--CCUserDefault class will be Deprecated,begin
function DeprecatedClass.CCUserDefault()
    deprecatedTip("CCUserDefault","cc.UserDefault")
    return cc.UserDefault
end
_G["CCUserDefault"] = DeprecatedClass.CCUserDefault()
--CCUserDefault class will be Deprecated,end

--CCFadeOutUpTiles class will be Deprecated,begin
function DeprecatedClass.CCFadeOutUpTiles()
    deprecatedTip("CCFadeOutUpTiles","cc.FadeOutUpTiles")
    return cc.FadeOutUpTiles
end
_G["CCFadeOutUpTiles"] = DeprecatedClass.CCFadeOutUpTiles()
--CCFadeOutUpTiles class will be Deprecated,end

--CCParticleRain class will be Deprecated,begin
function DeprecatedClass.CCParticleRain()
    deprecatedTip("CCParticleRain","cc.ParticleRain")
    return cc.ParticleRain
end
_G["CCParticleRain"] = DeprecatedClass.CCParticleRain()
--CCParticleRain class will be Deprecated,end

--CCWaves class will be Deprecated,begin
function DeprecatedClass.CCWaves()
    deprecatedTip("CCWaves","cc.Waves")
    return cc.Waves
end
_G["CCWaves"] = DeprecatedClass.CCWaves()
--CCWaves class will be Deprecated,end

--CCEaseOut class will be Deprecated,begin
function DeprecatedClass.CCEaseOut()
    deprecatedTip("CCEaseOut","cc.EaseOut")
    return cc.EaseOut
end
_G["CCEaseOut"] = DeprecatedClass.CCEaseOut()
--CCEaseOut class will be Deprecated,end

--CCEaseBounceIn class will be Deprecated,begin
function DeprecatedClass.CCEaseBounceIn()
    deprecatedTip("CCEaseBounceIn","cc.EaseBounceIn")
    return cc.EaseBounceIn
end
_G["CCEaseBounceIn"] = DeprecatedClass.CCEaseBounceIn()
--CCEaseBounceIn class will be Deprecated,end

--CCMenuItemFont class will be Deprecated,begin
function DeprecatedClass.CCMenuItemFont()
    deprecatedTip("CCMenuItemFont","cc.MenuItemFont")
    return cc.MenuItemFont
end
_G["CCMenuItemFont"] = DeprecatedClass.CCMenuItemFont()
--CCMenuItemFont class will be Deprecated,end

--CCEaseSineOut class will be Deprecated,begin
function DeprecatedClass.CCEaseSineOut()
    deprecatedTip("CCEaseSineOut","cc.EaseSineOut")
    return cc.EaseSineOut
end
_G["CCEaseSineOut"] = DeprecatedClass.CCEaseSineOut()
--CCEaseSineOut class will be Deprecated,end

--CCTextureCache class will be Deprecated,begin
function DeprecatedClass.CCTextureCache()
    deprecatedTip("CCTextureCache","cc.TextureCache")
    return cc.TextureCache
end
_G["CCTextureCache"] = DeprecatedClass.CCTextureCache()
--CCTextureCache class will be Deprecated,end

--CCTiledGrid3D class will be Deprecated,begin
function DeprecatedClass.CCTiledGrid3D()
    deprecatedTip("CCTiledGrid3D","cc.TiledGrid3D")
    return cc.TiledGrid3D
end
_G["CCTiledGrid3D"] = DeprecatedClass.CCTiledGrid3D()
--CCTiledGrid3D class will be Deprecated,end

--CCRemoveSelf class will be Deprecated,begin
function DeprecatedClass.CCRemoveSelf()
    deprecatedTip("CCRemoveSelf","cc.RemoveSelf")
    return cc.RemoveSelf
end
_G["CCRemoveSelf"] = DeprecatedClass.CCRemoveSelf()
--CCRemoveSelf class will be Deprecated,end

--CCLabelTTF class will be Deprecated,begin
function DeprecatedClass.CCLabelTTF()
    deprecatedTip("CCLabelTTF","cc.LabelTTF")
    return cc.LabelTTF
end
_G["CCLabelTTF"] = DeprecatedClass.CCLabelTTF()
--CCLabelTTF class will be Deprecated,end

--CCTouch class will be Deprecated,begin
function DeprecatedClass.CCTouch()
    deprecatedTip("CCTouch","cc.Touch")
    return cc.Touch
end
_G["CCTouch"] = DeprecatedClass.CCTouch()
--CCTouch class will be Deprecated,end

--CCMoveBy class will be Deprecated,begin
function DeprecatedClass.CCMoveBy()
    deprecatedTip("CCMoveBy","cc.MoveBy")
    return cc.MoveBy
end
_G["CCMoveBy"] = DeprecatedClass.CCMoveBy()
--CCMoveBy class will be Deprecated,end

--CCMotionStreak class will be Deprecated,begin
function DeprecatedClass.CCMotionStreak()
    deprecatedTip("CCMotionStreak","cc.MotionStreak")
    return cc.MotionStreak
end
_G["CCMotionStreak"] = DeprecatedClass.CCMotionStreak()
--CCMotionStreak class will be Deprecated,end

--CCRotateBy class will be Deprecated,begin
function DeprecatedClass.CCRotateBy()
    deprecatedTip("CCRotateBy","cc.RotateBy")
    return cc.RotateBy
end
_G["CCRotateBy"] = DeprecatedClass.CCRotateBy()
--CCRotateBy class will be Deprecated,end

--CCFileUtils class will be Deprecated,begin
function DeprecatedClass.CCFileUtils()
    deprecatedTip("CCFileUtils","cc.FileUtils")
    return cc.FileUtils
end
_G["CCFileUtils"] = DeprecatedClass.CCFileUtils()
--CCFileUtils class will be Deprecated,end

--CCBezierBy class will be Deprecated,begin
function DeprecatedClass.CCBezierBy()
    deprecatedTip("CCBezierBy","cc.BezierBy")
    return cc.BezierBy
end
_G["CCBezierBy"] = DeprecatedClass.CCBezierBy()
--CCBezierBy class will be Deprecated,end

--CCTransitionFade class will be Deprecated,begin
function DeprecatedClass.CCTransitionFade()
    deprecatedTip("CCTransitionFade","cc.TransitionFade")
    return cc.TransitionFade
end
_G["CCTransitionFade"] = DeprecatedClass.CCTransitionFade()
--CCTransitionFade class will be Deprecated,end

--CCTransitionProgressOutIn class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgressOutIn()
    deprecatedTip("CCTransitionProgressOutIn","cc.TransitionProgressOutIn")
    return cc.TransitionProgressOutIn
end
_G["CCTransitionProgressOutIn"] = DeprecatedClass.CCTransitionProgressOutIn()
--CCTransitionProgressOutIn class will be Deprecated,end

--CCCatmullRomBy class will be Deprecated,begin
function DeprecatedClass.CCCatmullRomBy()
    deprecatedTip("CCCatmullRomBy","cc.CatmullRomBy")
    return cc.CatmullRomBy
end
_G["CCCatmullRomBy"] = DeprecatedClass.CCCatmullRomBy()
--CCCatmullRomBy class will be Deprecated,end

--CCGridAction class will be Deprecated,begin
function DeprecatedClass.CCGridAction()
    deprecatedTip("CCGridAction","cc.GridAction")
    return cc.GridAction
end
_G["CCGridAction"] = DeprecatedClass.CCGridAction()
--CCGridAction class will be Deprecated,end

--CCShaky3D class will be Deprecated,begin
function DeprecatedClass.CCShaky3D()
    deprecatedTip("CCShaky3D","cc.Shaky3D")
    return cc.Shaky3D
end
_G["CCShaky3D"] = DeprecatedClass.CCShaky3D()
--CCShaky3D class will be Deprecated,end

--CCTransitionEaseScene class will be Deprecated,begin
function DeprecatedClass.CCTransitionEaseScene()
    deprecatedTip("CCTransitionEaseScene","cc.TransitionEaseScene")
    return cc.TransitionEaseScene
end
_G["CCTransitionEaseScene"] = DeprecatedClass.CCTransitionEaseScene()
--CCTransitionEaseScene class will be Deprecated,end

--CCSequence class will be Deprecated,begin
function DeprecatedClass.CCSequence()
    deprecatedTip("CCSequence","cc.Sequence")
    return cc.Sequence
end
_G["CCSequence"] = DeprecatedClass.CCSequence()
--CCSequence class will be Deprecated,end

--CCTransitionFadeUp class will be Deprecated,begin
function DeprecatedClass.CCTransitionFadeUp()
    deprecatedTip("CCTransitionFadeUp","cc.TransitionFadeUp")
    return cc.TransitionFadeUp
end
_G["CCTransitionFadeUp"] = DeprecatedClass.CCTransitionFadeUp()
--CCTransitionFadeUp class will be Deprecated,end

--CCTransitionProgressRadialCW class will be Deprecated,begin
function DeprecatedClass.CCTransitionProgressRadialCW()
    deprecatedTip("CCTransitionProgressRadialCW","cc.TransitionProgressRadialCW")
    return cc.TransitionProgressRadialCW
end
_G["CCTransitionProgressRadialCW"] = DeprecatedClass.CCTransitionProgressRadialCW()
--CCTransitionProgressRadialCW class will be Deprecated,end

--CCShuffleTiles class will be Deprecated,begin
function DeprecatedClass.CCShuffleTiles()
    deprecatedTip("CCShuffleTiles","cc.ShuffleTiles")
    return cc.ShuffleTiles
end
_G["CCShuffleTiles"] = DeprecatedClass.CCShuffleTiles()
--CCShuffleTiles class will be Deprecated,end

--CCTransitionSlideInR class will be Deprecated,begin
function DeprecatedClass.CCTransitionSlideInR()
    deprecatedTip("CCTransitionSlideInR","cc.TransitionSlideInR")
    return cc.TransitionSlideInR
end
_G["CCTransitionSlideInR"] = DeprecatedClass.CCTransitionSlideInR()
--CCTransitionSlideInR class will be Deprecated,end

--CCScene class will be Deprecated,begin
function DeprecatedClass.CCScene()
    deprecatedTip("CCScene","cc.Scene")
    return cc.Scene
end
_G["CCScene"] = DeprecatedClass.CCScene()
--CCScene class will be Deprecated,end

--CCParallaxNode class will be Deprecated,begin
function DeprecatedClass.CCParallaxNode()
    deprecatedTip("CCParallaxNode","cc.ParallaxNode")
    return cc.ParallaxNode
end
_G["CCParallaxNode"] = DeprecatedClass.CCParallaxNode()
--CCParallaxNode class will be Deprecated,end

--CCTransitionSlideInL class will be Deprecated,begin
function DeprecatedClass.CCTransitionSlideInL()
    deprecatedTip("CCTransitionSlideInL","cc.TransitionSlideInL")
    return cc.TransitionSlideInL
end
_G["CCTransitionSlideInL"] = DeprecatedClass.CCTransitionSlideInL()
--CCTransitionSlideInL class will be Deprecated,end

--CCWavesTiles3D class will be Deprecated,begin
function DeprecatedClass.CCWavesTiles3D()
    deprecatedTip("CCWavesTiles3D","cc.WavesTiles3D")
    return cc.WavesTiles3D
end
_G["CCWavesTiles3D"] = DeprecatedClass.CCWavesTiles3D()
--CCWavesTiles3D class will be Deprecated,end

--CCTransitionSlideInB class will be Deprecated,begin
function DeprecatedClass.CCTransitionSlideInB()
    deprecatedTip("CCTransitionSlideInB","cc.TransitionSlideInB")
    return cc.TransitionSlideInB
end
_G["CCTransitionSlideInB"] = DeprecatedClass.CCTransitionSlideInB()
--CCTransitionSlideInB class will be Deprecated,end

--CCSpeed class will be Deprecated,begin
function DeprecatedClass.CCSpeed()
    deprecatedTip("CCSpeed","cc.Speed")
    return cc.Speed
end
_G["CCSpeed"] = DeprecatedClass.CCSpeed()
--CCSpeed class will be Deprecated,end

--CCShatteredTiles3D class will be Deprecated,begin
function DeprecatedClass.CCShatteredTiles3D()
    deprecatedTip("CCShatteredTiles3D","cc.ShatteredTiles3D")
    return cc.ShatteredTiles3D
end
_G["CCShatteredTiles3D"] = DeprecatedClass.CCShatteredTiles3D()
--CCShatteredTiles3D class will be Deprecated,end

--CCCallFuncN class will be Deprecated,begin
function DeprecatedClass.CCCallFuncN()
    deprecatedTip("CCCallFuncN","cc.CallFunc")
    return cc.CallFunc
end
_G["CCCallFuncN"] = DeprecatedClass.CCCallFuncN()
--CCCallFuncN class will be Deprecated,end

--CCEGLViewProtocol class will be Deprecated,begin
function DeprecatedClass.CCEGLViewProtocol()
    deprecatedTip("CCEGLViewProtocol","cc.GLViewProtocol")
    return cc.GLViewProtocol
end
_G["CCEGLViewProtocol"] = DeprecatedClass.CCEGLViewProtocol()
--CCEGLViewProtocol class will be Deprecated,end

--CCEGLView class will be Deprecated,begin
function DeprecatedClass.CCEGLView()
    deprecatedTip("CCEGLView","cc.GLView")
    return cc.GLView
end

_G["CCEGLView"] = DeprecatedClass.CCEGLView()
--CCEGLView class will be Deprecated,end

--XMLHttpRequest class will be Deprecated,begin
function DeprecatedClass.XMLHttpRequest()
    deprecatedTip("XMLHttpRequest","cc.XMLHttpRequest")
    return cc.XMLHttpRequest
end
_G["XMLHttpRequest"] = DeprecatedClass.XMLHttpRequest()
--XMLHttpRequest class will be Deprecated,end

--EGLViewProtocol class will be Deprecated,begin
function DeprecatedClass.EGLViewProtocol()
    deprecatedTip("cc.EGLViewProtocol","cc.GLViewProtocol")
    return cc.GLViewProtocol
end
_G["cc.EGLViewProtocol"] = DeprecatedClass.EGLViewProtocol()
--EGLViewProtocol class will be Deprecated,end

--EGLView class will be Deprecated,begin
function DeprecatedClass.EGLView()
    deprecatedTip("cc.EGLView","cc.GLView")
    return cc.GLView
end
_G["cc.EGLView"] = DeprecatedClass.EGLView()
--EGLView  class will be Deprecated,end

--EGLView class will be Deprecated,begin
function DeprecatedClass.EGLView()
    deprecatedTip("cc.EGLView","cc.GLView")
    print(cc.GLView)
    return cc.GLView
end
_G["cc.EGLView"] = DeprecatedClass.EGLView()
--EGLView  class will be Deprecated,end

--ShaderCache class will be Deprecated,begin
function DeprecatedClass.ShaderCache()
    deprecatedTip("cc.ShaderCache","cc.GLProgramCache")
    return cc.GLProgramCache
end
cc.ShaderCache = DeprecatedClass.ShaderCache()
--ShaderCache  class will be Deprecated,end




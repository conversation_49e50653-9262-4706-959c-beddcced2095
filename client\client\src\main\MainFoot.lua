-------------------------------------------------------------------------------
--  创世版1.0
--  大厅主界面 之 底部处理
--  @date 2018-05-07
--  @auth woodoo
-------------------------------------------------------------------------------
local MainScene = cs.app.client('main.MainScene')


-------------------------------------------------------------------------------
-- 初始化
-------------------------------------------------------------------------------
function MainScene:initFoot()
    local foot_node = self.main_node:child('foot_node')
    local panel_more = foot_node:child('panel_more'):hide()
    foot_node:zorder(3)
    if not cs.app.FANGKA_DIAMOND and foot_node:child('btn_add_fangka') then
        foot_node:child('btn_add_fangka'):hide()
    end
    helper.app.checkFangkDiamond(foot_node:child('icon_fangka'))
    helper.logic.addListenerByName(self, {foot_node:child('btn_add_fangka,btn_mall,btn_message,btn_score,btn_more,btn_change_district,btn_add_gold')})

    local onBtnRule = function(owner, event) owner:hidePanelMore(); owner:onBtnRule(event) end
    local onBtnSet = function(owner, event) owner:hidePanelMore(); owner:onBtnSet(event) end
    panel_more:child('btn_set'):addTouchEventListener( helper.app.tintClickHandler(self, onBtnSet) )
    panel_more:child('btn_rule'):addTouchEventListener( helper.app.tintClickHandler(self, onBtnRule) )
    panel_more:child('btn_bag'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnBag) )

    if yl.is_reviewing then panel_more:child('btn_bag'):hide() end

    -- 房卡版且没有配置地区选择，更多面板上的按钮直接显示
    if not cs.app.IS_GOLD_HALL and cs.app.NO_DISTRICT then
        foot_node:child('btn_change_district'):hide()
        -- 更多面板上的按钮直接显示
        local btn_more = foot_node:child('btn_more'):hide()
        for i, btn in ipairs{panel_more:child('btn_set, btn_bag, btn_rule')} do
            btn:retain()
            btn:removeFromParent()
            btn:addTo(foot_node)
            btn:release()
        end
        -- 所有按钮调整位置
        local x, y = 450, btn_more:py()
        for i, btn in ipairs{foot_node:child('btn_set, btn_bag, btn_rule, btn_score, btn_message, btn_mall')} do
            if btn:isVisible() then
                btn:pos(x, y)
                x = x - 98
            end
        end
    else
        self:setDistrictName()
    end
end


-------------------------------------------------------------------------------
-- 更多按钮点击
-------------------------------------------------------------------------------
function MainScene:hidePanelMore()
    local panel_more = self.main_node:child('foot_node/panel_more')
    panel_more:stop():runAction( cc.Sequence:create(
        cc.EaseBackIn:create( cc.ScaleTo:create(0.15, 0) ),
        cc.Hide:create()
    ) )
end


-------------------------------------------------------------------------------
-- 更多按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnMore()
    local panel_more = self.main_node:child('foot_node/panel_more')
    if not panel_more:isVisible() then
        panel_more:stop():scale(0):show():runAction(
            cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
        )
    else
        self:hidePanelMore()
    end
end


-------------------------------------------------------------------------------
-- 消息按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnMessage()
    self:hidePanelMore()
    helper.link.toMessage()
end


-------------------------------------------------------------------------------
-- 背包按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnBag()
    helper.link.toBag()
end


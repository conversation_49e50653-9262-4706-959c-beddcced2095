-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 提现规则
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbRuleDraw = class('OrbRuleDraw', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbRuleDraw:ctor(panel)
    print('OrbRuleDraw:ctor...')
    self.super.ctor(self, panel)

    panel:child('label_last'):setString( LANG{'ORB_RULE_DRAW', app_name = cs.app.APP_NAME} )
end


return OrbRuleDraw
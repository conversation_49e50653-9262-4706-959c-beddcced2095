-------------------------------------------------------------------------------
--  创世版1.0
--  金币场网络
--  @date 2017-10-30
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")
local cmd_private = cs.app.client('header.CMD_Private')


GoldRoom = GoldRoom or class("GoldRoom")


-------------------------------------------------------------------------------
-- 单例
-------------------------------------------------------------------------------
function GoldRoom:getInstance()
    if not GoldRoom._instance then
        GoldRoom._instance = GoldRoom:create()
    end
    return GoldRoom._instance
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GoldRoom:ctor()
end


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GoldRoom:onLoginRoomFinish()
    local meUser = PassRoom:getInstance():getMeUserItem()
    if nil == meUser then
        return false
    end

    if meUser.cbUserStatus == yl.US_FREE or meUser.cbUserStatus == yl.US_NULL then
        if cs.app.room_frame:isSocketServer() then
            local buffer = CCmd_Data:create(8)--ExternalFun.create_netdata(8)
            buffer:setcmdinfo(cmd_private.game.MDM_GR_PERSONAL_TABLE, cmd_private.game.SUB_GR_JOIN_GOLD_TABLE)
            if not cs.app.room_frame:sendSocketData(buffer) then
                helper.pop.message( LANG{'SEND_ERROR', main=cmd_private.game.MDM_GR_PERSONAL_TABLE, sub=cmd_private.game.SUB_GR_JOIN_GOLD_TABLE} )
            else
                return true
            end
        end    
    end

    return false
end

-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部成员列表
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')

-- 邀请玩家时间缓存
local invite_time_map = {}
-- 同一人邀请间隔，秒
local INVITE_INTERNAL = 10

local ClubRoomInviteLayer = class("ClubRoomInviteLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:ctor(club_id, rule_id, owner_id)
    print('ClubRoomInviteLayer:ctor...')
    self.m_club_id = club_id
    self.m_rule_id = rule_id
    self.m_owner_id = owner_id
    self.m_member_nodes = {}

    local main_node = ClubUtil.initUI(self, 'ClubRoomInviteLayer.csb')
    main_node:child('friend_template'):hide()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:onEnter()
    print('ClubRoomInviteLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_MEMBERS, self, self.onMemberListResp)
    ClubUtil.listen(cmd.SUB_CLUB_TABLE_INVITE, self, self.onInviteResp)

    LiveFrame:getInstance():connect()   -- 房间内默认是关闭LiveFrame的

    local bg = self.main_node:child('bg')
    local x, y = bg:pos()
    bg:px(-300):runAction( cc.Sequence:create(
        cc.EaseBackOut:create(cc.MoveTo:create(0.3, cc.p(x, y))),
        cc.CallFunc:create(function()
            if tolua.isnull(self) then return end
            self:perform(function()
                local listview = self.main_node:child('bg/listview')
                self.m_members = {}
                self:sendListCmd(0)
            end, 6, -1, nil, true)
        end)
    ) )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:onExit()
    print('ClubRoomInviteLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
    LiveFrame:getInstance():disconnect()
end


-------------------------------------------------------------------------------
-- 发送列表查询
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:sendListCmd(pos)
    local values = {dwID=self.m_club_id, nValue=pos, nValue2=2}
    ClubUtil.send(cmd.SUB_CLUB_MEMBERS, cmd.CMD_GR_IDValue, values)
end


-------------------------------------------------------------------------------
-- 成员列表生成
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:onMemberListResp(data)
    local members = LiveFrame:getInstance():resp(data, cmd.tagClubMember, true)
    if not members then return end

    local is_end = #members < ClubUtil.PER_PAGE -- 本页不足10条，认为没有下一页了
    local listview = self.main_node:child('bg/listview')
    if #members > 0 then
        local template = self.main_node:child('friend_template')
        for i, member in ipairs(members) do
            if member.dwUserID ~= GlobalUserItem.dwUserID then
                self.m_members[member.dwUserID] = member
                local item = self.m_member_nodes[member.dwUserID]
                if not item then
                    item = template:clone():show()
                    self.m_member_nodes[member.dwUserID] = item
                    ClubUtil.createPlayerHead(item:child('panel_head'), member.dwUserID, member.szHeadHttp)
                    item:child('label_name'):setString(member.szName)
                    item:child('btn_invite'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnInvite) )
                    listview:pushBackCustomItem(item)
                end
                item:child('img_status'):texture( ClubUtil.MEMBER_STATUS[member.cbStatus] )
                item:child('label_status'):setString( LANG['CLUB_MEMBER_STATUS' .. member.cbStatus] )
                item:child('btn_invite').member = member
            end
        end
    end

    -- 是否需要继续请求
    if not is_end then
        self:sendListCmd(members[#members].wIndex)
    else
        -- 旧节点删除
        for id, item in pairs(self.m_member_nodes) do
            if not self.m_members[id] then
                self.m_member_nodes[id] = nil
                listview:removeItem( listview:getIndex(item) )
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 邀请按钮点击
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:onBtnInvite(sender)
    local member = sender.member
    if invite_time_map[member.dwUserID] and os.time() - invite_time_map[member.dwUserID] <= INVITE_INTERNAL then
        helper.pop.message( LANG.CLUB_INVITE_WARNING )
        return
    end

    invite_time_map[member.dwUserID] = os.time()

    local values = {
        dwClubID    = self.m_club_id,
        dwRuleID    = self.m_rule_id,
        dwOwnerID   = self.m_owner_id,
        dwUserID    = member.dwUserID,
    }
    ClubUtil.send(cmd.SUB_CLUB_TABLE_INVITE, cmd.tagClubRuleTableInvite, values)
end


-------------------------------------------------------------------------------
-- 邀请返回
-------------------------------------------------------------------------------
function ClubRoomInviteLayer:onInviteResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDValueMsg, function(ret)
        helper.pop.message( LANG.CLUB_INVITE_SUCC )
    end)
end


return ClubRoomInviteLayer

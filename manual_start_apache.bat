@echo off
echo ===== 手动启动 Apache 并查看错误 =====
echo.

echo 1. 尝试手动启动 Apache...
echo 请注意观察错误信息！
echo.

echo 方法1: 使用 Apache 可执行文件测试配置
echo.
if exist "E:\xampp\apache\bin\httpd.exe" (
    echo 测试 Apache 配置文件...
    "E:\xampp\apache\bin\httpd.exe" -t
    echo.
    
    echo 尝试启动 Apache (前台模式，按 Ctrl+C 停止)...
    echo 如果看到错误信息，请记录下来
    echo.
    pause
    "E:\xampp\apache\bin\httpd.exe" -D FOREGROUND
) else (
    echo Apache 可执行文件不存在: E:\xampp\apache\bin\httpd.exe
    echo 请检查 XAMPP 安装路径
)

echo.
echo 2. 如果上面有错误，常见解决方案:
echo.
echo 端口占用错误:
echo   - 检查端口 80 和 443 是否被占用
echo   - 停止占用端口的程序
echo.
echo 配置文件错误:
echo   - 检查 E:\xampp\apache\conf\httpd.conf
echo   - 检查语法错误
echo.
echo 权限错误:
echo   - 以管理员身份运行 XAMPP
echo   - 检查文件夹权限
echo.
echo SSL 证书错误:
echo   - 检查 SSL 证书文件是否存在
echo   - 临时禁用 SSL 模块
echo.

pause

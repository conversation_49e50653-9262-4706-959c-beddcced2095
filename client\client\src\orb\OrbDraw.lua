-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 提现
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbDraw = class('OrbDraw', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbDraw:ctor(panel)
    print('OrbDraw:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('btn_rule'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRule) )
    self.m_panel:child('btn_draw_1'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnDraw1) )
    self.m_panel:child('btn_draw_15'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnDraw15) )
end


-------------------------------------------------------------------------------
-- 显示
-------------------------------------------------------------------------------
function OrbDraw:onShow()
    if not self.m_avatar_drawed then
        self.m_avatar_drawed = true
        local icon = self.m_panel:child('bg_avatar')
        local head = self:createHead(GlobalUserItem.dwUserID, GlobalUserItem.szHeadHttp, icon:size().width, 'orb/orb_bg_avatar_66.png')
        head:pos(icon:pos()):addTo(self.m_panel)
        icon:hide()
    end

    local amount = self:getAmount()
    self.m_panel:child('label_amount'):setString(string.format('%.2f', amount))

    local one_status = self:getOneStatus()
    local btn1 = self.m_panel:child('btn_draw_1')
    btn1:setTouchEnabled(false)
    btn1:ignoreContentAdaptWithSize(true)
    if one_status == 0 then
        btn1:texture('orb/orb_btn_draw_gray.png')
    elseif one_status == 1 then
        btn1:texture('orb/orb_btn_draw_red.png')
        btn1:setTouchEnabled(true)
    elseif one_status == 2 then
        btn1:texture('orb/orb_text_has_draw.png')
    end

    local btn15 = self.m_panel:child('btn_draw_15')
    btn15:ignoreContentAdaptWithSize(true)
    if amount >= 15 then
        btn15:texture('orb/orb_btn_draw_red.png')
    else
        btn15:texture('orb/orb_btn_draw_gray.png')
    end
end


-------------------------------------------------------------------------------
-- 规则按钮点击
-------------------------------------------------------------------------------
function OrbDraw:onBtnRule()
    self:open('panel_rule_draw')
end


-------------------------------------------------------------------------------
-- 1元提现按钮点击
-------------------------------------------------------------------------------
function OrbDraw:onBtnDraw1()
    if self:getAmount() < 1 then
        self:open('panel_mission')
        return
    end
    if self:getOneStatus() ~= 1 then
        return
    end

    self:request('draw', {type = 'one'}, function(data, response, http_status)
        if not helper.app.urlErrorCheck(data, response, http_status) then
            return
        end
        self:updateData({one_status = 2, balance = self:getAmount() - 1})
        self:addDrawLog(data.data.log)
        self:update('panel_main')
        self:close()
        helper.pop.message(LANG.ORB_DRAW_SUCC)
    end)
end


-------------------------------------------------------------------------------
-- 15元提现按钮点击
-------------------------------------------------------------------------------
function OrbDraw:onBtnDraw15()
    if self:getAmount() < 15 then
        self:open('panel_mission')
        return
    end

    self:request('draw', {type = 'all'}, function(data, response, http_status)
        if not helper.app.urlErrorCheck(data, response, http_status) then
            return
        end
        local amount = self:getAmount()
        self:updateData({balance = 0})
        self:addDrawLog(data.data.log)
        self:update('panel_main')
        self:close()
        self:open('panel_draw_success', {amount = amount})
    end)
end


return OrbDraw
-------------------------------------------------------------------------------
--  创世版1.0
--  房间设置
--  @date 2017-11-29
--  @auth woodoo
-------------------------------------------------------------------------------
local RoomSetting = class("RoomSetting", cc.Layer)


local FONT_SMALL_SCALE = 70
local FONT_BIG_SCALE = 90


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomSetting:ctor()
    print('RoomSetting:ctor...')
    self:enableNodeEvents()

    self.m_old_scale = GlobalUserItem.nCardFontScale
    self.m_old_cloth = GlobalUserItem.nTablecloth

    -- 载入主UI
    local main_node = helper.app.loadCSB('RoomSetting.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.panel_table = main_node:child('panel_table')
    self.panel_table:child('check_small').scale_value = FONT_SMALL_SCALE
    self.panel_table:child('check_big').scale_value = FONT_BIG_SCALE
    self.panel_table:child('check_green').cloth_index = 0
    self.panel_table:child('check_blue').cloth_index = 1

    -- 两张牌面大小示意牌
    --[[
    local card_small = cs.game.util.createCard(0x35, 3, 'pile')
    card_small:getChildByTag(1):scale( FONT_SMALL_SCALE / 100 )
    card_small:removeChildByTag(2)
    helper.layout.addCenter( self.panel_table:child('panel_small'), card_small )
    local card_big = cs.game.util.createCard(0x35, 3, 'pile')
    card_big:getChildByTag(1):scale( FONT_BIG_SCALE / 100 )
    card_big:removeChildByTag(2)
    helper.layout.addCenter( self.panel_table:child('panel_big'), card_big )
    self.panel_table:child('panel_small'):hide()
    self.panel_table:child('panel_big'):hide()
    --]]

    helper.logic.addListenerByName(self, {self.panel_table:child('panel_small,panel_big')}, {'tint', 'tint'})
    helper.logic.addListenerByName(self, {self.panel_table:child('img_blue,img_green')}, {'tint', 'tint'})
    self.panel_table:child('check_small'):addTouchEventListener( handler(self, self.onCheckSmall) )
    self.panel_table:child('check_small'):hide()
    self.panel_table:child('label_card'):hide()
    self.panel_table:child('check_big'):addTouchEventListener( handler(self, self.onCheckBig) )
    self.panel_table:child('check_big'):hide()
    self.panel_table:child('check_blue'):addTouchEventListener( handler(self, self.onCheckBlue) )
    self.panel_table:child('check_green'):addTouchEventListener( handler(self, self.onCheckGreen) )

    self.panel_table:child('check_small'):setSelected( GlobalUserItem.nCardFontScale == FONT_SMALL_SCALE )
    self.panel_table:child('check_big'):setSelected( GlobalUserItem.nCardFontScale == FONT_BIG_SCALE )
    self.panel_table:child('check_green'):setSelected( GlobalUserItem.nTablecloth == 0 )
    self.panel_table:child('check_blue'):setSelected( GlobalUserItem.nTablecloth == 1 )

    local children = self.panel_table:getChildren()
    for i, card in ipairs(children) do repeat
        if card:getName() == 'bg' or card:getName() == 'icon_table' then
            break
        end
        local x, y = card:pos()
        y = y + 100
        card:setPositionY(y)
    until true       
    end

end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function RoomSetting:onEnterTransitionFinish()
    print('RoomSetting:onEnterTransitionFinish...')
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RoomSetting:onExit()
    print('RoomSetting:onExit...')
end


-------------------------------------------------------------------------------
-- 关闭回调（main.SettingLayer）
-------------------------------------------------------------------------------
function RoomSetting:onCloseCallback()
    print('RoomSetting:onCloseCallback...')
    local game_layer = helper.app.getFromScene('game_room_layer')
    if game_layer then
        if self.m_old_scale ~= GlobalUserItem.nCardFontScale or 
            self.m_old_cloth ~= GlobalUserItem.nTablecloth then
            game_layer._gameView:onSettingChange()
        end
    end
end


-------------------------------------------------------------------------------
-- 小牌图片点击
-------------------------------------------------------------------------------
function RoomSetting:onPanelSmall(sender)
    self:onCheckSmall( self.panel_table:child('check_small') )
end


-------------------------------------------------------------------------------
-- 大牌图片点击
-------------------------------------------------------------------------------
function RoomSetting:onPanelBig(sender)
    self:onCheckBig( self.panel_table:child('check_big') )
end


-------------------------------------------------------------------------------
-- 小牌Checkbox点击
-------------------------------------------------------------------------------
function RoomSetting:onCheckSmall(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)

    self.panel_table:child('check_small'):setSelected(true)
    self.panel_table:child('check_big'):setSelected(false)
    GlobalUserItem.setCardFontSize(sender.scale_value)
end


-------------------------------------------------------------------------------
-- 大牌Checkbox点击
-------------------------------------------------------------------------------
function RoomSetting:onCheckBig(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)

    self.panel_table:child('check_small'):setSelected(false)
    self.panel_table:child('check_big'):setSelected(true)
    GlobalUserItem.setCardFontSize(sender.scale_value)
end


-------------------------------------------------------------------------------
-- 蓝色桌布图片点击
-------------------------------------------------------------------------------
function RoomSetting:onImgBlue(sender)
    self:onCheckBlue( self.panel_table:child('check_blue') )
end


-------------------------------------------------------------------------------
-- 绿色桌布图片点击
-------------------------------------------------------------------------------
function RoomSetting:onImgGreen(sender)
    self:onCheckGreen( self.panel_table:child('check_green') )
end


-------------------------------------------------------------------------------
-- 蓝色桌布Checkbox点击
-------------------------------------------------------------------------------
function RoomSetting:onCheckBlue(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)

    self.panel_table:child('check_blue'):setSelected(true)
    self.panel_table:child('check_green'):setSelected(false)
    GlobalUserItem.setTablecloth(sender.cloth_index)
end


-------------------------------------------------------------------------------
-- 绿色桌布Checkbox点击
-------------------------------------------------------------------------------
function RoomSetting:onCheckGreen(sender, event)
    if event == cc.EventCode.MOVED then return end
    helper.app.commClickEffect(sender, event)

    self.panel_table:child('check_blue'):setSelected(false)
    self.panel_table:child('check_green'):setSelected(true)
    GlobalUserItem.setTablecloth(sender.cloth_index)
end


return RoomSetting
-------------------------------------------------------------------------------
--  创世版1.0
--  可弹出信息头像
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
local HeadSprite = require(appdf.EXTERNAL_SRC .. 'HeadSprite')
local UserPop = cs.app.client('system.UserPop')
local UserPopGold = cs.app.client('system.UserPopGold')


local PopupHead = class('PopupHead', ccui.Layout)


-------------------------------------------------------------------------------
-- 构造
-------------------------------------------------------------------------------
function PopupHead:ctor(scene, useritem, size, pop_zorder)
    self.m_scene = scene
	self.m_useritem = useritem
    self.m_pop_zorder = pop_zorder
    self:size(size, size)
    self:anchor(0.5, 0.5)
    self:setCascadeColorEnabled(true)

	local head = HeadSprite:createNormal(useritem, size)
	head:pos(size/2, size/2):addTo(self)
    head:setCascadeColorEnabled(true)
    head:setName('head')

    if GlobalUserItem then
        self:setTouchEnabled(true)
        self:addTouchEventListener( helper.app.tintClickHandler(self, self.onHeadClick) )
    end
end


-------------------------------------------------------------------------------
-- 更新头像
-------------------------------------------------------------------------------
function PopupHead:updateHead(useritem)
    self.m_useritem = useritem
    self:child('head'):updateHead(useritem)
end


-------------------------------------------------------------------------------
-- 头像点击
-------------------------------------------------------------------------------
function PopupHead:onHeadClick(sender)
    local pos = self:convertToWorldSpace( cc.p(self:size().width/2, self:size().height/2) )
    if GlobalUserItem.bPrivateRoom then
        UserPop.popForUser(self.m_scene, self.m_useritem, pos):zorder(self.m_pop_zorder)
    elseif not GlobalUserItem.bIsRedArena then        -- 红包赛除外
        UserPopGold.popForUser(self.m_scene, self.m_useritem, pos):zorder(self.m_pop_zorder)
    end
end


return PopupHead
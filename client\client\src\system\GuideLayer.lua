-------------------------------------------------------------------------------
--  创世版1.0
--  引导层
--  @date 2018-04-20
--  @auth woodoo
-------------------------------------------------------------------------------
local GuideLayer = class("GuideLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GuideLayer:ctor(node, offset, mask_opacity)
    self:enableNodeEvents()
    self:setName('guide_layer')

    self.touch_rect = node:getBoundingBox()
    self.touch_pos = cc.p( self.touch_rect.x + self.touch_rect.width/2, self.touch_rect.y + self.touch_rect.height/2 )

    -- 裁剪对象
    self.clip_node = cc.ClippingNode:create()
    self.clip_node:pos(0, 0)
    self.clip_node:setInverted(true)
    self.clip_node:addTo(self)

    -- 颜色层
    local color_layer = ccui.Layout:create()
    self.color_layer = color_layer
    color_layer:setName('mask')
    color_layer:zorder(-100)
    color_layer:size(display.size)
    color_layer:setTouchEnabled(false)
    color_layer:setBackGroundColorType(ccui.LayoutBackGroundColorType.solid)
    color_layer:setBackGroundColor(cc.c3b(0, 0, 0))
    color_layer:opacity(mask_opacity or 120)
    color_layer:addTo(self.clip_node)

	-- 打孔节点
    local radius = math.min(self.touch_rect.width, self.touch_rect.height) / 2
	local stencil = cc.DrawNode:create()
	self.stencil = stencil
	stencil:drawSolidCircle(cc.p(0, 0), radius, math.rad(90), 50, cc.c4f(1.0, 1.0, 1.0, 1.0))
    stencil:pos(display.cx, display.cy)
    local target_pos = cc.pAdd(self.touch_pos, offset or cc.p(0, 0))
    stencil:scale(0):runAction( cc.Sequence:create(
        cc.Spawn:create(
            cc.ScaleTo:create(0.3, 1),
            cc.EaseOut:create( cc.MoveTo:create(0.3, target_pos), 3.0 )
        ),
        cc.CallFunc:create(function(sender)
            if tolua.isnull(self) then return end
            sender:runAction( cc.RepeatForever:create( cc.Sequence:create(
                cc.ScaleTo:create(0.5, 1.2), cc.ScaleTo:create(0.5, 1)
            ) ) )

            local arrow = display.newSprite('common/icon_guide_arrow.png'):addTo(self)
            arrow:anchor(0.5, 1 + self.touch_rect.height / 2 / arrow:size().height):pos(self.touch_pos)
            arrow:runAction(
                cc.RepeatForever:create( cc.Sequence:create(
                    cc.RotateBy:create(1, 60), cc.RotateBy:create(1, -60)
                ) )
            )
        end)
    ) )
	self.clip_node:setStencil(stencil)
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function GuideLayer:onEnter()
    print('GuideLayer:onEnter...')
    self:addListener()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GuideLayer:onExit()
    print('GuideLayer:onExit...')
	self:getEventDispatcher():removeEventListener(self.listener)
end


-------------------------------------------------------------------------------
-- 注册点击监听
-------------------------------------------------------------------------------
function GuideLayer:addListener()
    local function onTouchBegan(touch, event)
		local local_pos = touch:getLocation()
        self.touchBeganIn = cc.rectContainsPoint(self.touch_rect, local_pos)
		if not self.touchBeganIn then
			self.listener:setSwallowTouches(true)
		else
			self.listener:setSwallowTouches(false)
		end
		return true
	end

	local function onTouchEnded(touch, event)
		local local_pos = touch:getLocation()
		if cc.rectContainsPoint(self.touch_rect, local_pos) and self.touchBeganIn then
			self:removeFromParent()
		end
    end

    -- 单点触摸的监听器
    local listener = cc.EventListenerTouchOneByOne:create()
	self.listener = listener
	listener:setSwallowTouches(true)
    -- 注册两个回调监听方法
    listener:registerScriptHandler( onTouchBegan, cc.Handler.EVENT_TOUCH_BEGAN )  
    listener:registerScriptHandler( onTouchEnded, cc.Handler.EVENT_TOUCH_ENDED )  
    -- 绑定触摸事件到层当中
    self:getEventDispatcher():addEventListenerWithSceneGraphPriority(listener, self) 
end


return GuideLayer

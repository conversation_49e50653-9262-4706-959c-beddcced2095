-------------------------------------------------------------------------------
--  创世版1.0
--  登录场景
--  @date 2017-06-26
--  @auth woodoo
-------------------------------------------------------------------------------
local URL_BIND  = '/api/v1/user/bind_phone'         -- 绑定手机号

if not yl then
    appdf.req(appdf.CLIENT_SRC..'frame.yl')
end

if not cs then
    appdf.req(appdf.CLIENT_SRC..'system.init')
end

if not GlobalUserItem then
    appdf.req(appdf.CLIENT_SRC..'frame.GlobalUserItem')
end

if not PassRoom then
    appdf.req(appdf.CLIENT_SRC..'system.PassRoom')
    PassRoom:getInstance()
end


-- 全局处理lua错误
cc.exports.g_LuaErrorHandle = function ()
    cc.exports.bHandlePopErrorMsg = true
    if isDebug() then
        print('debug return')
        return true
    else
        print('release return')
        return false
    end
end

local LogonFrame = appdf.req(appdf.CLIENT_SRC.."frame.LogonFrame")
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
local WechatUtil = appdf.req(appdf.CLIENT_SRC..'system.WechatUtil')


local LoginScene = class('LoginScene', cc.load('mvc').ViewBase)


local FIRST_LOGIN = true


-------------------------------------------------------------------------------
-- 创建方法
-------------------------------------------------------------------------------
function LoginScene:onCreate()
    yl.app = self:getApp()
    local server_config = yl.app:getServerConfig()
    yl.is_reviewing = server_config.is_audit and server_config.is_audit == 1    -- 是否审核状态
    yl.IS_LOCATION_OPEN = server_config.is_open_location == 1    -- 是否开启定位
    if server_config.user_id then
        yl.TEST_USER_ID = tonumber(server_config.user_id)        -- 测试用户ID
    else
        yl.TEST_USER_ID = 0                                     -- 测试用户ID      
    end
    self:onceExcute()    

    if yl.IS_GAME_TOOL then
        self:initTool()
    else
        self:initLayer()  
    end
    
    self.m_frame = LogonFrame:create(self, handler(self, self.onLoginCallBack))

    -- 返回键
    helper.app.addKeyPadEvent(self, true)

    --读取配置
    GlobalUserItem.LoadData()

    --背景音乐
    helper.music.playLogin()
end

-- 正常初始化
function LoginScene:initLayer()
    -- 载入主UI
    local main_node = helper.app.loadCSB('Login.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('panel_policy'):hide()

    -- 健康游戏公告
    helper.app.createHealthGame(main_node, cc.p(main_node:getContentSize().width/2, 12))
    
    -- 版本
    main_node:child('label_ver'):setString( appdf.app:getVersion() )

    local btn_guest, btn_weixin, btn_phone = main_node:child('btn_guest,btn_weixin,btn_phone')
    helper.logic.addListenerByName(self, {btn_guest, btn_weixin, btn_phone})

    if device.platform ~= 'windows' then
        if yl.is_reviewing then -- 审核模式下游客可见，微信不可见，反之则反
            btn_guest:show():px(display.cx)
            btn_weixin:hide()
            btn_phone:hide()
        else
            btn_guest:hide()
            -- 短信服务已修复，恢复手机登录
            btn_phone:show()  -- 恢复显示
            btn_weixin:show()
        end
    else
        btn_guest:show():px(display.cx - 320)
        btn_phone:show():px(display.cx)
        btn_weixin:show():px(display.cx + 320)
    end 
end

-- 初始化隐私政策
function LoginScene:initGamePolicy()
    -- 服务协议和隐私政策
    local main_node = self.main_node
    if cs.app.USE_GAME_POLICY then
        main_node:child('panel_policy'):show()
        local has_agree = cc.UserDefault:getInstance():getIntegerForKey('GamePolicyAgree', 0) == 1
        self:setAgree(has_agree)
        main_node:child('panel_policy/check_agree'):addTouchEventListener( handler(self, self.onBtnAgree) )
        main_node:child('panel_policy/listview'):getItem(0):addTouchEventListener( handler(self, self.onBtnAgree) )
        main_node:child('panel_policy/listview'):getItem(1):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnService) )
        main_node:child('panel_policy/listview'):getItem(2):addTouchEventListener( handler(self, self.onBtnAgree) )
        main_node:child('panel_policy/listview'):getItem(3):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnPrivate) )
        if not has_agree then
            self:perform(function()
                self:showGamePolicy(nil)
            end, 0.1)  -- 必须延时，否则加到旧scene上了
        end
    else
        self.m_agree_policy = true
    end
end

-- 工具初始化
function LoginScene:initTool()
    -- 载入主UI
    local path = cs.app.CLIENT_SRC .. 'main.GameToolLayer'
    helper.pop.popLayer(path, self, nil, nil, true)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function LoginScene:onEnterTransitionFinish()
    print('LoginScene:onEnterTransitionFinish...')

    self:initGamePolicy()

    if FIRST_LOGIN then
        GlobalUserItem.m_tabOriginGameList = self:getApp()._gameList
    end

    local can_auto = device.platform ~= 'windows' and not yl.is_reviewing and FIRST_LOGIN and not yl.IS_GAME_TOOL -- 设备上并且非审核，自动点击微信登录
    if cs.app.USE_GAME_POLICY and not self.m_agree_policy then can_auto = false end
    local notice = yl.app:getServerConfig().notice
    if notice and notice ~= '' then     -- 有公告
        self:perform(function()
            local alert = helper.pop.alert({LANG.NOTICE, notice}, can_auto and handler(self, self.onBtnWeixin) or nil)
            alert:resize(800, 460)
        end, 0.1)  -- 必须延时，否则加到旧scene上了
    else    -- 没公告
        if can_auto then
            self:onBtnWeixin()
        end
    end
end



-------------------------------------------------------------------------------
-- 退出场景而且开始过渡动画时候触发。
-------------------------------------------------------------------------------
function LoginScene:onExitTransitionStart()
    print('LoginScene:onExitTransitionStart...')
    FIRST_LOGIN = false
    helper.app.addKeyPadEvent(self, false)
end


-------------------------------------------------------------------------------
-- 加载配置
-------------------------------------------------------------------------------
function LoginScene.onceExcute()
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. 'MultiPlatform')
    --文件日志
    LogAsset:getInstance():init(MultiPlatform:getInstance():getExtralDocPath(), true, true)
    --配置微信
    MultiPlatform:getInstance():thirdPartyConfig(yl.ThirdParty.WECHAT, yl.WeChat)
    --配置支付宝
    if yl.IS_ALIPAY_SHARE then
        MultiPlatform:getInstance():thirdPartyConfig(yl.ThirdParty.ALIPAY, yl.AliPay)
    end
    --配置竣付通
    --MultiPlatform:getInstance():thirdPartyConfig(yl.ThirdParty.JFT, yl.JFT)
    --配置分享
    MultiPlatform:getInstance():configSocial(yl.SocialShare)
    if yl.IS_LOCATION_OPEN then 
        --配置高德
        MultiPlatform:getInstance():thirdPartyConfig(yl.ThirdParty.AMAP, yl.AMAP)
    end
end


-------------------------------------------------------------------------------
-- 检查隐私政策是否已经同意
-------------------------------------------------------------------------------
function LoginScene:checkGamePolicy()
    if cs.app.USE_GAME_POLICY and not self.m_agree_policy then
        helper.pop.alert( LANG.GAME_POLICY_AGREE )
        return false
    else
        return true
    end
end


-------------------------------------------------------------------------------
-- 设置是否可续打点击
-------------------------------------------------------------------------------
function LoginScene:setAgree(is_agree)
	local check = self.main_node:child('panel_policy/check_agree')
    check:setSelected( is_agree )
    self.m_agree_policy = is_agree
    cc.UserDefault:getInstance():setIntegerForKey('GamePolicyAgree', is_agree and 1 or 0)
end


-------------------------------------------------------------------------------
-- 隐私政策点击(点击在文本和checkbox上都调用)
-------------------------------------------------------------------------------
function LoginScene:onBtnAgree(sender, event)
    if event == cc.EventCode.MOVED then return end

    local check = self.main_node:child('panel_policy/check_agree')
    helper.app.commClickEffect(check, event)

    if sender ~= check and event == cc.EventCode.ENDED then -- 点中文字上
        check:setSelected(not check:isSelected())
    end

	self.m_agree_policy = check:isSelected()
    cc.UserDefault:getInstance():setIntegerForKey('GamePolicyAgree', self.m_agree_policy and 1 or 0)
end


-------------------------------------------------------------------------------
-- 服务协议点击
-------------------------------------------------------------------------------
function LoginScene:onBtnService(sender)
    self:showGamePolicy('service')
end


-------------------------------------------------------------------------------
-- 隐私政策点击
-------------------------------------------------------------------------------
function LoginScene:onBtnPrivate(sender)
    self:showGamePolicy('private')
end


-------------------------------------------------------------------------------
-- 显示隐私政策(name为nil显示同意不同意选择窗口，否则显示对应的文本)
-------------------------------------------------------------------------------
function LoginScene:showGamePolicy(name)
    local path = cs.app.CLIENT_SRC .. 'main.GamePolicyLayer'
    helper.pop.popLayer(path, nil, {handler(self, self.onAgreeResult), name}, nil, true)
end


-------------------------------------------------------------------------------
-- 隐私政策同意结果
-------------------------------------------------------------------------------
function LoginScene:onAgreeResult(is_agree)
    self:setAgree(is_agree)
end


-------------------------------------------------------------------------------
-- 游客按钮点击
-------------------------------------------------------------------------------
function LoginScene:onBtnGuest()
    if not self:checkGamePolicy() then return end
    helper.pop.waiting()
    self.m_operate = 2
    self.m_frame:onLogonByVisitor()
end


-------------------------------------------------------------------------------
-- 手机号按钮点击
-------------------------------------------------------------------------------
function LoginScene:onBtnPhone()
    if not self:checkGamePolicy() then return end
    local path = cs.app.CLIENT_SRC .. 'main.MobileLoginLayer'
    helper.pop.popLayer(path, nil, {handler(self, self.afterPhoneLogin)}, nil, true)
end


-------------------------------------------------------------------------------
-- 手机登录后
-------------------------------------------------------------------------------
function LoginScene:afterPhoneLogin(data)
    if data.bind == 1 then
        -- 跳过授权，模拟微信返回数据登录
        local datatable = {
            unionid = data.account,
            screen_name = data.nick,
            profile_image_url = data.avatar_url,
            gender = data.gender,
        }
        self:afterWeixinLogin(datatable)
    else
        self.login_phone = {bind = data.bind, phone = data.phone, code = data.code}
        self:onBtnWeixin()
    end
end


-------------------------------------------------------------------------------
-- 微信按钮点击
-------------------------------------------------------------------------------
function LoginScene:onBtnWeixin()
    if not self:checkGamePolicy() then return end

    if device.platform ~= 'windows' then
        WechatUtil.checkToken( handler(self, self.doWeixinLogin), handler(self, self.afterWeixinLogin) )
    else
        self.m_frame:onLogonByAccount( yl.TEST_ACCOUNT, yl.TEST_PASSWORD )
    end
end


-------------------------------------------------------------------------------
-- 微信登录
-------------------------------------------------------------------------------
function LoginScene:doWeixinLogin()
    print('doWeixinLogin:', param)
    if tolua.isnull(self) then return end
    local ret = MultiPlatform:getInstance():thirdPartyLogin(yl.ThirdParty.WECHAT, handler(self, self.onWeixinCallBack))
    if ret then
        helper.pop.waiting()
        self:perform(function() helper.pop.waiting(false) end, 5)
    end
end


-------------------------------------------------------------------------------
-- 微信登录回调
-------------------------------------------------------------------------------
function LoginScene:onWeixinCallBack(param)
    print('onWeixinCallBack:', param)
    helper.pop.waiting(false)
    if tolua.isnull(self) then return end
    if type(param) == 'string' and string.len(param) > 0 then
        local ok, datatable = pcall(function()
                return cjson.decode(param)
        end)
        if ok and type(datatable) == 'table' then
            dump(datatable, '微信数据', 5)
            WechatUtil.saveToken(datatable)
            self:afterWeixinLogin(datatable)
        end
    end
end


-------------------------------------------------------------------------------
-- 微信登录后处理
-------------------------------------------------------------------------------
function LoginScene:afterWeixinLogin(datatable)
    if tolua.isnull(self) then return end
    if not self.m_frame then return end -- 多个sdk弹窗时，可能导致此时已经离开LoginScene了

    local account = datatable['unionid'] or ''
    local nick = datatable['screen_name'] or ''
    self.m_head_url = datatable['profile_image_url'] or ''
    local gender = tonumber(datatable['gender'] or '0')
    local plat = yl.PLATFORM_LIST[yl.ThirdParty.WECHAT]

    helper.pop.waiting()

    self.m_operate = 3
    self.m_frame:onLoginByThirdParty(account, nick, gender, self.m_head_url, plat)
end


-------------------------------------------------------------------------------
-- 登录回调
-------------------------------------------------------------------------------
function LoginScene:onLoginCallBack(result, message)
    if tolua.isnull(self) then return end
    helper.pop.waiting(false)
    if result == 1 then --成功
        if self.m_operate == 2 then -- 游客登录

        elseif self.m_operate == 3 then -- 微信登陆
            GlobalUserItem.szHeadHttp = self.m_head_url

        else    -- 游戏内注册登录
            GlobalUserItem.onSaveAccountConfig()
        end

        -- 进入主场景（金币场首次登录先进地区选择场景）
        local scene_name = 'main.MainScene'
        if not cs.app.NO_DISTRICT then
            if not yl.is_reviewing and GlobalUserItem.sysLastLogonDate.wYear < 2000 then
                scene_name = 'main.DistrictScene'
            else
                local has_default = require(cs.app.CLIENT_SRC .. 'main.DistrictSelectLayer').LoadDefaultPlugins()
                if not has_default then
                    scene_name = 'main.DistrictScene'
                end
            end
        end

        if self.login_phone and self.login_phone.bind == 0 then
            local params = {type='login', uid=GlobalUserItem.dwUserID, phone=self.login_phone.phone, code=self.login_phone.code}
            yl.GetUrl(URL_BIND, 'post', params, function(data, response, http_status)
                if data.code and data.code ~= 0 then return end
                GlobalUserItem.szMobilePhone = data.data.phone
            end)
        end

        self:getApp():enterSceneEx(cs.app.CLIENT_SRC .. scene_name, 'FADE', 0.5)

    elseif result == -1 then --失败
        if type(message) == 'string' and message ~= '' then
            helper.pop.message(message)
        end

    elseif result == 10 then --重复绑定
        -- todo: 不知道是干什么的
    end
end


return LoginScene
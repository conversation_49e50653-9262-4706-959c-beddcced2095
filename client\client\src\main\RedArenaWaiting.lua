-------------------------------------------------------------------------------
--  创世版1.0
--  万元红包赛匹配等待
--  @date 2018-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local RedArenaWaiting = class("RedArenaWaiting", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RedArenaWaiting:ctor()
    self:setName('red_arena_waiting')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('RedArenaWaiting.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    --main_node:child('label'):runAction( cc.RepeatForever:create( cc.Blink:create(2, 2) ) )

    main_node:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function RedArenaWaiting:onEnter()
    print('RedArenaWaiting:onEnter...')

    local tag = 1234
    self:perform(function()
        local game_layer = helper.app.getFromScene('game_room_layer')
        if not game_layer then
            self:stop(tag)
            local red_arena_layer = helper.app.getFromScene('red_arena_layer')
            if red_arena_layer then
                red_arena_layer:startGame()
            else
                self:removeFromParent()
            end
        end
    end, 0.1, 100, tag)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RedArenaWaiting:onExit()
    print('RedArenaWaiting:onExit...')
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function RedArenaWaiting:onBtnClose(sender)
    self:removeFromParent()
end


return RedArenaWaiting

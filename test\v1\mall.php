<?php
//活动接口
require(APPPATH.'/libraries/REST_Controller.php');

class Mall extends REST_Controller
{
    /*
     * 角色信息
     */
    private $_role;
    private $_game;
    private $_mall;

    private $_appId = '150155930270551';
    private $_appSecret = 'FdhPfq8XNzVQH27VEsPSO7A7k8WMK5XA';
    private $_url = 'https://pay.ipaynow.cn';

    public function __construct()
    {
        parent::__construct();
        $this->load->model('server_model');
        $this->load->model('user_model');
        $this->load->model('mp_model');
        $this->load->model('player_model');

        if($this->uri->uri_string() != 'api/v1/mall/notify') {
            $this->player_model->set_database($this->_channel['game_id']);
            $this->_game = $this->server_model->get_game_by_id($this->_channel['game_id']);

            $role_id = $this->input->post('uid');

            // 获取角色信息
            $role = $this->player_model->get_role_info($role_id);

            if(!$role){
                $this->response(array('code'=>1,'msg'=>'角色信息不存在'));
            }

            $this->_role = $role;

            $mall  = $this->server_model->get_mall_by_channel($this->_channel['channel_id']);

            if(!$mall) {
                $this->response(array('code'=>1,'msg'=>'未查询到商城配置'));
            }

            $this->_mall = $mall;
        }
    }

    public function goods_post()
    {
        // 获取渠道对应的商城

        // 获取角色类型
        $role_type = 1;

//        $agent = $this->user_model->get_one_agent($this->_role['UserID'],$this->_channel['game_id']);
//
//        if($agent) {
//            if($agent['group'] == 3) {
//                $role_type = 2;
//            } else if($agent['group'] == 8) {
//                $role_type = 3;
//            }
//        }

        $result = $this->mp_model->get_role_is_first_order($this->_role['UserID'],$this->_channel['game_id']);

        $goods = $this->server_model->get_mall_goods($this->_mall['id'],$role_type,TRUE,$result);

        $new_goods = [];

        foreach($goods as $k=>$good) {
            $new_goods[$k]['id'] = $good['id']*1;
            $new_goods[$k]['name'] = $good['name'];
            $new_goods[$k]['desc'] = $good['desc'];
//            $new_goods[$k]['good_desc'] = $good['good_desc'];
            $new_goods[$k]['icon'] = $good['icon'];
            $new_goods[$k]['promotion_type'] = $good['promotion_type']*1;
            $new_goods[$k]['is_first'] = $good['is_first']*1;
            $new_goods[$k]['price'] = $good['price']*1;
            $new_goods[$k]['icon'] = str_replace('https://','http://',$good['icon']);
            $new_goods[$k]['stock'] = $good['stock']*1;
            $new_goods[$k]['favorable_desc'] = $good['favorable_desc'];
        }

        $result['goods'] = array(
            1=>$new_goods,
            2=>$new_goods,
            3=>$new_goods);

        $score = $this->player_model->get_role_score($this->_role['UserID']);
        $score2 = $this->player_model->get_role_score2($this->_role['UserID']);

        $result['fangka'] = $score['RoomCard']*1;
        $result['jiangquan'] = isset($score2['Ticket']) ? $score2['Ticket'] : 0;

        $this->response(array('code'=>0,'msg'=>'','data'=>$result));

    }

    public function order_post() {

        $good_id = $this->input->post('good_id');

        // 获取商品
        $mall_good = $this->server_model->get_mall_good($this->_mall['id'],$good_id);

        if(empty($mall_good)) {
            $this->response(array('code' => 1, 'msg' => '商品不存在'));
        }

        if($mall_good['stock'] != '-99' && $mall_good['stock'] <= 0 ) {
            $this->response(array('code' => 1, 'msg' => '商品库存不足'));
        }

        $agent = $this->user_model->get_one_agent($this->_role['UserID'],$this->_game['game_id']);

        $this->load->helper('string');

        $order_no = date("YmdHis") . random_string('nozero', 3);

        $mp_user = $this->mp_model->get_mp_user_by_roleid($this->_role['UserID'],$this->_game['game_id']);

        if(empty($mp_user)) {
            $mp_user['user_id'] = 0;
        }

        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id'  => $this->_role['UserID'],
            'agent_id'  => $agent?$agent['id']:0,
            'good_id'  => $good_id,
            'game_id'  => $this->_game['game_id'],
            'order_no' => $order_no,
            'is_first' => $mall_good['is_first'],
            'is_agent' => ($mall_good['type']-1),
            'total_fee' => $mall_good['price'],
            'is_mall'  => 1,
            'mall_id'   => $this->_mall['id'],
            'kind_id'  =>$this->_role['CommonKindID'],
            'create_time' => time()
        );

         $this->db->insert('mp_order', $data);

        $order_id = $this->db->insert_id();

        $param['appId'] = $this->_appId;
        $param['deviceType'] = "0601";
        $param['frontNotifyUrl'] = base_url();
        $param['funcode'] = "WP001";
        $param['mhtCharset'] = "UTF-8";
        $param['mhtCurrencyType'] = "156";
        $param['mhtOrderAmt'] = $mall_good['price'] * 100;
        $param['mhtOrderDetail'] = '房卡';
        $param['mhtOrderName'] = '房卡';
        $param['mhtOrderNo'] = $order_no;
        $param['mhtOrderStartTime'] = date('YmdHis');
        $param['mhtOrderTimeOut'] = 3600;
        $param['mhtOrderType'] = "01";
        $param['mhtReserved'] = $order_no;
        $param['mhtSignType'] = "MD5";
        $param['notifyUrl'] = base_url().'api/v1/mall/notify';
        $param['outputType'] = 2;
        $param['payChannelType'] = 13;
        $param['version'] = "1.0.0";

        $str = '';

        foreach ($param as $k=>$v) {
            if($v) {
                $str .= $k.'='.$v.'&';
            }
        }

        $str .= md5($this->_appSecret);

        $param['mhtSignature'] = md5($str);

        $return = $this->_https_curl(http_build_query($param));

        parse_str($return,$result);

        if(isset($result['responseCode'])&&($result['responseCode'] == 'A001')) {
            echo $this->response(array('code' => 0, 'msg' => '','data'=> array('url'=>$result['tn'],'order_id'=>$order_id)));
        } else {
        //	 log_message("error","下单回调");
        //log_message("error",$result);
            echo $this->response(array('code' => 0, 'msg' => '下单错误'));
        }

    }

    public function query_post()
    {
        $order_ids = explode(',',$this->input->post('orders'));

        $success_ids = array();

        if($order_ids) {
            foreach ($order_ids as $order_id) {
                $this->db->where('id',$order_id);
                $query = $this->db->get('mp_order');
                $order = $query->row_array();

                if($order) {
                    if($order['status'] == 1) {
                        array_push($success_ids,$order_id*1);
                    }
                }
            }
        }

        if($success_ids) {

            // 获取房卡
            $score = $this->player_model->get_role_score($this->_role['UserID']);
            $score2 = $this->player_model->get_role_score2($this->_role['UserID']);

            $this->response(array('code' => 0, 'msg' => '','data'=>array(
                'success'=> $success_ids,
                'fangka'=>$score['RoomCard'],
                'jiangquan'=>isset($score2['Ticket']) ? $score2['Ticket'] : 0
            )));
        } else {
            $this->response(array('code' => 0, 'msg' => ''));
        }
    }

    public function notify_post() {
        $data = file_get_contents('php://input');
        log_message("error","现在支付商城回调");
        log_message("error",$data);

        parse_str($data,$data);

        if(!$this->_check_sign($data)) {
            echo "签名验证失败";exit;
        }

        // 查找订单
        $this->db->where('order_no',$data['mhtOrderNo']);
        $query = $this->db->get('mp_order');
        $order = $query->row_array();

        if($order['status'] == 1) {
            echo "success=Y";exit;
        }

        if($data['transStatus'] != 'A001') {
            echo "订单状态异常";exit;
        }

        $this->db->where('id',$order['id']);
        $this->db->update('mp_order',array('transaction_id'=>$data['channelOrderNo'],'pay_time'=>time(),'status'=>1));

        $this->player_model->set_database($order['game_id']);

        // 获取商品信息
        $good = $this->server_model->get_good_by_id($order['good_id']);
        $role = $this->player_model->get_role_info($order['role_id']);

        //添加房卡
        $this->player_model->update_role_score($order['role_id'], $good['score']);

//        if($order['game_id'] == 20 || $order['game_id'] == 36) {
//            //触发存储过程
//            $this->player_model->proc_pay_event($order['role_id'],$order['total_fee']);
//        }

        // 获取推荐代理
        if ($role && $role['SpreaderID'] > 0) {

            $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

            if ($agent) {

                $rate = 0.4;
                if ($order['game_id'] == 30){
                    $rate = 0.376;
                }
                if (in_array($order['game_id'],array(32,36))){
                    $rate = 0.5;
                }
                if (in_array($order['game_id'],array(35,37))){
                    $rate = 0.45;
                }

                // 插入到收益表
                $data = array(
                    'a_id' => $role['UserID'],
                    'a_account' => $role['Accounts'],
                    'b_id' => $agent['id'],
                    'b_account' => $agent['username'],
                    'create_time' => time(),
                    'type' => 4,
                    'money' => $rate * $order['total_fee']
                );

                $this->db->insert('bonus', $data);

                $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                // 获取上级代理
                $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);

                // 判断其下级代理个数
                $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                $rate = 0.06;

                if ($order['game_id'] == 30){
                    $rate = 0.047;
                }
                if ($order['game_id'] == 36){
                    $rate = 0.1;
                }
                if(in_array($order['game_id'],array(37))){
                    $rate = 0.075;
                }

                // 插入到收益表
                $data = array(
                    'a_id' => $agent['id'],
                    'a_account' => $agent['username'],
                    'b_id' => $parent['id'],
                    'b_account' => $parent['username'],
                    'create_time' => time(),
                    'type' => 1,
                    'money' => $rate * $order['total_fee']
                );

                $this->db->insert('bonus', $data);

                $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                if ($parent['spreader_id'] > 0) {
                    $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                    // 判断其下级代理个数
                    $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                    $rate = 0.04;

                    if ($order['game_id'] == 30){
                        $rate = 0.0188;
                    }
                    if (in_array($order['game_id'],array(36,37))){
                        $rate = 0.05;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $agent['id'],
                        'a_account' => $agent['username'],
                        'b_id' => $parent2['id'],
                        'b_account' => $parent2['username'],
                        'create_time' => time(),
                        'type' => 2,
                        'money' => $rate * $order['total_fee']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

                }


            }

        }

        // 更新商品库存

        $mall_good = $this->server_model->get_mall_good($order['mall_id'],$order['good_id']);

        if($mall_good && $mall_good['stock'] != -99 && $mall_good['stock'] > 0) {
            $this->db->where('good_id',$order['good_id']);
            $this->db->where('mall_id',$order['mall_id']);
            $this->db->set('stock','stock - 1',FALSE);
            $this->db->update('mall_goods');
        }

        echo "success=Y";


    }

    private function _check_sign($data)
    {
        ksort($data);
        reset($data);

        $str = '';

        foreach ($data as $k => $v) {
            if ($k == 'signature') continue;
            if ($v != '') {
                $str .= $k.'='.$v.'&';
            }
        }

        $str .= md5($this->_appSecret);

        $sign = md5($str);

        if(isset($data['signature']) && $sign == $data['signature']) {
            return TRUE;
        }

        return FALSE;
    }

    private function _https_curl($data)
    {
        $curl = curl_init($this->_url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HEADER ,0);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        $content = curl_exec($curl);
        curl_close($curl);

//        var_dump($content);

        return $content;
    }
}
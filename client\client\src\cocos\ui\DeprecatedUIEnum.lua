if nil == ccui then
    return
end

LAYOUT_COLOR_NONE                      = ccui.LayoutBackGroundColorType.none
LAYOUT_COLOR_SOLID                     = ccui.LayoutBackGroundColorType.solid
LAYOUT_COLOR_GRADIENT                  = ccui.LayoutBackGroundColorType.gradient

LAYOUT_ABSOLUTE                        = ccui.LayoutType.ABSOLUTE
LAYOUT_LINEAR_VERTICAL                 = ccui.LayoutType.VERTICAL
LAYOUT_LINEAR_HORIZONTAL               = ccui.LayoutType.HORIZONTAL
LAYOUT_RELATIVE                        = ccui.LayoutType.RELATIVE

BRIGHT_NONE                            = ccui.BrightStyle.none
BRIGHT_NORMAL                          = ccui.BrightStyle.normal
BRIGHT_HIGHLIGHT                       = ccui.BrightStyle.highlight

UI_TEX_TYPE_LOCAL                      = ccui.TextureResType.localType
UI_TEX_TYPE_PLIST                      = ccui.TextureResType.plistType

TOUCH_EVENT_BEGAN                      = ccui.TouchEventType.began
TOUCH_EVENT_MOVED                      = ccui.TouchEventType.moved
TOUCH_EVENT_ENDED                      = ccui.TouchEventType.ended
TOUCH_EVENT_CANCELED                   = ccui.TouchEventType.canceled

SIZE_ABSOLUTE                          = ccui.SizeType.absolute
SIZE_PERCENT                           = ccui.SizeType.percent

POSITION_ABSOLUTE                      = ccui.PositionType.absolute
POSITION_PERCENT                       = ccui.PositionType.percent

CHECKBOX_STATE_EVENT_SELECTED          = ccui.CheckBoxEventType.selected
CHECKBOX_STATE_EVENT_UNSELECTED        = ccui.CheckBoxEventType.unselected

CHECKBOX_STATE_EVENT_SELECTED          = ccui.CheckBoxEventType.selected
CHECKBOX_STATE_EVENT_UNSELECTED        = ccui.CheckBoxEventType.unselected

LoadingBarTypeLeft                     = ccui.LoadingBarDirection.LEFT
LoadingBarTypeRight                    = ccui.LoadingBarDirection.RIGHT

LoadingBarTypeRight                    = ccui.SliderEventType.percent_changed

TEXTFIELD_EVENT_ATTACH_WITH_IME        = ccui.TextFiledEventType.attach_with_ime
TEXTFIELD_EVENT_DETACH_WITH_IME        = ccui.TextFiledEventType.detach_with_ime
TEXTFIELD_EVENT_INSERT_TEXT            = ccui.TextFiledEventType.insert_text
TEXTFIELD_EVENT_DELETE_BACKWARD        = ccui.TextFiledEventType.delete_backward

SCROLLVIEW_EVENT_SCROLL_TO_TOP         = ccui.ScrollViewDir.none
SCROLLVIEW_DIR_VERTICAL                = ccui.ScrollViewDir.vertical
SCROLLVIEW_DIR_HORIZONTAL              = ccui.ScrollViewDir.horizontal
SCROLLVIEW_DIR_BOTH                    = ccui.ScrollViewDir.both

SCROLLVIEW_EVENT_SCROLL_TO_TOP         = ccui.ScrollviewEventType.scrollToTop
SCROLLVIEW_EVENT_SCROLL_TO_BOTTOM      = ccui.ScrollviewEventType.scrollToBottom
SCROLLVIEW_EVENT_SCROLL_TO_LEFT        = ccui.ScrollviewEventType.scrollToLeft
SCROLLVIEW_EVENT_SCROLL_TO_RIGHT       = ccui.ScrollviewEventType.scrollToRight
SCROLLVIEW_EVENT_SCROLLING             = ccui.ScrollviewEventType.scrolling
SCROLLVIEW_EVENT_BOUNCE_TOP            = ccui.ScrollviewEventType.bounceTop
SCROLLVIEW_EVENT_BOUNCE_BOTTOM         = ccui.ScrollviewEventType.bounceBottom
SCROLLVIEW_EVENT_BOUNCE_LEFT           = ccui.ScrollviewEventType.bounceLeft
SCROLLVIEW_EVENT_BOUNCE_RIGHT          = ccui.ScrollviewEventType.bounceRight

PAGEVIEW_EVENT_TURNING                 = ccui.PageViewEventType.turning

PAGEVIEW_TOUCHLEFT                     = ccui.PVTouchDir.touch_left
PAGEVIEW_TOUCHRIGHT                    = ccui.PVTouchDir.touch_right

LISTVIEW_DIR_NONE                      = ccui.ListViewDirection.none
LISTVIEW_DIR_VERTICAL                  = ccui.ListViewDirection.vertical
LISTVIEW_DIR_HORIZONTAL                = ccui.ListViewDirection.horizontal

LISTVIEW_MOVE_DIR_NONE                 = ccui.ListViewMoveDirection.none
LISTVIEW_MOVE_DIR_UP                   = ccui.ListViewMoveDirection.up
LISTVIEW_MOVE_DIR_DOWN                 = ccui.ListViewMoveDirection.down
LISTVIEW_MOVE_DIR_LEFT                 = ccui.ListViewMoveDirection.left
LISTVIEW_MOVE_DIR_RIGHT                = ccui.ListViewMoveDirection.right

LISTVIEW_EVENT_INIT_CHILD              = ccui.ListViewEventType.init_child
LISTVIEW_EVENT_UPDATE_CHILD            = ccui.ListViewEventType.update_child

LAYOUT_PARAMETER_NONE                  = ccui.LayoutParameterType.none
LAYOUT_PARAMETER_LINEAR                = ccui.LayoutParameterType.linear
LAYOUT_PARAMETER_RELATIVE              = ccui.LayoutParameterType.relative

ccui.LoadingBarType                    = ccui.LoadingBarDirection
ccui.LoadingBarType.left               = ccui.LoadingBarDirection.LEFT
ccui.LoadingBarType.right              = ccui.LoadingBarDirection.RIGHT

ccui.LayoutType.absolute               = ccui.LayoutType.ABSOLUTE
ccui.LayoutType.linearVertical         = ccui.LayoutType.VERTICAL
ccui.LayoutType.linearHorizontal       = ccui.LayoutType.HORIZONTAL
ccui.LayoutType.relative               = ccui.LayoutType.RELATIVE

ccui.ListViewEventType.onsSelectedItem = ccui.ListViewEventType.ONSELECTEDITEM_START

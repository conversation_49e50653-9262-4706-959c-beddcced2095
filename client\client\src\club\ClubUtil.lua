-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部工具
--  @date 2018-01-14
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local HeadSprite = cs.app.client('external.HeadSprite')
local cmd = cs.app.client('header.CMD_Common')
local ClubBrowser = cs.app.client('club.ClubBrowser')
local ClubUtil = class("ClubUtil")


-- 分页每页数量
ClubUtil.PER_PAGE = 10
-- 俱乐部玩法数量
ClubUtil.MAX_CLUB_KINDS = 3
-- 俱乐部名称限定在20个字以内（中文2）
ClubUtil.CLUB_NAME_LEN = 20


ClubUtil.MEMBER_STATUS = {
    [0] = 'common/icon_plat_gray_point.png',
    [2] = 'common/icon_plat_red_point.png',
    [3] = 'common/icon_plat_green_point.png',
}
-- 给状态加个容错
local ms_mt = {
    __index = function(self, key)
        return rawget(self, 0)
    end,
}
setmetatable(ClubUtil.MEMBER_STATUS, ms_mt)


-------------------------------------------------------------------------------
-- SYSTEMTIME结构转日期时间
-------------------------------------------------------------------------------
function ClubUtil.convertTime(sys_time, has_time)
    local ret = string.format('%d-%02d-%02d', sys_time.wYear, sys_time.wMonth, sys_time.wDay)
    if has_time then
        ret = ret .. string.format(' %02d:%02d:%02d', sys_time.wHour, sys_time.wMinute, sys_time.wSecond)
    end
    return ret
end


-------------------------------------------------------------------------------
-- 中文字符串长度检查
-------------------------------------------------------------------------------
function ClubUtil.getUTF8Len(s)
    local chars = helper.str.splitUTF8(s)
    local len = 0
    for i, v in ipairs(chars) do
        len = len + (#v > 1 and 2 or 1)   -- 中文字2
    end
    return len
end


-------------------------------------------------------------------------------
-- 回退
-------------------------------------------------------------------------------
function ClubUtil.back(this)
    ClubBrowser.back(this)
end


-------------------------------------------------------------------------------
-- 打开弹窗
-------------------------------------------------------------------------------
function ClubUtil.open(this, class_name, parent, layer_params, mask_opacity, mask_closable, no_blur)
    ClubBrowser.open(this, class_name, parent, layer_params, mask_opacity, mask_closable, no_blur)
end


-------------------------------------------------------------------------------
-- 初始化UI
-------------------------------------------------------------------------------
function ClubUtil.initUI(owner, csb, onback_call)
    owner:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB(csb)
    owner.main_node = main_node
    owner:addChild(main_node)

    ClubUtil.initTopbar(main_node:child('topbar'), owner, onback_call)

    return main_node
end


-------------------------------------------------------------------------------
-- 初始化topbar
-------------------------------------------------------------------------------
function ClubUtil.initTopbar(top_bar, owner, onback_call)
    if not top_bar then return end

    if not onback_call then
        onback_call = function(a)
            ClubUtil.back(a)
            a:removeFromParent()
        end
    end
    local btn_back = top_bar:child('btn_back')
    if btn_back then
        btn_back:addTouchEventListener( helper.app.commClickHandler(owner, onback_call) )
    end
end


-------------------------------------------------------------------------------
-- 邀请朋友加入俱乐部
-------------------------------------------------------------------------------
function ClubUtil.shareClub(id, name)
    local id_str = string.format('%06d', id)
    local url = helper.app.makeInviteUrl('c' .. id_str)
    local title = LANG{'CLUB_INVITE_TITLE', app_name=cs.app.APP_NAME or '', id=id_str}
    local desc = LANG{'CLUB_INVITE_MSG', club = name}
    helper.pop.shareLink(url, title, desc, 'word/font_title_invite.png'):showButtons('hy,mowang,xianliao,copy')
end


-------------------------------------------------------------------------------
-- 命令监听
-------------------------------------------------------------------------------
function ClubUtil.listen(sub, obj, func)
	LiveFrame:getInstance():addListen(cmd.MDM_CLUB_SERVICE, sub, obj, func)
end


-------------------------------------------------------------------------------
-- 数据发送
-------------------------------------------------------------------------------
function ClubUtil.send(sub, define_table, data_table)
	local cmd_data = ExternalFun.create_netdata( define_table, data_table )
	cmd_data:setcmdinfo(cmd.MDM_CLUB_SERVICE, sub)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 通用返回处理
-------------------------------------------------------------------------------
function ClubUtil.commonResp(data, t, succ_callback)
    local ret = LiveFrame:getInstance():resp(data, t)
    if not ret then return false end

    -- 判断是否成功
    if ret.dwID > 0 then
        helper.pop.message(ret.szMsg)
        return false
    else
        succ_callback(ret)
        return true
    end
end


-------------------------------------------------------------------------------
-- 创建头像
-------------------------------------------------------------------------------
function ClubUtil.createPlayerHead(parent, user_id, url)
    local size = parent:size()
	local head = HeadSprite:createNormal({dwUserID=user_id, szHeadHttp=url}, size.width)
    head:pos(size.width/2, size.height/2):addTo(parent)
    return head
end


-------------------------------------------------------------------------------
-- 显示玩法创建
-------------------------------------------------------------------------------
function ClubUtil.showKindCreator(params, callback)
    -- 调用创建房间UI
    local path = cs.app.CLIENT_SRC .. 'main.RoomCreateLayer'
    helper.pop.popLayer(path, nil, {false, params, callback}):setName('room_create_layer')
end


-------------------------------------------------------------------------------
-- 发送玩法命令
-------------------------------------------------------------------------------
function ClubUtil.sendKindCmd(commond, rule_id, club_id, param)
    local values = {
        dwRuleID            = rule_id,
        dwClubID            = club_id,
        wKindID             = param.kind,
        wJoinGamePeopleCount= param.renshu,
        cbPayType           = param.pay_type,
        cbPlayCountIndex    = param.jushu_index,
        nBaseScore          = param.difen,
        llGameRule          = param.rule,
        dwOwnerID           = 0,
        szRule              = param.room_desc,
        cbCanContinue       = param.continue,
    }
    ClubUtil.send(commond, cmd.tagClubRule, values)
end


-------------------------------------------------------------------------------
-- 检查是否有申请
-------------------------------------------------------------------------------
function ClubUtil.sendApplyCountCmd(club_id)
    ClubUtil.send(cmd.SUB_CLUB_APPLY_COUNT, cmd.CMD_GR_ID, {dwID=club_id})
end


-------------------------------------------------------------------------------
-- 红点
-------------------------------------------------------------------------------
function ClubUtil.addRedPoint(btn, is_add)
    local sp = btn:child('sp_redpoint')
    if is_add and not sp then
        sp = display.newSprite('common/icon_red_point.png')
        sp:setName('sp_redpoint')
        sp:pos(btn:size().width - 5, btn:size().height - 5):addTo(btn)
    elseif not is_add and sp then
        sp:removeFromParent()
    end
end


return ClubUtil

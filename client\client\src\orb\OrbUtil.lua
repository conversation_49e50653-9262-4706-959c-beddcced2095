-------------------------------------------------------------------------------
--  创世版3.0
--  拆红包 - 通用方法
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local HeadSprite = cs.app.client('external.HeadSprite')

local root = nil
local module_zorder = 0
local modules = {}
local main_data = {}    -- 主数据

local OrbUtil = class("OrbUtil")


-------------------------------------------------------------------------------
-- 设置根节点
-------------------------------------------------------------------------------
function OrbUtil.setRoot(root_node, all_modules)
    root = root_node
    modules = all_modules
end


-------------------------------------------------------------------------------
-- 设置主数据
-------------------------------------------------------------------------------
function OrbUtil.setMainData(data)
    main_data = data
    main_data.end_at = os.time() + data.time_left
end


-------------------------------------------------------------------------------
-- 获取主数据
-------------------------------------------------------------------------------
function OrbUtil.getMainData()
    return main_data
end


-------------------------------------------------------------------------------
-- 剩余时间
-------------------------------------------------------------------------------
function OrbUtil.getTimedown()

end


-------------------------------------------------------------------------------
-- 回退
-------------------------------------------------------------------------------
function OrbUtil.close(panel)
    if type(panel) == 'string' then
        panel = root:child(panel)
    end
    panel:hide()
end


-------------------------------------------------------------------------------
-- 打开弹窗
-------------------------------------------------------------------------------
function OrbUtil.open(panel_name, params)
    local panel = root:child(panel_name)
    if not panel then return end

    module_zorder = module_zorder + 1
    panel:zorder(module_zorder)
    panel:show()

    if not params or not params.no_effect then
        local bg = panel:child('bg')
        if bg then
            bg:stop():scale(0.9):runAction(cc.EaseBackOut:create( cc.ScaleTo:create(0.1, 1) ))
        end
    end

    modules[panel_name]:onShow(params)
end


-------------------------------------------------------------------------------
-- 更新窗口
-------------------------------------------------------------------------------
function OrbUtil.update(panel_name)
    local panel = root:child(panel_name)
    if not panel then return end

    modules[panel_name]:onUpdate()
end


-------------------------------------------------------------------------------
-- 分享
-------------------------------------------------------------------------------
function OrbUtil.share(callback, no_result)
    OrbUtil.open('panel_share', {callback = callback, no_effect = true, no_result = no_result})
    --[[
    local id_str = string.format('%06d', id)
    local url = helper.app.makeInviteUrl('c' .. id_str)
    local title = LANG{'CLUB_INVITE_TITLE', app_name=cs.app.APP_NAME or '', id=id_str}
    local desc = LANG{'CLUB_INVITE_MSG', club = name}
    helper.pop.shareLink(url, title, desc, 'word/font_title_invite.png'):showButtons('hy,mowang,xianliao,copy')
    --]]
end


-------------------------------------------------------------------------------
-- 创建头像
-------------------------------------------------------------------------------
function OrbUtil.createHead(user_id, url, width, png)
	return HeadSprite:createClipHead({dwUserID=user_id, szHeadHttp=url}, width, png)
end


return OrbUtil

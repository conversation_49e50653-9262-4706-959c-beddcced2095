# LHMJ313 游戏模块文档

## 游戏模块概述

LHMJ313 项目支持多种棋牌游戏，每个游戏都是独立的模块，采用统一的配置格式和架构设计。游戏模块位于 `client/game/` 目录下，支持通过包配置系统进行定制。

## 游戏模块架构

### 模块结构
```
client/game/{game_name}/
├── src/
│   ├── game.lua           # 游戏基础配置
│   ├── init.lua           # 游戏初始化脚本
│   ├── room/              # 房间逻辑
│   │   ├── GameLayer.lua  # 游戏主界面
│   │   ├── CMD_Game.lua   # 游戏命令定义
│   │   └── GameLogic.lua  # 游戏逻辑
│   └── ui/                # 游戏界面
└── res/                   # 游戏资源
```

### 配置扩展机制
每个游戏支持通过 `game_ext.lua` 文件进行定制：
```
client/package/{package_name}/game/{game_name}/src/game_ext.lua
```

## 支持的游戏类型

### 1. 麻将类游戏 (mahjong)

#### 基础配置
- **模块名**: mahjong
- **最大玩家数**: 4人
- **支持续打**: 是
- **听牌提示**: 可配置

#### 地方变种

##### 三门麻将 (301)
- **适用地区**: 浙江三门、台州
- **特色规则**:
  - 支持买马（不买马/买一马/买二马）
  - 三摊承包机制
  - 财神系统（有财神/无财神）
  - 缺门规则（不缺门/缺一门/缺二门）

```lua
[301] = {
    NAME = '三门麻将',
    ZHUANG = true,      -- 庄标记
    FENG = true,        -- 风标记
    JUSHU = '8局,4;16局,8',
    RENSHU = '4;3;2',
    DIFEN = '1;2;5;10;20',
    WANFA = '0,1,0|1,1,0|2,1,1;3,1,1;4,1,1',
    RULE0 = '不买马',
    RULE1 = '买一马',
    RULE2 = '买二马',
    RULE3 = '三摊承包',
    RULE4 = '有财神',
}
```

##### 临海麻将 (403)
- **适用地区**: 浙江临海
- **特色规则**:
  - 翻屁股机制
  - 白板财神
  - 三摊承包
  - 抓杠头规则

##### 磐安碰碰胡 (404)
- **适用地区**: 浙江磐安
- **特色规则**:
  - 翻财神/白板财神
  - 60倍封顶/不封顶
  - 碰碰胡玩法

##### 天台三阿磨 (304)
- **适用地区**: 浙江天台
- **特色规则**:
  - 买马机制
  - 胡牌封顶（300胡/500胡）
  - 乱花规则

##### 百搭麻将 (300)
- **适用地区**: 通用
- **特色规则**:
  - 抓鸟（2鸟/4鸟）
  - 只自摸/可放炮

### 2. 斗地主 (land)

#### 基础配置
- **模块名**: land
- **最大玩家数**: 3人
- **游戏类型**: 经典斗地主

#### 游戏规则
```lua
[200] = {
    NAME = '斗地主',
    ZHUANG = 'feng',
    JUSHU = '10局,4;20局,8',
    RENSHU = '3',
    DIFEN = '1',
    ZHIFU = '0;1',
}
```

### 3. 四副牌 (fourcards)

#### 基础配置
- **模块名**: fourcards
- **最大玩家数**: 4人
- **游戏类型**: 扑克牌游戏

#### 游戏规则
```lua
[101] = {
    NAME = '四副牌',
    ZHUANG = 'feng',
    JUSHU = '4局,4;8局,8',
    RENSHU = '4',
    DIFEN = '1',
    WANFA = '0,1,0|0,0,1;1,1,0|1,0,1;2,1,0',
    RULE0 = '独奖开启',
    RULE1 = '正点开启',
    RULE2 = '四同花',
}
```

### 4. 十三水 (thirteen)

#### 基础配置
- **模块名**: thirteen
- **游戏类型**: 十三张牌型游戏

#### 游戏规则
```lua
[100] = {
    NAME = '磐安十三水',
    ZHUANG = 'feng',
    JUSHU = '10局,4;20局,8',
    RENSHU = '4;3;2',
    DIFEN = '1',
    ZHIFU = '0;1',
}
```

### 5. 红十 (redten)

#### 基础配置
- **模块名**: redten
- **最大玩家数**: 4人

#### 游戏规则
```lua
[110] = {
    NAME = '红十',
    ZHUANG = 'feng',
    JUSHU = '6局,4;12局,8',
    RENSHU = '4',
    DIFEN = '1',
    ZHIFU = '0;1;3',
}
```

### 6. 双扣 (shuangkou)

#### 基础配置
- **模块名**: shuangkou
- **最大玩家数**: 4人

#### 游戏规则
```lua
[201] = {
    NAME = '双扣',
    ZHUANG = 'feng',
    JUSHU = '4局,4;8局,8',
    RENSHU = '4',
    DIFEN = '1',
    WANFA = '0,1,1|1,1,0;2,1,1|3,1,0',
    RULE0 = '双扣2, 单扣1, 平扣0',
    RULE1 = '双扣4, 单扣2, 平扣1',
    RULE2 = '普通双扣',
    RULE3 = '百变双扣'
}
```

### 7. 其他游戏

#### 跑得快 (paodekuai)
- 快速出牌游戏

#### 翻翻炸 (fanfanzha)
- 特色炸金花游戏

#### 牛牛 (oxex)
- 牛牛比大小游戏

#### 比鸡 (biji)
- 扑克比较游戏

#### 大同 (datong)
- 地方特色游戏

## 游戏配置系统

### 配置参数说明

#### 基础参数
- **MODULE_NAME**: 游戏模块名
- **RES**: 资源路径
- **SRC**: 脚本路径
- **MAX_PLAYER**: 最大玩家数
- **BRAND**: 品牌标识

#### 房间配置
- **JUSHU**: 局数配置 (格式: "显示文本,房卡数")
- **RENSHU**: 人数配置
- **DIFEN**: 底分配置
- **ZHIFU**: 支付方式 (0=房主支付, 1=AA支付, 3=赢家支付)

#### 玩法配置
- **WANFA**: 玩法规则配置
- **WANFA_COL**: 玩法列数
- **WANFA_HEIGHT**: 玩法面板高度
- **RULE{N}**: 具体规则描述
- **RULE{N}_NONE**: 规则的反向描述

#### 特殊配置
- **ZHUANG**: 庄家标记类型 (true=庄, feng=东南西北)
- **FENG**: 风标记
- **QUAN_FENG**: 圈风标记
- **DISCARD_IMMEDIATELY**: 弃牌立即显示
- **IS_FAST_OUT_CARD**: 快速出牌
- **HAS_GAME_RESULT**: 是否有小结算界面

### 包配置系统

#### 包结构
```
client/package/{package_name}/
├── client/src/pack.lua    # 包配置
└── game/                  # 游戏定制
    └── {game_name}/src/game_ext.lua
```

#### 包配置示例
```lua
local pack = {
    LOGONSERVER = 'lhmj.tuo3.com.cn',
    LOGONPORT = 8607,
    BASE_C_VERSION = 10,
    BASE_C_RESVERSION = 1,
    PACK_GAMES = 'mahjong|fourcards',
    WX_APPID = 'wx...',
    WX_SECRET = '...',
}
```

## 游戏扩展机制

### 扩展文件加载
```lua
local function loadExt(ext)
    local path = string.gsub(game.SRC, '%.', '/') .. 'game_ext.' .. ext
    if cc.FileUtils:getInstance():isFileExist(path) then
        local game_ext = require(game.SRC .. 'game_ext')
        for k, v in pairs(game_ext) do
            game[k] = v
        end
        return true
    end
end
```

### 定制优先级
1. 基础游戏配置 (`game.lua`)
2. 包定制配置 (`game_ext.lua`)
3. 运行时动态配置

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

<?php
/**
 * 修改后的 user.php 短信验证部分
 * 添加腾讯云短信支持，云之讯作为备用
 */

class User extends MY_Controller {
    
    /**
     * 腾讯云短信发送方法
     */
    private function sendTencentCloudSMS($phone, $code) {
        // 腾讯云短信配置 - 请替换为您的实际配置
        $secretId = 'YOUR_TENCENT_SECRET_ID';
        $secretKey = 'YOUR_TENCENT_SECRET_KEY';
        $appId = 'YOUR_TENCENT_APP_ID';
        $signName = '您的签名';
        $templateId = 'YOUR_TEMPLATE_ID';
        
        // 如果没有配置腾讯云，返回失败
        if ($secretId == 'YOUR_TENCENT_SECRET_ID') {
            return array('success' => false, 'msg' => '腾讯云短信未配置');
        }
        
        try {
            // 腾讯云短信API调用
            $result = $this->callTencentCloudAPI($secretId, $secretKey, $appId, $signName, $templateId, $phone, $code);
            
            if ($result['success']) {
                return array('success' => true, 'msg' => '发送成功', 'provider' => 'tencent');
            } else {
                return array('success' => false, 'msg' => $result['msg'], 'provider' => 'tencent');
            }
            
        } catch (Exception $e) {
            return array('success' => false, 'msg' => $e->getMessage(), 'provider' => 'tencent');
        }
    }
    
    /**
     * 调用腾讯云短信API
     */
    private function callTencentCloudAPI($secretId, $secretKey, $appId, $signName, $templateId, $phone, $code) {
        // 简化版腾讯云API调用
        // 实际使用时建议使用腾讯云官方SDK
        
        $url = 'https://sms.tencentcloudapi.com/';
        $timestamp = time();
        $nonce = rand(10000, 99999);
        
        $params = array(
            'Action' => 'SendSms',
            'Version' => '2021-01-11',
            'Region' => 'ap-beijing',
            'SmsSdkAppId' => $appId,
            'SignName' => $signName,
            'TemplateId' => $templateId,
            'PhoneNumberSet.0' => '+86' . $phone,
            'TemplateParamSet.0' => $code,
            'Timestamp' => $timestamp,
            'Nonce' => $nonce,
            'SecretId' => $secretId
        );
        
        // 生成签名
        $signature = $this->generateTencentSignature($params, $secretKey);
        $params['Signature'] = $signature;
        
        // 发送HTTP请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/x-www-form-urlencoded'
        ));
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return array('success' => false, 'msg' => 'CURL错误: ' . $error);
        }
        
        if ($httpCode != 200) {
            return array('success' => false, 'msg' => 'HTTP错误: ' . $httpCode);
        }
        
        $response = json_decode($result, true);
        
        if (isset($response['Response']['SendStatusSet'][0]['Code']) && 
            $response['Response']['SendStatusSet'][0]['Code'] == 'Ok') {
            return array('success' => true, 'msg' => '发送成功');
        } else {
            $errorMsg = isset($response['Response']['Error']['Message']) ? 
                       $response['Response']['Error']['Message'] : '未知错误';
            return array('success' => false, 'msg' => $errorMsg);
        }
    }
    
    /**
     * 生成腾讯云签名
     */
    private function generateTencentSignature($params, $secretKey) {
        // 简化版签名生成，实际使用时建议使用官方SDK
        ksort($params);
        $queryString = http_build_query($params);
        $stringToSign = 'POST&%2F&' . urlencode($queryString);
        return base64_encode(hash_hmac('sha1', $stringToSign, $secretKey, true));
    }
    
    /**
     * 修改后的获取验证码方法
     */
    public function get_verify_code_post() {
        $role_id = $this->input->post('uid');
        $phone = $this->input->post('phone');
        $type = $this->input->post('type') ? $this->input->post('type') : 'bind';
        
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            $this->response(array('code' => 1, 'msg' => '手机号格式错误'));
            return;
        }
        
        // 检查发送频率限制（60秒内只能发送一次）
        $this->db->where('phone', $phone);
        $this->db->where('create_time >', time() - 60);
        $recent = $this->db->get('tuo3_verify_code')->row_array();
        
        if ($recent) {
            $this->response(array('code' => 1, 'msg' => '发送过于频繁，请稍后再试'));
            return;
        }
        
        // 生成4位随机验证码
        $code = rand(1000, 9999);
        
        $sms_success = false;
        $sms_provider = '';
        $sms_msg = '';
        
        // 方案1: 优先尝试腾讯云短信
        $tencent_result = $this->sendTencentCloudSMS($phone, $code);
        if ($tencent_result['success']) {
            $sms_success = true;
            $sms_provider = 'tencent';
            $sms_msg = '腾讯云短信发送成功';
            error_log("腾讯云短信发送成功: $phone, $code");
        } else {
            error_log("腾讯云短信发送失败: " . $tencent_result['msg']);
        }
        
        // 方案2: 如果腾讯云失败，尝试云之讯备用
        if (!$sms_success) {
            try {
                require(APPPATH . '/libraries/Ucpaas.class.php');
                
                $options['accountsid'] = '5cbc351f17a418686094747a62ffd946';
                $options['token'] = 'fac1c2af0c05327677d33916ba841079';
                $ucpass = new Ucpaas($options);
                $appId = "9dc983c028e54d5fbc97228e6af5344e";
                $templateId = "174333";
                
                $result_json = $ucpass->templateSMS($appId, $phone, $templateId, $code);
                $result_obj = json_decode($result_json);
                
                if ($result_obj && $result_obj->resp->respCode == "000000") {
                    $sms_success = true;
                    $sms_provider = 'ucpaas';
                    $sms_msg = '云之讯短信发送成功';
                    error_log("云之讯短信发送成功: $phone, $code");
                } else {
                    $error_code = $result_obj ? $result_obj->resp->respCode : 'UNKNOWN';
                    error_log("云之讯短信发送失败: $error_code");
                    $sms_msg = '云之讯短信发送失败: ' . $error_code;
                }
                
            } catch (Exception $e) {
                error_log("云之讯短信异常: " . $e->getMessage());
                $sms_msg = '云之讯短信异常: ' . $e->getMessage();
            }
        }
        
        // 无论短信是否发送成功，都保存验证码到数据库
        // 这样即使短信服务异常，也可以通过其他方式（如客服）提供验证码
        $data = array(
            'role_id' => $role_id,
            'game_id' => $this->_channel['game_id'],
            'create_time' => time(),
            'phone' => $phone,
            'type' => $type,
            'code' => $code,
            'sms_status' => $sms_success ? 1 : 0,
            'sms_provider' => $sms_provider,
            'sms_msg' => $sms_msg
        );
        
        $this->db->insert('tuo3_verify_code', $data);
        
        if ($sms_success) {
            $this->response(array('code' => 0, 'msg' => '验证码发送成功'));
        } else {
            // 即使短信发送失败，也可以返回成功状态，避免用户重复请求
            // 同时提示用户联系客服
            $this->response(array('code' => 0, 'msg' => '验证码发送中，如未收到请联系客服'));
        }
    }
}

?>

<!-- 
数据库表结构修改:

ALTER TABLE tuo3_verify_code ADD COLUMN sms_status TINYINT DEFAULT 0 COMMENT '短信发送状态 0失败 1成功';
ALTER TABLE tuo3_verify_code ADD COLUMN sms_provider VARCHAR(50) DEFAULT '' COMMENT '短信服务商';
ALTER TABLE tuo3_verify_code ADD COLUMN sms_msg VARCHAR(255) DEFAULT '' COMMENT '短信发送结果信息';

使用说明:

1. 申请腾讯云短信服务:
   - 登录腾讯云控制台 https://console.cloud.tencent.com/
   - 搜索"短信"，开通短信服务
   - 创建应用，获取 AppID
   - 创建签名和模板，获取 TemplateID
   - 在访问管理中创建 SecretId 和 SecretKey

2. 修改配置:
   - 将上面代码中的 YOUR_TENCENT_SECRET_ID 等替换为实际值
   - 确保模板格式正确，通常为: "您的验证码是{1}，请在10分钟内使用"

3. 测试:
   - 先测试腾讯云短信是否正常
   - 再测试整个验证流程

4. 监控:
   - 查看数据库中的 sms_status 字段
   - 查看服务器错误日志
   - 监控短信发送成功率

优势:
- 双重保障，提高短信发送成功率
- 详细的日志记录，便于问题排查
- 即使短信失败也能保存验证码
- 支持发送频率限制
-->

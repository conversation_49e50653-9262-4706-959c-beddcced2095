-------------------------------------------------------------------------------
--  创世版1.0
--  背包
--  @date 2018-03-06
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')

local TAB_COUNT = 4             -- tab类型最大

local ITEM_TYPE_USE     = 3     -- 使用
local ITEM_TYPE_POST    = 2     -- 填写地址
local ITEM_TYPE_VIEW    = 1     -- 查看


local BagLayer = class("BagLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function BagLayer:ctor()
    self:enableNodeEvents()
    self.m_items = {}

    -- 载入主UI
    local main_node = helper.app.loadCSB('BagLayer.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('item_template'):hide()
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    
    self.panel_detail = self.main_node:child('panel_detail'):hide()
    self.panel_detail:addTouchEventListener( handler(self, self.onDetailMaskClick) )
    self.panel_detail:child('bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.hideDetail) )

    self:initTabs()

    if table.nums(GlobalUserItem.tItemConfigs) == 0 then    -- tItemConfigs是key、value结构，不能用#
        self:requestItemConfigs()
    else
        self:requestItems()
    end
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function BagLayer:onEnterTransitionFinish()
    print('BagLayer:onEnterTransitionFinish...')
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_CONFIG_ITEM_LIST, self, self.onConfigListResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_LIST, self, self.onListResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_USE, self, self.onItemUseResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_ADDRESS, self, self.onItemPostResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_ADD_LIST, self, self.onItemAddNotify)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function BagLayer:onExit()
    print('BagLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 初始化Tab列表
-------------------------------------------------------------------------------
function BagLayer:initTabs()
    local tabs = {}
    local main_node = self.main_node
    for i = 0, TAB_COUNT do
        local tab = main_node:child('tab_' .. i)
        tab.tab_type = i
        tab:child('bg'):show():py(-tab:child('bg'):size().height)
        tab:addTouchEventListener( helper.app.tintClickHandler(self, self.onTabClick) )
        table.insert(tabs, tab)
    end
    self.m_tabs = tabs
end


-------------------------------------------------------------------------------
-- Tab点击
-------------------------------------------------------------------------------
function BagLayer:onTabClick(sender)
    if self.m_cur_tab == sender.tab_type then return end

    for i, tab in ipairs(self.m_tabs) do
        local bg = tab:child('bg')
        if tab == sender then
            bg:stop():runAction(cc.MoveTo:create(0.1, cc.p(bg:px(), 0)))
        else
            bg:stop():runAction(cc.MoveTo:create(0.1, cc.p(bg:px(), -bg:size().height)))
        end
    end
    self.m_cur_tab = sender.tab_type
    self:createItems(sender.tab_type)
end


-------------------------------------------------------------------------------
-- 请求道具配置列表
-------------------------------------------------------------------------------
function BagLayer:requestItemConfigs()
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_CONFIG_ITEM_LIST)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 配置列表返回（可能分批返回，多次触发）
-------------------------------------------------------------------------------
function BagLayer:onConfigListResp(data)
    local items = LiveFrame:getInstance():resp(data, cmd_common.tagConfigItem, true)
    if not items then return false end
    if #items == 0 then
        self:requestItems()
    else
        for i, v in ipairs(items) do
            GlobalUserItem.tItemConfigs[v.dwItemID] = v
        end
    end
end


-------------------------------------------------------------------------------
-- 请求道具列表
-------------------------------------------------------------------------------
function BagLayer:requestItems()
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_LIST)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 列表返回（可能分批返回，多次触发）
-------------------------------------------------------------------------------
function BagLayer:onListResp(data)
    local items = LiveFrame:getInstance():resp(data, cmd_common.tagItem, true)
    if not items then return false end

    if #items == 0 then
        self:onTabClick(self.m_tabs[1])
    else
        for i, v in ipairs(items) do
            local config = GlobalUserItem.tItemConfigs[v.dwItemID]
            if config then
                setmetatable(v, {__index = config})
            end
            table.insert(self.m_items, v)
        end
    end
end


-------------------------------------------------------------------------------
-- 创建项目
-------------------------------------------------------------------------------
function BagLayer:addTimeout(label)
    label:setString('')
    label:perform(function(sender)
        local num = sender:getParent().item.nOverDueSeconds
        if num == 0 then
            sender:stop()
            sender:setString(LANG.ITEM_OVER_DUE)
            sender:setTextColor(cc.c3b(255, 0, 0))
        else
            local str = helper.time.intToCountDown(num)
            sender:setString(str)
            sender:getParent().item.nOverDueSeconds = math.max(0, num - 1)
        end
    end, 1, -1, nil, true)
end


-------------------------------------------------------------------------------
-- 下载图标
-------------------------------------------------------------------------------
function BagLayer:downloadIcon(node, item, event_handler, cache_table)
    node:child('img_icon'):hide()
    if not item.szIcon or item.szIcon == '' then return end

    local function add(node, image)
        image:pos(node:child('img_icon'):pos()):addTo(node)
        node:child('img_icon'):removeFromParent()
        image:setName('img_icon')
        if event_handler then
            image:setTouchEnabled(true)
            image:addTouchEventListener( event_handler )
        end
    end
    helper.app.addURLImage(self, cache_table or {}, 'itemicon', item.szIcon, node, add)
end


-------------------------------------------------------------------------------
-- 创建项目
-------------------------------------------------------------------------------
function BagLayer:createItems(tab_type)
    self.m_file_images = {}
    local listview = self.main_node:child('listview')
    local template = self.main_node:child('item_template')
    local l_size = listview:size()
    local i_size = template:size()
    listview:removeAllItems()

    local col_count, col_gap = 4, 10
    local row, i = nil, 0
    for _, item in ipairs(self.m_items) do repeat
        if tab_type ~= 0 and item.cbTabType ~= tab_type then break end    -- 类型过滤
        
        i = i + 1
        if i % col_count == 1 then
            row = ccui.Layout:create()
            row:size(i_size.width * col_count + (col_count - 1) * col_gap, i_size.height)
            listview:pushBackCustomItem(row)
        end
        node = template:clone():show()
        node.item = item
        node:child('label_name'):setString(item.szName)
        node:child('label_num'):setString('x' .. item.nNum)
        if item.nOverDueSeconds > 0 then
            self:addTimeout(node:child('label_num'), item.nOverDueSeconds)
        end
        if item.cbItemType == ITEM_TYPE_VIEW then
            node:child('btn_act/label'):setString( LANG.ITEM_TYPE_VIEW )
        end
        node:child('img_icon'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnItemIcon) )
        node:child('btn_act'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnItemAct) )
        node:child('btn_act'):setCascadeColorEnabled(true)

        local col = (i - 1) % col_count + 1
        node:pos((col - 1) * (i_size.width + col_gap), 0)
        node:addTo(row)

        self:downloadIcon(node, item, helper.app.tintClickHandler(self, self.onBtnItemIcon), self.m_file_images)
    until true end

    listview:jumpToTop()
end


-------------------------------------------------------------------------------
-- 隐藏详情
-------------------------------------------------------------------------------
function BagLayer:hideDetail(sender, event)
    self.panel_detail:hide()
end


-------------------------------------------------------------------------------
-- 详情蒙板点击
-------------------------------------------------------------------------------
function BagLayer:onDetailMaskClick(sender, event)
    if event == cc.EventCode.ENDED then
        self.panel_detail:hide()
    end
end


-------------------------------------------------------------------------------
-- 道具图标点击
-------------------------------------------------------------------------------
function BagLayer:onBtnItemIcon(sender)
    self:showDetail(sender:getParent().item)
end


-------------------------------------------------------------------------------
-- 查看道具详情
-------------------------------------------------------------------------------
function BagLayer:showDetail(item)
    local bg = self.panel_detail:child('bg')
    bg:child('label_name'):setString(item.szName)
    bg:child('label_type'):setString(LANG['ITEM_TYPE_' .. item.cbItemType])
    self:downloadIcon(bg, item)

    if item.nOverDueSeconds >= 0 then
        if item.nOverDueSeconds == 0 then
            bg:child('label_tip'):setString(LANG.ITEM_OVER_DUE)
        else
            local str = helper.time.intToCountDown(item.nOverDueSeconds)
            bg:child('label_tip'):setString(LANG{'ITEM_OVER_DUE_LEFT', time=str})
        end
    else
        bg:child('label_tip'):setString(LANG{'ITEM_HAS_NUM', num=item.nNum})
    end
    bg:child('label_desc'):setString(item.szDesc)
    self.panel_detail:show()
end


-------------------------------------------------------------------------------
-- 道具动作按钮点击
-------------------------------------------------------------------------------
function BagLayer:onBtnItemAct(sender)
    -- 按类型调用UI
    local item = sender:getParent().item
    local item_type = item.cbItemType
    if item_type == ITEM_TYPE_VIEW then
        self:showDetail(item)
    elseif item_type == ITEM_TYPE_POST then
        local path = cs.app.CLIENT_SRC .. 'main.BagPostLayer'
        local callback = function(infos) self:sendItemPost(item, infos) end
        helper.pop.popLayer(path, nil, {callback}, nil, true)
    else
        if item.nNum > 1 then
            helper.pop.numSelect(1, item.nNum, LANG{'ITEM_HAS_NUM', num=item.nNum}, function(num)
                self:sendItemUse(item, num)
            end)
        else
            helper.pop.alert(LANG.ITEM_USE_CONFIRM, function()
                self:sendItemUse(item, 1)
            end, true)
        end
    end
end


-------------------------------------------------------------------------------
-- 发送道具使用请求
-------------------------------------------------------------------------------
function BagLayer:sendItemUse(item, use_num)
    item.use_num = use_num    -- 保存一下先
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_LLIDValue, {llID = item.llID, nValue = use_num} )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_USE)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 使用返回
-------------------------------------------------------------------------------
function BagLayer:onItemUseResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_ItemUseResult)
    if not ret then return false end

    -- 判断是否成功
    if ret.nError > 0 then
        helper.pop.message(ret.szMsg)
    else
        for i, item in ipairs(self.m_items) do
            if item.llID == ret.llID and item.use_num then
                if item.nNum == item.use_num then
                    table.remove(self.m_items, i)
                else
                    item.nNum = item.nNum - item.use_num
                end
                self:createItems(self.m_cur_tab)
                break
            end
        end

        if ret.dwItemID == cs.app.ITEM_GOLD_PIG or ret.dwItemID == cs.app.ITEM_GOLD_PIG_PRO then
            local items = {}
            local rewards = ret.nRewardIDNums[1]
            for i = 1, #rewards, 2 do
                local id = rewards[i]
                local num = rewards[i + 1]
                if id > 0 then
                    items[#items + 1] = {id = id, num = num}
                else
                    break
                end
            end
            self:showGoldPig(items)
        else
            helper.pop.message(LANG.ITEM_USE_SUCC)
        end
    end
end


-------------------------------------------------------------------------------
-- 发送道具寄件地址请求
-------------------------------------------------------------------------------
function BagLayer:showGoldPig(items)
    -- for test: items = {{id = 45, num = 1}, {id = 46, num = 2}, {id = 45, num = 1}, {id = 46, num = 2}, {id = 47, num = 3}, {id = 45, num = 1}, {id = 46, num = 2}, {id = 47, num = 3}, {id = 45, num = 1}, {id = 46, num = 2}, {id = 47, num = 3}}
    local path = cs.app.CLIENT_SRC .. 'main.BagGoldPig'
    helper.pop.popLayer(path, nil, {items}, nil, true)
end


-------------------------------------------------------------------------------
-- 发送道具寄件地址请求
-------------------------------------------------------------------------------
function BagLayer:sendItemPost(item, infos)
    local t = {
        llID = item.llID,
        szAddress = infos.address,
        szName = infos.name,
        szPhoneNo = infos.phone,
    }
	local cmd_data = ExternalFun.create_netdata( cmd_common.tagItemAddress, t )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_ITEM_ADDRESS)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 地址返回
-------------------------------------------------------------------------------
function BagLayer:onItemPostResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_LLIDValueMsg)
    if not ret then return false end

    -- 判断是否成功
    if ret.nValue > 0 then
        helper.pop.message(ret.szMsg)
    else
        helper.pop.message(LANG.ITEM_POST_SUCC)

        -- 本类道具不堆叠，直接删除
        for i, item in ipairs(self.m_items) do
            if item.llID == ret.llID then
                table.remove(self.m_items, i)
                self:createItems(self.m_cur_tab)
                break
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 新物品增加推送
-------------------------------------------------------------------------------
function BagLayer:onItemAddNotify(data)
    local items = LiveFrame:getInstance():resp(data, cmd_common.tagItem, true)
    if not items then return false end

    local id_map = {}
    for i, item in ipairs(self.m_items) do
        id_map[item.llID] = item
    end

    -- 唯一ID已存在则覆盖数量，否则新增项目
    for i, item in ipairs(items) do
        if id_map[item.llID] then
            id_map[item.llID].nNum = item.nNum
            print('set item num:', item.nNum)
        else
            id_map[item.llID] = item
            local config = GlobalUserItem.tItemConfigs[item.dwItemID]
            if config then
                setmetatable(item, {__index = config})
            end
            print('insert new item...')
            table.insert(self.m_items, item)
        end
    end
    print('self.m_cur_tab:', self.m_cur_tab)
    self:createItems(self.m_cur_tab)
end


return BagLayer
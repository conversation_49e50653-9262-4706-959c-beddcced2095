# LHMJ313 网络通信文档

## 网络通信架构概述

LHMJ313 项目采用自定义的 TCP Socket 通信协议，支持加密传输、心跳保活、断线重连等功能。网络通信架构分为多个层次，从底层的 Socket 封装到上层的业务逻辑处理。

```
┌─────────────────────────────────────────┐
│            业务逻辑层                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  游戏逻辑   │  │   房间管理      │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            框架层 (Frame)                │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  RoomFrame  │  │   LogonFrame    │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            封装层 (Wrapper)              │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  BaseFrame  │  │  ClientSocket   │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            内核层 (Kernel)               │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ SocketService│  │   TCPSocket     │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 网络协议设计

### 1. 数据包结构

#### TCP 包头结构
```cpp
// 网络内核信息
struct TCP_Info {
    BYTE cbDataKind;        // 数据类型 (映射/加密/压缩)
    BYTE cbCheckCode;       // 校验字段
    WORD wPacketSize;       // 数据大小
};

// 网络命令
struct TCP_Command {
    WORD wMainCmdID;        // 主命令码
    WORD wSubCmdID;         // 子命令码
};

// 网络包头
struct TCP_Head {
    TCP_Info TCPInfo;       // 基础结构
    TCP_Command CommandInfo; // 命令信息
};

// 网络缓冲
struct TCP_Buffer {
    TCP_Head Head;          // 数据包头
    BYTE cbBuffer[SOCKET_TCP_PACKET]; // 数据缓冲
};
```

#### 数据类型定义
```cpp
#define DK_MAPPED     0x01  // 映射类型
#define DK_ENCRYPT    0x02  // 加密类型
#define DK_COMPRESS   0x04  // 压缩类型
```

#### 缓冲区大小
```cpp
#define SOCKET_TCP_BUFFER  16384  // 网络缓冲区大小
#define SOCKET_TCP_PACKET  (SOCKET_TCP_BUFFER-sizeof(TCP_Head)) // 有效数据大小
```

### 2. 消息结构

#### 消息定义
```cpp
struct CMessage {
    int nHandler;           // 处理器标识
    unsigned int wType;     // 消息类型
    unsigned int wMain;     // 主命令
    unsigned int wSub;      // 子命令
    unsigned int wSize;     // 数据大小
    unsigned char* pData;   // 数据指针
};
```

#### 消息处理接口
```cpp
interface IMsgHandler {
    virtual bool HanderMessage(
        unsigned short wType,
        int nHandler,
        unsigned short wMain,
        unsigned short wSub,
        unsigned char *pBuffer = nullptr,
        unsigned short wSize = 0
    ) = 0;
};
```

## 网络通信层次

### 1. 底层 Socket 实现 (TCPSocket)

#### 主要功能
- 原生 TCP Socket 封装
- 跨平台支持 (Windows/Linux/iOS/Android)
- 非阻塞 I/O 模式
- 连接管理

```cpp
class CTCPSocket {
private:
    SOCKET m_sock;          // Socket 句柄
    fd_set fdR;             // 读取文件描述符集
    addrinfo *server_addr;  // 服务器地址信息
    
public:
    bool OnEventSocketCreate(int af = AF_INET, int type = SOCK_STREAM, int protocol = 0);
    bool OnEventSocketConnet(const char *ip, WORD port);
    bool OnEventSocketSend(const char *buf, int len, int flags);
    int OnEventSocketRecv(char *buf, int len, int flags);
    int OnEventSocketClose();
};
```

#### 连接建立
```cpp
// 创建 Socket
bool OnEventSocketCreate(int af, int type, int protocol);

// 连接服务器 (IP 地址)
bool OnEventSocketConnetByIP(const char *ip, WORD port);

// 连接服务器 (域名)
bool OnEventSocketConnetByDomain(const char *domain, WORD port);
```

### 2. Socket 服务层 (SocketService)

#### 主要功能
- Socket 生命周期管理
- 数据加密解密
- 心跳保活机制
- 消息队列处理

```cpp
class CSocketService : public IObj, public ISocketServer {
private:
    bool m_bServe;              // 服务状态
    bool m_bRun;                // 运行状态
    char m_szUrl[256];          // 服务器地址
    WORD m_wPort;               // 服务器端口
    bool m_bHeartBeatKeep;      // 心跳保活
    CTCPSocket *m_pSocket;      // TCP Socket 实例
    
public:
    virtual bool Connect(const char* szUrl, unsigned short wPort, unsigned char* pValidate = nullptr);
    virtual bool SendSocketData(unsigned short wMain, unsigned short wSub, const void* pData = nullptr, unsigned short wDataSize = 0);
    virtual void SetHeartBeatKeep(bool bKeep);
};
```

#### 数据发送
```cpp
bool CSocketService::SendSocketData(unsigned short wMain, unsigned short wSub, const void* pData, unsigned short wDataSize) {
    TCP_Buffer TcpBuffer;
    
    // 构建数据包
    TcpBuffer.Head.CommandInfo.wMainCmdID = wMain;
    TcpBuffer.Head.CommandInfo.wSubCmdID = wSub;
    TcpBuffer.Head.TCPInfo.wPacketSize = wDataSize + sizeof(TCP_Head);
    
    if (pData && wDataSize) {
        memcpy(&TcpBuffer.cbBuffer, pData, wDataSize);
    }
    
    // 加密数据
    CCipher::encryptBuffer(&TcpBuffer, (wDataSize + sizeof(TCP_Head)));
    
    // 发送数据
    return m_pSocket->OnEventSocketSend((const char *)&TcpBuffer, TcpBuffer.Head.TCPInfo.wPacketSize, 0);
}
```

### 3. 客户端 Socket 封装 (ClientSocket)

#### Lua 绑定接口
```cpp
class CClientSocket : public cocos2d::Node, public ISocketServer {
private:
    ISocketServer* pISocketServer;  // Socket 服务实例
    
public:
    // 连接服务器
    virtual bool Connect(const char* szUrl, unsigned short wPort, unsigned char* pValidate = nullptr);
    
    // 发送数据
    virtual bool SendSocketData(unsigned short wMain, unsigned short wSub, const void* pData = nullptr, unsigned short wDataSize = 0);
    
    // 发送命令数据
    int sendData(CCmd_Data* pData);
    
    // 设置心跳保活
    virtual void SetHeartBeatKeep(bool bKeep);
};
```

### 4. 基础框架层 (BaseFrame)

#### Lua 网络框架
```lua
local BaseFrame = class("BaseFrame")

-- 创建 Socket 连接
function BaseFrame:onCreateSocket(szUrl, nPort)
    if self._socket ~= nil then
        return false
    end
    
    -- 创建连接
    self._szServerUrl = szUrl 
    self._nServerPort = nPort
    self._SocketFun = function(pData)
        self:onSocketCallBack(pData)
    end
    
    self._socket = CClientSocket:createSocket(self._SocketFun)
    self._socket:setwaittime(0)
    
    if self.m_keep_heart_beat then
        self._socket:setheartbeatkeep(true)
    end
    
    if self._socket:connectSocket(self._szServerUrl, self._nServerPort, yl.VALIDATE) then
        self._threadid = 0
        return true
    else
        self:onCloseSocket() 
        return false
    end
end
```

#### 数据发送
```lua
function BaseFrame:sendSocketData(pData)
    if self._socket == nil then
        self._callBack(-1)
        return false
    end
    
    -- 缓存消息用于调试
    local tabCache = {}
    tabCache["main"] = pData:getmain()
    tabCache["sub"] = pData:getsub()
    tabCache["len"] = pData:getlen()
    tabCache["kindid"] = GlobalUserItem.nCurGameKind
    table.insert(self.m_tabCacheMsg, tabCache)
    
    if #self.m_tabCacheMsg > 5 then
        table.remove(self.m_tabCacheMsg, 1)
    end
    
    print('发送数据:', 'main', pData:getmain(), 'sub', pData:getsub())
    
    if not self._socket:sendData(pData) then
        self:onSocketError("发送数据失败！")
        return false
    end
    
    return true
end
```

## 业务框架层

### 1. 房间框架 (RoomFrame)

#### 主要功能
- 游戏房间网络管理
- 游戏状态同步
- 玩家数据管理
- 游戏消息处理

```lua
local RoomFrame = class("RoomFrame", BaseFrame)

-- 初始化房间数据
function RoomFrame:onInitData()
    -- 重置房间状态
    self.m_bGameStarted = false
    self.m_bGameEnded = false
    
    -- 清空玩家数据
    for i = 1, yl.MAX_CHAIR do
        self.m_tabUserItem[i] = {}
    end
end

-- 登录房间
function RoomFrame:onLogonRoom()
    local cmddata = CCmd_Data:create(0, 0)
    cmddata:setcmdinfo(yl.MDM_GR_LOGON, yl.SUB_GR_LOGON_USERID)
    
    -- 构建登录数据
    local logonData = {
        dwUserID = GlobalUserItem.dwUserID,
        szPassword = GlobalUserItem.szPassword,
        -- ... 其他登录信息
    }
    
    cmddata:pushstring(cjson.encode(logonData))
    self:sendSocketData(cmddata)
end
```

### 2. 登录框架 (LogonFrame)

#### 主要功能
- 用户登录验证
- 服务器列表管理
- 账号系统集成

```lua
local LogonFrame = class("LogonFrame", BaseFrame)

-- 用户登录
function LogonFrame:onUserLogon(loginType, account, password)
    local cmddata = CCmd_Data:create(0, 0)
    cmddata:setcmdinfo(yl.MDM_MB_LOGON, yl.SUB_MB_LOGON_ACCOUNTS)
    
    local logonData = {
        cbDeviceType = yl.DEVICE_TYPE_ANDROID,
        wBehaviorFlags = 0,
        wPageTableCount = 0,
        szLogonPass = password,
        szAccounts = account,
        -- ... 其他登录参数
    }
    
    cmddata:pushstring(cjson.encode(logonData))
    self:sendSocketData(cmddata)
end
```

## 消息处理机制

### 1. 命令数据封装 (CMD_Data)

#### 数据结构
```cpp
class CCmd_Data {
private:
    unsigned short m_wMainCmd;  // 主命令
    unsigned short m_wSubCmd;   // 子命令
    unsigned char* m_pBuffer;   // 数据缓冲
    unsigned short m_wDataSize; // 数据大小
    
public:
    void setcmdinfo(unsigned short wMainCmd, unsigned short wSubCmd);
    void pushstring(const char* str);
    void pushbyte(unsigned char value);
    void pushword(unsigned short value);
    void pushdword(unsigned int value);
};
```

### 2. 消息回调处理

#### 回调函数
```lua
function BaseFrame:onSocketCallBack(pData)
    if pData == nil then
        return
    end
    
    local main = pData:getmain()
    local sub = pData:getsub()
    local len = pData:getlen()
    
    print('接收数据:', 'main', main, 'sub', sub, 'len', len)
    
    -- 分发消息到具体处理函数
    self:onEventTCPSocketRead(main, sub, pData)
end
```

## 安全机制

### 1. 数据加密

#### 加密算法
- 使用自定义加密算法
- 支持数据完整性校验
- 防止数据篡改

```cpp
// 加密数据缓冲
CCipher::encryptBuffer(&TcpBuffer, dataSize);

// 解密数据缓冲
CCipher::decryptBuffer(&TcpBuffer, dataSize);
```

### 2. 连接验证

#### 验证机制
```cpp
struct TCP_Validate {
    TCHAR szValidateKey[64];  // 验证字符串
};
```

### 3. 心跳保活

#### 心跳机制
- 定时发送心跳包
- 检测连接状态
- 自动断线重连

```lua
-- 启用心跳保活
self._socket:setheartbeatkeep(true)

-- 设置心跳间隔
self._socket:setDelayTime(30000)  -- 30秒
```

## 错误处理

### 1. 连接错误

#### 错误类型
```cpp
enum SocketServiceRunErrorCode {
    sser_NoError,                           // 无错误
    sser_SocketCreateFailed,                // Socket 创建失败
    sser_SocketConnectFailed,               // 连接失败
    sser_SocketSendValidatePackageFailed,   // 验证包发送失败
    sser_SocketSendHeartBeatPackageFailed,  // 心跳包发送失败
};
```

### 2. 断线重连

#### 重连机制
```lua
function BaseFrame:onSocketError(errorMsg)
    print("网络错误:", errorMsg)
    
    -- 关闭当前连接
    self:onCloseSocket()
    
    -- 尝试重新连接
    if self.m_auto_reconnect then
        self:perform(function()
            self:onCreateSocket(self._szServerUrl, self._nServerPort)
        end, 3.0)  -- 3秒后重连
    end
end
```

## 性能优化

### 1. 消息队列

#### 队列管理
- 异步消息处理
- 消息优先级
- 流量控制

### 2. 连接池

#### 连接复用
- 减少连接建立开销
- 提高网络利用率
- 支持并发连接

### 3. 数据压缩

#### 压缩算法
- 减少网络传输量
- 提高传输效率
- 可配置压缩级别

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

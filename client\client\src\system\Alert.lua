-------------------------------------------------------------------------------
--  创世版1.0
--  通用弹出对话框
--  @date 2017-06-09
--  @auth woodoo
-------------------------------------------------------------------------------
local PopupMask	= cs.app.client('system.PopupMask')


local Alert = class('Alert', PopupMask)



-------------------------------------------------------------------------------
--[[
eg:
    Alert.new('...')
    Alert.new({'title', 'content'})
    Alert.new({title_node, 'content'})
    Alert.new('...', handler(self, self.onOk))
    Alert.new('...', handler(self, self.onOk), handler(self, self.onCancel))
    Alert.new('...', {'购买', handler(self, self.onOk)})
    Alert.new('...', {'使用', handler(self, self.onOk)}, {'丢弃', handler(self, self.onCancel)})
--]]
-------------------------------------------------------------------------------
function Alert:ctor(msg, button1, button2, input_default)
    self.super.ctor(self)
    self:zorder(2)  -- 因为PopupMask的默认zorder是负的
    self.is_input = input_default ~= nil

    local main_node = helper.app.loadCSB(self.is_input and 'InputLayer.csb' or 'AlertLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    
    if type(msg) == 'table' then
        local label_title = main_node:child('img_bg/label_title')
        if type(msg[1]) == 'string' then
            label_title:setRich(msg[1])
        else
            label_title:setString('')
            label_title:addChild(msg[1])
        end
        msg = msg[2]
    end

    if self.is_input then
        local editor = cs.app.client('system.EditBox').convertTextField( main_node:child('img_bg/input'), 'common/bg_transparent.png', ccui.TextureResType.localType)
        editor:setPlaceholderFontColor(cc.c3b(220, 220, 220))
        editor:setString(input_default)
    end

    self.m_msg = msg
    self.m_origin_content_size = main_node:child('img_bg/label_content'):size()
    main_node:child('img_bg/label_content'):setString('')
    self:setContent(msg)

    main_node:child('img_bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClick) )
    main_node:child('img_bg/btn_close')._index = button2 and 2 or 1

    self:initButtons(button1, button2)

    main_node:anchor(0.5, 0.5):pos(self:size().width/2, self:size().height/2):setScale(0.8)
    main_node:runMyAction(cc.EaseBackOut:create(cc.ScaleTo:create(0.2, 1)))
end


-------------------------------------------------------------------------------
-- 设置内容
-------------------------------------------------------------------------------
function Alert:setContent(msg)
    local label_content = self.main_node:child('img_bg/label_content')
    label_content:removeAllChildren()
    local size = self.m_origin_content_size
    local rich = cs.app.client('system.RichText').new(msg, label_content:getFontSize(), nil, cs.app.FONT_NAME, {['='] = label_content:getTextColor()})
    if rich.is_multi_line or rich:size().width > size.width or rich:size().height > size.height then
        rich = cs.app.client('system.RichText').new(msg, label_content:getFontSize(), 
                cc.size(size.width, 0), cs.app.FONT_NAME, {['='] = label_content:getTextColor()})
    end
    rich:anchor(label_content:anchor()):pos(label_content:getAnchorPointInPoints()):addTo(label_content)
end


-------------------------------------------------------------------------------
-- 获取按钮
-------------------------------------------------------------------------------
function Alert:getButtons()
    return self.main_node:child('img_bg/btn_1, img_bg/btn_2')
end


-------------------------------------------------------------------------------
-- 初始化按钮
-------------------------------------------------------------------------------
function Alert:initButtons(button1, button2)
    local btn_1, btn_2 = self:getButtons()

    -- 按钮1，如果是table，认为第一个元素时名称，后面是handler，否则认为整个是handler，使用默认名称
    if button1 then
        if type(button1) == 'table' then
            if type(button1[1]) == 'string' then
                btn_1:child('text'):setString(button1[1])
            elseif type(button1[1]) == 'function' then
                button1[1](btn_1)
            end
            self.handler1 = button1[2]
            btn_1._dont_close = button1[3]	-- true：表示点击后不关闭，特殊地方使用
        else
            self.handler1 = button1
        end
        btn_1._index = 1
    else
        btn_1._index = 0
    end
    btn_1:stroke():addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClick) )

    -- 按钮2，可以不存在，如果是table，认为第一个元素时名称，后面是handler，否则认为整个是handler，使用默认名称
    if button2 then
        if type(button2) == 'table' then
            btn_2:child('text'):setString(button2[1])
            self.handler2 = button2[2]
        else
            self.handler2 = button2
        end
        btn_2._index = 2
        btn_2:stroke():addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClick) )
    else
        btn_1:px(btn_1:px() + (btn_2:px() - btn_1:px()) / 2)	-- 按钮1移到中间
        btn_2:removeFromParent()
    end
end


-------------------------------------------------------------------------------
-- 按钮点击
-------------------------------------------------------------------------------
function Alert:onBtnClick(sender, event)
    local input_text = nil
    if self.is_input then
        input_text = self.main_node:child('img_bg/input'):getString()
    end
    local handler = self['handler' .. sender._index]
    if not sender._dont_close then
        self:removeFromParent()
    end
    if type(handler) == 'function' then
        handler(input_text)
    end
end


-------------------------------------------------------------------------------
-- 改变大小
-------------------------------------------------------------------------------
function Alert:resize(width, height, text_width)
    local bg = self.main_node:child('img_bg')
    local old_size = bg:size()
    local xoffset, yoffset = width - old_size.width, height - old_size.height
    bg:size(width, height)
    local label_title, label_content, btn_close, btn_1, btn_2 = bg:child('label_title, label_content, btn_close, btn_1, btn_2')
    label_title:pos(width / 2, label_title:py() + yoffset)
    label_content:size(text_width or (self.m_origin_content_size.width + xoffset), 0)
    label_content:pos(width / 2, label_content:py() + yoffset / 2)
    if self.is_input then
        bg:child('input'):pos(width / 2, bg:child('input'):py() + yoffset / 2)
        bg:child('bg_input'):pos(width / 2, bg:child('bg_input'):py() + yoffset / 2)
    end
    self.m_origin_content_size.width = text_width or (self.m_origin_content_size.width + xoffset)
    self:setContent(self.m_msg)
    btn_close:pos(btn_close:px() + xoffset, btn_close:py() + yoffset)
    if not btn_2 then
        btn_1:px(width / 2)
    else
        btn_1:px(width / 2 - btn_1:size().width/2 - 15)
        btn_2:px(width / 2 + btn_2:size().width/2 + 15)
    end
end


return Alert
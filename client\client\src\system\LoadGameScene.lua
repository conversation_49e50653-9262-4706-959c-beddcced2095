-------------------------------------------------------------------------------
--  创世版1.0
--  游戏切换场景
--  @date 2017-08-02
--  @auth woodoo
-------------------------------------------------------------------------------
local LoadGameScene = class('LoadGameScene', cc.Scene)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function LoadGameScene:ctor(app, cur_game, new_game)
    print('LoadGameScene:ctor...')
    self.m_app = app
    self.m_cur_game = cur_game
    self.m_new_game = new_game
    self:enableNodeEvents()

    local label = ccui.Text:create('Loading...', cs.app.FONT_NAME, 32)
    helper.layout.addCenter(self, label)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function LoadGameScene:onEnterTransitionFinish()
    print('LoadGameScene:onEnterTransitionFinish...')

    self:perform( handler(self, self.doLoadGame), 0.1 )
end


-------------------------------------------------------------------------------
-- 载入
-------------------------------------------------------------------------------
function LoadGameScene:doLoadGame()
    if self.m_cur_game then
        helper.app.cleanPackages('game.' .. self.m_cur_game .. '.src.')
        cc.Director:getInstance():getTextureCache():removeUnusedTextures()
        cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
        helper.app.removeGameSearchPath(self.m_cur_game)
    end

    -- 载入初始化游戏
    cs.app.req(cs.app.GAME_ROOT .. '.' .. self.m_new_game .. '.src.init')
    self.m_app:enterSceneEx(cs.game.SRC .. 'MainScene', 'FADE', 1)
end


return LoadGameScene
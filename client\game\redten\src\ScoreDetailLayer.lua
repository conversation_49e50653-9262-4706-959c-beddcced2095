-------------------------------------------------------------------------------
--  创世版1.0
--  战绩详情
--      父类：ScoreDetailBase
--      目前考虑到该功能比较通用，因此主要逻辑都写在父类中
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local ScoreDetailBase = cs.app.client('system.ScoreDetailBase')
local ScoreDetailLayer = class('ScoreDetailLayer', ScoreDetailBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ScoreDetailLayer:ctor(record, close_callback)
    self.super.ctor(self, record, close_callback)
end


return ScoreDetailLayer
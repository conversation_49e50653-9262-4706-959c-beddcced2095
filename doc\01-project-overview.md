# LHMJ313 项目概述

## 项目简介

LHMJ313 是一个基于 Cocos2d-x 3.13 引擎开发的多人在线棋牌游戏平台，主要专注于麻将类游戏。项目采用 Lua 脚本语言进行游戏逻辑开发，支持 Android、iOS、Windows 等多个平台。

## 项目特点

### 技术特点
- **跨平台支持**: 基于 Cocos2d-x 引擎，支持 Android、iOS、Windows、Linux 等多个平台
- **脚本化开发**: 使用 Lua 脚本进行游戏逻辑开发，便于快速迭代和热更新
- **模块化架构**: 采用模块化设计，游戏模块可独立开发和部署
- **网络通信**: 自定义网络通信协议，支持实时多人在线游戏

### 游戏特点
- **多种游戏类型**: 支持麻将、斗地主、四副牌、十三水等多种棋牌游戏
- **地方特色**: 针对不同地区定制游戏规则（如临海麻将、大盘山麻将等）
- **社交功能**: 集成微信分享、房间系统、好友邀请等社交功能
- **实时对战**: 支持多人实时在线对战

## 项目结构

```
lhmj313/
├── client/                 # 客户端代码
│   ├── client/            # 主客户端代码
│   ├── game/              # 游戏模块
│   └── package/           # 不同版本的打包配置
├── frameworks/            # Cocos2d-x 框架
│   ├── cocos2d-x/        # Cocos2d-x 引擎源码
│   └── runtime-src/      # 原生代码（C++/Java/OC）
├── src/                   # 基础源码
├── res/                   # 资源文件
├── simulator/             # 模拟器相关
├── runtime/               # 运行时文件
├── publish/               # 发布文件
├── client_publish/        # 客户端发布版本
└── info/                  # 工具和文档
```

## 支持的游戏类型

### 麻将类游戏
- **三门麻将**: 传统麻将玩法
- **临海麻将**: 浙江临海地区特色麻将
- **大盘山麻将**: 大盘山地区特色麻将
- **湖州麻将**: 湖州地区特色麻将
- **六安麻将**: 安徽六安地区特色麻将

### 扑克类游戏
- **斗地主**: 经典三人斗地主
- **四副牌**: 四人扑克游戏
- **红十**: 传统红十游戏
- **跑得快**: 快速出牌游戏
- **十三水**: 十三张牌型游戏

### 其他游戏
- **翻翻炸**: 特色炸金花游戏
- **牛牛**: 牛牛比大小游戏
- **双扣**: 双人扣牌游戏

## 技术栈

### 客户端技术
- **游戏引擎**: Cocos2d-x 3.13
- **脚本语言**: Lua 5.1
- **UI框架**: Cocos Studio + CSB
- **网络通信**: 自定义 TCP Socket 协议
- **音频**: Cocos2d-x 音频引擎

### 原生平台
- **Android**: Java + JNI，支持 API Level 10+
- **iOS**: Objective-C，支持 iOS 8.0+
- **Windows**: C++ Win32 API
- **Linux**: C++ + X11

### 构建工具
- **Android**: Gradle + Android Studio
- **iOS**: Xcode
- **跨平台**: CMake
- **打包工具**: Python 脚本 + Cocos Console

## 版本信息

- **引擎版本**: Cocos2d-x 3.13
- **项目类型**: Lua 项目
- **最低 Android 版本**: API Level 10 (Android 2.3.3)
- **最低 iOS 版本**: iOS 8.0
- **编译工具**: 
  - Android: Gradle 1.3.0, Build Tools 22.0.1
  - iOS: Xcode 7.0+

## 第三方集成

### 社交分享
- **微信 SDK**: 支持微信登录和分享
- **魔窗 SDK**: 深度链接和推广

### 推送服务
- **个推 SDK**: 消息推送服务

### 统计分析
- **友盟统计**: 用户行为分析

## 部署架构

项目支持多种部署方式：
- **企业版**: 面向企业客户的定制版本
- **应用商店版**: 面向应用商店发布的版本
- **渠道包**: 针对不同渠道的定制包

每个版本都有独立的配置文件，支持不同的服务器地址、微信 AppID、推送配置等。

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

local GameModel = cs.app.client('system.GameModel')
local GameLayer = class("GameLayer", GameModel)

local ExternalFun =  cs.app.client('external.ExternalFun')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')
local GameViewLayer = cs.app.game("room.GameViewLayer")
local GameResultLayer = cs.app.game("room.GameResultLayer")

function GameLayer:ctor( frameEngine,scene )        
    GameLayer.super.ctor(self, frameEngine, scene)
    self:OnInitGameEngine()
    self._roomRule = self._gameFrame._dwServerRule
    self.m_bLeaveGame = false    

    --[[
    self.isLaizi = true
    local testDatas = { 56, 24, 72, 72, 38, 6, 21, 5}
    self.m_tabLastCards = {}
    GameLogic:setLaiziCard(testDatas, 8, 0x07)
    self:compareWithLastCards(testDatas, 3)
    --local result = GameLogic:GetAllTypeCards(testDatas, 8, 0x07, 0, false)
    
    local testDatas = {0x4E}
    local tt = {0x4F, 0x02}
    local result = GameLogic:SearchOutCard(tt,2,testDatas,1)
    --]]
    --dump(result)
    print('test result is ... ')
    --dump(result)
    
    
    --进入房间点击准备按钮的状态，点击后变成false， 未点击按钮是true

    -- 一轮结束
    self.m_bRoundOver = false
    -- 自己是否是地主
    self.m_bIsMyBanker = false
    -- 地主座椅
    self.m_cbBankerChair = 0
    -- 地主视图ID
    self.m_cbBankerChairViewID = 1
    -- 提示牌数组
    self.m_tabPromptList = {}
    -- 当前出牌
    self.m_tabCurrentCards = {}
    -- 提示牌
    self.m_tabPromptCards = {}
    -- 比牌结果
    self.m_bLastCompareRes = false
    -- 上轮出牌视图
    self.m_nLastOutViewId = cmd.INVALID_VIEWID
    -- 上轮出牌
    self.m_tabLastCards = {}
    -- 是否洗牌
    self.isBuXiPai = false
    -- 是否癞子
    self.isLaizi = false
    -- 是否双人
    self.isTwo = false
    -- 癞子牌值
    self.cbLaiziCardData = 0x0
    -- 上一家出的牌的牌型
    self.preCardType = 0
    -- 是否叫分状态进入
    self.m_bCallStateEnter = false

    self.is_ready_btn_press_status = false

    self.wPeiyin = {0, 0, 0}

    print('1111111111111111111111 鬥地主')
end

--获取gamekind
function GameLayer:getGameKind()
    return cmd.KIND_ID
end

--创建场景
function GameLayer:CreateView()
    return GameViewLayer:create(self, GameLogic)
        :addTo(self)
end

function GameLayer:getParentNode( )
    return self._scene
end

function GameLayer:getFrame( )
    return self._gameFrame
end

function GameLayer:logData(msg)
    if nil ~= self._scene.logData then
        self._scene:logData(msg)
    end
end

function GameLayer:reSetData()
    self.m_bIsMyBanker = false
    self.m_tabPromptList = {}
    self.m_tabCurrentCards = {}
    self.m_tabPromptCards = {}
    self.m_bLastCompareRes = false
    self.m_nLastOutViewId = cmd.INVALID_VIEWID
    self.m_tabLastCards = {}    
end

---------------------------------------------------------------------------------------
------继承函数
function GameLayer:onEnterTransitionFinish()
    GameLayer.super.onEnterTransitionFinish(self)
end

function GameLayer:onExit()
    self:KillGameClock()
    self:dismissPopWait()
    GameLayer.super.onExit(self)
end

function GameLayer:showExitTip()
    local runScene = cc.Director:getInstance():getRunningScene()
    showToast(runScene, "准备时间太长，请重新进入房间准备", 3)
end

--退出桌子
function GameLayer:onExitTable()
    self:stopAllActions()
    self:KillGameClock()
    if self.is_ready_btn_press_status == true then
        self.is_ready_btn_press_status = false
        self:showExitTip()
    end
    local MeItem = self:GetMeUserItem()
    if MeItem and MeItem.cbUserStatus > yl.US_FREE then
        --[[
            
        self:runAction(cc.Sequence:create(
            cc.CallFunc:create(
                function () 
                    self._gameFrame:StandUp(1)
                end
                ),
            cc.DelayTime:create(2),
            cc.CallFunc:create(
                function ()
                    print("delay leave")
                    self:dismissPopWait()
                    self:onExitRoom()
                end
                )
            )
        )
        --]]
        local wait = self._gameFrame:StandUp(1)
        if wait then
            self:showPopWait()
            return
        end
        return
    end
    self:dismissPopWait()
    self:onExitRoom()
end


--离开房间
function GameLayer:onExitRoom()
    --self._scene:onKeyBack()
    self:startOrStopHeartBeat( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end
    end
    self:removeFromParent()
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair,time,clockId)
    if nil ~= self._gameView and nil ~= self._gameView.updateClock then
        self._gameView:updateClock(clockId, time)
    end
end

-- 设置计时器
function GameLayer:SetGameClock(chair,id,time)
    GameLayer.super.SetGameClock(self,chair,id,time)
end

function GameLayer:onGetSitUserNum()
    return table.nums(self._gameView.m_tabUserHead)
end

function GameLayer:getUserInfoByChairID( chairid )
    local viewId = self:SwitchViewChairID(chairid)
    return self._gameView.m_tabUserItem[viewId]
end

function GameLayer:OnResetGameEngine()
    self:reSetData() 
    GameLayer.super.OnResetGameEngine(self)
end

-- 刷新提示列表
-- @param[cards]        出牌数据
-- @param[handCards]    手牌数据
-- @param[outViewId]    出牌视图id
-- @param[curViewId]    当前视图id
function GameLayer:updatePromptList(cards, handCards, outViewId, curViewId)
    self.m_tabCurrentCards = cards
    self.m_tabPromptList = {}

    local result = {}
    if outViewId == curViewId then
        self.m_tabCurrentCards = {}
        result = GameLogic:SearchOutCard(handCards, #handCards, {}, 0)
    else
        result = GameLogic:SearchOutCard(handCards, #handCards, cards, #cards)
    end

    --dump(result, "出牌提示", 6)    
    local resultCount = result[1]
    print("## 提示牌组 " .. resultCount)
    for i = resultCount, 1, -1 do
        local tmplist = {}
        local total = result[2][i]
        local cards = result[3][i]
        for j = 1, total do
            local cbCardData = cards[j] or 0
            table.insert(tmplist, cbCardData)
        end
        table.insert(self.m_tabPromptList, tmplist)
    end
    self.m_tabPromptCards = self.m_tabPromptList[#self.m_tabPromptList] or {}
    self._gameView.m_promptIdx = 0
end

-- 扑克对比
-- @param[cards]        当前出牌
-- @param[outView]      出牌视图id
function GameLayer:compareWithLastCards( cards, outView)
    local bRes = false
    self.m_bLastCompareRes = false
    local outCount = #cards
    dump(cards)
    if outCount > 0 then
        if outView ~= self.m_nLastOutViewId then
            --返回true，表示cards数据大于m_tagLastCards数据
            self.m_bLastCompareRes = GameLogic:CompareCard(self.m_tabLastCards, #self.m_tabLastCards, cards, outCount)
            self.m_nLastOutViewId = outView
        end
        if self.isLaizi then
            self.m_tabLastCards = cards
        else
            self.m_tabLastCards = cards
        end
        self.preCardType = GameLogic:GetCardType(self.m_tabLastCards, #self.m_tabLastCards)
        print('self.preCardType is ... ', self.preCardType )
    end
    return bRes
end

------------------------------------------------------------------------------------------------------------
--网络处理
------------------------------------------------------------------------------------------------------------

-- 发送准备
function GameLayer:sendReady()
    self:KillGameClock()
    self._gameFrame:SendUserReady()
end

-- 发送叫分
function GameLayer:sendCallScore( score )
    self:KillGameClock()
    local cmddata = CCmd_Data:create(1)
    cmddata:pushbyte(score)
    self:SendData(cmd.SUB_C_CALL_SCORE,cmddata)
end

-- 发送出牌
function GameLayer:sendOutCard(cards, bPass, cbLaiziNum)
    self:KillGameClock()
    cbLaiziNum = cbLaiziNum or 0
    if bPass then
        local cmddata = CCmd_Data:create()
        self:SendData(cmd.SUB_C_PASS_CARD,cmddata)
    else
        local cardcount = 1 + cmd.MAX_COUNT + 5
        local cmddata = CCmd_Data:create(cardcount)
        cmddata:pushbyte(#cards)
        for i = 1, #cards do
            local pushCard = cards[i]
            cmddata:pushbyte(pushCard)
            print('push card is ', pushCard)
        end
        for i = #cards + 1, cmd.MAX_COUNT do
            cmddata:pushbyte(0)
        end
        if cbLaiziNum > 0 then
            cmddata:pushbyte(cbLaiziNum)
            for i = 1, cbLaiziNum do
                if i > 4 then
                    break
                end
                cmddata:pushbyte(GameLogic.cbLaiziCardData[i])
                print('push cbLaiziCardData is ', GameLogic.cbLaiziCardData[i], cbLaiziNum)
            end
            for i = cbLaiziNum + 1, 4 do
                cmddata:pushbyte(0)
                --print('push card is ',  cbLaiziNum)
            end
        else
            cmddata:pushbyte(0)
            for i = 1, 4 do
                cmddata:pushbyte(0)
            end
        end
        self:SendData(cmd.SUB_C_OUT_CARD,cmddata)
    end
end

-- 场景信息
function GameLayer:onEventGameScene(cbGameStatus,dataBuffer)
    print("场景数据:" .. cbGameStatus)
    if self.m_bOnGame then
        return
    end
    self.m_cbGameStatus = cbGameStatus
    self.m_bOnGame = true
    --初始化已有玩家
    for i = 1, cmd.PLAYER_COUNT do
        local userItem = self._gameFrame:getTableUserItem(self._gameFrame:GetTableID(), i-1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i-1)
            self.wPeiyin[wViewChairId] = userItem.wPeiyin
            self._gameView:OnUpdateUser(wViewChairId, userItem)
            if PriRoom then
                PriRoom:getInstance():onEventUserState(wViewChairId, userItem, false)
            end
        end
    end
    
    local room_data = PassRoom:getInstance().m_tabPriData
    if room_data.cbGameRule[1][3] == 1 then
        self.isBuXiPai = true
    elseif room_data.cbGameRule[1][4] == 1 then
        self.isLaizi = true
        self._gameView:createFourTopCard()
    elseif room_data.cbGameRule[1][5] == 1 then
        self.isTwo = true
    end

    print('self.isLaizi is ', self.isLaizi)

    if cbGameStatus == cmd.GAME_SCENE_FREE then                                 --空闲状态
        self:onEventGameSceneFree(dataBuffer)
    elseif cbGameStatus == cmd.GAME_SCENE_CALL then                             --叫分状态
        self.m_bCallStateEnter = true
        self.isInGame = true
        self:onEventGameSceneCall(dataBuffer)        
    elseif cbGameStatus == cmd.GAME_SCENE_PLAY then                             --游戏状态
        self.isInGame = true
        self:onEventGameScenePlay(dataBuffer)
    end
    self:startOrStopHeartBeat( true )
    self:dismissPopWait()
end

-- 用户配音改变
function GameLayer:onUserPeiyinChange(user_item)
	local view_id = self:SwitchViewChairID(user_item.wChairID)
    self.wPeiyin[view_id] = user_item.wPeiyin
end

function GameLayer:onEventGameSceneFree( dataBuffer )
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusFree, dataBuffer)
    dump(cmd_table, "scene free", 6)
    --print('onEventGameSceneFree', PassRoom:getInstance().m_tabPriData.szServerID)
    cmd.COUNTDOWN_READY = cmd_table.cbTimeStartGame
    cmd.COUNTDOWN_CALLSCORE = cmd_table.cbTimeCallScore
    cmd.COUNTDOWN_OUTCARD = cmd_table.cbTimeOutCard
    cmd.COUNTDOWN_HANDOUTTIME = cmd_table.cbTimeHeadOutCard
    -- 更新底分
    self._gameView:onGetCellScore(cmd_table.lCellScore)

    -- 空闲消息
    self._gameView:onGetGameFree()

    self:KillGameClock()
    -- 私人房无倒计时
    if not GlobalUserItem.bPrivateRoom and not self._gameView.isFirstLogin then
        -- 设置倒计时
        self:SetGameClock(self:GetMeChairID(), cmd.TAG_COUNTDOWN_READY, cmd.COUNTDOWN_READY)
        self.is_ready_btn_press_status = true
    end    

    if self._gameView.isFirstLogin then
        self._gameView.isFirstLogin = false
        self._gameView:onClickReady()
    end
end

function GameLayer:onEventGameSceneCall( dataBuffer )
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusCall, dataBuffer)
    dump(cmd_table, "scene call", 6)
    cmd.COUNTDOWN_READY = cmd_table.cbTimeStartGame
    cmd.COUNTDOWN_CALLSCORE = cmd_table.cbTimeCallScore
    cmd.COUNTDOWN_OUTCARD = cmd_table.cbTimeOutCard
    cmd.COUNTDOWN_HANDOUTTIME = cmd_table.cbTimeHeadOutCard

    self.cbLaiziCardData = cmd_table.cbLaiziCard

    self.m_bRoundOver = false
    -- 更新底分
    self._gameView:onGetCellScore(cmd_table.lCellScore)
 
    -- 叫分信息
    local scoreinfo = cmd_table.cbScoreInfo[1]
    local tmpScore = 0
    local lastScore = 0
    local lastViewId = self:SwitchViewChairID(cmd_table.wCurrentUser)
    for i = 1, 3 do
        local chair = i - 1
        local score = scoreinfo[i]
        -- 扑克
        local viewId = self:SwitchViewChairID(chair)
        if chair ~= cmd_table.wCurrentUser and 0 ~= score then
            self._gameView:onGetCallScore(-1, viewId, 0, score, true)
        end

        if 0 ~= score then
            tmpScore = ((score == 255) and 0 or score)
        end

        if tmpScore > lastScore then
            lastScore = tmpScore
            lastViewId = viewId
        end
    end
    -- 叫分状态
    local currentScore = cmd_table.cbBankerScore
    local curViewId = self:SwitchViewChairID(cmd_table.wCurrentUser)

    -- 玩家拿牌
    local cards = GameLogic:SortCardList(cmd_table.cbHandCardData[1], cmd.NORMAL_COUNT, 0)
    self._gameView:onGetGameCard(cmd.MY_VIEWID, cards, true)

    self._gameView:setRecordNum(cmd_table.cbCardValueNum[1])

    -- 其余玩家
    local empTyCard = GameLogic:emptyCardList(cmd.NORMAL_COUNT)
    self._gameView:onGetGameCard(cmd.LEFT_VIEWID, empTyCard, true)
    empTyCard = GameLogic:emptyCardList(cmd.NORMAL_COUNT)
    self._gameView:onGetGameCard(cmd.RIGHT_VIEWID, empTyCard, true)

    self._gameView:onGetCallScore(curViewId, lastViewId, currentScore, lastScore, false)
    -- 设置倒计时
    self:SetGameClock(cmd_table.wCurrentUser, cmd.TAG_COUNTDOWN_CALLSCORE, cmd.COUNTDOWN_CALLSCORE)

    -- 刷新局数
    if PriRoom and GlobalUserItem.bPrivateRoom then
        local curcount = PriRoom:getInstance().m_tabPriData.dwPlayCount
        PriRoom:getInstance().m_tabPriData.dwPlayCount = curcount - 1
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end

function GameLayer:onEventGameScenePlay( dataBuffer )
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_StatusPlay, dataBuffer)
   --dump(cmd_table, "scene play", 6)
    cmd.COUNTDOWN_READY = cmd_table.cbTimeStartGame
    cmd.COUNTDOWN_CALLSCORE = cmd_table.cbTimeCallScore
    cmd.COUNTDOWN_OUTCARD = cmd_table.cbTimeOutCard
    cmd.COUNTDOWN_HANDOUTTIME = cmd_table.cbTimeHeadOutCard

    self.cbLaiziCardData = cmd_table.cbLaiziCard
    self.m_bRoundOver = false
    -- 更新底分
    self._gameView:onGetCellScore(cmd_table.lCellScore)

    -- 用户手牌
    local countlist = cmd_table.cbHandCardCount[1]
    for i = 1, 3 do
        local chair = i - 1
        local cards = {}
        local count = countlist[i]
        local viewId = self:SwitchViewChairID(chair)
        if cmd.MY_VIEWID == viewId then
            local tmp = cmd_table.cbHandCardData[1]
            for j = 1, count do
                table.insert(cards, tmp[j])
            end
            cards = GameLogic:SortCardList(cards, count, 0)
        else
            cards = GameLogic:emptyCardList(count)
        end
        self._gameView:onGetGameCard(viewId, cards, true)
    end


    self._gameView:setRecordNum(cmd_table.cbCardValueNum[1])

    -- 庄家信息    
    local bankerView = self:SwitchViewChairID(cmd_table.wBankerUser)
    local bankerCards = GameLogic:SortCardList(cmd_table.cbBankerCard[1], 3, 0)
    local bankerscore = cmd_table.cbBankerScore
    if self:IsValidViewID(bankerView) then
        self._gameView:onGetBankerInfo(bankerView, bankerscore, bankerCards, true)
    end
    self.m_cbBankerChair = cmd_table.wBankerUser

    self.m_cbBankerChairViewID = bankerView
    -- 自己是否庄家
    self.m_bIsMyBanker = (bankerView == cmd.MY_VIEWID)

    -- 设置
    self._gameView:setBankerShape(bankerView, self:SwitchViewChairID(self:GetMeChairID()))
    
    -- 出牌信息
    local cbOutTime = cmd_table.cbTimeOutCard
    local lastOutView = self:SwitchViewChairID(cmd_table.wTurnWiner)
    local outCards = {}
    local serverOut = cmd_table.cbTurnCardData[1]
    for i = 1, cmd_table.cbTurnCardCount do
        table.insert(outCards, serverOut[i])
    end
    outCards = GameLogic:SortCardList(outCards, cmd_table.cbTurnCardCount, 0)
    local currentView = self:SwitchViewChairID(cmd_table.wCurrentUser)
    if self:IsValidViewID(lastOutView) and self:IsValidViewID(currentView) then
        self.m_nLastOutViewId = lastOutView
        self:compareWithLastCards(outCards, lastOutView)

        if currentView == cmd.MY_VIEWID then
            -- 构造提示
            local handCards = self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:getHandCards()
            self:updatePromptList(outCards, handCards, currentView, lastOutView)
        end

        -- 不出按钮
        if #self.m_tabPromptList > 0 then
            self._gameView:onChangePassBtnState(not (currentView == lastOutView--[[#self.m_tabPromptList > 0]]))
        else
            self._gameView:onChangePassBtnState( true )
        end        

        self._gameView:onGetOutCard(currentView, lastOutView, outCards, true)

        -- 设置倒计时
        self:SetGameClock(cmd_table.wCurrentUser, cmd.TAG_COUNTDOWN_OUTCARD, cmd.COUNTDOWN_OUTCARD)
    end
end

function GameLayer:onEventGameMessage(sub,dataBuffer)
    if nil == self._gameView then
        return
    end

    if cmd.SUB_S_GAME_START == sub then                 --游戏开始
        self.m_cbGameStatus = cmd.GAME_SCENE_CALL
        self:onSubGameStart(dataBuffer)
    elseif cmd.SUB_S_CALL_SCORE == sub then             --用户叫分
        self.m_cbGameStatus = cmd.GAME_SCENE_CALL
        self:onSubCallScore(dataBuffer)
    elseif cmd.SUB_S_BANKER_INFO == sub then            --庄家信息
        self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
        self:onSubBankerInfo(dataBuffer)
    elseif cmd.SUB_S_OUT_CARD == sub then               --用户出牌
        self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
        self:onSubOutCard(dataBuffer)
    elseif cmd.SUB_S_PASS_CARD == sub then              --用户放弃
        self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
        self:onSubPassCard(dataBuffer)
    elseif cmd.SUB_S_GAME_CONCLUDE == sub then          --游戏结束
        self.m_cbGameStatus = cmd.GAME_SCENE_END
        self:onSubGameConclude(dataBuffer)
    end
end

-- 文本聊天
function GameLayer:onUserChat(chatdata, chairid)
    local viewid = self:SwitchViewChairID(chairid)    
    if self:IsValidViewID(viewid) then
        self._gameView:onUserChat(chatdata, viewid)
    end
end

-- 表情聊天
function GameLayer:onUserExpression(chatdata, chairid)
    local viewid = self:SwitchViewChairID(chairid)
    if self:IsValidViewID(viewid) then
        self._gameView:onUserExpression(chatdata, viewid)
    end
end

-- 语音播放开始
function GameLayer:onUserVoiceStart( useritem, filepath )
    local viewid = self:SwitchViewChairID(useritem.wChairID)
    local roleItem = self._gameView.m_tabUserHead[viewid]
    if nil ~= roleItem then
        roleItem:onUserVoiceStart()
    end
end

-- 语音播放结束
function GameLayer:onUserVoiceEnded( useritem, filepath )
    local viewid = self:SwitchViewChairID(useritem.wChairID)
    local roleItem = self._gameView.m_tabUserHead[viewid]
    if nil ~= roleItem then
        roleItem:onUserVoiceEnded()
    end
end

-- 游戏开始
function GameLayer:onSubGameStart(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_GameStart, dataBuffer)
    --dump(cmd_table, "onSubGameStart", 6)
    dump(cmd_table)

    GameLogic.bIsNeedShowLaizi = false

    self.cbLaiziCardData = cmd_table.cbLaiziCard
    
    self.is_game_start = true

    self.m_bRoundOver = false
    self:reSetData()
    --游戏开始
    self._gameView:onGameStart()
    local curView = self:SwitchViewChairID(cmd_table.wCurrentUser)
    local startView = self:SwitchViewChairID(cmd_table.wStartUser)   

    self:KillGameClock()
    if self:IsValidViewID(curView) and self:IsValidViewID(startView) then
        print("&& 游戏开始 " .. curView .. " ## " .. startView)
        -- 音效
        if GlobalUserItem.bVoiceAble then
            AudioEngine.playEffect('sound/start.wav')
        end
        --ExternalFun.playSoundEffect( "start.wav" )
        --发牌
        local carddata = GameLogic:SortCardList(cmd_table.cbCardData[1], cmd.NORMAL_COUNT, 0)
        if self.isLaizi == true then
            GameLogic:setLaiziCard(carddata, #carddata, self.cbLaiziCardData)
        end
        
        self._gameView:onGetGameCard(cmd.MY_VIEWID, carddata, false, cc.CallFunc:create(function()
        self._gameView:onGetCallScore(curView, startView, 0, -1, nil, true)

        self._gameView:setRecordNum(cmd_table.cbCardValueNum[1])
        -- 设置倒计时
        self:SetGameClock(cmd_table.wCurrentUser, cmd.TAG_COUNTDOWN_CALLSCORE, cmd.COUNTDOWN_CALLSCORE)
        end))
    else
        print("viewid invalid" .. curView .. " ## " .. startView)
    end
end

-- 用户叫分
function GameLayer:onSubCallScore(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_CallScore, dataBuffer)
    --dump(cmd_table, "CMD_S_CallScore", 3)

    self.isInGame = true

    local curView = self:SwitchViewChairID(cmd_table.wCurrentUser)
    local lastView = self:SwitchViewChairID(cmd_table.wCallScoreUser)    
    if self:IsValidViewID(curView) and self:IsValidViewID(lastView) then
        print("&& 游戏叫分 " .. curView .. " ## " .. lastView)
        self._gameView:onGetCallScore(curView, lastView, cmd_table.cbCurrentScore, cmd_table.cbUserCallScore)

        -- 设置倒计时
        self:SetGameClock(cmd_table.wCurrentUser, cmd.TAG_COUNTDOWN_CALLSCORE, cmd.COUNTDOWN_CALLSCORE)
    else
        print("viewid invalid" .. curView .. " ## " .. lastView)
    end    
end

-- 庄家信息
function GameLayer:onSubBankerInfo(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_BankerInfo, dataBuffer)
    --dump(cmd_table, "onSubBankerInfo", 6)
    local bankerView = self:SwitchViewChairID(cmd_table.wBankerUser)
    local curView = self:SwitchViewChairID(cmd_table.wCurrentUser)
    GameLogic.bIsNeedShowLaizi = true
    self.m_cbBankerChair = cmd_table.wBankerUser
    self.m_cbBankerChairViewID = bankerView

    -- 自己是否庄家
    self.m_bIsMyBanker = (bankerView == cmd.MY_VIEWID)

    -- 设置
    self._gameView:setBankerShape(curView, curView)

    -- 庄家信息
    if self:IsValidViewID(bankerView) and self:IsValidViewID(curView) then
        print("&& 庄家信息 " .. bankerView .. " ## " .. curView)
        -- 音效
        if GlobalUserItem.bVoiceAble then
            AudioEngine.playEffect('sound/bankerinfo.wav')
        end
        --ExternalFun.playSoundEffect( "bankerinfo.wav" )

        self.m_cbBankerViewId = cmd_table.wBankerUser
        local bankercard = GameLogic:SortCardList(cmd_table.cbBankerCard[1], 3, 0)
        self._gameView:onGetBankerInfo(bankerView, cmd_table.cbBankerScore, bankercard, false)

        self.m_nLastOutViewId = bankerView
        -- 构造提示
        local handCards = self._gameView.m_tabNodeCards[bankerView]:getHandCards()

        if self.isLaizi == true then
            GameLogic:setLaiziCard(handCards, cmd.MAX_COUNT, self.cbLaiziCardData)
        end
        
        if bankerView == cmd.MY_VIEWID then
            self:updatePromptList({}, handCards, cmd.MY_VIEWID, cmd.MY_VIEWID)

            -- 不出按钮
            self._gameView:onChangePassBtnState(false)

            -- 开始出牌
            self._gameView:onGetOutCard(curView, curView, {})
        end

       

        -- 设置倒计时
        self:SetGameClock(cmd_table.wBankerUser, cmd.TAG_COUNTDOWN_OUTCARD, cmd.COUNTDOWN_HANDOUTTIME)
    else
        print("viewid invalid" .. bankerView .. " ## " .. curView)
    end

    -- 刷新局数
    if PriRoom and not self.m_bCallStateEnter and GlobalUserItem.bPrivateRoom then
        local curcount = PriRoom:getInstance().m_tabPriData.dwPlayCount
        PriRoom:getInstance().m_tabPriData.dwPlayCount = curcount + 1
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
    self.m_bCallStateEnter = false

    
    if cmd.MY_VIEWID ~= bankerView then
        local handCards = self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:getHandCards()
        GameLogic:SortLaiziCardList(handCards)
        self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:arrangeAllCards(true)
        self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:updateTextureAllCards()
    end
end

-- 用户出牌
function GameLayer:onSubOutCard(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_OutCard, dataBuffer)
    --dump(cmd_table, "onSubOutCard", 6)
    dump(cmd_table)

    local curView = self:SwitchViewChairID(cmd_table.wCurrentUser)
    local outView = self:SwitchViewChairID(cmd_table.wOutCardUser)

    print("&& 出牌 " .. outView .. " ## " .. curView)
    local outCard = {}--cmd_table.cbCardData[1]
    for i = 1, cmd_table.cbCardCount do
        table.insert(outCard, cmd_table.cbCardData[1][i] )
    end
    local outCount = #outCard
    local carddata = GameLogic:SortCardList(outCard, outCount, 0)
    
    -- 扑克对比
    self:compareWithLastCards(carddata, outView)

    local handCards = self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:getHandCards()
    self:updatePromptList(carddata, handCards, outView, curView)

    -- 不出按钮
    self._gameView:onChangePassBtnState(true)

    self._gameView:onGetOutCard(curView, outView, carddata)

    self._gameView:setRecordNum(cmd_table.cbCardValueNum[1])
    
    
    if self.isLaizi == true then
        -- 构造提示
        local handCards = self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:getHandCards()    
        dump(handCards)
        GameLogic:setLaiziCard(handCards, #handCards, self.cbLaiziCardData)
        if cmd.MY_VIEWID == outView then
            GameLogic:SortLaiziCardList(handCards)
            self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:arrangeAllCards(true)
        end
        
    end


    -- 设置倒计时
    self:SetGameClock(cmd_table.wCurrentUser, cmd.TAG_COUNTDOWN_OUTCARD, cmd.COUNTDOWN_OUTCARD)
end

-- 用户放弃
function GameLayer:onSubPassCard(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_PassCard, dataBuffer)
    dump(cmd_table)

    local curView = self:SwitchViewChairID(cmd_table.wCurrentUser)
    local passView = self:SwitchViewChairID(cmd_table.wPassCardUser)
    if self:IsValidViewID(curView) and self:IsValidViewID(passView) then
        print("&& pass " .. curView .. " ## " .. passView)
        if 1 == cmd_table.cbTurnOver then
            print("一轮结束")
            self:compareWithLastCards({}, curView)
            -- 构造提示
            local handCards = self._gameView.m_tabNodeCards[cmd.MY_VIEWID]:getHandCards()
            self:updatePromptList({}, handCards, curView, curView)

            -- 不出按钮
            self._gameView:onChangePassBtnState(false)
        end
        -- 不出牌
        self._gameView:onGetPassCard(passView)

        self._gameView:onGetOutCard(curView, curView, {})

        -- 设置倒计时
        self:SetGameClock(cmd_table.wCurrentUser, cmd.TAG_COUNTDOWN_OUTCARD, cmd.COUNTDOWN_OUTCARD)
    else
        print("viewid invalid" .. curView .. " ## " .. passView)
    end
end

-- 游戏结束
function GameLayer:onSubGameConclude(dataBuffer)
    local cmd_table = ExternalFun.read_netdata(cmd.CMD_S_GameConclude, dataBuffer)
    --dump(cmd_table, "onSubGameConclude", 6)
    -- 音效
    if GlobalUserItem.bVoiceAble then
        AudioEngine.playEffect('sound/gameconclude.wav')
    end
    --ExternalFun.playSoundEffect( "gameconclude.wav" )
    self.isInGame = false

    local path = cs.game.SRC .. 'room.GameResultLayer'
    helper.pop.popLayer(path, nil, {self._gameView, cmd_table})

    self.is_game_start = false
    self.m_bRoundOver = true
    
    local str = ""
    --local rs = GameResultLayer.getTagGameResult()

    local scorelist = cmd_table.lGameScore[1]
    local countlist = cmd_table.cbCardCount[1]
    local cardlist = cmd_table.cbHandCardData[1]
    local haveCount = 0

    for i = 1, 3 do
        local chair = i - 1
        local viewId = self:SwitchViewChairID(chair)

        -- 结算
        local score = scorelist[i]
        if score > 0 then
            str = "+" .. score
        else
            str = "" .. score
        end
        local settle = GameResultLayer.getTagSettle()
        settle.m_userName = self._gameView:getUserNick(viewId)
        settle.m_settleCoin = str
        if cmd.MY_VIEWID == viewId then
            --rs.enResult = self:getWinDir(score)
        end
        --rs.settles[i] = settle

        -- 手牌
        local count = countlist[i]
        local cards = {}
        for j = 1, count do 
            table.insert(cards, cardlist[j + haveCount])
        end
        haveCount = haveCount + count
        if count > 0 then
            self._gameView.m_tabNodeCards[viewId]:showLeftCards(cards)
        end
    end
    -- 标志
    for i = 1, 3 do
        local chair = i - 1
        -- 春天
        if 1 == cmd_table.bChunTian then
            if chair == self.m_cbBankerViewId then
                --rs.settles[i].m_cbFlag = cmd.kFlagChunTian
            end
        end

        -- 反春天
        if 1 == cmd_table.bFanChunTian then
            if chair ~= self.m_cbBankerViewId then
                --rs.settles[i].m_cbFlag = cmd.kFlagFanChunTian
            end
        end
    end

    self._gameView:runResultNum( scorelist )
    self._gameView:onGetGameConclude( rs )

    self:KillGameClock()
    -- 私人房无倒计时
    if not GlobalUserItem.bPrivateRoom then
        -- 设置倒计时
        self:SetGameClock(self:GetMeChairID(), cmd.TAG_COUNTDOWN_READY, cmd.COUNTDOWN_READY)
        self.is_ready_btn_press_status = false
    end

    self:reSetData()
end
------------------------------------------------------------------------------------------------------------
--网络处理
------------------------------------------------------------------------------------------------------------

function GameLayer:getWinDir( score )
    print("## is my Banker")
    print(self.m_bIsMyBanker)
    print("## is my Banker")
    if true == self.m_bIsMyBanker then
        if score > 0 then
            return cmd.kLanderWin
        elseif score < 0 then
            return cmd.kLanderLose
        end
    else
        if score > 0 then
            return cmd.kFarmerWin
        elseif score < 0 then
            return cmd.kFarmerLose
        end
    end
    return cmd.kDefault
end


-- 刷新界面
function GameLayer:updateView()
end


-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
        self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
       -- print('stop.............replay')
        self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
    if self._gameView.bCanNextReplay then
        if not PassRoom:getInstance():getNetFrame():doNextReplay() then
            self:startOrStopReplay( false )
        end
    end

end


-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:stop( yl.ActionTag.HEART )

            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )

        end
    else
      --  print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end

-- 检测心跳
function GameLayer:checkHeartBeat()
    --print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end

-- 重连
function GameLayer:doReConnect()
    --print('网络重连。。。')
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()
    --print('PassRoom:getInstance().m_tabPriData.szServerID', dwCurServerID)
    --PassRoom:getInstance():getNetFrame():onSearchRoom( GlobalUserItem.dwCurServerID )
    PassRoom:getInstance():onLoginRoom(GlobalUserItem.dwCurServerID)
end


-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
        --print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
        --print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    --print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置
function GameLayer:onCheckSocketIsOK()
    self:startCheckIsSocketOK( false )
    self.check_is_socket_ok_times = 0
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('subGameResultLayer')
    if view then
        view:removeFromParent()
    end
end

return GameLayer
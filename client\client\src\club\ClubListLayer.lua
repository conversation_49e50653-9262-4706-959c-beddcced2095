-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部列表
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')


local ClubListLayer = class("ClubListLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubListLayer:ctor(clubs)
    print('ClubListLayer:ctor...')
    self:setName('club_list_layer')
    self.m_clubs = clubs
    local main_node = ClubUtil.initUI(self, 'ClubListLayer.csb')
    main_node:child('row_template'):hide()

    self:onActive()
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubListLayer:onEnter()
    print('ClubListLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_APPLY_COUNT, self, self.onApplyCountResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubListLayer:onExit()
    print('ClubListLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 申请检查返回
-------------------------------------------------------------------------------
function ClubListLayer:onApplyCountResp(data)
    -- club对应main中的club，已经被更新
    local items = self.main_node:child('listview'):getItems()
    for i, item in ipairs(items) do
        local btn = item:child('btn_manage')
        if btn and not btn.club.is_join then -- item可能是空白，此时btn不存在
            ClubUtil.addRedPoint(btn, btn.club.apply_num > 0)
        end
    end
end


-------------------------------------------------------------------------------
-- 俱乐部列表刷新(外部可能调用)
-------------------------------------------------------------------------------
function ClubListLayer:onActive()
    self:createList()
end


-------------------------------------------------------------------------------
-- 俱乐部列表生成
-------------------------------------------------------------------------------
function ClubListLayer:createList()
    local clubs = self.m_clubs
    local listview = self.main_node:child('listview')
    listview:removeAllItems()
    local template = self.main_node:child('row_template')
    helper.layout.pushEmpty(listview, {10, 10})
    for i, club in ipairs(clubs) do
        local item = template:clone():show()

        item:child('img_logo'):texture('common/icon_club_logo_' .. club.nLogoID .. '.png')
        item:child('label_name'):setString(club.szName)
        item:child('label_info'):setString( LANG{'CLUB_INFO', owner=club.szPresidentName, id=string.format('%06d', club.dwClubID)} )
        item:child('btn_manage'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnManage) )
        item:child('btn_manage').club = club

        if club.is_join  then
            if club.cbIsManager == 0 then
                item:child('btn_manage/font'):texture('word/font_btn_view.png')
            end
            item:child('img_zhu'):removeFromParent()
        else
            item:child('img_zhu'):px(item:child('label_name'):px() + item:child('label_name'):size().width + 60)
            ClubUtil.addRedPoint(item:child('btn_manage'), item:child('btn_manage').club.apply_num > 0)
        end
        listview:pushBackCustomItem(item)
    end
    helper.layout.pushEmpty(listview, {10, 10})
end


-------------------------------------------------------------------------------
-- 管理（查看）按钮点击
-------------------------------------------------------------------------------
function ClubListLayer:onBtnManage(sender)
    ClubUtil.open(self, 'club.ClubManageLayer', nil, {sender.club})
end


-------------------------------------------------------------------------------
-- 移除俱乐部
-------------------------------------------------------------------------------
function ClubListLayer:removeClub(club)
    for i, c in ipairs(self.m_clubs) do
        if c.dwClubID == club.dwClubID then
            table.remove(self.m_clubs, i)
        end
    end
end


return ClubListLayer

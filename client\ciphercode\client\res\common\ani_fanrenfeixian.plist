<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_fanrenfeixian_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{486,324},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{486,162},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{324,324},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{324,162},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{486,0},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{324,0},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{162,324},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{162,162},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{162,0},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,324},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,162},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
            <key>ani_fanrenfeixian_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{160,160}}</string>
                <key>sourceSize</key>
                <string>{160,160}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_fanrenfeixian.png</string>
            <key>size</key>
            <string>{646,484}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:75df0ab4225844d7b9fb28610ea81159:a996a24e1c568755f56a3010f8d81379:8258536b198475767ea9341a9051030e$</string>
            <key>textureFileName</key>
            <string>ani_fanrenfeixian.png</string>
        </dict>
    </dict>
</plist>

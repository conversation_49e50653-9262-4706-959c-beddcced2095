@echo off
echo ===== FileZilla 错误检查脚本 =====
echo.

echo 1. 检查 FileZilla FTP 服务状态...
sc query "FileZilla Server"
echo.

echo 2. 检查 FTP 端口占用情况...
echo 检查端口 21 (FTP控制端口):
netstat -ano | findstr :21
echo.
echo 检查端口 14147 (FileZilla管理端口):
netstat -ano | findstr :14147
echo.

echo 3. 检查可能的端口冲突...
echo 常见 FTP 端口检查:
netstat -ano | findstr :20
netstat -ano | findstr :990
echo.

echo 4. 检查 FileZilla 进程...
tasklist | findstr -i filezilla
echo.

echo 5. 建议的解决方案:
echo.
echo 如果 FileZilla 不是必需的服务:
echo   - 在 XAMPP 控制面板中停止 FileZilla
echo   - 这不会影响 Apache 和短信验证功能
echo.
echo 如果需要 FTP 服务:
echo   - 检查 FileZilla 配置文件
echo   - 重启 FileZilla 服务
echo   - 检查防火墙设置
echo.

echo 6. FileZilla 日志位置:
echo E:\xampp\FileZillaFTP\Logs\
echo.

pause

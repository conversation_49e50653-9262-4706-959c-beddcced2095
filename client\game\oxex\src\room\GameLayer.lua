local GameModel = appdf.req(appdf.CLIENT_SRC.."system.GameModel")

local GameLayer = class("GameLayer", GameModel)

local cmd = appdf.req(appdf.GAME_SRC.."oxex.src.room.CMD_Game")
local GameLogic = appdf.req(appdf.GAME_SRC.."oxex.src.room.GameLogic")
local GameViewLayer = appdf.req(appdf.GAME_SRC.."oxex.src.room.GameViewLayer")

function GameLayer:ctor(frameEngine, scene)
    GameLayer.super.ctor(self, frameEngine, scene)
    self.m_cbGameStatus = -1
    self.isSendCardRunning = false

    
    if yl.IS_REPLAY_MODEL then
        self.m_cbGameStatus = cmd.GS_TK_PLAYING
        self:updatePriRoom()
    end
end

GameLayer.SendCardActionTag = 100

function GameLayer:CreateView()
    return GameViewLayer:create(self):addTo(self)
end

function GameLayer:OnInitGameEngine()
    GameLayer.super.OnInitGameEngine(self)
    self.cbPlayStatus = {0, 0, 0, 0, 0}
    self.cbCardData = {}
    self.wBankerUser = yl.INVALID_CHAIR
end

function GameLayer:OnResetGameEngine()
    GameLayer.super.OnResetGameEngine(self)
end

function GameLayer:SwitchViewChairID(chair)
    local viewid = yl.INVALID_CHAIR
    local nChairCount = self._gameFrame:GetChairCount()
    local nChairID = self:GetMeChairID()
    print('nChairCount is : ', nChairCount)
    if chair ~= yl.INVALID_CHAIR and chair < nChairCount then
        viewid = math.mod(chair + math.floor(cmd.GAME_PLAYER * 3/2) - nChairID, cmd.GAME_PLAYER) + 1
        if nChairCount == 2 and viewid ~= 3 then        -- 两人时对方总在1号位
            viewid = 1
        --[[
        elseif nChairCount == 3 and viewid == 1 then    -- 三人时1号位总空着
            if nChairID == 0 then
                viewid = 2
            elseif nChairID == 2 then
                viewid = 4
            end
        --]]
        end
    end
    return viewid
end

-- 计时器响应
function GameLayer:OnEventGameClockInfo(chair,time,clockId)
    -- body
    if clockId == cmd.IDI_NULLITY then
        if time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
        --[[
    elseif clockId == cmd.IDI_START_GAME then
        if time == 0 then
            self._gameFrame:setEnterAntiCheatRoom(false)--退出防作弊
            self:onExitTable()
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
        --]]
    elseif clockId == cmd.IDI_CALL_BANKER then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CANCEL)
        end
    elseif clockId == cmd.IDI_TIME_USER_ADD_SCORE then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_CHIP + 4)
        elseif time <= 5 then
            AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WARN.WAV")
        end
    elseif clockId == cmd.IDI_TIME_OPEN_CARD then
        if time < 1 then
            self._gameView:onButtonClickedEvent(GameViewLayer.BT_OPENCARD)
        end
    end
end

--用户聊天
function GameLayer:onUserChat(chat, wChairId)
    self._gameView:userChat(self:SwitchViewChairID(wChairId), chat.szChatString)
end

--用户表情
function GameLayer:onUserExpression(expression, wChairId)
    self._gameView:userExpression(self:SwitchViewChairID(wChairId), expression.wItemIndex)
end

-- 场景信息
function GameLayer:onEventGameScene(cbGameStatus, dataBuffer)
    self.m_cbGameStatus = cbGameStatus
	local tableId = self._gameFrame:GetTableID()
	self._gameView:setTableID(tableId)
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:OnUpdateUser(wViewChairId, userItem)
        end
    end
    self._gameView:onResetView()

	if cbGameStatus == cmd.GS_TK_FREE	then				--空闲状态
        self:onSceneFree(dataBuffer)
	elseif cbGameStatus == cmd.GS_TK_CALL	then			--叫分状态
        self:onSceneCall(dataBuffer)
	elseif cbGameStatus == cmd.GS_TK_SCORE	then			--下注状态
        self:onSceneScore(dataBuffer)
    elseif cbGameStatus == cmd.GS_TK_PLAYING  then            --游戏状态
        self:onScenePlaying(dataBuffer)
    else
        self.m_cbGameStatus = 0
    end
    
    print('GameLayer:onEventGameScene is ' .. self.m_cbGameStatus)

    self:updatePriRoom()
    self._gameView:recoverUserScore()

    -- 启动 心跳
    self:startOrStopHeartBeat( true )
    
    self:dismissPopWait()
end

-- 刷新界面
function GameLayer:updateView()
end

--空闲场景
function GameLayer:onSceneFree(dataBuffer)
    print("onSceneFree")
    local int64 = Integer64.new()
    local lCellScore = dataBuffer:readscore(int64):getvalue()

    local lTurnScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTurnScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    local lCollectScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lCollectScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = dataBuffer:readbyte()
    end

    self.cbPlayCount = dataBuffer:readbyte()
    self._gameView:showCurJushu()

    self._gameView:setCellScore(lCellScore)
    self._gameView:hideCallBankerBtn()
    self._gameView:showUserOperateStatus()

    if yl.IS_REPLAY_MODEL then
        self._gameView.btStart:hide()
    end
    --[[
    if not GlobalUserItem.isAntiCheat() then    --非作弊房间
        self._gameView.btStart:setVisible(true)
        self._gameView:setClockPosition(cmd.MY_VIEWID)
        self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    end
    --]]

    
    
end
--叫庄场景
function GameLayer:onSceneCall(dataBuffer)
    print("onSceneCall")
    local int64 = Integer64.new()
    local wCallBanker = dataBuffer:readword()
    --self.wBankerUser = wCallBanker
    self.cbDynamicJoin = dataBuffer:readbyte()
    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = dataBuffer:readbyte()
        print('333333333333333333333', self.cbPlayStatus[i])
    end
    local lCellScore = dataBuffer:readscore(int64):getvalue()
    local lTurnScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTurnScore[i] = dataBuffer:readscore(int64):getvalue()
    end
    local lCollectScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lCollectScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    self.cbCallStatus = self.cbCallStatus or {}
    for i = 1, cmd.GAME_PLAYER do
        self.cbCallStatus[i] = dataBuffer:readbyte()
        print('call status is ', self.cbCallStatus[i])
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbCardData[i] = {}
        for j = 1, cmd.GAME_CARD_NUM do
            self.cbCardData[i][j] = dataBuffer:readbyte()
        end
    end

    local wCurrent = dataBuffer:readword()
    if wCurrent ~= self:GetMeChairID() then
        return
    end

    self.cbPlayCount = dataBuffer:readbyte()
    self._gameView:showCurJushu()

    self:recoverUserCard()
    
    local wViewBankerId = self:SwitchViewChairID(wCallBanker)
    if wViewBankerId == nil or wViewBankerId == yl.INVALID_CHAIR then
        return
    end
    
    self._gameView:setCellScore(lCellScore)
    self._gameView.btStart:hide()
    if self.cbCallStatus[self:GetMeChairID() + 1] == 0 then
        self._gameView:gameCallBanker(self:SwitchViewChairID(wCallBanker))
        self._gameView:setClockPosition(wViewBankerId)
        self:SetGameClock(wCallBanker, cmd.IDI_CALL_BANKER, cmd.TIME_USER_CALL_BANKER)
    end
end
--下注场景
function GameLayer:onSceneScore(dataBuffer)
    print("onSceneScore")
    local int64 = Integer64.new()
    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = dataBuffer:readbyte()
        print('4444444444444444444444', self.cbPlayStatus[i])
    end
    
    self.cbDynamicJoin = dataBuffer:readbyte()
    local lTurnMaxScore = dataBuffer:readscore(int64):getvalue()
    local lTableScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTableScore[i] = dataBuffer:readscore(int64):getvalue() 
        if self.cbPlayStatus[i] == 1 then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:setUserTableScore(wViewChairId, lTableScore[i])
        end
    end
    self.wBankerUser = dataBuffer:readword()
   

    local lTurnScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTurnScore[i] = dataBuffer:readscore(int64):getvalue()
    end
    local lCollectScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lCollectScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbCardData[i] = {}
        for j = 1, cmd.GAME_CARD_NUM do
            self.cbCardData[i][j] = dataBuffer:readbyte()
        end
    end

    local wCurrent = dataBuffer:readword()
    if wCurrent ~= self:GetMeChairID() then
        return
    end

    self.cbPlayCount = dataBuffer:readbyte()
    self._gameView:showCurJushu()

    self._gameView:hideCallBankerBtn()
    self._gameView.btStart:hide()

    self:recoverUserCard()

    local viewBankerId = self:SwitchViewChairID(self.wBankerUser)
    self._gameView:setBankerUser(viewBankerId)
    self._gameView:setTurnMaxScore(lTurnMaxScore)
    
    if lTableScore[self:GetMeChairID() + 1] <= 0 then
        self._gameView:gameStart(viewBankerId)

        self._gameView:setClockPosition()
        self:SetGameClock(self.wBankerUser, cmd.IDI_TIME_USER_ADD_SCORE, cmd.TIME_USER_ADD_SCORE)
       
    end
    
    for i = 1, cmd.GAME_PLAYER do
        if self.cbPlayStatus[i] == 1 and lTableScore[i] ~= 0 then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:gameAddScore(wViewChairId, lTableScore[i])
        end
    end
end

--游戏场景
function GameLayer:onScenePlaying(dataBuffer)
    print("onScenePlaying")
    local wCurrent = dataBuffer:readword()
    local int64 = Integer64.new()
    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = dataBuffer:readbyte()
    end
    self.cbDynamicJoin = dataBuffer:readbyte()
    local lTurnMaxScore = dataBuffer:readscore(int64):getvalue()
    local lTableScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTableScore[i] = dataBuffer:readscore(int64):getvalue()
        if self.cbPlayStatus[i] == 1 and lTableScore[i] ~= 0 then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:gameAddScore(wViewChairId, lTableScore[i])
        end
    end
    self.wBankerUser = dataBuffer:readword()
    for i = 1, cmd.GAME_PLAYER do
        self.cbCardData[i] = {}
        for j = 1, cmd.GAME_CARD_NUM do
            self.cbCardData[i][j] = dataBuffer:readbyte()
        end
    end
   

    --用户是否已经摊牌
    local bOxCard = {}
    for i = 1, cmd.GAME_PLAYER do
        bOxCard[i] = dataBuffer:readbyte()
        local wViewChairId = self:SwitchViewChairID(i - 1)
        if nil ~= bOxCard[i] and self.cbPlayStatus[i] == 1 and wViewChairId ~= cmd.MY_VIEWID then
            self._gameView:setOpenCardVisible(wViewChairId, true)
        end
    end

    local lTurnScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lTurnScore[i] = dataBuffer:readscore(int64):getvalue()
    end
    local lCollectScore = {}
    for i = 1, cmd.GAME_PLAYER do
        lCollectScore[i] = dataBuffer:readscore(int64):getvalue()
    end

    self.cbPlayCount = dataBuffer:readbyte()
    self._gameView:showCurJushu()
    self._gameView.btStart:hide()

    --显示牌并开自己的牌
    if wCurrent == self:GetMeChairID() then
        self:recoverUserCard()
    end
    
    self._gameView:setBankerUser(self:SwitchViewChairID(self.wBankerUser))
    self._gameView:gameScenePlaying()
    self._gameView:setClockPosition()
    self:SetGameClock(self.wBankerUser, cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
end

function GameLayer:recoverUserCard()
    -- body
    local self_chair_id = self:GetMeChairID() + 1
    --打开自己的牌  
    for i = 1, cmd.GAME_CARD_NUM do
        local data = self.cbCardData[self_chair_id][i]
        local value = GameLogic:getCardValue(data)
        local color = GameLogic:getCardColor(data)
        local card = self._gameView.nodeCard[cmd.MY_VIEWID][i]
        card:show()
        if self:isMingPaiBanker() and i == cmd.GAME_CARD_NUM then
            break
        end
        self._gameView:setCardTextureRect(cmd.MY_VIEWID, i, value, color)
    end
    
    
    for i = 1, cmd.GAME_PLAYER do repeat
        if i == self_chair_id then
            break
        end
        local wViewChairId = self:SwitchViewChairID(i - 1)
        if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
            break
        end
        for j = 1, cmd.GAME_CARD_NUM do
            local card = self._gameView.nodeCard[wViewChairId][j]
            card:show()
            --[[
            if wViewChairId == 4 or wViewChairId == 5 then
                card:setLocalZOrder(6 - j)
            end
            
            if self:isMingPaiBanker() then
                local data = self.cbCardData[i][j]
                local value = GameLogic:getCardValue(data)
                local color = GameLogic:getCardColor(data)
                self._gameView:setCardTextureRect(wViewChairId, j, value, color)
            end
            --]]
        end
    until true
    end

end

-- 游戏消息
function GameLayer:onEventGameMessage(sub,dataBuffer)
	if sub == cmd.SUB_S_CALL_BANKER then 
		self:onSubCallBanker(dataBuffer)
	elseif sub == cmd.SUB_S_GAME_START then 
		self:onSubGameStart(dataBuffer)
	elseif sub == cmd.SUB_S_ADD_SCORE then 
		self:onSubAddScore(dataBuffer)
	elseif sub == cmd.SUB_S_SEND_CARD then 
		self:onSubSendCard(dataBuffer)
	elseif sub == cmd.SUB_S_OPEN_CARD then 
		self:onSubOpenCard(dataBuffer)
	elseif sub == cmd.SUB_S_PLAYER_EXIT then 
		--self:onSubPlayerExit(dataBuffer)
	elseif sub == cmd.SUB_S_GAME_END then 
        self:onSubGameEnd(dataBuffer)
    elseif sub == cmd.SUB_S_START_COMPARE then
        self:onStartCompare(dataBuffer)
	else
		print("unknow gamemessage sub is"..sub)
	end
end

function GameLayer:getPlayNum()
    local num = 0
    for i = 1, cmd.GAME_PLAYER do
        if self.cbPlayStatus[i] == 1 then
            num = num + 1
        end
    end

    return num
end

--用户叫庄
function GameLayer:onSubCallBanker(dataBuffer)
    self.m_cbGameStatus = cmd.GS_TK_CALL
    if self._gameView._priView then
        self._gameView._priView:onRefreshInviteBtn()
    end

    local wCallBanker = dataBuffer:readword()
    local bFirstTimes = dataBuffer:readbool()
    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = dataBuffer:readbyte()
    end
   --[[ 
    if wCallBanker ~= self:GetMeChairID() then
        return
    end
--]]

    --print('yl.IS_REPLAY_MODEL onSubCallBanker start')
    self:perform(function ( )
        --print('yl.IS_REPLAY_MODEL onSubCallBanker', self.isSendCardRunning)
        if self.isSendCardRunning == false then
            self:stop(GameLayer.SendCardActionTag)
            self._gameView:gameCallBanker(self:SwitchViewChairID(wCallBanker), bFirstTimes)
            self._gameView:setClockPosition(self:SwitchViewChairID(wCallBanker))
            self:SetGameClock(wCallBanker, cmd.IDI_CALL_BANKER, cmd.TIME_USER_CALL_BANKER)
        end
        
    end, 0.01, -1, GameLayer.SendCardActionTag )

end

--游戏开始
function GameLayer:onSubGameStart(dataBuffer)
    -- 局数
    --PassRoom:getInstance().m_tabPriData.dwPlayCount = PassRoom:getInstance().m_tabPriData.dwPlayCount + 1

    self.m_cbGameStatus = cmd.GS_TK_SCORE
    if self._gameView._priView then
        self._gameView._priView:onRefreshInviteBtn()
    end

    local int64 = Integer64:new()
    local lTurnMaxScore = dataBuffer:readscore(int64):getvalue()
    self.wBankerUser = dataBuffer:readword()
    print('cur bankuser is ', self.wBankerUser)
    self._gameView:setBankerUser(self:SwitchViewChairID(self.wBankerUser))

    self._gameView:setTurnMaxScore(lTurnMaxScore)

    if self.wBankerUser == self:GetMeChairID() then
        return
    end

    --print('yl.IS_REPLAY_MODEL onSubGameStart start')
    self:perform(function ( )
        --print('yl.IS_REPLAY_MODEL onSubGameStart', self.isSendCardRunning)
        if self.isSendCardRunning == false then
            self:stop(GameLayer.SendCardActionTag)
            self._gameView:gameStart(self:SwitchViewChairID(self.wBankerUser))
            self._gameView:setClockPosition()
            self:SetGameClock(self.wBankerUser, cmd.IDI_TIME_USER_ADD_SCORE, cmd.TIME_USER_ADD_SCORE)
            self._gameView:hideCallBankerBtn()
        end
        
    end, 0.01, -1, GameLayer.SendCardActionTag )
    
    
end

--用户下注
function GameLayer:onSubAddScore(dataBuffer)
    local int64 = Integer64:new()
    local wAddScoreUser = dataBuffer:readword()
    local lAddScoreCount = dataBuffer:readscore(int64):getvalue()

    --[[
    if wAddScoreUser ~= self:GetMeChairID() then
        return
    end
    --]]
    self._gameView:hideCallBankerBtn()
    local userViewId = self:SwitchViewChairID(wAddScoreUser)
    print("onSubAddScore",wAddScoreUser,  userViewId, lAddScoreCount)
    self._gameView:gameAddScore(userViewId, lAddScoreCount)

    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/ADD_SCORE.WAV")
end

--发牌消息
function GameLayer:onSubSendCard(dataBuffer)
    self.isSendCardRunning = true
    for i = 1, cmd.GAME_PLAYER do
        self.cbCardData[i] = {}
        for j = 1, cmd.GAME_CARD_NUM do
            self.cbCardData[i][j] = dataBuffer:readbyte()
        end
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = dataBuffer:readbyte()
    end
    local wCurrent = dataBuffer:readword()

    self.cbPlayCount = dataBuffer:readbyte()
    if wCurrent ~= self:GetMeChairID() then
        return
    end
    self._gameView:showCurJushu()

    local self_chair_id = self:GetMeChairID() + 1
    --打开自己的牌  
    for i = 1, cmd.GAME_CARD_NUM do
        if self:isMingPaiBanker() and i == cmd.GAME_CARD_NUM then
            break
        end
        local data = self.cbCardData[self_chair_id][i]
        local value = GameLogic:getCardValue(data)
        local color = GameLogic:getCardColor(data)
        local card = self._gameView.nodeCard[cmd.MY_VIEWID][i]
        self._gameView:setCardTextureRect(cmd.MY_VIEWID, i, value, color)
    end
    --[[
    if self:isMingPaiBanker() then
        for i = 1, cmd.GAME_PLAYER do repeat
            if i == self_chair_id then
                break
            end
            local wViewChairId = self:SwitchViewChairID(i - 1)
            if wViewChairId == nil or wViewChairId == yl.INVALID_CHAIR then
                break
            end
            for j = 1, cmd.GAME_CARD_NUM - 1 do
                 
                local data = self.cbCardData[i][j]
                local value = GameLogic:getCardValue(data)
                local color = GameLogic:getCardColor(data)
                local card = self._gameView.nodeCard[wViewChairId][j]
                if wViewChairId == 4 or wViewChairId == 5 then
                    card:setLocalZOrder(6 - j)
                end
                self._gameView:setCardTextureRect(wViewChairId, j, value, color)
            end
        until true
        end
    end
    --]]
    
    self._gameView:gameSendCard(cmd.MY_VIEWID, self:getPlayNum()*5)
    --self._gameView:gameSendCard(self:SwitchViewChairID(self.wBankerUser), self:getPlayNum()*5)
    --self:KillGameClock()
end

function GameLayer:onStartCompare(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    self._gameView:hideChip()
    if wPlayerID == self:GetMeChairID() then
        self._gameView:showOpenCardButton()
        self._gameView:setClockPosition()
        self:SetGameClock(self:GetMeChairID(), cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
        
    end

end

--用户摊牌
function GameLayer:onSubOpenCard(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    local bOpen = dataBuffer:readbyte()
    self.cardType = {}
    for i = 1, cmd.GAME_PLAYER do
        self.cardType[i] = dataBuffer:readword()
    end

    self:KillGameClock()
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    if wViewChairId == cmd.MY_VIEWID then
        self:openCard(wPlayerID)
        self._gameView:hideOpenCardBtn()
    else
        self._gameView:setOpenCardVisible(wViewChairId, true)
    end
    
    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/OPEN_CARD.wav")
end

function GameLayer:isMingPaiBanker()
    local room_data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[ GlobalUserItem.nCurGameKind ]
    local rule_arr = room_data.cbGameRule[1]
    if rule_arr[3] == 1 then
        return true
    end
    return false
end

--用户强退
function GameLayer:onSubPlayerExit(dataBuffer)
    local wPlayerID = dataBuffer:readword()
    local wViewChairId = self:SwitchViewChairID(wPlayerID)
    self.cbPlayStatus[wPlayerID + 1] = 0
    self._gameView.nodePlayer[wViewChairId]:setVisible(false)
    self._gameView.bCanMoveCard = false
    self._gameView.btOpenCard:setVisible(false)
    --self._gameView.btPrompt:setVisible(false)
    self._gameView.spritePrompt:setVisible(false)
    self._gameView.spriteCardBG:setVisible(false)
    self._gameView:setOpenCardVisible(wViewChairId, false)
end

--游戏结束
function GameLayer:onSubGameEnd(dataBuffer)
    local int64 = Integer64:new()

    self.cbIsJieSan = dataBuffer:readbyte()
    local pre_player_num = self:getPlayNum()
    print('self.m_userRecord is , ', pre_player_num)
    self.m_userRecord = {}
	for i = 1, pre_player_num do
		self.m_userRecord[i] = {}
		self.m_userRecord[i].cbHuCount = 0
    end
    
    

    local lGameTax = {}
    for i = 1, cmd.GAME_PLAYER do
        lGameTax[i] = dataBuffer:readscore(int64):getvalue()
    end

    local lGameScore = {}
    for i = 1, cmd.GAME_PLAYER do repeat
        lGameScore[i] = dataBuffer:readscore(int64):getvalue()
        if self.cbIsJieSan == 1 then
            break
        end
        if self.cbPlayStatus[i] == 1 then
            local wViewChairId = self:SwitchViewChairID(i - 1)
            self._gameView:setUserTableScore(wViewChairId, lGameScore[i])
            self._gameView:runWinLoseAnimate(wViewChairId, lGameScore[i])
        end
    until true
    end
    --开牌
    
    for i = 1, cmd.GAME_PLAYER do repeat
        local data = {}
        for j = 1, cmd.GAME_CARD_NUM do
            data[j] = dataBuffer:readbyte()
        end
         
        if self.cbIsJieSan == 1 then
            break
        end

        if self.cbPlayStatus[i] == 1 then
            self:openCard(i - 1, data)
        end
    until true
    end

    for i = 1, cmd.GAME_PLAYER do
        self.cbPlayStatus[i] = 0
    end

    if self.cbIsJieSan == 1 then
        return
    end

    if yl.IS_REPLAY_MODEL then
        return
    end

    local index = self:GetMeChairID() + 1
    self._gameView:gameEnd(lGameScore[index] > 0)
    
    self._gameView:setClockPosition(cmd.MY_VIEWID)
    self:SetGameClock(self:GetMeChairID(), cmd.IDI_START_GAME, cmd.TIME_USER_START_GAME)
    AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_END.WAV")

end

function GameLayer:getDetailScore()
	return self.m_userRecord
end

function GameLayer:getUserInfoByChairID(chairId)
	local viewId = self:SwitchViewChairID(chairId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:getUserInfoByViewID(viewId)
	return self._gameView.m_sparrowUserItem[viewId]
end

function GameLayer:onExitRoom()
    self:startOrStopHeartBeat( false )
    self._gameFrame:onCloseSocket()
    self:stopAllActions()
    self:KillGameClock()
    self:dismissPopWait()
    self:stop()
    --self._scene:onChangeShowMode(yl.SCENE_ROOMLIST)
    self._scene:onExitRoom()
    --回放回退的 时候 设置 操作层
    if yl.IS_REPLAY_MODEL then
        local view = helper.app.getFromScene('subScoreLayer')
        print('回放回退的 时候 设置 操作层', view)
        if view then
            PassRoom:getInstance():setViewFrame( view )
        end 
    end
    self:removeFromParent()
end

-- 开始或者关闭回放定时器
function GameLayer:startOrStopReplay( status )
    if status then
       self:perform( handler(self, self.nextReplayStep), 0.01, -1, yl.ActionTag.REPLAY )
    else
       print('stop.............replay')
       self:stop( yl.ActionTag.REPLAY )
    end
end

-- 下一步回放
function GameLayer:nextReplayStep()
   
    --if not self.isSendCardRunning or self.isSendCardRunning == false then
        --print('self._gameView.bCanNextReplay is ', self._gameView.bCanNextReplay)
        if not PassRoom:getInstance():getNetFrame():doNextReplay() then
            self:startOrStopReplay( false )
        end
    --end
     
end

-- 初始化心跳包
function GameLayer:startOrStopHeartBeat( status )
    if status then
        if not yl.IS_REPLAY_MODEL then
            self:stop( yl.ActionTag.HEART )
            
            self:perform( handler(self, self.checkHeartBeat), 5, -1, yl.ActionTag.HEART )
        end
    else
        print('stop.............HeartBeat')
        self:stop( yl.ActionTag.HEART )
    end
end


-- 检测心跳
function GameLayer:checkHeartBeat()
    --print('心跳检测...')
    if not cs.app.room_frame:isSocketServer() then
        self:doReConnect()
    end
end


-- 重连
function GameLayer:doReConnect()
    print('网络重连。。。')
    helper.pop.waiting({true, 'reconect', 10, yl.LoadingTypes.RECONECT, cc.p(0, 100) })
    PassRoom:getInstance():getNetFrame():onCloseSocket()
    PassRoom:getInstance():getNetFrame():onSearchRoom( PassRoom:getInstance().m_tabPriData.szServerID )
end

-- 检测socket是否正常
function GameLayer:startCheckIsSocketOK( status )
    ----[[
    if yl.IS_REPLAY_MODEL then
        return
    end
    if status then
        self.check_is_socket_ok_times = 0
        print('start.............CheckIsSocketOK')
        self:startCheckIsSocketOK(false)
        self:perform( handler( self, self.doCheckIsSocketOK ), 1, -1, yl.ActionTag.CHECKSOCKET )
        self:doCheckIsSocketOK()
    else
        print('stop.............CheckIsSocketOK')
        self:stop( yl.ActionTag.CHECKSOCKET )
    end
    ----]]
end

-- 刷新房卡数据
function GameLayer:updatePriRoom()
    if PassRoom then
        if nil ~= self._gameView._priView and nil ~= self._gameView._priView.onRefreshInfo then
            self._gameView._priView:onRefreshInfo()
        end
    end
end


-- 检查 socket是否正常 5 次机会
function GameLayer:doCheckIsSocketOK()
    print('检测...')
    self.check_is_socket_ok_times = self.check_is_socket_ok_times + 1
    if self.check_is_socket_ok_times > 5 then
        self:startCheckIsSocketOK( false )
        if cs.app.room_frame:isSocketServer() then
            cs.app.room_frame:onCloseSocket()
            self:doReConnect()
        end
        return
    end
    self._gameFrame:sendCheckIsSocketOK()
end

-- 刷新位置 
function GameLayer:onCheckSocketIsOK()
    self:startCheckIsSocketOK( false )
    self.check_is_socket_ok_times = 0
 end


--开始游戏
function GameLayer:onStartGame()
    -- body
    --self.m_cbGameStatus = cmd.GAME_SCENE_PLAY
    print('game start is ')
    self:KillGameClock()
    self._gameView:onResetView()
    self._gameFrame:SendUserReady()
end

--将视图id转换为普通id
function GameLayer:isPlayerPlaying(viewId)
    if viewId < 1 or viewId > cmd.GAME_PLAYER then
        print("view chair id error!")
        return false
    end

    for i = 1, cmd.GAME_PLAYER do
        if self:SwitchViewChairID(i - 1) == viewId then
            if self.cbPlayStatus[i] == 1 then
                return true
            end
        end
    end

    return false
end

function GameLayer:sendCardFinish()
    self.isSendCardRunning = false
    self._gameView.bCanNextReplay = true
    --self._gameView:setClockPosition()
    --self:SetGameClock(self:GetMeChairID(), cmd.IDI_TIME_OPEN_CARD, cmd.TIME_USER_OPEN_CARD)
end

function GameLayer:openCard(chairId, cbCardData)
    --排列cbCardData
    local index = chairId + 1
    local tmpCards = GameLogic:copyTab(self.cbCardData[index])
    if cbCardData then
        tmpCards = GameLogic:copyTab(cbCardData)
    end

    GameLogic:getOxCard(tmpCards)
   
    local cbOx = self.cardType[index] --GameLogic:getCardType(self.cbCardData[index])
    print('before card type is ', cbOx)
    if cbOx == GameLogic.OX_FOUR_SAME then
        cbOx = 12
    elseif cbOx == GameLogic.OX_FIVEHUANIU then
        cbOx = 11
    elseif cbOx == GameLogic.OX_FIVEXIAONIU then
        cbOx = 13
    end
     print('after card type is ', cbOx)

    local viewId = self:SwitchViewChairID(chairId)
    for i = 1, cmd.GAME_CARD_NUM do
        local data = tmpCards[i]
        local value = GameLogic:getCardValue(data)
        local color = GameLogic:getCardColor(data)
        local card = self._gameView.nodeCard[viewId][i]
        self._gameView:setCardTextureRect(viewId, i, value, color)
    end

    self._gameView:gameOpenCard(viewId, cbOx)
end

function GameLayer:getMeCardLogicValue(num)
    local index = self:GetMeChairID() + 1

    --此段为测试错误
    if nil == index then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == num then
        showToast(self, "nil == index", 1)
        return false
    end
    if nil == self.cbCardData[index][num] then
        showToast(self, "nil == index", 1)
        return false
    end

    return GameLogic:getCardLogicValue(self.cbCardData[index][num])
end

function GameLayer:getOxCard(cbCardData)
    return GameLogic:getOxCard(cbCardData)
end

--********************   发送消息     *********************--
function GameLayer:onBanker(cbBanker)
    local dataBuffer = CCmd_Data:create(1)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME,cmd.SUB_C_CALL_BANKER)
    dataBuffer:pushbyte(cbBanker)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onAddScore(lScore)
	print("下注金币", lScore)
    local dataBuffer = CCmd_Data:create(8)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_ADD_SCORE)
    dataBuffer:pushscore(lScore)
    return self._gameFrame:sendSocketData(dataBuffer)
end

function GameLayer:onOpenCard()
    local index = self:GetMeChairID() + 1
    local bOx = GameLogic:getOxCard(self.cbCardData[index])
    
    local dataBuffer = CCmd_Data:create(1)
    dataBuffer:setcmdinfo(yl.MDM_GF_GAME, cmd.SUB_C_OPEN_CARD)
    dataBuffer:pushbyte(bOx and 1 or 0)
    return self._gameFrame:sendSocketData(dataBuffer)
end

-- 退出 重连
function GameLayer:exitToMainScene()
    self:onExitRoom()
    local view = helper.app.getFromScene('subRoomResultLayer')
    if view then
        view:removeFromParent()
    end
    view = helper.app.getFromScene('subGameResultLayer')
    if view then
        view:removeFromParent()
    end
end

return GameLayer
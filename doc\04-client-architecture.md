# LHMJ313 客户端架构文档

## 客户端架构概述

LHMJ313 客户端采用分层架构设计，基于 MVC 模式，使用 Lua 脚本进行业务逻辑开发。整体架构分为表现层、业务逻辑层、框架层和系统层。

```
┌─────────────────────────────────────────┐
│              表现层 (UI Layer)           │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   场景管理  │  │    界面组件     │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│           业务逻辑层 (Logic Layer)       │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  游戏逻辑   │  │   系统模块      │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            框架层 (Framework Layer)      │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  MVC 框架   │  │   辅助工具      │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│            系统层 (System Layer)         │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  网络通信   │  │   平台接口      │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 目录结构

### 主要目录
```
client/client/src/
├── app/                    # 应用程序核心
│   ├── models/            # 数据模型
│   ├── views/             # 视图控制器
│   └── MyApp.lua          # 应用程序主类
├── external/              # 外部扩展模块
├── frame/                 # 网络框架
├── header/                # 协议头文件
├── helper/                # 辅助工具类
├── main/                  # 主要场景
├── packages/              # 框架包
├── system/                # 系统模块
└── main.lua               # 程序入口
```

## MVC 架构设计

### 1. 应用程序基类 (AppBase)

#### 配置系统
```lua
-- 客户端配置
self.configs_ = {
    viewsRoot = "client.src.app.views",
    modelsRoot = "client.src.app.models", 
    defaultSceneName = "StartScene",
}
```

#### 视图管理
```lua
-- 创建视图
function AppBase:createView(name)
    for _, root in ipairs(self.configs_.viewsRoot) do
        local packageName = string.format("%s.%s", root, name)
        local view = require(packageName)
        return view:create(self, name)
    end
end

-- 场景切换
function AppBase:enterScene(sceneName, transition, time, more)
    local view = self:createView(sceneName)
    view:showWithScene(transition, time, more)
    return view
end
```

### 2. 视图基类 (ViewBase)

#### 基础功能
- 继承自 `cc.Node`
- 自动事件管理
- 资源绑定机制
- 生命周期管理

```lua
function ViewBase:ctor(app, name)
    self:enableNodeEvents()
    self.app_ = app
    self.name_ = name
    
    -- 检查 CSB 资源文件
    local res = rawget(self.class, "RESOURCE_FILENAME")
    if res then
        self:createResourceNode(res)
    end
    
    if self.onCreate then 
        self:onCreate() 
    end
end
```

## 场景管理系统

### 1. 主要场景

#### 启动场景 (StartScene)
- 应用程序启动入口
- 资源预加载
- 版本检查和更新

#### 登录场景 (LoginScene)
- 用户登录界面
- 多种登录方式支持
- 服务器连接管理

```lua
function LoginScene:initLayer()
    -- 载入主UI
    local main_node = helper.app.loadCSB('Login.csb', true)
    self.main_node = main_node
    self:addChild(main_node)
    
    -- 健康游戏公告
    helper.app.createHealthGame(main_node, cc.p(main_node:getContentSize().width/2, 12))
    
    -- 版本信息
    main_node:child('label_ver'):setString(appdf.app:getVersion())
end
```

#### 主场景 (MainScene)
- 游戏大厅主界面
- 房间管理
- 社交功能
- 活动系统

```lua
function MainScene:onCreate()
    -- 载入主UI
    local main_node = helper.app.loadCSB('MainLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    
    -- 载入底部UI
    local foot_csb = cs.app.IS_GOLD_HALL and 'MainFootGold.csb' or 'MainFoot.csb'
    local foot_node = helper.app.loadCSB(foot_csb)
    foot_node:setName('foot_node')
    foot_node:pos(main_node:size().width/2, 0):addTo(main_node)
end
```

### 2. 场景切换机制

#### 动态加载
```lua
-- 进入游戏场景
function MainScene:enterGame(enter_game, isReplay)
    local module_name = GlobalUserItem.m_tabEnterGame._Module
    
    -- 初始化游戏模块
    cs.app.req(cs.app.GAME_ROOT .. '.' .. module_name .. '.src.init')
    
    -- 创建游戏层
    local GameLayer = cs.app.game('room.GameLayer')
    local game_layer = GameLayer:create(cs.app.room_frame, self)
    
    -- 设置游戏层
    cs.app.room_frame:setViewFrame(game_layer)
    helper.app.addToScene(game_layer)
end
```

## 辅助工具系统

### 1. 应用辅助 (AppHelper)

#### 主要功能
- CSB 文件加载
- 动画创建
- 音效管理
- 场景管理

```lua
-- 加载 CSB 文件
function AppHelper.loadCSB(filename, is_cache)
    local node = cc.CSLoader:createNode(filename)
    if is_cache then
        cc.CSLoader:setRecordJsonPath(true)
    end
    return node
end

-- 创建骨骼动画
function AppHelper.createArmature(name, path, event_callback, do_load)
    if do_load and cc.FileUtils:getInstance():isFileExist(path) then
        ccs.ArmatureDataManager:getInstance():addArmatureFileInfo(path)
    end
    
    local armature = ccs.Armature:create(name)
    if armature then
        armature:getAnimation():setSpeedScale(cs.app.ARMATURE_SPEED)
        if event_callback then
            armature:getAnimation():setMovementEventCallFunc(event_callback)
        end
    end
    return armature
end
```

### 2. 组件辅助 (CompHelper)

#### 主要功能
- 二维码生成
- 自定义控件创建
- UI 组件封装

```lua
-- 创建二维码
function CompHelper.createQr(content, size)
    local node = QrNode:createQrNode(content, size)
    -- 修正二维码位置
    local qr = node:child('_@@_qr_node_name_@@_')
    local qr_real_size = size / node:getScale()
    local x = (node:size().width - qr_real_size) / 2
    local y = node:size().height + (qr_real_size - node:size().height) / 2
    qr:pos(x, y)
    return node
end
```

### 3. 逻辑辅助 (LogicHelper)

#### 主要功能
- 事件监听管理
- 业务逻辑封装
- 数据处理工具

### 4. 布局辅助 (LayoutHelper)

#### 主要功能
- 自动布局
- 响应式设计
- 屏幕适配

## 系统模块

### 1. 网络框架 (Frame)

#### 基础框架 (BaseFrame)
- TCP Socket 封装
- 消息队列管理
- 断线重连机制

#### 房间框架 (RoomFrame)
- 游戏房间网络管理
- 游戏状态同步
- 玩家数据管理

#### 登录框架 (LogonFrame)
- 用户登录验证
- 服务器列表管理
- 账号系统集成

### 2. 数据模型 (Models)

#### 应用数据 (AppDF)
- 全局常量定义
- 屏幕尺寸配置
- 路径配置

```lua
-- 屏幕配置
appdf.WIDTH = 1136
appdf.HEIGHT = 640
appdf.g_scaleY = display.height / appdf.HEIGHT

-- 路径配置
appdf.CLIENT_SRC = "client.src."
appdf.GAME_SRC = "game."
appdf.EXTERNAL_SRC = "client.src.external."
appdf.HEADER_SRC = "client.src.header."
```

#### 版本管理 (Version)
- 版本信息管理
- 更新检查
- 资源版本控制

#### 全局用户 (GlobalUserItem)
- 用户信息管理
- 游戏状态保存
- 配置数据持久化

### 3. 外部模块 (External)

#### 多平台支持 (MultiPlatform)
- 平台特定功能封装
- 第三方 SDK 集成
- 原生接口调用

#### 外部功能 (ExternalFun)
- 工具函数集合
- 通用算法实现
- 数据转换工具

## UI 框架

### 1. CSB 文件系统

#### 文件组织
- 界面文件存储在 `client/res/` 目录
- 支持热更新
- 自动缓存管理

#### 加载机制
```lua
-- 加载 CSB 文件
local main_node = helper.app.loadCSB('MainLayer.csb')

-- 获取子节点
local btn_start = main_node:child('btn_start')

-- 批量获取节点
local btn_guest, btn_weixin, btn_phone = main_node:child('btn_guest,btn_weixin,btn_phone')
```

### 2. 事件系统

#### 事件监听
```lua
-- 添加触摸事件监听
helper.logic.addListenerByName(self, {btn_guest, btn_weixin, btn_phone})

-- 自定义事件处理
function MainScene:onBtnGuest()
    -- 游客登录逻辑
end
```

#### 键盘事件
```lua
-- 返回键处理
helper.app.addKeyPadEvent(self, true)
```

### 3. 动画系统

#### 骨骼动画
- 基于 Cocos Studio 动画
- 支持事件回调
- 可配置播放速度

#### 特效动画
- 粒子系统
- 帧动画
- 缓动动画

## 资源管理

### 1. 搜索路径管理

```lua
-- 基础搜索路径
cc.FileUtils:getInstance():addSearchPath("client/src/")
cc.FileUtils:getInstance():addSearchPath("client/res/")

-- 动态下载路径
cc.FileUtils:getInstance():addSearchPath(device.writablePath.."download/", true)
cc.FileUtils:getInstance():addSearchPath(device.writablePath.."client/res/", true)
```

### 2. 热更新机制

#### 版本检查
- 服务器版本对比
- 增量更新支持
- 断点续传

#### 资源下载
- 后台下载
- 进度显示
- 错误重试

### 3. 缓存管理

#### 纹理缓存
```lua
-- 清理未使用的纹理
cc.Director:getInstance():getTextureCache():removeUnusedTextures()
cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
```

#### 模块缓存
```lua
-- 清理游戏模块
helper.app.cleanPackages('game.' .. game_name .. '.src.')
```

## 性能优化

### 1. 内存管理
- 及时释放未使用资源
- 对象池模式
- 弱引用机制

### 2. 渲染优化
- 批量渲染
- 纹理合并
- 减少 Draw Call

### 3. 脚本优化
- 避免频繁的 table 创建
- 缓存常用对象
- 合理使用局部变量

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

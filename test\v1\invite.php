<?php
//活动接口
require(APPPATH . '/libraries/REST_Controller.php');

require_once(APPPATH . 'libraries/phpqrcode/phpqrcode.php');


class Invite extends REST_Controller
{

    /**
     * 角色信息
     */
    private $_role;

    /**
     * 游戏
     */
    private $_game;

    /**
     * 奖励
     */
    private $_prizes;

    public function __construct()
    {
        parent::__construct();

        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->player_model->set_database($this->_channel['game_id']);

        $this->_game = $this->server_model->get_game_by_id($this->_channel['game_id']);

        // 金币场邀请奖励红包券
        if ($this->_game['game_id'] != 59) {
            $prizes = json_decode($this->_game['invite_prizes'], TRUE);

            if (!is_array($prizes) || empty($prizes)) {
                $this->response(array('code' => 1, 'msg' => '邀请奖励配置异常'));
            }

            $this->_prizes = $prizes;
        }

        $role_id = $this->input->post('uid');

        // 获取角色信息
        $role = $this->player_model->get_role_info($role_id);

        if (!$role) {
            $this->response(array('code' => 1, 'msg' => '角色信息不存在'));
        }

        $this->_role = $role;
    }

    /**
     * 邀请绑定
     */
    public function bind_post()
    {
        date_default_timezone_set('PRC');

        $from_user = $this->input->post('source');

        $arr = explode('_', $from_user);

        $game_id = isset($arr[0]) ? $arr[0] : '';
        $role_id = isset($arr[1]) ? $arr[1] : '';

        if (empty($game_id) || empty($role_id)) {
            $this->response(array('code' => 1, 'msg' => '邀请码无效'));
        }

        if ($game_id != $this->_game['game_id']) {
            $this->response(array('code' => 1, 'msg' => '邀请码无效'));
        }

        if ($role_id == $this->_role['UserID']) {
            $this->response(array('code' => 1, 'msg' => '邀请码对象不能为自己'));
        }

        // 判断用户是否存在
        $this->player_model->set_database($game_id);
        $role = $this->player_model->get_role_info($role_id);

        if (!$role) {
            $this->response(array('code' => 1, 'msg' => '角色不存在'));
        }

        // 金币场绑定逻辑
        if($this->_game['game_id'] == 59) {

            // 判断该🈷️用户下是否有绑定
            $spreaders = $this->player_model->get_role_spreader($this->_role['UserID']);

            if(!$spreaders) {
                $data = array(
                    'role_id' => $this->_role['UserID'],
                    'game_id' => $this->_game['game_id'],
                    'agent_id' => $role['UserID'],
                    'is_play' => $this->_role['PlayTimeCount'] > 0 ? 1 : 0,
                    'bind_time' => time()
                );

                $this->db->insert('role_bind', $data);

                $this->response(array('code' => 0, 'msg' => '邀请绑定成功'));

            } else {
                $this->response(array('code' => 1, 'msg' => '已存在绑定玩家'));
            }

        } else {


            if(in_array($this->_game['game_id'],array(20,37,54)) ) {
                $record = $this->player_model->get_last_login_day($this->_role['UserID']);

                $last_login_date = $record?$record['LogDate']:'';

                $diff_days = 0;

                if($last_login_date) {

                    $diff_days = round((time()-strtotime($last_login_date))/86400);

                }

                if($this->_role['PlayTimeCount'] > 0 && $diff_days < 30) {
                    $this->response(array('code' => 0, 'msg' => '30天内登录过游戏'));
                }
            } else {
                if ($this->_role['PlayTimeCount'] > 0 ) {
                    $this->response(array('code' => 0, 'msg' => '已玩过游戏'));
                }
            }

            // 判断是否绑定过
            if (!$this->player_model->check_role_invite($this->_game['game_id'], $this->_role['UserID'])) {

                $data = array(
                    'from_id' => $role['UserID'],
                    'from_name' => $role['NickName'],
                    'to_id' => $this->_role['UserID'],
                    'to_name' => $this->_role['NickName'],
                    'game_id' => $this->_game['game_id'],
                    'score' => $this->_game['invite_score'],
                    'create_time' => time()
                );

                if ($this->player_model->insert_invite_log($data)) {

                    // 获取邀请的人数
                    $logs = $this->player_model->get_role_invite_logs($this->_game['game_id'], $this->_role['UserID']);

                    foreach ($this->_prizes as $prize) {

                        if (count($logs) == $prize['num']) {
                            // 检查是否已经插入该项记录
                            $log = $this->player_model->get_role_invite_log_by_num($this->_game['game_id'], $this->_role['UserID'], $prize['num']);
                            if (!$log) {
                                $data = array(
                                    'role_id' => $this->_role['UserID'],
                                    'game_id' => $this->_game['game_id'],
                                    'create_time' => time(),
                                    'num' => $prize['num'],
                                    'score' => $prize['score'],
                                );
                                $this->player_model->insert_invite_prize($data);
                            }

                            break;
                        }
                    }

                    $this->response(array('code' => 0, 'msg' => '邀请成功'));
                } else {
                    $this->response(array('code' => 1, 'msg' => '邀请失败'));
                }

            } else {
                $this->response(array('code' => 0, 'msg' => '已被邀请'));
            }

        }




    }

    /**
     * 首页显示
     */
    public function index_post()
    {
        $result = array();

        $logs = $this->player_model->get_role_invite_logs($this->_game['game_id'], $this->_role['UserID']);

        $result['total_num'] = count($logs);

        // 从游戏中读取配置
        $prizes = array();

        $total_score = 0;

        foreach ($this->_prizes as $k => $v) {

            $prize = $this->player_model->get_role_invite_prize_by_num($this->_game['game_id'], $this->_role['UserID'], $v["num"]);

            if ($prize) {
                $v['is_receive'] = $prize['is_receive'] * 1;

                if ($prize['is_receive'] == 1) {
                    $total_score += $prize['score'];
                }
            } else {
                $v['is_receive'] = 0;
            }

            $prizes[$k] = $v;
        }

        $result['prizes'] = $prizes;

        foreach ($logs as $log) {
            if ($log['is_receive'] == 1) {
                $total_score += $log['score'];
            }
        }

        $result['total_score'] = $total_score;

        $url = base_url() . 'down/invite?game_id=' . $this->_game['game_id'] . '&role_id=' . $this->_role['UserID'];

        $result['qrcode_url'] = $url;
        $result['content'] = $this->_game['invite_content'];


        $this->response(array('code' => 0, 'msg' => '', 'data' => $result));
    }

    /**
     * 记录查询
     */
    public function logs_post()
    {
        $logs = $this->player_model->get_role_invite_logs($this->_game['game_id'], $this->_role['UserID']);

        $data = array();

        foreach ($logs as $k => $log) {
            $data[$k]['id'] = $log['id'] * 1;
            $data[$k]['role_id'] = $log['to_id'] * 1;
            $data[$k]['role_name'] = $log['to_name'];
            $data[$k]['create_time'] = $log['create_time'] * 1;
            $data[$k]['is_receive'] = $log['is_receive'] * 1;
        }

        $this->response(array('code' => 0, 'msg' => '', 'data' => $data));
    }

    /**
     * 奖励领取
     */
    public function receive_post()
    {
        $id = $this->input->post('id');
        $type = $this->input->post('type');

        if ($type == 'prize') {
            $prize = $this->server_model->get_invite_prize_by_num($this->_game['game_id'], $this->_role['UserID'], $id);

            if (!$prize) {
                $this->response(array('code' => 1, 'msg' => '记录不存在'));
            }

            if ($prize['is_receive'] == 1) {
                $this->response(array('code' => 1, 'msg' => '奖励已领取'));
            }

            if ($this->server_model->update_invite_prize($prize['id'], array('is_receive' => 1, 'receive_time' => time()))) {


                $this->player_model->update_role_score($this->_role['UserID'], $prize["score"]);
                // 插入记录
                $data = array(
                    'a_id' => 1,
                    'a_name' => 'admin',
                    'b_id' => $this->_role['UserID'],
                    'b_name' => $this->_role['NickName'],
                    'score' => $prize["score"],
                    'type' => 4,
                    'is_order' => 0,
                    'remark' => '邀请累计奖励<ID:' . $prize['id'] . '>',
                    'game_id' => $this->_game['game_id'],
                    'create_time' => date('Y-m-d H:i:s')
                );

                $this->db->insert('recharges', $data);

                $role_score = $this->player_model->get_role_score($this->_role['UserID']);

                $data = array(
                    'total_score' => $role_score['RoomCard'],
                    'invite_score' => $this->_get_invite_score()
                );

                $this->response(array('code' => 0, 'msg' => '领取成功', 'data' => $data), 200);
            } else {
                $this->response(array('code' => 1, 'msg' => '领取失败'));
            }

        } else if ($type == 'log') {
            $log = $this->server_model->get_invite_log_by_id($id);

            if (!$log) {
                $this->response(array('code' => 1, 'msg' => '记录不存在'));
            }

            if ($log['is_receive'] == 1) {
                $this->response(array('code' => 1, 'msg' => '奖励已领取'));
            }

            if ($log['is_play'] == 0) {
                $this->response(array('code' => 1, 'msg' => '无效邀请玩家'));
            }

            if ($this->server_model->update_invite_log($id, array('is_receive' => 1, 'receive_time' => time()))) {

                $this->player_model->update_role_score($this->_role['UserID'], $log["score"]);
                // 插入记录
                $data = array(
                    'a_id' => 1,
                    'a_name' => 'admin',
                    'b_id' => $this->_role['UserID'],
                    'b_name' => $this->_role['NickName'],
                    'score' => $log["score"],
                    'type' => 4,
                    'is_order' => 0,
                    'remark' => '邀请奖励<ID:' . $log['id'] . '>',
                    'game_id' => $this->_game['game_id'],
                    'create_time' => date('Y-m-d H:i:s')
                );

                $this->db->insert('recharges', $data);


                $role_score = $this->player_model->get_role_score($this->_role['UserID']);

                $data = array(
                    'total_score' => $role_score['RoomCard'],
                    'invite_score' => $this->_get_invite_score()
                );

                $this->response(array('code' => 0, 'msg' => '领取成功', 'data' => $data), 200);
            } else {
                $this->response(array('code' => 1, 'msg' => '领取失败'));
            }

        }

        $this->response(array('code' => 1, 'msg' => '未知的类型'));


    }

    /**
     * 获取邀请的房卡
     */
    private function _get_invite_score()
    {
        $score = 0;

        foreach ($this->_prizes as $k => $v) {

            $prize = $this->player_model->get_role_invite_prize_by_num($this->_game['game_id'], $this->_role['UserID'], $v["num"]);

            if ($prize) {

                if ($prize['is_receive'] == 1) {
                    $score += $prize['score'];
                }
            }

        }

        $logs = $this->player_model->get_role_invite_logs($this->_game['game_id'], $this->_role['UserID']);

        foreach ($logs as $log) {
            if ($log['is_receive'] == 1) {
                $score += $log['score'];
            }
        }

        return $score;
    }


}
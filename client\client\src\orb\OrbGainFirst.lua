-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 首次拆红包
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbGainFirst = class('OrbGainFirst', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbGainFirst:ctor(panel)
    print('OrbGainFirst:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    self.m_panel:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
end


-------------------------------------------------------------------------------
-- 显示
-------------------------------------------------------------------------------
function OrbGainFirst:onShow()
    if not self.m_avatar_drawed then
        self.m_avatar_drawed = true
        local icon = self.m_panel:child('bg_avatar')
        local head = self:createHead(GlobalUserItem.dwUserID, GlobalUserItem.szHeadHttp, icon:size().width, 'orb/orb_bg_avatar_87.png')
        head:pos(icon:pos()):addTo(self.m_panel)
        icon:hide()
    end
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function OrbGainFirst:onBtnClose()
    self:closeTop()
end


-------------------------------------------------------------------------------
-- 分享钮点击
-------------------------------------------------------------------------------
function OrbGainFirst:onBtnShare()
    self:share(function()
        self:request('open', {is_share = 1}, function(data, response, http_status)
            if not helper.app.urlErrorCheck(data, response, http_status) then
                return
            end
            self:updateData({balance = data.data.balance, status = data.data.status})
            self:addOpenLog(data.data.log)
            OrbUtil.close('panel_first')
            self:close()
            self:open('panel_main')
            self:open('panel_first_share')
        end)
    end, true)
end


return OrbGainFirst
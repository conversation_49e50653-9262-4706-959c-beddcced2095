-------------------------------------------------------------------------------
--  创世版1.0
--  大转盘
--  @date 2017-08-31
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')
local EditBox = cs.app.client('system.EditBox')


local TurntableLayer = class("TurntableLayer", cc.Layer)

local this = nil
local ITEM_COUNT = 12
local ITEM_TYPE_REDBAG = 2
local ITEM_TYPE_REAL = 3
local TAG_LIGHT_ACTION = 1100
local TAG_LIGHT_SPEED = 1101
local TAG_PLATE_ACTION = 1102
local TAG_PLATE_SPEED = 1103

-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function TurntableLayer:ctor()
    print('TurntableLayer:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('TurntableLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.m_panel_result, self.m_panel_info = main_node:child('panel_result, panel_info')
    self.m_panel_result:hide()
    self.m_panel_info:hide()
    local input_name, input_phone, input_address = self.m_panel_info:child('bg'):child('input_name, input_phone, input_address')
    EditBox.convertTextField(input_name, 'turn_bg_input.png')
    EditBox.convertTextField(input_phone, 'turn_bg_input.png')
    EditBox.convertTextField(input_address, 'turn_bg_input.png')

    self.m_panel_plate, self.m_panel_rule, self.m_panel_record = main_node:child('panel_plate, panel_rule, panel_record')
    self.m_panel_plate:scale(0)
    self.m_panel_rule:px(self.m_panel_rule:px() + 600)
    self.m_panel_record:px(self.m_panel_record:px() + 600)

    self.m_panel_record:child('panel_all/row_template'):hide()
    self.m_panel_record:child('panel_my/row_template'):hide()
    self.m_panel_result:child('bg/panel_share'):hide()

    self.m_plate = self.m_panel_plate:child('plate_wrap/plate')    -- 转盘（旋转部分）
    self.m_plate:rotation(15)   -- 指向第一格
    self.m_plate:child('item_template'):hide():setBackGroundColorType( ccui.LayoutBackGroundColorType.none )

    self.m_lights = {}  -- 转盘边缘的灯
    for i = 1, 18 do
        local light = self.m_panel_plate:child('light_' .. i)
        light.is_light = false
        table.insert(self.m_lights, light)
    end
    self:lightRandom()

    self.m_panel_record:child('panel_all'):setCascadeOpacityEnabled(true)
    self.m_panel_record:child('panel_my'):setCascadeOpacityEnabled(true)
    self:onBtnMy()

    this = self
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    self.m_panel_plate:child('point'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnGo) )
    self.m_panel_result:child('bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnResultClose) )
    self.m_panel_result:child('bg/btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnGet) )
    self.m_panel_result:child('bg/panel_share/btn_share'):addTouchEventListener( helper.app.commClickHandler(this, this.onBtnShareResult) )
    self.m_panel_info:child('bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnInfoClose) )
    self.m_panel_info:child('bg/btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnInfoOK) )
    helper.logic.addListenerByName(self, {self.m_panel_record:child('btn_all, btn_my')}, {'tint', 'tint'})
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function TurntableLayer:onEnterTransitionFinish()
    print('TurntableLayer:onEnterTransitionFinish...')
    self:requestPrizes()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function TurntableLayer:onExit()
    print('TurntableLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 请求奖品列表
-------------------------------------------------------------------------------
function TurntableLayer:requestPrizes()
    helper.pop.waiting()
    yl.GetUrl(yl.URL_TURNTABLE, 'post', {uid=GlobalUserItem.dwUserID}, handler(self, self.onPrizesRespose) )
end


-------------------------------------------------------------------------------
-- 添加记录
-------------------------------------------------------------------------------
function TurntableLayer:addLog(log, to_head, template, listview)
    local row = template:clone():show()
    row.log = log
    local items = listview:getItems()
    row:child('bg'):setVisible(#items > 0 and not items[to_head and 1 or #items]:child('bg'):isVisible())
    row:child('label_rwd'):setString(log.item_name .. ' x' .. log.item_num)
    row:child('label_time'):setString( helper.time.format(log.draw_time, '%m-%d %H:%M') )

    if to_head then
        listview:insertCustomItem(row, 0)
    else
        listview:pushBackCustomItem(row)
    end

    return row
end


-------------------------------------------------------------------------------
-- 添加全部记录
-------------------------------------------------------------------------------
function TurntableLayer:addAllLog(log, to_head, template, listview)
    local template = template or self.m_panel_record:child('panel_all/row_template')
    local listview = listview or self.m_panel_record:child('panel_all/listview')
    local row = self:addLog(log, to_head, template, listview)
    row:child('label_name'):setString(log.role_name)
end


-------------------------------------------------------------------------------
-- 添加自己记录
-------------------------------------------------------------------------------
function TurntableLayer:addMyLog(log, to_head, template, listview)
    local template = template or self.m_panel_record:child('panel_my/row_template')
    local listview = listview or self.m_panel_record:child('panel_my/listview')
    local row = self:addLog(log, to_head, template, listview)
    row:child('btn_view'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnView) )
    self:updateMyLogStatus(row, log)
end


-------------------------------------------------------------------------------
-- 更新次数
-------------------------------------------------------------------------------
function TurntableLayer:updateTimes(times)
   self.m_panel_plate:child('point/label_times'):setString( LANG{'LEFT_TIMES', times=times} )
    if times == 0 then
        self.m_panel_plate:child('point'):setTouchEnabled(false)
    end
end


-------------------------------------------------------------------------------
-- 基本信息返回
-------------------------------------------------------------------------------
function TurntableLayer:onPrizesRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    data = data.data
    self.m_prizes = data.prizes
    self.m_panel_rule:child('label_rule'):setRich(data.content)
    self.m_panel_rule:child('label_progress'):setString( string.format('%d/%d', data.progress_value, tonumber(data.progress_max) ) )
    self.m_panel_rule:child('loading'):setPercent(data.progress_value * 100 / tonumber(data.progress_max))

    self:updateTimes(data.remain_draws)

    -- 转盘上的物品，延时，防止动画出现卡顿
    self:perform(function() self:createPlateItems(data.prizes) end, 0.4)

    -- 所有记录
    local template = self.m_panel_record:child('panel_all/row_template')
    local listview = self.m_panel_record:child('panel_all/listview')
    listview:removeAllItems()
    for i, log in ipairs(data.all_logs) do
        self:addAllLog(log, false, template, listview)
    end

    -- 自己记录
    template = self.m_panel_record:child('panel_my/row_template')
    listview = self.m_panel_record:child('panel_my/listview')
    listview:removeAllItems()
    for i, log in ipairs(data.my_logs) do
        self:addMyLog(log, false, template, listview)
    end

    if not self.m_after_prizes then
        self.m_panel_plate:runMyAction(cc.Spawn:create(cc.RotateBy:create(0.3, 360), cc.ScaleTo:create(0.3, 1)))
        self.m_panel_rule:runMyAction(cc.EaseBackOut:create(cc.MoveBy:create(0.3, cc.p(-600, 0))))
        self.m_panel_record:runMyAction(cc.Sequence:create(cc.DelayTime:create(0.1), cc.EaseBackOut:create(cc.MoveBy:create(0.3, cc.p(-600, 0)))))

    else
        self:perform(function()
            self.m_after_prizes()
            self.m_after_prizes = nil
        end, 0.5)
    end
end


-------------------------------------------------------------------------------
-- 创建转盘物品项
-------------------------------------------------------------------------------
function TurntableLayer:createPlateItems(items)
    for i = 1, ITEM_COUNT do
        self.m_plate:removeChildByTag(i)
    end
    self.m_file_images = {}
    local template = self.m_plate:child('item_template')
    for i = 1, math.min(ITEM_COUNT, #items) do
        local item_data = items[i]
        local save_path, save_name, use_path = helper.app.getDownloadParam('turntable', item_data.icon)
        item_data.use_path = use_path

        local item = template:clone():show()
        item:setTag(i)
        item:child('name'):setString(item_data.name .. 'x' .. item_data.num)

        local function add(node)
            if tolua.isnull(node) then return end
            local image = ccui.ImageView:create()
            image:texture(use_path)
            helper.layout.addCenter(node:child('place'), image)
        end

        -- 必须判断文件大小，如果多项文件名相同，则开始下载时文件会存在
        if cc.FileUtils:getInstance():isFileExist(use_path) and cc.FileUtils:getInstance():getFileSize(use_path) > 0 then
            add(item)
        else
            -- 确保相同的文件只下载一次
            if not self.m_file_images[save_name] then
                self.m_file_images[save_name] = {}
 
                local function callback(filename)
                    local t = self.m_file_images[filename]
                    for i, node in ipairs(t) do
                        add(node)
                    end
                end
                helper.app.download(item_data.icon, save_path, save_name, self, callback)
            end
            table.insert(self.m_file_images[save_name], item)
        end

        item:rotation((i - 1) * -30 - 15)
        item:addTo(self.m_plate)
    end
end


-------------------------------------------------------------------------------
-- 更新我的记录状态
-------------------------------------------------------------------------------
function TurntableLayer:updateMyLog(id, log)
    local listview = self.m_panel_record:child('panel_my/listview')
    for i, item in ipairs(listview:getItems()) do
        if item.log.id == id then
            self:updateMyLogStatus(item, log)
        end
    end
end


-------------------------------------------------------------------------------
-- 更新我的记录状态
-------------------------------------------------------------------------------
function TurntableLayer:updateMyLogStatus(row, log)
    row.log = log
    local status = log.status
    row:child('btn_view'):setVisible(status < 2)
    row:child('label_status'):setVisible(status == 2)
    if log.item_type == ITEM_TYPE_REAL and status == 1 then
        row:child('btn_view'):child('text'):setString( LANG.TURNTABLE_VIEW )
    end
end


-------------------------------------------------------------------------------
-- 抽奖返回
-------------------------------------------------------------------------------
function TurntableLayer:onDrawRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then
        self.m_panel_plate:child('point'):setTouchEnabled(true)
        return
    end

    self.m_draw_result = data.data

    -- 检查奖品是否在之前的列表中的同一位置，如果不是需要刷新列表
    local found = false
    local prize = data.data.prize
    for i, p in ipairs(self.m_prizes) do
        if p.item_id == prize.item_id and i == prize.index + 1 then
            found = true
            break
        end
    end
    if not found then
        self.m_after_prizes = function()
            self:go(self.m_draw_result.prize.index % ITEM_COUNT + 1)
        end
        self:requestPrizes()
    else
        self:go(self.m_draw_result.prize.index % ITEM_COUNT + 1)
    end

    -- 发送公告命令
    if prize.item_type == ITEM_TYPE_REAL or prize.item_type == ITEM_TYPE_REDBAG then
        local msg = LANG{'TURNTABLE_GLOBAL', user=GlobalUserItem.szNickName, prize=prize.name, num=prize.num}
        local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_IDMsg, {dwID = 1, szMsg = msg} )
        cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_GLOBAL_REWARD_MSG)
        LiveFrame:getInstance():send(cmd_data)
    end
end


-------------------------------------------------------------------------------
-- 显示抽奖结果
-------------------------------------------------------------------------------
function TurntableLayer:showResult()
    self:addMyLog(self.m_draw_result.log, true)
    self:addAllLog(self.m_draw_result.log, true)
    local index = self.m_draw_result.prize.index + 1
    local prize = self.m_prizes[index]
    self.m_draw_result.prize.use_path = prize.use_path
    self:doShowResult(self.m_draw_result.prize)
end


-------------------------------------------------------------------------------
-- 显示抽奖结果
-------------------------------------------------------------------------------
function TurntableLayer:doShowResult(prize)
    self.m_share_prize = prize
    local panel = self.m_panel_result:show()
    local bg = panel:child('bg')
    bg:child('img_icon'):ignoreContentAdaptWithSize(true)
    if prize.use_path then
        bg:child('img_icon'):texture(prize.use_path)
    end
    bg:child('label_name'):setString((prize.item_name or prize.name) .. ' x' .. (prize.num or prize.item_num))
    if prize.item_type == ITEM_TYPE_REAL or prize.item_type == ITEM_TYPE_REDBAG then
        bg:child('btn_ok'):hide()
        bg:child('panel_share'):show()
    else
        bg:child('btn_ok'):show()
        bg:child('panel_share'):hide()
        bg:child('btn_ok/font'):texture(prize.item_type == ITEM_TYPE_REAL and '#turn_font_write.png' or 'word/font_btn_ok_big.png')
    end

    bg:scale(0.8):runMyAction( cc.EaseBackOut:create( cc.ScaleTo:create(0.3, 1) ) )
end


-------------------------------------------------------------------------------
-- 抽奖结果关闭按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnResultClose(sender)
    self.m_panel_result:hide()
end


-------------------------------------------------------------------------------
-- 抽奖结果分享按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnShareResult(sender)
    self:doShare(self.m_share_prize)
end


-------------------------------------------------------------------------------
-- 抽奖结果分享
-------------------------------------------------------------------------------
function TurntableLayer:doShare(prize)
    local save_path, save_name, use_path = helper.app.getDownloadParam('turntablebig', prize.big_icon)
    local function callback(filename)
        helper.pop.waiting(false)
        local image_path = 'myturntableshare.jpg'

        local bg = display.newSprite('common/bg_turntable_share.jpg')
        local icon = display.newSprite(use_path)
        local label_name = ccui.Text:create((prize.item_name or prize.name) .. ' x' .. (prize.num or prize.item_num), cs.app.FONT_NAME, 60)
        label_name:setTextColor( cc.c3b(193, 0, 0) )
        label_name:setTextHorizontalAlignment( cc.TEXT_ALIGNMENT_CENTER )

        local size = bg:size()
        local w, h = size.width, size.height
        local render = cc.RenderTexture:create(w, h)
        render:begin()

        bg:pos(w * 0.5, h * 0.5)
        bg:visit()

        icon:pos(w * 0.5, h * 0.5)
        icon:visit()

        label_name:pos(w * 0.5, 380)
        label_name:visit()

        render:endToLua()
        render:saveToFile(image_path, cc.IMAGE_FORMAT_JPEG)

        -- 在saveToFile模式下，需要下一帧文件才会生成
        self:perform(function()
            local layer = helper.pop.shareImage(image_path, false, false, false, function()
                self.m_panel_result:hide()
                self:doGet(prize)
            end)
            layer:showButtons('pyq')
            layer:hide():doShare(yl.ThirdParty.WECHAT_CIRCLE)
        end, 0.1)
    end

    -- 大图标下载，必须判断文件大小，如果多项文件名相同，则开始下载时文件会存在
    if not cc.FileUtils:getInstance():isFileExist(use_path) or cc.FileUtils:getInstance():getFileSize(use_path) == 0 then
        helper.pop.waiting()
        helper.app.download(prize.big_icon, save_path, save_name, self, callback)
    else
        callback()
    end
end


-------------------------------------------------------------------------------
-- 抽奖结果领取按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnGet(sender, prize)
    self:doGet(self.m_draw_result.prize)
end


-------------------------------------------------------------------------------
-- 领取
-------------------------------------------------------------------------------
function TurntableLayer:doGet(prize)
    if prize.item_type == ITEM_TYPE_REAL then
        self.m_panel_info:show()
        self.m_panel_info:child('bg/btn_ok').prize_id = prize.id
        self.m_panel_info:child('bg'):scale(0.8):runMyAction( cc.EaseBackOut:create( cc.ScaleTo:create(0.3, 1) ) )
    else
        helper.pop.waiting()
        yl.GetUrl(yl.URL_TURNTABLE_RECEIVE, 'post', 
            {uid=GlobalUserItem.dwUserID, id=prize.id},
            handler(self, self.onReceiveRespose) )
    end
end


-------------------------------------------------------------------------------
-- 地址填写关闭按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnInfoClose(sender)
    self.m_panel_info:hide()
end


-------------------------------------------------------------------------------
-- 地址填写确定按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnInfoOK(sender)
    local input_name, input_phone, input_address = self.m_panel_info:child('bg'):child('input_name,input_phone,input_address')
    local name = input_name:getString():trim()
    local phone = input_phone:getString():trim()
    local address = input_address:getString():trim()
    if name == '' or phone == '' or address == '' then
        helper.pop.message( LANG.TURNTABLE_INPUT_HINT )
        return
    end

    local prize_id = sender.prize_id
    local params = {uid=GlobalUserItem.dwUserID, id=prize_id, name=name, phone=phone, address=address}
    helper.pop.waiting()
    yl.GetUrl(yl.URL_TURNTABLE_RECEIVE, 'post', params, handler(self, self.onReceiveRespose) )
end


-------------------------------------------------------------------------------
-- 领取返回
-------------------------------------------------------------------------------
function TurntableLayer:onReceiveRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then
        return
    end

    helper.pop.message( LANG.TURNTABLE_SUCCESS )
    self.m_panel_result:hide()
    self.m_panel_info:hide()

    if data.data.fangka or data.data.jiangquan then
        PassRoom:getInstance():getPlazaScene():updateFangka( tonumber(data.data.fangka), nil, tonumber(data.data.jiangquan) )
    end

    self:updateMyLog(data.data.prize.id, data.data.prize)
end


-------------------------------------------------------------------------------
-- 显示实物信息输入界面
-------------------------------------------------------------------------------
function TurntableLayer:showInput(prize_id)
    local panel = self.m_panel_info:show()
    local bg = panel:child('bg')
    bg:child('btn_ok').prize_id = prize_id
    bg:scale(0.8):runMyAction( cc.EaseBackOut:create( cc.ScaleTo:create(0.3, 1) ) )
end


-------------------------------------------------------------------------------
-- 亮灯
-------------------------------------------------------------------------------
function TurntableLayer:light(light, b)
    light:texture(b and '#turn_dot_light.png' or '#turn_dot_dark.png')
    light.is_light = b
end


-------------------------------------------------------------------------------
-- 灯随机闪烁
-------------------------------------------------------------------------------
function TurntableLayer:lightRandom()
    self:perform(function()
        local lights = {}
        for i, light in ipairs(self.m_lights) do
            if not light.is_light then
                table.insert(lights, light)
            end
        end
        if #lights == 0 then return end

        local probability = 100 * #lights / #self.m_lights
        local light = lights[math.random(1, #lights)]
        if math.random(1, 100) < probability then
            self:light(light, true)
            light:stop():perform(function(sender) self:light(sender, false) end, 0.5)
        end
    end, 0.1, -1, TAG_LIGHT_ACTION)
end


-------------------------------------------------------------------------------
-- 灯全闪烁
-------------------------------------------------------------------------------
function TurntableLayer:lightBlink(times)
    self:perform(function()
        for i, light in ipairs(self.m_lights) do
            light:stop()
            self:light(light, not light.is_light)
        end
    end, 0.1, times, TAG_LIGHT_ACTION)
end


-------------------------------------------------------------------------------
-- 灯全亮或灭
-------------------------------------------------------------------------------
function TurntableLayer:lightAll(is_light)
    for i, light in ipairs(self.m_lights) do
        self:light(light, is_light)
    end
end


-------------------------------------------------------------------------------
-- 灯滚动特效
-------------------------------------------------------------------------------
function TurntableLayer:lightTurn()
    local index = 0
    local action = cc.RepeatForever:create( cc.Sequence:create(
        cc.DelayTime:create(0.5),
        cc.CallFunc:create(function()
            if index > 1 then
                self:light(self.m_lights[(index - 2) % #self.m_lights + 1], false)
                self:light(self.m_lights[(index - 2 + 9) % #self.m_lights + 1], false)
            end
            self:light(self.m_lights[index % #self.m_lights + 1], true)
            self:light(self.m_lights[(index + 9) % #self.m_lights + 1], true)
            index = index + 1
        end)
    ) )
    local value = 1
    local speed = cc.Speed:create(action, value)
    speed:setTag(TAG_LIGHT_ACTION)
    self:runMyAction(speed)

    local dir = 1
    self:perform(function()
        if value <= 0 then
            self:stop(TAG_LIGHT_SPEED)
            self:lightAll(false)
            self:perform(function() self:lightRandom() end, 1.5)
            return
        end

        value = value + dir * (dir > 0 and 0.5 or 0.17)
        speed:setSpeed(value)
        if value >= 26 then
            dir = -1
        end
    end, 0.05, -1, TAG_LIGHT_SPEED)
end


-------------------------------------------------------------------------------
-- 转盘转动特效
-------------------------------------------------------------------------------
function TurntableLayer:plateTurn(index)
    local rotate = 15 * 360
    local action = cc.Sequence:create(
        cc.EaseInOut:create( cc.RotateBy:create(7, rotate), 4 )
    )
    self.m_plate:getParent():stop():runMyAction(action)

    local at_rotation = (index - 1) * 30 + 15
    local by_rotation = (index - (self.m_stop_index or 1) + ITEM_COUNT ) % ITEM_COUNT * 30
    local rotate2 = 360 + by_rotation
    local action2 = cc.Sequence:create(
        cc.EaseInOut:create( cc.RotateBy:create(10, rotate2), 1.1 ),
        cc.CallFunc:create(function()
            local item = self.m_plate:getChildByTag(index)
            local particle = helper.app.createParticle('common/turntable_get')
            helper.layout.addCenter(item:child('place'), particle)
        end),
        cc.DelayTime:create(1.5),
        cc.CallFunc:create(function(sender)
            self.m_stop_index = index
            sender:rotation(at_rotation)  -- 确保
            self.m_panel_plate:child('point'):setTouchEnabled(true)
            self:updateTimes(self.m_draw_result.remain_draws)
            self:showResult()
        end)
    )
    self.m_plate:runMyAction(action2)
end


-------------------------------------------------------------------------------
-- Go按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnGo(sender)
    sender:setTouchEnabled(false)
    helper.pop.waiting()
    yl.GetUrl(yl.URL_TURNTABLE_DRAW, 'post', {uid=GlobalUserItem.dwUserID}, handler(self, self.onDrawRespose) )
end


-------------------------------------------------------------------------------
-- Go
-------------------------------------------------------------------------------
function TurntableLayer:go(index)
    ---[[
    self:stop(TAG_LIGHT_ACTION)
    self:stop(TAG_LIGHT_SPEED)
    self:runMyAction(cc.Sequence:create(
        cc.CallFunc:create(function() self:lightBlink(2) end),
        cc.DelayTime:create(0.5),
        cc.CallFunc:create(function() self:lightTurn() end)
    ))

    self:plateTurn(index)
    --]]
    local particle = helper.app.createParticle('common/turntable_go')
    helper.layout.addCenter(self.m_panel_plate, particle)
end


-------------------------------------------------------------------------------
-- 记录tab按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:doTabToggle(old_tab, new_tab)
    self.m_panel_record:child('panel_' .. new_tab):show()--stop():fadeTo{time=0.3, opacity=255}
    self.m_panel_record:child('panel_' .. old_tab):hide()--stop():fadeTo{time=0.3, opacity=0}
    self.m_panel_record:child('btn_' .. new_tab):texture('#turn_tab_selected.png')
    self.m_panel_record:child('btn_' .. old_tab):texture('#turn_tab_normal.png')
end


-------------------------------------------------------------------------------
-- 所有记录按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnAll()
    self:doTabToggle('my', 'all')
end


-------------------------------------------------------------------------------
-- 我的记录按钮点击
-------------------------------------------------------------------------------
function TurntableLayer:onBtnMy()
    self:doTabToggle('all', 'my')
end


-------------------------------------------------------------------------------
-- 查看我的记录
-------------------------------------------------------------------------------
function TurntableLayer:onBtnView(sender)
    local log = sender:getParent().log
    if log.item_type == ITEM_TYPE_REAL and log.status == 1 then
        local msg = LANG{'TURNTABLE_DRAW_INFO', name=log.name, phone=log.phone, address=log.address}
        helper.pop.alert(msg)
    elseif log.status == 0 then
        if log.item_type == ITEM_TYPE_REAL or log.item_type == ITEM_TYPE_REDBAG then
            -- 从转盘上获取小图标的use_path
            for i, p in ipairs(self.m_prizes) do
                if p.item_id == log.item_id then
                    log.use_path = p.use_path
                    break
                end
            end
            self:doShowResult(log)
        else
            self:doGet(log)
        end
    end
end


return TurntableLayer
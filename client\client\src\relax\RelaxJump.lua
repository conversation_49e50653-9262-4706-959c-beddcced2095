-------------------------------------------------------------------------------
--  创世版1.0
--  小游戏 - 跑酷
--  @date 2019-05-13
--  @auth woodoo
-------------------------------------------------------------------------------
local JUMP_TIME = 0.3
local JUMP_HEIGHT = 360
local TREE_DISTANCE = 800
local TREE_SCALE = 1


-------------------------------------------------------------------------------
-------------------------------------------------------------------------------
local NodePool = class("NodePool")

function NodePool:ctor(app, name)
    self.templates = {}
    self.pools = {}
end

function NodePool:setTemplate(name, node)
    self.templates[name] = node
    node:hide()
    self.pools[name] = {}
end

function NodePool:get(name)
    local pool = self.pools[name]
    local node
    if #pool == 0 then
        node = self.templates[name]:clone()
    else
        node = pool[1]
        table.remove(pool, 1)
    end
    return node:show()
end

function NodePool:put(name, node)
    local pool = self.pools[name]
    node:hide()
    pool[#pool + 1] = node
end
-------------------------------------------------------------------------------
-------------------------------------------------------------------------------





local RelaxJump = class("RelaxJump", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RelaxJump:ctor()
    print('RelaxJump:ctor')
    self:enableNodeEvents()
    self.drags = {}
    self.trees = {}
    self.birds = {}
    self.tree_pool = {}
    self.high_score = 0
    self.speed_sum = 0
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function RelaxJump:onEnterTransitionFinish()
    print('RelaxJump:onEnterTransitionFinish')
    AudioEngine.playMusic('relax/jump/jump_bgm.mp3', true)
    self:initUI()
    self:registerTouch()
end


-------------------------------------------------------------------------------
-- 启动游戏
-------------------------------------------------------------------------------
function RelaxJump:gameStart()
    self.score = 0
    self.speed_sum = 0
    self.last_tree_at = 0 
    self.is_jumping = false
    self.panel_road:px(0)
    for i, road in ipairs(self.roads) do road:px(self.road_width * (i - 1), 0) end
    self.panel_dragon:pos(self.dragon_pos)
    for i, tree in ipairs(self.trees) do self:cacheTree(tree) end
    for i, bird in ipairs(self.birds) do bird:hide() end
    self.trees = {}
    self.drags.stand:hide()
    self.drags.run:show()
    self.drags.run:resume()
    self.panel_road:resume()
    self.speed:setSpeed(1)
    self:setScore(0)
    self.panel_over:hide()
    self.is_running = true
end

-------------------------------------------------------------------------------
-- 游戏结束
-------------------------------------------------------------------------------
function RelaxJump:gameOver()
    self.is_running = false
    self.panel_dragon:stop()
    self.drags.run:pause()
    self.panel_road:pause()
    self.panel_over:show()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RelaxJump:onExit()
end


-------------------------------------------------------------------------------
-- 注册触摸事件
-------------------------------------------------------------------------------
function RelaxJump:registerTouch()
    self.main_node:child('panel_touch'):addTouchEventListener(handler(self, self.onTouch))
end


-------------------------------------------------------------------------------
-- 触摸事件
-------------------------------------------------------------------------------
function RelaxJump:onTouch(sender, event)
    if not self.inited then return end
    if event == cc.EventCode.BEGAN then
        if not self.is_running then
            self:gameStart()
            return
        end
        self:dragonJump()
    end
end


-------------------------------------------------------------------------------
-- 设置得分
-------------------------------------------------------------------------------
function RelaxJump:setScore(score)
    self.speed_sum = self.speed_sum + score - self.score
    self.score = score
    if score > self.high_score then self.high_score = score end
    self.label_score:setString('HI ' .. self.high_score .. ' ' .. score)

    if self.speed_sum > 50 then
        self.speed_sum = 0
        self.speed:setSpeed(self.speed:getSpeed() + 0.1)
    end
end


-------------------------------------------------------------------------------
-- 创建鸟
-------------------------------------------------------------------------------
function RelaxJump:createBirds()
    for i = 1, 2 do
        local bird = self.panel_bird:clone()
        bird:child('boxes'):zorder(10)

        local anim = helper.app.createAnimation('relax/jump/ani_relax_jump_fly', 25, 0.5, true)
        helper.layout.addCenter(bird, anim)
        bird:hide():addTo(self.panel_tree)
        self.birds[#self.birds + 1] = bird
    end
end


-------------------------------------------------------------------------------
-- 创建恐龙
-------------------------------------------------------------------------------
function RelaxJump:createDragon(sender, event)
    local anim = helper.app.createAnimation('relax/jump/ani_relax_jump_stand', 24, 0.8, true)
    anim:anchor(0.5, 0)
    anim:pos(self.panel_run:child('panel_dragon'):size().width/2 - 15, -15)
    self.drags.stand = anim
    self.panel_dragon:addChild(anim)

    anim = helper.app.createAnimation('relax/jump/ani_relax_jump_run', 24, 0.6, true)
    anim:anchor(0.5, 0)
    anim:pos(self.panel_run:child('panel_dragon'):size().width/2 - 15, -15)
    self.drags.run = anim
    self.panel_dragon:addChild(anim)

    self.drags.stand:hide()
    self.drags.run:show()
    self.panel_dragon:px(self.panel_dragon:px() - 300)
    self.panel_dragon:runAction(cc.Sequence:create(
        cc.MoveBy:create(2, cc.p(300, 0)),
        cc.CallFunc:create(function()
            self.drags.stand:show()
            self.drags.run:hide()
            self.inited = true
        end)
    ))
end


-------------------------------------------------------------------------------
-- 跳起
-------------------------------------------------------------------------------
function RelaxJump:dragonJump()
    if self.is_jumping then return end

    AudioEngine.playEffect('relax/jump/jump.mp3')
    self.is_jumping = true
    local drag = self.panel_dragon
    drag:runAction(cc.Sequence:create(
        cc.EaseOut:create(cc.MoveBy:create(JUMP_TIME, cc.p(0, JUMP_HEIGHT)), 1.5),
        cc.EaseIn:create(cc.MoveBy:create(JUMP_TIME, cc.p(0, -JUMP_HEIGHT)), 1.5),
        cc.CallFunc:create(function()
            self.is_jumping = false
        end)
    ))
end


-------------------------------------------------------------------------------
-- 缓存树
-------------------------------------------------------------------------------
function RelaxJump:cacheTree(tree)
    tree:hide()
    self.tree_pool[#self.tree_pool + 1] = tree
end


-------------------------------------------------------------------------------
-- 创建树
-------------------------------------------------------------------------------
function RelaxJump:createTree()
    -- 已看不见树移除
    for i = #self.trees, 1, -1 do
        local tree = self.trees[i]
        local pos = tree:convertToWorldSpace(cc.p(tree:size().width, 0))
        if pos.x < 0 then
            table.remove(self.trees, i)
            self:cacheTree(tree)
        end
    end

    local dist = -self.panel_road:px()
    if dist - self.last_tree_at < TREE_DISTANCE then return end
    if math.random(1, 100) < 90 then return end

    self.last_tree_at = dist

    local is_bird = self.speed:getSpeed() > 1.3 and math.random(1, 100) <= 50
    local bird
    if is_bird then
        bird = self:showBird()
        if bird then
            bird:px(dist + display.width)
        end
    end
    if not bird then
        local num = math.random(1, 3)
        for i = 1, num do
            local tree = nil
            if #self.tree_pool > 0 then
                tree = self.tree_pool[1]:show()
                table.remove(self.tree_pool, 1)
            else
                tree = self.tree_templates[math.random(1, #self.tree_templates)]:clone():show()
                tree:addTo(self.panel_tree)
            end
            --tree:scale(TREE_SCALE * math.random(60, 100) / 100)
            tree:pos(dist + display.width + math.random(100, 200), 0)
            table.insert(self.trees, 1, tree)
        end
    end
end


-------------------------------------------------------------------------------
-- 初始化界面
-------------------------------------------------------------------------------
function RelaxJump:initUI()
    -- 载入主UI
    local main_node = helper.app.loadCSB('RelaxJump.csb')
    self.main_node = main_node
    self:addChild(main_node)

    local panel_cloud, panel_road, panel_run, panel_over, panel_bird = main_node:child('panel_cloud, panel_road, panel_run, panel_over, panel_bird');
    local roads = {}
    for i = 1, 3 do
        roads[#roads + 1] = panel_road:child('road' .. i)
    end
    local tree_templates = {}
    for i = 1, 3 do
        tree_templates[#tree_templates + 1] = panel_road:child('tree' .. i):hide()
    end
    local cloud_templates = {}
    for i = 1, 2 do
       cloud_templates[#cloud_templates + 1] = panel_cloud:child('cloud' .. i):scale(math.random(50, 100) / 100)
    end
    panel_over:child('btn_restart'):addTouchEventListener( helper.app.commClickHandler(self, self.gameStart) )
    self.dragon_boxes = panel_run:child('panel_dragon'):getChildren()
    self.road_width = roads[1]:size().width
    self.panel_cloud = panel_cloud
    self.panel_road = panel_road
    self.panel_run = panel_run
    self.panel_over = panel_over:hide()
    self.panel_bird = panel_bird:hide()
    self.panel_dragon = panel_run:child('panel_dragon')
    self.panel_tree = panel_road:child('panel_tree')
    self.label_score = main_node:child('label_score')
    self.roads = roads;
    self.tree_templates = tree_templates;
    self.cloud_templates = cloud_templates;

    self:createBirds()
    self.dragon_pos = cc.p(self.panel_dragon:pos())
    self:createDragon()

    panel_cloud:runAction(cc.RepeatForever:create(
        cc.MoveBy:create(1, cc.p(-20, 0))
    ))

    local speed = cc.Speed:create(cc.RepeatForever:create(
        cc.MoveBy:create(1, cc.p(-700, 0))
    ), 1)
    panel_road:runAction(speed)
    panel_road:pause()
    self.speed = speed

    self:scheduleUpdate(handler(self, self.onTimer))
end


-------------------------------------------------------------------------------
-- 帧定时器
-------------------------------------------------------------------------------
function RelaxJump:onTimer()
    self:moveCloud()
    if not self.is_running then return end

    self:checkCollision()
    self:moveRoad()
    self:moveBird()
    self:createTree()
end


-------------------------------------------------------------------------------
-- 移动道路
-------------------------------------------------------------------------------
function RelaxJump:moveRoad()
    self:setScore(math.floor(-self.panel_road:px() / 100))
    --self.panel_road:px(self.panel_road:px() - self.speed)
    local road = self.roads[1]
    local pos = road:convertToWorldSpace(cc.p(0, 0))
    if pos.x + self.road_width < 0 then
        road:px(self.roads[#self.roads]:px() + self.road_width)
        table.remove(self.roads, 1)
        self.roads[#self.roads + 1] = road
    end
end


-------------------------------------------------------------------------------
-- 移动云
-------------------------------------------------------------------------------
function RelaxJump:moveCloud()
    for i = #self.cloud_templates, 1, -1 do
        local cloud = self.cloud_templates[i]
        local size = cloud:size()
        local pos = cloud:convertToWorldSpace(cc.p(size.width, 0))
        if pos.x < 0 then
            local x = cloud:px() + display.width + size.width
            local y = math.random(0, cloud:getParent():size().height);
            cloud:scale(math.random(50, 100) / 100)
            cloud:pos(x, y)
        end
    end
end


-------------------------------------------------------------------------------
-- 移动鸟
-------------------------------------------------------------------------------
function RelaxJump:moveBird()
    for i, bird in ipairs(self.birds) do
        local pos = bird:convertToWorldSpace(cc.p(bird:size().width, 0))
        if pos.x < 0 then
            bird:hide()
        end
    end
end


-------------------------------------------------------------------------------
-- 显示
-------------------------------------------------------------------------------
function RelaxJump:showBird()
    for i, bird in ipairs(self.birds) do
        if not bird:isVisible() then
            local y = math.random(0, self.panel_tree:size().height)
            bird:py(y):show()
            return bird
        end
    end
end


-------------------------------------------------------------------------------
-- 碰撞检测
-------------------------------------------------------------------------------
function RelaxJump:checkCollision()
    local rect = function(node)
        local a = node:getBoundingBox()
        local p = node:getParent():convertToWorldSpace(cc.p(a.x, a.y))
        return cc.rect(p.x, p.y, a.width, a.height)
    end
    local check = function(a, node)
        local b = rect(node)
        if cc.rectIntersectsRect(a, b) then
            self:gameOver()
            return true
        end
    end

    for i, box in ipairs(self.dragon_boxes) do
        local a = rect(box)
        for _, tree in ipairs(self.trees) do
            if check(a, tree:child('box')) then return end
        end
        for _, bird in ipairs(self.birds) do
            if bird:isVisible() then
                for j, box in ipairs(bird:child('boxes'):getChildren()) do
                    if check(a, box) then return end
                end
            end
        end
    end
end


return RelaxJump
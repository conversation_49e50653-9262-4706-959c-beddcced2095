[{"logtime": "2017/12/12 21:14:03", "logdata": "error loading module 'game.mahjong.src.room.GameViewLayer' from file '.\\game\\mahjong\\src\\room\\GameViewLayer.lua':\n\t.\\game\\mahjong\\src\\room\\GameViewLayer.lua:1507: unexpected symbol near ')'\nstack traceback:\n\t[C]: in function 'game'\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:3: in main chunk\n\t[C]: in function 'game'\n\t[string \"game/mahjong/src/MainScene.lua\"]:596: in function 'onEnterTable'\n\t[string \"client/src/system/RoomFrame.lua\"]:623: in function 'onSocketUserStatus'\n\t[string \"client/src/system/RoomFrame.lua\"]:295: in function 'onSocketUserEvent'\n\t[string \"client/src/system/RoomFrame.lua\"]:116: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}, {"logtime": "2017/12/12 21:14:03", "logdata": "[string \"cocos/init.lua\"]:57: attempt to call global 'buglyReportLuaException' (a nil value)\nstack traceback:\n\t[C]: at 0x0172dac0\n\t[C]: in function 'game'\n\t[string \"game/mahjong/src/room/GameLayer.lua\"]:3: in main chunk\n\t[C]: in function 'game'\n\t[string \"game/mahjong/src/MainScene.lua\"]:596: in function 'onEnterTable'\n\t[string \"client/src/system/RoomFrame.lua\"]:623: in function 'onSocketUserStatus'\n\t[string \"client/src/system/RoomFrame.lua\"]:295: in function 'onSocketUserEvent'\n\t[string \"client/src/system/RoomFrame.lua\"]:116: in function 'onSocketEvent'\n\t[string \"client/src/frame/BaseFrame.lua\"]:144: in function 'onSocketCallBack'\n\t[string \"client/src/frame/BaseFrame.lua\"]:96: in function <[string \"client/src/frame/BaseFrame.lua\"]:95>"}]
-------------------------------------------------------------------------------
--  创世版1.0
--  麻将
--  游戏配置
--      访问方式：cs.game.RES
--  @date 2017-06-05
--  @auth woodoo
-------------------------------------------------------------------------------
local name = 'fourcards'
local game = {
    -- 游戏模块名
    MODULE_NAME = name,
    -- 资源路径
    RES = cs.app.GAME_ROOT .. '/' .. name .. '/res/',
    -- 脚本路径
    SRC = cs.app.GAME_ROOT .. '.' .. name .. '.src.',
    -- 字体
    FONT_NAME = 'Arial',
    -- 服务器
    SERVER_IP = '*************',
    -- 服务器端口
    SERVER_PORT = 8600,
    -- Http服务地址
    HTTP_URL = 'http://*************',
    -- WebView相关url
    url = {
        GAME_RULE = 'http://*************',    -- 游戏规则
    },

    -- 是否有小结算界面
    HAS_GAME_RESULT = true,

    -- 是否显示界面中间的低分和局数
    IS_SHOW_DIFEN_JUSHU = false,

    -- 是否显示邀请按钮等按钮
    IS_SHOW_YQHY = false,

    IS_RECIVE_GAME_RESULT = false,

    ----------------------------游戏逻辑---------------------------------------
    -- 是否听牌提示
    TING_PROMPT = false,
    -- 最多人数
    MAX_PLAYER = 4,
    -- 分享送房卡数量
    SHARE_FANGKA = 5,
    -- 微信公众号
    WEIXIN_GONGZHONG = '云来牌友圈',
    -- 客服微信
    WEIXIN_KEFU = 'ylpkq01',
    -- 品牌
    BRAND = '磐安',
    -- 邀请链接
    INVITE_URL = 'https://a.mlinks.cc/AchW',
    -- 载入类型
    KINDS = { 101 },
    -- 配音列表
    PEIYINS = {'普通话-男', '普通话-女'},

}



--------------------------------------------------------------------------------
-- 颜色
--------------------------------------------------------------------------------
display.COLOR_STROKE		= cc.c4b(34, 24, 15, 255)		-- #22180F	-- 所有文字描边
display.COLOR_PURE_WHITE	= cc.c3b(255, 255, 255)			-- #FFFFFF	-- 纯白
display.COLOR_WHITE			= cc.c3b(231, 231, 231)			-- #E7E7E7	-- 灰白
display.COLOR_BLACK			= cc.c3b(0, 0, 0)				-- #000000	-- 纯黑
display.COLOR_RED			= cc.c3b(255, 85, 85)			-- #FF5555	-- 红色
display.COLOR_GREEN			= cc.c3b(8,137,0)			    -- #088900	-- 绿色
display.COLOR_ORANGE		= cc.c3b(211, 137, 65)			-- #D38941	-- 橙色
display.COLOR_GOLDEN		= cc.c3b(255, 248, 142)			-- #FFF88E	-- 金色
display.COLOR_BLUE			= cc.c3b(92, 172, 223)			-- #5CACDF	-- 蓝色
display.COLOR_BROWN         = cc.c3b(111, 54, 37)			-- #6F3625	-- 棕色，大多数内容
display.COLOR_GRAY_BROWN	= cc.c3b(184, 165, 141)			-- #6F3625	-- 灰棕
display.COLOR_PURPLE		= cc.c3b(192, 79, 195)			-- #C04FC3	-- 紫色
display.COLOR_GRAY_ORANGE	= cc.c3b(214, 159, 97)			-- #D69F61	-- 橙灰
display.COLOR_GRAY			= cc.c3b(80, 80, 80)			-- #505050	-- 灰色

-- 富文本颜色定义
game.RICH_COLOR = {
    w = display.COLOR_WHITE,
    h = display.COLOR_BLACK,
    y = cc.c3b(255, 255, 0),
    r = display.COLOR_RED,
    g = display.COLOR_GREEN,
    b = display.COLOR_BLUE,
    o = display.COLOR_ORANGE,
    p = display.COLOR_PURPLE,
    j = display.COLOR_GOLDEN,
    z = display.COLOR_BROWN,
    gb = display.COLOR_GRAY_BROWN,
    go = display.COLOR_GRAY_ORANGE,
    gr = display.COLOR_GRAY,
}

--helper.app.addGameSearchPath(name)

helper.app.addGameSearchPath(name, appdf.PACK_PATH)

local function loadExt(ext)
    local path = string.gsub( game.SRC, '%.', '/' ) .. 'game_ext.' .. ext
    if cc.FileUtils:getInstance():isFileExist(path) then
        local game_ext = require(game.SRC .. 'game_ext')
        -- 注意，此处不能用元表，因为要保证game_ext中的内容优先
        --setmetatable(game, {__index = game_ext})
        for k, v in pairs(game_ext) do
            game[k] = v
        end
        return true
    end
end
local ret = loadExt('luac') or loadExt('lua')


return game
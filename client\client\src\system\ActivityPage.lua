-------------------------------------------------------------------------------
--  创世版1.0
--  活动翻页控件
--  @date 2017-07-26
--  @auth woodoo
-------------------------------------------------------------------------------
local ActivityPage = class('ActivityPage', ccui.PageView)


local TAG_PAGING = 123

-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ActivityPage:ctor(size)
    self.m_downloaded_count = 0
    self:size(size)
    self:setIndicatorEnabled(true)
    self:setIndicatorSelectedIndexColor( cc.c3b(0, 0, 0) )
    self:setIndicatorPositionAsAnchorPoint( cc.p(0.5, 0.026) )
end


-------------------------------------------------------------------------------
-- 是否都已下载
-------------------------------------------------------------------------------
function ActivityPage:isAllLoaded()
    return self.m_downloaded_count == #self.m_page_data
end


-------------------------------------------------------------------------------
-- 更新数据
-------------------------------------------------------------------------------
function ActivityPage:refreshData(page_data, click_callback)
    self.m_downloaded_count = 0
    self.m_page_data = page_data
    self:stop(TAG_PAGING)
    self:removeAllPages()
    local size = self:size()
    for i, data in ipairs(page_data) do
        local panel = ccui.Layout:create()
        panel.index = i
        panel:size(size)
        panel:setTouchEnabled(true)
        panel:setCascadeColorEnabled(true)
        panel:addTouchEventListener( helper.app.tintClickHandler(nil, click_callback) )

        local path = data.image
        local img = ccui.ImageView:create()
        if cc.FileUtils:getInstance():isFileExist(path) then
            self.m_downloaded_count = self.m_downloaded_count + 1
            img:texture(path)
        else
            local callback = function(filename)
                self.m_downloaded_count = self.m_downloaded_count + 1
                img:texture(path)
            end
            helper.app.download(data.image_url, data.image_save_path, data.image_save_name, self, callback)
        end
        img:pos(size.width/2, 0):anchor(0.5, 0):addTo(panel)

        if data.title and data.title ~= '' and data.show_title then
            local label = ccui.Text:create(data.title, cs.app.FONT_NAME, 20)
            label:pos(size.width/2, 40):addTo(panel)
        end

        self:addPage(panel)
    end

    local count = #page_data
    if count > 1 then
        local action = cc.RepeatForever:create( cc.Sequence:create(
            cc.DelayTime:create(4),
            cc.CallFunc:create(function(sender)
                local index = sender:getCurrentPageIndex()
                index = index + 1
                if index == count then
                    index = 0
                end
                sender:scrollToPage(index)
            end)
        ) )
        action:setTag(TAG_PAGING)
        self:runMyAction(action)
    end
end


return ActivityPage
local GameLogic = {}

local cmd = import(".CMD_Game")

--牌库数目
GameLogic.FULL_COUNT				= 112
GameLogic.MAGIC_DATA 				= 0x00 -- 53
GameLogic.MAGIC_INDEX 				= 0
GameLogic.MAGIC_DATA_ARRAY 			= {}   -- 多个财神时的补充
GameLogic.MAGIC_INDEX_COUNT 		= 0    -- 多个财神时的补充
GameLogic.REPLACE_INDEX 			= 0
GameLogic.REPLACE_DATA 			    = 0x00
GameLogic.NORMAL_DATA_MAX 			= 0x29 -- 41
GameLogic.NORMAL_INDEX_MAX 			= 27


--显示类型
GameLogic.SHOW_NULL					= 0							--无操作
GameLogic.SHOW_CHI 					= 1 						--吃
GameLogic.SHOW_PENG					= 2 						--碰
GameLogic.SHOW_MING_GANG			= 3							--明杠（碰后再杠）
GameLogic.SHOW_FANG_GANG			= 4							--放杠
GameLogic.SHOW_AN_GANG				= 5							--暗杠
GameLogic.SHOW_CHOU_PAI				= 6							--抽牌

--动作标志
GameLogic.WIK_NULL					= 0x00						--没有类型--0
GameLogic.WIK_LEFT					= 0x01						--左吃类型--1
GameLogic.WIK_CENTER				= 0x02						--中吃类型--2
GameLogic.WIK_RIGHT					= 0x04						--右吃类型--4
GameLogic.WIK_PENG					= 0x08						--碰牌类型--8
GameLogic.WIK_GANG					= 0x10						--杠牌类型--16
GameLogic.WIK_LISTEN				= 0x20						--听牌类型--32
GameLogic.WIK_CHI_HU				= 0x40						--吃胡类型--64
GameLogic.WIK_CHOU_PAI				= 0x80						--抽排
GameLogic.WIK_ZIMO  				= 0xA0						--自摸--该标记仅前台使用
GameLogic.WIK_BU_HUA  				= 0xB0						--补花--该标记仅前台使用
GameLogic.WIK_PIAO_CAI  		    = 0xC0						--漂财--该标记仅前台使用
GameLogic.WIK_SHENG_PAI  			= 0xD0						--生牌阶段
GameLogic.WIK_CHENG_BAO  			= 0xE0						--承包
--动作类型
GameLogic.WIK_GANERAL				= 0x00						--普通操作
GameLogic.WIK_MING_GANG				= 0x01						--明杠（碰后再杠）
GameLogic.WIK_FANG_GANG				= 0x02						--放杠
GameLogic.WIK_AN_GANG				= 0x03						--暗杠

GameLogic.LocalCardData =
{
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
	0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
	0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
    0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
    0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49
}

GameLogic.TotalCardData =
{
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
	0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
	0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
	0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
	0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
	0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
	0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
	0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
	0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
	0x35, 0x35, 0x35, 0x35,
}
-- 是否是财神牌
function GameLogic.IsMagicCard( card )
    if GameLogic.MAGIC_INDEX_COUNT > 1 then
        for i = 1, GameLogic.MAGIC_INDEX_COUNT do
            if GameLogic.MAGIC_DATA_ARRAY[i] == card then
                return true
            end
        end
    else
        if GameLogic.MAGIC_DATA == card then
            return true
        end
    end
    return false
end

-- 是否是痞子牌
function GameLogic.IsPiCard( cardData )
    --print('是否是痞子牌', GameLogic.SwitchToCardIndex( cardData ),  GameLogic.IS_PI_CARDS_INDEX[ GameLogic.SwitchToCardIndex( cardData )])
    if not GameLogic.IS_PI_CARDS_INDEX then
        return false
    end
    return GameLogic.IS_PI_CARDS_INDEX[ GameLogic.SwitchToCardIndex( cardData ) ]
end

-- 设置痞子牌
function GameLogic.setPiCard( piCards )
    GameLogic.IS_PI_CARDS_INDEX = piCards
end

function GameLogic.SwitchToCardIndex(card)
	local index = 0
	for i = 1, #GameLogic.LocalCardData do
		if GameLogic.LocalCardData[i] == card then
			index = i
			break
		end
	end
	local strError = string.format("The card %d is error!", card)
	assert(index ~= 0, strError)
	return index
end

function GameLogic.SwitchToCardData(index)
	--assert(index >= 1 and index <= cmd.MAX_CARD_INDEX, "The card index is error!")
	return GameLogic.LocalCardData[index]
end

function GameLogic.DataToCardIndex(cbCardData)
	assert(type(cbCardData) == "table")
	--初始化
	local cbCardIndex = {}
	for i = 1, cmd.MAX_CARD_INDEX do
		cbCardIndex[i] = 0
	end
	--累加
	for i = 1, #cbCardData do
		local bCardExist = false
		for j = 1, cmd.MAX_CARD_INDEX do
			if  cbCardData[i] == GameLogic.LocalCardData[j] then
				cbCardIndex[j] = cbCardIndex[j] + 1
				bCardExist = true
			end
		end
		--assert(bCardExist, "This card is not exist!" )
	end
	return cbCardIndex
end

--获取牌的数量
function GameLogic.GetCardCount( cbCardData, cbCard )
    local cbCardIndex = GameLogic.DataToCardIndex( cbCardData )
    local index = GameLogic.SwitchToCardIndex( cbCard )
    return cbCardIndex[ index ]
end

--获取财神的数量
function GameLogic.GetMagicCardCount( cbCardData )
    local cbCardIndex = GameLogic.DataToCardIndex( cbCardData )
    local num = 0
    if GameLogic.MAGIC_INDEX_COUNT > 1 then
        for i = 1, GameLogic.MAGIC_INDEX_COUNT do
            local card = GameLogic.MAGIC_DATA_ARRAY[ i ]
            if card > 0 then
                local index = GameLogic.SwitchToCardIndex( card )
                num = num + cbCardIndex[ index ]
            end
        end
    else
        local cbCardIndex = GameLogic.DataToCardIndex( cbCardData )
	    num = cbCardIndex[ GameLogic.MAGIC_INDEX ]
    end
    if not num then
        num = 0
    end
    return num
end

--按顺序 获取 财神牌
function GameLogic.GetMagicCards( cbCardData, num )
    local tmpTable = {}
    if num > 0 then
        for j = 1, #cbCardData do
           if GameLogic.IsMagicCard(cbCardData[j]) then
              table.insert(tmpTable, cbCardData[j])
              num = num - 1
              if num <= 0 then
                  break
              end
           end
        end    
    end
    return tmpTable
end

--添加牌到组合中
function GameLogic.addCardsWaves( cards, operate, tempTable )
    local info = {}
    info.cards = cards
    info.operate = operate
    table.insert(tempTable, info)
end

--分析吃的组合 手牌
function GameLogic.AnyseChiCards( cbCardData, cbTargetCard )
    local magicNum = GameLogic.GetMagicCardCount( cbCardData )
    local cbCardIndex = GameLogic.DataToCardIndex( cbCardData )
    local cbTargetCardIndex = GameLogic.SwitchToCardIndex( cbTargetCard )
    local cardsWaves = {}

    --可以左吃
    if math.mod(cbTargetCardIndex - 1, 9) + 2 <= 8 then
        if cbCardIndex[cbTargetCardIndex + 1] ~= 0 and cbCardIndex[cbTargetCardIndex + 2] ~= 0 and not GameLogic.IsMagicCard( cbTargetCard + 1 ) and not GameLogic.IsMagicCard( cbTargetCard + 2 ) then
            GameLogic.addCardsWaves( {cbTargetCard, cbTargetCard + 1, cbTargetCard + 2}, GameLogic.WIK_LEFT, cardsWaves )
        end
        if magicNum > 0 then
            --1个财神
            if cbCardIndex[cbTargetCardIndex + 1] ~= 0 and not GameLogic.IsMagicCard( cbTargetCard + 1 ) then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                table.insert(tempTable, 1, cbTargetCard)
                table.insert(tempTable, 2, cbTargetCard + 1)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_LEFT, cardsWaves )
            end
            if cbCardIndex[cbTargetCardIndex + 2] ~= 0  and not GameLogic.IsMagicCard( cbTargetCard + 2 ) then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                table.insert(tempTable, 1, cbTargetCard)
                table.insert(tempTable, cbTargetCard + 2)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_LEFT, cardsWaves )
            end
            --2个财神
            if  magicNum > 1 then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 2)
                table.insert(tempTable, 1, cbTargetCard)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_LEFT, cardsWaves )
            end
        end
    end
    --可以中吃
    if math.mod(cbTargetCardIndex - 1, 9) + 1 <= 8 and math.mod(cbTargetCardIndex - 1, 9) - 1 >= 0 then
        if cbCardIndex[cbTargetCardIndex + 1] ~= 0 and cbCardIndex[cbTargetCardIndex - 1] ~= 0 and not GameLogic.IsMagicCard( cbTargetCard + 1 ) and not GameLogic.IsMagicCard( cbTargetCard - 1 ) then
            GameLogic.addCardsWaves({cbTargetCard-1, cbTargetCard, cbTargetCard + 1}, GameLogic.WIK_CENTER, cardsWaves )
        end
        if magicNum > 0 then
            --1个财神
            if cbCardIndex[cbTargetCardIndex + 1] ~= 0  and not GameLogic.IsMagicCard( cbTargetCard + 1 ) then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                table.insert(tempTable, cbTargetCard)
                table.insert(tempTable, cbTargetCard + 1)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_CENTER, cardsWaves )
            end
            if cbCardIndex[cbTargetCardIndex - 1] ~= 0  and not GameLogic.IsMagicCard( cbTargetCard - 1 ) then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                table.insert(tempTable, 1, cbTargetCard)
                table.insert(tempTable, 1, cbTargetCard - 1)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_CENTER, cardsWaves )
            end
            --2个财神
            if  magicNum > 1 then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 2)
                table.insert(tempTable, 2, cbTargetCard)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_CENTER, cardsWaves )
            end
        end
    end
    --可以右吃
    if math.mod(cbTargetCardIndex - 1, 9) - 2 >= 0 then
        if cbCardIndex[cbTargetCardIndex - 1] ~= 0 and cbCardIndex[cbTargetCardIndex - 2] ~= 0 and not GameLogic.IsMagicCard( cbTargetCard - 1 ) and not GameLogic.IsMagicCard( cbTargetCard - 2 ) then
            GameLogic.addCardsWaves({cbTargetCard - 2, cbTargetCard - 1, cbTargetCard }, GameLogic.WIK_RIGHT, cardsWaves )
        end
        if magicNum > 0 then
            --1个财神
            if cbCardIndex[cbTargetCardIndex - 1] ~= 0  and not GameLogic.IsMagicCard( cbTargetCard - 1 ) then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                table.insert(tempTable, cbTargetCard - 1)
                table.insert(tempTable, cbTargetCard)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_RIGHT, cardsWaves )
            end
            if cbCardIndex[cbTargetCardIndex - 2] ~= 0  and not GameLogic.IsMagicCard( cbTargetCard - 2 ) then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                table.insert(tempTable, cbTargetCard)
                table.insert(tempTable, 1, cbTargetCard - 2)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_RIGHT, cardsWaves )
            end
            --2个财神
            if  magicNum > 1 then
                local tempTable = GameLogic.GetMagicCards(cbCardData, 2)
                table.insert(tempTable, cbTargetCard)
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_RIGHT, cardsWaves )
            end
        end
    end
    return cardsWaves
end

--分析碰的组合 手牌
function GameLogic.AnysePengCards( cbCardData, cbTargetCard )
    local magicNum = GameLogic.GetMagicCardCount( cbCardData )
    local cardNum = math.min(GameLogic.GetCardCount( cbCardData, cbTargetCard ), 2)
    local cardsWaves = {}
    if cardNum + magicNum >= 2 then
        for i = 0, cardNum  do
            if i + magicNum >= 2 then
                local tempTable
                if i == 0 then
                    tempTable = GameLogic.GetMagicCards(cbCardData, 2)
                    table.insert(tempTable, 1, cbTargetCard)
                elseif i == 1 then
                    tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                    table.insert(tempTable, 1, cbTargetCard)
                    table.insert(tempTable, 1, cbTargetCard)
                elseif i == 2 then
                    tempTable = {cbTargetCard, cbTargetCard, cbTargetCard}
                end
                GameLogic.addCardsWaves(tempTable, GameLogic.WIK_PENG, cardsWaves )
            end
        end
    end
    return cardsWaves
end

--分析杠的组合 手牌
function GameLogic.AnyseGangCards( cbCardData, cbTargetCard, isFangGang, weaves )
    local magicNum = math.min(GameLogic.GetMagicCardCount( cbCardData ), 3) 
    local cardsWaves = {}
    if isFangGang then
        print('放杠')
        local cardNum = GameLogic.GetCardCount( cbCardData, cbTargetCard )
        if cardNum + magicNum >= 3 then
            for i = 0, cardNum  do
                if i + magicNum >= 3 then
                    local tempTable
                    if i == 0 then
                        tempTable = GameLogic.GetMagicCards(cbCardData, 3)
                        table.insert(tempTable, 1, cbTargetCard)
                    elseif i == 1 then
                        tempTable = GameLogic.GetMagicCards(cbCardData, 2)
                        table.insert(tempTable, 1, cbTargetCard)
                        table.insert(tempTable, 1, cbTargetCard)
                    elseif i == 2 then
                        tempTable = GameLogic.GetMagicCards(cbCardData, 1)
                        table.insert(tempTable, 1, cbTargetCard)
                        table.insert(tempTable, 1, cbTargetCard)
                        table.insert(tempTable, 1, cbTargetCard)
                    elseif i == 3 then
                        tempTable = {cbTargetCard,cbTargetCard,cbTargetCard,cbTargetCard}
                    end
                    GameLogic.addCardsWaves(tempTable, GameLogic.WIK_GANG, cardsWaves )
                end
            end
        end
    else
        print('自摸杠')
        local cbCardIndex = GameLogic.DataToCardIndex( cbCardData )
        for index = 1, cmd.MAX_CARD_INDEX do
            local num = cbCardIndex[ index ]
            local cardData = GameLogic.SwitchToCardData( index )
            local totalNum = num + magicNum
            local isMagic = GameLogic.IsMagicCard( cardData )
            if not isMagic and totalNum >= 4 then
                for j = 0, magicNum do
                    if j + num >= 4 then
                        local tempTable = GameLogic.GetMagicCards(cbCardData, j)
                        for n = 1, 4 - j do
                            table.insert(tempTable, 1, cardData)
                        end
                        GameLogic.addCardsWaves(tempTable, GameLogic.WIK_GANG, cardsWaves )
                    end
                end
            end
        end
        for index, weaveInfo in pairs(weaves) do
            if weaveInfo.cbOperateCode == GameLogic.WIK_PENG then
                local cardIndex = GameLogic.SwitchToCardIndex( weaveInfo.cbCenterCard )
                local num = cbCardIndex[ cardIndex ]
                if num > 0 then
                   local tempTable = {weaveInfo.cbCardData[1], weaveInfo.cbCardData[2], weaveInfo.cbCardData[3]} 
                   table.insert(tempTable, weaveInfo.cbCenterCard)
                   GameLogic.addCardsWaves(tempTable, GameLogic.WIK_GANG, cardsWaves )
                end
                if magicNum > 0 then
                   local tempTable = {weaveInfo.cbCardData[1], weaveInfo.cbCardData[2], weaveInfo.cbCardData[3]} 
                   local tempTable2 = GameLogic.GetMagicCards(cbCardData, 1)
                   table.insert(tempTable, tempTable2[1])
                   GameLogic.addCardsWaves(tempTable, GameLogic.WIK_GANG, cardsWaves )
                end
            end
        end
    end
    return cardsWaves
end

--分析杠的组合 手牌
function GameLogic.AnyseChouCards( cbCardData, cbWeaveCardDatas )
    local cardsWaves = {}
    for index, cbWeaveCard in pairs(cbWeaveCardDatas) do
        --先找假的
        if not cbWeaveCard.isReal then
            local isChou = false
            local operate = cbWeaveCard.cbOperateCode
            if operate == GameLogic.WIK_LEFT then
                if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[2]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard + 1 ) > 0 then
                    isChou = true
                end
                if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[3]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard + 2 ) > 0 then
                    isChou = true
                end
            elseif operate == GameLogic.WIK_RIGHT then
                if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[2]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard - 1 ) > 0 then
                    isChou = true
                end
                if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[1]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard - 2 ) > 0 then
                    isChou = true
                end
            elseif operate == GameLogic.WIK_CENTER then
                if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[1]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard - 1 ) > 0 then
                    isChou = true
                end
                if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[3]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard + 1 ) > 0 then
                    isChou = true
                end
            else
                for i = 1, 4 do
                    if GameLogic.IsMagicCard(cbWeaveCard.cbCardData[i]) and GameLogic.GetCardCount( cbCardData, cbWeaveCard.cbCenterCard ) > 0 then
                        isChou = true
                        break
                    end
                end
            end
            -- 是否加入抽牌列表
            if isChou then
                GameLogic.addCardsWaves(cbWeaveCard.cbCardData, GameLogic.WIK_CHOU_PAI, cardsWaves )
            end
        end
    end
    return cardsWaves
end

--删除扑克
function GameLogic.RemoveCard(cbCardData, cbRemoveCard)
	assert(type(cbCardData) == "table" and type(cbRemoveCard) == "table")
	local cbCardCount, cbRemoveCount = #cbCardData, #cbRemoveCard
	assert(cbRemoveCount <= cbCardCount)

	--置零扑克
	for i = 1, cbRemoveCount do
		for j = 1, cbCardCount do
			if cbRemoveCard[i] == cbCardData[j] then
				cbCardData[j] = 0
				break
			end
		end
	end
	--清理扑克
	local resultData = {}
	local cbCardPos = 1
	for i = 1, cbCardCount do
		if cbCardData[i] ~= 0 then
			resultData[cbCardPos] = cbCardData[i]
			cbCardPos = cbCardPos + 1
		end
	end

	return resultData
end

--混乱扑克
function GameLogic.RandCardList(cbCardData)
	assert(type(cbCardData) == "table")
	--混乱准备
	local cbCardCount = #cbCardData
	local cbCardTemp = clone(cbCardData)
	local cbCardBuffer = {}
	--开始
	local cbRandCount, cbPosition = 0, 0
	while cbRandCount < cbCardCount do
		cbPosition = math.random(cbCardCount - cbRandCount)
		cbCardBuffer[cbRandCount + 1] = cbCardTemp[cbPosition]
		cbCardTemp[cbPosition] = cbCardTemp[cbCardCount - cbRandCount]
		cbRandCount = cbRandCount + 1
	end
	return cbCardBuffer
end

--排序
function GameLogic.SortCardList(cbCardData)
	--校验
	assert(type(cbCardData) == "table" and #cbCardData > 0)
	local cbCardCount = #cbCardData
	--排序操作
	local bSorted = false
	local cbLast = cbCardCount - 1
	while bSorted == false do
		bSorted = true
		for i = 1, cbLast do
            local left_value = cbCardData[i]
            local right_value = cbCardData[i + 1]
            if GameLogic.IsMagicCard( left_value ) then
                left_value = -1
            elseif GameLogic.IsPiCard( left_value ) then
                left_value = 0
            else
                left_value = GameLogic.getUseCardData(left_value, GameLogic.MAGIC_DATA)
            end
            if GameLogic.IsMagicCard( right_value ) then
                right_value = -1
            elseif GameLogic.IsPiCard( right_value ) then
                right_value = 0
            else
                right_value = GameLogic.getUseCardData(right_value, GameLogic.MAGIC_DATA)
            end
			if left_value > right_value then
				bSorted = false
				cbCardData[i], cbCardData[i + 1] = cbCardData[i + 1], cbCardData[i]
			end
		end
		cbLast = cbLast - 1
	end
end

--分析打哪一张牌后听哪张牌
function GameLogic.AnalyseListenCard(cbCardData)
	assert(type(cbCardData) == "table")
	local cbCardCount = #cbCardData
	assert(math.mod(cbCardCount - 2, 3) == 0)

	local cbListenList = {}
	local cbListenData = {cbOutCard = 0, cbListenCard = {}}
	local tempCard = 0
	local bWin = false
	for i = 1, cbCardCount do
		if tempCard ~= cbCardData[i] then		--过滤重复牌
			cbListenData.cbOutCard = cbCardData[i]
			cbListenData.cbListenCard = {}
			local cbTempData = clone(cbCardData)
			--table.remove(cbTempData, i)
			for j = 1, GameLogic.NORMAL_INDEX_MAX do
				local localCard = GameLogic.LocalCardData[j]
				local insertData = clone(cbTempData)
				--table.insert(insertData, GameLogic.LocalCardData[j])
				insertData[i] = localCard
				GameLogic.SortCardList(insertData)
				if GameLogic.AnalyseChiHuCard(insertData, true) then
					table.insert(cbListenData.cbListenCard, localCard)
					if cbCardData[i] == GameLogic.LocalCardData[j] then
						bWin = true --胡牌
						break
					end
				end
			end
			if #cbListenData.cbListenCard > 0 then
				table.insert(cbListenList, cbListenData)
				--print("听牌")
			end

			if bWin then
				break
			end
		end
		tempCard = cbCardData[i]
	end
	return cbListenList, bWin
end

--分析是否胡牌(带红中)
function GameLogic.AnalyseChiHuCard(cbCardData, bNoneThePair)
	local cbCardCount = #cbCardData
	--红中统计
	local cbCardIndex = GameLogic.DataToCardIndex(cbCardData)
	local cbMagicCardCount = cbCardIndex[GameLogic.MAGIC_INDEX]
	--成功，全部合格
	if cbCardCount == 0 then
		--print("这个时候已经算胡了！")
		return true
	end
	local cbRemoveData = {0, 0, 0}
	--三张相同
	if cbCardData[1] == cbCardData[2] and
	cbCardData[1] == cbCardData[3] then
		--print("三张相同")
		bThree = true
		cbRemoveData[1] = cbCardData[1]
		cbRemoveData[2] = cbCardData[2]
		cbRemoveData[3] = cbCardData[3]
		local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
		if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
			return true
		end
	end
	--三张相连
	local index = GameLogic.SwitchToCardIndex(cbCardData[1])
	if math.mod(index, 9) + 2 <= 9 and
	cbCardIndex[index + 1] ~= 0 and
	cbCardIndex[index + 2] ~= 0 then
		--print("三张相连")
		cbRemoveData[1] = cbCardData[1]
		cbRemoveData[2] = GameLogic.SwitchToCardData(index + 1)
		cbRemoveData[3] = GameLogic.SwitchToCardData(index + 2)
		local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
		if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
			return true
		end
	end
	--两张相同组成一对将（不使用红中代替）
	if cbCardData[1] == cbCardData[2] and bNoneThePair then
		--print("两张相同组成一对将（不使用红中代替）")
		bNoneThePair = false
		cbRemoveData[1] = cbCardData[1]
		cbRemoveData[2] = cbCardData[2]
		cbRemoveData[3] = 0
		local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
		if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
			return true
		end
	end
	--有红中时使用红中代替
	if cbMagicCardCount > 0 then
		--两张相同
		if cbCardData[1] == cbCardData[2] then
			--print("两张相同")
			cbRemoveData[1] = cbCardData[1]
			cbRemoveData[2] = cbCardData[2]
			cbRemoveData[3] = GameLogic.MAGIC_DATA
			local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
			if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
				return true
			end
		end
		--两张相邻
		if cbCardData[1] + 1 == cbCardData[2] then
			--print("两张相邻")
			cbRemoveData[1] = cbCardData[1]
			cbRemoveData[2] = cbCardData[2]
			cbRemoveData[3] = GameLogic.MAGIC_DATA
			local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
			if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
				return true
			end
		end
		--两张相隔
		if cbCardData[1] + 2 == cbCardData[2] then
			--print("两张相隔")
			cbRemoveData[1] = cbCardData[1]
			cbRemoveData[2] = cbCardData[2]
			cbRemoveData[3] = GameLogic.MAGIC_DATA
			local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
			if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
				return true
			end
		end
		--一张组成一对将
		if bNoneThePair then
			--print("一张组成一对将")
			bNoneThePair = false
			local cbRemoveData = {cbCardData[1], }
			cbRemoveData[1] = cbCardData[1]
			cbRemoveData[2] = GameLogic.MAGIC_DATA
			cbRemoveData[3] = 0
			local cbTempData = GameLogic.RemoveCard(clone(cbCardData), cbRemoveData)
			if GameLogic.AnalyseChiHuCard(cbTempData, bNoneThePair) then 		--递归
				return true
			end
		end
	end

	return false
end

--临近牌统计
function GameLogic.NearCardGether(cbCardData)
	assert(type(cbCardData) == "table")
	local nearCardData = {}
	for i = 1, #cbCardData do
		assert(not GameLogic.IsMagicCard( cbCardData[i] ))
		for j = cbCardData[i] - 1, cbCardData[i] + 1 do
			local num = math.mod(j, 16)
			if num >= 1 and num <= 9 then
				table.insert(nearCardData, j)
			end
		end
	end
	return GameLogic.RemoveRepetition(nearCardData)
end

--去除重复元素
function GameLogic.RemoveRepetition(cbCardData)
	assert(type(cbCardData) == "table")

	local bExist = {}
	for v, k in pairs(cbCardData) do
		bExist[k] = true
	end

	local result = {}
	for v, k in pairs(bExist) do
		table.insert(result, v)
	end

	GameLogic.SortCardList(result)

	return result
end


-------------------------------------------------------------------------------
-- 设置替换牌
-------------------------------------------------------------------------------
function GameLogic.setSwitchCardData(card_1, card_2)
    GameLogic.m_switch_cards = {card_1, card_2}
end


-------------------------------------------------------------------------------
-- 实际使用牌值
--  比如：有些玩法，白板要当财神的实际牌值使用，排序、显示吃牌选择及摊堆时都要以实际使用牌值计算
-------------------------------------------------------------------------------
function GameLogic.getUseCardData(cbCardData, cbCaishen)
    if GameLogic.m_switch_cards and #GameLogic.m_switch_cards == 2 then
	    local card_old = GameLogic.m_switch_cards[1]
	    local card_new = GameLogic.m_switch_cards[2]
        --print( '实际使用牌值', card_old, card_new )
	    if card_old ~= card_new then
		    if cbCardData == card_old then
			    return card_new
		    elseif cbCardData == card_new then
			    return card_old
		    end
	    end
    end
	return cbCardData
end


-------------------------------------------------------------------------------
-- 获取吃的另外两张牌
-------------------------------------------------------------------------------
function GameLogic.getChiOthers(cbCardData, cbWeaveKind, cbCaishen)
	local a, b
	if cbWeaveKind == GameLogic.WIK_LEFT then
		a = GameLogic.getUseCardData(cbCardData+1, cbCaishen)
		b = GameLogic.getUseCardData(cbCardData+2, cbCaishen)
	elseif cbWeaveKind == GameLogic.WIK_CENTER then
		a = GameLogic.getUseCardData(cbCardData-1, cbCaishen)
		b = GameLogic.getUseCardData(cbCardData+1, cbCaishen)
	elseif cbWeaveKind == GameLogic.WIK_RIGHT then
		a = GameLogic.getUseCardData(cbCardData-2, cbCaishen)
		b = GameLogic.getUseCardData(cbCardData-1, cbCaishen)
	end
	return a, b
end


return GameLogic

-------------------------------------------------------------------------------
--  创世版1.0
--  欢迎界面
--      功能：本地版本记录读取，如无记录，则解压原始大厅及附带游戏
--      注意：此时client和game目录下的任何代码都还未导入，不能使用
--  @date 2017-06-26
--  @auth woodoo
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
-- 初次运行或覆盖安装时的版本号处理
--    必须检查包中的版本号，也就是要在添加更新目录到搜索路径前执行
--    当前时间点还没加载除main.lua以外的任何其它游戏代码（cocos框架已加载）
-------------------------------------------------------------------------------
if device.platform ~= 'windows' then
    local paths = cc.FileUtils:getInstance():getSearchPaths()
    local new_paths = {}    -- 屏蔽了更新目录的其它
    for i, path in ipairs(paths) do
        if path:find(device.writablePath, nil, true) ~= 1 then -- nil or not 1
            table.insert(new_paths, path)
        end
    end
    cc.FileUtils:getInstance():setSearchPaths(new_paths)
    local vm = require("client.src.app.models.Version"):create()
    local pack = require('client.src.pack')
    local c_version = tonumber(vm:getVersion())
    -- 若大版本号和本地不同，则认为是初次或覆盖安装，删除所有资源
    if not c_version or pack.BASE_C_VERSION > c_version then
        local fu = cc.FileUtils:getInstance()
        fu:removeFile(device.writablePath .. 'version.plist')
        fu:removeDirectory(device.writablePath .. 'client/')
        fu:removeDirectory(device.writablePath .. 'game/')

		vm:setVersion( pack.BASE_C_VERSION )
		vm:setResVersion( pack.BASE_C_RESVERSION )
        local games = pack.PACK_GAMES:split('|')
        local verions = pack.PACK_GAME_VERSIONS:split('|')
        for i, v in ipairs(games) do
            vm:setResVersion(verions[i] or 0, v)
        end
    end
    vm:release()    -- vm是个node，且在create方法中被retain
    package.loaded['client.src.app.models.Version'] = nil
    package.loaded['client.src.pack'] = nil
    cc.FileUtils:getInstance():setSearchPaths(paths)
end
-------------------------------------------------------------------------------


require("client.src.app.models.bit")
require("client.src.app.models.AppDF")
appdf.req("client.src.app.views.layer.other.Toast")
cjson = require("cjson")

local Version = require("client.src.app.models.Version")

local MyApp = class("MyApp", cc.load("mvc").AppBase)

function MyApp:onCreate()
    print = release_print -- todo: 正式包取消
    math.randomseed(os.time())

    appdf.app = self
	
	--版本信息
	self._version = Version:create()
	--游戏信息
	self._gameList = {}
	--更新地址
	self._updateUrl = ""
    --更新内容
    self._updateContent = ""
	--初次启动获取的配置信息
	self._serverConfig = {}

end


-------------------------------------------------------------------------------
-- 获取版本管理器
-------------------------------------------------------------------------------
function MyApp:getVersionManager()
    if not self._version then
        self._version = Version:create()
    end
	return self._version
end


-------------------------------------------------------------------------------
-- 获取服务器配置
-------------------------------------------------------------------------------
function MyApp:getServerConfig()
	return self._serverConfig
end


-------------------------------------------------------------------------------
-- 获取版本号
-------------------------------------------------------------------------------
function MyApp:getVersion()
    local c_base = appdf.BASE_C_VERSION
    local c_res = self:getVersionManager():getResVersion() or 0
    local game_res = cs and cs.game and self:getVersionManager():getResVersion( cs.game.MODULE_NAME ) or 0
    local os_name = device.platform == 'android' and '*' or (device.platform == 'ios' and '!' or '#')
    local info = string.format('%d.%d.%d%s', c_base, c_res, game_res or 0, os_name)
    return info
end


return MyApp

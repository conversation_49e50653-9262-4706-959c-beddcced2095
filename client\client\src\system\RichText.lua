-------------------------------------------------------------------------------
--  创世版1.0
--  RichText控件
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------

local inner_icon_flags = 
{
	[1]		= 'common/icon_gold_small.png',
	[2]		= 'common/icon_quan_small.png',
	[3]		= 'common/icon_guess_small.png',
}


local RichText = class('RichText', function()
    return ccui.RichText:create()
end)




--------------------------------------------------------------------------------
-- 构造函数
--	除text外其它参数可选
--	content_size: 是要求的整体尺寸
--	color_escapes: {w='y', ...}，转义一些颜色
--	如果text是table，表示以具名参数传递参数

--	eg: RichText.new('hello')
--	eg: RichText.new('hello', 32, cc.size(200, 100), nil, {w='y'})
--	eg: RichText.new{text='hello', font_size=32}
--------------------------------------------------------------------------------
function RichText:ctor(text, font_size, content_size, font_name, color_escapes)
    if text then
        self:pushText(text, font_size, content_size, font_name, color_escapes)
    end
end


--------------------------------------------------------------------------------
-- 加入文本
--------------------------------------------------------------------------------
function RichText:pushText(text, font_size, content_size, font_name, color_escapes)
    if type(text) == 'table' then
        font_size		= text.font_size
        content_size	= text.content_size
        font_name		= text.font_name
        color_escapes	= text.color_escapes
        text			= text.text
    elseif text and type(text) ~= 'string' then
        text = tostring(text)
    end

    text		= text or ''
    font_name	= font_name and font_name ~= '' and font_name or cs.app.FONT_NAME
    font_size	= font_size or 20
    
    if content_size then
        self:ignoreContentAdaptWithSize(false)
        self:size(content_size)
    end

    local sign_v = {[''] = 1, ['-'] = -1, ['+'] = 1}
    local items = self:parse(text)
    for i, item in ipairs(items) do
        if item[1] == '|' then
            local node = ccui.RichElementNewLine:create(1, cc.c3b(255, 255, 255), 255)
            self:pushBackElement(node)
            self.is_multi_line = true
        elseif item[1] ~= '*' then
            local size = font_size + (sign_v[item[3] or ''] or 0) * (tonumber(item[4] or 0) or 0)
            local color = self:getColor(item[1], color_escapes)
            local node = ccui.RichElementText:create(1, color, 255, item[2], font_name, size)
            self:pushBackElement(node)
        else
            -- 自定义图片
            local path = inner_icon_flags[tonumber(item[4])]
            if path then
                local node = display.newSprite(path)
                if node then
                    self.custom_images = self.custom_images or {}	-- 保留一份列表
                    table.insert(self.custom_images, node)
                    local rich_node = ccui.RichElementCustomNode:create(1, cc.c3b(255, 255, 255), 255, node)
                    self:pushBackElement(rich_node)
                end
            end
        end
    end

    -- 强制刷新，计算出大小
    self:formatText()
end


--------------------------------------------------------------------------------
-- 颜色
--------------------------------------------------------------------------------
function RichText:getColor(flag, color_escapes)
    flag = color_escapes and color_escapes[flag] or flag
    return type(flag) == 'table' and flag or (cs.app.RICH_COLOR[flag] or display.COLOR_WHITE)
end


--------------------------------------------------------------------------------
-- 解析格式
--	返回: {{'w', 'abc'}, {'r', 'efg'}, ...}
--------------------------------------------------------------------------------
function RichText:parse(text, default_flag)
    local last_flag = default_flag or '='
    local last_sign = nil
    local last_num = 0
    local ret = {}
    local last_p, p1, p2, flag
    local add = function(text, flag, num)
        if not ISLEFT then
            text = string.gsub(text, '^([-+xX])(%s*)([0-9.]+)([%%]?)', '%4%3%2%1')	-- 将+5.0%这类反向
        end
        table.insert(ret, {flag or last_flag:lower(), text, last_sign, num or last_num})
    end
    repeat
        p1, p2, flag, sign, num = text:find('<([%a|~!@#$*=][%a|~!@#$*=]?)([-+]?)(%d*)>', last_p)

        if p1 then
            if p1 > 1 then
                add(text:sub(last_p or 1, p1 - 1))
            end
            if flag == '|' then
                add('', '|')
            elseif flag == '*' then
                add('', '*', num)
            else
                last_flag = flag
                last_sign = sign
                last_num = num
            end
            last_p = p2 + 1
        end

    until not p1

    if not last_p then
        add(text)

    elseif last_p <= #text then
        add(text:sub(last_p))
    end

    return ret
end


--------------------------------------------------------------------------------
-- 加入自定义节点
--------------------------------------------------------------------------------
function RichText:pushNode(node)
    if not node then return end
    local rich_node = ccui.RichElementCustomNode:create(1, cc.c3b(255, 255, 255), 255, node)
    self:pushBackElement(rich_node)

    -- 强制刷新，计算出大小
    self:formatText()
end


--------------------------------------------------------------------------------
-- 按顺序获取自定义图片
--------------------------------------------------------------------------------
function RichText:getCustomImage(index)
    return self.custom_images and self.custom_images[index]
end


return RichText
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 首次进入
--  @date 2019-01-23
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbFirst = class('OrbFirst', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbFirst:ctor(panel)
    print('OrbFirst:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('btn_open'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOpen) )
    self.m_panel:child('btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
end


-------------------------------------------------------------------------------
-- 拆红包按钮点击
-------------------------------------------------------------------------------
function OrbFirst:onBtnOpen(panel)
    self:request('open', {}, function(data, response, http_status)
        if not helper.app.urlErrorCheck(data, response, http_status) then
            return
        end
        self:updateData({balance = data.data.balance, status = data.data.status})
        self:addOpenLog(data.data.log)
        self:open('panel_gain_first')
    end)
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function OrbFirst:onBtnClose()
    self:closeTop()
end


return OrbFirst
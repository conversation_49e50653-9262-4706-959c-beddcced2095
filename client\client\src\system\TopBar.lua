-------------------------------------------------------------------------------
--  创世版1.0
--  通用TopBar处理类
--      方法都为静态
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
local TopBar = class("TopBar")


-------------------------------------------------------------------------------
-- 初始化
--  top_bar: 显示对象对应
--          top_bar
--              |_top
--                 |_btn_back
--              |_bg
--
-------------------------------------------------------------------------------
function TopBar.init(top_bar, owner, onback_call)
    if not top_bar then return end

    local top = top_bar:child('top')
    if not top then return end

    TopBar.updateFangka(top)

    if not onback_call then
        onback_call = function(a)
            a:removeFromParent()
        end
    end
    local btn_back = top:child('btn_back')
    btn_back:addTouchEventListener( helper.app.commClickHandler(owner, onback_call) )
    local btn_add_fangka = top:child('btn_add_fangka')
    if yl.is_reviewing or not cs.app.FANGKA_DIAMOND then -- 审核模式下游客可见，微信不可见，反之则反
        btn_add_fangka:hide()
    else
        btn_add_fangka:addTouchEventListener( helper.app.commClickHandler(owner, TopBar.onBtnFangka) )
    end
    helper.app.checkFangkDiamond(top:child('icon_fangka'))
end


-------------------------------------------------------------------------------
-- 更新房卡数量
-------------------------------------------------------------------------------
function TopBar.updateFangka(top)
    top:child('label_fangka'):setString( GlobalUserItem.lRoomCard )
end


-------------------------------------------------------------------------------
-- 房卡点击
--  owner: 所在窗口对象
-------------------------------------------------------------------------------
function TopBar.onBtnFangka(owner)
    helper.link.toFangka()
end


return TopBar
# LHMJ313 开发指南文档

## 开发环境搭建

### 1. 基础环境要求

#### 必需软件
- **Cocos2d-x**: 3.13 版本
- **Python**: 2.7+ (用于构建脚本)
- **Git**: 版本控制工具

#### 平台特定要求

**Android 开发**
- Android Studio 2.0+
- Android SDK (API Level 22+)
- Android NDK r10e+
- Java JDK 1.8+

**iOS 开发**
- Xcode 7.0+
- iOS SDK 8.0+
- macOS 10.10+

**Windows 开发**
- Visual Studio 2015+
- Windows SDK 8.1+
- CMake 3.1+

### 2. 项目初始化

#### 克隆项目
```bash
git clone <repository_url> lhmj313
cd lhmj313
git submodule update --init --recursive
```

#### 环境变量设置
```bash
# 设置 Cocos2d-x 环境变量
export COCOS_CONSOLE_ROOT=/path/to/cocos2d-x/tools/cocos2d-console/bin
export COCOS_X_ROOT=/path/to/cocos2d-x
export COCOS_TEMPLATES_ROOT=/path/to/cocos2d-x/templates
export PATH=$COCOS_CONSOLE_ROOT:$PATH

# Android 环境变量
export ANDROID_SDK_ROOT=/path/to/android-sdk
export NDK_ROOT=/path/to/android-ndk
export PATH=$ANDROID_SDK_ROOT/tools:$ANDROID_SDK_ROOT/platform-tools:$PATH
```

## 项目结构理解

### 1. 目录结构
```
lhmj313/
├── client/                 # 客户端代码
│   ├── client/src/        # 主客户端源码
│   ├── game/              # 游戏模块
│   └── package/           # 包配置
├── frameworks/            # Cocos2d-x 框架
├── src/                   # 基础源码
├── res/                   # 资源文件
└── doc/                   # 项目文档
```

### 2. 核心模块

#### 应用程序入口
- **文件**: `client/client/src/main.lua`
- **功能**: 程序启动入口，初始化搜索路径

#### 应用程序主类
- **文件**: `client/client/src/app/MyApp.lua`
- **功能**: 应用程序主类，继承自 MVC 框架

#### 场景管理
- **启动场景**: `client/client/src/app/views/StartScene.lua`
- **登录场景**: `client/client/src/main/LoginScene.lua`
- **主场景**: `client/client/src/main/MainScene.lua`

## 开发工作流程

### 1. 新功能开发

#### 创建新场景
```lua
-- 1. 创建场景文件 client/client/src/app/views/NewScene.lua
local NewScene = class("NewScene", cc.load("mvc").ViewBase)

function NewScene:onCreate()
    -- 加载 CSB 文件
    local main_node = helper.app.loadCSB('NewScene.csb')
    self:addChild(main_node)
    
    -- 初始化界面元素
    self:initUI()
end

function NewScene:initUI()
    -- UI 初始化逻辑
end

return NewScene
```

#### 添加网络通信
```lua
-- 2. 创建网络框架 client/client/src/frame/NewFrame.lua
local NewFrame = class("NewFrame", BaseFrame)

function NewFrame:ctor(scene, callback)
    self.super.ctor(self, scene, callback)
    -- 初始化网络框架
end

function NewFrame:sendRequest(data)
    local cmddata = CCmd_Data:create(0, 0)
    cmddata:setcmdinfo(yl.MDM_CUSTOM, yl.SUB_CUSTOM_REQUEST)
    cmddata:pushstring(cjson.encode(data))
    self:sendSocketData(cmddata)
end

return NewFrame
```

### 2. 游戏模块开发

#### 创建新游戏模块
```bash
# 1. 创建游戏目录结构
mkdir -p client/game/newgame/src
mkdir -p client/game/newgame/res

# 2. 创建游戏配置文件
touch client/game/newgame/src/game.lua
touch client/game/newgame/src/init.lua
```

#### 游戏配置文件
```lua
-- client/game/newgame/src/game.lua
local name = 'newgame'
local game = {
    -- 游戏模块名
    MODULE_NAME = name,
    -- 资源路径
    RES = cs.app.GAME_ROOT .. '/' .. name .. '/res/',
    -- 脚本路径
    SRC = cs.app.GAME_ROOT .. '.' .. name .. '.src.',
    -- 最多人数
    MAX_PLAYER = 4,
    -- 品牌
    BRAND = '云来',
    
    -- 游戏类型配置
    [500] = {
        NAME = '新游戏',
        ZHUANG = true,
        FENG = true,
        JUSHU = '8局,4;16局,8',
        RENSHU = '4;3;2',
        DIFEN = '1;2;5',
        ZHIFU = '0;1',
        WANFA_COL = 2,
        WANFA = '0,1,0|1,1,0',
        RULE0 = '规则一',
        RULE1 = '规则二',
    },
}

return game
```

### 3. UI 界面开发

#### CSB 文件使用
```lua
-- 加载 CSB 文件
local main_node = helper.app.loadCSB('MainLayer.csb')

-- 获取子节点
local btn_start = main_node:child('btn_start')
local label_title = main_node:child('label_title')

-- 批量获取节点
local btn1, btn2, btn3 = main_node:child('btn1,btn2,btn3')

-- 设置节点属性
btn_start:setString('开始游戏')
label_title:setString('游戏标题')
```

#### 事件处理
```lua
-- 添加触摸事件监听
helper.logic.addListenerByName(self, {btn_start, btn_exit})

-- 事件处理函数
function MyScene:onBtnStart()
    print('开始游戏按钮被点击')
    -- 处理开始游戏逻辑
end

function MyScene:onBtnExit()
    print('退出按钮被点击')
    -- 处理退出逻辑
end
```

## 编码规范

### 1. Lua 编码规范

#### 命名规范
```lua
-- 类名使用 PascalCase
local MainScene = class("MainScene", ViewBase)

-- 函数名使用 camelCase
function MainScene:onCreate()
end

-- 变量名使用 camelCase
local playerCount = 4
local gameConfig = {}

-- 常量使用 UPPER_CASE
local MAX_PLAYER_COUNT = 4
local DEFAULT_TIMEOUT = 30
```

#### 代码组织
```lua
-- 1. 引入依赖
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')

-- 2. 类定义
local MainScene = class("MainScene", cc.load("mvc").ViewBase)

-- 3. 常量定义
local TAG_BUTTON_START = 1001
local TAG_BUTTON_EXIT = 1002

-- 4. 构造函数
function MainScene:ctor(app, name)
    self.super.ctor(self, app, name)
    -- 初始化代码
end

-- 5. 公共方法
function MainScene:onCreate()
    -- 创建方法
end

-- 6. 私有方法
function MainScene:initUI()
    -- 私有方法
end

-- 7. 事件处理
function MainScene:onBtnStart()
    -- 事件处理
end

-- 8. 返回类
return MainScene
```

### 2. 注释规范

#### 文件头注释
```lua
-------------------------------------------------------------------------------
--  LHMJ313 项目
--  主场景控制器
--  @date 2025-07-23
--  @auth developer
-------------------------------------------------------------------------------
```

#### 函数注释
```lua
-------------------------------------------------------------------------------
-- 初始化游戏界面
-- @param gameType 游戏类型
-- @param playerCount 玩家数量
-- @return 是否初始化成功
-------------------------------------------------------------------------------
function MainScene:initGame(gameType, playerCount)
    -- 函数实现
end
```

#### 行内注释
```lua
local playerData = {}  -- 玩家数据缓存
self.m_gameStarted = false  -- 游戏是否已开始
```

## 调试技巧

### 1. 日志输出

#### 基础日志
```lua
-- 普通日志
print('调试信息:', value)

-- 格式化日志
print(string.format('玩家 %d 加入房间 %d', playerId, roomId))

-- 条件日志
if DEBUG then
    print('调试模式下的日志')
end
```

#### 结构化日志
```lua
-- 使用 dump 函数输出复杂数据
dump(playerData, '玩家数据')

-- 使用 LogAsset 记录日志
LogAsset:getInstance():logData('重要日志信息', true)
```

### 2. 断点调试

#### 使用 IDE 调试
- 在 IDE 中设置断点
- 启动调试模式
- 单步执行代码

#### 手动断点
```lua
-- 在代码中插入断点
if DEBUG then
    error('手动断点')  -- 会中断执行
end
```

### 3. 性能分析

#### 内存监控
```lua
-- 监控内存使用
local memBefore = collectgarbage('count')
-- 执行代码
local memAfter = collectgarbage('count')
print('内存使用:', memAfter - memBefore, 'KB')
```

#### 时间测量
```lua
-- 测量执行时间
local startTime = os.clock()
-- 执行代码
local endTime = os.clock()
print('执行时间:', (endTime - startTime) * 1000, 'ms')
```

## 测试指南

### 1. 单元测试

#### 测试框架
```lua
-- 简单的测试框架
local TestFramework = {}

function TestFramework.assertEqual(expected, actual, message)
    if expected ~= actual then
        error(string.format('断言失败: %s, 期望 %s, 实际 %s', 
              message or '', tostring(expected), tostring(actual)))
    end
end

function TestFramework.assertTrue(condition, message)
    if not condition then
        error('断言失败: ' .. (message or '条件为假'))
    end
end
```

#### 测试用例
```lua
-- 测试游戏逻辑
function testGameLogic()
    local game = GameLogic:new()
    
    -- 测试初始化
    game:init()
    TestFramework.assertEqual(0, game:getPlayerCount(), '初始玩家数量应为0')
    
    -- 测试添加玩家
    game:addPlayer(1001)
    TestFramework.assertEqual(1, game:getPlayerCount(), '添加玩家后数量应为1')
end
```

### 2. 集成测试

#### 网络测试
```lua
-- 测试网络连接
function testNetworkConnection()
    local frame = TestFrame:new()
    local connected = frame:connect('test.server.com', 8080)
    TestFramework.assertTrue(connected, '网络连接应该成功')
end
```

#### UI 测试
```lua
-- 测试界面元素
function testUIElements()
    local scene = TestScene:new()
    scene:onCreate()
    
    local button = scene:getButton('btn_start')
    TestFramework.assertTrue(button ~= nil, '开始按钮应该存在')
    TestFramework.assertTrue(button:isVisible(), '开始按钮应该可见')
end
```

## 性能优化

### 1. 内存优化

#### 及时释放资源
```lua
-- 清理纹理缓存
cc.Director:getInstance():getTextureCache():removeUnusedTextures()
cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()

-- 清理 Lua 模块
helper.app.cleanPackages('game.oldgame.src.')

-- 手动垃圾回收
collectgarbage('collect')
```

#### 对象池模式
```lua
-- 对象池实现
local ObjectPool = {}
ObjectPool.__index = ObjectPool

function ObjectPool:new()
    local pool = {
        objects = {},
        createFunc = nil,
        resetFunc = nil,
    }
    setmetatable(pool, ObjectPool)
    return pool
end

function ObjectPool:getObject()
    if #self.objects > 0 then
        return table.remove(self.objects)
    else
        return self.createFunc()
    end
end

function ObjectPool:returnObject(obj)
    if self.resetFunc then
        self.resetFunc(obj)
    end
    table.insert(self.objects, obj)
end
```

### 2. 渲染优化

#### 批量渲染
```lua
-- 使用 SpriteBatchNode 进行批量渲染
local batchNode = cc.SpriteBatchNode:create('sprites.png')
self:addChild(batchNode)

for i = 1, 100 do
    local sprite = cc.Sprite:createWithSpriteFrameName('sprite_frame.png')
    batchNode:addChild(sprite)
end
```

#### 减少 Draw Call
```lua
-- 合并纹理
-- 使用纹理图集
-- 减少节点层次
```

### 3. 网络优化

#### 消息合并
```lua
-- 合并多个小消息为一个大消息
local messageQueue = {}

function queueMessage(message)
    table.insert(messageQueue, message)
    
    if #messageQueue >= 10 then
        sendBatchMessages(messageQueue)
        messageQueue = {}
    end
end
```

#### 数据压缩
```lua
-- 使用 JSON 压缩数据
local compressedData = cjson.encode(data)

-- 使用二进制格式减少数据量
```

## 常见问题解决

### 1. 编译问题

#### Android 编译错误
```bash
# NDK 版本不匹配
# 解决方案：使用推荐的 NDK 版本 r10e

# Gradle 版本问题
# 解决方案：检查 gradle-wrapper.properties 中的版本配置
```

#### iOS 编译错误
```bash
# 证书问题
# 解决方案：检查开发者证书和 Provisioning Profile

# 架构不匹配
# 解决方案：确保 Valid Architectures 设置正确
```

### 2. 运行时问题

#### Lua 脚本错误
```lua
-- 模块找不到
-- 解决方案：检查搜索路径设置
cc.FileUtils:getInstance():addSearchPath("path/to/scripts/")

-- 内存泄漏
-- 解决方案：及时释放不用的对象和资源
```

#### 网络连接问题
```lua
-- 连接超时
-- 解决方案：增加超时时间，检查网络状态

-- 数据解析错误
-- 解决方案：验证数据格式，添加错误处理
```

## 最佳实践

### 1. 代码组织
- 按功能模块组织代码
- 使用清晰的命名规范
- 保持函数简洁，单一职责
- 及时重构重复代码

### 2. 资源管理
- 合理使用纹理图集
- 及时释放不用的资源
- 使用对象池减少内存分配
- 预加载关键资源

### 3. 网络通信
- 实现断线重连机制
- 添加网络状态检测
- 使用消息队列管理网络消息
- 实现数据校验和错误处理

### 4. 用户体验
- 添加加载进度提示
- 实现平滑的场景切换
- 提供友好的错误提示
- 支持多分辨率适配

---

*本文档基于项目代码自动生成，最后更新时间: 2025-07-23*

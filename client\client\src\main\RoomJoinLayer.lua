-------------------------------------------------------------------------------
--  创世版1.0
--  房间加入
--      父类：RoomJoinBase
--      目前考虑到该功能比较通用，因此主要逻辑都写在父类中
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local RoomJoinBase = cs.app.client('system.RoomJoinBase')
local RoomJoinLayer = class('RoomJoinLayer', RoomJoinBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RoomJoinLayer:ctor()
    self.super.ctor(self)
end


return RoomJoinLayer
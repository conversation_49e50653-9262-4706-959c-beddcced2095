-------------------------------------------------------------------------------
--  创世版1.0
--  规则查看
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local RuleLayer = class("RuleLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function RuleLayer:ctor(init_kind)
    self.m_init_kind = init_kind
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('RuleLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)
    main_node:child('tab_template'):hide()

    -- 初始化TopBar
    helper.logic.initTopBar(main_node, self)
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function RuleLayer:onEnterTransitionFinish()
    print('RuleLayer:onEnterTransitionFinish...')
    self:createKindList()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function RuleLayer:onExit()
    print('RuleLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 游戏列表生成
-------------------------------------------------------------------------------
function RuleLayer:createKindList()
    local kind_first = nil
    local listview = self.main_node:child('listview_kind')
    local template = self.main_node:child('tab_template')
    for i, plugin in ipairs(cs.app.plugins) do repeat
        local kind = plugin.kind
        if not kind then break end
        if yl.is_reviewing and plugin.review then break end
        if not cc.FileUtils:getInstance():isFileExist('common/rule_' .. kind .. '.png') then break end

        local tab = template:clone():show()
        tab.kind = kind
        tab.group = 1
        tab:child('text'):setString(plugin.name)
        helper.layout.scaleToWidth(tab:child('text'), template:size().width * 0.7)
        helper.logic.initImageCheck(tab, false, 'common/btn_tab_s.png', 'common/btn_tab_n.png', 
            handler(self, self.onBtnKind))
        listview:pushBackCustomItem(tab)

        if self.m_init_kind == kind then kind_first = tab end
    until true end

    if not kind_first then
	    kind_first = listview:getItem(0)
	    local last_kind = cc.UserDefault:getInstance():getStringForKey('GameKind', '')
	    if last_kind ~= '' then
		    for i, item in ipairs( listview:getItems() ) do
			    if tostring(item.kind) == last_kind then
				    kind_first = item
				    break
			    end
		    end
	    end
    end
    if kind_first then
        helper.layout.scrollToItem(listview, kind_first)
    end

    template:removeFromParent()

    kind_first:toggle()
    self:onBtnKind(kind_first)
end


-------------------------------------------------------------------------------
-- 游戏类型点击
-------------------------------------------------------------------------------
function RuleLayer:onBtnKind(sender)
    local kind = sender.kind
    local listview = self.main_node:child('listview_content')
    local img = ccui.ImageView:create()
    img:texture('common/rule_' .. kind .. '.png')
    listview:removeAllItems()
    listview:pushBackCustomItem(img)
    listview:jumpToTop()
end


return RuleLayer
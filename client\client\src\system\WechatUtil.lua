-------------------------------------------------------------------------------
--  创世版1.0
--  微信相关
--  @date 2017-08-24
--  @auth woodoo
-------------------------------------------------------------------------------

local WechatUtil = {}

local url_refresh_token = 'https://api.weixin.qq.com/sns/oauth2/refresh_token?'
local url_get_user_info = 'https://api.weixin.qq.com/sns/userinfo?'


-------------------------------------------------------------------------------
-- 检查本地授权信息
--  login_call: 授权登录
--  after_call: 续期成功后
-------------------------------------------------------------------------------
function WechatUtil.checkToken(login_call, after_call)
    WechatUtil._login_call = login_call
    WechatUtil._after_call = after_call
	local access_token = cc.UserDefault:getInstance():getStringForKey('access_token')
    local refresh_token = cc.UserDefault:getInstance():getStringForKey('refresh_token')
    local openid = cc.UserDefault:getInstance():getStringForKey('openid')
    local expires_at = cc.UserDefault:getInstance():getIntegerForKey('expires_at', 0)

	print( string.format('logonWeiXin access_token:%s, refresh_token:%s, openid:%s', access_token, refresh_token, openid) )

	if access_token ~= '' and openid ~= '' then
		print('logonWeiXin request userinfo...')
		if expires_at == 0 or os.time() + 120 >= expires_at then	-- +120是提前2分钟续期
			WechatUtil.refreshToken(refresh_token)
		else
			WechatUtil.getUserInfo(access_token, openid)
        end
	else
		print('logonWeiXin do login...')
		login_call()
	end
end


-------------------------------------------------------------------------------
-- 续期
-------------------------------------------------------------------------------
function WechatUtil.refreshToken(refresh_token)
	print('refreshToken .....')
    helper.pop.waiting()
    local params = {appid=yl.WeChat.AppID, grant_type='refresh_token', refresh_token=refresh_token}
    appdf.onHttpJsionTable(url_refresh_token, 'post', helper.str.makeUrlParams(params), WechatUtil.onRefreshToken)
end


-------------------------------------------------------------------------------
-- 返回通用处理
-------------------------------------------------------------------------------
function WechatUtil.errorCheck(data, response, http_status, flag)
    print(response)
    helper.pop.waiting(false)

    if type(data) ~= 'table' then
        print(flag .. ' json error, do relogin...')
        WechatUtil._login_call()    -- 续期失败，直接登录
        return false
    end

	if data.errcode and data.errcode ~= 0 then
		print(flag .. ' error, do relogin...', data.errcode, data.errmsg)
		WechatUtil._login_call()    -- 续期失败，直接登录
		return false
	end

    return true
end


-------------------------------------------------------------------------------
-- 续期返回
-------------------------------------------------------------------------------
function WechatUtil.onRefreshToken(data, response, http_status)
    if not WechatUtil.errorCheck(data, response, http_status, 'onRefreshToken') then return end
    WechatUtil.saveToken(data)
    WechatUtil.getUserInfo(data.access_token, data.openid)
end


-------------------------------------------------------------------------------
-- 保存信息
-------------------------------------------------------------------------------
function WechatUtil.saveToken(data)
	cc.UserDefault:getInstance():setStringForKey('access_token', data.access_token)
	cc.UserDefault:getInstance():setStringForKey('refresh_token', data.refresh_token or data.refreshToken)
	cc.UserDefault:getInstance():setStringForKey('openid', data.openid)
	cc.UserDefault:getInstance():setStringForKey('unionid', data.unionid or '')
	cc.UserDefault:getInstance():setIntegerForKey('expires_at', os.time() + data.expires_in)
	cc.UserDefault:getInstance():flush()
end


-------------------------------------------------------------------------------
-- 删除信息（通常是切换账号时使用）
-------------------------------------------------------------------------------
function WechatUtil.removeToken()
	cc.UserDefault:getInstance():deleteValueForKey('access_token')
	cc.UserDefault:getInstance():deleteValueForKey('refresh_token')
	cc.UserDefault:getInstance():deleteValueForKey('openid')
	cc.UserDefault:getInstance():deleteValueForKey('unionid')
	cc.UserDefault:getInstance():deleteValueForKey('expires_at')
	cc.UserDefault:getInstance():flush()
end


-------------------------------------------------------------------------------
-- 获取用户信息
-------------------------------------------------------------------------------
function WechatUtil.getUserInfo(access_token, openid)
    helper.pop.waiting()
	local params = {access_token=access_token, openid=openid}
    appdf.onHttpJsionTable(url_get_user_info, 'post', helper.str.makeUrlParams(params), WechatUtil.onGetUserInfo)
end


-------------------------------------------------------------------------------
-- 获取用户信息
-------------------------------------------------------------------------------
function WechatUtil.onGetUserInfo(data, response, http_status)
    if not WechatUtil.errorCheck(data, response, http_status, 'onGetUserInfo') then return end

	print('onGetUserInfo silence login ok')

	cc.UserDefault:getInstance():setStringForKey('openid', data.openid)
	cc.UserDefault:getInstance():setStringForKey('unionid', data.unionid or '')
	cc.UserDefault:getInstance():flush()

    local datatable = {
        unionid             = data.unionid,
        screen_name         = data.nickname,
        profile_image_url   = data.headimgurl,
        gender              = data.sex
    }

    WechatUtil._after_call(datatable)
end


return WechatUtil
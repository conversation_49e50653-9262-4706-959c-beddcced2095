-------------------------------------------------------------------------------
--  创世版1.0
--  通用弹出规则框
--  @date 2018-01-19
--  @auth mike
-------------------------------------------------------------------------------
local PopupMask	= cs.app.client('system.PopupMask')

local PopupRuleLayer = class('PopupRuleLayer', PopupMask)

-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
function PopupRuleLayer:ctor( msg_array )
    self.super.ctor(self)
    self:zorder(0)  -- 因为PopupMask的默认zorder是负的

    local main_node = helper.app.loadCSB('PopRuleLayer.csb')
    self.main_node = main_node
    self:addChild( main_node )

    local list_view = main_node:child('img_bg/list_view')
    list_view:removeAllItems()

    for i, msg in ipairs( msg_array ) do
        local label = cc.Label:createWithSystemFont(msg, cs.app.FONT_NAME, 24, cc.size(480, 0))
        label:anchor(cc.p(0,0))
        label:setColor(cc.c3b(111, 54, 37))
        local item = ccui.Layout:create()
        item:setContentSize(label:getContentSize())
        item:addChild(label)
        list_view:pushBackCustomItem( item )
    end
    
    main_node:child('img_bg/btn_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClick) )
    main_node:anchor(0.5, 0.5):pos(self:size().width/2, self:size().height/2):setScale(0.8)
    main_node:runMyAction(cc.EaseBackOut:create(cc.ScaleTo:create(0.2, 1)))
end

-------------------------------------------------------------------------------
-- 按钮点击
-------------------------------------------------------------------------------
function PopupRuleLayer:onBtnClick(sender, event)
    self:removeFromParent()
end

return PopupRuleLayer
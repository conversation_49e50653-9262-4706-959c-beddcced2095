
cc.FileUtils:getInstance():setPopupNotify(false)
cc.FileUtils:getInstance():addSearchPath("client/src/")
cc.FileUtils:getInstance():addSearchPath("client/res/")

require "config"
require "cocos.init"

cc.FileUtils:getInstance():addSearchPath(device.writablePath.."download/", true)
cc.FileUtils:getInstance():addSearchPath(device.writablePath.."client/res/", true)
if device.platform ~= "windows" then
	cc.FileUtils:getInstance():addSearchPath(device.writablePath, true)
end

local function main()
    
    require("client.src.app.MyApp"):create():run()
end

local status, msg = xpcall(main, __G__TRACKBACK__)
if not status then
    print(msg)
end

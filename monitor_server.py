#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
import urllib3
from datetime import datetime

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_server_status():
    """检查服务器状态"""
    
    test_urls = [
        'http://lhmj.tuo3.com.cn',
        'http://lhmj.tuo3.com.cn:8607',
        'https://lhmj.tuo3.com.cn'
    ]
    
    results = {}
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10, verify=False)
            results[url] = {
                'status': 'OK' if response.status_code == 200 else f'HTTP {response.status_code}',
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            results[url] = {
                'status': 'ERROR',
                'error': str(e)[:100]
            }
    
    return results

def monitor_loop():
    """持续监控循环"""
    print("开始监控服务器状态...")
    print("按 Ctrl+C 停止监控")
    print("="*60)
    
    try:
        while True:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n[{timestamp}] 检查服务器状态:")
            
            results = check_server_status()
            
            all_ok = True
            for url, result in results.items():
                status = result.get('status', 'UNKNOWN')
                if status == 'OK':
                    print(f"  ✅ {url} - {status} ({result.get('response_time', 0):.2f}s)")
                elif 'HTTP' in status:
                    print(f"  ⚠️  {url} - {status}")
                    if status != 'HTTP 502':  # 如果不是502，说明有改善
                        all_ok = False
                else:
                    print(f"  ❌ {url} - {status}")
                    if 'error' in result:
                        print(f"     错误: {result['error']}")
                    all_ok = False
            
            if all_ok:
                print("🎉 服务器已恢复正常！")
                break
            
            # 等待30秒后再次检查
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == '__main__':
    monitor_loop()

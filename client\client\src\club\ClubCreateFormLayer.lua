-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部创建表单
--  @date 2018-01-13
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local LiveFrame = cs.app.client('frame.LiveFrame')
local EditBox = cs.app.client('system.EditBox')
local districts = cs.app.client('system.district')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')


local ClubCreateFormLayer = class("ClubCreateFormLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubCreateFormLayer:ctor()
    print('ClubCreateFormLayer:ctor...')
    local main_node = ClubUtil.initUI(self, 'ClubCreateFormLayer.csb')

    main_node:child('panel_logo'):zorder(100):hide()
    main_node:child('img_logo'):texture('common/icon_club_logo_1.png')
    main_node:child('panel_logo'):addTouchEventListener( handler(self, self.onPanelLogo) )

    local editor = EditBox.convertTextField( main_node:child('input_name'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    editor:setPlaceholderFontColor(cc.c3b(220, 220, 220))
    editor = EditBox.convertTextField( main_node:child('input_weixin'), 'common/bg_transparent.png', ccui.TextureResType.localType)
    editor:setPlaceholderFontColor(cc.c3b(220, 220, 220))

    helper.logic.addListenerByName(self, {main_node:child('btn_province, btn_city, btn_district, btn_ok, img_logo')}, {'tint', 'tint', 'tint', 'tint'})

    main_node:child('label_rule'):setRich( LANG.CLUB_CREATE_RULE )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onEnter()
    print('ClubCreateFormLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_CREATE, self, self.onCreateResp)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onExit()
    print('ClubCreateFormLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 头像列表初始化
-------------------------------------------------------------------------------
function ClubCreateFormLayer:initLogoList()
    local panel, listview = self.main_node:child('panel_logo, panel_logo/listview')
    local index = 0
    local l_size = listview:size()
    local row = nil
    local logos = {}
    for i = 1, 16 do
        local path = 'common/icon_club_logo_' .. i .. '.png'
        if cc.FileUtils:getInstance():isFileExist(path) then
            local item = ccui.ImageView:create()
            item:texture(path)
            local t_size = item:size()
            index = index + 1
            if index % 4 == 1 then
                row = ccui.Layout:create()
                row:size(l_size.width, t_size.height)
                listview:pushBackCustomItem(row)
            end
            item.index = i
            item:setTouchEnabled(true)
            item:addTouchEventListener( helper.app.tintClickHandler(self, self.onLogoSelect) )
            local col = (index - 1) % 4
            item:pos((2 * col + 1) * t_size.width / 2 , t_size.height/2):addTo(row)
            table.insert(logos, item)
        end
    end
    self.m_logos = logos
end


-------------------------------------------------------------------------------
-- 头像选择点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onImgLogo(sender)
    if not self.m_logos then
        self:initLogoList()
    end

    self.main_node:child('panel_logo'):show()
    self.main_node:child('panel_logo/listview'):jumpToTop()

    local img_logo = self.main_node:child('img_logo')
    local pos = img_logo:getParent():convertToWorldSpace(cc.p(img_logo:pos()))

    for i, item in ipairs(self.m_logos) do
        local p = item:getParent():convertToNodeSpace(pos)
        if not item._origin_pos then
            item._origin_pos = cc.p(item:pos())
        end
        local pp = item._origin_pos
        item:stop():pos(p):runAction( cc.Sequence:create(
            cc.DelayTime:create(0.03*(i-1)),
            cc.EaseExponentialOut:create( cc.MoveTo:create(0.3, pp) )
        ) )
    end
end


-------------------------------------------------------------------------------
-- logo面板点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onPanelLogo(sender, event)
    if event == cc.EventCode.BEGAN and not sender._on_close then
        sender._on_close = true
        local img_logo = self.main_node:child('img_logo')
        local pos = img_logo:getParent():convertToWorldSpace(cc.p(img_logo:pos()))
        for i, item in ipairs(self.m_logos) do
            local p = item:getParent():convertToNodeSpace(pos)
            local pp = cc.p(item:pos())
            item:stop():runAction( cc.Sequence:create(
                cc.EaseExponentialIn:create( cc.MoveTo:create(0.15, p) )
            ) )
        end
        sender:perform(function(sender)
            sender._on_close = false
            sender:hide()
        end, 0.15)
    end
end


-------------------------------------------------------------------------------
-- 头像选择
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onLogoSelect(sender)
    self.m_logo_id = sender.index
    self.main_node:child('img_logo'):texture('common/icon_club_logo_' .. self.m_logo_id .. '.png')
    self:onPanelLogo(self.main_node:child('panel_logo'), cc.EventCode.BEGAN)
end


-------------------------------------------------------------------------------
-- 获取下拉位置参数
-------------------------------------------------------------------------------
function ClubCreateFormLayer:getDropParams(name)
    local btn = self.main_node:child(name)
    local size = btn:size()
    local pos = cc.p(btn:px(), btn:py() - size.height/2)
    size.height = 280
    return size, pos
end


-------------------------------------------------------------------------------
-- 省份选择点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onBtnProvince(sender)
    self.m_cur_label = sender:child('text')

    -- 省份列表较多，每次创建太慢，单独创建一个固定的
    if not self.m_drop_rovince then
        local onRemove = function()
            self.m_drop_rovince:hide()
        end
        local size, pos = self:getDropParams('btn_province')
        self.m_drop_rovince = helper.pop.drop(districts.provinces, size, pos, 
            handler(self, self.onDropClick), onRemove, nil, self):zorder(100)
    else
        self.m_drop_rovince:show():showDrop()
    end
end


-------------------------------------------------------------------------------
-- 市选择点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onBtnCity(sender)
    local province = self.main_node:child('btn_province/text'):getString()
    if province == LANG.CLUB_SELECT then return end

    self.m_cur_label = sender:child('text')
    local size, pos = self:getDropParams('btn_city')
    helper.pop.drop(districts.cities[province], size, pos, handler(self, self.onDropClick))
end


-------------------------------------------------------------------------------
-- 地区选择点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onBtnDistrict(sender)
    local city = self.main_node:child('btn_city/text'):getString()
    if city == LANG.CLUB_SELECT then return end
    local province = self.main_node:child('btn_province/text'):getString()

    self.m_cur_label = sender:child('text')
    local size, pos = self:getDropParams('btn_district')
    helper.pop.drop(districts.districts[province .. '-' .. city], size, pos, handler(self, self.onDropClick))
end


-------------------------------------------------------------------------------
-- 下拉选项点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onDropClick(value)
    self.m_cur_label:setString(value)

    -- 调整大小适应显示框
    local size = self.m_cur_label:size()
    local p_size = self.m_cur_label:getParent():size()
    local scale = 1
    if size.width > p_size.width - 50 then
        scale = (p_size.width - 50) / size.width
    end
    self.m_cur_label:scale(scale)

    -- 前面的改变，要清除后面的
    local btn_name = self.m_cur_label:getParent():getName()
    if btn_name == 'btn_province' then
        self.main_node:child('btn_city/text'):scale(1):setString(LANG.CLUB_SELECT)
        self.main_node:child('btn_district/text'):scale(1):setString(LANG.CLUB_SELECT)
    elseif btn_name == 'btn_city' then
       self.main_node:child('btn_district/text'):scale(1):setString(LANG.CLUB_SELECT)
    end
end


-------------------------------------------------------------------------------
-- 创建按钮点击
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onBtnOk(sender)
    local name = self.main_node:child('input_name'):getString():trim()
    if name == '' then
        helper.pop.message( LANG.CLUB_NAME_EMPTY )
        return
    end
    local name_len = ClubUtil.getUTF8Len(name)
    if name_len > ClubUtil.CLUB_NAME_LEN then
        helper.pop.message( LANG{'CLUB_NAME_BEYOND', max=ClubUtil.CLUB_NAME_LEN} )
        return
    end

    local weixin = self.main_node:child('input_weixin'):getString():trim()
    if weixin == '' then
        helper.pop.message( LANG.CLUB_WEIXIN_EMPTY )
        return
    end

    local label_province = self.main_node:child('btn_province/text')
    local label_city = self.main_node:child('btn_city/text')
    local label_district = self.main_node:child('btn_district/text')
    local province = label_province:getString()
    local city = label_city:getString()
    local district = label_district:getString()
    if province == LANG.CLUB_SELECT or city == LANG.CLUB_SELECT or district == LANG.CLUB_SELECT then
        helper.pop.message( LANG.CLUB_SELECT_PLEASE )
        return
    end

    local logo_id = self.m_logo_id or 1

    local values = {
        dwClubID            = 0,
        dwPresidentID       = 0,
        sPresidentAccount   = weixin,
        szName              = name,
        nLogoID             = logo_id,
        szProvince          = province,
        szCity              = city,
        szArea              = district,
        szMsg               = '',
        nMemberCount        = 0,
        szPresidentName     = '',
    }
    ClubUtil.send(cmd.SUB_CLUB_CREATE, cmd.tagClub, values)
end


-------------------------------------------------------------------------------
-- 俱乐部创建返回
-------------------------------------------------------------------------------
function ClubCreateFormLayer:onCreateResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd.tagClub)
    if not ret then return end

    -- 判断是否成功
    if ret.dwClubID == 0 then
        helper.pop.message(ret.szMsg)
    else
        helper.pop.message( LANG.CLUB_CREATE_SUCC ) -- 成功只提示即可，还需审核
        
        --[[
        -- 设置父窗口标记
        local parent = helper.app.getFromScene('club_create_layer')
        if parent then
            parent:onClubCreated(ret)
        end
        --]]
    end

    ClubUtil.back(self)
    self:removeFromParent()
end


return ClubCreateFormLayer

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_room_act_zhadan_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,512},{58,62}}</string>
                <key>offset</key>
                <string>{1,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{37,37},{58,62}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_10.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,424},{94,86}}</string>
                <key>offset</key>
                <string>{-4,-5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{14,27},{94,86}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_11.png</key>
            <dict>
                <key>frame</key>
                <string>{{96,418},{94,86}}</string>
                <key>offset</key>
                <string>{-4,-5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{14,27},{94,86}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_12.png</key>
            <dict>
                <key>frame</key>
                <string>{{96,506},{92,82}}</string>
                <key>offset</key>
                <string>{-5,-5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{14,29},{92,82}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_13.png</key>
            <dict>
                <key>frame</key>
                <string>{{236,218},{1,1}}</string>
                <key>offset</key>
                <string>{-64.5,64.5}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{1,1}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{108,318},{104,98}}</string>
                <key>offset</key>
                <string>{2,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{15,14},{104,98}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,0},{118,124}}</string>
                <key>offset</key>
                <string>{3,-3}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{9,6},{118,124}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,120},{124,114}}</string>
                <key>offset</key>
                <string>{3,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{6,6},{124,114}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{126,0},{116,118}}</string>
                <key>offset</key>
                <string>{2,4}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{9,2},{116,118}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{126,118},{116,98}}</string>
                <key>offset</key>
                <string>{-4,-3}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{3,19},{116,98}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,236},{106,96}}</string>
                <key>offset</key>
                <string>{-5,-4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{7,21},{106,96}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{126,218},{108,98}}</string>
                <key>offset</key>
                <string>{-3,-4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{8,20},{108,98}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
            <key>ani_room_act_zhadan_9.png</key>
            <dict>
                <key>frame</key>
                <string>{{0,334},{94,88}}</string>
                <key>offset</key>
                <string>{-4,-4}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{14,25},{94,88}}</string>
                <key>sourceSize</key>
                <string>{130,130}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_room_act_zhadan.png</string>
            <key>size</key>
            <string>{244,588}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:9b109102f2fdafc318fb5b93627bf29a:b718d0ce68b7377184bcbc4560c82404:9f6228b53c87043b0f9ae92f1a861b8c$</string>
            <key>textureFileName</key>
            <string>ani_room_act_zhadan.png</string>
        </dict>
    </dict>
</plist>

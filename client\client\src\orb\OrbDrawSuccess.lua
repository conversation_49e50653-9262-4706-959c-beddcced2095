-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 提现成功
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbDrawSuccess = class('OrbDrawSuccess', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbDrawSuccess:ctor(panel)
    print('OrbDrawSuccess:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('btn_my'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnMy) )
end


-------------------------------------------------------------------------------
-- 显示事件
-------------------------------------------------------------------------------
function OrbDrawSuccess:onShow(params)
    self.m_panel:child('label_amount'):setString(string.format('%.2f', params.amount))

    if not self.m_avatar_drawed then
        self.m_avatar_drawed = true
        local icon = self.m_panel:child('bg_avatar')
        local head = self:createHead(GlobalUserItem.dwUserID, GlobalUserItem.szHeadHttp, icon:size().width, 'orb/orb_bg_avatar_75.png')
        head:pos(icon:pos()):addTo(self.m_panel)
        icon:hide()
    end
end


-------------------------------------------------------------------------------
-- 查看我的红包按钮点击
-------------------------------------------------------------------------------
function OrbDrawSuccess:onBtnMy()
    self:close()
    self:update('panel_main')
    self:open('panel_my')
end


return OrbDrawSuccess
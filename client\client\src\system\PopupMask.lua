-------------------------------------------------------------------------------
--  创世版1.0
--  弹窗蒙版
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
local PopupMask = class("PopupMask", ccui.Layout)


-------------------------------------------------------------------------------
-- 构造
--	opacity：蒙版透明度
--	mask_closable：点击蒙版是否关闭
-------------------------------------------------------------------------------
function PopupMask:ctor(opacity, mask_closable, no_blur)
    no_blur = true  -- todo: 暂时屏蔽
	self.model_opacity = opacity or ((no_blur or not SpriteBlur) and 160 or 120)
	self.mask_closable = mask_closable
	self._mask_create_at = yl.time()
    self:setName('mask')
	self:zorder(-100)
	self:size(display.size)
	self:align(display.CENTER, display.cx, display.cy)
	self:setTouchEnabled(true)
	self:setBackGroundColorType(ccui.LayoutBackGroundColorType.solid)
	self:setBackGroundColor(cc.c3b(0, 0, 0))
	self:effectShow()
	self:addTouchEventListener( handler(self, self.onMaskClick) )

    -- 创建模糊背景
    if not no_blur and SpriteBlur then
        local screen_texture = SpriteBlur:ScreenShot()
        local blur_texture = SpriteBlur:SpriteBlurer(screen_texture:getSprite(), 12, 4)
        local sprite = cc.Sprite:createWithSpriteFrame(blur_texture:getSprite():getSpriteFrame())
        sprite:zorder(-1)
        sprite:setRotationSkewX(180)
        sprite:pos(display.width/2, display.height/2):addTo(self)
    end
end


-------------------------------------------------------------------------------
-- 设置透明度
-------------------------------------------------------------------------------
function PopupMask:setModelOpacity(opacity)
	self.model_opacity = opacity
	self:opacity(self.model_opacity)
end


-------------------------------------------------------------------------------
-- 设置透动画
-------------------------------------------------------------------------------
function PopupMask:effectShow()
	self:opacity(0)
	self:runMyAction(cc.FadeTo:create(0.3, self.model_opacity))
end


-------------------------------------------------------------------------------
-- 无效遮罩
-------------------------------------------------------------------------------
function PopupMask:disableMask()
	self:hide()
end


-------------------------------------------------------------------------------
-- 蒙版收到点击事件
-------------------------------------------------------------------------------
function PopupMask:onMaskClick(sender, event)
	if self.mask_closable and (yl.time() - self._mask_create_at > 0.5) and event == cc.EventCode.ENDED then
		if self:getParent().onPopClose then
			self:getParent():onPopClose()
		else
			self:getParent():removeFromParent()
		end
	end
end


return PopupMask
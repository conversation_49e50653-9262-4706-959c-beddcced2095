-------------------------------------------------------------------------------
--  创世版1.0
--  布局显示辅助方法类
--      访问方式：helper.layout.
--  @date 2017-06-06
--  @auth woodoo
-------------------------------------------------------------------------------
local LayoutHelper = {}
helper = helper or {}
helper.layout = LayoutHelper


-------------------------------------------------------------------------------
-- 滚动列表到指定项目
-------------------------------------------------------------------------------
function LayoutHelper.scrollToItem(listview, item)
	listview:doLayout()
	local y = item:getBottomBoundary()
	listview:jumpToPercentVertical(100 - 100 * y / listview:getInnerContainerSize().height)
end


-------------------------------------------------------------------------------
-- 画节点的外形和锚点
--	后面3个参数可选
--	****注意：会增加给node增加子节点
-------------------------------------------------------------------------------
function LayoutHelper.track(node, border_color, fill_color, border_width)
	fill_color = fill_color or cc.c4f(0,0,0,0)
	border_color = border_color or cc.c4f(1,1,0,1)
	border_width = border_width or 0.5

	local size = node:size()

	local shape = display.newRect(cc.rect(0, 0, size.width, size.height),
        {fillColor = fill_color, borderColor = border_color, borderWidth = border_width})
	local anchor_pos = node:getAnchorPointInPoints()
	shape:drawDot(anchor_pos, border_width * 5, border_color)
	shape:addTo(node)
end


-------------------------------------------------------------------------------
-- 从容器中间向两边扩散的通用方法
--  返回坐标x, y
-------------------------------------------------------------------------------
function LayoutHelper.calcEvenPos(parent, node, index, count, gap)
    local p_size = parent:size()
    local n_size = node:size()
    local start_x = p_size.width/2 + (count % 2 == 1 and -math.floor(count/2) * (n_size.width + gap) or -(count - 1) * 0.5 * (n_size.width + gap))
    local x = start_x + (index - 1) * (n_size.width + gap)
    local start_y = p_size.height/2 + (count % 2 == 1 and math.floor(count/2) * (n_size.height + gap) or (count - 1) * 0.5 * (n_size.height + gap))
    local y = start_y - (index - 1) * (n_size.height + gap)
    return x, y
end


-------------------------------------------------------------------------------
-- 加入到父容器中间
-------------------------------------------------------------------------------
function LayoutHelper.addCenter(parent, node, offset)
    offset = offset or {x=0, y=0}
    local p_size = parent:size()
    local n_size = node:size()
    local scale = node:getScale()
    local anchor = node:anchor()
    node:pos((anchor.x - 0.5) * n_size.width * scale + p_size.width/2 + offset.x, (anchor.y - 0.5) * n_size.height * scale + p_size.height/2 + offset.y):addTo(parent)
end


-------------------------------------------------------------------------------
-- 缩放节点宽带到指定大小
-------------------------------------------------------------------------------
function LayoutHelper.scaleToWidth(node, width)
    if not width or width <= 0 then return end
    if node:size().width > width then
        node:scale( width / node:size().width )
    end
end


-------------------------------------------------------------------------------
-- 插入空白占位节点到listview
-------------------------------------------------------------------------------
function LayoutHelper.pushEmpty(listview, size)
    local layout = ccui.Layout:create()
    layout:size(cc.size(size.width or size[1], size.height or size[2]))
    listview:pushBackCustomItem(layout)
end

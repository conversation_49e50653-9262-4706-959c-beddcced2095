-------------------------------------------------------------------------------
--  创世版1.0
--  世界杯竞猜
--  @date 2018-05-18
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')

-- 投注按钮配置
local bet_configs = {10000, 50000, 200000, 500000}

-- 格式化赔率
local function formatRate(rate)
    return string.format('%.2f', rate/100)
end


local WorldCupGuess = class("WorldCupGuess", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function WorldCupGuess:ctor()
    self:setName('arena_layer')
    self:enableNodeEvents()
    self.m_history = {}
    self.m_my_guesses = {}
    self.m_my_guess_map = {}
    self.m_guess_nodes = {}
    self.m_my_guess_nodes = {}

    -- 载入主UI
    local main_node = helper.app.loadCSB('WorldCupGuess.csb', true)
    self.main_node = main_node
    self:addChild(main_node)

    self.panel_main = main_node:child('panel_main'):hide()
    self.panel_rank = main_node:child('panel_rank'):hide()
    self.panel_bet = main_node:child('panel_bet'):hide()
    self.panel_my = main_node:child('panel_my'):hide()
    self.m_cur_panel = self.panel_main:show()

    self.panel_main:child('template_match'):hide()
    self.panel_my:child('template_match'):hide()
    self.panel_rank:child('template_row'):hide()

    self.panel_rank.toggles = {main_node:child('btn_rank')}
    self.panel_my.toggles = {main_node:child('btn_my')}

    helper.logic.addListenerByName(self, {main_node:child('btn_back,btn_rank,btn_rule,btn_exchange,btn_my')}, {'tint'})

    self:initBetUI()
    self:showPanel(self.panel_main)
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function WorldCupGuess:onEnter()
    print('WorldCupGuess:onEnter...')
    ---[[
    LiveFrame:getInstance():addListen(cmd_common.MDM_GUESS_SERVICE, cmd_common.SUB_GUESS_LIST, self, self.onGuessListResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_GUESS_SERVICE, cmd_common.SUB_GUESS_MY_LIST, self, self.onMyGuessResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_GUESS_SERVICE, cmd_common.SUB_GUESS_BET, self, self.onBetResp)
    LiveFrame:getInstance():addListen(cmd_common.MDM_GUESS_SERVICE, cmd_common.SUB_GUESS_RANK_LIST, self, self.onRankResp)

    self:sendMyGuessRequest()
    self:sendGuessRequest()
    --]]
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function WorldCupGuess:onExit()
    print('WorldCupGuess:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 发送请求
-------------------------------------------------------------------------------
function WorldCupGuess:doSendRequest(cmd, cmd_struct, data_table)
	local cmd_data = ExternalFun.create_netdata( cmd_struct, data_table )
	cmd_data:setcmdinfo(cmd_common.MDM_GUESS_SERVICE, cmd)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 发送所有竞猜列表请求
-------------------------------------------------------------------------------
function WorldCupGuess:sendGuessRequest()
    self:doSendRequest(cmd_common.SUB_GUESS_LIST, cmd_common.CMD_GR_ID, {dwID = 0})
end


-------------------------------------------------------------------------------
-- 发送我的竞猜列表请求
-------------------------------------------------------------------------------
function WorldCupGuess:sendMyGuessRequest()
    self:doSendRequest(cmd_common.SUB_GUESS_MY_LIST, cmd_common.CMD_GR_ID, {dwID = 0})
end


-------------------------------------------------------------------------------
-- 发送排行榜列表请求
-------------------------------------------------------------------------------
function WorldCupGuess:sendRankRequest()
    self:doSendRequest(cmd_common.SUB_GUESS_RANK_LIST, cmd_common.tagGuessRank, {dwID = 0})
end


-------------------------------------------------------------------------------
-- 发送投注请求
-------------------------------------------------------------------------------
function WorldCupGuess:sendBetRequest(guess_id, side, bet)
    local t = {dwID = guess_id, nValue = side, nValue2 = bet}
    self:doSendRequest(cmd_common.SUB_GUESS_BET, cmd_common.CMD_GR_IDValue, t)
end


-------------------------------------------------------------------------------
-- 切换面板
-------------------------------------------------------------------------------
function WorldCupGuess:showPanel(panel)
    -- 切换面板关联按钮
    local showRelative = function(p, visible)
        if p.toggles then
            for i, obj in ipairs(p.toggles) do
                obj:setVisible(visible)
            end
        end
    end

    local count = #self.m_history
    if count > 0 then
        local cur = self.m_history[count]:hide()
        showRelative(cur, true)
    end
    if panel then
        --table.insert(self.m_history, panel:show())         -- 这是纯堆栈模式
        self.m_history[count == 0 and 1 or 2] = panel:show() -- 这是只两层模式
    else
        table.remove(self.m_history, count)
        panel = self.m_history[count - 1]:show()
    end
    showRelative(panel, false)
end


-------------------------------------------------------------------------------
-- 返回按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnBack()
    if #self.m_history == 1 then
        self:removeFromParent()
    else
        self:showPanel()
    end
end


-------------------------------------------------------------------------------
-- 排行榜按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnRank(sender)
    self:showPanel(self.panel_rank)

    -- 发送命令查询列表(间隔一段时间查询一次即可)
    if not self._last_rank_at or self._last_rank_at + 60 < os.time() then
        self._last_rank_at = os.time()
        self:sendRankRequest()
    end
end


-------------------------------------------------------------------------------
-- 规则点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnRule(sender)
    local alert = helper.pop.alert( {LANG.WCG_RULE_TITLE, LANG.WCG_RULE} )
    alert:resize(700, 550, 600)
end


-------------------------------------------------------------------------------
-- 竞猜币兑换按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnExchange(sender)
    helper.link.toMall( LANG.MALL_TAB_GUESS )
end


-------------------------------------------------------------------------------
-- 我的竞猜按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnMy(sender)
    self:showPanel(self.panel_my)
end


-------------------------------------------------------------------------------
-- 场次列表返回
-------------------------------------------------------------------------------
function WorldCupGuess:onGuessListResp(data)
    local guesses = LiveFrame:getInstance():resp(data, cmd_common.tagGuess, true)
    if not guesses then return false end
    
    self:showGuesses(guesses, true)
end


-------------------------------------------------------------------------------
-- 我的竞猜列表返回
-------------------------------------------------------------------------------
function WorldCupGuess:onMyGuessResp(data)
    local guesses = LiveFrame:getInstance():resp(data, cmd_common.tagPlayerGuess, true)
    if not guesses then return false end
    
    self.m_my_guesses = guesses
    self:showMyGuesses(guesses)
end


-------------------------------------------------------------------------------
-- 投注返回
-------------------------------------------------------------------------------
function WorldCupGuess:onBetResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.tagGuessBetResult)
    if not ret then return false end

    if ret.nErrorCode > 0 then
        helper.pop.message( ret.szMsg )
    else
        helper.pop.message( LANG.WCG_BET_SUCC )
        self._last_rank_at = 0  -- 可以在打开排行旁后直接刷新

        local gid = ret.nGuessID

        -- 更新guess
        for i, item in ipairs(self.panel_main:child('listview'):getItems()) do
            local g = item.guess
            if g.nGuessID == gid then
                -- 先构建我的竞猜数据，updateGuess需要用到
                local my_guess = self.m_my_guess_map[gid]
                if not my_guess then
                    my_guess = clone(g)
                    my_guess.nWin1 = 0
                    my_guess.nWin2 = 0
                    my_guess.nWin3 = 0
                    table.insert(self.m_my_guesses, 1, my_guess)
                    self.m_my_guess_map[gid] = my_guess
                end
                my_guess.nBet1 = ret.nBet1
                my_guess.nBet2 = ret.nBet2
                my_guess.nBet3 = ret.nBet3
                self:showMyGuesses(self.m_my_guesses, true)

                -- 更新主列表
                g.nPlayerCount1 = ret.nPlayerCount1
                g.nPlayerCount2 = ret.nPlayerCount2
                g.nPlayerCount3 = ret.nPlayerCount3
                g.nRewardPool = ret.nRewardPool
                self:updateGuess(item)

                -- 更新投注界面
                if self.panel_bet:isVisible() then
                    self:updateBet(g)
                end

                break
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 排行榜返回
-------------------------------------------------------------------------------
function WorldCupGuess:onRankResp(data)
    local ranks = LiveFrame:getInstance():resp(data, cmd_common.tagGuessRank, true)
    if not ranks then return false end

    local listviews = {self.panel_rank:child('panel_today/listview, panel_yesterday/listview, panel_total/listview')}
    listviews[1]:removeAllItems()
    listviews[2]:removeAllItems()
    listviews[3]:removeAllItems()
    local rank_arr = {0, 0, 0}
    local template = self.panel_rank:child('template_row')
    for i, data in ipairs(ranks) do
        local listview = listviews[data.cbType]
        if listview then
            local rank = rank_arr[data.cbType] + 1
            rank_arr[data.cbType] = rank
            local row = template:clone():show()
            if rank % 2 == 0 then
                row:setBackGroundColorOpacity(0)
            end
            row:child('label_name'):setString(data.szName)
            row:child('label_num'):setString(data.nScore)
            if rank <= 3 then
                row:child('img_rank'):show():texture('common/icon_rank_' .. rank .. '.png')
                row:child('label_rank'):hide()
            else
                row:child('img_rank'):hide()
                row:child('label_rank'):show():setString(rank)
            end
            listview:pushBackCustomItem(row)
        end
    end
end


-------------------------------------------------------------------------------
-- 设置比赛基本信息
-------------------------------------------------------------------------------
function WorldCupGuess:setMatchBase(obj, guess)
    obj:child('label_match'):setString( guess.szName )
    obj:child('label_name_a'):setString( guess.szTeamName1 )
    obj:child('label_name_b'):setString( guess.szTeamName2 )
    obj:child('img_team_a'):texture('#icon_worldcup_team_' .. guess.nTeamID1 .. '.png')
    obj:child('img_team_b'):texture('#icon_worldcup_team_' .. guess.nTeamID2 .. '.png')
end


-------------------------------------------------------------------------------
-- 显示场次列表
-------------------------------------------------------------------------------
function WorldCupGuess:showGuesses(guesses, is_first)
    local listview, template = self.panel_main:child('listview, template_match')
    local items = listview:getItems()
    local new_map = {}
    local ft = function(v) return string.format('%02d', v) end
    local create = function(guess)
        local gid = guess.nGuessID
        new_map[gid] = guess
        local item = self.m_guess_nodes[gid]
        if not item then
            item = template:clone():show()
            self.m_guess_nodes[gid] = item

            self:setMatchBase(item, guess)
            item:child('label_rate_a_name'):setString( LANG{'WCG_WIN_NAME', name=guess.szTeamName1} )
            item:child('label_rate_b_name'):setString( LANG{'WCG_WIN_NAME', name=guess.szTeamName2} )
            item:child('btn_guess'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnGuess) )

            listview:pushBackCustomItem(item)
        end
        item:child('label_rate_a'):setString( formatRate(guess.nOdds1) )
        item:child('label_rate_b'):setString( formatRate(guess.nOdds2) )
        item:child('label_rate_p'):setString( formatRate(guess.nOdds3) )
        item:child('label_person'):setString( guess.nPlayerCount1 + guess.nPlayerCount2 + guess.nPlayerCount3 )
        item:child('label_pool'):setString( guess.nRewardPool )
        local t = guess.sysMatchTime
        item:child('label_time'):setString( LANG{'WCG_START_TIME', month=ft(t.wMonth), day=ft(t.wDay), hour=ft(t.wHour), minute=ft(t.wMinute)} )
        item:child('img_join'):setVisible( self.m_my_guess_map[gid] and true or false )
        item.guess = guess
    end
    for i, guess in ipairs(guesses) do
        if not is_first or i <= 4 then
            create(guess)
        else
            local g = guess
            self:perform(function() create(g) end, 0.1 + (i - 5) * 0.001)
        end
    end

    -- 删除已不存在项目
    if not is_first then
        for id, item in pairs(self.m_guess_nodes) do
            if not new_map[id] then
                self.m_guess_nodes[id] = nil
                listview:removeItem( listview:getIndex(item) )
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 显示我的竞猜列表
-------------------------------------------------------------------------------
function WorldCupGuess:showMyGuesses(guesses, is_update)
    local listview, template = self.panel_my:child('listview, template_match')
    local items = listview:getItems()
    self.m_my_guess_map = {}
    for i, guess in ipairs(guesses) do
        local gid = guess.nGuessID
        self.m_my_guess_map[gid] = guess
        for i = 1, 3 do repeat
            if guess['nBet' .. i] == 0 then break end

            local key = gid .. '-' .. i
            local item = self.m_my_guess_nodes[key]
            if not item then
                item = template:clone():show()
                self.m_my_guess_nodes[key] = item

                self:setMatchBase(item, guess)
                local str = i == 3 and LANG.WCG_PING or LANG{'WCG_WIN_NAME', name=guess['szTeamName' .. i]}
                item:child('label_bet_name'):setString( str )

                if is_update then
                    listview:insertCustomItem(item, 0)
                else
                    listview:pushBackCustomItem(item)
                end
            end
            item:child('label_rate'):setString( formatRate(guess['nOdds' .. i]) )
            item:child('label_bet'):setString( guess['nBet' .. i] )

            local get_str = guess.nStatus < 2 and math.floor(guess['nBet' .. i] * guess['nOdds' .. i] / 100) or guess['nWin' .. i]
            item:child('label_get'):setString(get_str)
                
            local type_str = guess.nStatus < 2 and LANG.WCG_BET_CAN or LANG.WCG_BET_GOT
            item:child('label_type'):setString(type_str)

            local status_str = guess.nStatus < 2 and LANG.WCG_UNOPEN or (guess['nWin' .. i] > 0 and LANG.WCG_WIN or LANG.WCG_LOSE)
            item:child('label_status'):setString(status_str)

            local status = guess.nStatus < 2 and 0 or (guess['nWin' .. i] > 0 and 1 or 2)
            item:child('img_status'):texture('#bg_worldcup_status_' .. status .. '.png')
        until true end
    end
end


-------------------------------------------------------------------------------
-- 初始化投注
-------------------------------------------------------------------------------
function WorldCupGuess:initBetUI()
    local panel = self.panel_bet
    for i, btn in ipairs{panel:child('btn_win_a, btn_win_b, btn_win_p')} do
        btn.index = i
        btn:addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnBetChoice) )
    end
    for i = 1, 4 do
        local btn = panel:child('btn_bet_' .. i)
        btn.bet_num = bet_configs[i]
        btn:child('label'):setString( LANG{'WCG_BET_BTN_TEXT', num=string.format('%.0f', btn.bet_num/10000)} )
        btn:addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnAddBet, false) )
    end
    panel:child('btn_bet'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnBet) )
end


-------------------------------------------------------------------------------
-- 更新竞猜界面
-------------------------------------------------------------------------------
function WorldCupGuess:updateBet(guess)
    local panel = self.panel_bet
    panel.guess = guess
    
    local my_guess = self.m_my_guess_map[guess.nGuessID]
    for i, btn in ipairs{panel:child('btn_win_a, btn_win_b, btn_win_p')} do
        local bet = my_guess and my_guess['nBet' .. i] or 0
        btn:child('label_bet'):setString( LANG{'WCG_HAS_BET', bet=bet} )
        if panel.bet_index == i then
            self:onBtnBetChoice(btn)
        end
    end

    panel:child('label_coin'):setString( GlobalUserItem.nGuessCoin )
    panel:child('label_pool'):setString( guess.nRewardPool )
end


-------------------------------------------------------------------------------
-- 竞猜按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnGuess(sender)
    local panel = self.panel_bet
    local guess = sender:getParent().guess
    panel.guess = guess
    panel.bet_index = 1
    self:showPanel(panel)
    self:setMatchBase(panel, guess)
    local t = guess.sysMatchTime
    panel:child('label_time'):setString( LANG{'WCG_START_TIME2', year=t.wYear, month=t.wMonth, day=t.wDay, hour=string.format('%02d', t.wHour), minute=string.format('%02d', t.wMinute)} )
    
    local my_guess = self.m_my_guess_map[guess.nGuessID]
    for i, btn in ipairs{panel:child('btn_win_a, btn_win_b, btn_win_p')} do
        btn:child('label_name'):setString( i == 3 and LANG.WCG_PING or LANG{'WCG_WIN_NAME', name=guess['szTeamName' .. i]} )
        btn:child('label_rate'):setString( formatRate(guess['nOdds' .. i]) )
    end

    self:updateBet(guess)
end


-------------------------------------------------------------------------------
-- 竞猜按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnBetChoice(sender)
    local panel = self.panel_bet
    for i, btn in ipairs{panel:child('btn_win_a, btn_win_b, btn_win_p')} do
        btn:texture('#bg_worldcup_choice_n.png')
    end
    sender:texture('#bg_worldcup_choice_s.png')

    local guess = panel.guess
    local my_guess = self.m_my_guess_map[guess.nGuessID]
    local index = sender.index
    panel:child('label_bet'):setString('0')
    panel:child('label_get'):setString('0')
    panel:child('label_person'):setString(guess['nPlayerCount' .. index])

    panel.bet_index = index
    panel.bet_num = 0   -- 清空
end


-------------------------------------------------------------------------------
-- 加注按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnAddBet(sender, event)
    local panel = self.panel_bet
    local new_num = panel.bet_num + sender.bet_num
    if new_num > GlobalUserItem.nGuessCoin then
        helper.pop.message( LANG.WCG_COIN_NOT_ENOUGH )
        return
    end
    panel.bet_num = new_num
    panel:child('label_bet'):setString(panel.bet_num)
    panel:child('label_get'):setString( math.floor(panel.bet_num * panel.guess['nOdds' .. panel.bet_index] / 100) )
end


-------------------------------------------------------------------------------
-- 投注确定按钮点击
-------------------------------------------------------------------------------
function WorldCupGuess:onBtnBet(sender)
    local panel = self.panel_bet
    if panel.bet_num == 0 then
        helper.pop.message( LANG.WCG_BET_FIRST )
        return
    end
    local name = panel:child('btn_win_' .. (panel.bet_index == 1 and 'a' or (panel.bet_index == 2 and 'b' or 'p')) .. '/label_name'):getString()
    helper.pop.alert( LANG{'WCG_BET_CONFIRM', name=name, num=panel.bet_num}, function()
        self:sendBetRequest(panel.guess.nGuessID, panel.bet_index, panel.bet_num)
    end, true )
end


-------------------------------------------------------------------------------
-- 更新场次
-------------------------------------------------------------------------------
function WorldCupGuess:updateGuess(item)
    local guess = item.guess
    local gid = guess.nGuessID
    item:child('label_person'):setString( guess.nPlayerCount1 + guess.nPlayerCount2 + guess.nPlayerCount3 )
    item:child('label_pool'):setString( guess.nRewardPool )
    item:child('img_join'):setVisible( self.m_my_guess_map[gid] and true or false )
end


return WorldCupGuess
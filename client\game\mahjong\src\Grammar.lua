local Grammar = class("Grammar", cc.load("mvc").ViewBase)


function Grammar:ctor()
	self.items = {}

	self:appAbout()
	self:langAbout()
	self:constAbout()
	self:helperAbout()
	self:globalAbout()
	self:protobufAbout()

	local file = io.open('grammar.txt', 'w')
	file:write(table.concat(self.items, '\n'))
	file:write('\n')
	file:close()

    local label = cc.Label:createWithTTF('See grammer.txt', FONT_TTF, 64)
	label:pos(display.cx, display.cy):addTo(self)
end


function Grammar:addItem(item, include_private)
	---[[
	local t = string.split(item, '.')
    if not include_private then
	    for i, v in ipairs(t) do
		    if v:sub(1, 1) == '_' then
			    return
		    end
	    end
    end
	if t[#t] == 'ctor' or t[#t] == 'new' then 
		return
	end
	--]]
    print(item)
	table.insert(self.items, '\t\t<word>' .. item .. '</word>' )
end


function Grammar:addTable(t, name, loop, include_private)
    if type(name) == 'string' then
        name = {name}
    end
    loop = loop or 1
	for k, v in pairs(t) do
        if type(k) == 'string' then
            if type(v) ~= 'table' or loop >= 2 then
                for _, key in ipairs(name) do
		            self:addItem(key .. '.' .. k, include_private)
                end
            else
                for _, key in ipairs(name) do
                    self:addTable(v, key .. '.' .. k, loop + 1, include_private)
                end
            end
        end
	end
end


function Grammar:appAbout()
    self:addTable(bit, 'bit', 1, true)
    self:addTable(cs.app, 'cs.app')
    self:addTable(cs.game, 'cs.game')
    self:addTable(yl, 'yl')
    self:addTable(appdf, 'appdf')

    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    self:addTable(MultiPlatform, 'MultiPlatform')
 
    local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")
    self:addTable(ExternalFun, 'ExternalFun')

    local cmd_private = cs.app.client('header.CMD_Private')
    self:addTable(cmd_private.login, 'cmd_pri_login')
    self:addTable(cmd_private.game, 'cmd_pri_game')
    
    local logincmd = appdf.req(appdf.HEADER_SRC .. "CMD_LogonServer")
    self:addTable(logincmd, {'logincmd', 'login_cmd'})
    
    local game_cmd = appdf.req(appdf.HEADER_SRC .. "CMD_GameServer")
    self:addTable(game_cmd, {'gamecmd', 'game_cmd'})

    local cmd = cs.app.game('room.CMD_Game')
    self:addTable(cmd, {'cmd', 'room_cmd'})
end


function Grammar:helperAbout()
	for k, v in pairs(helper) do
		if type(v) == 'table' then
			for kk, vv in pairs(v) do
				self:addItem( string.format('helper.%s.%s', k, kk) )
			end
		end
	end
end


function Grammar:globalAbout()
    self:addItem('handler')
    self:addItem('showToast')
    self:addTable(GlobalUserItem, 'GlobalUserItem')
end


function Grammar:langAbout()
	for k, v in pairs(LANG) do
		self:addItem( string.format('LANG.%s', k) )
	end
end


function Grammar:constAbout()
end


function Grammar:protobufAbout()
end


return Grammar
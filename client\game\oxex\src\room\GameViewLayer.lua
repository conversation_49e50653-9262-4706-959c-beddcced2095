local GameViewLayer = class("GameViewLayer",function(scene)
		local gameViewLayer =  display.newLayer()
    return gameViewLayer
end)

--require("client/src/frame/yl")
local cmd = appdf.req(appdf.GAME_SRC.."oxex.src.room.CMD_Game")
local PopupHead = appdf.req("client.src.system.PopupHead")
--local GameChatLayer = appdf.req(appdf.CLIENT_SRC.."plaza.views.layer.game.GameChatLayer")
local ExternalFun = require(appdf.EXTERNAL_SRC .. "ExternalFun")

GameViewLayer.BT_PROMPT 			= 2
GameViewLayer.BT_OPENCARD 			= 3
GameViewLayer.BT_START 				= 4
GameViewLayer.BT_CALLBANKER 		= 5
GameViewLayer.BT_CANCEL 			= 6
GameViewLayer.BT_CHIP 				= 7
GameViewLayer.BT_CHIP1 				= 8
GameViewLayer.BT_CHIP2 				= 9
GameViewLayer.BT_CHIP3 				= 10
GameViewLayer.BT_CHIP4 				= 11

GameViewLayer.BT_SWITCH 			= 12
GameViewLayer.BT_EXIT 				= 13
--GameViewLayer.BT_CHAT 				= 14
--GameViewLayer.BT_SOUND 				= 14
--GameViewLayer.BT_TAKEBACK 			= 16

GameViewLayer.BT_INVATE				= 17

GameViewLayer.FRAME 				= 1
GameViewLayer.NICKNAME 				= 2
GameViewLayer.SCORE 				= 3
GameViewLayer.FACE 					= 7

GameViewLayer.TIMENUM   			= 1
GameViewLayer.CHIPNUM 				= 1

--牌间距
GameViewLayer.CARDSPACING 			= 40

GameViewLayer.VIEWID_CENTER 		= 5

GameViewLayer.RES_PATH 				= "game/oxex/res/"
GameViewLayer.CHIP_ZORDER			= 100

local ZORDER_HEAD_INFO = 10
local XIAZHU_NUM = 4

local pointPlayer = {cc.p(170, 115), cc.p(897, 625)}
local pointCard = {cc.p(display.cx, 100), cc.p(display.cx, 617)}
local pointClock = {cc.p(157, 275), cc.p(1037, 640)}
local pointOpenCard = {cc.p(display.size.width - 350, 115), cc.p(display.cx - 200, display.cy + 250)}
local pointTableScore = {cc.p(display.cx, 342), cc.p(display.cx, 505)}
local pointBankerFlag = {cc.p(243, 208), cc.p(965, 715)}
local pointChat = {cc.p(230, 250), cc.p(767, 690)}
local ptWinLoseAnimate = {cc.p(320, 60), cc.p(1065, 500)}
local pointUserInfo = {cc.p(205, 170), cc.p(445, 240)}
local anchorPoint = {cc.p(0, 0), cc.p(1, 1)}

local AnimationRes = 
{
--	{name = "banker", file = GameViewLayer.RES_PATH.."animation_banker/banker_", nCount = 11, fInterval = 0.2, nLoops = 1},
	{name = "faceFlash", file = GameViewLayer.RES_PATH.."animation_faceFlash/faceFlash_", nCount = 2, fInterval = 0.6, nLoops = -1},
	{name = "lose", file = GameViewLayer.RES_PATH.."animation_lose/lose_", nCount = 17, fInterval = 0.1, nLoops = 1},
	{name = "start", file = GameViewLayer.RES_PATH.."animation_start/start_", nCount = 11, fInterval = 0.15, nLoops = 1},
	{name = "victory", file = GameViewLayer.RES_PATH.."animation_victory/victory_", nCount = 17, fInterval = 0.13, nLoops = 1},
	{name = "yellow", file = GameViewLayer.RES_PATH.."animation_yellow/yellow_", nCount = 5, fInterval = 0.2, nLoops = 1},
	{name = "blue", file = GameViewLayer.RES_PATH.."animation_blue/blue_", nCount = 5, fInterval = 0.2, nLoops = 1}
}

function GameViewLayer:onInitData()
	self.bCardOut = {false, false, false, false, false}
	self.lUserMaxScore = {1, 2, 3, 5, 10}
	self.chatDetails = {}
	self.bCanMoveCard = false
	self.bCanNextReplay = true
end

function GameViewLayer:onExit()
	print("GameViewLayer onExit")
	cc.Director:getInstance():getTextureCache():removeTextureForKey(GameViewLayer.RES_PATH.."card.png")
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile(GameViewLayer.RES_PATH.."game_oxnew_res.plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey(GameViewLayer.RES_PATH.."game_oxnew_res.png")
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
	cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
	yl.IS_REPLAY_MODEL = false
end

local this
function GameViewLayer:ctor(scene)
	this = self
	self._scene = scene

	local main_node = helper.app.loadCSB('GameLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

	--房卡需要
	self.m_sparrowUserItem = {}
	self:onInitDataEx()

	self:onInitData()
	self:preloadUI()

	--节点事件
	local function onNodeEvent(event)
		if event == "exit" then
			self:onExit()
		end
	end
	self:registerScriptHandler(onNodeEvent)

	

--[[
	display.newSprite(GameViewLayer.RES_PATH.."background.png")
		:move(display.center)
		:addTo(self)
--]]
	local  btcallback = function(ref, type)
		if type == ccui.TouchEventType.ended then
         	this:onButtonClickedEvent(ref:getTag(),ref)
        end
    end

    --特殊按钮
    local pointBtSwitch = cc.p(display.size.width - 60, display.size.height - 60)
	-- self.btTakeBack = ccui.Button:create("bt_takeBack_0.png", "bt_takeBack_1.png", "", ccui.TextureResType.plistType)
	-- 	:move(pointBtSwitch)
	-- 	:setTag(GameViewLayer.BT_TAKEBACK)
	-- 	:setTouchEnabled(false)
	-- 	:addTo(self)
	-- self.btTakeBack:addTouchEventListener(btcallback)

	local bAble = GlobalUserItem.bSoundAble or GlobalUserItem.bVoiceAble					--声音
	if GlobalUserItem.bVoiceAble then
		AudioEngine.playMusic(GameViewLayer.RES_PATH.."sound/backMusic.mp3", true)
	end
	--[[
	self.btSound = ccui.CheckBox:create("bt_sound_0.png",
										"bt_sound_1.png",
										"bt_soundOff_0.png",
										"bt_soundOff_1.png", 
										"bt_soundOff_1.png", ccui.TextureResType.plistType)
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_SOUND)
		:setTouchEnabled(false)
		:setSelected(not bAble)
		:addTo(self)
	self.btSound:addTouchEventListener(btcallback)

	
	self.btChat = ccui.Button:create(GameViewLayer.RES_PATH.."bt_chat_0.png", GameViewLayer.RES_PATH.."bt_chat_1.png")
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_CHAT)
		:setTouchEnabled(false)
		:addTo(self)
	self.btChat:addTouchEventListener(btcallback)
--]]
	self.btExit = ccui.Button:create("bt_exit_0.png", "bt_exit_1.png", "", ccui.TextureResType.plistType)
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_EXIT)
		:setTouchEnabled(false)
		:addTo(self)
	self.btExit:addTouchEventListener(btcallback)

	self.btSwitch = ccui.Button:create("bt_switch_0.png", "bt_switch_1.png", "", ccui.TextureResType.plistType)
		:move(pointBtSwitch)
		:setTag(GameViewLayer.BT_SWITCH)
		:addTo(self)
	self.btSwitch:addTouchEventListener(btcallback)

	

	--普通按钮
	-- self.btPrompt = ccui.Button:create("bt_prompt_0.png", "bt_prompt_1.png", "", ccui.TextureResType.plistType)
	-- 	:move(yl.WIDTH - 163, 60)
	-- 	:setTag(GameViewLayer.BT_PROMPT)
	-- 	:setVisible(false)
	-- 	:addTo(self)
	-- self.btPrompt:addTouchEventListener(btcallback)

	self.btOpenCard = self.main_node:child('btn_opencard')
	self.btOpenCard:setTag(GameViewLayer.BT_OPENCARD)
	self.btOpenCard:addTouchEventListener(btcallback)

	self.btStart = self.main_node:child('btn_start')
	self.btStart:setTag(GameViewLayer.BT_START)
	self.btStart:setVisible(true)
	self.btStart:addTouchEventListener(btcallback)

	--[[
	local x, y = self.btStart:pos()
	x = x - 200
	self.btInvate = ccui.Button:create('room/btn01.png', 'room/btn01.png', '')
		:move(x, y)
		:setTag(GameViewLayer.BT_INVATE)
		:addTo(self)
	self.btInvate:addTouchEventListener(btcallback)

	local btInvateSize = self.btInvate:size()
	ccui.ImageView:create('word/font_btn_inroom_invite.png')
		:addTo(self.btInvate)
		:move(btInvateSize.width / 2, btInvateSize.height / 2)
--]]

	self.btCallBanker = self.main_node:child('btn_callbank')
	self.btCallBanker:setTag(GameViewLayer.BT_CALLBANKER)
	self.btCallBanker:addTouchEventListener(btcallback)

	self.btCancel = self.main_node:child('btn_cancel')
	self.btCancel:setTag(GameViewLayer.BT_CANCEL)
	self.btCancel:addTouchEventListener(btcallback)

	self.cur_score = {0, 0, 0, 0, 0}
	--四个下注的筹码按钮
	self.btChip = {}
	local chip_bg = self.main_node:child('chip_bg')
	chip_bg:setLocalZOrder(GameViewLayer.CHIP_ZORDER)
	self.chip_bg = chip_bg
	for i = 1, XIAZHU_NUM do
		self.btChip[i] = chip_bg:child('btn_chip_' .. i)
		self.btChip[i]:setTag(GameViewLayer.BT_CHIP + i)
		self.btChip[i]:addTouchEventListener(btcallback)
		self.btChip[i]:child('txt_num'):setTag(GameViewLayer.CHIPNUM)
	end

	self.txt_CellScore = cc.Label:createWithTTF("底注：0","common/round_body.ttf",24)
		:move(1040, display.size.height - 20)
		:setVisible(false)
		:addTo(self)
	self.txt_TableID = cc.Label:createWithTTF(LANG.DESKTOP_NUM,"common/round_body.ttf",24)
		:move(100, display.size.height - 20)
		:addTo(self)

	--牌提示背景
	self.spritePrompt = self.main_node:child('suanfen_bg')
	--牌值
	self.labAtCardPrompt = {}
	for i = 1, 3 do
		self.labAtCardPrompt[i] = self.spritePrompt:child('num_' .. i)
	end
	self.labCardType = self.spritePrompt:child('num_4')

	--时钟
	self.spriteClock = self.main_node:child('daojishibg')
	self.spriteClock:setLocalZOrder(10)

	local labAtTime = self.spriteClock:child('num')
	labAtTime:setTag(GameViewLayer.TIMENUM)

	--用于发牌动作的那张牌
	self.animateCard = self.main_node:child('animateCard')

	--四个玩家
	self.nodePlayer = {}
	for i = 1 ,cmd.GAME_PLAYER do
		--玩家结点
		self.nodePlayer[i] = self.main_node:child('touxiangbg_' .. i)
		--[[
		if GlobalUserItem.cbGender == 0 then   --- 0 : man
		else
		end
		--]]

		--昵称
		self.nicknameConfig = string.getConfig("common/round_body.ttf", 18)
		local name = self.nodePlayer[i]:child('name')
		name:setTag(GameViewLayer.NICKNAME)

		--金币
		local score = self.nodePlayer[i]:child('score')
		score:setTag(GameViewLayer.SCORE)

--[[
		PassRoom:getInstance()._priView.main_node:child('label_difen'):hide()
		PassRoom:getInstance()._priView.main_node:child('label_jushu'):hide()
--]]
		--score:hide()
	end


	--牌节点
	self.nodeCard = {}
	self.nodeCardPos = {}
	--牌的类型
	self.cardType = {}
	--下注的分数
	self.tableScore = {}

	self.runNode = {}
	
	--准备标志
	self.flag_ready = {}
	--摊牌标志
	self.flag_openCard = {}
	for i = 1, cmd.GAME_PLAYER do
		--牌
		self.nodeCard[i] = {}
		self.nodeCardPos[i] = {}
		for j = 1, 5 do
			self.nodeCard[i][j] = self.main_node:child('card_' .. i .. j )
			local x, y = self.nodeCard[i][j]:pos()
			self.nodeCardPos[i][j] = cc.p(x, y)
		end
		--牌型
		self.cardType[i] = self.main_node:child('cardType_' .. i)

		--桌面金币
		local score = self.nodePlayer[i]:child('score')
		self.tableScore[i] = score

		--准备
		self.flag_ready[i] = self.main_node:child('zhuangbei_state_' .. i)
		if i == cmd.MY_VIEWID then
			local x, y = self.flag_ready[i]:pos()
			self.ready_pos = cc.p(x, y)
		end

	end

	if yl.IS_REPLAY_MODEL then
		self.btStart:hide()
	end

	self.nodeLeaveCard = cc.Node:create():addTo(self)

	self.spriteBankerFlag = display.newSprite()
		:setVisible(false)
		:setLocalZOrder(2)
		:addTo(self)

	
	--点击事件
	self:setTouchEnabled(true)
	
	self:registerScriptTouchHandler(function(eventType, x, y)
		
		if eventType == "ended" then
			self:onEventTouchCallback(x, y)
		end
		
		return true
	end)
	
end

-- 初始化玩家
function GameViewLayer:initPlayers()
end

function GameViewLayer:recoverUserScore()
	local tableId = self._scene._gameFrame:GetTableID()
    --初始化已有玩家
    for i = 1, cmd.GAME_PLAYER do
        local userItem = self._scene._gameFrame:getTableUserItem(tableId, i - 1)
        if nil ~= userItem then
            local wViewChairId = self._scene:SwitchViewChairID(i - 1)
			print('recover score is ', userItem.nTableScore)
			self.tableScore[i]:setString(userItem.nTableScore)
        end
    end
end

function GameViewLayer:onResetView()
	self.nodeLeaveCard:removeAllChildren()
	self.spriteBankerFlag:setVisible(false)
	--self.spriteCardBG:setVisible(false)
	--重排列牌
	local cardWidth = self.animateCard:getContentSize().width
	local cardHeight = self.animateCard:getContentSize().height
	for i = 1, cmd.GAME_PLAYER do
		
		for j = 1, 5 do
			self.nodeCard[i][j]:pos(self.nodeCardPos[i][j].x, self.nodeCardPos[i][j].y)
			self.nodeCard[i][j]:setVisible(false)
			self.nodeCard[i][j]:setLocalZOrder(0)
			self.nodeCard[i][j]:texture('room/paibeimian.png')
		end
		--self.tableScore[i]:setVisible(false)
		self.cardType[i]:setVisible(false)
		local fen = self.nodePlayer[i]:child('fen')
		fen:setString('0')
		fen:hide()
		local font_fen = self.nodePlayer[i]:child('font_fen')
		font_fen:hide()
		self.nodePlayer[i]:child('img_zhuang'):setVisible(false)
	end
	self.bCardOut = {false, false, false, false, false}
	self.labCardType:setString("")
	for i = 1, 3 do
		self.labAtCardPrompt[i]:setString("")
	end

	for i = 1, #self.runNode do
		self.runNode[i]:removeFromParent()
	end
	self.runNode = {}
end

function GameViewLayer:showCurJushu( )
	local room_data = PassRoom:getInstance().m_tabPriData
    -- 局数
	local count = self._scene.cbPlayCount or 1
    local limit = room_data.nPlayCount
	print('count, limit, ', count, limit)
	self.main_node:child('title'):setString(LANG{'ROOM_JUSHU_TITLE', count=count, limit=limit})
end

--更新用户显示
function GameViewLayer:OnUpdateUser(viewId, userItem)
	if not viewId or viewId == yl.INVALID_CHAIR then
		print("OnUpdateUser viewId is nil")
		return
	end

	self.m_sparrowUserItem[viewId] = userItem

	
	--头像
	local head = self.nodePlayer[viewId]:child('sp_head')
	if not userItem then
		self.nodePlayer[viewId]:setVisible(false)
		self.flag_ready[viewId]:setVisible(false)
		
		if head then
			head:setVisible(false)
		end
		
	else
		self.nodePlayer[viewId]:setVisible(true)

		local touxiang = self.nodePlayer[viewId]:child('touxiang')
		if GlobalUserItem.cbGender == 0 then   --- 0 : man
			touxiang:texture('room/nan.png')
		else
			touxiang:texture('room/nv.png')
		end

--[[
		local fen = self.nodePlayer[viewId]:child('fen')
		local font_fen = self.nodePlayer[viewId]:child('font_fen')
--]]
		self:setNickname(viewId, userItem.szNickName)
		--self:setScore(viewId, userItem.lScore)
		self.flag_ready[viewId]:setVisible(yl.US_READY == userItem.cbUserStatus)
		if viewId == cmd.MY_VIEWID then
			if yl.US_READY == userItem.cbUserStatus then
				local x, y = self.btStart:pos()
				self.flag_ready[viewId]:pos(x, y)
			else
				self.flag_ready[viewId]:pos(self.ready_pos.x, self.ready_pos.y)
			end
		end
		

		local is_fangzhu = userItem.dwUserID == PassRoom:getInstance().m_tabPriData.dwTableOwnerUserID
		self.nodePlayer[viewId]:child('fangzhu'):setVisible(is_fangzhu)
		self.nodePlayer[viewId]:child('fangzhu'):setLocalZOrder(10)
		
		--[[
		local is_zhuang = viewId == self.bank_user
		self.nodePlayer[viewId]:child('img_zhuang'):setVisible(is_zhuang)
		self.nodePlayer[viewId]:child('img_zhuang'):setLocalZOrder(10)
		print('viewID , self.bank_user', viewId, self.bank_user, is_zhuang)
		--]]

		if not head then
			head = PopupHead:create(self, userItem, 81, ZORDER_HEAD_INFO)
			head:setName(sp_head)
			head:pos(touxiang:pos())
			self.nodePlayer[viewId]:addChild(head)

			--遮盖层，美化头像
			--[[
			display.newSprite(GameViewLayer.RES_PATH.."oxex_frameTop.png")
				--:move(1, 1)
				:addTo(head)
--]]
		else
			head:updateHead(userItem)
		end
		head:setVisible(true)
		--local score = self.nodePlayer[i]:child('score')
		
	end
	
end

--****************************      计时器        *****************************--
function GameViewLayer:OnUpdataClockView(viewId, time)
	if not viewId or viewId == yl.INVALID_CHAIR or not time then
		self.spriteClock:getChildByTag(GameViewLayer.TIMENUM):setString("")
		self.spriteClock:setVisible(false)
	else
		self.spriteClock:getChildByTag(GameViewLayer.TIMENUM):setString(time)
	end
end

function GameViewLayer:setClockPosition(viewId)
	--[[
	if viewId and pointClock[viewId] ~= nil then
		self.spriteClock:move(pointClock[viewId])
	else
		self.spriteClock:move(display.cx, display.cy + 50)
	end
	--]]
    self.spriteClock:setVisible(true)
end

--**************************      点击事件        ****************************--
--用于触发手牌的点击事件
function GameViewLayer:onEventTouchCallback(x, y)
	--按钮滚回
	print(self.bBtnInOutside)
	if self.bBtnInOutside then
		self:onButtonSwitchAnimate(true)
	end

	if self.isNeedMove == nil or self.isNeedMove == false then
		return
	end

	-- --聊天框
	-- if self._chatLayer:isVisible() then
	-- 	self._chatLayer:showGameChat(false)
	-- end

	--牌可点击
	if self.bCanMoveCard == true then
		--local size1 = self.nodeCard[cmd.MY_VIEWID]:getContentSize()
		--local x1, y1 = self.nodeCard[cmd.MY_VIEWID]:getPosition()
		for i = 1, 5 do
			local card = self.nodeCard[cmd.MY_VIEWID][i]
			local x2, y2 = card:getPosition()
			local size2 = card:getContentSize()
			local rect = card:getBoundingBox()
			--rect.x = x1 - size1.width/2 + x2 - size2.width/2
			--rect.y = y1 - size1.height/2 + y2 - size2.height/2
			if cc.rectContainsPoint(rect, cc.p(x, y)) then
				if false == self.bCardOut[i] then
					card:move(x2, y2 + 30)
				elseif true == self.bCardOut[i] then
					card:move(x2, y2 - 30)
				end
				self.bCardOut[i] = not self.bCardOut[i]
				self:updateCardPrompt()
				return
			end
		end
	end

end

--按钮点击事件
function GameViewLayer:onButtonClickedEvent(tag,ref)
	if tag == GameViewLayer.BT_EXIT then
		if not yl.IS_REPLAY_MODEL then
			self._scene:onQueryExitGame()
		else
			self._scene:onExitRoom()
		end
	elseif tag == GameViewLayer.BT_OPENCARD then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self.bCanMoveCard = false
		self.btOpenCard:setVisible(false)
		--self.btPrompt:setVisible(false)
		self.spritePrompt:setVisible(false)
		--self.spriteCardBG:setVisible(false)

		self._scene:onOpenCard()
	elseif tag == GameViewLayer.BT_PROMPT then
		self:promptOx()
	elseif tag == GameViewLayer.BT_START then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self.btStart:setVisible(false)
		--self.btInvate:hide()
		self._scene:onStartGame()
	elseif tag == GameViewLayer.BT_INVATE then
		--self:InviteHY()
	elseif tag == GameViewLayer.BT_CALLBANKER then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self.btCallBanker:setVisible(false)
		self.btCancel:setVisible(false)
		self._scene:onBanker(1)
	elseif tag == GameViewLayer.BT_CANCEL then
		if yl.IS_REPLAY_MODEL then
			return
		end
		self.btCallBanker:setVisible(false)
		self.btCancel:setVisible(false)
		self._scene:onBanker(0)
	elseif tag - GameViewLayer.BT_CHIP == 1 or
			tag - GameViewLayer.BT_CHIP == 2 or
			tag - GameViewLayer.BT_CHIP == 3 or
			tag - GameViewLayer.BT_CHIP == 4 then
				if yl.IS_REPLAY_MODEL then
					return
				end
			self.chip_bg:setVisible(false)
		for i = 1, XIAZHU_NUM do
			self.btChip[i]:setVisible(false)
		end
		local index = tag - GameViewLayer.BT_CHIP
		self._scene:onAddScore(self.lUserMaxScore[index])
	elseif tag == GameViewLayer.BT_CHAT then
		--self._chatLayer:showGameChat(true)
	elseif tag == GameViewLayer.BT_SWITCH then
		print("BT_SWITCH")
		self:onButtonSwitchAnimate()
	elseif tag == GameViewLayer.BT_SOUND then
		local effect = not (GlobalUserItem.bSoundAble or GlobalUserItem.bVoiceAble)
		if effect == true then
			AudioEngine.playMusic(GameViewLayer.RES_PATH.."sound/backMusic.mp3", true)
		end
		GlobalUserItem.setSoundAble(effect)
		GlobalUserItem.setVoiceAble(effect)
		print("BT_SOUND", effect)
	-- elseif tag == GameViewLayer.BT_TAKEBACK then
	-- 	print("BT_TAKEBACK")
	-- 	self:onButtonSwitchAnimate(true)
	else
		showToast(self, LANG.FUNC_NOT_OPEN, 1)
	end
end

function GameViewLayer:hideChip(  )
	self.chip_bg:setVisible(false)
	for i = 1, XIAZHU_NUM do
		self.btChip[i]:setVisible(false)
	end
end

function GameViewLayer:hideOpenCardBtn(  )
	self.btOpenCard:setVisible(false)
	--self.btPrompt:setVisible(false)
	self.spritePrompt:setVisible(false)
end

function GameViewLayer:InviteHY()
	local data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[GlobalUserItem.nCurGameKind]

    local name = config.NAME
    local brand = cs.game.BRAND
    local room_id = data.szServerID
    local jushu = data.nPlayCount
    local cost = data.nFee
    local pay_type = data.cbPayType
    local zhifu = LANG{'CREATE_ZHIFU_'..pay_type, fangka = cost}
    if jushu < 0 then
        jushu = LANG{'ROOM_KUN', kun=-jushu}
    end
    local renshu = PassRoom:getInstance():getChairCount()
	local difen = data.lCellScore
	local wanfa = self:makeRuleStr()

	local url = yl.INVITE_URL .. '?roomId=' .. room_id
    local title = LANG{'ROOM_INVITE_TITLE', brand = brand, name = name, room = room_id}
	local desc = LANG{'ROOM_INVITE_CONTENT', jushu = jushu, renshu = renshu, wanfa = wanfa, zhifu = zhifu}
    helper.pop.shareLink(url, title, desc, 'word/font_title_invite.png')
end

-------------------------------------------------------------------------------
-- 生成玩法字符串
-------------------------------------------------------------------------------
function GameViewLayer:makeRuleStr()
	local room_data = PassRoom:getInstance().m_tabPriData
    local config = cs.game[ GlobalUserItem.nCurGameKind ]
    local t = {}
	self.m_rule_arr = self.m_rule_arr or {}
	self.m_rule_arr = room_data.cbGameRule[1]
    for i, v in ipairs(self.m_rule_arr) do
        if i > 2 then   -- 1是标志位
            local rule_key = 'RULE' .. (i - 3) .. (v == 1 and '' or '_NONE')
            local str = config[rule_key]
            if str then 
                t[#t + 1] = str
            end
        end
    end
    return table.concat(t, '/')
end

function GameViewLayer:onButtonSwitchAnimate(bTakeBack)
	local fInterval = 0.15
	local spacing = 100
	local originX, originY = self.btSwitch:getPosition()
	for i = GameViewLayer.BT_EXIT, GameViewLayer.BT_EXIT do
		local nCount = i - GameViewLayer.BT_EXIT + 1
		local button = self:getChildByTag(i)
		button:setTouchEnabled(false)
		--算时间和距离
		local time = fInterval*nCount
		local pointTarget = cc.p(0, spacing*nCount)

		local fRotate = 720
		if not bTakeBack then 			--按钮滚出(否则滚回)
			fRotate = -fRotate
			pointTarget = cc.p(-pointTarget.x, -pointTarget.y)
		end

		button:runAction(cc.Sequence:create(
			cc.Spawn:create(cc.MoveBy:create(time, pointTarget), cc.RotateBy:create(time, fRotate)),
			cc.CallFunc:create(function()
				if not bTakeBack then
					button:setTouchEnabled(true)
					self.bBtnInOutside = true
				else
					self.bBtnInOutside = false
				end
			end)))
	end
	if not bTakeBack then
		self.btSwitch:setTouchEnabled(false)
	else
		self.btSwitch:setTouchEnabled(true)
	end
end

function GameViewLayer:hideCallBankerBtn(  )
	self.btCallBanker:setVisible(false)
    self.btCancel:setVisible(false)
end

function GameViewLayer:showUserOperateStatus()

	for i = 1, cmd.GAME_PLAYER do
		if self._scene.cbPlayStatus[i] == 1 then
			local ViewID = self._scene:SwitchViewChairID(i - 1)
			local userItem = self._scene._gameFrame:getTableUserItem(self.table_id, i - 1)

			if self.flag_ready[ViewID] and userItem then
				if ViewID ~= cmd.MY_VIEWID then
					self.flag_ready[ViewID]:setVisible(yl.US_READY == userItem.cbUserStatus)
				else
					if yl.US_READY == userItem.cbUserStatus then
						self.btStart:hide()
					end
				end
			end
		end
	end
end

function GameViewLayer:gameCallBanker(callBankerViewId, bFirstTimes)
	if callBankerViewId == cmd.MY_VIEWID then
        self.btCallBanker:setVisible(true)
		self.btCancel:setVisible(true)
		
		if bFirstTimes then
			display.newSprite()
				:move(display.center)
				:addTo(self)
				:runAction(self:getAnimate("start", true))
			AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_START.WAV")
		end
    end

    
end

function GameViewLayer:gameStart(bankerViewId)
    if bankerViewId ~= cmd.MY_VIEWID then
        for i = 1, XIAZHU_NUM do
			self.chip_bg:setVisible(true)
            self.btChip[i]:setVisible(true)
        end
    end
end

function GameViewLayer:gameAddScore(viewId, score)
	--self.tableScore[viewId]:setTitleText(score)
	--self.tableScore[viewId]:setVisible(true)
	--self.tableScore[viewId]:child('txt_num'):setString(score)
	local font_fen = self.nodePlayer[viewId]:child('font_fen')
	font_fen:show()

	local fen = self.nodePlayer[viewId]:child('fen')
	fen:setString(score)
	fen:show()
	--[[
    local labelScore = self.tableScore[viewId]
    local lScore = tonumber(labelScore:getString())
	lScore = lScore or 0
	labelScore:setString(lScore - score)
	--]]
    --self:setScore(viewId, lScore - score)
end

function GameViewLayer:gameSendCard(firstViewId, totalCount)
	--开始发牌
	--self.spriteCardBG:setVisible(true)
	self:runSendCardAnimate(firstViewId, totalCount)
end

--开牌
function GameViewLayer:gameOpenCard(wViewChairId, cbOx)
	local cardWidth = self.animateCard:getContentSize().width
	local cardHeight = self.animateCard:getContentSize().height
	local fSpacing = GameViewLayer.CARDSPACING
	local fWidth
	if cbOx > 0 then
		fWidth = cardWidth + fSpacing*2
	else
		fWidth = cardWidth + fSpacing*4
	end
	--牌的排列
	--self.nodeCard[wViewChairId]:setContentSize(cc.size(fWidth, cardHeight))
	local origin_pos = self.nodeCardPos[wViewChairId][3]
	for i = 1, 5 do
        local card = self.nodeCard[wViewChairId][i]
		if wViewChairId == cmd.MY_VIEWID then
			card:move(origin_pos.x - cardWidth / 2 + fSpacing*(i - 1), origin_pos.y)
		end
		
		if wViewChairId == 4 or wViewChairId == 5 then
			card:setLocalZOrder(6 - i)
		else
			card:setLocalZOrder(5)
		end

		if cbOx > 0 and i >= 4 then
			local positionX, positionY
			positionX = origin_pos.x + fSpacing * (i - 5)
			positionY = origin_pos.y + 50
			card:move(positionX, positionY)
			card:setLocalZOrder(1)
			if wViewChairId == 4 or wViewChairId == 5 then
				--positionY = positionY + 20
				positionX = origin_pos.x - cardWidth + fSpacing*(i - 0.72)
				card:move(positionX, positionY)
				card:setLocalZOrder(1)
			elseif wViewChairId == 1 or wViewChairId == 2 then
				--positionY = positionY + 20
				positionX = origin_pos.x + fSpacing * (i - 5.4)
				card:move(positionX, positionY)
				card:setLocalZOrder(1)
			end
		end
	end
	--牌型
	if cbOx >= 10 then
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_OXOX.wav")
		--cbOx = 10
	end
	local strFile = string.format("word/ox_%d.png", cbOx)
	--local spriteFrame = cc.SpriteFrameCache:getInstance():getSpriteFrame(strFile)
	self.cardType[wViewChairId]:texture(strFile)
	self.cardType[wViewChairId]:setLocalZOrder(6)
	self.cardType[wViewChairId]:setVisible(true)
	--隐藏摊牌图标
    self:setOpenCardVisible(wViewChairId, false)
end

function GameViewLayer:gameEnd(bMeWin)
	local name
	if bMeWin then
		name = "victory"
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_WIN.WAV")
	else
		name = "lose"
		AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/GAME_LOST.WAV")
	end

    self.btStart:setVisible(true)

	display.newSprite()
		:move(display.center)
		:addTo(self)
		:runAction(self:getAnimate(name, true))
end

function GameViewLayer:gameScenePlaying()
    self.btOpenCard:setVisible(true)
    --self.btPrompt:setVisible(true)
    self.spritePrompt:setVisible(true)
end

function GameViewLayer:setCellScore(cellscore)
	if not cellscore then
		self.txt_CellScore:setString("底注：")
	else
		self.txt_CellScore:setString("底注："..cellscore)
	end
end

function GameViewLayer:setTableID(id)
	self.table_id = id
	if not id or id == yl.INVALID_TABLE then
		self.txt_TableID:setString(LANG.DESKTOP_NUM)
	else
		self.txt_TableID:setString(LANG{'DESKTOP_NUM_1', num = (id + 1) })
	end
end

function GameViewLayer:setCardTextureRect(viewId, tag, cardValue, cardColor)
	if viewId < 1 or viewId > 5 then
		print("card texture rect error!")
		return
	end
	
	local card = self.nodeCard[viewId][tag]
	local png = string.format('card/card%d%02d.png', cardColor, cardValue)
	
	card:texture(png)
	--local rectCard = card:getTextureRect()
	--rectCard.x = rectCard.width*(cardValue - 1)
	--rectCard.y = rectCard.height*cardColor
	--card:setTextureRect(rectCard)
end

function GameViewLayer:setNickname(viewId, strName)
	local name = string.EllipsisByConfig(strName, 133, self.nicknameConfig)
	local labelNickname = self.nodePlayer[viewId]:getChildByTag(GameViewLayer.NICKNAME)
	labelNickname:setString(name)

	-- local labelWidth = labelNickname:getContentSize().width
	-- if labelWidth > 113 then
	-- 	labelNickname:setScaleX(113/labelWidth)
	-- elseif labelNickname:getScaleX() ~= 1 then
	-- 	labelNickname:setScaleX(1)
	-- end
end

function GameViewLayer:setScore(viewId, lScore)
	local labelScore = self.nodePlayer[viewId]:getChildByTag(GameViewLayer.SCORE)
	labelScore:setString(lScore)

	local labelWidth = labelScore:getContentSize().width
	if labelWidth > 98 then
		labelScore:setScaleX(98/labelWidth)
	elseif labelScore:getScaleX() ~= 1 then
		labelScore:setScaleX(1)
	end
end

function GameViewLayer:setUserScore(wViewChairId, lScore)
	self.nodePlayer[wViewChairId]:getChildByTag(GameViewLayer.SCORE):setString(lScore)
end

function GameViewLayer:setReadyVisible(wViewChairId, isVisible)
	self.flag_ready[wViewChairId]:setVisible(isVisible)
	if true == isVisible and wViewChairId == cmd.MY_VIEWID then
		self.flag_ready[wViewChairId]:pos(self.ready_pos.x, self.ready_pos.y)
	end
end

function GameViewLayer:setOpenCardVisible(wViewChairId, isVisible)
	--self.flag_openCard[wViewChairId]:setVisible(isVisible)
end

function GameViewLayer:setTurnMaxScore(lTurnMaxScore)
	for i = 1, XIAZHU_NUM do
		--self.lUserMaxScore[i] = math.max(lTurnMaxScore, 1)
		self.btChip[i]:getChildByTag(GameViewLayer.CHIPNUM):setString(self.lUserMaxScore[i])
		lTurnMaxScore = math.floor(lTurnMaxScore/2)
	end
end

function GameViewLayer:setBankerUser(wViewChairId)
	local fangzhu = self.nodePlayer[wViewChairId]:child('fangzhu')
	--[[
	local mv_pos = fangzhu:convertToWorldSpace(cc.p(fangzhu:pos()))
	self.spriteBankerFlag:move(mv_pos.x, mv_pos.y)
	self.spriteBankerFlag:setVisible(true)
	self.spriteBankerFlag:runAction(self:getAnimate("banker"))
--]]

	self.nodePlayer[wViewChairId]:child('img_zhuang'):setVisible(true)
	self.nodePlayer[wViewChairId]:child('img_zhuang'):setLocalZOrder(10)
	print('viewID , self.bank_user', wViewChairId, self.bank_user, is_zhuang)

	----闪烁动画
	-- display.newSprite()
	-- 	:move(pointPlayer[wViewChairId].x + 2, pointPlayer[wViewChairId].y - 12)
	-- 	:addTo(self)
	-- 	:runAction(self:getAnimate("faceFlash", true))
end

function GameViewLayer:setUserTableScore(wViewChairId, lScore)
	if lScore == 0 then
		return
	end

	local cur_fen = tonumber(self.tableScore[wViewChairId]:getString())
	self.tableScore[wViewChairId]:setString(cur_fen + lScore)
	--self.tableScore[wViewChairId]:setVisible(true)
end

function GameViewLayer:showOpenCardButton( )
	-- body
	self.bCanMoveCard = true
	
	self.btOpenCard:setVisible(true)
	--self.btPrompt:setVisible(true)
	self.spritePrompt:setVisible(true)
end

--发牌动作
function GameViewLayer:runSendCardAnimate(wViewChairId, nCount)
	local nPlayerNum = self._scene:getPlayNum()
	--local viewID = self._scene:SwitchViewChairID(wViewChairId)
	--print('runSendCardAnimate is , ', wViewChairId, 5 -  nCount % 5, nCount)
	if nCount == nPlayerNum*5 then
		self.animateCard:setVisible(true)
		self.cur_card_index = 5
		self.cbTurnCount = 0
	elseif nCount < 1 then
		self.animateCard:setVisible(false)
		self._scene:sendCardFinish()
    	AudioEngine.playEffect(GameViewLayer.RES_PATH.."sound/SEND_CARD.wav")
		return
	end

	--local pointMove = {cc.p(0, -180), cc.p(0, 250)}
	--print('move pos is : ', self.nodeCardPos[wViewChairId][3].x, self.nodeCardPos[wViewChairId][3].y)
	self.animateCard:runAction(cc.Sequence:create(
			cc.MoveTo:create(0.15, self.nodeCardPos[wViewChairId][3]),
			cc.CallFunc:create(function(ref)
				ref:move(display.center)

				--local cur_card_index = nCount % 5 + 1
				local card = self.nodeCard[wViewChairId][self.cur_card_index]
				if not card then return end
				card:setVisible(true)
				
				wViewChairId = wViewChairId + 1
				if wViewChairId > cmd.GAME_PLAYER then
					wViewChairId = 1
				end
				while not self._scene:isPlayerPlaying(wViewChairId) do
					wViewChairId = wViewChairId + 1
					if wViewChairId > cmd.GAME_PLAYER then
						wViewChairId = 1
					end
				end
				self.cbTurnCount = self.cbTurnCount + 1
				if self.cbTurnCount >= nPlayerNum then
					self.cur_card_index = self.cur_card_index - 1
					self.cbTurnCount = 0
				end
				self:runSendCardAnimate(wViewChairId, nCount - 1)
			end)))
end

--检查牌类型
function GameViewLayer:updateCardPrompt()
	--弹出牌显示，统计和
	local nSumTotal = 0
	local nSumOut = 0
	local nCount = 1
	for i = 1, 5 do
		local nCardValue = self._scene:getMeCardLogicValue(i)
		nSumTotal = nSumTotal + nCardValue
		if self.bCardOut[i] then
	 		if nCount <= 3 then
	 			self.labAtCardPrompt[nCount]:setString(nCardValue)
	 		end
	 		nCount = nCount + 1
			nSumOut = nSumOut + nCardValue
		end
	end
	for i = nCount, 3 do
		self.labAtCardPrompt[i]:setString("")
	end
	--判断是否构成牛
	local nDifference = nSumTotal - nSumOut
	if nCount == 1 then
		self.labCardType:setString("")
	elseif nCount == 3 then 		--弹出两张牌
		if self:mod(nDifference, 10) == 0 then
			self.labCardType:setString(LANG{'NIU_NUM', num = (nSumOut > 10 and nSumOut - 10 or nSumOut)})
		else
			self.labCardType:setString(LANG.NIU_NO)
		end
	elseif nCount == 4 then 		--弹出三张牌
		if self:mod(nSumOut, 10) == 0 then
			self.labCardType:setString(LANG{'NIU_NUM', num = (nDifference > 10 and nDifference - 10 or nDifference)})
		else
			self.labCardType:setString(LANG.NIU_NO)
		end
	else
		self.labCardType:setString(LANG.NIU_NO)
	end
end

function GameViewLayer:preloadUI()
	display.loadSpriteFrames(GameViewLayer.RES_PATH.."game_oxex_res.plist",
							GameViewLayer.RES_PATH.."game_oxex_res.png")

	for i = 1, #AnimationRes do
		local animation = cc.Animation:create()
		animation:setDelayPerUnit(AnimationRes[i].fInterval)
		animation:setLoops(AnimationRes[i].nLoops)

		for j = 1, AnimationRes[i].nCount do
			local strFile = AnimationRes[i].file..string.format("%d.png", j)
			animation:addSpriteFrameWithFile(strFile)
		end

		cc.AnimationCache:getInstance():addAnimation(animation, AnimationRes[i].name)
	end
end

function GameViewLayer:getAnimate(name, bEndRemove)
	local animation = cc.AnimationCache:getInstance():getAnimation(name)
	local animate = cc.Animate:create(animation)

	if bEndRemove then
		animate = cc.Sequence:create(animate, cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end))
	end

	return animate
end

function GameViewLayer:promptOx()
	--首先将牌复位
	for i = 1, 5 do
		if self.bCardOut[i] == true then
			local card = self.nodeCard[cmd.MY_VIEWID][i]
			local x, y = card:getPosition()
			y = y - 30
			card:move(x, y)
			self.bCardOut[i] = false
		end
	end
	--将牛牌弹出
	local index = self._scene:GetMeChairID() + 1
	local cbDataTemp = self:copyTab(self._scene.cbCardData[index])
	if self._scene:getOxCard(cbDataTemp) then
		for i = 1, 5 do
			for j = 1, 3 do
				if self._scene.cbCardData[index][i] == cbDataTemp[j] then
					local card = self.nodeCard[cmd.MY_VIEWID][i]
					local x, y = card:getPosition()
					y = y + 30
					card:move(x, y)
					self.bCardOut[i] = true
				end
			end
		end
	end
	self:updateCardPrompt()
end

--用户聊天
function GameViewLayer:userChat(wViewChairId, chatString)
	if chatString and #chatString > 0 then
		--self._chatLayer:showGameChat(false)
		--取消上次
		if self.chatDetails[wViewChairId] then
			self.chatDetails[wViewChairId]:stopAllActions()
			self.chatDetails[wViewChairId]:removeFromParent()
			self.chatDetails[wViewChairId] = nil
		end

		--创建label
		local limWidth = 24*12
		local labCountLength = cc.Label:createWithSystemFont(chatString,"Arial", 24)  
		if labCountLength:getContentSize().width > limWidth then
			self.chatDetails[wViewChairId] = cc.Label:createWithSystemFont(chatString,"Arial", 24, cc.size(limWidth, 0))
		else
			self.chatDetails[wViewChairId] = cc.Label:createWithSystemFont(chatString,"Arial", 24)
		end
		if wViewChairId == cmd.MY_VIEWID then
			self.chatDetails[wViewChairId]:move(pointChat[wViewChairId].x + 24 , pointChat[wViewChairId].y + 9)
				:setAnchorPoint( cc.p(0, 0.5) )
		else
			self.chatDetails[wViewChairId]:move(pointChat[wViewChairId].x - 24 , pointChat[wViewChairId].y + 9)
				:setAnchorPoint(cc.p(1, 0.5))
		end
		self.chatDetails[wViewChairId]:addTo(self, 2)

	    --改变气泡大小
		self.chatBubble[wViewChairId]:setContentSize(self.chatDetails[wViewChairId]:getContentSize().width+48, self.chatDetails[wViewChairId]:getContentSize().height + 40)
			:setVisible(true)
		--动作
	    self.chatDetails[wViewChairId]:runAction(cc.Sequence:create(
	    	cc.DelayTime:create(3),
	    	cc.CallFunc:create(function(ref)
	    		self.chatDetails[wViewChairId]:removeFromParent()
				self.chatDetails[wViewChairId] = nil
				self.chatBubble[wViewChairId]:setVisible(false)
	    	end)))
    end
end

--用户表情
function GameViewLayer:userExpression(wViewChairId, wItemIndex)
	if wItemIndex and wItemIndex >= 0 then
		--self._chatLayer:showGameChat(false)
		--取消上次
		if self.chatDetails[wViewChairId] then
			self.chatDetails[wViewChairId]:stopAllActions()
			self.chatDetails[wViewChairId]:removeFromParent()
			self.chatDetails[wViewChairId] = nil
		end

	    local strName = string.format("e(%d).png", wItemIndex)
	    self.chatDetails[wViewChairId] = cc.Sprite:createWithSpriteFrameName(strName)
	        :move(pointChat[wViewChairId])
	        :addTo(self, 2)
	    if wViewChairId == cmd.MY_VIEWID then
			self.chatDetails[wViewChairId]:move(pointChat[wViewChairId].x + 45 , pointChat[wViewChairId].y + 5)
		else
			self.chatDetails[wViewChairId]:move(pointChat[wViewChairId].x - 45 , pointChat[wViewChairId].y + 5)
		end

	    --改变气泡大小
		self.chatBubble[wViewChairId]:setContentSize(90,80)
			:setVisible(true)

	    self.chatDetails[wViewChairId]:runAction(cc.Sequence:create(
	    	cc.DelayTime:create(3),
	    	cc.CallFunc:create(function(ref)
	    		self.chatDetails[wViewChairId]:removeFromParent()
				self.chatDetails[wViewChairId] = nil
				self.chatBubble[wViewChairId]:setVisible(false)
	    	end)))
    end
end

--拷贝表
function GameViewLayer:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
 end

--取模
function GameViewLayer:mod(a,b)
    return a - math.floor(a/b)*b
end

--运行输赢动画
function GameViewLayer:runWinLoseAnimate(viewid, score)
	local strAnimate
	local strSymbol
	local strNum
	--[[
	if score > 0 then
		
		strSymbol = GameViewLayer.RES_PATH.."symbol_add.png"
		strNum = GameViewLayer.RES_PATH.."num_add.png"
	else
		score = -score
		
		strSymbol = GameViewLayer.RES_PATH.."symbol_reduce.png"
		strNum = GameViewLayer.RES_PATH.."num_reduce.png"
	end

	--加减
	local node = cc.Node:create()
		:move(self.nodeCardPos[viewid])
		:setAnchorPoint(cc.p(0.5, 0.5))
		:setOpacity(0)
		:setCascadeOpacityEnabled(true)
		:addTo(self, 4)

	local spriteSymbol = display.newSprite(strSymbol)		--符号
		:addTo(node)
	local sizeSymbol = spriteSymbol:getContentSize()
	spriteSymbol:move(sizeSymbol.width/2, sizeSymbol.height/2)
--]]
	local x, y = self.nodePlayer[viewid]:pos()
	local size = self.nodePlayer[viewid]:size()
	y = y - size.height / 2
	local word_pos = self.nodePlayer[viewid]:getParent():convertToWorldSpace(cc.p(x, y))
	local node = cc.Node:create()
		:move(word_pos)
		:setAnchorPoint(cc.p(0.5, 0.5))
		:setOpacity(0)
		:setCascadeOpacityEnabled(true)
		:addTo(self, 4)

	local is_win = false
	local png_path = 'room/n2.png'
	local start_char = '.'
	if score > 0 then
		score = '.' .. tostring(score)
		strAnimate = "yellow"
		is_win = true
	else
		if score == 0 then
			score = tostring(score)
		else
			score = '/' .. tostring(-score)
		end
		
		strAnimate = "blue"
		png_path = 'room/n3.png'
		start_char = '/'
	end

--[[
	for i = 1, self._scene:getPlayNum() do
		if i == self._scene:GetMeChairID() then
			self.cur_score[i] = self.cur_score[i] + score
		else
			self.cur_score[i] = self.cur_score[i] - score
		end
	end
	--]]
	local labAtNum = cc.LabelAtlas:_create(score, png_path, 24, 30, string.byte(start_char))		--数字
		:setAnchorPoint(cc.p(0.5, 0.5))
		:addTo(node)
	local sizeNum = labAtNum:getContentSize()
	
	--local num_pos = node:convertToNodeSpace(word_pos)
	--labAtNum:move(num_pos)
	--labAtNum:move(self.nodeCardPos[viewid])

	node:setContentSize(labAtNum.width, labAtNum.height)

	--底部动画
	local nTime = 1.5
	local spriteAnimate = display.newSprite()
		:move(self.nodeCardPos[viewid])
		:addTo(self, 3)
	spriteAnimate:runAction(cc.Sequence:create(
		cc.Spawn:create(
			cc.MoveBy:create(nTime, cc.p(0, 100)),
			self:getAnimate(strAnimate)
		),
		cc.DelayTime:create(2),
		cc.CallFunc:create(function(ref)
			ref:removeFromParent()
		end)
	))

	node:runAction(cc.Sequence:create(
		cc.Spawn:create(
			cc.MoveBy:create(nTime, cc.p(0, 100)), 
			cc.FadeIn:create(nTime)
		),
		cc.DelayTime:create(2))
	)
	table.insert(self.runNode, node)
end


return GameViewLayer
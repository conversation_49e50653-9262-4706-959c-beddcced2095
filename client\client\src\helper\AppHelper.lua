-------------------------------------------------------------------------------
--  创世版1.0
--  应用辅助方法类
--      访问方式：helper.app.
--  @date 2017-06-05
--  @auth woodoo
-------------------------------------------------------------------------------
local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. 'MultiPlatform')


local AppHelper = {}
helper = helper or {}
helper.app = AppHelper

-- 当前游戏模块名称
local current_game = nil

-- 玩法配音是否存在缓存
local kind_peiyin_exists = {}


-------------------------------------------------------------------------------
-- 注册全局电池监听
-------------------------------------------------------------------------------
local battery_percent = 0
local battery_handler = nil
local function batteryNotify(percent)
    battery_percent = math.min(1, tonumber(percent))
    if battery_handler then battery_handler(battery_percent) end
end
MultiPlatform:getInstance():setBatteryCallback(batteryNotify)


-------------------------------------------------------------------------------
-- 设置电池监听
--  用法
--      在onEnter中 helper.app.setBatteryCallback( handler(self, self.onBatteryNotify) )
--      在onExit中需要 helper.app.setBatteryCallback(nil) 来取消
-------------------------------------------------------------------------------
function AppHelper.setBatteryCallback(callback)
    battery_handler = callback
    if battery_handler then battery_handler(battery_percent) end
end


-------------------------------------------------------------------------------
-- 载入游戏
-------------------------------------------------------------------------------
function AppHelper.loadGame(module_name, app)
    --if current_game == module_name then return end
    
    if not app then
        -- 非切换，直接载入初始化游戏
        cs.app.req(cs.app.GAME_ROOT .. '.' .. module_name .. '.src.init')
    else
        -- 进行切换
        local load_scene = cs.app.client('system.LoadGameScene'):create(app, current_game, module_name)
        display.runScene(load_scene)
    end

    current_game = module_name
end


-------------------------------------------------------------------------------
-- 清理package
-------------------------------------------------------------------------------
function AppHelper.cleanPackages(name)
    for k, v in pairs(package.loaded) do
        if k and type(k) == 'string' and k:find(name) then
            --print('package clean:' .. k)
            package.loaded[k] = nil
        end
    end
end


-------------------------------------------------------------------------------
-- 载入.csb
-------------------------------------------------------------------------------
function AppHelper.loadCSB(file_name, has_timeline)
    local path = file_name
    if not cc.FileUtils:getInstance():isAbsolutePath(path) then
        path = 'csb/' .. file_name
    end
    local main_node = cc.CSLoader:createNode(path)
    if has_timeline then
        local action = cc.CSLoader:createTimeline(path)
        main_node:runAction(action)
        action:gotoFrameAndPlay(0, false)
    end
    return main_node
end


-------------------------------------------------------------------------------
-- 防重复加入搜索路径
-------------------------------------------------------------------------------
function AppHelper.addSearchPath(path, is_first)
    local paths = cc.FileUtils:getInstance():getSearchPaths()
    if not table.indexof(paths, path) and not table.indexof(paths, 'assets/' .. path) then
        cc.FileUtils:getInstance():addSearchPath(path, is_first)
    end
end


-------------------------------------------------------------------------------
-- 加入游戏资源搜索路径
-------------------------------------------------------------------------------
function AppHelper.addGameSearchPath(module_name, package)
    local add = AppHelper.addSearchPath

    local base_path = cs.app.GAME_ROOT .. '/' .. module_name .. '/res/'

    -- 开发环境下使用哪个包资源
    if device.platform == 'windows' and package and package ~= '' then
        add(device.writablePath .. 'package/' .. package .. '/' .. base_path, true)    -- 游戏资源
    end

    if device.platform ~= 'windows' then
        add(device.writablePath .. base_path)   -- 更新目录，加在前面优先
    end
    
    add(base_path)  -- 包中路径
end


-------------------------------------------------------------------------------
-- 移除游戏资源搜索路径
-------------------------------------------------------------------------------
function AppHelper.removeGameSearchPath(module_name)
    cc.FileUtils:getInstance():purgeCachedEntries()  -- 重要：否则会从缓存的路径读取

    local remove = function(path)
        local paths = cc.FileUtils:getInstance():getSearchPaths()

        local index = table.indexof(paths, path) or table.indexof(paths, 'assets/' .. path)
        if index then
            table.remove(paths, index)
            cc.FileUtils:getInstance():setSearchPaths(paths)
        end
     end

    local base_path = cs.app.GAME_ROOT .. '/' .. module_name .. '/res/'
    remove(device.writablePath .. base_path)   -- 更新目录
    remove(base_path)  -- 包中路径

    -- 开发环境下使用哪个包资源
    local package = appdf.PACK_PATH
    if device.platform == 'windows' and package and package ~= '' then
        remove(device.writablePath .. 'package/' .. package .. '/' .. base_path)
    end
end


-------------------------------------------------------------------------------
-- 加入玩法资源搜索路径
-------------------------------------------------------------------------------
function AppHelper.addKindSearchPath(module_name, kind, package)
    AppHelper.removeKindSearchPath()

    local base_path = cs.app.GAME_ROOT .. '/' .. module_name .. '/res/r' .. kind

    AppHelper.addSearchPath(base_path, true)  -- 包中路径
    AppHelper.addSearchPath(device.writablePath .. base_path, true)   -- 更新目录，加在前面优先


    -- 开发环境下使用哪个包资源
    if device.platform == 'windows' and package and package ~= '' then
        local path = device.writablePath .. 'package/' .. package .. '/' .. base_path
        AppHelper.addSearchPath(path, true)
    end
end


-------------------------------------------------------------------------------
-- 移除所有玩法资源搜索路径
-------------------------------------------------------------------------------
function AppHelper.removeKindSearchPath()
    local paths = cc.FileUtils:getInstance():getSearchPaths()
    local new_paths = {}    -- 屏蔽了更新目录的其它
    for i, path in ipairs(paths) do
        if not path:match('/r%d+/?$') then -- 如果不是以/r301结尾，保留
            table.insert(new_paths, path)
        end
    end
    cc.FileUtils:getInstance():setSearchPaths(new_paths)
end


-------------------------------------------------------------------------------
-- 通用按钮点击效果
-------------------------------------------------------------------------------
function AppHelper.commClickEffect(sender, event)
    if event == cc.EventCode.BEGAN then
        if not sender._old_scale then
            sender._old_scale = sender:getScale()
        end
        if sender._click_act then
            sender:stopAction(sender._click_act)
        end

        sender._click_act = cc.Sequence:create(cc.ScaleTo:create(0.05, sender._old_scale * 0.9), cc.CallFunc:create(function(a) a._click_act = ni end))
        sender:runMyAction(sender._click_act)
        --[[
        local pos = sender:getTouchBeganPosition()
        pos = sender:convertToNodeSpace(pos)
        local particle = helper.particle.create('particle/ui/comm_click', true)
        particle:pos(pos):addTo(sender)
        --]]

    elseif event == cc.EventCode.CANCELLED or event == cc.EventCode.ENDED then
        if sender._click_act then
            sender:stopAction(sender._click_act)
        end

        local scale = sender._old_scale or 1
        local ra = cc.Repeat:create(cc.Sequence:create(cc.ScaleTo:create(0.05, scale + 0.1), cc.ScaleTo:create(0.05, scale)), 3)
        local ta = cc.Sequence:create(cc.ScaleTo:create(0.05, scale), ra, cc.ScaleTo:create(0.05, scale), cc.CallFunc:create(function(a) a._click_act = ni end))
        sender._click_act = ta
        sender:runMyAction(sender._click_act)
    end
end


-------------------------------------------------------------------------------
-- 通用按钮点击效果2
-------------------------------------------------------------------------------
function AppHelper.tintClickEffect(sender, event)
    if event == cc.EventCode.BEGAN then
        sender:tintTo(0.1, 150, 150, 150)

    elseif event == cc.EventCode.CANCELLED or event == cc.EventCode.ENDED then
        sender:tintTo(0.1, 255, 255, 255)
    end
end


-------------------------------------------------------------------------------
-- 重复点击保护
--	注意：该方法会在保护期间点击穿透控件，仅在穿透无所谓时使用
-------------------------------------------------------------------------------
function AppHelper.clickProtect(widget, duration)
    widget:setTouchEnabled(false)
    widget:retain()
    scheduler.performWithDelayGlobal(function() 
        widget:setTouchEnabled(true)
        widget:release()
    end, duration)
end


-------------------------------------------------------------------------------
-- 通用按钮点方法包装
-------------------------------------------------------------------------------
for i, name in ipairs{'comm', 'tint'} do
    AppHelper[name .. 'ClickHandler'] = function(owner, func, click_protect)
        local protect_time = 300
        if type(click_protect) == 'number' then
            protect_time = click_protect
            click_protect = true
        end

        return function(sender, event)
            if click_protect ~= false then
                -- 重复点击保护，不用clickProtect，主要是那个方法会导致保护期间控件点击穿透到下层
                local now_time = yl.time() * 1000
                local last_time = sender._last_click_protect_time_	-- 绝对的私有变量，取复杂点
                if last_time and now_time - last_time < protect_time then
                    print('click time protected')
                    return false
                end
            end

            AppHelper[name .. 'ClickEffect'](sender, event)
            if event == cc.EventCode.ENDED then
                if click_protect ~= false then
                    sender._last_click_protect_time_ = yl.time() * 1000
                end
                if not sender.click_sound then
                    if device.platform ~= 'windows' then    -- windows音效太卡
                        helper.music.playClickSound()
                    end
                else
                    helper.music.playSound(sender.click_sound)
                end
                if owner then
                    func(owner, sender, event)
                else
                    func(sender, event)
                end
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 通用关闭处理
-------------------------------------------------------------------------------
function AppHelper.commCloseHandler(node)
    return AppHelper.commClickHandler(nil, function() node:removeFromParent() end)
end


-------------------------------------------------------------------------------
-- 加入到runningScene
-------------------------------------------------------------------------------
function AppHelper.addToScene(node, scene)
    node:addTo( scene or cc.Director:getInstance():getRunningScene() )
end


-------------------------------------------------------------------------------
-- 从runningScene获取
-------------------------------------------------------------------------------
function AppHelper.getFromScene(name, scene)
    return (scene or cc.Director:getInstance():getRunningScene()):child(name)
end


-------------------------------------------------------------------------------
-- 从runningScene显示或隐藏
-------------------------------------------------------------------------------
function AppHelper.showFromScene(name, visible, scene)
    local node = AppHelper.getFromScene(name, scene)
    if node then
        node:setVisible(visible)
    end
end


-------------------------------------------------------------------------------
-- 从runningScene移除
-------------------------------------------------------------------------------
function AppHelper.removeFromScene(node, scene)
    if type(node) == 'string' then
        node = (scene or cc.Director:getInstance():getRunningScene()):child(node)
    end
    if node then
        node:removeFromParent()
    end
end


-------------------------------------------------------------------------------
-- 启动引导
-------------------------------------------------------------------------------
function AppHelper.guide(step_name)
    cs.app.client('system.GuideManager').getInScene():startStep(step_name)
end


-------------------------------------------------------------------------------
-- 添加返回键处理
-------------------------------------------------------------------------------
function AppHelper.addKeyPadEvent(parent, is_add)
    local layer = parent:child('_keypad_layer_')
    if not layer and is_add then
        layer = display.newLayer():addTo(parent)
        layer:registerScriptKeypadHandler(function(event)
            if event == 'backClicked' then
                local name = '_exit_game_alert_'
                local alert = cc.Director:getInstance():getRunningScene():child(name)
                if not alert then
                    alert = helper.pop.alert( LANG.SURE_EXIT, function()
                        cc.Director:getInstance():endToLua()
                    end, true)
                    alert:zorder( cs.app.zorder.POP_EXIT ):setName(name)
                end
            end
        end)
        layer:setKeyboardEnabled(true)
    elseif layer and not is_add then
        layer:unregisterScriptKeypadHandler()
        layer:setKeyboardEnabled(false)
    end
end


-------------------------------------------------------------------------------
-- 创建MainFrame
-------------------------------------------------------------------------------
function AppHelper.createFrame(view, callback)
    local MainFrame = cs.app.client('frame.MainFrame')
    if not MainFrame then return end

    return MainFrame:create(view, callback)
end


-------------------------------------------------------------------------------
-- 移除MainFrame
-------------------------------------------------------------------------------
function AppHelper.removeFrame(frame)
    if frame then
        frame:onCloseSocket()
    end
end


-------------------------------------------------------------------------------
-- 创建骨骼动画
-------------------------------------------------------------------------------
function AppHelper.createArmature(name, path, event_callback, do_load)
	if do_load == nil then
		do_load = true
	end

	if do_load and cc.FileUtils:getInstance():isFileExist(path) then
		ccs.ArmatureDataManager:getInstance():addArmatureFileInfo(path)
	end

    local armature = ccs.Armature:create(name)
	if armature then
		armature:getAnimation():setSpeedScale( cs.app.ARMATURE_SPEED )

		if event_callback then
			armature:getAnimation():setMovementEventCallFunc(event_callback)
		end
	end

	return armature
end


-------------------------------------------------------------------------------
-- 创建序列帧动画
--  name: 路径名，不包含文件类型后缀: common/logo_duoduo_light对应common目录下的logo_duoduo_light.plist
--  count：帧数
--  duration：持续时间
--  is_repeat：是否无限循环
--  callback: 动画执行完毕后的回调，可以为nil
--  res_type：资源类型，是本地还是plist: ccui.TextureResType.localType or ccui.TextureResType.plistType（默认）
-------------------------------------------------------------------------------
function AppHelper.createAnimation(name, count, duration, is_repeat, callback, res_type)
    res_type = res_type or ccui.TextureResType.plistType
    local AnimationMgr = cs.app.client('external.AnimationMgr')
    local animation = cc.AnimationCache:getInstance():getAnimation(name)
    if not animation then
        if res_type == ccui.TextureResType.plistType then
            display.loadSpriteFrames(name .. '.plist', name .. '.png')

            -- name是含路径的，而frame的名字是不包含路径的
            local arr = name:split('/')
            local file_name = arr[#arr]
            AnimationMgr.loadAnimationFromFrame(file_name .. '_%d.png', 1, count, name)
        else
            AnimationMgr.loadAnimationFromFrame(name .. '_%d.png', 1, count, name, ccui.TextureResType.localType)
        end
    end

    animation = cc.AnimationCache:getInstance():getAnimation(name)
    if nil == animation then
        return
    end

    local param = AnimationMgr.getAnimationParam()
    param.m_strName = name
    param.m_bRepeat = is_repeat
    param.m_bResetParam = true
    param.m_bRestore = false
    param.m_fDelay = duration / count

    local first_frame = animation:getFrames()[1]:getSpriteFrame()
    local sprite = display.newSprite(first_frame)
    AnimationMgr.playAnimation(sprite, param, callback)

    return sprite
end


-------------------------------------------------------------------------------
-- 移除动画资源
-------------------------------------------------------------------------------
function AppHelper.removeAnimationRes(name)
    cc.AnimationCache:getInstance():removeAnimation(name)
	cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile(name .. ".plist")
	cc.Director:getInstance():getTextureCache():removeTextureForKey(name .. ".png")
end


-------------------------------------------------------------------------------
-- 创建序粒子
-------------------------------------------------------------------------------
function AppHelper.createParticle(path, auto_remove, pos_type)
	auto_remove = auto_remove ~= false
	path = path .. '.plist'

	local particle = cc.ParticleSystemQuad:create(path)
	particle:setPositionType(pos_type or cc.POSITION_TYPE_RELATIVE)
	particle:setAutoRemoveOnFinish(auto_remove)

	return particle
end


-------------------------------------------------------------------------------
-- 获取配音路径
--  peiyin是索引，从0开始，0普通话男，1普通话女，2方言男，3方言女
-------------------------------------------------------------------------------
function AppHelper.getPeiyinPath(peiyin)
    if peiyin == nil then peiyin = 0 end
    if peiyin > 3 then peiyin = 0 end
    local t = {'putong/b_', 'putong/g_'}
    local path = t[peiyin + 1]
    if peiyin == 2 or peiyin == 3 then
        local suffix = peiyin == 2 and '/b_' or '/g_'
        local kind = GlobalUserItem.nCurGameKind
        local kind_path = kind .. suffix

        -- 以下处理是为了当玩法特定配音不存在时使用普通话
        if kind_peiyin_exists[kind] == nil then  -- 未建缓存
            if cs.game and cs.game.MUST_PEIYIN then
                kind_peiyin_exists[kind] = cc.FileUtils:getInstance():isFileExist('sound/' .. kind .. '/' .. cs.game.MUST_PEIYIN) and true or false
            else
                kind_peiyin_exists[kind] = false
            end
        end
        if kind_peiyin_exists[kind] then
            path = kind_path
        else
            path = t[peiyin - 2 + 1]    -- 对应的普通话
        end
    end
    return path
end


-------------------------------------------------------------------------------
-- 添加金币显示文本点击事件
-------------------------------------------------------------------------------
function AppHelper.addGoldLabelEvent(label)
    label:setTouchEnabled(true)
    label:addTouchEventListener(function(sender, event)
        helper.app.tintClickEffect(sender, event)
        if event ~= cc.EventCode.ENDED then return end
        helper.pop.message( LANG{'GOLD_NUM_POP', num=GlobalUserItem.lUserScore} )
    end)
end


-------------------------------------------------------------------------------
-- 获取web接口信息后通用错误检查
--  返回true表示成功
-------------------------------------------------------------------------------
function AppHelper.urlErrorCheck(data, response, http_status)
    print(response)
    helper.pop.waiting(false)
    if type(data) == 'table' then
        if data.code then
            if data.code ~= 0 then
                helper.pop.message(data.msg)
            else
                return true
            end
        end
    else
        helper.pop.message( LANG{'NETWORK_ERROR', code = http_status} )
    end
end


-------------------------------------------------------------------------------
-- 下载文件
-------------------------------------------------------------------------------
local FILE_DOWNLOAD_NOTIFY = "file_download_notify"

function AppHelper.getDownloadParam(prefix, url)
    local arr = url:split('/')
    local save_name = prefix .. '_' .. arr[#arr]
    local save_path = device.writablePath .. 'download/'
    local use_path = 'download/' .. save_name
    return save_path, save_name, use_path
end

--全局通知函数
cc.exports.g_FileDownloadListener = function (ncode, msg, filename)
	local event = cc.EventCustom:new(FILE_DOWNLOAD_NOTIFY)
	event.code = ncode
	event.msg = msg
	event.filename = filename
    print('app file download event dispatch...')
	cc.Director:getInstance():getEventDispatcher():dispatchEvent(event)
end

function AppHelper.download(url, save_path, save_name, owner, callback)
    local downloader = CurlAsset:createDownloader('g_FileDownloadListener', url)
    if false == cc.FileUtils:getInstance():isDirectoryExist(save_path) then
        cc.FileUtils:getInstance():createDirectory(save_path)
    end

    local retry_times = 0
    local function callbackWrap(event)
        print('app file download complete...', event.code)
        if event.filename and event.filename == save_name and callback then -- 文件名相同必须：event.filename == save_name
            if 0 == event.code then
       	        callback(event.filename)
            elseif -1 ~= event.code then    -- -1是无法创建文件，不重试
                -- 尝试重新下载，不再需要注册监听，要删除可能已存在的非完整文件
                retry_times = retry_times + 1
                if retry_times < 3 then
                    if cc.FileUtils:getInstance():isFileExist(save_path .. save_name) then
                        cc.FileUtils:getInstance():removeFile(save_path .. save_name)
                    end
                    local downloader = CurlAsset:createDownloader('g_FileDownloadListener', url)
                    downloader:downloadFile(save_path, save_name)
                end
            end
        end
    end

    local listener = cc.EventListenerCustom:create(FILE_DOWNLOAD_NOTIFY, callbackWrap)
    cc.Director:getInstance():getEventDispatcher():addEventListenerWithSceneGraphPriority(listener, owner)
    downloader:downloadFile(save_path, save_name)
end

function AppHelper.addURLImage(owner, cache_table, cache_prefix, url, relative_obj, add_callback)
    local save_path, save_name, use_path = AppHelper.getDownloadParam(cache_prefix, url)

    local function add(obj)
        if tolua.isnull(obj) then return end
        local image = ccui.ImageView:create()
        image:texture(use_path)
        add_callback(obj, image)
    end

    -- 必须判断文件大小，如果多项文件名相同，则开始下载时文件会存在（注意，windows下getFileSize总是0）
    if cc.FileUtils:getInstance():isFileExist(use_path) and cc.FileUtils:getInstance():getFileSize(use_path) > 0 then
        add(relative_obj)
    else
        -- 确保相同的文件只下载一次
        if not cache_table[save_name] then
            cache_table[save_name] = {}
 
            local function callback(filename)
                local t = cache_table[filename]
                for i, obj in ipairs(t) do
                    add(obj)
                end
            end
            AppHelper.download(url, save_path, save_name, owner, callback)
        end
        table.insert(cache_table[save_name], relative_obj)
    end
end


-------------------------------------------------------------------------------
-- 是否有某个类
-------------------------------------------------------------------------------
function AppHelper.hasClass(package)
    local path = string.gsub(package, '%.', '/')
    local has_sub_class = cc.FileUtils:getInstance():isFileExist(path .. '.luac') or cc.FileUtils:getInstance():isFileExist(path .. '.lua')
    return has_sub_class
end


-------------------------------------------------------------------------------
-- 添加进入前后台监听（模块调用）
-------------------------------------------------------------------------------
function AppHelper.addBackgroundCallback(owner, callback)
    if tolua.isnull(owner) or not callback then return end
    if not appdf.app._background_callbacks then appdf.app._background_callbacks = {} end
    local calls = appdf.app._background_callbacks
    table.insert(calls, {owner, callback})
end


-------------------------------------------------------------------------------
-- 移除进入前后台监听（模块调用）, callback可为空
-------------------------------------------------------------------------------
function AppHelper.removeBackgroundCallback(owner, callback)
    if tolua.isnull(owner) then return end
    if not appdf.app._background_callbacks then appdf.app._background_callbacks = {} end
    local calls = appdf.app._background_callbacks
    local removes = {}
    for i, obj in ipairs(calls) do
        if obj[1] == owner and (not callback or obj[2] == callback) then
            table.insert(removes, 1, i)
        end
    end
    for i, index in ipairs(removes) do
        table.remove(calls, index)
    end
end


-------------------------------------------------------------------------------
-- 触发进入前后台监听（系统调用）
-------------------------------------------------------------------------------
function AppHelper.triggerBackgroundCallback(is_foreground)
    if not appdf.app._background_callbacks then appdf.app._background_callbacks = {} end
    local calls = appdf.app._background_callbacks
    local removes = {}
    for i, obj in ipairs(calls) do
        if tolua.isnull(obj[1]) then
            table.insert(removes, 1, i)
        else
            obj[2](obj[1], is_foreground)
        end
    end
    for i, index in ipairs(removes) do
        table.remove(calls, index)
    end
end


-------------------------------------------------------------------------------
-- 创建健康游戏公告
-------------------------------------------------------------------------------
function AppHelper.createHealthGame(parent, pos)
    local label_health = ccui.Text:create( LANG.HEALTH_GAME, 'Arial', 14)
    label_health:setTextColor( cc.c3b(40, 40, 40) )
    label_health:pos(pos):addTo(parent)
    return label_health
end


-------------------------------------------------------------------------------
-- 创建邀请链接
-------------------------------------------------------------------------------
function AppHelper.makeInviteUrl(room_id)
    local channel = cs.app.client('external.MultiPlatform'):getInstance():getChannel()
    local url = string.format('%s?roomId=%s&channelId=%s', (yl.INVITE_URL_INNER or yl.INVITE_URL), room_id, channel)
    return url
end


-------------------------------------------------------------------------------
-- 切换房卡图标
-------------------------------------------------------------------------------
function AppHelper.checkFangkDiamond(sp)
    if not cs.app.IS_GOLD_HALL and cs.app.FANGKA_DIAMOND then
        sp:texture('common/diamondfangka.png')
    end
end


-------------------------------------------------------------------------------
-- 显示月卡头像框
-------------------------------------------------------------------------------
function AppHelper.addAvatorCard(panel, scale, pos, value)
    value = value or GlobalUserItem.nMonthTicketType
    if value > 0 then
        local sp = display.newSprite('common/bg_avator_card_' .. value .. '.png')
        sp:scale(scale)
        if pos then
            sp:pos(pos):addTo(panel)
        else
            helper.layout.addCenter(panel, sp)
        end
        return true
    end
end


-------------------------------------------------------------------------------
-- 检查房间状态（维护、版本等状态）
--  kind: 创建房间时需要kind
-------------------------------------------------------------------------------
function AppHelper.checkRoom(ok_callback, kind, room_id)
    helper.pop.waiting()

    local callback = function(data, response, http_status)
        if not AppHelper.urlErrorCheck(data, response, http_status) then return end
        if data.is_restart == 1 then
            helper.pop.alert(data.msg, function()
                MultiPlatform:getInstance():restartGame()
            end)
        else
            yl.INVITE_URL_INNER = data.room_link and data.room_link ~= '' and data.room_link or nil
            ok_callback()
        end
    end

    local c_version = appdf.BASE_C_VERSION
    local res_version = tonumber(yl.app:getVersionManager():getResVersion()) or 0
    local game_version = tonumber(yl.app:getVersionManager():getResVersion(game)) or 0
    local version_no = string.format('%d.%d.%d', c_version, res_version, game_version)  -- 大厅版本.大厅资源版本.游戏资源版本
    local uid =  GlobalUserItem.dwUserID
    yl.GetUrl(yl.URL_ROOM_CHECK, 'post', {uid=uid, kind=kind, room_id=room_id, version_no=version_no}, callback)
end


-------------------------------------------------------------------------------
-- 获取游戏配置(后台配置信息)
-------------------------------------------------------------------------------
function AppHelper.getGameConfigByName(name)
    for i, info in ipairs( appdf.app._gameList ) do
        if info._Module == name then
            return info
        end
    end
end


-------------------------------------------------------------------------------
-- 获取游戏配置(后台配置信息)
-------------------------------------------------------------------------------
function AppHelper.getGameConfigByKind(kind)
    for i, info in ipairs( appdf.app._gameList ) do
        for _, cfg in ipairs(info.kindlist) do
            if cfg.kind_id == kind then
                return info
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 获取外链配置(后台配置信息)
-------------------------------------------------------------------------------
function AppHelper.getLinkConfigByKind(kind)
    local link_list = appdf.app._serverConfig.linklist
    if not link_list then return end
    for i, info in ipairs( link_list ) do
        if info.link_id == kind then
            return info
        end
    end
end


-------------------------------------------------------------------------------
-- 检查游戏版本更新
-- nil: 配置不存在
-- false： 版本低，需更新
-- true：可以进入
-------------------------------------------------------------------------------
function AppHelper.checkGameUpdate(game_name, kind, ok_callback)
    local config = AppHelper.getGameConfigByName(game_name)
    if not config then
        helper.pop.message( LANG.NO_GAME_CONFIG )
        return
    end

    if device.platform == 'windows' then
        ok_callback()
        return true
    end

    -- 大厅版本比较
    local web_c_version = appdf.app:getServerConfig().client_version
    local my_c_version = appdf.BASE_C_VERSION
    if my_c_version > web_c_version then
        ok_callback()
        return true
    end

    local res_version = config._ServerResVersion
    local version = tonumber(appdf.app:getVersionManager():getResVersion(game_name))
    if not version or res_version > version then
        helper.pop.alert(LANG.GAME_NEED_UPDATE, function()
            local app = appdf.app
            local update = {}
            update.isClient = false
            update.newfileurl = app._updateUrl..'/game/'..game_name..'/res/filemd5List.json'
            update.downurl = app._updateUrl .. '/game/'
            update.dst = device.writablePath .. 'game/'
            if device.platform == 'windows' then
                update.dst = device.writablePath .. 'download/game/'
            end
            update.src = 'game/'..game_name..'/res/filemd5List.json'
            if device.platform == 'windows' then
                update.src = device.writablePath..'ciphercode/game/'..game_name..'/res/filemd5List.json'
            end
            update._ServerResVersion = res_version
            update._Module = game_name
            update._Kind = kind
            
            local path = cs.app.CLIENT_SRC .. 'main.GameUpdateLayer'
            helper.pop.popLayer(path, nil, {update, ok_callback}, 120)
        end, true)
        return false
    else
        ok_callback()
        return true
    end
end


-------------------------------------------------------------------------------
-- 打印搜索路径
-------------------------------------------------------------------------------
function AppHelper.printSearchPath()
    print('-------------------- search paths --------------------')
    local paths = cc.FileUtils:getInstance():getSearchPaths()
    for i, path in ipairs(paths) do
        print(path)
    end
    print('------------------------------------------------------')
end

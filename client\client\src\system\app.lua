-------------------------------------------------------------------------------
--  创世版1.0
--  应用配置
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
local app = {
    -- 字体
    FONT_NAME = 'Arial',

    CLIENT_RES = 'client/res/',

    CLIENT_SRC = 'client.src.',

    -- 拆红包活动分享链接
    ORB_SHARE_URL = 'https://lhmj.tuo3.com.cn/adminzj/wap/dismantle',

    -- 最多人数
    MAX_PLAYER = 4,

    -- 所有游戏根路径
    GAME_ROOT = 'game',

    -- 骨骼动画速度
	ARMATURE_SPEED = 0.55,

    -- 创建房间是否显示tab
    CREATE_ROOM_TAB = false,

    -- zorder
    zorder = {
        POP_MESSAGE = 801,      -- 悬浮消息
        POP_WAIT    = 900,      -- 蒙版
        POP_EXIT    = 901,      -- 退出提示
    },

    -- 分享类型
    SHARE = {
        HY  = 0,
        PYQ = 1
    },

    -- 金猪活动道具
    ITEM_GOLD_PIG = 48,
    ITEM_GOLD_PIG_PRO = 50,

    plugins = {
        {
            group       = '麻将',
            game        = 'mahjong',
            kind        = 308,
            name        = '霍邱麻将',
        },
        {
            group       = '麻将',
            game        = 'mahjong',
            kind        = 304,
            name        = '天台三阿磨',
        },
        {
            group       = '扑克',
            game        = 'fourcards',
            kind        = 104,
            name        = '天台四副牌',
        },
        {
            group       = '扑克',
            game        = 'fourcards',
            kind        = 105,
            name        = '台州四副牌',
        },
    },
}


--------------------------------------------------------------------------------
-- 颜色
--------------------------------------------------------------------------------
display.COLOR_STROKE		= cc.c4b(34, 24, 15, 255)		-- #22180F	-- 所有文字描边
display.COLOR_PURE_WHITE	= cc.c3b(255, 255, 255)			-- #FFFFFF	-- 纯白
display.COLOR_WHITE			= cc.c3b(231, 231, 231)			-- #E7E7E7	-- 灰白
display.COLOR_BLACK			= cc.c3b(0, 0, 0)				-- #000000	-- 纯黑
display.COLOR_RED			= cc.c3b(255, 0, 0)			    -- #FF0000	-- 红色
display.COLOR_GREEN			= cc.c3b(8,137,0)			    -- #088900	-- 绿色
display.COLOR_ORANGE		= cc.c3b(211, 137, 65)			-- #D38941	-- 橙色
display.COLOR_GOLDEN		= cc.c3b(255, 248, 142)			-- #FFF88E	-- 金色
display.COLOR_BLUE			= cc.c3b(92, 172, 223)			-- #5CACDF	-- 蓝色
display.COLOR_BROWN         = cc.c3b(111, 54, 37)			-- #6F3625	-- 棕色，大多数内容
display.COLOR_GRAY_BROWN	= cc.c3b(184, 165, 141)			-- #6F3625	-- 灰棕
display.COLOR_PURPLE		= cc.c3b(192, 79, 195)			-- #C04FC3	-- 紫色
display.COLOR_GRAY_ORANGE	= cc.c3b(214, 159, 97)			-- #D69F61	-- 橙灰
display.COLOR_GRAY			= cc.c3b(80, 80, 80)			-- #505050	-- 灰色
display.COLOR_YELLOW		= cc.c3b(246, 255, 80)			-- #505050	-- 黄色

-- 富文本颜色定义
app.RICH_COLOR = {
    w = display.COLOR_WHITE,
    h = display.COLOR_BLACK,
    y = cc.c3b(255, 255, 0),
    r = display.COLOR_RED,
    g = display.COLOR_GREEN,
    b = display.COLOR_BLUE,
    o = display.COLOR_ORANGE,
    p = display.COLOR_PURPLE,
    j = display.COLOR_GOLDEN,
    z = display.COLOR_BROWN,
    gb = display.COLOR_GRAY_BROWN,
    go = display.COLOR_GRAY_ORANGE,
    gr = display.COLOR_GRAY,
}


local app_ext = appdf.loadPack('luac', 'app_ext') or appdf.loadPack('lua', 'app_ext')
if app_ext then
    for k, v in pairs(app_ext) do
        app[k] = v
    end

    -- 额外的语言配置
    if LANG and app_ext.LANG then
        for k, v in pairs(app_ext.LANG) do
            LANG[k] = v
        end
    end
end

-- require封装
app.req = function(path)
    if path and type(path) == "string" then
        return require(path)
    else
        print("require paht unknow")
    end
end

-- require客户端脚本
app.client = function(path)
    return require(app.CLIENT_SRC .. path)
end

-- require当前游戏脚本
app.game = function(path)
    return require(cs.game.SRC .. path) -- todo: 
end

-- 是否语言左序
ISLEFT = true


return app
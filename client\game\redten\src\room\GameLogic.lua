local GameLogic = {}


--**************    扑克类型    ******************--
--混合牌型
GameLogic.OX_VALUEO				= 100

GameLogic.USER_OPERATE_PASS     = 200
GameLogic.USER_OPERATE_HINT     = 201
GameLogic.USER_OPERATE_OUTCARD  = 202
GameLogic.USER_OPERATE_REPUSH 	= 203


--最大手牌数目
GameLogic.MAX_CARDCOUNT			= 5
--牌库数目
GameLogic.FULL_COUNT			= 52
--正常手牌数目
GameLogic.NORMAL_COUNT			= 5


--- 排序方案
GameLogic.ST_ORDER              = 0           -- 大小
GameLogic.ST_COUNT              = 1           -- 大小
GameLogic.ST_VALUE              = 2           -- 大小
GameLogic.ST_LINK               = 3           -- 大小

--扑克类型
GameLogic.CT_ERROR					=	0	--错误类型
GameLogic.CT_SINGLE					=	1	--单牌类型
GameLogic.CT_DOUBLE					=	2	--对子类型
--GameLogic.CT_THREE					=	3	--三条类型
--GameLogic.CT_DOUBLE_LINK			=	4	--对连类型
--GameLogic.CT_THREE_LINK				=	5	--三连类型
GameLogic.CT_SHUNZI					=	4	--顺子类型
GameLogic.CT_BOMB					=	11	--炸弹类型
GameLogic.CT_BOMB_TW				=	12	--天王炸弹
--GameLogic.CT_BOMB_LINK				=	9	--排炸类型
GameLogic.CT_REDTEN                 =   13



--取模
function GameLogic:mod(a,b)
    return a - math.floor(a/b)*b
end

--获得牌的数值
function GameLogic:getCardValue(cbCardData)
    return self:mod(cbCardData, 16)
end

--获得牌的数值
function GameLogic:getCardLinkValue(cbCardData)
	local val = self:mod(cbCardData, 16)
	if val == 0 then
		return val
	end
	if val >= 0x0E then
		val = val + 2
	elseif val <= 2 then
        val = val + 13
	end	
	return val
end

--获得牌的数值
function GameLogic:getCardLogicValue(cbCardData)
	local val = self:mod(cbCardData, 16)
	if val == 0 then
		return val
	end
	if val >= 0x0E then
		val = val + 2
	elseif val <= 2 then
        val = val + 13
    elseif val == 10 then
        local color = self:getCardColor(cbCardData)
        if color == 0 or color == 2 then
            val = val + 13
        end
	end	
	return val
end

--获得牌的颜色（0 -- 4）
function GameLogic:getCardColor(cbCardData)
    return math.floor(cbCardData/16)
end

--拷贝表
function GameLogic:copyTab(st)
    local tab = {}
    for k, v in pairs(st) do
        if type(v) ~= "table" then
            tab[k] = v
        else
            tab[k] = self:copyTab(v)
        end
    end
    return tab
end

function GameLogic:SortCardList( cbCardData, cbCardCount, cbSortType )
	if cbCardCount <= 0 then
		return 
	end

	cbSortType = cbSortType or GameLogic.ST_ORDER

	if cbSortType == GameLogic.ST_ORDER then
		table.sort(cbCardData, 
			function(a, b) 
				local a_v = self:getCardLogicValue(a)
				local b_v = self:getCardLogicValue(b)
				if a_v > b_v then
					return true
				elseif a_v < b_v then
					return false
				else
					return a > b
				end
			end)
	elseif cbSortType == GameLogic.ST_VALUE then
		table.sort(cbCardData, function(a, b) 
			local a_v = self:getCardValue(a)
			local b_v = self:getCardValue(b)
			if a_v > b_v then
				return true
			elseif a_v < b_v then
				return false
			else
				return a > b
			end
        end)
    elseif cbSortType == GameLogic.ST_LINK then
		table.sort(cbCardData, function(a, b) 
			local a_v = self:getCardLinkValue(a)
			local b_v = self:getCardLinkValue(b)
			if a_v > b_v then
				return true
			elseif a_v < b_v then
				return false
			else
				return a > b
			end
		end)
	end
	
end

--排序
function GameLogic:sort(cbCardData, sort_card_num)
	sort_card_num = sort_card_num or #cbCardData
	self:SortCardList(cbCardData, sort_card_num)	
end

--是否连牌
function GameLogic:IsStructureLink(cbCardData, cbCardCount, cbCellCount)
	if self:mod(cbCardCount, cbCellCount) ~= 0 then
		return false
    end
    
    GameLogic:SortCardList(cbCardData, cbCardCount, GameLogic.ST_LINK)

	local cbBlockCount = cbCardCount / cbCellCount
    local cbFirstLinkValue = self:getCardLinkValue(cbCardData[1])
    local cbFirstValue = self:getCardValue(cbCardData[1])

 
    print('cbFirstValue is ... ', cbFirstValue)
    if cbFirstLinkValue >= 15 and cbFirstValue ~= 2 then return false end
    if cbFirstLinkValue == 15 then
        local cp_cards = {}
        cp_cards = self:copyTab(cbCardData)
        GameLogic:SortCardList(cp_cards, cbCardCount, GameLogic.ST_VALUE)
        cbFirstLinkValue = self:getCardValue(cp_cards[1])

        for i = 2, cbBlockCount do
            -- print('cbBlockCount is ', cbBlockCount, self:getCardLogicValue(cbCardData[i * cbCellCount]))
             if cbFirstLinkValue ~= (self:getCardValue(cp_cards[i * cbCellCount]) + i - 1) then
                 return false
             end
         end
         return true
    end

    cbFirstLinkValue = self:getCardLinkValue(cbCardData[1])
    if cbFirstLinkValue > 14 then return false end

    for i = 2, cbBlockCount do
       -- print('cbBlockCount is ', cbBlockCount, self:getCardLogicValue(cbCardData[i * cbCellCount]))
		if cbFirstLinkValue ~= (self:getCardLinkValue(cbCardData[i * cbCellCount]) + i - 1) then
			return false
		end
	end

	return true
end

function GameLogic:AnalysebCardData(cbCardData, cbCardCount)
	local AnalyseResult = {}
	AnalyseResult.cbBlockCount = {0, 0, 0, 0, 0, 0, 0, 0}
	AnalyseResult.cbCardData = {}
	for i = 1, 4 do
		AnalyseResult.cbCardData[i] = {}
	end
	 
	local i = 1
	while i <= cbCardCount do
		local cbSameCount = 1
		local cbLogicValue = self:getCardLinkValue(cbCardData[i])

		--搜索同牌
		for j = i + 1, cbCardCount do
			if self:getCardLinkValue(cbCardData[j] ) ~= cbLogicValue then
				break
			end
			cbSameCount = cbSameCount + 1
        end
        
        if cbSameCount > 4 then
            print("这儿有错误")
            return
        end

		--设置结果
		local cbIndex = AnalyseResult.cbBlockCount[cbSameCount]
		AnalyseResult.cbBlockCount[cbSameCount] = AnalyseResult.cbBlockCount[cbSameCount] + 1
		--print('cbSameCount is ', cbSameCount)
		for j = 1, cbSameCount do
            --table.insert(AnalyseResult.cbCardData[cbSameCount], cbCardData[i + j - 1])
            AnalyseResult.cbCardData[cbSameCount][cbIndex * cbSameCount+j] = cbCardData[i + j - 1]
		end

		i = i + cbSameCount
	end
	--dump(AnalyseResult)

	return AnalyseResult
end


function GameLogic:GetCardType(cbCardData, cbCardCount)
	local cbStarLevel = 0
	if cbCardCount == 0 then
		return GameLogic.CT_ERROR, cbStarLevel
	elseif cbCardCount == 1 then
		return GameLogic.CT_SINGLE, cbStarLevel
    end
    
    self:SortCardList(cbCardData,cbCardCount,GameLogic.ST_ORDER)

	print('card typ num :', cbCardCount)
	--排炸类型
	if cbCardCount >= 12 then
		local cbCardIndex = 1
		local cbBlockCount = 0
		
		while cbCardIndex <= cbCardCount  do
			local cbSameCount = 1
			local cbCardValue = self:getCardLogicValue(cbCardData[cbCardIndex])
			for i = cbCardIndex + 1, cbCardCount do
				if self:getCardLogicValue(cbCardData[i])  == cbCardValue then
					cbSameCount = cbSameCount + 1
				else
					break
				end
			end
			
			--连牌判断
			if cbSameCount >= 4 then
				cbBlockCount = cbBlockCount + 1
				cbCardIndex = cbCardIndex + cbSameCount
			else
				break
			end
		end
		

		--结果判断
		if cbBlockCount >= 3 and cbCardIndex == cbCardCount and 
			self:mod(cbCardCount,cbBlockCount) == 0 and 
			self:IsStructureLink(cbCardData, cbCardCount, cbCardCount / cbBlockCount)
			then
				cbStarLevel = cbBlockCount + cbCardCount / cbBlockCount
				return GameLogic.CT_BOMB_LINK
		end
	end

	print('普通分析 开始')
	local AnalyseResult = self:AnalysebCardData(cbCardData, cbCardCount)
    --dump("普通分析 结束")
    --dump(cbCardData)
    --dump(AnalyseResult)

    --[[
    if cbCardCount == 3 or cbCardCount == 4 then
        if AnalyseResult.cbBlockCount[cbCardCount] == 1 then
            local redten_count = 0
            for i = 1, cbCardCount do
                local v = self:getCardLogicValue(cbCardData[i])
                if v == 23 then
                    redten_count = redten_count + 1
                end
            end
            if redten_count == 2 then
                cbStarLevel = 10
                return GameLogic.CT_REDTEN, cbStarLevel
            end
        end
    end
    --]]

	--同牌判断
	if cbCardCount == 3 and AnalyseResult.cbBlockCount[3] == 1 then return GameLogic.CT_BOMB, cbStarLevel end
    if cbCardCount == 2 and AnalyseResult.cbBlockCount[2] == 1 then 
        local v1 = self:getCardLogicValue(cbCardData[1])
        local v2 = self:getCardLogicValue(cbCardData[2])
        if v1 == v2 and v1 == 23 then
            cbStarLevel = 10
            return  GameLogic.CT_REDTEN, cbStarLevel
        else
            return GameLogic.CT_DOUBLE, cbStarLevel 
        end
    elseif cbCardCount == 2 and AnalyseResult.cbBlockCount[1] == 2 then
        local v1 = self:getCardLogicValue(cbCardData[1])
        local v2 = self:getCardLogicValue(cbCardData[2])
        if v1 == 23 and v2 == 10 then
            return GameLogic.CT_DOUBLE, cbStarLevel 
        end
    end

	--天王炸弹
	if cbCardCount == 2 and cbCardData[1] == 0x4F and cbCardData[2] == 0x4E then
		cbStarLevel = 7
		return GameLogic.CT_BOMB_TW, cbStarLevel
    end

	--同相炸弹
	if cbCardCount == 4 and AnalyseResult.cbBlockCount[cbCardCount] == 1 then
		cbStarLevel = cbCardCount
		return GameLogic.CT_BOMB, cbStarLevel
	end


	-- 顺子类型
    if cbCardCount >= 3 and AnalyseResult.cbBlockCount[1] == cbCardCount then
		if self:IsStructureLink(AnalyseResult.cbCardData[1], cbCardCount, 1) == true then
			return GameLogic.CT_SHUNZI, cbStarLevel
		end
	end

	return  GameLogic.CT_ERROR, cbStarLevel
end 

function GameLogic:concat(a, b)
	local result = {}
	for _, v in ipairs(a) do
		table.insert( result, v)
	end
	for _, v in ipairs(b) do
		table.insert( result, v)
	end

	return result
end

-- 获取相同的牌
function GameLogic:anayseCard(cbCardData, card_count, is_sort_card_value, is_out_card)
	is_sort_card_value = is_sort_card_value or false
	is_out_card = is_out_card or false
	local max_king_card = {}
    local min_king_card = {}
    local red_ten_card = {}
	local vec_cards = {}
	local cards = {}
	local pre_card = 0
	local king_is_ok = false
	self:sort(cbCardData)
	--dump(cbCardData)
	for i = 1, card_count do repeat
		if cbCardData[i] == 0 then
			break
        end
        local card_value = self:getCardLogicValue(cbCardData[i])
        if card_value == 23 then
            table.insert(red_ten_card, cbCardData[i])
            break
        end
		if cbCardData[i] == 0x4F then
			table.insert(max_king_card, cbCardData[i])
			break
		end
		if cbCardData[i] == 0x4E then
			table.insert(min_king_card, cbCardData[i])
			break
		end
		if not king_is_ok then
			pre_card = cbCardData[i];
			king_is_ok = true;
			table.insert(cards, cbCardData[i]);
			print('first not king ', self:getCardValue(cbCardData[i]))
			break
		end

		if self:getCardValue(pre_card) == self:getCardValue(cbCardData[i]) then
			table.insert(cards, cbCardData[i])
			break
		end
		
		--dump(cards)
		table.insert(vec_cards, cards)
		cards = {}
		pre_card = cbCardData[i]
		table.insert(cards, cbCardData[i])
	until true
	end

	if #cards > 0 then
		table.insert(vec_cards, cards)
	end

	if #min_king_card > 0 then
		table.insert(vec_cards, 1, min_king_card)
	end

	if #max_king_card > 0 then
		table.insert(vec_cards, 1, max_king_card)
    end
    
    if #red_ten_card > 0 then
        table.insert(vec_cards, 1, red_ten_card)
    end

	return vec_cards
end


function GameLogic:compareTwoCard(card_a, card_b)
	local a_v = self:getCardLogicValue(card_a)
	local b_v = self:getCardLogicValue(card_b)
	return a_v < b_v
end

function GameLogic:GuanCard(vec_cards, out_card, can_spilt_card)
	can_spilt_card = can_spilt_card or false
	local result = {}
	local is_has_zha = false
	--dump(out_card)
	for _, v in ipairs(vec_cards) do repeat
		if out_card.card_num == 0 then
			table.insert(result, v.card)
			break
		end
		if not is_has_zha then
			if v.card_num >= 4 then
				is_has_zha = true
			end
		end
		if out_card.card_num >= 4 then
			if out_card.card_num < v.card_num then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if v.is_single_king and not out_card.is_single_king then
					table.insert(result, v.card)
					break
				end
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end
			end
			
		end
		if out_card.card_num < 4 then
			if v.card_num >= 4 then
				table.insert(result, v.card)
				break
			elseif v.card_num == out_card.card_num then
				if self:compareTwoCard(out_card.card_real_v, v.card_v) then
					table.insert(result, v.card)
					break
				end 
			end
		end

	until true
	end

	if can_spilt_card == false then
		return result
	end

	if #result == 0 and not is_has_zha then
		for _, v in ipairs(vec_cards) do
			if self:compareTwoCard(out_card.card_real_v, v.card_v) and out_card.card_num <= #v.card then
				local ret_vec = {}
				for i = 1, out_card.card_num do
					table.insert(ret_vec, v.card[i])
				end
				table.insert(result, ret_vec)
			end
		end
	end
	return result
end

--同牌搜索
function GameLogic:SearchSameCard(cbHandCardData, cbHandCardCount, cbReferCard, cbSameCardCount)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
	local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--local cbCardData = self:copyTab(cbHandCardData)
	local cbCardData = cbHandCardData
	local cbCardCount = cbHandCardCount
	if cbCardCount < cbSameCardCount then
		return tagSearchCardResult
    end

    --排序扑克
    GameLogic:SortCardList(cbCardData, cbHandCardCount, GameLogic.ST_ORDER)
    --分析结构
    local tagAnalyseResult = GameLogic:AnalysebCardData(cbCardData, cbCardCount)
    --dump(tagAnalyseResult, "tagAnalyseResult", 6)
    local cbReferLogicValue =  0 --(cbReferCard == 0 and 0 or GameLogic:getCardLogicValue(cbReferCard))
    if cbReferCard and cbReferCard ~= 0 then
        cbReferLogicValue = GameLogic:getCardLogicValue(cbReferCard)
    end
    print('cbReferLogicValue num is ', cbReferLogicValue)
    local cbBlockIndex = cbSameCardCount
    local is_red_ten = false
    while cbBlockIndex <= 4 do
        for i=1,tagAnalyseResult.cbBlockCount[cbBlockIndex] do
            local cbIndex = (tagAnalyseResult.cbBlockCount[cbBlockIndex] - i) * cbBlockIndex + 1
            local cbNowLogicValue = GameLogic:getCardLogicValue(tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex])
            local bIsAdd = false
            local search_num = 0
            if cbBlockIndex == 2 and cbNowLogicValue == 23 then
                is_red_ten = true
            end
            if cbSameCardCount == cbBlockIndex then
                if cbNowLogicValue > cbReferLogicValue then
                    bIsAdd = true
                    search_num = cbSameCardCount
                end
            else
                if cbBlockIndex >= 3 then
                    bIsAdd = true
                    search_num = cbBlockIndex
                else
                    if cbNowLogicValue > cbReferLogicValue then
                        bIsAdd = true
                        search_num = cbSameCardCount
                    end
                end
            end
            if not is_red_ten and bIsAdd then
                cbResultCardCount[cbResultCount] = search_num
                tagSearchCardResult[2] = cbResultCardCount
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount][1] = tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex]
                for i=2, search_num do
                    cbResultCard[cbResultCount][i] = tagAnalyseResult.cbCardData[cbBlockIndex][cbIndex+i-1]
                end --此处修改
                tagSearchCardResult[3] = cbResultCard
                cbResultCount = cbResultCount + 1
            end
            is_red_ten = false
        end
        cbBlockIndex = cbBlockIndex + 1
    end
    tagSearchCardResult[1] = cbResultCount - 1
    return tagSearchCardResult
end

--分析分布
function GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    local cbCardCount1 = 0
    local cbDistributing = {}
    for i=1,15 do
        local distributing = {}
        for j=1,6 do
            distributing[j] = 0
        end
        cbDistributing[i] = distributing
    end
    local Distributing = {cbCardCount1,cbDistributing}
    for i=1,cbCardCount do
        if cbCardData[i] ~= 0 then
            local cbCardColor = GameLogic:getCardColor(cbCardData[i])
            local cbCardValue = GameLogic:getCardValue(cbCardData[i])
            
            --分布信息
            cbCardCount1 = cbCardCount1 + 1
            cbDistributing[cbCardValue][6] = cbDistributing[cbCardValue][6]+1
            local color = cbCardColor + 1--bit:_rshift(cbCardColor,4) + 1
            print('cbCardColor is ', cbCardColor, color)
            cbDistributing[cbCardValue][color] = cbDistributing[cbCardValue][color]+1
        end
    end
    Distributing[1] = cbCardCount1
    Distributing[2] = cbDistributing

    return Distributing
end

--构造扑克
function GameLogic:MakeCardData(cbValueIndex,cbColorIndex)
    --print("构造扑克 " ..bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)..",".. GameLogic:getCardLogicValue(bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)))
    return bit:_or(bit:_lshift(cbColorIndex,4),cbValueIndex)
end

--连牌搜索
function GameLogic:SearchLineCardType(cbHandCardData, cbHandCardCount, cbReferCard, cbBlockCount, cbLineCount)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
	--排序扑克
	local cbCardData = cbHandCardData
    GameLogic:SortCardList(cbCardData, cbHandCardCount, 0)
    local cbCardCount = cbHandCardCount
    --连牌最少数
    local cbLessLineCount = 0
    if cbLineCount == 0 then
        if cbBlockCount == 1 then
            cbLessLineCount = 3
        elseif cbBlockCount == 2 then
            cbLessLineCount = 3
        else
            cbLessLineCount = 3
        end
    else
        cbLessLineCount = cbLineCount
    end
    --print("连牌最少数 " .. cbLessLineCount)
    
    local cbReferIndex = 1
    if cbReferCard ~= 0 then
        local cbReferCardV1 = GameLogic:getCardLinkValue(cbReferCard)
        if (cbReferCardV1-cbLessLineCount) >= 0 then
            cbReferIndex = cbReferCardV1-cbLessLineCount+1+1
        end
    end 
    --超过A
    if cbReferIndex + cbLessLineCount > 15 then
        return tagSearchCardResult
    end
    --长度判断
    if cbHandCardCount < cbLessLineCount * cbBlockCount then
        return tagSearchCardResult
    end
   -- print("搜索顺子开始点 " .. cbReferIndex)
    local Distributing = GameLogic:AnalysebDistributing(cbCardData, cbCardCount)
    --搜索顺子
    local cbTmpLinkCount = 0
    local cbValueIndex = cbReferIndex
    local flag = false
    while cbValueIndex <= 13 do
        if cbResultCard[cbResultCount] == nil then
            cbResultCard[cbResultCount] = {}
        end
        if Distributing[2][cbValueIndex][6] < cbBlockCount then
            if cbTmpLinkCount < cbLessLineCount  then
                cbTmpLinkCount = 0
                flag = false
            else
                cbValueIndex = cbValueIndex - 1
                flag = true
            end
        else
            cbTmpLinkCount = cbTmpLinkCount + 1
            if cbLineCount == 0 then
                flag = false
            else
                flag = true
            end
        end
        if flag == true then
            flag = false
            if cbTmpLinkCount >= cbLessLineCount then
                --复制扑克
                local cbCount = 0
                local cbIndex=(cbValueIndex-cbTmpLinkCount+1)
                while cbIndex <= cbValueIndex do
                    local cbTmpCount = 0
                    local cbColorIndex=1
                    while cbColorIndex <= 4 do --在四色中取一个
                        local cbColorCount = 1
                        while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                            cbCount = cbCount + 1
                            local val = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                            print('1 val is ', val, cbIndex, 5-cbColorIndex-1)
                            cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                            tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]
                            cbTmpCount = cbTmpCount + 1
                            if cbTmpCount == cbBlockCount then
                                break
                            end
                            cbColorCount = cbColorCount + 1
                        end
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorIndex = cbColorIndex + 1
                    end
                    cbIndex = cbIndex + 1
                end
                tagSearchCardResult[2][cbResultCount] = cbCount
                cbResultCount = cbResultCount + 1
                if cbLineCount ~= 0 then
                    cbTmpLinkCount = cbTmpLinkCount - 1
                else
                    cbTmpLinkCount = 0
                end
            end
        end
        cbValueIndex = cbValueIndex + 1
    end

    --特殊顺子(寻找A)
    if cbTmpLinkCount >= cbLessLineCount-1 and cbValueIndex == 14 then
        --print("特殊顺子(寻找A)")
        if (Distributing[2][1][6] >= cbBlockCount) or (cbTmpLinkCount >= cbLessLineCount) then
            if cbResultCard[cbResultCount] == nil then
                cbResultCard[cbResultCount] = {}
            end
            --复制扑克
            local cbCount = 0
            local cbIndex=(cbValueIndex-cbTmpLinkCount)
            while cbIndex <= 13 do
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][cbIndex][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        local val = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                        print('2 val is ', val, cbIndex, 5-cbColorIndex-1)
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(cbIndex,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
                cbIndex = cbIndex + 1
            end
            --复制A
            if Distributing[2][1][6] >= cbBlockCount then
                local cbTmpCount = 0
                local cbColorIndex=1
                while cbColorIndex <= 4 do --在四色中取一个
                    local cbColorCount = 1
                    while cbColorCount <= Distributing[2][1][5-cbColorIndex] do
                        cbCount = cbCount + 1
                        local val = GameLogic:MakeCardData(1,5-cbColorIndex-1)
                        print('3 val is ', val, 1, 5-cbColorIndex-1)
                        cbResultCard[cbResultCount][cbCount] = GameLogic:MakeCardData(1,5-cbColorIndex-1)
                        tagSearchCardResult[3][cbResultCount] = cbResultCard[cbResultCount]

                        cbTmpCount = cbTmpCount + 1
                        if cbTmpCount == cbBlockCount then
                            break
                        end
                        cbColorCount = cbColorCount + 1
                    end
                    if cbTmpCount == cbBlockCount then
                        break
                    end
                    cbColorIndex = cbColorIndex + 1
                end
            end
            tagSearchCardResult[2][cbResultCount] = cbCount
            cbResultCount = cbResultCount + 1
        end
    end
    tagSearchCardResult[1] = cbResultCount - 1
    --dump(tagSearchCardResult)
    return tagSearchCardResult
end

--出牌搜索
function GameLogic:SearchOutCard(cbHandCardData,cbHandCardCount,cbTurnCardData,cbTurnCardCount) 
    print("出牌搜索")
    --dump(cbTurnCardData)
    --结果数目
    local cbResultCount = 1
    --扑克数目
    local cbResultCardCount = {}
    --结果扑克
    local cbResultCard = {}
    --搜索结果
    local tagSearchCardResult = {cbResultCount-1,cbResultCardCount,cbResultCard}
    
	--排序扑克
	local cbCardData = cbHandCardData --= self:copyTab(cbHandCardData)
	local cbCardCount = cbHandCardCount
    GameLogic:SortCardList(cbCardData, cbCardCount, GameLogic.ST_ORDER)
    GameLogic:SortCardList(cbTurnCardData, cbTurnCardCount, GameLogic.ST_ORDER)
	
    --出牌分析
	local cbTurnOutType, cbStarLevel = GameLogic:GetCardType(cbTurnCardData, cbTurnCardCount)
	print("cbTurnCardData type", cbTurnOutType, cbStarLevel)
    if cbTurnOutType == GameLogic.CT_ERROR then --错误类型
        print("上家为空")
		--是否一手出完
		local type, lvl = GameLogic:GetCardType(cbCardData, cbCardCount)
		print("cbTurnCardData type", type, lvl)
        if type ~= GameLogic.CT_ERROR  then
            cbResultCardCount[cbResultCount] = cbCardCount
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = cbCardData
            cbResultCount = cbResultCount+1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
        --如果最小牌不是单牌，则提取如果最小牌不是单牌，则提取
        local cbSameCount = 1  
		if cbCardCount > 1 and (GameLogic:getCardLogicValue(cbCardData[cbCardCount]) == 
		GameLogic:getCardLogicValue(cbCardData[cbCardCount-1])) then
            cbSameCount = 2
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount][1] = cbCardData[cbCardCount]
            local cbCardValue = GameLogic:getCardLogicValue(cbCardData[cbCardCount])
            local i = cbCardCount - 1
            while i >= 1 do
                if GameLogic:getCardLogicValue(cbCardData[i]) == cbCardValue then
                    cbResultCard[cbResultCount][cbSameCount] = cbCardData[i]
                    cbSameCount = cbSameCount + 1
                end
                i = i - 1
            end
            cbResultCardCount[cbResultCount] = cbSameCount-1
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end
		--单牌
		print("单牌", cbSameCount)
        local cbTmpCount = 1
        if cbSameCount ~= 2 then
            --print("单牌Pan")
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 1)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
		--对牌
		print("对牌", cbSameCount)
        if cbSameCount ~= 3 then
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 2)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
		--炸弹
		print("炸弹", cbSameCount)
        if cbSameCount ~= 4 then
            local tagSearchCardResult1 = GameLogic:SearchSameCard(cbCardData, cbCardCount, 0, 3)
            cbTmpCount = tagSearchCardResult1[1]
            if cbTmpCount > 0 then
                cbResultCardCount[cbResultCount] = tagSearchCardResult1[2][1]
                cbResultCard[cbResultCount] = {}
                cbResultCard[cbResultCount] = tagSearchCardResult1[3][1]
                cbResultCount = cbResultCount + 1
                tagSearchCardResult[2] = cbResultCardCount
                tagSearchCardResult[3] = cbResultCard
            end
        end
      
        --单连
        print("单连", cbSameCount)
        local tagSearchCardResult4 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, 0, 1, 0)
        cbTmpCount = tagSearchCardResult4[1]
        if cbTmpCount > 0 then
            cbResultCardCount[cbResultCount] = tagSearchCardResult4[2][1]
            cbResultCard[cbResultCount] = {}
            cbResultCard[cbResultCount] = tagSearchCardResult4[3][1]
            cbResultCount = cbResultCount + 1
            tagSearchCardResult[2] = cbResultCardCount
            tagSearchCardResult[3] = cbResultCard
        end

        tagSearchCardResult[1] = cbResultCount - 1
        return tagSearchCardResult
    elseif cbTurnOutType == GameLogic.CT_SINGLE or cbTurnOutType == GameLogic.CT_DOUBLE then
        --单牌、对牌、三条
        local cbReferCard = cbTurnCardData[1]
        if cbTurnOutType == GameLogic.CT_DOUBLE then
            cbReferCard = cbTurnCardData[2]
        end

        local cbSameCount = 1
        if cbTurnOutType == GameLogic.CT_DOUBLE then
            cbSameCount = 2
        end
        print('cbReferCard is ', cbReferCard)
        
        local tagSearchCardResult21 = GameLogic:SearchSameCard(cbCardData, cbCardCount, cbReferCard, cbSameCount)
        cbResultCount = tagSearchCardResult21[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult21[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult21[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1

    elseif cbTurnOutType == GameLogic.CT_SHUNZI then
        --单连、对连、三连
        local cbBlockCount = 1
    
        local cbLineCount = cbTurnCardCount/cbBlockCount
        GameLogic:SortCardList(cbTurnCardData, cbCardCount, GameLogic.ST_LINK)
        local cbFirstValue = self:getCardLogicValue(cbTurnCardData[1])
        if cbFirstValue == 15 then
            GameLogic:SortCardList(cbTurnCardData, cbCardCount, GameLogic.ST_VALUE)
        end
    
        local tagSearchCardResult31 = GameLogic:SearchLineCardType(cbCardData, cbCardCount, cbTurnCardData[1], cbBlockCount, cbLineCount)
        cbResultCount = tagSearchCardResult31[1]
        cbResultCount = cbResultCount + 1
        cbResultCardCount = tagSearchCardResult31[2]
        tagSearchCardResult[2] = cbResultCardCount
        cbResultCard = tagSearchCardResult31[3]
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    end

    --搜索炸弹
	if (cbCardCount >= 3 and cbTurnOutType ~= GameLogic.CT_BOMB_TW) then
        local cbReferCard = 0
        local card_num = 3
        if cbTurnOutType == GameLogic.CT_BOMB then
            cbReferCard = cbTurnCardData[1]
            card_num = cbTurnCardCount
		end
        
        --搜索炸弹
        local tagSearchCardResult61 = GameLogic:SearchSameCard(cbCardData,cbCardCount,cbReferCard, card_num)
        local cbTmpResultCount = tagSearchCardResult61[1]
        for i=1,cbTmpResultCount do
            cbResultCardCount[cbResultCount] = tagSearchCardResult61[2][i]
            tagSearchCardResult[2] = cbResultCardCount
            cbResultCard[cbResultCount] = tagSearchCardResult61[3][i]
            tagSearchCardResult[3] = cbResultCard
            cbResultCount = cbResultCount + 1
        end
        tagSearchCardResult[1] = cbResultCount - 1
    end

    --搜索天王炸弹
    if (cbTurnOutType < GameLogic.CT_BOMB_TW) and GameLogic:IsHasBombTW(cbCardData) then
        --and (cbCardCount >= 2) and (cbCardData[1]==0x4F and cbCardData[4]==0x4E) 
        cbResultCardCount[cbResultCount] = 2
        cbResultCard[cbResultCount] = {0x4F, 0x4E}
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    end

       --搜索天王炸弹
    if (cbTurnOutType < GameLogic.CT_REDTEN) and (cbCardCount >= 2) and
     (self:getCardLogicValue(cbCardData[1]) == 23 
        and self:getCardLogicValue(cbCardData[2]) == 23) then
        cbResultCardCount[cbResultCount] = 2
        cbResultCard[cbResultCount] = {cbCardData[1],cbCardData[2] }
        cbResultCount = cbResultCount + 1
        tagSearchCardResult[2] = cbResultCardCount
        tagSearchCardResult[3] = cbResultCard
        tagSearchCardResult[1] = cbResultCount - 1
    end

    return tagSearchCardResult
end

function GameLogic:IsHasBombTW(cbCardData)
    local count = 0
    for i = 1, #cbCardData do
        if cbCardData[i] == 0x4E or cbCardData[i] == 0x4F then
            count = count + 1
        end
    end

    if count == 2 then
        return true
    end
    return false
end

return GameLogic
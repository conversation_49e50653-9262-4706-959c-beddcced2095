
-------------------------------------------------------------------------------
--  创世版1.0
--  大厅主界面
--  @date 2017-06-02
--  @auth woodoo
-------------------------------------------------------------------------------
if not GoldRoom then
    cs.app.client('system.GoldRoom')
    GoldRoom:getInstance()
end

local ExternalFun = cs.app.client('external.ExternalFun')
local LiveFrame = cs.app.client('frame.LiveFrame')
local cmd_common = cs.app.client('header.CMD_Common')
local HeadSprite = require(appdf.EXTERNAL_SRC .. "HeadSprite")


local MainScene = class("MainScene", cc.load("mvc").ViewBase)


local TAG_CHECK_ACTIVITY = 2342

local ROLL_MSG_TOGGLE_SECONDS = 180


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function MainScene:ctor(app, name)
    self.super.ctor(self, app, name)
    print('MainScene:ctor...')
    self:setName('main_scene')

    self.m_activity_all = {}
    self.m_notify_roll_msg = {} -- 实时推送的跑马灯消息队列

    -- 创建网络
    cs.app.room_frame = cs.app.client('system.RoomFrame'):create(self, handler(self, self.onRoomCallback))

    -- 滚动消息
    local rollmsg = self:getApp():getServerConfig().rollmsg
    self.m_roll_msg = rollmsg and rollmsg ~= '' and rollmsg or LANG{'DEFAULT_ROLL_MSG', weixin=cs.app.WEIXIN_KEFU}

    -- 显示插件
    self:showPlugins()

    if yl.IS_LOCATION_OPEN and GlobalUserItem.bPrivateRoom then
        self:startOrStopLocation(true)
    end
end


-------------------------------------------------------------------------------
-- 进入场景而且过渡动画结束时候触发。
-------------------------------------------------------------------------------
function MainScene:onEnterTransitionFinish()
    print('MainScene:onEnterTransitionFinish...')

    -- 登录服务器网络短连接（每日标记用）
    local callback = function(code, msg, result)
        if code < 0 then
            helper.pop.message(ms)
        else
            -- do nothing
        end
    end
    self.m_frame = helper.app.createFrame(self, callback)

	if PassRoom then
		PassRoom:getInstance():onEnterPlaza(self, cs.app.room_frame)
	end
    -- todo: self:registerNotifyList()
    
    -- todo: 请求公告
    --self:requestNotice()
    self:rollMessage()

    -- 返回键
    helper.app.addKeyPadEvent(self, true)

    -- 必须是onEnter之后，因为头像中用到了自定义事件，必须是running对象才能响应
    self:showPlayer()

    -- 检查加入房间
    self:perform( handler(self, self.checkLinkRoom), 1, -1 )

    -- 检查邀请链接
    self:perform( handler(self, self.checkInvite), 0.5 )

    -- 请求活动列表
    self:requestActivity()

    -- 创建大厅长连接
    LiveFrame:getInstance():connect()

    -- 重置socket监听（连接成功且验证通过）
    LiveFrame:getInstance():addListen(cmd_common.MDM_SOCKET_SERVICE, cmd_common.SUB_SET_SOCKET, self, self.onSetSocketResp)
    -- 类型数值变化通知
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_TYPE_VALUE_NOTIFY, self, self.onTypeValueNotify)
    -- 金币等数量刷新
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_GOLD_INFO, self, self.onGoldInfoResp)
    -- 俱乐部房间加入邀请通知
    LiveFrame:getInstance():addListen(cmd_common.MDM_CLUB_SERVICE, cmd_common.SUB_CLUB_TABLE_INVITE_NOTIFY, self, self.onClubInviteNotify)
    -- 竞猜币变化通知
    LiveFrame:getInstance():addListen(cmd_common.MDM_GUESS_SERVICE, cmd_common.SUB_GUESS_COIN_NOTIFY, self, self.onGuessCoinNotify)
    -- 公告通知
    LiveFrame:getInstance():addListen(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_GLOBAL_REWARD_MSG, self, self.onGlobalRewardNotify)

    -- 新手引导
    if self.main_node:child('btn_my_red_bag'):isVisible() and GlobalUserItem.sysLastLogonDate.wYear < 2000 then -- 首次登录
        helper.pop.popLayer( cs.app.CLIENT_SRC .. 'main.NewbieLayer', self )
    else
    end
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function MainScene:onExit()
    print('MainScene:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
    helper.app.addKeyPadEvent(self, false)

    removebackgroundcallback()

    if cs.app.room_frame:isSocketServer() then
        cs.app.room_frame:onCloseSocket()
    end
    cs.app.room_frame = nil
    --[[
    self:releasePublicRes()
    self:removeListener()

    self:unregisterNotify()		
    --]]

    if PassRoom then
        PassRoom:getInstance():onExitPlaza()
    end

    helper.app.removeAnimationRes('common/ani_waiting_dice')
    helper.app.removeAnimationRes('common/ani_waiting_light')
    helper.app.removeAnimationRes('common/ani_circle_light')
    -- 如果有单独加载合图，在此移除，如：
	--cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("gameScene.plist")
	--cc.Director:getInstance():getTextureCache():removeTextureForKey("gameScene.png")

    helper.app.removeFrame(self.m_frame)
    LiveFrame:getInstance():dtor()
end


-------------------------------------------------------------------------------
-- 初始化界面
-------------------------------------------------------------------------------
function MainScene:onCreate()
    print('MainScene:onCreate...')

    setbackgroundcallback( handler(self, self.onBackgroundCallback) )

    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    MultiPlatform:getInstance():setBackGroundCallBack( handler(self, self.onPhoneBackgroundCallback) )
    
    -- 载入主UI
    local main_node = helper.app.loadCSB('MainLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 健康游戏公告
    if yl.is_reviewing then
        helper.app.createHealthGame(main_node, cc.p(main_node:getContentSize().width/2, 117))
    end

    -- 载入foot UI
    local foot_csb = cs.app.IS_GOLD_HALL and 'MainFootGold.csb' or 'MainFoot.csb'
    local foot_node = helper.app.loadCSB(foot_csb)
    foot_node:setName('foot_node')
    foot_node:pos(main_node:size().width/2, 0):addTo(main_node)

    self:changeBackground( GlobalUserItem.nBackground )

    -- 版本审核（如果审核状态，隐藏下面这些节点）
    yl.ReviewCheck(main_node:child('img_rollmsg,btn_bind,btn_turntable,btn_invite,btn_red_bag,btn_my_red_bag,btn_worldcup,btn_orb'))
    yl.ReviewCheck(foot_node:child('btn_add_fangka,btn_mall,btn_message,btn_change_district'))

    local server_config = yl.app:getServerConfig()

	-- 无推荐人，显示礼包
    if server_config.is_open_giftbag == 0 or 
        (GlobalUserItem.dwSpreader ~= 0 and not cs.app.CAN_CHANGE_BIND) then
	    main_node:child('btn_bind'):hide()
    end

    -- 大转盘屏蔽
    if server_config.is_open_wheel == 0 then
        main_node:child('btn_turntable'):hide()
    end

    -- 邀请屏蔽
    if server_config.is_open_invite == 0 then
        main_node:child('btn_invite'):hide()
    end

    -- 世界杯竞猜屏蔽
    if server_config.is_open_guess == 0 then
        main_node:child('btn_worldcup'):hide()
    end

    -- 拆红包屏蔽
    if device.platform ~= 'windows' and (server_config.is_open_red_bag == 0 or cs.app.IS_GOLD_HALL) then
        main_node:child('btn_orb'):hide()
    end
    if main_node:child('btn_orb'):isVisible() then
        main_node:child('btn_orb/icon'):runAction( cc.RepeatForever:create( cc.Sequence:create(
            cc.DelayTime:create(2),
            cc.Repeat:create( cc.Sequence:create(
                cc.RotateTo:create(0.05, -30),
                cc.RotateTo:create(0.1, 30),
                cc.RotateTo:create(0.05, 0)
            ), 2 )
        ) ) )
    end
    
    helper.logic.addListenerByName(self, {main_node:child('btn_share,btn_bind,btn_turntable,btn_invite,btn_red_bag,btn_my_red_bag,btn_worldcup,btn_orb')})

    foot_node:child('panel_avator'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnAvator) )

    -- 每日分享提醒
    if yl.IS_RED_POINT_OPEN and bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_SHARE) == 0 then
        local light = helper.app.createAnimation('common/ani_circle_light', 25, 1.5, true)
        light:setName('light')
        helper.layout.addCenter(main_node:child('btn_share'), light, {x=3, y=-3})
        main_node:child('btn_share/word'):zorder(10)
    end
    -- 暂时隐藏分享按钮
    main_node:child('btn_share'):hide()

    -- 每日大转盘提醒
    if yl.IS_RED_POINT_OPEN and bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_TURNTABLE) == 0 then
        local light = helper.app.createAnimation('common/ani_circle_light', 25, 1.5, true)
        light:setName('light')
        helper.layout.addCenter(main_node:child('btn_turntable'), light, {x=3, y=0})
        main_node:child('btn_turntable/word'):zorder(10)
    end

    main_node:child('panel_activity'):hide()

        -- 红包活动
    local server_config = yl.app:getServerConfig()
    if server_config.is_open_envelop == 1 and not yl.is_reviewing then
        local light = helper.app.createAnimation('common/ani_circle_light', 25, 1.5, true)
        light:setName('light')
        helper.layout.addCenter(main_node:child('btn_red_bag'), light, {x=3, y=-3})
        main_node:child('btn_red_bag/word'):zorder(10)
    else
        main_node:child('btn_red_bag'):hide()
    end

    -- 是否需要 首次登陆 显示
    local checkPopup = function()
    -- if GlobalUserItem.isShowAdNotice() then
        self:runAction(cc.Sequence:create(
            cc.DelayTime:create( 1.0 ),
            cc.CallFunc:create(function()
                self:checkIsNeedPoupup()
            end)  
        ))
    -- end
    end
    
    local mobile = GlobalUserItem.szMobilePhone
    if not yl.is_reviewing and (not mobile or mobile:trim() == '') then
        helper.link.toMobileBind(checkPopup, self)
    else
        checkPopup()
    end

    if GlobalUserItem.isFirstLogon then
        GlobalUserItem.isFirstLogon = false
    end

    -- 切换地区屏蔽
    if server_config.is_open_district == 0 then
        foot_node:child('btn_change_district'):hide()
    end

    cs.app.client('main.MainFoot')
    self:initFoot()

    -- 是否金币场大厅
    if cs.app.IS_GOLD_HALL then
        cs.app.client('main.MainGoldEx')
        self:InitGoldUI()

        if server_config.is_open_my_red_bag == 0 then
            main_node:child('btn_my_red_bag'):hide()
        end
    else
        main_node:child('btn_my_red_bag'):hide()
        --main_node:child('btn_share/word'):texture('word/font_share.png')    -- 默认是“签到分享”，如果不是金币场换成普通的“分享”
    end

    -- 右上角几个按钮位置调整，显示的按钮按固定顺序显示
    local btn_share,btn_bind,btn_red_bag,btn_my_red_bag,btn_worldcup,btn_orb = main_node:child('btn_share,btn_bind,btn_red_bag,btn_my_red_bag,btn_worldcup,btn_orb')
    local start_x, y = btn_my_red_bag:pos()
    local index = 0
    for i, btn in ipairs{btn_my_red_bag,btn_bind,btn_share,btn_red_bag,btn_worldcup,btn_orb} do
        if btn:isVisible() then
            btn:pos(start_x - 90 * index, y)
            index = index + 1
        end
    end
end


-------------------------------------------------------------------------------
-- 改变背景(设置会调用)
-------------------------------------------------------------------------------
function MainScene:changeBackground(index)
    local path = 'common/bg_main' .. (index == 1 and '' or '_' .. index) .. '.jpg'
    if cc.FileUtils:getInstance():isFileExist(path) then
        self.main_node:child('bg_main'):texture(path)
    end
end


-------------------------------------------------------------------------------
-- 设置当前地区名称
-- 地区名称只显示市县
-- 名称可能很长，先直接显示：“台州市临海县”
-- 如果太长，缩小字体，换行显示
-- 如果还太长，整体缩放
-------------------------------------------------------------------------------
function MainScene:setDistrictName(label, good_width)
    local districts = cc.UserDefault:getInstance():getStringForKey('mydistrict', ''):split('|')
    local city = districts[2] or ''
    local dist = districts[3] or ''

    -- 可能放到foot上
    if not label then
        local btn = self.main_node:child('btn_change_district') or self.main_node:child('foot_node/btn_change_district')
        label = btn:child('text'):scale(1)
    end
    label:setFontSize(22)
    label:setString(city .. dist)
    good_width = good_width or 136
    local size = label:size()
    if size.width > good_width then
        label:setFontSize(20)
        label:setString(city .. '\n' .. dist)
        size = label:size()
        if size.width > good_width then
            helper.layout.scaleToWidth(label, good_width)
        end
    end
end


-------------------------------------------------------------------------------
-- 获取金币等信息
-------------------------------------------------------------------------------
function MainScene:requestGoldInfo()
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = 0} )
	cmd_data:setcmdinfo(cmd_common.MDM_ITEM_SERVICE, cmd_common.SUB_GOLD_INFO)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 长连接认证返回（监听是为了中连接完成后发送命令）
--  可能情形：1、首次正常连接，2、退出房间后重连，3、意外断线重连
-------------------------------------------------------------------------------
function MainScene:onSetSocketResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_ID)
    if not ret then return false end

    -- 只执行一次的任务
    if not self._has_set_socket then
        self._has_set_socket = true
        -- nothing now ...
    end

    -- 金币等属性刷新
    self:requestGoldInfo()

    -- 金币场大厅扩展
    if self.onSetSockGold then
        self:onSetSockGold()
    end
end


-------------------------------------------------------------------------------
-- 类型数值变化通知
-------------------------------------------------------------------------------
function MainScene:onTypeValueNotify(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDValue)
    if not ret then return false end

    if ret.dwID == 1 then -- 1金币
        self:updateFangka( nil, ret.nValue )
    elseif ret.dwID == 2 then -- 2房卡
        self:updateFangka( ret.nValue )
    elseif ret.dwID == 3 then -- 3奖券
        self:updateFangka( nil, nil, ret.nValue )
    end
end


-------------------------------------------------------------------------------
-- 金币信息返回
-------------------------------------------------------------------------------
function MainScene:onGoldInfoResp(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.tagGoldInfo)
    if not ret then return false end

    self:updateFangka( nil, ret.llGold, ret.dwTicket )
end


-------------------------------------------------------------------------------
-- 俱乐部加入房间邀请通知
-------------------------------------------------------------------------------
function MainScene:onClubInviteNotify(data)
    if helper.app.getFromScene('game_room_layer') then return end
    local ret = LiveFrame:getInstance():resp(data, cmd_common.tagClubRuleTableInviteNotify)
    if not ret then return end    

    local msg = LANG{'CLUB_INVITE_NOTIFY', club=ret.szClubName, member=ret.szUserName, rule=ret.szRule}
    local title = display.newSprite('word/font_title_room_invite.png')
    local alert = helper.pop.alert({title, msg}, function()
        helper.pop.waiting()
        PassRoom:getInstance():getNetFrame():onSearchRoom( string.format('%06d', ret.nRoomNo), yl.SEARCH_ROOM_TYPE_JOIN )
    end, true)
    alert:resize(720, 440)
end


-------------------------------------------------------------------------------
-- 竞猜币变化通知
-------------------------------------------------------------------------------
function MainScene:onGuessCoinNotify(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_ID)
    if not ret then return end

    GlobalUserItem.nGuessCoin = ret.dwID
end


-------------------------------------------------------------------------------
-- 公告通知
-------------------------------------------------------------------------------
function MainScene:onGlobalRewardNotify(data)
    local ret = LiveFrame:getInstance():resp(data, cmd_common.CMD_GR_IDMsg)
    if not ret then return end

    if ret.dwID == 1 then -- 跑马灯消息
        table.insert(self.m_notify_roll_msg, ret.szMsg)
    end
end


-------------------------------------------------------------------------------
-- 请求活动信息
-------------------------------------------------------------------------------
function MainScene:requestActivity()
    helper.pop.waiting()
    yl.GetUrl(yl.URL_ACTIVITY, 'post', {uid=GlobalUserItem.dwUserID}, handler(self, self.onActivityRespose) )
end


-------------------------------------------------------------------------------
-- 活动返回
-------------------------------------------------------------------------------
function MainScene:onActivityRespose(data, response, http_status)
    if tolua.isnull(self) then return end
    if not helper.app.urlErrorCheck(data, response, http_status) then return end

    GlobalUserItem.qrcode_url = data.qrcode_url or ''
    self.m_activity_all = data.slides
    if #self.m_activity_all == 0 then return end

    for i, item in ipairs(self.m_activity_all) do
        for _, name in ipairs{'image', 'res'} do
            local save_path, save_name, use_path = helper.app.getDownloadParam('activity', item[name])
            item[name .. '_url'] = item[name]
            item[name .. '_save_path'] = save_path
            item[name .. '_save_name'] = save_name
            item[name] = use_path
        end
    end

    self:updateActivity()
end


-------------------------------------------------------------------------------
-- 检查房间返回
-------------------------------------------------------------------------------
function MainScene:updateActivity()
    self.m_activity_data = {}

    local districts = cc.UserDefault:getInstance():getStringForKey('mydistrict', ''):split('|')
    local city = districts[2] or ''
    local dist = districts[3] or ''
    for i, item in ipairs(self.m_activity_all) do
        if not item.district_name or item.district_name == '' then
            self.m_activity_data[#self.m_activity_data + 1] = item
        else
            local t = item.district_name:split('|')
            if table.indexof(t, dist) then
                self.m_activity_data[#self.m_activity_data + 1] = item
            end
        end
    end

    local panel_activity = self.main_node:child('panel_activity')
    local panel_place = panel_activity:child('panel_place')
    local activity_page = panel_place:child('activity_page')
    if not activity_page then
        activity_page = cs.app.client('system.ActivityPage'):create(panel_place:size())
        activity_page:setName('activity_page')
        helper.layout.addCenter(panel_place, activity_page)

        local origin_x, origin_y = panel_activity:pos()
        panel_activity:px(origin_x - 400)
        self:perform(function()
            if activity_page:isAllLoaded() then
                self:stop(TAG_CHECK_ACTIVITY)
                panel_activity:show():moveTo{time=0.2, x=origin_x, y=origin_y}
            end
        end, 0.1, -1, TAG_CHECK_ACTIVITY)
    end
    activity_page:refreshData( self.m_activity_data, handler(self, self.onActivityClick) )
end


-------------------------------------------------------------------------------
-- 检查是否需要进入链接房间
-------------------------------------------------------------------------------
function MainScene:checkLinkRoom()
    local room_id = getLinkRoomID()
    if not room_id or room_id == '' then return end

    if string.sub(room_id, 1, 1) == 'c' then
        print('link to club join: ', string.sub(room_id, 2))
        self:onBtnClub('join', {string.sub(room_id, 2)})
    else
        print('link to room: ', room_id)
        helper.app.checkRoom(function()
            helper.pop.waiting()
            PassRoom:getInstance():getNetFrame():onSearchRoom(room_id, yl.SEARCH_ROOM_TYPE_LINK)
        end, nil, room_id)
    end
end


-------------------------------------------------------------------------------
-- 检查是否邀请安装
-------------------------------------------------------------------------------
function MainScene:checkInvite()
    if cc.UserDefault:getInstance():getBoolForKey("invitebinded", false) then
        return
    end
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    print('get invite uid...')
    local invite_uid = MultiPlatform:getInstance():getWMParam('u_id')

    print('invite_uid...............', invite_uid)
    if not invite_uid or invite_uid == '' then return end

    yl.GetUrl(yl.URL_INVITE_BIND, 'post', {uid=GlobalUserItem.dwUserID, source=invite_uid}, 
        function(data, response, http_status)
            if not helper.app.urlErrorCheck(data, response, http_status) then return end
            cc.UserDefault:getInstance():setBoolForKey("invitebinded", true)
        end
    )
end


-------------------------------------------------------------------------------
-- 进入后台或返回前台回调
-------------------------------------------------------------------------------
function MainScene:onBackgroundCallback( is_foreground )
    print('返回前台')

    LiveFrame:getInstance():onBackgroundCallback(is_foreground)

    if is_foreground then   -- 回到前台
        -- 如果是在解散等待，则修正时间
        local game_layer = PassRoom:getInstance()._viewFrame
        if game_layer then
            local dismiss_layer = game_layer:child('dismiss_layer')
            if dismiss_layer then
                dismiss_layer:resetTime()
            end
        end
        -- 刷新界面某些显示
        local cur_gamelayer = helper.app.getFromScene('game_room_layer')
        if cur_gamelayer then
            cur_gamelayer:updateView()
            cur_gamelayer:startCheckIsSocketOK(true)
        end
    else    -- 进入后台

    end

    helper.app.triggerBackgroundCallback(is_foreground)
end


-------------------------------------------------------------------------------
-- 进入后台或返回前台回调 电话
-------------------------------------------------------------------------------
function MainScene:onPhoneBackgroundCallback( is_foreground )
    print('电话返回前台')
    if is_foreground == 1 then   -- 回到前台
        -- 刷新界面某些显示
        local cur_gamelayer = helper.app.getFromScene('game_room_layer')
        if cur_gamelayer then
            cur_gamelayer:startCheckIsSocketOK(true)
        end
    else    -- 进入后台

    end
end

-------------------------------------------------------------------------------
-- 网络回调 - 短
-------------------------------------------------------------------------------
function MainScene:onGameCallback()
end


-------------------------------------------------------------------------------
-- 网络回调 - 房间(RoomFrame的回调）
-------------------------------------------------------------------------------
function MainScene:onRoomCallback(code, msg)
    helper.pop.waiting(false)
    helper.pop.message(msg)
end


-------------------------------------------------------------------------------
-- 显示插件
-------------------------------------------------------------------------------
function MainScene:showPlugins()
    local plugin_node = cs.app.client('main.GamePlugin').new( handler(self, self.onPluginClick) )
    local plugin_place = self.main_node:child('plugin_place')
    plugin_node:pos(plugin_place:pos()):addTo(self.main_node)
    plugin_place:removeFromParent()
    self.plugin_node = plugin_node
end


-------------------------------------------------------------------------------
-- 显示个人信息
-------------------------------------------------------------------------------
function MainScene:showPlayer()
    local foot_node = self.main_node:child('foot_node')

    -- 头像
    local panel = foot_node:child('panel_avator')
    panel:removeAllChildren()
    panel:setCascadeColorEnabled(true)

    local size = panel:size()
	local head = HeadSprite:createNormal(GlobalUserItem, 70)
    head:pos(size.width/2, size.height/2):addTo(panel)
    head:setCascadeColorEnabled(true)

    -- 月卡
    if helper.app.addAvatorCard(panel, 0.56) then
        foot_node:child('bg_avator'):hide()
    end

    -- 昵称、ID
    local id_str = LANG{'ID_STR', id=helper.str.formatUserID(GlobalUserItem.dwUserID)}
    local name_str = helper.str.getLenCharsFromString(GlobalUserItem.szNickName, 10, '...')
    foot_node:child('label_name'):setString( name_str .. '  ' ..  id_str)

    -- 房卡数量lRoomCard
    self:updateFangka( GlobalUserItem.lRoomCard, GlobalUserItem.lUserScore )
end


-------------------------------------------------------------------------------
-- 更新房卡数量
-------------------------------------------------------------------------------
function MainScene:updateFangka(num_fangka, num_gold, num_quan)
    local foot_node = self.main_node:child('foot_node')
    if num_fangka then
        GlobalUserItem.lRoomCard = num_fangka
        if foot_node:child('label_fangka') then
            foot_node:child('label_fangka'):setString(num_fangka)
        end
    end
    if num_gold then
        GlobalUserItem.lUserScore = num_gold
        if foot_node:child('label_gold') then
            foot_node:child('label_gold'):setString( helper.str.makeFormatNum(num_gold, 1) )
        end
    end
    if num_quan then
        GlobalUserItem.dwTicket = num_quan
    end
end


-------------------------------------------------------------------------------
-- 设置绑定成功
-------------------------------------------------------------------------------
function MainScene:setRecommendBind()
    if cs.app.CAN_CHANGE_BIND then return end
    self.main_node:child('btn_bind'):hide()
end


-------------------------------------------------------------------------------
-- 滚动消息
-------------------------------------------------------------------------------
function MainScene:rollMessage()
    local content = self.m_roll_msg
    local is_queue = false
    if #self.m_notify_roll_msg > 0 then
        content = self.m_notify_roll_msg[1]
        table.remove(self.m_notify_roll_msg, 1)
        is_queue = true
    end

    local panel = self.main_node:child('img_rollmsg/panel')
    panel:removeAllChildren()
    local size = panel:size()
	local msg = cs.app.client('system.RichText').new(content, 22)
	local distance = size.width + msg:size().width
	local duration = distance / 100
    local sum_duration = 0
    local start_pos = cc.p(ISLEFT and size.width or 0, size.height/2)
	msg:opacity(0):anchor(ISLEFT and 0 or 1, 0.5):addTo(panel)
	msg:runMyAction( cc.RepeatForever:create( cc.Sequence:create(
        cc.Place:create(start_pos),
		cc.Spawn:create(
			cc.FadeIn:create(1),
			cc.MoveBy:create(duration, cc.p( (ISLEFT and -1 or 1) * distance, 0 )),
			cc.Sequence:create( cc.DelayTime:create(duration - 1), cc.FadeOut:create(0.5) )
		),
        cc.CallFunc:create(function()
            if tolua.isnull(self) then return end
            sum_duration = sum_duration + duration
            if not is_queue and #self.m_notify_roll_msg > 0 then -- 当前非队列，且队列有了数据，立即切换到队列
                msg:stop()
                self:rollMessage()
            elseif is_queue and sum_duration >= ROLL_MSG_TOGGLE_SECONDS then -- 当前为队列，时间已经到3分钟
                msg:stop()
                self:rollMessage()
            end
        end)
	) ) )
end


-------------------------------------------------------------------------------
-- 设置标记
-------------------------------------------------------------------------------
function MainScene:setDailyFlag(sender, flag_digit)
    if not yl.IS_RED_POINT_OPEN then return end
    GlobalUserItem.lClientFlag = bit:_or(GlobalUserItem.lClientFlag, flag_digit)
    self.m_frame:onDailyFlag()
    sender:removeChildByName('light')
end


-------------------------------------------------------------------------------
-- 头像点击
-------------------------------------------------------------------------------
function MainScene:onBtnAvator(sender)
    if not yl.is_reviewing then
        helper.link.toPersonalCenter()
    end
end


-------------------------------------------------------------------------------
-- 加房卡按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnAddFangka(sender)
    helper.link.toFangka()
end


-------------------------------------------------------------------------------
-- 加金币按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnAddGold(sender)
    helper.link.toMall( LANG.MALL_TAB_GOLD )
end


-------------------------------------------------------------------------------
-- 打开外链
-------------------------------------------------------------------------------
function MainScene:openLink(base_url)
    local params = {
        uid  = GlobalUserItem.dwUserID
    }
    local full_param = yl.signWebParams(params)
    local full_url = base_url .. '?' .. full_param
    print('open link:', full_url)
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    MultiPlatform:getInstance():openBrowser(full_url)
end


-------------------------------------------------------------------------------
-- 插件点击
-------------------------------------------------------------------------------
function MainScene:onPluginClick(sender)
    local config = sender.config
    if config.link then -- 外链
        -- 检查下发中是否存在
        local server_config = helper.app.getLinkConfigByKind(config.link)
        if not server_config then
            helper.pop.message( LANG.NO_KIND_CONFIG )
            return
        end
        self:openLink(server_config.link_url)
    elseif not config.kind then  -- 游戏
        if config.group == 'function' then
            local name = config.game
            if name == 'club' then
                self:onBtnClub()
            elseif name == 'join' then
                self:onBtnRoomJoin()
            elseif name == 'gold' then
                self:onBtnRoomGold()
            elseif name == 'arena' then
                self:onBtnArena()
            elseif name == 'district' then
                self:onBtnDistrict()
            end
        end
    else
        if not helper.app.getGameConfigByKind(config.kind) then
            helper.pop.message( LANG.NO_KIND_CONFIG )
            return
        end

        if not cs.app.IS_GOLD_HALL then
            helper.app.checkGameUpdate(config.game, config.kind, function()
                local path = cs.app.CLIENT_SRC .. 'main.RoomCreateLayer'
                helper.pop.popLayer(path, nil, config.kind):setName('room_create_layer')
            end)
        else
            local path = cs.app.CLIENT_SRC .. 'main.GoldKindLayer'
            helper.pop.popLayer(path, nil, {config})
        end
    end
end


-------------------------------------------------------------------------------
-- 加入房间按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnRoomJoin()
    local path = cs.app.CLIENT_SRC .. 'main.RoomJoinLayer'
    helper.pop.popLayer(path):setName('room_join_layer')
end


-------------------------------------------------------------------------------
-- 金币场按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnRoomGold()
    if cs.app.IS_GOLD_OPEN then
        local path = cs.app.CLIENT_SRC .. 'main.GoldLayer'
        helper.pop.popLayer(path)
    else
        helper.pop.message( LANG.OPEN_SOON )
    end
end


-------------------------------------------------------------------------------
-- 俱乐部按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnClub(target_name, params)
    helper.link.toClub(target_name, params)
end


-------------------------------------------------------------------------------
-- 比赛场按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnArena()
    if yl.app:getServerConfig().is_arena_open == 0 then
        helper.pop.message( LANG.OPEN_SOON )
    else
        helper.link.toArena()
    end
end


-------------------------------------------------------------------------------
-- 切换地区按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnChangeDistrict()
    self:onBtnDistrict()
end


-------------------------------------------------------------------------------
-- 地区选择点击
-------------------------------------------------------------------------------
function MainScene:onBtnDistrict()
    local callback = function()
        self:setDistrictName()
        self.plugin_node:refresh()
        self:updateActivity()
    end
    helper.pop.popLayer( cs.app.CLIENT_SRC .. 'main.DistrictSelectLayer', nil, {callback} )
end


-------------------------------------------------------------------------------
-- 战绩按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnScore(sender)
    helper.link.toScore()
end


-------------------------------------------------------------------------------
-- 设置按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnSet(sender)
    helper.link.toSetting()
end


-------------------------------------------------------------------------------
-- 规则按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnRule(sender)
    helper.link.toRule()
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnShare(sender)
    if bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_SHARE) == 0 then
        self:setDailyFlag(sender, yl.DAILY_FLAG_SHARE)
    end

    if not yl.is_reviewing and not cs.app.USE_FANGKA_SHARE then
        helper.link.toSignIn()
    else
        local share = nil
        if cs.app.HALL_SHARE_URL and LANG.HALL_SHARE_TITLE and LANG.HALL_SHARE_DESC then
            local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
            local channel = MultiPlatform:getInstance():getChannel()
            local uid = GlobalUserItem.dwUserID
            local titles = LANG.HALL_SHARE_TITLE:split('||')
            local title = titles[#titles > 1 and math.random(1, #titles) or 1]
            local url = string.format('%s?channel_id=%s&role_id=%d', cs.app.HALL_SHARE_URL, channel, uid)
            share = helper.pop.shareLink(url, title, LANG.HALL_SHARE_DESC, false, true, 'share_game')
        else
            share = helper.pop.shareImage('common/share_image.jpg', false, true, 'share_game')
        end
        share:showButtons('pyq')
    end
end


-------------------------------------------------------------------------------
-- 商城按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnMall(sender)
    helper.link.toMall()
end


-------------------------------------------------------------------------------
-- 礼包绑定按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnBind(sender)
    helper.link.toBind()
end


-------------------------------------------------------------------------------
-- 大转盘点击
-------------------------------------------------------------------------------
function MainScene:onBtnTurntable(sender)
    if bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_TURNTABLE) == 0 then
        self:setDailyFlag(sender, yl.DAILY_FLAG_TURNTABLE)
    end

    helper.link.toTurntable()
end


-------------------------------------------------------------------------------
-- 邀请点击
-------------------------------------------------------------------------------
function MainScene:onBtnInvite(sender)
    helper.link.toInvite()
end


-------------------------------------------------------------------------------
-- 我的红包
-------------------------------------------------------------------------------
function MainScene:onBtnMyRedBag(sender)
    helper.link.toMyRedBag()
end


-------------------------------------------------------------------------------
-- 红包
-------------------------------------------------------------------------------
function MainScene:onBtnRedBag(sender)
    helper.link.toRedBag()
end


-------------------------------------------------------------------------------
-- 世界杯竞猜
-------------------------------------------------------------------------------
function MainScene:onBtnWorldcup(sender)
    helper.link.toWorldCup()
end


-------------------------------------------------------------------------------
-- 拆红包
-------------------------------------------------------------------------------
function MainScene:onBtnOrb(sender)
    helper.link.toOrb()
end


-------------------------------------------------------------------------------
-- 活动点击
-------------------------------------------------------------------------------
function MainScene:onActivityClick(sender)
    helper.link.toActivity(self.m_activity_data, sender.index)
end


-------------------------------------------------------------------------------
-- 启动游戏
-------------------------------------------------------------------------------
function MainScene:onStartGame()
    local enter_game = helper.app.getGameConfigByKind(GlobalUserItem.nCurGameKind)
    if not enter_game then
        helper.pop.message( LANG.NO_GAME_CONFIG )
        return
    end 

    -- 对加入房间、金币场进入有效(创建房间已在各自界面检查过)
    helper.app.checkGameUpdate(enter_game._Module, GlobalUserItem.nCurGameKind, function()
        GlobalUserItem.m_tabEnterGame = enter_game
	    enter_game.nEnterRoomIndex = GlobalUserItem.nCurRoomIndex

	    helper.pop.waiting()
	    cs.app.room_frame:onInitData()
	    cs.app.room_frame:setKindInfo(GlobalUserItem.nCurGameKind, enter_game._ClientVersion)
	    cs.app.room_frame:setViewFrame(self)
	    cs.app.room_frame:onCloseSocket()
	    cs.app.room_frame:onLogonRoom()
    end)
end


-------------------------------------------------------------------------------
-- 启动游戏
-------------------------------------------------------------------------------
function MainScene:onEnterTable( isReplay )
	print("MainScene onEnterTable")

    if not GlobalUserItem.nCurGameKind or not GlobalUserItem.m_tabEnterGame then
        helper.pop.message( LANG.UNKNOW_OPERATE )
        return
    end

    LiveFrame:getInstance():disconnect()

    if isReplay then
        yl.IS_REPLAY_MODEL = true
    end
    -- 目前不太清楚什么更好的时机去移除加入房间界面，先放在这里，这句一定要放前
    helper.app.removeFromScene('room_join_layer')
    helper.app.removeFromScene('room_create_layer')
    helper.app.removeFromScene('gold_kind_layer')    
    helper.app.removeFromScene('arena_wait_layer')
    helper.app.removeFromScene('red_arena_waiting')
    helper.app.showFromScene('club_main_layer', false)
    helper.app.showFromScene('arena_layer', false)

	if PassRoom then
		-- 动作记录
		PassRoom:getInstance().m_nLoginAction = PassRoom.L_ACTION.ACT_ENTERTABLE
	end

    local module_name = GlobalUserItem.m_tabEnterGame._Module

    -- 初始化游戏，从此之后可以使用cs.game、cs.ap.game(...)
    cs.app.req(cs.app.GAME_ROOT .. '.' .. module_name .. '.src.init')
    --print(cs.app.GAME_ROOT .. '.' .. module_name .. '.src.init')

    -- 加载玩法特定资源路径到搜索路径
    helper.app.addKindSearchPath( module_name, GlobalUserItem.nCurGameKind, appdf.PACK_PATH )

	local GameLayer = cs.app.game( 'room.GameLayer' )
	local game_layer = GameLayer:create(cs.app.room_frame, self)
    game_layer:setName( 'game_room_layer' )
    local cur_game_layer = helper.app.getFromScene( 'game_room_layer' )
    if cur_game_layer then
        cur_game_layer:KillGameClock()
        helper.app.removeFromScene( 'game_room_layer' )
    end

    if isReplay then
        yl.IS_REPLAY_MODEL = true
        game_layer:startOrStopReplay( true )
        print( "yl.IS_REPLAY_MODEL true" )
    end
    
    cs.app.room_frame:setViewFrame( game_layer )
    helper.app.addToScene( game_layer )
	if PassRoom then
		PassRoom:getInstance():enterGame( game_layer, self )
	end

    helper.music.playRoom()
end


-------------------------------------------------------------------------------
-- 退出房间
-------------------------------------------------------------------------------
function MainScene:onExitRoom()
    print('MainScene:onExitRoom...')

    -- 移除玩法特定资源路径
    helper.app.removeKindSearchPath()

    if PassRoom then
	    PassRoom:getInstance():exitRoom()
        PassRoom:getInstance():exitGame()
    end
    cs.app.room_frame:setViewFrame(self)
    helper.voice.cancelRecord()
    cs.app.room_frame:clearVoiceQueue()

    local module_name = GlobalUserItem.m_tabEnterGame._Module
    helper.app.cleanPackages('game.' .. module_name .. '.src.')
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
    cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
    helper.app.removeGameSearchPath(module_name)

    cs.game = nil
    helper.music.playPlaza()

    helper.app.showFromScene('club_main_layer', true)

    local arena_layer = helper.app.getFromScene('arena_layer')
    if arena_layer then
        arena_layer:show()
        arena_layer:requestArenas()
    end

    LiveFrame:getInstance():connect()
end


-------------------------------------------------------------------------------
-- 房间重连
-------------------------------------------------------------------------------
function MainScene:startOrStopLocation( status )
    if status then
        -- print('start.............location')
        -- self:perform( handler(self, self.requestLocation), 10, -1, yl.ActionTag.LOCATION )
        self:requestLocation()
    else
        -- print('stop.............location')
        -- self:stop( yl.ActionTag.LOCATION )
    end
end


-------------------------------------------------------------------------------
-- 定位 
-------------------------------------------------------------------------------
function MainScene:requestLocation()
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    MultiPlatform:getInstance():requestLocation(function(res)
    local ok, datatable = pcall(function()
	    return cjson.decode(res)
	end)
    if ok then
        --dump(datatable, " location data mike ", 6) 
        if datatable.berror then
            datatable.longitude = 0
            datatable.latitude = 0
            datatable.accuracy = 0
        end
        PassRoom:getInstance():getNetFrame():onSendLocation(datatable)
    else
        datatable = {}
        datatable.berror = true
        datatable.longitude = 0
        datatable.latitude = 0
        datatable.accuracy = 0
        PassRoom:getInstance():getNetFrame():onSendLocation(datatable)
    end
    end)
end

-- 检测是否需要弹窗
function MainScene:checkIsNeedPoupup()
    local uid = GlobalUserItem.dwUserID
    yl.GetUrl( yl.URL_POPUP, 'post', { uid = uid }, handler(self, self.onGetIsNeedPoupupResp))
end

-- 检测是否需要弹窗的返回
function MainScene:onGetIsNeedPoupupResp(data, response, http_status)
    if tolua.isnull(self) then return end

    local showSignIn = function()
        -- 今日未分享 -- todo: remove windows
        if device.platform ~= 'windows' and not yl.is_reviewing and bit.band(GlobalUserItem.lClientFlag, yl.DAILY_FLAG_SHARE) == 0 then  
            if not cs.app.USE_FANGKA_SHARE then
                helper.link.toSignIn()
            end
        end
    end

    if not helper.app.urlErrorCheck(data, response, http_status) then
        showSignIn()
        return
    end
    --dump(data.data, '检测是否需要弹窗的返回', 9)
    local len = table.nums(data.data)
    if len <= 0 then
        showSignIn()
        return
    end
    helper.link.toFirstAD( data.data )
end

return MainScene
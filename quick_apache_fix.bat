@echo off
echo ===== Apache 快速修复脚本 =====
echo.

echo 1. 检查并结束可能冲突的进程...
echo.

echo 检查 IIS 服务...
sc query W3SVC 2>nul
if %errorlevel% equ 0 (
    echo 发现 IIS 服务，尝试停止...
    net stop W3SVC
)

echo.
echo 检查占用端口 80 的进程...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":80 " ^| findstr "LISTENING"') do (
    echo 发现进程 PID: %%a 占用端口 80
    tasklist /FI "PID eq %%a"
    echo 是否要结束此进程？ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        taskkill /PID %%a /F
    )
)

echo.
echo 检查占用端口 443 的进程...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":443 " ^| findstr "LISTENING"') do (
    echo 发现进程 PID: %%a 占用端口 443
    tasklist /FI "PID eq %%a"
    echo 是否要结束此进程？ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        taskkill /PID %%a /F
    )
)

echo.
echo 2. 尝试重新启动 Apache...
echo.

if exist "E:\xampp\apache\bin\httpd.exe" (
    echo 测试 Apache 配置...
    "E:\xampp\apache\bin\httpd.exe" -t
    
    if %errorlevel% equ 0 (
        echo 配置文件正常，尝试启动服务...
        net start Apache2.4
        
        echo 等待 3 秒...
        timeout /t 3 /nobreak >nul
        
        echo 检查 Apache 是否成功启动...
        netstat -ano | findstr ":80 "
        netstat -ano | findstr ":443 "
        
    ) else (
        echo 配置文件有错误，请检查！
    )
) else (
    echo Apache 可执行文件不存在，请检查 XAMPP 安装
)

echo.
echo 3. 测试结果...
curl -I http://localhost/ 2>nul || echo 无法连接到 localhost

echo.
pause

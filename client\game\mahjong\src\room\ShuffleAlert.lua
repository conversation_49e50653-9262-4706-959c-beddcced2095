-------------------------------------------------------------------------------
--  创世版1.0
--  绑定
--  @date 2017-06-07
--  @auth woodoo
-------------------------------------------------------------------------------
local ShuffleAlert = class("ShuffleAlert", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function <PERSON><PERSON><PERSON>lert:ctor(rate, callback)
    print('ShuffleAlert:ctor...')
    self:enableNodeEvents()
    self.callback = callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('ShuffleAlert.csb')
    self.main_node = main_node
    self:addChild(main_node)

    main_node:child('title'):setString( LANG{'ROOM_SHUFFLE_CONFIRM', rate = rate} )

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_cancel'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_ok'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnOk) )
    
    main_node:child('hint'):addTouchEventListener( helper.app.commClickHandler(self, self.onHintClick) )
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ShuffleAlert:onExit()
    print('ShuffleAlert:onExit...')
end


-------------------------------------------------------------------------------
-- 不再提示文字点击
-------------------------------------------------------------------------------
function ShuffleAlert:onHintClick(sender)
    local check = self.main_node:child('check')
    check:setSelected( not check:isSelected() )
end


-------------------------------------------------------------------------------
-- 绑定按钮点击
-------------------------------------------------------------------------------
function ShuffleAlert:onBtnOk(sender)
    local check = self.main_node:child('check')
    self.callback(check:isSelected())
    self:removeFromParent()
end


return ShuffleAlert
local cmd =  {}

--游戏版本
cmd.VERSION 					= appdf.VersionValue(6,7,0,1)
--游戏标识
cmd.KIND_ID						= 113
	
--游戏人数
cmd.GAME_PLAYER					= 5

--每个人的起始手牌
cmd.GAME_CARD_NUM               = 9

--视图位置
cmd.MY_VIEWID					= 3

--******************         游戏状态             ************--
--等待开始
cmd.GS_TK_FREE					= 0
--叫庄状态
cmd.GS_TK_CALL					= 100
--下注状态
cmd.GS_TK_SCORE					= 101
--游戏进行
cmd.GS_TK_PLAYING				= 102

--*********************      服务器命令结构       ************--
--游戏开始
cmd.SUB_S_GAME_START			= 100
--加注结果
cmd.SUB_S_ADD_SCORE				= 101
--用户强退
cmd.SUB_S_PLAYER_EXIT			= 102
--发牌消息
cmd.SUB_S_SEND_CARD				= 103
--游戏结束
cmd.SUB_S_GAME_END				= 104
--用户摊牌
cmd.SUB_S_OPEN_CARD				= 105
--用户叫庄
cmd.SUB_S_CALL_BANKER			= 106
cmd.SUB_S_READY_STATE			= 113
cmd.SUB_S_RECORD				= 111								--房卡结算记录

--**********************    客户端命令结构        ************--
--用户叫庄
cmd.SUB_C_CALL_BANKER			= 1
--用户加注
cmd.SUB_C_ADD_SCORE				= 2
--用户摊牌
cmd.SUB_C_OPEN_CARD				= 3
--更新库存
cmd.SUB_C_STORAGE				= 6
--设置上限
cmd.SUB_C_STORAGEMAXMUL			= 7
--请求查询用户
cmd.SUB_C_REQUEST_QUERY_USER	= 8
--用户控制
cmd.SUB_C_USER_CONTROL			= 9

--********************       定时器标识         ***************--
--无效定时器
cmd.IDI_NULLITY					= 200
--开始定时器
cmd.IDI_START_GAME				= 201
--叫庄定时器
cmd.IDI_CALL_BANKER				= 202
--加注定时器
cmd.IDI_TIME_USER_ADD_SCORE		= 1
--摊牌定时器
cmd.IDI_TIME_OPEN_CARD			= 2

--*******************        时间标识         *****************--
--叫庄定时器
cmd.TIME_USER_CALL_BANKER		= 30
--开始定时器
cmd.TIME_USER_START_GAME		= 30
--加注定时器
cmd.TIME_USER_ADD_SCORE			= 30
--摊牌定时器
cmd.TIME_USER_OPEN_CARD			= 30

--空闲开始
cmd.GAME_SCENE_FREE				= 0
--叫庄状态
cmd.GAME_SCENE_PLAY				= 100
--等待状态
cmd.GAME_SCENE_WAITING			= 200

--空闲状态
cmd.CMD_S_StatusFree = 
{
	--基础积分
	{k = "lCellScore", t = "score"},								--基础积分
	--时间信息
	--[[
 	{k = "cbTimeOutCard", t = "byte"},							--出牌时间
 	{k = "cbTimeOperateCard", t = "byte"},						--操作时间
	 {k = "cbTimeStartGame", t = "byte"},						--开始时间
	 --]]
	--历史积分
	{k = "lTurnScore", t = "score", l = {cmd.GAME_PLAYER}},		--积分信息
	{k = "lCollectScore", t = "score", l = {cmd.GAME_PLAYER}},	--积分信息
	{k = "cbPlayerCount", t = "byte"},
	{k = "cbJushu", t = "byte"},
}

--游戏状态
cmd.CMD_S_StatusPlay = 
{
	{k = "lCellScore", t = "score"},								--基础积分
	--时间信息
	{k = "cbPlayStatus", t = "byte", l = {cmd.GAME_PLAYER}},							--出牌时间
	{k = "cbDynamicJoin", t = "byte"},						--叫分时间
	{k = "lTurnMaxScore", t = "score"},						--开始时间
	--游戏变量
	{k = "lTableScore", t = "score", l = {cmd.GAME_PLAYER}},							--单元积分
	{k = "wBankerUser", t = "word"},							--庄家用户
	{k = "cbHandCardData", t = "byte", l = {cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM}},							--当前用户

	{k = "lTurnScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "lCollectScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "cbCurStatus", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbSpecailType", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbSpecailWinDao", t = "int", l = {cmd.GAME_PLAYER}},
	{k = "cbGiveup", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbJushu", t = "byte"},
}


--游戏状态
cmd.CMD_S_SendCard = 
{
	--时间信息
	{k = "wCurrentUser", t = "word"},						--开始时间
	{k = "cbCardData", t = "byte", l = {cmd.GAME_CARD_NUM}},							--出牌时间
	{k = "cbPlayerStatus", t = "byte", l = {cmd.GAME_PLAYER}},						--叫分时间
	{k = "cbSpecailType", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbSpecailWinDao", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbJushu", t = "byte"},						--开始时间
}

cmd.CMD_S_Open_Card = 
{
	--时间信息
	{k = "wPlayerID", t = "word"},						--开始时间
	{k = "bOpen", t = "byte"},							--出牌时间
	{k = "bSpecialType", t = "byte", l = {cmd.GAME_PLAYER}},						--叫分时间
	{k = "bIsSPecaialType", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbIsGiveup", t = "byte", l = {cmd.GAME_PLAYER}},
}

cmd.CMD_S_GameEnd = 
{
	--时间信息
	{k = "lGameTax", t = "score", l = {cmd.GAME_PLAYER}},						--开始时间
	{k = "lGameScore", t = "score", l = {cmd.GAME_PLAYER}},						--开始时间
	{k = "m_lscore", t = "score", l = {3, 3, 3, 3, 3}},						--开始时间
	{k = "m_cbCardType", t = "byte", l = {3, 3, 3, 3, 3}},						--开始时间
	{k = "cbCardData", t = "byte", l = {cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM, cmd.GAME_CARD_NUM}},							--出牌时间
	{k = "cbUseSpcialType", t = "byte", l = {cmd.GAME_PLAYER}},						--叫分时间
	{k = "cbSpeicalType", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "cbSpeicalScore", t = "score", l = {cmd.GAME_PLAYER}},
	{k = "cbGiveup", t = "byte", l = {cmd.GAME_PLAYER}},
	{k = "wDaTongUser", t = "word"},	
	{k = "isJieSan", t = "byte"},						--开始时间
	{k = "wCurrentUser", t = "word"},
}

cmd.CMD_C_OxCard = 
{
	--时间信息
	{k = "bOX", t = "byte"},							--出牌时间
	{k = "cbCardData", t = "byte", l = {cmd.GAME_CARD_NUM}},						--叫分时间
	{k = "is_specailCard", t = "byte",},
	{k = "cbIsGiveup", t = "byte"},
}

return cmd
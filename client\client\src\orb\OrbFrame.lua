-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 框架
--  @date 2019-01-23
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbNet = cs.app.client('orb.OrbNet')
local OrbUtil = cs.app.client('orb.OrbUtil')


local OrbFrame = class("OrbFrame", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbFrame:ctor()
    print('OrbFrame:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('OpenRedBag.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.m_modules = {}
    OrbUtil.setRoot(main_node, self.m_modules)

    -- 创建各个模块
    local names = {
        'panel_first',
        'panel_main',
        'panel_gain_first',
        'panel_first_share',
        'panel_summary',
        'panel_my',
        'panel_share_success',
        'panel_rule_activity',
        'panel_rule_draw',
        'panel_draw',
        'panel_mission',
        'panel_draw_success',
        'panel_share',
        'panel_leave',
    }
    for i, name in ipairs(names) do
        self:createModule(name)
    end

    OrbNet.request('index', {}, handler(self, self.onCmdMain))
end


-------------------------------------------------------------------------------
-- 创建模块
-------------------------------------------------------------------------------
function OrbFrame:createModule(name)
    local panel = self.main_node:child(name)
    local t = name:split('_')
    table.remove(t, 1) -- 去panel
    table.map(t, function(v, k) return v:ucfirst() end)
    local class_name = 'Orb' .. table.concat(t, '')
    local ModuleClass = cs.app.client('orb.' .. class_name)
    self.m_modules[name] = ModuleClass:create(panel)
    panel:hide()
end


-------------------------------------------------------------------------------
-- 主命令请求返回
-------------------------------------------------------------------------------
function OrbFrame:onCmdMain(data, response, http_status)
    if not helper.app.urlErrorCheck(data, response, http_status) then
        self:removeFromParent()
        return
    end

    local data = data.data
    OrbUtil.setMainData(data)

    if data.status == 0 then
        OrbUtil.open('panel_first')
    elseif data.status == 1 then
        OrbUtil.open('panel_main')
        OrbUtil.open('panel_gain_first')
    elseif data.status == 2 then
        OrbUtil.open('panel_main')
    elseif data.status == 3 then
        helper.pop.message( LANG.ORB_OVER )
        self:removeFromParent()
    end
end


return OrbFrame
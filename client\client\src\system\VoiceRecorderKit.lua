-------------------------------------------------------------------------------
--  创世版1.0
--  语音录音
--  @date 2017-06-30
--  @auth woodoo
-------------------------------------------------------------------------------
VoiceRecorderKit = VoiceRecorderKit or {}
-- 录音间隔
VoiceRecorderKit.nInterval = 0
-- 是否在录音
VoiceRecorderKit.bRecordVoice = false


local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
local game_cmd = appdf.req(appdf.HEADER_SRC .. "CMD_GameServer")
local ExternalFun = appdf.req(appdf.EXTERNAL_SRC .. "ExternalFun")

local RECORD_STATE = true
local MAX_RECORD_TIME = 14
local RECORD_SAVE_PATH = device.writablePath .. "/saverec/"


local VoiceRecorderLayer = class("VoiceRecorderLayer", cc.Layer)


--全局函数(ios/android端调用)
cc.exports.g_NativeRecord = function (msg)
    if msg == "record error" then
        local runScene = cc.Director:getInstance():getRunningScene()
        if nil ~= runScene then
            showToastNoFade(runScene, LANG.VOICE_FAILURE, 2)
        end
        RECORD_STATE = false
    end
end


function VoiceRecorderLayer:ctor( room_frame )
    -- 加载csb资源
    local main_node = helper.app.loadCSB('VoiceRecordLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 剩余时间
    self.m_label_tips = main_node:getChildByName("label_tips")
    self.m_label_tips:setRich( LANG{'VOICE_TIME_LEFT', seconds=MAX_RECORD_TIME} )

    -- 移除文件
    cc.FileUtils:getInstance():removeFile(RECORD_SAVE_PATH .. "record.mp3")
    RECORD_STATE = true
    -- 权限检查
    if RECORD_STATE then
        AudioEngine.setMusicVolume(0)
        AudioRecorder:getInstance():startRecord("record.mp3")
    end
    self.m_nRecordTime = 0
    self.m_room_frame = room_frame
    self.m_bCancelRecord = false
    VoiceRecorderKit.bRecordVoice = true

    self:perform( handler(self, self.onCountDown), 1, -1 )
end


function VoiceRecorderLayer:onCountDown(dt)
    self.m_nRecordTime = self.m_nRecordTime + 1
    local left = MAX_RECORD_TIME - self.m_nRecordTime
    self.m_label_tips:setRich( LANG{'VOICE_TIME_LEFT', seconds=left} )
    if 0 == left then
        self:removeRecorde()
    end
end


function VoiceRecorderLayer:removeRecorde()
    VoiceRecorderKit.bRecordVoice = false

    local is_del = true
    if self.m_nRecordTime < 1 then        
        helper.pop.message( LANG.VOICE_TOO_SHORT )
        AudioRecorder:getInstance():cancelRecord()
        AudioEngine.setMusicVolume(GlobalUserItem.nMusic/100.0)
        self:removeFromParent()
        return
    end

    if RECORD_STATE then
        if not self.m_bCancelRecord then
            AudioRecorder:getInstance():endRecord()
            AudioEngine.setMusicVolume(GlobalUserItem.nMusic/100.0)
            
            is_del = false;
            self:perform(function () 
                local buffer = AudioRecorder:getInstance():createSendBuffer()
                if nil ~= buffer and nil ~= self.m_room_frame then
                    print(" 发送录音 ")
                    buffer:setcmdinfo(game_cmd.MDM_GF_FRAME,game_cmd.SUB_GF_USER_VOICE)
                    self.m_room_frame:sendSocketData(buffer)
                end
                self:removeFromParent()
            end, 0.5)
            
            
        end
    else
        AudioRecorder:getInstance():cancelRecord()
        AudioEngine.setMusicVolume(GlobalUserItem.nMusic/100.0)
    end  

    if is_del then
        self:removeFromParent()
    end  
    
end


function VoiceRecorderLayer:cancelVoiceRecord()
    VoiceRecorderKit.bRecordVoice = false

    self.m_label_tips:setRich( LANG.VOICE_CANCEL )
    AudioRecorder:getInstance():cancelRecord()
    AudioEngine.setMusicVolume(GlobalUserItem.nMusic/100.0)
    self.m_bCancelRecord = true
    self:removeFromParent()
end


function VoiceRecorderKit.init()
    -- 配置路径
    AudioRecorder:getInstance():init(device.writablePath .. "/saverec/", device.writablePath .. "/downrec/")
end


function VoiceRecorderKit.createRecorderLayer( room_frame )
    -- 录音间隔
    local lasttime = VoiceRecorderKit.nInterval
    local curtime = os.time()
    if curtime - lasttime < 2 then
        helper.pop.message( LANG.VOICE_INTERVAL )
        return nil
    end

    -- 权限请求
    ---[[
    local bRequest = cc.UserDefault:getInstance():getBoolForKey("recordpermissionreq",false)
    if false == bRequest then
        print("###2")
        -- 尝试请求
        AudioRecorder:getInstance():startRecord("record.mp3")
        AudioRecorder:getInstance():cancelRecord()
        cc.UserDefault:getInstance():setBoolForKey("recordpermissionreq",true)
        VoiceRecorderKit.nInterval = os.time()
        return nil
    end
    --]]

    print("###3")
    -- 权限检查(ios端有效)
    if false == MultiPlatform:getInstance():checkRecordPermission() then
        helper.pop.message( LANG.VOICE_PERMISSION )
        VoiceRecorderKit.nInterval = os.time()
        return nil
    end
    print("###4")
    VoiceRecorderKit.nInterval = os.time()
    return VoiceRecorderLayer:create(room_frame)
end
VoiceRecorderKit.init()


function VoiceRecorderKit.startPlayVoice( spath )
    AudioEngine.setMusicVolume(0)
    AudioEngine.setVoiceVolume(1.0)
    return AudioEngine.playVoice(spath)
end


function VoiceRecorderKit.finishPlayVoice()
    if not VoiceRecorderKit.bRecordVoice then
        AudioEngine.setMusicVolume(GlobalUserItem.nMusic/100.0)
        AudioEngine.setVoiceVolume(0)  
    end
end


return VoiceRecorderKit
<?php
/**
 * 云之讯短信服务测试脚本
 * 用于测试短信验证码功能是否正常
 */

// 云之讯短信配置（从 user.php 中提取）
$accountsid = '5cbc351f17a418686094747a62ffd946';
$token = 'fac1c2af0c05327677d33916ba841079';
$appId = '9dc983c028e54d5fbc97228e6af5344e';
$templateId = '174333';

// 测试手机号（请替换为您的真实手机号）
$phone = '***********';

// 生成验证码
$code = rand(1000, 9999);

echo "=== 云之讯短信服务测试 ===\n";
echo "配置信息:\n";
echo "AccountSID: $accountsid\n";
echo "AppID: $appId\n";
echo "TemplateID: $templateId\n";
echo "测试手机号: $phone\n";
echo "验证码: $code\n\n";

/**
 * 简化版云之讯短信类
 */
class SimpleUcpaas {
    private $accountsid;
    private $token;
    private $baseUrl = 'https://open.ucpaas.com';
    
    public function __construct($options) {
        $this->accountsid = $options['accountsid'];
        $this->token = $options['token'];
    }
    
    /**
     * 发送模板短信
     */
    public function templateSMS($appId, $phone, $templateId, $param) {
        $url = $this->baseUrl . '/ol/sms/sendsms';
        
        // 构建请求数据
        $data = array(
            'sid' => $this->accountsid,
            'token' => $this->token,
            'appid' => $appId,
            'templateid' => $templateId,
            'param' => $param,
            'mobile' => $phone,
            'uid' => ''
        );
        
        echo "请求URL: $url\n";
        echo "请求数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";
        
        // 发送HTTP请求
        $result = $this->sendHttpRequest($url, $data);
        
        return $result;
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendHttpRequest($url, $data) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Accept: application/json'
        ));
        
        // 忽略SSL证书验证（仅用于测试）
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        echo "HTTP状态码: $httpCode\n";
        
        if ($error) {
            echo "CURL错误: $error\n";
            return json_encode(array('resp' => array('respCode' => 'CURL_ERROR', 'respDesc' => $error)));
        }
        
        echo "响应内容: $result\n";
        
        return $result;
    }
}

// 测试短信发送
try {
    $options = array(
        'accountsid' => $accountsid,
        'token' => $token
    );
    
    $ucpaas = new SimpleUcpaas($options);
    
    echo "开始发送短信...\n";
    $result_json = $ucpaas->templateSMS($appId, $phone, $templateId, $code);
    
    echo "\n=== 解析响应 ===\n";
    $result_obj = json_decode($result_json, true);
    
    if ($result_obj && isset($result_obj['resp'])) {
        $respCode = $result_obj['resp']['respCode'];
        $respDesc = isset($result_obj['resp']['respDesc']) ? $result_obj['resp']['respDesc'] : '';
        
        echo "响应代码: $respCode\n";
        echo "响应描述: $respDesc\n";
        
        if ($respCode == '000000') {
            echo "✅ 短信发送成功！\n";
        } else {
            echo "❌ 短信发送失败！\n";
            echo "错误代码: $respCode\n";
            echo "错误描述: $respDesc\n";
        }
    } else {
        echo "❌ 响应格式错误\n";
        echo "原始响应: $result_json\n";
    }
    
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "\n";
}

echo "\n=== 常见错误代码说明 ===\n";
echo "000000: 成功\n";
echo "160001: 账户余额不足\n";
echo "160002: 账户状态异常\n";
echo "160003: 手机号格式错误\n";
echo "160004: 模板不存在\n";
echo "160005: 签名错误\n";
echo "160006: IP白名单限制\n";
echo "160007: 发送频率限制\n";

echo "\n=== 排查建议 ===\n";
echo "1. 检查云之讯账户余额\n";
echo "2. 确认账户状态正常\n";
echo "3. 验证 AccountSID 和 Token\n";
echo "4. 检查短信模板是否审核通过\n";
echo "5. 确认手机号格式正确\n";
echo "6. 检查IP是否在白名单中\n";
echo "7. 避免频繁发送测试\n";

?>

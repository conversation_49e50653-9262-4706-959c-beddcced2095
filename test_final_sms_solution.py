#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终短信解决方案测试脚本
测试阿里云短信接口替换和SSL修复的完整解决方案
"""

import hashlib
import time
import requests
import json
import urllib3
import ssl

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FinalSMSSolutionTester:
    def __init__(self, base_url='https://lhmj.tuo3.com.cn'):
        self.base_url = base_url
        self.channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
        self.session = self.create_session_with_ssl_fix()
        
    def create_session_with_ssl_fix(self):
        """创建带SSL修复的会话"""
        session = requests.Session()
        
        # SSL修复配置
        session.verify = False  # 禁用SSL验证
        
        # 创建自定义SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # 配置适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
        
    def generate_sign(self, params, channel_key):
        """生成签名"""
        sorted_keys = sorted(params.keys())
        param_str = ''.join(str(params[key]) for key in sorted_keys)
        sign_str = param_str + channel_key
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    
    def test_ssl_connectivity(self):
        """测试SSL连接修复效果"""
        print(f"\n=== 测试SSL连接修复效果 ===")
        
        test_urls = [
            f'{self.base_url}/',
            f'{self.base_url}/admin/',
            f'{self.base_url}/admin/api/',
        ]
        
        ssl_results = {}
        
        for url in test_urls:
            print(f"测试: {url}")
            try:
                start_time = time.time()
                response = self.session.get(url, timeout=10)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                
                print(f"  ✅ 连接成功")
                print(f"  状态码: {response.status_code}")
                print(f"  响应时间: {response_time:.2f}ms")
                
                ssl_results[url] = True
                
            except Exception as e:
                print(f"  ❌ 连接失败: {e}")
                ssl_results[url] = False
        
        return ssl_results
    
    def test_ali_sms_verify_code_with_ssl_fix(self, phone='13067894828'):
        """测试带SSL修复的阿里云短信验证码"""
        print(f"\n=== 测试带SSL修复的阿里云短信验证码 ===")
        print(f"手机号: {phone}")
        
        # 基础参数
        params = {
            'uid': '123456',
            'phone': phone,
            'type': 'login',
            'uuid': 'TEST_DEVICE_ID',
            'timestamp': str(int(time.time() * 1000)),
            'channel': '50010001',
            'c_version': '10',
            'res_version': '1',
        }
        
        # 生成签名
        sign = self.generate_sign(params, self.channel_key)
        params['sign'] = sign
        
        # 请求URL
        url = f'{self.base_url}/admin/api/v1/user/get_verify_code'
        
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        
        try:
            # 使用修复后的会话发送请求
            start_time = time.time()
            response = self.session.post(url, data=params, timeout=30)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            print(f"状态码: {response.status_code}")
            print(f"响应时间: {response_time:.2f}ms")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"JSON数据: {json_data}")
                    
                    if json_data.get('code') == 0:
                        print("✅ SSL修复后，阿里云短信验证码发送成功！")
                        return True, json_data
                    else:
                        print(f"❌ 短信发送失败: {json_data.get('msg', '未知错误')}")
                        return False, json_data
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式")
                    return False, {'error': 'Invalid JSON response'}
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False, {'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL错误仍然存在: {e}")
            return False, {'error': f'SSL Error: {e}'}
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return False, {'error': str(e)}
    
    def test_http_fallback(self, phone='13067894828'):
        """测试HTTP回退方案"""
        print(f"\n=== 测试HTTP回退方案 ===")
        
        # 使用HTTP而不是HTTPS
        http_url = self.base_url.replace('https://', 'http://')
        
        params = {
            'uid': '123456',
            'phone': phone,
            'type': 'login',
            'uuid': 'TEST_DEVICE_ID',
            'timestamp': str(int(time.time() * 1000)),
            'channel': '50010001',
            'c_version': '10',
            'res_version': '1',
        }
        
        sign = self.generate_sign(params, self.channel_key)
        params['sign'] = sign
        
        url = f'{http_url}/admin/api/v1/user/get_verify_code'
        
        print(f"HTTP回退URL: {url}")
        
        try:
            response = self.session.post(url, data=params, timeout=30)
            
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    if json_data.get('code') == 0:
                        print("✅ HTTP回退方案成功！")
                        return True, json_data
                    else:
                        print(f"❌ HTTP回退失败: {json_data.get('msg')}")
                        return False, json_data
                except json.JSONDecodeError:
                    print("❌ HTTP回退响应格式错误")
                    return False, {'error': 'Invalid JSON'}
            else:
                print(f"❌ HTTP回退失败，状态码: {response.status_code}")
                return False, {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ HTTP回退异常: {e}")
            return False, {'error': str(e)}
    
    def generate_deployment_report(self, ssl_results, sms_result, http_result):
        """生成部署报告"""
        print(f"\n" + "="*60)
        print(f"🎯 最终短信解决方案部署报告")
        print("="*60)
        
        # SSL连接测试结果
        print(f"\n📡 SSL连接测试:")
        ssl_success_count = sum(ssl_results.values())
        ssl_total_count = len(ssl_results)
        
        for url, success in ssl_results.items():
            status = "✅ 正常" if success else "❌ 失败"
            print(f"  {url}: {status}")
        
        print(f"  SSL连接成功率: {ssl_success_count}/{ssl_total_count}")
        
        # 短信功能测试结果
        print(f"\n📱 短信功能测试:")
        sms_status = "✅ 成功" if sms_result[0] else "❌ 失败"
        print(f"  阿里云短信验证码: {sms_status}")
        
        http_status = "✅ 成功" if http_result[0] else "❌ 失败"
        print(f"  HTTP回退方案: {http_status}")
        
        # 总体评估
        print(f"\n🎯 总体评估:")
        
        if sms_result[0]:
            print(f"  ✅ 主要方案成功 - SSL修复有效，阿里云短信正常工作")
            print(f"  📋 建议: 配置真实的阿里云AccessKey参数")
            print(f"  🚀 状态: 可以正式部署")
        elif http_result[0]:
            print(f"  ⚠️ 备用方案成功 - HTTP回退可用")
            print(f"  📋 建议: 修复SSL证书问题，然后切换回HTTPS")
            print(f"  🔧 状态: 临时可用，需要进一步修复")
        else:
            print(f"  ❌ 所有方案失败 - 需要深入排查")
            print(f"  📋 建议: 检查服务器配置和网络连接")
            print(f"  🆘 状态: 需要技术支持")
        
        # 下一步行动计划
        print(f"\n📋 下一步行动计划:")
        
        if sms_result[0]:
            print(f"  1. 🔑 配置真实的阿里云AccessKey ID和Secret")
            print(f"  2. 📝 更新短信签名和模板（如需要）")
            print(f"  3. 🧪 在生产环境中进行小规模测试")
            print(f"  4. 📊 监控短信发送成功率和成本")
            print(f"  5. 🔒 在生产环境中启用SSL验证")
        else:
            print(f"  1. 🔧 修复服务器SSL证书配置")
            print(f"  2. 🌐 检查防火墙和网络设置")
            print(f"  3. 📞 联系服务器运维人员")
            print(f"  4. 🔄 考虑使用其他短信服务商")
        
        return {
            'ssl_success_rate': ssl_success_count / ssl_total_count,
            'sms_success': sms_result[0],
            'http_fallback_success': http_result[0],
            'overall_success': sms_result[0] or http_result[0]
        }

def run_final_solution_test():
    """运行最终解决方案测试"""
    print("🎯 开始最终短信解决方案测试...")
    print("="*60)
    
    # 创建测试器
    tester = FinalSMSSolutionTester()
    
    # 1. 测试SSL连接修复效果
    ssl_results = tester.test_ssl_connectivity()
    
    # 2. 测试带SSL修复的阿里云短信
    sms_result = tester.test_ali_sms_verify_code_with_ssl_fix()
    
    # 3. 测试HTTP回退方案
    http_result = tester.test_http_fallback()
    
    # 4. 生成部署报告
    report = tester.generate_deployment_report(ssl_results, sms_result, http_result)
    
    # 5. 最终建议
    print(f"\n🎉 测试完成！")
    
    if report['overall_success']:
        print(f"✅ 短信服务解决方案可用！")
        if report['sms_success']:
            print(f"🚀 推荐使用: HTTPS + 阿里云短信（主要方案）")
        else:
            print(f"🔧 推荐使用: HTTP + 阿里云短信（临时方案）")
    else:
        print(f"❌ 需要进一步排查和修复")
    
    return report

if __name__ == '__main__':
    run_final_solution_test()

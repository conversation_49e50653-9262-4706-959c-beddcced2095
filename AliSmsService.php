<?php
/**
 * 阿里云短信服务类
 * 用于替换云之讯短信接口
 */

class AliSmsService {
    private $accessKeyId;
    private $accessKeySecret;
    private $signName;
    private $templateCode;
    private $endpoint = 'dysmsapi.aliyuncs.com';
    
    public function __construct($config) {
        $this->accessKeyId = $config['accessKeyId'];
        $this->accessKeySecret = $config['accessKeySecret'];
        $this->signName = $config['signName'];
        $this->templateCode = $config['templateCode'];
    }
    
    /**
     * 发送短信验证码
     * @param string $phone 手机号
     * @param string $code 验证码
     * @return array 发送结果
     */
    public function sendVerifyCode($phone, $code) {
        try {
            // 构建请求参数
            $params = [
                'PhoneNumbers' => $phone,
                'SignName' => $this->signName,
                'TemplateCode' => $this->templateCode,
                'TemplateParam' => json_encode(['code' => $code]),
                'Action' => 'SendSms',
                'Version' => '2017-05-25',
                'RegionId' => 'cn-hangzhou',
                'Format' => 'JSON',
                'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
                'SignatureMethod' => 'HMAC-SHA1',
                'SignatureVersion' => '1.0',
                'SignatureNonce' => uniqid(),
                'AccessKeyId' => $this->accessKeyId
            ];
            
            // 生成签名
            $signature = $this->generateSignature($params, 'POST');
            $params['Signature'] = $signature;
            
            // 发送HTTP请求
            $result = $this->sendHttpRequest($params);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'code' => 'EXCEPTION',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 生成阿里云API签名
     */
    private function generateSignature($params, $method) {
        // 排序参数
        ksort($params);
        
        // 构建查询字符串
        $queryString = '';
        foreach ($params as $key => $value) {
            if ($queryString) {
                $queryString .= '&';
            }
            $queryString .= $this->percentEncode($key) . '=' . $this->percentEncode($value);
        }
        
        // 构建待签名字符串
        $stringToSign = $method . '&' . $this->percentEncode('/') . '&' . $this->percentEncode($queryString);
        
        // 计算签名
        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->accessKeySecret . '&', true));
        
        return $signature;
    }
    
    /**
     * URL编码
     */
    private function percentEncode($str) {
        $res = urlencode($str);
        $res = str_replace(['+', '*', '%7E'], ['%20', '%2A', '~'], $res);
        return $res;
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendHttpRequest($params) {
        $url = 'https://' . $this->endpoint . '/';
        
        // 构建POST数据
        $postData = http_build_query($params);
        
        // 初始化CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        // SSL配置修复 - 解决SSL连接问题
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  // 禁用SSL证书验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // 禁用主机名验证
        curl_setopt($ch, CURLOPT_SSLVERSION, CURL_SSLVERSION_DEFAULT);  // 使用默认SSL版本
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);   // 跟随重定向
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);           // 最大重定向次数

        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'User-Agent: LHMJ-AliSMS/1.0'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'code' => 'CURL_ERROR',
                'message' => $error
            ];
        }
        
        if ($httpCode !== 200) {
            return [
                'success' => false,
                'code' => 'HTTP_ERROR',
                'message' => "HTTP状态码: $httpCode"
            ];
        }
        
        // 解析响应
        $result = json_decode($response, true);
        
        if (!$result) {
            return [
                'success' => false,
                'code' => 'JSON_ERROR',
                'message' => '响应解析失败'
            ];
        }
        
        // 检查阿里云API响应
        if (isset($result['Code']) && $result['Code'] === 'OK') {
            return [
                'success' => true,
                'code' => 'OK',
                'message' => '发送成功',
                'bizId' => $result['BizId'] ?? '',
                'requestId' => $result['RequestId'] ?? ''
            ];
        } else {
            return [
                'success' => false,
                'code' => $result['Code'] ?? 'UNKNOWN',
                'message' => $result['Message'] ?? '未知错误'
            ];
        }
    }
}

/**
 * 简化版阿里云短信服务（不依赖SDK）
 */
class SimpleAliSms {
    private $accessKeyId;
    private $accessKeySecret;
    private $signName;
    private $templateCode;
    
    public function __construct($accessKeyId, $accessKeySecret, $signName, $templateCode) {
        $this->accessKeyId = $accessKeyId;
        $this->accessKeySecret = $accessKeySecret;
        $this->signName = $signName;
        $this->templateCode = $templateCode;
    }
    
    /**
     * 发送短信（兼容云之讯接口格式）
     */
    public function templateSMS($appId, $phone, $templateId, $code) {
        $aliSms = new AliSmsService([
            'accessKeyId' => $this->accessKeyId,
            'accessKeySecret' => $this->accessKeySecret,
            'signName' => $this->signName,
            'templateCode' => $this->templateCode
        ]);
        
        $result = $aliSms->sendVerifyCode($phone, $code);
        
        // 转换为云之讯格式的响应
        if ($result['success']) {
            return json_encode([
                'resp' => [
                    'respCode' => '000000',
                    'respDesc' => '成功'
                ],
                'smsid' => $result['bizId'] ?? '',
                'requestId' => $result['requestId'] ?? ''
            ]);
        } else {
            return json_encode([
                'resp' => [
                    'respCode' => $result['code'],
                    'respDesc' => $result['message']
                ]
            ]);
        }
    }
}

?>

<!-- 
阿里云短信配置说明:

1. 申请阿里云短信服务:
   - 登录阿里云控制台 https://ecs.console.aliyun.com/
   - 搜索"短信服务"，开通短信服务
   - 创建签名和模板
   - 获取 AccessKey ID 和 AccessKey Secret

2. 配置参数:
   - AccessKey ID: 访问密钥ID
   - AccessKey Secret: 访问密钥Secret
   - SignName: 短信签名（如"快马互娱"）
   - TemplateCode: 短信模板代码（如"SMS_229700066"）

3. 模板格式:
   - 验证码模板示例: "您的验证码是${code}，请在10分钟内使用。"
   - 参数格式: {"code": "1234"}

4. 常见错误代码:
   - OK: 发送成功
   - isv.BUSINESS_LIMIT_CONTROL: 业务限流
   - isv.INVALID_PARAMETERS: 参数异常
   - isv.MOBILE_NUMBER_ILLEGAL: 手机号码格式错误
   - isv.TEMPLATE_MISSING_PARAMETERS: 模板缺少变量
-->

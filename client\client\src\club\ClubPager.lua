-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部翻页处理类
--  @date 2018-01-23
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')


local ClubPager = class("ClubPager")


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubPager:ctor(node, key_id, cmd_page)
    self.m_key_id = key_id
    self.m_cmd = cmd_page
    self.m_first_index = 0
    self.m_last_index = 0
    self.m_last_pos = 0 -- 上一次发送命令位置

    local btn_left, btn_right, label_page = node:child('btn_left, btn_right, label_page')
    btn_left.is_left = true
    btn_left:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnPage) )
    btn_right:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnPage) )

    btn_left:hide()
    btn_left:hide()

    self.btn_left = btn_left
    self.btn_right = btn_right
    self.label_page = label_page
end


-------------------------------------------------------------------------------
-- 更新
-------------------------------------------------------------------------------
function ClubPager:updateParams(total, first_index, last_index)
    self.m_first_index = first_index
    self.m_last_index = last_index
    local page_num = math.ceil(total / ClubUtil.PER_PAGE)
    local page_now = math.ceil(first_index / ClubUtil.PER_PAGE)
    self.label_page:setString(string.format('%d/%d', page_now, page_num))
    if page_num <= 1 then
        self.btn_left:hide()
        self.btn_right:hide()
    else
        self.btn_left:setVisible(page_now > 1)
        self.btn_right:setVisible(page_now < page_num)
    end
end


-------------------------------------------------------------------------------
-- 翻页按钮点击
-------------------------------------------------------------------------------
function ClubPager:onBtnPage(sender)
    self:stepPage(sender.is_left and -1 or 1)
end


-------------------------------------------------------------------------------
-- 往前或往后
-------------------------------------------------------------------------------
function ClubPager:stepPage(offset)
    local index = self.m_last_pos
    if offset ~= 0 then
        if offset == -1 then
            index = math.max(0, self.m_first_index - ClubUtil.PER_PAGE - 1)
        else
            index = self.m_last_index
        end
    end
    self:sendCmd(index)
end


-------------------------------------------------------------------------------
-- 发送列表查询
-------------------------------------------------------------------------------
function ClubPager:sendCmd(pos)
    self.m_last_pos = pos
    local values = {dwID=self.m_key_id, nValue=pos, nValue2=0}
    ClubUtil.send(self.m_cmd, cmd.CMD_GR_IDValue, values)
end


return ClubPager
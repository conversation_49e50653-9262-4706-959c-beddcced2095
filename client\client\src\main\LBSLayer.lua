-------------------------------------------------------------------------------
--  创世版1.0
--  定位
--  @date 2017-09-02
--  @auth woodoo
-------------------------------------------------------------------------------
local LBSLayer = class("LBSLayer", cc.Layer)
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
-------------------------------------------------------------------------------
-- 构造方法
--  player_num: 玩家人数 
-------------------------------------------------------------------------------
function LBSLayer:ctor( player_num )
    print('LBSLayer:ctor...', player_num)
    self:enableNodeEvents()
    -- 载入主UI
    local main_node = helper.app.loadCSB('LBSLayer.csb')
    self.main_node = main_node
    self.player_num = player_num
    self:addChild(main_node)
    self.userItemList = {}
    self.playerStausList = {}
    --位置记录
    self.lbsMakMap = {}
    -- 初始化 状态
    for viewID = 1, cmd.GAME_PLAYER do
        local status = {}
        self.playerStausList[viewID] = status
    end
    self:initPlayerIPStatus()
    self:initPlayerLocationStatus()
    self.cur_player_num = 0
    self:initUI()
    self.messageCD = 0
end

--
function LBSLayer:initUI()
    local img_bg = self.main_node:child('img_bg')
    self.player_panel = img_bg:child('Panel_player')
    
    img_bg:child('img_close'):addTouchEventListener( helper.app.commClickHandler(self, self.onPopClose) )
    self:initPlayerIPStatus()
    self:initPlayerLocationStatus()
    self:initPlayerHead()
    --print('LBSLayer:initUI...........刷新定位界面.....')
end

-- 玩家进入
function LBSLayer:userItemEnter( viewID, userItem )
    --[[
    userItemList[viewID] = userItem
    self:userNumUpdate()
    self:userDisUpdate( viewID )
    --]]
    self:initUI()
end

-- 玩家退出
function LBSLayer:userItemExit( viewID )
    --[[
    userItemList[viewID] = nil
    self:userNumUpdate()
    self:userDisUpdate( viewID )
    --]]
    self:initUI()
end

-- 刷新玩家数量
function LBSLayer:userNumUpdate()
    local num = 0
    for k, userItem in pairs( self.userItemList ) do
        if userItem ~= nil then
            num = num + 1 
        end
    end
    self.cur_player_num = num
end

-- 刷新玩家位置
function LBSLayer:userDisUpdate( viewID )
   local game_layer = helper.app.getFromScene('game_room_layer')
   local curUserItem = game_layer:getUserInfoByViewID(viewID)
   local isLbsOK = true
   local icon_bg = self.player_panel:child('lbs_icon_bg_'..viewID)
   for j = 1, self.player_num do
        local compareViewID = game_layer:SwitchViewChairID(j - 1)
        local compareUserItem = game_layer:getUserInfoByViewID(compareViewID)
        if compareViewID ~= viewID then
            local dis, isShow, isOK = self:caculateDis(curUserItem, compareUserItem)
            self:initPlayerDistance(viewID, compareViewID, isShow, dis)
            if not isOK then
                isLbsOK = false
            end
        end    
   end
   if curUserItem then
      local icon_status = icon_bg:child('icon_status'):show()
      if curUserItem.locationData.location_status <= 0 then
        icon_status:texture('room/warnning_icon_status_3.png')
        self.playerStausList[viewID].dis = yl.WarningTypes.LOCATION_FAIL
      else 
        icon_status:texture('room/warnning_icon_status_0.png')
      end
      if not isLbsOK then
        self.playerStausList[viewID].dis = yl.WarningTypes.LOCATION_DIS
      end
   else
      local icon_status = icon_bg:child('icon_status'):hide()
   end
end
-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function LBSLayer:onExit()
    print('LBSLayer:onExit...')
end

-------------------------------------------------------------------------------
-- 初始化玩家距离
-------------------------------------------------------------------------------
function LBSLayer:initPlayerDistance(mainPlayerNo1, comparePlayerNo2, isShow, dis)
    local lbs_icon_bg = self.player_panel:child('lbs_icon_bg_'..mainPlayerNo1) 
    local name = ''
    if mainPlayerNo1 < comparePlayerNo2 then
        name = 'label_dis_' .. mainPlayerNo1 .. '_' .. comparePlayerNo2
    else
        name = 'label_dis_' .. comparePlayerNo2 .. '_' .. mainPlayerNo1
    end
    local label_dis = self.player_panel:child(name)
    if label_dis then
        label_dis:setVisible(isShow)
        label_dis:setString(dis..'m')
    end
end

-- 计算距离
function LBSLayer:caculateDis(userItem1, userItem2)
    local MultiPlatform = appdf.req(appdf.EXTERNAL_SRC .. "MultiPlatform")
    local isOK = false
    local isShow = false
    local dis = 0
    if userItem1 and userItem2 and userItem1.locationData.location_status > 0 and userItem2.locationData.location_status > 0 then
        local  locationData1 = self.lbsMakMap[userItem1.dwUserID] 
        local  locationData2 = self.lbsMakMap[userItem2.dwUserID]
        local  isNeedCheckDis = false
        -- 是否需要重新检查距离 优化 计算频率
        if locationData1 then
            if locationData1.latitude ~= userItem1.locationData.latitude
                or locationData1.longitude ~= userItem1.locationData.longitude then
                isNeedCheckDis = true
            end
        else
            self.lbsMakMap[userItem1.dwUserID] = userItem1.locationData
            isNeedCheckDis = true
        end 
        if locationData2 then
            if locationData2.latitude ~= userItem2.locationData.latitude
                or locationData2.longitude ~= userItem2.locationData.longitude then
                isNeedCheckDis = true
            end
        else
            self.lbsMakMap[userItem2.dwUserID] = userItem2.locationData
            isNeedCheckDis = true
        end
        if not isNeedCheckDis then
            local key = userItem1.dwUserID ..'-'.. userItem2.dwUserID
            dis = self.lbsMakMap[key]
        end
        if isNeedCheckDis or not dis then
            local l = 
            {
    	        myLatitude = userItem1.locationData.latitude, 
		        myLongitude = userItem1.locationData.longitude,
		        otherLatitude = userItem2.locationData.latitude, 
		        otherLongitude = userItem2.locationData.longitude, 
	        }
            dis = tonumber(MultiPlatform:getInstance():metersBetweenLocation(l))
            dis = helper.math.getIntPart( dis )
            local key1 = userItem1.dwUserID ..'-'.. userItem2.dwUserID
            local key2 = userItem2.dwUserID ..'-'.. userItem1.dwUserID
            self.lbsMakMap[key1] = dis
            self.lbsMakMap[key2] = dis
        end 
        print('metersBetweenLocation dis................', dis)
        isShow = true
    end
    if (dis and dis > 50) or userItem1 == nil or userItem2 == nil or userItem1.locationData.location_status <= 0 or userItem2.locationData.location_status <= 0 then
        isOK = true
    end
    return dis, isShow, isOK
end

-------------------------------------------------------------------------------
-- 初始化玩家头像
-------------------------------------------------------------------------------
function LBSLayer:initPlayerHead()
    local game_layer = helper.app.getFromScene('game_room_layer')
    if game_layer then
        self:initPlayers()
        for i = 1, self.player_num do
            local chairID = i - 1
            local viewID = game_layer:SwitchViewChairID(chairID)
            local userItem = game_layer:getUserInfoByViewID(viewID)
            local icon_bg = self.player_panel:child('lbs_icon_bg_'..viewID)
            if icon_bg then
                local icon_status = icon_bg:child('icon_status'):hide():zorder(10)
                local label_ip = icon_bg:child('label_ip'):hide()
                local label_name = icon_bg:child('label_name'):hide()
                self:userDisUpdate( viewID )
                icon_bg:removeChildByName('sp_head')
                if userItem and userItem.szIpAddress then
                    local head = PopupHead:create(self, userItem, 90, 90)
                    head:pos(icon_bg:getContentSize().width/2, icon_bg:getContentSize().height/2)
			        head:setName('sp_head')
			        head:addTo( icon_bg )
                    if userItem.szIpAddress then
                        local isSameIP, sameips = self:checkSameIP(viewID, userItem.szIpAddress)
                        label_ip:show():setString(string.format('IP: %s', userItem.szIpAddress))
                        if isSameIP then
                            label_ip:setTextColor(display.COLOR_RED)
                        else
                            label_ip:setTextColor(display.COLOR_GREEN)
                        end
                    end
                    label_name:show():setString(userItem.szNickName)
                end
            end
        end
    end
end

-------------------------------------------------------------------------------
-- 蒙版点击
-------------------------------------------------------------------------------
function LBSLayer:onPopClose()
    print('LBSLayer:onPopClose')
    local this = self
    self.main_node:child('img_bg'):stop():runMyAction( cc.Sequence:create(
        cc.EaseBackIn:create( cc.ScaleTo:create(0.2, 0) ),
        cc.CallFunc:create( function() this:hide() end )
    ) )
end


-------------------------------------------------------------------------------
-- 效果显示
-------------------------------------------------------------------------------
function LBSLayer:effectShow()
    self:initUI()
    self:show()
    self.main_node:child('img_bg'):stop():scale(0):runMyAction( cc.Sequence:create(
        cc.EaseBackOut:create( cc.ScaleTo:create(0.2, 1) )
    ) )
    if self:child('mask') then
        self:child('mask'):effectShow()
    end
end

-- 是否需要警告
function LBSLayer:isNeedWarning( isWarning )
   local isNeedWarning = yl.WarningTypes.NULL
   local game_layer = helper.app.getFromScene('game_room_layer')
   if not game_layer then
        return
   end
   local msgData = {} 
   msgData.player = {}
   msgData.player[yl.WarningTypes.IP_SAME] = {}
   msgData.player[yl.WarningTypes.LOCATION_DIS] = {}
   msgData.player[yl.WarningTypes.LOCATION_FAIL] = {}
   msgData.status = yl.WarningTypes.NULL
   local num = 0
   -- dump(self.playerStausList, 'self.playerStausList', 9)
   for viewID = 1, cmd.GAME_PLAYER do
        local userItem = game_layer:getUserInfoByViewID(viewID)
        local status = self.playerStausList[viewID]
        if status.dis > isNeedWarning then
            isNeedWarning = status.dis
        end
        if status.ip > isNeedWarning then
            isNeedWarning = status.ip
        end
        if userItem then
            num = num + 1
            if status.dis > yl.WarningTypes.NULL then
                table.insert(msgData.player[status.dis], userItem.szNickName)
            end
            if status.ip > yl.WarningTypes.NULL then
                table.insert(msgData.player[status.ip], userItem.szNickName)
            end
        end
   end
   local isFull = false
   --print( '人满再提示', num, self.player_num )
   if num >= self.player_num then
       isFull = true
   end
   -- 人满再提示
   local cur_time = yl.time()
   local dleta_time = cur_time - self.messageCD 
   --print( '人满再时间',self.messageCD, cur_time, dleta_time )
   if isWarning and isFull and dleta_time > 1.0 then
       msgData.status = isNeedWarning
       local msg = {}
       msg[yl.WarningTypes.IP_SAME] = LANG.WARNING_IP_SAME
       msg[yl.WarningTypes.LOCATION_DIS] = LANG.WARNING_LOCATION_DIS
       msg[yl.WarningTypes.LOCATION_FAIL] = LANG.WARNING_LOCATION_FAIL
       if isNeedWarning > 0 then
          local str = ''
          for k, v in pairs(msgData.player[isNeedWarning]) do
              str = str..'      ' ..v
          end
          str = str .. msg[isNeedWarning]
          helper.pop.message( str )
          self.messageCD = cur_time
       end
   end
   return isNeedWarning
end

-------------------------------------------------------------------------------
-- 相同IP检查
--  @overide 父类GameModel调用
-------------------------------------------------------------------------------
function LBSLayer:checkSameIP(curViewID, curIpAddress)
    local game_layer = helper.app.getFromScene('game_room_layer')
    if not game_layer then
        return
    end
    -- 汇总ip信息
    local sameips = {}
    local isSame = false
	for i = 1, self.player_num do
        local viewID = game_layer:SwitchViewChairID(i - 1)
        if curViewID ~= viewID then
            local user_item = game_layer:getUserInfoByViewID(viewID)
            if user_item and user_item.szIpAddress and user_item.szIpAddress == curIpAddress then
               table.insert(sameips, viewID)
               isSame = true
            end
        end
    end
    if isSame then
        self.playerStausList[curViewID].ip = yl.WarningTypes.IP_SAME
    else
        self.playerStausList[curViewID].ip = yl.WarningTypes.NULL
    end
    return isSame, sameips
end

-- 初始化 玩家的 ip状态
function LBSLayer:initPlayerIPStatus()
    for viewID = 1, cmd.GAME_PLAYER do
        local status = self.playerStausList[viewID]
        status.ip = yl.WarningTypes.NULL
    end
end

-- 初始化 玩家的 定位状态
function LBSLayer:initPlayerLocationStatus()
    for viewID = 1, cmd.GAME_PLAYER do
        local status = self.playerStausList[viewID]
        status.dis = yl.WarningTypes.NULL
    end
end

-- 初始化玩家
function LBSLayer:initPlayers()
    print('self.player_num', self.player_num)
    -- 房间人数对应的座位显示
    local player_visibles = {true, true, true, true}
    local renshu = self.player_num
    if renshu == 3 then
        if cs.game[GlobalUserItem.nCurGameKind].FIX3 ~= false then  -- true or nil
            player_visibles[1] = false
        else
            local game_layer = helper.app.getFromScene('game_room_layer')
            local chair_id = game_layer:GetMeChairID()
            if chair_id == 0 then
                player_visibles[2] = false
            elseif chair_id == 1 then
                player_visibles[1] = false
            elseif chair_id == 2 then
                player_visibles[4] = false
            end
        end
    elseif renshu == 2 then
        player_visibles[2] = false
        player_visibles[4] = false
    end
    for i = 1, cmd.GAME_PLAYER do
        local icon_bg = self.player_panel:child('lbs_icon_bg_'..i)
        icon_bg:show()
        for j = 1, cmd.GAME_PLAYER do
            if j ~= i then
               local min = math.min(i, j)
               local max = math.max(i, j)
               self.player_panel:child('img_line_'.. min ..'_'.. max):show()
               self.player_panel:child('label_dis_'.. min ..'_'.. max):show()
            end     
        end
	end
    for i = 1, cmd.GAME_PLAYER do
        local icon_bg = self.player_panel:child('lbs_icon_bg_'..i)
        if not player_visibles[i] then
           icon_bg:hide()
           for j = 1, cmd.GAME_PLAYER do
               if j ~= i then
                  local min = math.min(i, j)
                  local max = math.max(i, j)
                  self.player_panel:child('img_line_'.. min ..'_'.. max):hide()
                  self.player_panel:child('label_dis_'.. min ..'_'.. max):hide()
               end     
           end
        end
	end
end

return LBSLayer
-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 模块基类
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbNet = cs.app.client('orb.OrbNet')
local OrbUtil = cs.app.client('orb.OrbUtil')


local TAG_TIMEDOW = 1122 -- 倒计时标记


local OrbBase = class('OrbBase')


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbBase:ctor(panel)
    self.m_panel = panel

    local btn_close = panel:child('btn_close')
    if btn_close then
        btn_close:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnClose) )
    end
    local btn_share = panel:child('btn_share')
    if btn_share then
        btn_share:addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    end
end


-------------------------------------------------------------------------------
-- 显示
-------------------------------------------------------------------------------
function OrbBase:onShow(params)
end


-------------------------------------------------------------------------------
-- 关闭按钮点击
-------------------------------------------------------------------------------
function OrbBase:onBtnClose()
    self:close()
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function OrbBase:onBtnShare()
    self:share()
end


-------------------------------------------------------------------------------
-- 关闭模块
-------------------------------------------------------------------------------
function OrbBase:closeTop()
    self.m_panel:getParent():getParent():removeFromParent()
end


-------------------------------------------------------------------------------
-- 关闭自己
-------------------------------------------------------------------------------
function OrbBase:close()
    self.m_panel:stop(TAG_TIMEDOW)
    OrbUtil.close(self.m_panel)
end


-------------------------------------------------------------------------------
-- 打开面板
-------------------------------------------------------------------------------
function OrbBase:open(panel_name, params)
    OrbUtil.open(panel_name, params)
end


-------------------------------------------------------------------------------
-- 更新面板
-------------------------------------------------------------------------------
function OrbBase:update(panel_name)
    OrbUtil.update(panel_name)
end


-------------------------------------------------------------------------------
-- 分享
-------------------------------------------------------------------------------
function OrbBase:share(callback, no_result)
    OrbUtil.share(callback, no_result)
end


-------------------------------------------------------------------------------
-- 创建头像
-------------------------------------------------------------------------------
function OrbBase:createHead(user_id, url, width, png)
    return OrbUtil.createHead(user_id, url, width, png)
end


-------------------------------------------------------------------------------
-- 请求
-------------------------------------------------------------------------------
function OrbBase:request(url, params, callback)
    OrbNet.request(url, params, callback)
end


-------------------------------------------------------------------------------
-- 活动id
-------------------------------------------------------------------------------
function OrbBase:getMainId()
    return OrbUtil.getMainData().id
end


-------------------------------------------------------------------------------
-- 开始倒计时
-------------------------------------------------------------------------------
function OrbBase:getAmount()
    return OrbUtil.getMainData().balance
end


-------------------------------------------------------------------------------
-- 开始倒计时
-------------------------------------------------------------------------------
function OrbBase:getOffset()
    return math.max(0, 15 - OrbUtil.getMainData().balance)
end


-------------------------------------------------------------------------------
-- 是否可提现
-------------------------------------------------------------------------------
function OrbBase:canDraw()
    return self:getAmount() >= 15
end


-------------------------------------------------------------------------------
-- 拆红包记录
-------------------------------------------------------------------------------
function OrbBase:getOpenLogs()
    return OrbUtil.getMainData().open_logs
end


-------------------------------------------------------------------------------
-- 公告
-------------------------------------------------------------------------------
function OrbBase:getNotices()
    return OrbUtil.getMainData().notices
end


-------------------------------------------------------------------------------
-- 领取记录
-------------------------------------------------------------------------------
function OrbBase:getDrawLogs()
    return OrbUtil.getMainData().withdraw_logs
end


-------------------------------------------------------------------------------
-- 1元状态
-------------------------------------------------------------------------------
function OrbBase:getOneStatus()
    return OrbUtil.getMainData().one_status
end


-------------------------------------------------------------------------------
-- 更新数据
-------------------------------------------------------------------------------
function OrbBase:updateData(param)
    local main_data = OrbUtil.getMainData()
    for k, v in pairs(param) do
        main_data[k] = v
    end
end


-------------------------------------------------------------------------------
-- 新增拆红包记录
-------------------------------------------------------------------------------
function OrbBase:addOpenLog(log)
    local main_data = OrbUtil.getMainData()
    table.insert(main_data.open_logs, log)
end


-------------------------------------------------------------------------------
-- 新增提现记录
-------------------------------------------------------------------------------
function OrbBase:addDrawLog(log)
    local main_data = OrbUtil.getMainData()
    table.insert(main_data.withdraw_logs, 1, log)
end


-------------------------------------------------------------------------------
-- 开始倒计时
-------------------------------------------------------------------------------
function OrbBase:startTimedown()
    self.m_panel:stop(TAG_TIMEDOW)
    local macro = 0
    self.m_panel:perform(function()
        macro = macro + 1
        if macro == 10 then macro = 0 end
        local seconds = math.max(0, OrbUtil.getMainData().end_at - os.time())
        local hour = math.floor(seconds / 3600)
        local minute = math.floor(seconds % 3600 / 60)
        local second = seconds % 60
        self.m_panel:child('label_hour'):setString(string.format('%02d', hour))
        self.m_panel:child('label_minute'):setString(string.format('%02d', minute))
        self.m_panel:child('label_second'):setString(string.format('%02d', second))
        self.m_panel:child('label_macro'):setString(string.format('%02d', macro))
    end, 0.1, -1, TAG_TIMEDOW, true)
end


-------------------------------------------------------------------------------
-- 显示公告
-------------------------------------------------------------------------------
function OrbBase:showNotice(panel_notice, label_notice, lang_key)
    local notices = self:getNotices()
    if not notices or #notices == 0 then return end

    local index = (self.m_notice_index or 0) + 1
    if index > #notices then index = 1 end
    self.m_notice_index = index
    local notice = notices[index]

    local panel_size = panel_notice:size()

    local label = label_notice:clone():show()
    label:setString( LANG{lang_key, name=notice.nickname, amount=notice.money} )
    label:pos(0, -label:size().height/2)
    label:addTo(panel_notice)

    label:runAction(cc.Sequence:create(
        cc.MoveTo:create(0.1, cc.p(0, panel_size.height/2)),
        cc.DelayTime:create(3),
        cc.MoveTo:create(0.1, cc.p(0, panel_size.height + label:size().height/2)),
        cc.CallFunc:create(function()
            self:showNotice(panel_notice, label_notice, lang_key)
        end),
        cc.RemoveSelf:create(true)
    ))
end


return OrbBase
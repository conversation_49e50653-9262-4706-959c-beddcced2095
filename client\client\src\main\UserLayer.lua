-------------------------------------------------------------------------------
--  创世版1.0
--  玩家信息
--  @date 2017-06-22
--  @auth woodoo
-------------------------------------------------------------------------------
local UserLayer = class("UserLayer", cc.Layer)
local HeadSprite = require(appdf.EXTERNAL_SRC .. "HeadSprite")


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function UserLayer:ctor()
    print('UserLayer:ctor...')
    self:enableNodeEvents()

    -- 载入主UI
    local main_node = helper.app.loadCSB('UserLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    -- 不确定是否已绑定手机前都隐藏
    main_node:child('label_mobile'):hide()
    main_node:child('btn_change_mobile'):hide().is_change = true
    main_node:child('btn_cert'):hide()

    -- 确定和关闭按钮，简易关闭
    main_node:child('btn_close'):addTouchEventListener( helper.app.commCloseHandler(self) )
    main_node:child('btn_cert'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCert) )
    main_node:child('btn_change_mobile'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnCert) )

    self:initInfo()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function UserLayer:onExit()
    print('UserLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 显示信息
-------------------------------------------------------------------------------
function UserLayer:initInfo()
    local main_node = self.main_node

    -- 头像
    local panel = main_node:child('panel_avator')
    panel:removeAllChildren()
    local size = panel:size()
	local head = HeadSprite:createNormal(GlobalUserItem, 132)
    head:pos(size.width/2, size.height/2):addTo(panel)

    -- 昵称
    main_node:child('label_name'):setString( GlobalUserItem.szNickName )

    -- ID
    main_node:child('label_id'):setString( LANG{'ID_STR', id=helper.str.formatUserID(GlobalUserItem.dwUserID)} )

    -- IP
    main_node:child('label_ip'):setString( LANG{'IP_STR', ip=GlobalUserItem.szIpAdress} )

    -- 推荐码
    if GlobalUserItem.dwSpreader == 0 then
        main_node:child('label_bind'):hide()
    else
        main_node:child('label_bind'):setString( LANG{'RECOMMEND_CODE', code=string.format('%06d', GlobalUserItem.dwSpreader)} )
    end

    -- 手机绑定
    local mobile = GlobalUserItem.szMobilePhone
    if mobile and mobile:trim() ~= '' then
        mobile = mobile:trim()
        if #mobile > 7 then
            mobile = mobile:sub(1, 3) .. ('*'):rep(#mobile - 7) .. mobile:sub(-4)
        end
        main_node:child('label_mobile'):show():setString(LANG{'MOBILE_BIND_SHOW', value=mobile})
        main_node:child('btn_change_mobile'):show()
    else
        main_node:child('btn_cert'):show()
    end
end


-------------------------------------------------------------------------------
-- 实名认证按钮点击
-------------------------------------------------------------------------------
function UserLayer:onBtnCert(sender)
    local path = cs.app.CLIENT_SRC .. 'main.MobileBindLayer'
    helper.pop.popLayer(path, nil, nil, nil, true)
    self:removeFromParent()
end


return UserLayer
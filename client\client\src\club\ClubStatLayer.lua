-------------------------------------------------------------------------------
--  创世版3.0
--  俱乐部统计
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')


-- 日期选择列表
local days = {
    {text=LANG{'LAST_DAYS', num=7}, value=-7},
    {text=LANG{'LAST_DAYS', num=15}, value=-15},
}


local ClubStatLayer = class("ClubStatLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubStatLayer:ctor(club)
    print('ClubStatLayer:ctor...')
    self.m_club = club

    local main_node = ClubUtil.initUI(self, 'ClubStatLayer.csb')
    main_node:child('row_template'):hide()

    main_node:child('bg_date'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDrop) )
    main_node:child('btn_drop'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnDrop) )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubStatLayer:onEnter()
    print('ClubStatLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_RECORD_QUERY, self, self.onQueryResp)

    self:onDateChange(days[1])
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubStatLayer:onExit()
    print('ClubStatLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 发送查询
-------------------------------------------------------------------------------
function ClubStatLayer:sendSearch(day_num)
    local values = {dwID=self.m_club.dwClubID, nValue=day_num, nValue2=1}
    ClubUtil.send(cmd.SUB_CLUB_RECORD_QUERY, cmd.CMD_GR_IDValue, values)
end


-------------------------------------------------------------------------------
-- 列表生成
-------------------------------------------------------------------------------
function ClubStatLayer:onQueryResp(data)
    local stats = LiveFrame:getInstance():resp(data, cmd.tagClubRecord, true)
    if not stats then return end

    local listview = self.main_node:child('listview')
    listview:removeAllItems()
    local template = self.main_node:child('row_template')
    for i, stat in ipairs(stats) do
        local item = template:clone():show()

        if i % 2 == 1 then
            item:setBackGroundColorType(ccui.LayoutBackGroundColorType.none)
        end

        item:child('label_date'):setString( ClubUtil.convertTime(stat.sysTime) )
        item:child('label_player'):setString(stat.nPlayers)
        item:child('label_room'):setString(stat.nSetCount)
        item:child('label_game'):setString(stat.nGameCount)
        item:child('label_diamond'):setString(stat.nRoomDiamondCost)
        item:child('label_fangka'):setString(stat.nRoomCardCost)

        listview:pushBackCustomItem(item)
    end

    listview:jumpToTop()
end


-------------------------------------------------------------------------------
-- 日期选择下拉按钮点击
-------------------------------------------------------------------------------
function ClubStatLayer:onBtnDrop(sender)
    local size = cc.size(235, 150)
    local pos = cc.p(169, 490)
    helper.pop.drop(days, size, pos, handler(self, self.onDateChange))
end


-------------------------------------------------------------------------------
-- 日期变化
-------------------------------------------------------------------------------
function ClubStatLayer:onDateChange(value)
    self.main_node:child('label_date'):setString(value.text)
    self:sendSearch(value.value)
end


return ClubStatLayer

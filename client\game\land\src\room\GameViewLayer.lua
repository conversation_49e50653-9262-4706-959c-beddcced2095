--
-- Author: zhong
-- Date: 2016-11-02 17:28:24
--
local ExternalFun = cs.app.client("external.ExternalFun")
--local GameChatLayer = appdf.req(appdf.CLIENT_SRC.."plaza.views.layer.game.GameChatLayer")
local ClipText = cs.app.client("external.ClipText")
local AnimationMgr = cs.app.client('external.AnimationMgr')
local PopupHead = cs.app.client("system.PopupHead")

local cmd = cs.app.game("room.CMD_Game")
local Define = cs.app.game("room.Define")
local GameRoleItem = cs.app.game("room.GameRoleItem")
local CardSprite = cs.app.game("room.gamecard.CardSprite")
local CardsNode = cs.app.game("room.gamecard.CardsNode")
--local GameLogic = cs.app.game("room.GameLogic")

local GameResultLayer = cs.app.game("room.GameResultLayer")
--local SettingLayer = appdf.req(module_pre .. ".views.layer.SettingLayer")

local GameLogic = nil

local TAG_ENUM = Define.TAG_ENUM
local TAG_ZORDER = Define.TAG_ZORDER

local GameViewLayer = class("GameViewLayer",function(scene)
        local gameViewLayer = display.newLayer()
    return gameViewLayer
end)


cs.app.client('system.GameViewLayerEx').assign(GameViewLayer)

function GameViewLayer:ctor(scene, gamelogic)
    --注册node事件
    ExternalFun.registerNodeEvent(self)
    self._scene = scene

    GameLogic = gamelogic
    CardsNode:setGameLogic(gamelogic)
    CardSprite:setGameLogic(gamelogic)
    self.smallCards = {}

    --初始化
    self:paramInit()

    --加载资源
    self:loadResource()
    -- 最后一个出牌的用户
    self.lastOutViewID = cmd.INVALID_VIEWID 
end

function GameViewLayer:paramInit()
    -- 聊天层
    self.m_chatLayer = nil
    -- 结算层
    self.m_resultLayer = nil

    -- 手牌控制
    self.m_cardControl = nil
    -- 手牌数量
    self.m_tabCardCount = {}
    -- 手牌底图
    self.m_tabCardCountBg = {}
    -- 报警动画
    self.m_tabSpAlarm = {}

    -- 叫分text
    self.m_textGameCall = nil
    -- 庄家牌
    self.m_nodeBankerCard = nil
    self.m_tabBankerCard = {}
    -- 准备按钮
    self.m_btnReady = nil
    
    -- 准备标签
    self.m_tabReadySp = {}
    -- 状态标签
    self.m_tabStateSp = {}

    -- 叫分控制
    self.m_callScoreControl = nil
    self.m_nMaxCallScore = 0
    self.m_tabCallScoreBtn = {}

    -- 操作控制
    self.m_onGameControl = nil
    self.m_btnOutCard = nil
    self.m_btnPass = nil
    self.m_bMyCallBanker = false
    self.m_bMyOutCards = false

    -- 出牌控制
    self.m_outCardsControl = nil
    -- 能否出牌
    self.m_bCanOutCard = false

    -- 用户信息
    self.m_userinfoControl = nil
    -- 用户头像
    self.m_tabUserHead = {}
    self.m_tabUserHeadPos = {}
    -- 用户信息
    self.m_tabUserItem = {}
    -- 用户昵称
    self.m_tabCacheUserNick = {}
    -- 用户金币
    self.m_atlasScore = nil
    -- 底分
    self.m_atlasDiFeng = nil
    -- 提示
    self.m_spInfoTip = nil
    -- 一轮提示组合
    self.m_promptIdx = 0
    -- 倒计时
    self.m_spTimer = nil
    -- 倒计时
    self.m_atlasTimer = nil
    self.m_tabTimerPos = {}

    -- 托管
    self.m_trusteeshipControl = nil

    -- 扑克
    self.m_tabNodeCards = {}

    -- 火箭
    self.m_actRocketRepeat = nil
    -- 火箭飞行
    self.m_actRocketShoot = nil

    -- 飞机
    self.m_actPlaneRepeat = nil
    -- 飞机飞行
    self.m_actPlaneShoot = nil

    -- 炸弹
    self.m_actBomb = nil
end

function GameViewLayer:getParentNode()
    return self._scene
end

function GameViewLayer:addToRootLayer( node , zorder)
    if nil == node then
        return
    end

    self.m_rootLayer:addChild(node)
    if type(zorder) == "number" then
        node:setLocalZOrder(zorder)
    end    
end

function GameViewLayer:loadResource()
    -- 加载卡牌纹理
    --[[
    cc.Director:getInstance():getTextureCache():addImage("game/card.png")
    cc.Director:getInstance():getTextureCache():addImage("game/cardsmall.png")
    -- 加载动画纹理
    
    cc.SpriteFrameCache:getInstance():addSpriteFrames("game/animation.plist")
    -- 叫分
    AnimationMgr.loadAnimationFromFrame("call_point_0%d.png", 0, 5, Define.CALLSCORE_ANIMATION_KEY)
    -- 一分
    AnimationMgr.loadAnimationFromFrame("call_point_0%d.png", 5, 3, Define.CALLONE_ANIMATION_KEY)
    -- 两分
    AnimationMgr.loadAnimationFromFrame("call_point_1%d.png", 5, 3, Define.CALLTWO_ANIMATION_KEY)
    -- 三分
    AnimationMgr.loadAnimationFromFrame("call_point_2%d.png", 5, 3, Define.CALLTHREE_ANIMATION_KEY)
    -- 飞机
    AnimationMgr.loadAnimationFromFrame("plane_%d.png", 0, 5, Define.AIRSHIP_ANIMATION_KEY)
    -- 火箭
    AnimationMgr.loadAnimationFromFrame("rocket_%d.png", 0, 5, Define.ROCKET_ANIMATION_KEY)
    -- 报警
    AnimationMgr.loadAnimationFromFrame("game_alarm_0%d.png", 0, 5, Define.ALARM_ANIMATION_KEY)
    -- 炸弹
    AnimationMgr.loadAnimationFromFrame("game_bomb_0%d.png", 0, 5, Define.BOMB_ANIMATION_KEY)
    -- 语音动画
    AnimationMgr.loadAnimationFromFrame("record_play_ani_%d.png", 1, 3, Define.VOICE_ANIMATION_KEY)
--]]
    --播放背景音乐
    --if GlobalUserItem.bMusicAble then
        --helper.music.playMusic('sound/background.mp3')
        --AudioEngine.playMusic('sound/background.mp3')
    --end
    --ExternalFun.playBackgroudAudio("background.mp3")


    local csbNode = helper.app.loadCSB("GameLayer.csb")
    self.m_rootLayer = csbNode
    self.m_csbNode = csbNode
    --csbNode:pos(display.cx, display.cy)
    self:addChild(csbNode)
    --print('111111111111111111111111111', csbNode)

    local function btnEvent( sender, eventType )
        if eventType == ccui.TouchEventType.began then
            ExternalFun.popupTouchFilter(1, false)
        elseif eventType == ccui.TouchEventType.canceled then
            ExternalFun.dismissTouchFilter()
        elseif eventType == ccui.TouchEventType.ended then
            ExternalFun.dismissTouchFilter()
            self:onButtonClickedEvent(sender:getTag(), sender)
        end
    end
    local csbNode = self.m_csbNode

    self.m_cardControl = csbNode:getChildByName("card_control")
    self.m_tabCardCount[cmd.LEFT_VIEWID] = self.m_cardControl:getChildByName("atlas_count_1")
    self.m_tabCardCount[cmd.LEFT_VIEWID]:setLocalZOrder(1)
    self.m_tabCardCount[cmd.LEFT_VIEWID]:hide()
    self.m_tabCardCount[cmd.RIGHT_VIEWID] = self.m_cardControl:getChildByName("atlas_count_3")
    self.m_tabCardCount[cmd.RIGHT_VIEWID]:setLocalZOrder(1)
    self.m_tabCardCount[cmd.RIGHT_VIEWID]:hide()
    self.m_tabCardCountBg[cmd.LEFT_VIEWID] = self.m_cardControl:child('left_card_bg_1')
    self.m_tabCardCountBg[cmd.RIGHT_VIEWID] = self.m_cardControl:child('left_card_bg_3')
    self.m_tabCardCountBg[cmd.LEFT_VIEWID]:hide()
    self.m_tabCardCountBg[cmd.RIGHT_VIEWID]:hide()

    ------
    --顶部菜单

    local top = csbNode:getChildByName("btn_layout")
    --聊天按钮
    --[[
    local btn = top:getChildByName("chat_btn")
    btn:setTag(TAG_ENUM.BT_CHAT)
    btn:setSwallowTouches(true)
    btn:addTouchEventListener(btnEvent)

    --托管按钮
    btn = top:getChildByName("tru_btn")
    btn:setTag(TAG_ENUM.BT_TRU)
    btn:setSwallowTouches(true)
    btn:addTouchEventListener(btnEvent)
    btn:setEnabled(not GlobalUserItem.bPrivateRoom)
    if GlobalUserItem.bPrivateRoom then
        btn:setOpacity(125)
    end
    --]]

    btn = top:child('btn_more')
    btn:setTag(TAG_ENUM.BT_MORE)
    btn:setSwallowTouches(true)
    btn:addTouchEventListener(btnEvent)

    local more_bg = top:child('more_bg')
    self.more_bg = more_bg
    --设置按钮
    btn = more_bg:getChildByName("set_btn")
    btn:setTag(TAG_ENUM.BT_SET)
    btn:setSwallowTouches(true)
    btn:addTouchEventListener(btnEvent)
    self.btnSet =  btn
    --self.btnSet:setEnabled(false)

    --退出按钮
    btn = more_bg:getChildByName("back_btn")
    btn:setTag(TAG_ENUM.BT_EXIT)
    btn:setSwallowTouches(true)
    btn:addTouchEventListener(btnEvent)
    self.btnExit =  btn
    --self.btnExit:setEnabled(false)

    --self.btnExit:setTouchEnabled(false)
    --self.btnSet:setTouchEnabled(false)

    

    --叫分
    self.m_textGameCall = top:getChildByName("gamecall_text")

    -- 庄家扑克
    self.m_nodeBankerCard = cc.Node:create()
    self.m_nodeBankerCard:setPosition(568, 620)
    top:addChild(self.m_nodeBankerCard)

    --准备按钮
    self.m_btnReady = top:getChildByName("ready_btn")
    self.m_btnReady:setTag(TAG_ENUM.BT_READY)
    self.m_btnReady:addTouchEventListener(btnEvent)
    self.m_btnReady:setEnabled(false)
    self.m_btnReady:setVisible(false)
    --self.m_btnReady:loadTextureDisabled("btn_ready_0.png",UI_TEX_TYPE_PLIST)

    -- 邀请按钮
    --[[
    self.m_btnInvite = top:getChildByName("btn_invite")
    self.m_btnInvite:setTag(TAG_ENUM.BT_INVITE)
    self.m_btnInvite:addTouchEventListener(btnEvent)
    if GlobalUserItem.bPrivateRoom then
        self.m_btnInvite:setVisible(false)
        self.m_btnInvite:setEnabled(false)
    end
    --]]

    -- 语音按钮 gameviewlayer -> gamelayer -> clientscene
    --[[
    self:getParentNode():getParentNode():createVoiceBtn(cc.p(1250, 120), 0, top)
    --]]

    -- 帮助按钮 gameviewlayer -> gamelayer -> clientscene
    local url = yl.HTTP_URL .. "/Mobile/Introduce.aspx?kindid=200&typeid=0"
    --self:getParentNode():getParentNode():createHelpBtn(cc.p(1287, 698), 0, url, top)

    --顶部菜单
    ------

    local tabCardPosition = 
    {
        cc.p(179.82, 489.83),
        cc.p(570, 150),
        cc.p(971.42, 489.83)
    }

    local tabBankerCardPosition = 
    {
        cc.p(-35, -10),
        cc.p(5, -10),
        cc.p(45, -10),
        --cc.p(105, -10)
    }
    ------
    --用户状态
    local userState = csbNode:getChildByName("userstate_control")    

    --标签
    local str = ""
    for i = 1, 3 do
        -- 准备标签
        str = "ready" .. i
        local tmpsp = userState:getChildByName(str)
        self.m_tabReadySp[i] = tmpsp

        -- 状态标签
        str = "state_sp" .. i
        tmpsp = userState:getChildByName(str)
        self.m_tabStateSp[i] = tmpsp

        -- 扑克牌
        self.m_tabNodeCards[i] = CardsNode:createEmptyCardsNode(i)
        self.m_tabNodeCards[i]:setPosition(tabCardPosition[i])
        self.m_tabNodeCards[i]:setListener(self)
        self.m_cardControl:addChild(self.m_tabNodeCards[i])

        -- 庄家扑克牌
        tmpsp = CardSprite:createCard(0, {_width = 40, _height = 50, _file = "game/cardsmall.png"})
        tmpsp:setVisible(false)
        tmpsp:setPosition(tabBankerCardPosition[i])
        self.m_nodeBankerCard:addChild(tmpsp)
        tmpsp:setScale(0.28)
        self.m_tabBankerCard[i] = tmpsp

        -- 报警动画
        tmpsp = self.m_cardControl:getChildByName("alarm_" .. i)
        self.m_tabSpAlarm[i] = tmpsp
    end

    --[[
    if self._scene.isLaizi == true then
        local tmpsp = CardSprite:createCard(0, {_width = 40, _height = 50, _file = "game/cardsmall.png"})
        tmpsp:setVisible(false)
        tmpsp:setPosition(tabBankerCardPosition[4])
        self.m_nodeBankerCard:addChild(tmpsp)
        tmpsp:setScale(0.28)
        self.m_tabBankerCard[4] = tmpsp
    end
    --]]

    --用户状态
    ------

    ------
    --叫分控制

    local callScore = csbNode:getChildByName("callscore_control")
    self.m_callScoreControl = callScore
    self.m_callScoreControl:setVisible(false)
    --叫分按钮
    for i = 1, 4 do
        str = "score_btn" .. i
        btn = callScore:getChildByName(str)
        btn:setTag(TAG_ENUM.BT_CALLSCORE0 + i - 1)
        btn:addTouchEventListener(btnEvent)
        self.m_tabCallScoreBtn[i] = btn
    end

    --叫分控制
    ------

    ------
    --操作控制

    local onGame = csbNode:getChildByName("ongame_control")
    self.m_onGameControl = onGame
    self.m_onGameControl:setVisible(false)

    --不出按钮
    btn = onGame:getChildByName("pass_btn")
    btn:setTag(TAG_ENUM.BT_PASS)
    btn:addTouchEventListener(btnEvent)
    self.m_btnPass = btn

    --提示按钮
    btn = onGame:getChildByName("suggest_btn")
    btn:setTag(TAG_ENUM.BT_SUGGEST)
    btn:addTouchEventListener(btnEvent)

    --出牌按钮
    btn = onGame:getChildByName("outcard_btn")
    btn:setTag(TAG_ENUM.BT_OUTCARD)
    btn:addTouchEventListener(btnEvent)
    btn:setSwallowTouches(true)
    self.m_btnOutCard = btn

    local card_type_sel = self.m_csbNode:child('card_type_sel')
    btn = card_type_sel:child('img_card_sample')
    btn:setTag(TAG_ENUM.BT_CARD_SEL)
    btn:addTouchEventListener(btnEvent)

    --操作控制
    ------

    ------
    -- 出牌控制
    self.m_outCardsControl = csbNode:getChildByName("outcards_control")
    -- 出牌控制
    ------

    ------
    -- 用户信息

    local infoLayout = csbNode:getChildByName("info")
    self.m_userinfoControl = infoLayout

    self.isShowRecord = false
    self:setRecordState(false, true)

     --出牌按钮
     local img_record = infoLayout:getChildByName("img_record")
     img_record:setTag(TAG_ENUM.BT_RECORD)
     img_record:addTouchEventListener(btnEvent)
     img_record:setSwallowTouches(true)

    --用户昵称
    self.m_clipNick = ClipText:createClipText(cc.size(200, 30), GlobalUserItem.szNickName, FONT_TTF, 30)
    local tmp = infoLayout:getChildByName("tmpname_text")
    self.m_clipNick:setPosition(tmp:getPosition())
    self.m_clipNick:setAnchorPoint(tmp:getAnchorPoint())
    infoLayout:addChild(self.m_clipNick)
    self.m_clipNick:hide()
    tmp:removeFromParent()

    -- 金币
    self.m_atlasScore = infoLayout:getChildByName("score_atlas")

    -- 底分
    self.m_atlasDiFeng = top:getChildByName("dizhu_txt")
    self.m_atlasDiFeng:setString("")

    -- 头像位置
    self.m_tabUserHeadPos[cmd.LEFT_VIEWID] = cc.p(50, 416)
    self.m_tabUserHeadPos[cmd.MY_VIEWID] = cc.p(122, 178)
    self.m_tabUserHeadPos[cmd.RIGHT_VIEWID] = cc.p(1100, 416)

    -- 提示tip
    self.m_spInfoTip = infoLayout:getChildByName("info_tip")

    -- 倒计时
    self.m_spTimer = infoLayout:getChildByName("bg_clock")
    self.m_atlasTimer = self.m_spTimer:getChildByName("atlas_time")
    self.m_tabTimerPos[cmd.MY_VIEWID] = cc.p(display.width * 0.5, 380)
    self.m_tabTimerPos[cmd.LEFT_VIEWID] = cc.p(display.width * 0.22, 500)
    self.m_tabTimerPos[cmd.RIGHT_VIEWID] = cc.p(display.width* 0.80, 500)
    -- 用户信息
    ------

    ------
    -- 游戏托管
    self.m_trusteeshipControl = csbNode:getChildByName("tru_control")
    self.m_trusteeshipControl:addTouchEventListener(function( ref, tType)
        if tType == ccui.TouchEventType.ended then
            if self.m_trusteeshipControl:isVisible() then
                self.m_trusteeshipControl:setVisible(false)
            end
        end
    end)
    -- 游戏托管
    ------

    self:reSetGame()
    self:createAnimation()

    self:setTouchEnabled(true)
	self:registerScriptTouchHandler(function(eventType, x, y)
		return true
    end)
    
    self.m_node_player = {}
    for i = 1, 3 do
        local head_bg = self.m_userinfoControl:child('head_bg_' .. i)
        self.m_node_player[i] = head_bg
    end
    

    --第一次进入房间自动准备
    self.isFirstLogin = true
end

function GameViewLayer:createFourTopCard(  )
    -- body
    if self._scene.isLaizi == true then
        local tmpsp = CardSprite:createCard(0, {_width = 40, _height = 50, _file = "game/cardsmall.png"})
        tmpsp:setVisible(false)
        tmpsp:setPosition(cc.p(105, -10))
        self.m_nodeBankerCard:addChild(tmpsp)
        tmpsp:setScale(0.28)
        self.m_tabBankerCard[4] = tmpsp
    end
end

function GameViewLayer:setRecordState( isShow, isInit )
    print('isShow', isShow)
    self.m_userinfoControl:child('img_record_bg'):setVisible(isShow)
    for i=1, 15 do
        self.m_userinfoControl:child('card_left_' .. i):setVisible(isShow)
        if isInit then
            self.m_userinfoControl:child('card_left_' .. i):setString('')
        end
    end
end

function GameViewLayer:setRecordNum( card_nums)
    --dump(card_nums)
    for i=1, 15 do repeat
        --[[
        if card_nums[i] <= 0 then
            break
        end
        --]]
        local lbl_card_num = self.m_userinfoControl:child('card_left_' .. i)
        --lbl_card_num:show()
        print('i and card nums ', i, card_nums[i], lbl_card_num)
        if card_nums[i] <= 0 then
            lbl_card_num:setString("")
        else
            lbl_card_num:setString(card_nums[i])
        end
        
        if card_nums[i] >= 4 then
            lbl_card_num:setColor(cc.c3b(0xFD, 0xF4, 0x65))
        else
            lbl_card_num:setColor(display.COLOR_WHITE)
        end
        
    until true
    end
end

function GameViewLayer:createAnimation()
    local param = AnimationMgr.getAnimationParam()
    param.m_fDelay = 0.1
    -- 火箭动画
    param.m_strName = Define.ROCKET_ANIMATION_KEY
    local animate = AnimationMgr.getAnimate(param)
    if animate == nil then
        --self:unloadResource()
        --self:loadResource()
        --animate = AnimationMgr.getAnimate(param)
        return
    end
    if nil ~= animate then
        local rep = cc.RepeatForever:create(animate)
        self.m_actRocketRepeat = rep
        self.m_actRocketRepeat:retain()
        local moDown = cc.MoveBy:create(0.1, cc.p(0, -20))
        local moBy = cc.MoveBy:create(2.0, cc.p(0, 500))
        local fade = cc.FadeOut:create(2.0)
        local seq = cc.Sequence:create(cc.DelayTime:create(2.0), cc.CallFunc:create(function()

            end), fade)
        local spa = cc.Spawn:create(cc.EaseExponentialIn:create(moBy), seq)
        self.m_actRocketShoot = cc.Sequence:create(cc.CallFunc:create(function( ref )
            ref:runAction(rep)
            if GlobalUserItem.bVoiceAble then
                AudioEngine.playEffect('sound/rocket.mp3')
            end
        end), moDown, spa, cc.RemoveSelf:create(true))
        self.m_actRocketShoot:retain()
    end

    -- 飞机动画    
    param.m_strName = Define.AIRSHIP_ANIMATION_KEY
    local animate = AnimationMgr.getAnimate(param)
    if nil ~= animate then
        local rep = cc.RepeatForever:create(animate)
        self.m_actPlaneRepeat = rep
        self.m_actPlaneRepeat:retain()
        local moTo = cc.MoveTo:create(3.0, cc.p(0, display.height * 0.5))
        local fade = cc.FadeOut:create(1.5)
        local seq = cc.Sequence:create(cc.DelayTime:create(1.5), cc.CallFunc:create(function()
            if GlobalUserItem.bVoiceAble then
                AudioEngine.playEffect('sound/common_plane.wav')
            end
            --ExternalFun.playSoundEffect("common_plane.wav")
            end), fade)
        local spa = cc.Spawn:create(moTo, seq)
        self.m_actPlaneShoot = cc.Sequence:create(cc.CallFunc:create(function( ref )
            ref:runAction(rep)
        end), spa, cc.RemoveSelf:create(true))
        self.m_actPlaneShoot:retain()
    end

    -- 炸弹动画
    param.m_strName = Define.BOMB_ANIMATION_KEY
    local animate = AnimationMgr.getAnimate(param)
    if nil ~= animate then
        local fade = cc.FadeOut:create(1.0)
        self.m_actBomb = cc.Sequence:create(animate, fade, cc.RemoveSelf:create(true))
        self.m_actBomb:retain()
    end    
end

function GameViewLayer:unloadResource()
    cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("game/animation.plist")
    cc.Director:getInstance():getTextureCache():removeTextureForKey("game/animation.png")
    AnimationMgr.removeCachedAnimation(Define.CALLSCORE_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.CALLONE_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.CALLTWO_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.CALLTHREE_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.AIRSHIP_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.ROCKET_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.ALARM_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.BOMB_ANIMATION_KEY)
    AnimationMgr.removeCachedAnimation(Define.VOICE_ANIMATION_KEY)

    cc.Director:getInstance():getTextureCache():removeTextureForKey("game/card.png")
    cc.Director:getInstance():getTextureCache():removeTextureForKey("game/cardsmall.png")
    cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("game/game.plist")
    cc.Director:getInstance():getTextureCache():removeTextureForKey("game/game.png")
    cc.SpriteFrameCache:getInstance():removeSpriteFramesFromFile("public_res/public_res.plist")
    cc.Director:getInstance():getTextureCache():removeTextureForKey("public_res/public_res.png")
    cc.Director:getInstance():getTextureCache():removeUnusedTextures()
    cc.SpriteFrameCache:getInstance():removeUnusedSpriteFrames()
end
-- 重置
function GameViewLayer:reSetGame()
    self:reSetUserState()

    self.m_spTimer:setVisible(false)
    self.m_atlasTimer:setString("")
    -- 取消托管
    self.m_trusteeshipControl:setVisible(false)

    self.m_bMyCallBanker = false
    self.m_bMyOutCards = false

    
end

-- 重置(新一局)
function GameViewLayer:reSetForNewGame()
    -- 清理手牌
    for k,v in pairs(self.m_tabNodeCards) do
        v:removeAllCards()

        self.m_tabSpAlarm[k]:stopAllActions()
        self.m_tabSpAlarm[k]:hide()
        --self.m_tabSpAlarm[k]:setSpriteFrame("blank.png")
    end
    for k,v in pairs(self.m_tabCardCount) do
        v:setString("")
        v:hide()
    end
    for k, v in pairs(self.m_tabCardCountBg) do
        v:hide()
    end
    -- 清理桌面
    self.m_outCardsControl:removeAllChildren()
    -- 庄家叫分
    self.m_textGameCall:setString("1")
    -- 庄家扑克
    for k,v in pairs(self.m_tabBankerCard) do
        v:setVisible(false)
        v:setCardValue(0)
    end
    -- 用户切换
    for k,v in pairs(self.m_tabUserHead) do
        v:reSet()
    end
end

-- 重置用户状态
function GameViewLayer:reSetUserState()
    for k,v in pairs(self.m_tabReadySp) do
        v:setVisible(false)
    end

    for k,v in pairs(self.m_tabStateSp) do
        v:hide()
        --v:setSpriteFrame("blank.png")
        break
    end
end

-- 重置用户信息
function GameViewLayer:reSetUserInfo()
    local score = self:getParentNode():GetMeUserItem().lScore or 0
    --[[
    local str = ""
    if score < 0 then
        str = "." .. score
    else
        str = "" .. score        
    end 
    if string.len(str) > 11 then
        str = string.sub(str, 1, 11)
        str = str .. "///"
    end  
    --]]
    local str = helper.str.makeFormatNum(score, 1)
    print(str)
    self.m_atlasScore:setString(str) 
end

function GameViewLayer:onExit()
    self:onExitEx()
    if nil ~= self.m_actRocketRepeat then
        self.m_actRocketRepeat:release()
        self.m_actRocketRepeat = nil
    end

    if nil ~= self.m_actRocketShoot then
        self.m_actRocketShoot:release()
        self.m_actRocketShoot = nil
    end

    if nil ~= self.m_actPlaneRepeat then
        self.m_actPlaneRepeat:release()
        self.m_actPlaneRepeat = nil
    end

    if nil ~= self.m_actPlaneShoot then
        self.m_actPlaneShoot:release()
        self.m_actPlaneShoot = nil
    end

    if nil ~= self.m_actBomb then
        self.m_actBomb:release()
        self.m_actBomb = nil
    end
    --self:unloadResource()

    self.m_tabUserItem = {}
end

function GameViewLayer:onButtonClickedEvent(tag, ref)   
    --ExternalFun.playClickEffect()
    if TAG_ENUM.BT_CHAT == tag then             --聊天        
        if nil == self.m_chatLayer then
            --self.m_chatLayer = GameChatLayer:create(self._scene._gameFrame)
            --self:addToRootLayer(self.m_chatLayer, TAG_ZORDER.CHAT_ZORDER)
        end
        --self.m_chatLayer:showGameChat(true)
    elseif TAG_ENUM.BT_TRU == tag then          --托管
        self:onGameTrusteeship(true)
    elseif TAG_ENUM.BT_MORE == tag then         -- 右上角的更多按钮
        self:onMoreEvent()
    elseif TAG_ENUM.BT_SET == tag then          --设置
        --local set = SettingLayer:create()
        --self:addToRootLayer(set, TAG_ZORDER.SET_ZORDER)
        helper.link.toSetting( cs.game.SRC .. 'RoomSetting')
    elseif TAG_ENUM.BT_EXIT == tag then         --退出
        if self._scene.isInGame then
            helper.pop.message( LANG.NOT_EXIT_ROOT )
            return
        end
        self:getParentNode().is_ready_btn_press_status = false
        self:getParentNode():onQueryExitGame()
        
    elseif TAG_ENUM.BT_READY == tag then        --准备
        self:onClickReady()
    elseif TAG_ENUM.BT_INVITE == tag then       -- 邀请
        GlobalUserItem.bAutoConnect = false
        self:getParentNode():getParentNode():popTargetShare(function(target, bMyFriend)
            bMyFriend = bMyFriend or false
            local function sharecall( isok )
                if type(isok) == "string" and isok == "true" then
                    --showToast(self, "分享成功", 2)
                end
                GlobalUserItem.bAutoConnect = true
            end
            local shareTxt = "斗地主游戏精彩刺激, 一起来玩吧! "
            local url = GlobalUserItem.szSpreaderURL or yl.HTTP_URL
            if bMyFriend then
                PriRoom:getInstance():getTagLayer(PriRoom.LAYTAG.LAYER_FRIENDLIST, function( frienddata )
                    dump(frienddata)
                end)
            elseif nil ~= target then
                MultiPlatform:getInstance():shareToTarget(target, sharecall, "斗地主游戏邀请", shareTxt, url, "")
            end
        end)        
    elseif TAG_ENUM.BT_CALLSCORE0 == tag then   --不叫
        helper.music.playPeiyin( self._scene.wPeiyin[cmd.MY_VIEWID], "no" )
        --ExternalFun.playSoundEffect( "cs0.wav", self:getParentNode():GetMeUserItem())
        self:getParentNode():sendCallScore(255)
        self.m_callScoreControl:setVisible(false)
    elseif TAG_ENUM.BT_CALLSCORE1 == tag then   --一分
        helper.music.playPeiyin( self._scene.wPeiyin[cmd.MY_VIEWID], "one_point" )
        --ExternalFun.playSoundEffect( "cs1.wav", self:getParentNode():GetMeUserItem())
        self:getParentNode():sendCallScore(1)
        self.m_callScoreControl:setVisible(false)
    elseif TAG_ENUM.BT_CALLSCORE2 == tag then   --两分
        helper.music.playPeiyin( self._scene.wPeiyin[cmd.MY_VIEWID], "two_point" )
        --ExternalFun.playSoundEffect( "cs2.wav", self:getParentNode():GetMeUserItem())
        self:getParentNode():sendCallScore(2)
        self.m_callScoreControl:setVisible(false)
    elseif TAG_ENUM.BT_CALLSCORE3 == tag then   --三分
        helper.music.playPeiyin( self._scene.wPeiyin[cmd.MY_VIEWID], "three_point" )
        --ExternalFun.playSoundEffect( "cs3.wav", self:getParentNode():GetMeUserItem())
        self:getParentNode():sendCallScore(3)
        self.m_callScoreControl:setVisible(false)
    elseif TAG_ENUM.BT_PASS == tag then         --不出
        self:onPassOutCard()
    elseif TAG_ENUM.BT_SUGGEST == tag then      --提示
        self:onPromptOut(false)        
    elseif TAG_ENUM.BT_RECORD == tag then   --img_record
        self.isShowRecord = not self.isShowRecord
        self:setRecordState(self.isShowRecord)
    elseif TAG_ENUM.BT_CARD_SEL == tag then
        self:ShowCardSel(false, nil)
        local card = ref
        local index = card.index        
        local sel = self.result[3][index]
        local cbLaiziNum = GameLogic:GetLaiziNum(sel, #sel, self._scene.cbLaiziCardData)
        dump(sel)
        print('cbLaiziNum is ', cbLaiziNum, index)
        -- 扑克对比
        self:getParentNode():compareWithLastCards(sel, cmd.MY_VIEWID)
        --if self:getParentNode().m_bLastCompareRes == true then
            local vec = self.m_tabNodeCards[cmd.MY_VIEWID]:outCard(sel)
            self:outCardEffect(cmd.MY_VIEWID, sel, vec)
            self.m_onGameControl:setVisible(false)
            self:getParentNode():sendOutCard(sel, false, cbLaiziNum)
        --end
        

    elseif TAG_ENUM.BT_OUTCARD == tag then      --出牌
        local sel = self.m_tabNodeCards[cmd.MY_VIEWID]:getSelectCards()
        dump(sel)
        local cbNum = GameLogic:GetLaiziNum(sel, #sel, self._scene.cbLaiziCardData)
        if cbNum > 0 then
            self.result = nil
            print('BT_OUTCARD', cbNum)
            -- 任意出牌
            if self._scene.m_nLastOutViewId == cmd.MY_VIEWID then
                self.result = GameLogic:GetAllTypeCards(sel, #sel, self._scene.cbLaiziCardData, 0)
            else -- 压牌
                self.result = GameLogic:GetAllTypeCards(sel, #sel, self._scene.cbLaiziCardData, self._scene.preCardType)
            end
            dump(self.result)
            if self.result[1] == 1 then
                -- 扑克对比
                self:getParentNode():compareWithLastCards(self.result[3][1], cmd.MY_VIEWID)

                --if self:getParentNode().m_bLastCompareRes == true then
                    self.m_onGameControl:setVisible(false)
                    local vec = self.m_tabNodeCards[cmd.MY_VIEWID]:outCard(self.result[3][1])
                    self:outCardEffect(cmd.MY_VIEWID, self.result[3][1], vec)
                    self:getParentNode():sendOutCard(self.result[3][1], false, cbNum)
                --end
            else
                if self.result[1] > 1 then
                    self:ShowCardSel(true ,self.result)
                end
            end            
        else
            -- 扑克对比
            dump(sel)
            self:getParentNode():compareWithLastCards(sel, cmd.MY_VIEWID)
            --if self:getParentNode().m_bLastCompareRes == true then
                self.m_onGameControl:setVisible(false)
                local vec = self.m_tabNodeCards[cmd.MY_VIEWID]:outCard(sel)
                self:outCardEffect(cmd.MY_VIEWID, sel, vec)
                self:getParentNode():sendOutCard(sel)
            --end
        end
        
    end
end

function GameViewLayer:ShowCardSel( bIsShow, result )
    -- body
    local card_type_sel = self.m_csbNode:child('card_type_sel')
    card_type_sel:setVisible(bIsShow)
    if not bIsShow then
        return
    end

    if #self.smallCards > 0 then
        for i = 1, #self.smallCards do
            self.smallCards[i]:removeFromParent()
        end
        self.smallCards = {}
    end
    
    --local index = card.index        
    --local cbLaiziNum = card.cbNum
    local card_type_sel = self.m_csbNode:child('card_type_sel')
    local imgSample = card_type_sel:child('img_card_sample')
    local x, y = imgSample:pos()
    
    for i = 1, result[1] do
        local cur_y = y - (i - 1) * (53 + 10)
        for j = 1, result[2][i] do
            local cur_x = x + (j - 1) *(37 + 5)
            local clone = imgSample:clone()
            clone.index = i
            clone.cbNum = result[2][i]
            clone:pos(cur_x, cur_y)
            
            local color = GameLogic:GetCardColor(result[3][i][j])
            color = bit:_rshift(color,4)
            local val = GameLogic:GetCardValue(result[3][i][j])
            local png = string.format('card/card%d%d.png', color, val)
            print('cur result card val is ', result[3][i][j], color, val, png)
            --clone:setCardValue(result[3][i][j])
            clone:texture(png)
            clone:show()
            card_type_sel:addChild(clone)
            table.insert( self.smallCards, clone)
        end
    end
end

function GameViewLayer:IsNeedLaizi( sel )
    -- body
    for i = 1, #sel do
        if GameLogic:GetCardLogicValue(sel[i]) == GameLogic.cbLaiziLogicVal then
            return true
        elseif sel[i] > 0x40 and sel[i] < 0x4E then
            return true
        end
    end

    return false
end

function GameViewLayer:onMoreEvent()
    self.isOpenMore = self.isOpenMore or false
    self.isOpenMore = not self.isOpenMore
    if self.isOpenMore then
        self.more_bg:show()
        self.btnExit:setTouchEnabled(true)
        self.btnSet:setTouchEnabled(true)
        
    else
        self.more_bg:hide()
        self.btnExit:setTouchEnabled(false)
        self.btnSet:setTouchEnabled(false)
    end
end

function GameViewLayer:onClickReady()
    self.m_btnReady:setEnabled(false)
    self.m_btnReady:setVisible(false)

    self:getParentNode().is_ready_btn_press_status = false

    self:getParentNode():sendReady()

    if self:getParentNode().m_bRoundOver then
        self:getParentNode().m_bRoundOver = false
        -- 界面清理
        self:reSetForNewGame()
    end 

    self:setRecordState(false,true)
end

-- 出牌效果
-- @param[outViewId]        出牌视图id
-- @param[outCards]         出牌数据
-- @param[vecCards]         扑克精灵
function GameViewLayer:outCardEffect(outViewId, outCards, vecCards)
    local controlSize = self.m_outCardsControl:getContentSize()

    -- 移除出牌
    self.m_outCardsControl:removeChildByTag(outViewId)
    local holder = cc.Node:create()
    self.m_outCardsControl:addChild(holder)
    holder:setTag(outViewId)
    self.lastOutViewID = outViewId

    local outCount = #outCards
    dump(outCards)
    -- 计算牌型
    local cardType = GameLogic:GetCardType(outCards, outCount)
    if GameLogic.CT_THREE_TAKE_ONE == cardType then
        if outCount > 4 then
            cardType = GameLogic.CT_THREE_LINE
        end        
    end
    if GameLogic.CT_THREE_TAKE_TWO == cardType then
        if outCount > 5 then
            cardType = GameLogic.CT_THREE_LINE
        end        
    end

    -- 出牌
    local targetPos = cc.p(0, 0)
    local center = outCount * 0.5
    local scale = 0.5
    local animation_pos = cc.p(0,0)
    holder:setPosition(self.m_tabNodeCards[outViewId]:getPosition())
    if cmd.MY_VIEWID == outViewId then
        scale = 0.6
        targetPos = holder:convertToNodeSpace(cc.p(controlSize.width * 0.50, controlSize.height * 0.52))
        animation_pos = cc.p(controlSize.width * 0.50, controlSize.height * 0.52)
    elseif cmd.LEFT_VIEWID == outViewId then
        center = 0
        holder:setAnchorPoint(cc.p(0, 0.5))
        targetPos = holder:convertToNodeSpace(cc.p(controlSize.width * 0.27, controlSize.height * 0.76))
        animation_pos = cc.p(controlSize.width * 0.27, controlSize.height * 0.76)
    elseif cmd.RIGHT_VIEWID == outViewId then
        center = outCount
        holder:setAnchorPoint(cc.p(1, 0.5))
        targetPos = holder:convertToNodeSpace(cc.p(controlSize.width * 0.73, controlSize.height * 0.76))
        animation_pos = cc.p(controlSize.width * 0.73, controlSize.height * 0.76)
    end
    local zIndex = 1
    for k,v in pairs(vecCards) do
        v:retain()
        v:removeFromParent()
        holder:addChild(v)
        v:release()

        v:show()
        if cmd.MY_VIEWID == outViewId then
            v:setLocalZOrder(zIndex)
            zIndex = zIndex + 1
        end
        v:showCardBack(false)
        local pos = cc.p((k - center) * CardsNode.CARD_X_DIS * scale + targetPos.x, targetPos.y)
        local moveTo = cc.MoveTo:create(0.1, pos)
        local spa = cc.Spawn:create(moveTo, cc.ScaleTo:create(0.1, scale))
        v:stopAllActions()
        v:runAction(spa)
    end

    --print("## 出牌类型")
    --print(cardType)
    --print("## 出牌类型")
    --[[
    print('123213123123123123')
    local headitem = self.m_tabUserHead[outViewId]
    if nil == headitem then
        return
    end
    --]]

    -- 牌型音效
    print('123213123123123123')
    local bCompare = self:getParentNode().m_bLastCompareRes
    if GameLogic.CT_SINGLE == cardType then
        -- 音效
        local poker = yl.POKER_VALUE[outCards[1]]
        if nil ~= poker then
            print('outViewId ',  self._scene.wPeiyin[outViewId], '1_' .. poker)
            helper.music.playPeiyin( self._scene.wPeiyin[outViewId], '1_' .. poker )
            --ExternalFun.playSoundEffect(poker .. ".wav", headitem.m_userItem)
        end 
    elseif GameLogic.CT_DOUBLE == cardType then
        local poker = GameLogic:GetCardValue(outCards[1]) --yl.POKER_VALUE[outCards[1]]
        if nil ~= poker then
            print('outViewId ',  self._scene.wPeiyin[outViewId], '2_' .. poker)
            helper.music.playPeiyin( self._scene.wPeiyin[outViewId], '2_' .. poker )
            --ExternalFun.playSoundEffect(poker .. ".wav", headitem.m_userItem)
        end  
    elseif GameLogic.CT_THREE == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'sange' ) 
    elseif GameLogic.CT_THREE_TAKE_ONE == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'sandaiyi' ) 
    elseif GameLogic.CT_THREE_TAKE_TWO == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'sandaier' ) 
    elseif GameLogic.CT_SINGLE_LINE == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'shunzi' ) 
    elseif GameLogic.CT_DOUBLE_LINE == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'liandui' ) 
    elseif GameLogic.CT_THREE_LINE == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'feiji' ) 
    elseif GameLogic.CT_BOMB_CARD == cardType or 
        GameLogic.CT_BOMB_SOFT == cardType or 
        GameLogic.CT_BOMB_LAIZI == cardType  then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'zhadan' ) 
    elseif GameLogic.CT_MISSILE_CARD == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'huojian' ) 
    elseif GameLogic.CT_FOUR_TAKE_ONE == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'sidaier' ) 
    elseif GameLogic.CT_FOUR_TAKE_TWO == cardType then
        helper.music.playPeiyin( self._scene.wPeiyin[outViewId], 'sidailiangdui' ) 
    else
        if bCompare then
            -- 音效
            helper.music.playPeiyin( self._scene.wPeiyin[outViewId], "ya" .. math.random(1, 2) )
            --ExternalFun.playSoundEffect("ya" .. math.random(0, 1) .. ".wav", headitem.m_userItem)
        else
            -- 音效
            helper.music.playPeiyin( self._scene.wPeiyin[outViewId], "type" .. cardType )
            --ExternalFun.playSoundEffect( "type" .. cardType .. ".wav", headitem.m_userItem)
        end
    end

    self:stopAllActions()
    self.m_rootLayer:stopAllActions()
    self.m_rootLayer:removeChildByName("__effect_ani_name__")
    -- 牌型动画/牌型音效
    if GameLogic.CT_THREE_LINE == cardType then             -- 飞机
        self:feijiAction(cc.p(display.width * 0.5, display.height * 0.5))
        --[[
        local frame = cc.SpriteFrameCache:getInstance():getSpriteFrame("plane_0.png")
        if nil ~= frame then
            local sp = cc.Sprite:createWithSpriteFrame(frame)
            sp:setPosition(display.width * 0.5, display.height * 0.5)
            sp:setName("__effect_ani_name__")
            self:addToRootLayer(sp, TAG_ZORDER.EFFECT_ZORDER)
            sp:runAction(self.m_actPlaneShoot)
        end
        --]]
    elseif GameLogic.CT_BOMB_CARD == cardType then          -- 炸弹
        local file_name = 'room/bombAni/bombAni'
		local frame_num = 19
        local action_time = 0.1
        local callback_delay =  cc.CallFunc:create(function()
            if GlobalUserItem.bVoiceAble then
                AudioEngine.playEffect('sound/bomb.mp3')
            end
        end)
		local callback = cc.RemoveSelf:create(true)
        local sprite = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, callback, ccui.TextureResType.localType)
        sprite:setName("__effect_ani_name__")
        sprite:setPosition(animation_pos.x, animation_pos.y)
        self:addToRootLayer(sprite, TAG_ZORDER.EFFECT_ZORDER)
        self:runAction(cc.Sequence:create(
            cc.DelayTime:create(action_time * 2),
            callback_delay
        ))
		--self.main_node:addChild(sprite, 20)
        --sprite:pos(zha_pos_x, start_y)
        
        --[[
        local frame = cc.SpriteFrameCache:getInstance():getSpriteFrame("game_bomb_01.png")
        if nil ~= frame then
            local sp = cc.Sprite:createWithSpriteFrame(frame)
            sp:setPosition(display.width * 0.5, display.height * 0.5)
            sp:setName("__effect_ani_name__")
            self:addToRootLayer(sp, TAG_ZORDER.EFFECT_ZORDER)
            sp:runAction(self.m_actBomb)
            -- 音效
            --helper.music.playPeiyin( self._scene.wPeiyin[outViewId], "type" .. cardType )
            --ExternalFun.playSoundEffect( "common_bomb.wav" )
        end
        --]]
    elseif GameLogic.CT_DOUBLE_LINE == cardType then          -- 炸弹
        local file_name = 'room/doubleLine/doubleLine'
		local frame_num = 35
        local action_time = 0.025
        local callback_delay =  cc.CallFunc:create(function()
            if GlobalUserItem.bVoiceAble then
                AudioEngine.playEffect('sound/continuous_pair.mp3')
            end
        end)
		local callback = cc.RemoveSelf:create(true)
        local sprite = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, callback, ccui.TextureResType.localType)
        sprite:setName("__effect_ani_name__")
        sprite:setPosition(animation_pos.x, animation_pos.y)
        self:addToRootLayer(sprite, TAG_ZORDER.EFFECT_ZORDER)
        self:runAction(cc.Sequence:create(
            cc.DelayTime:create(action_time * 2),
            callback_delay
        ))
    elseif GameLogic.CT_SINGLE_LINE == cardType then          -- 炸弹
        local file_name = 'room/singleLine/singleLine'
		local frame_num = 36
        local action_time = 0.025
        local callback_delay =  cc.CallFunc:create(function()
            if GlobalUserItem.bVoiceAble then
                AudioEngine.playEffect('sound/straight.mp3')
            end
        end)
		local callback = cc.RemoveSelf:create(true)
        local sprite = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, callback, ccui.TextureResType.localType)
        sprite:setName("__effect_ani_name__")
        sprite:setPosition(animation_pos.x, animation_pos.y)
        self:addToRootLayer(sprite, TAG_ZORDER.EFFECT_ZORDER)
        self:runAction(cc.Sequence:create(
            cc.DelayTime:create(action_time * 2),
            callback_delay
        ))
    elseif GameLogic.CT_MISSILE_CARD == cardType then       -- 火箭
        self:huojianAction(cc.p(display.width * 0.5, display.height * 0.5))
        --[[
        local frame = cc.SpriteFrameCache:getInstance():getSpriteFrame("rocket_0.png")
        if nil ~= frame then
            local sp = cc.Sprite:createWithSpriteFrame(frame)
            sp:setPosition(display.width * 0.5, display.height * 0.5)
            --sprite:setPosition(animation_pos.x, animation_pos.y)
            sp:setName("__effect_ani_name__")
            self:addToRootLayer(sp, TAG_ZORDER.EFFECT_ZORDER)
            sp:runAction(self.m_actRocketShoot)
        end
        --]]
    end

    self.pre_card_type = cardType
end

function GameViewLayer:huojianAction(pos)
    local node = cc.Node:create()
    node:pos(pos.x, pos.y)
    node:setName("__effect_ani_name__")
    self:addToRootLayer(node, TAG_ZORDER.EFFECT_ZORDER)


    local file_name = 'room/rocket_font/rocket_font'
    local action_time = 0.025
    local frame_num = 36

    file_name = 'room/rocket_up/rocket_up'
    frame_num = 5
    action_time = 0.1
    local rocket_up = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, false, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
    local size = rocket_up:size()
    --rocket_up:pos(size.width / 2, size.height / 2)
    node:addChild(rocket_up)
    local call_back = cc.CallFunc:create(
        function (  )
            if tolua.isnull(node) then
                return 
            end
            file_name = 'room/rocket_down/rocket_down'
            frame_num = 12
            action_time = 0.1
            local rocket_down = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, false, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
            size = rocket_down:size()
            --rocket_down:pos(size.width / 2, size.height / 2)
            node:addChild(rocket_down)

            local call_back_1 = cc.CallFunc:create(
                function ()
                    if tolua.isnull(node) then
                        return 
                    end
                    file_name = 'room/rocket_bomb/rocket_bomb'
                    frame_num = 11
                    action_time = 0.1
                    local rocket_bomb = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
                    --size = rocket_bomb:size()
                    --rocket_bomb:pos(size.width / 2, size.height / 2)
                    node:addChild(rocket_bomb)

                    file_name = 'room/rocket_font/rocket_font'
                    action_time = 0.025
                    frame_num = 36
                    local rocket_font = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, false, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
                    --size = rocket_font:size()
                    --rocket_font:pos(size.width / 2, size.height / 2)
                    node:addChild(rocket_font)

                    local end_call = cc.CallFunc:create(
                        function ()
                            if tolua.isnull(node) then
                                return 
                            end
                            node:removeFromParent()
                        end
                    )
                    self:runAction(cc.Sequence:create(cc.DelayTime:create(action_time * frame_num), end_call))
                end
            )
            local time = action_time * frame_num
            time = time - 0.3
            self:runAction(cc.Sequence:create(cc.DelayTime:create(time), call_back_1))
            
        end
    )
    if GlobalUserItem.bVoiceAble then
        AudioEngine.playEffect('sound/rocket.mp3')
    end
    self:runAction(cc.Sequence:create(cc.DelayTime:create(action_time * frame_num), call_back))
end

function GameViewLayer:feijiAction(pos)
    local node = cc.Node:create()
    node:pos(pos.x, pos.y)
    node:setName("__effect_ani_name__")
    self:addToRootLayer(node, TAG_ZORDER.EFFECT_ZORDER)


    local file_name = 'room/air/air'
	
    local action_time = 0.1
    local frame_num = 12
    local air_sprite = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
    node:addChild(air_sprite)

    file_name = 'room/airfont/airfont'
    frame_num = 36
    action_time = 0.025
    local airfont = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
    node:addChild(airfont)

    file_name = 'room/airtailgas/airtailgas'
    frame_num = 12
    action_time = 0.1
    local airtailgas = helper.app.createAnimation(file_name, frame_num, action_time * frame_num, true, cc.RemoveSelf:create(true), ccui.TextureResType.localType)
    air_sprite:addChild(airtailgas)
   

    local moTo = cc.MoveBy:create(1.0, cc.p(display.width / 2, 0))
        --local fade = cc.FadeOut:create(1.5)
    local seq =  cc.CallFunc:create(function()
        if GlobalUserItem.bVoiceAble then
            AudioEngine.playEffect('sound/common_plane.wav')
        end
        --node:removeFromParent()
            --ExternalFun.playSoundEffect("common_plane.wav")
    end)
    local spa = cc.Spawn:create(moTo, seq)
    local ac = cc.Sequence:create( spa, cc.RemoveSelf:create(true))

    node:runAction(ac)
end

-------------------------------------------------------------------------------
-- 重新设置桌布
-------------------------------------------------------------------------------
function GameViewLayer:resetTablecloth()
    if GlobalUserItem.nTablecloth >= 0 and GlobalUserItem.nTablecloth <= 1 then
        self.m_csbNode:child('game_bg_0_1'):texture('room/bg_room' .. GlobalUserItem.nTablecloth .. '.jpg')
    end
end

function GameViewLayer:onSettingChange()
	self:resetTablecloth()
end

function GameViewLayer:onChangePassBtnState( bEnable )
    if self.pre_card_type == GameLogic.CT_MISSILE_CARD then
        self.m_btnPass:setEnabled(false)
        self.m_btnPass:setOpacity(125)
        return 
    end
    self.m_btnPass:setEnabled(bEnable)
    if bEnable then
        self.m_btnPass:setOpacity(255)
    else
        self.m_btnPass:setOpacity(125)
    end
end

function GameViewLayer:onPassOutCard()
    self:getParentNode():sendOutCard({}, true)
    self.m_tabNodeCards[cmd.MY_VIEWID]:reSetCards()
    self.m_onGameControl:setVisible(false)
    -- 提示
    --self.m_spInfoTip:setSpriteFrame("blank.png")
    -- 显示不出
    --local frame = cc.SpriteFrameCache:getInstance():getSpriteFrame("game_nooutcard.png")
    --if nil ~= frame then
        self.m_tabStateSp[cmd.MY_VIEWID]:show()
        self.m_tabStateSp[cmd.MY_VIEWID]:texture('word/game_nooutcard.png')
        --self.m_tabStateSp[cmd.MY_VIEWID]:setSpriteFrame(frame)
    --end

    -- 音效
    helper.music.playPeiyin( self._scene.wPeiyin[cmd.MY_VIEWID], "pass_" .. math.random(1, 2) )
    --ExternalFun.playSoundEffect( "pass" .. math.random(0, 1) .. ".wav", self:getParentNode():GetMeUserItem())
end

function GameViewLayer:getUserNick( viewId )
    if nil ~= self.m_tabUserHead[viewId] then
        return self.m_tabUserHead[viewId].m_userItem.szNickName
    end
    return ""
end

------
-- 扑克代理

-- 扑克状态变更
-- @param[cbCardData]       扑克数据
-- @param[status]           状态(ture:选取、false:非选取)
-- @param[cardsNode]        扑克节点
function GameViewLayer:onCardsStateChange( cbCardData, status, cardsNode )

end

-- 扑克选择
-- @param[selectCards]      选择扑克
-- @param[cardsNode]        扑克节点
function GameViewLayer:onSelectedCards( selectCards, cardsNode )
    -- 出牌对比
    local outCards = self:getParentNode().m_tabCurrentCards
    local outCount = #outCards

    local selectCount = #selectCards

    local selectType = GameLogic:GetCardType(selectCards, selectCount)
    
    local enable = false
    local opacity = 125

    print('outCount and self.m_bCanOutCard', self.m_bCanOutCard, selectType, outCount)

    if 0 == outCount then
        if true == self.m_bCanOutCard and GameLogic.CT_ERROR ~= selectType then
            enable = true
            opacity = 255
        end        
    elseif GameLogic:CompareCard(outCards, outCount, selectCards, selectCount) and true == self.m_bCanOutCard then
        enable = true
        opacity = 255
    end

    if self._scene.isLaizi then
           
        dump(selectCards)
        local cbNum = GameLogic:GetLaiziNum(selectCards, selectCount, self._scene.cbLaiziCardData)
        if cbNum > 0 then
                -- 任意出牌
            local result = nil
            if self._scene.m_nLastOutViewId == cmd.MY_VIEWID then
                result = GameLogic:GetAllTypeCards(selectCards, #selectCards, self._scene.cbLaiziCardData, 0)
            else -- 压牌
                result = GameLogic:GetAllTypeCards(selectCards, #selectCards, self._scene.cbLaiziCardData, self._scene.preCardType)
            end
            
            if result[1] > 0 then
                enable = true
                opacity = 255
            end
        end
    
    end

    self.m_btnOutCard:setEnabled(enable)
    self.m_btnOutCard:setOpacity(opacity)
end

-- 牌数变动
-- @param[outCards]         出牌数据
-- @param[cardsNode]        扑克节点
function GameViewLayer:onCountChange( count, cardsNode, isOutCard )
    isOutCard = isOutCard or false
    local viewId = cardsNode.m_nViewId
    if nil ~= self.m_tabCardCount[viewId] then
        self.m_tabCardCount[cardsNode.m_nViewId]:setString(count .. "")
        self.m_tabCardCount[cardsNode.m_nViewId]:show()
        if self.m_tabCardCountBg[cardsNode.m_nViewId] then
            self.m_tabCardCountBg[cardsNode.m_nViewId]:show()
        end
    end

    if count <= 2 and nil ~= self.m_tabSpAlarm[viewId] and isOutCard then
        --local param = AnimationMgr.getAnimationParam()
        --param.m_fDelay = 0.1
        local name = 'room/game_alarm/game_alarm'
        --self.m_tabSpAlarm[viewId]:stopAllActions()
        self.m_tabSpAlarm[viewId]:removeAllChildren()
        local animation = helper.app.createAnimation(name, 4, 0.4, true, nil, ccui.TextureResType.localType)
        self.m_tabSpAlarm[viewId]:addChild(animation)
        --local rep = cc.RepeatForever:create(animate)
        self.m_tabSpAlarm[viewId]:show()
        --self.m_tabSpAlarm[viewId]:runAction(rep)

        -- 音效
        if count == 1 then
            helper.music.playPeiyin( self._scene.wPeiyin[viewId], "dan_" .. math.random(1, 2)  )
        elseif count == 2 then
            helper.music.playPeiyin( self._scene.wPeiyin[viewId], "shuang_" .. math.random(1, 2)  )
        end
        
        --ExternalFun.playSoundEffect( "common_alert.wav" )
    end
end

------
-- 扑克代理

-- 提示出牌
-- @param[bOutCard]        是否出牌
function GameViewLayer:onPromptOut( bOutCard )
    bOutCard = bOutCard or false
    if bOutCard then
        local promptCard = self:getParentNode().m_tabPromptCards
        local promptCount = #promptCard
        if promptCount > 0 then


            local sel = promptCard
            dump(sel)
            local bIsTurn = false
            if self.lastOutViewID == cmd.INVALID_VIEWID or self.lastOutViewID == cmd.MY_VIEWID then
                bIsTurn = true
            end
            local cbNum = GameLogic:GetLaiziNum(sel, #sel, self._scene.cbLaiziCardData)
            if cbNum > 0 then
                self.result = nil
                --print('BT_OUTCARD', cbNum)
                -- 任意出牌
                if self.lastOutViewID == cmd.INVALID_VIEWID or self.lastOutViewID == cmd.MY_VIEWID then
                    self.result = GameLogic:GetAllTypeCards(sel, #sel, self._scene.cbLaiziCardData, 0, true)
                else
                    self.result = GameLogic:GetAllTypeCards(sel, #sel, self._scene.cbLaiziCardData, self.pre_card_type, true)
                end
                
                
                dump(self.result)
                if self.result[1] >= 1 then
                    if bIsTurn == false then
                        local type = self.result[4][1]
                        if (type ~= self.pre_card_type and type < GameLogic.CT_BOMB_SOFT) then
                            self:onPassOutCard()
                            return 
                        end
                    end

                    -- 扑克对比
                    self:getParentNode():compareWithLastCards(self.result[3][1], cmd.MY_VIEWID)

                    self.m_onGameControl:setVisible(false)
                    local vec = self.m_tabNodeCards[cmd.MY_VIEWID]:outCard(self.result[3][1])
                    self:outCardEffect(cmd.MY_VIEWID, self.result[3][1], vec)
                    self:getParentNode():sendOutCard(self.result[3][1], false, cbNum)
                else
                    self:onPassOutCard()        
                end            
            else
                -- 扑克对比
                dump(sel)
                self:getParentNode():compareWithLastCards(sel, cmd.MY_VIEWID)

                self.m_onGameControl:setVisible(false)
                local vec = self.m_tabNodeCards[cmd.MY_VIEWID]:outCard(sel)
                self:outCardEffect(cmd.MY_VIEWID, sel, vec)
                self:getParentNode():sendOutCard(sel)
            end
        

        --[[
            promptCard = GameLogic:SortCardList(promptCard, promptCount, 0)

            -- 扑克对比
            self:getParentNode():compareWithLastCards(promptCard, cmd.MY_VIEWID)

            local vec = self.m_tabNodeCards[cmd.MY_VIEWID]:outCard(promptCard)
            self:outCardEffect(cmd.MY_VIEWID, promptCard, vec)
            self:getParentNode():sendOutCard(promptCard)
            self.m_onGameControl:setVisible(false)
            --]]
        else
            self:onPassOutCard()
        end
    else
        if 0 >= self.m_promptIdx then
            self.m_promptIdx = #self:getParentNode().m_tabPromptList
        end

        if 0 ~= self.m_promptIdx then
            -- 提示回位
            local sel = self.m_tabNodeCards[cmd.MY_VIEWID]:getSelectCards()
            if #sel > 0 
                and self.m_tabNodeCards[cmd.MY_VIEWID].m_bSuggested
                and #self:getParentNode().m_tabPromptList > 1 then
                self.m_tabNodeCards[cmd.MY_VIEWID]:suggestShootCards(sel, sel)
            end
            -- 提示扑克
            local prompt = self:getParentNode().m_tabPromptList[self.m_promptIdx]
            print("## 提示扑克")
            for k,v in pairs(prompt) do
                print(yl.POKER_VALUE[v])
            end
            print("## 提示扑克")
            if #prompt > 0 then
                local realCards = {}
                local tmpIndex = 1
                for i = 1, #prompt do
                    if prompt[i] > 0x40 and prompt[i] < 0x4E then
                        realCards[i] = GameLogic.cbLaiziCardData[tmpIndex]
                        tmpIndex = tmpIndex + 1
                    else
                        realCards[i] = prompt[i]
                    end
                end
                self.m_tabNodeCards[cmd.MY_VIEWID]:suggestShootCards(prompt, realCards)
            else
                self:onPassOutCard()
            end
            self.m_promptIdx = self.m_promptIdx - 1
        else
            self:onPassOutCard()
        end
    end
end

function GameViewLayer:onGameTrusteeship( bTrusteeship )
    self.m_trusteeshipControl:setVisible(bTrusteeship)
    if bTrusteeship then
        if self.m_bMyCallBanker then
            self.m_bMyCallBanker = false
            self.m_callScoreControl:setVisible(false)
            self:getParentNode():sendCallScore(255)          
        end

        if self.m_bMyOutCards then
            self.m_bMyOutCards = false
            --self:onPassOutCard()
            self:onPromptOut(true)
        end
    end
end

function GameViewLayer:updateClock( clockId, cbTime)
    --print(string.format("%02d", cbTime ))
    self.m_atlasTimer:setString( string.format("%02d", cbTime ))
    if cbTime <= 0 then
        if cmd.TAG_COUNTDOWN_READY == clockId then
            --退出防作弊
            --self:getParentNode():SetGameClock()
            self:getParentNode():getFrame():setEnterAntiCheatRoom(false)
        elseif cmd.TAG_COUNTDOWN_CALLSCORE == clockId then
            -- 私人房无自动托管
            self._scene.is_ready_btn_press_status = false
            if self.m_bMyCallBanker then
                if not GlobalUserItem.bPrivateRoom then
                    self:onGameTrusteeship(true)
                end  
            end
        elseif cmd.TAG_COUNTDOWN_OUTCARD == clockId then
            -- 私人房无自动托管
            print('self.m_bMyOutCards is , ', self.m_bMyOutCards)
            if self.m_bMyOutCards then
                if not GlobalUserItem.bPrivateRoom then
                    self:onGameTrusteeship(true)
                end
            end
        end
    end
end

function GameViewLayer:OnUpdataClockView( viewId, cbTime )
    self.m_spTimer:setVisible(cbTime ~= 0)
    self.m_atlasTimer:setString( string.format("%02d", cbTime ))

    if self:getParentNode():IsValidViewID(viewId) then
        self.m_spTimer:setPosition(self.m_tabTimerPos[viewId])
    end
end
------------------------------------------------------------------------------------------------------------
--更新
------------------------------------------------------------------------------------------------------------

-- 文本聊天
function GameViewLayer:onUserChat(chatdata, viewId)
    local roleItem = self.m_tabUserHead[viewId]
    if nil ~= roleItem then
        roleItem:textChat(chatdata.szChatString)
    end
end

-- 表情聊天
function GameViewLayer:onUserExpression(chatdata, viewId)
    local roleItem = self.m_tabUserHead[viewId]
    if nil ~= roleItem then
        roleItem:browChat(chatdata.wItemIndex)
    end
end

-- 用户更新
function GameViewLayer:OnUpdateUser(viewId, userItem, bLeave)
    print(" update user " .. viewId)
    if bLeave then
        local roleItem = self.m_tabUserHead[viewId]
        if nil ~= roleItem then
            roleItem:removeFromParent()
            self.m_tabUserHead[viewId] = nil
        end
        self:onUserReady(viewId, false)
    end
    local bHide = ((table.nums(self.m_tabUserHead)) == (self:getParentNode():getFrame():GetChairCount()))
    --[[
    if not GlobalUserItem.bPrivateRoom then
        self.m_btnInvite:setVisible(not bHide)
        self.m_btnInvite:setEnabled(not bHide)
    end    
    self.m_btnInvite:setVisible(false)
    self.m_btnInvite:setEnabled(false)
--]]
    local head_bg = self.m_userinfoControl:child('head_bg_' .. viewId)
    if not head_bg then
        return 
    end
    if nil == userItem then 
        -- 用户离开的时候调用
        self:onUserReady(viewId, false)
        if head_bg and viewId ~= 2 then
            local score_atlas = head_bg:child('score_atlas')
            --local game_gold = head_bg:child('game_gold')
            local name = head_bg:child('name')
            local sp_head = head_bg:child('sp_head')
            if sp_head then
                --sp_head:hide()
                sp_head:opacity(0)
            end
            if score_atlas then
                score_atlas:setString("")
            end
            if name then
                name:setString("")
            end
        end
        return
    end
    self.m_tabUserItem[viewId] = userItem

    local bReady = userItem.cbUserStatus == yl.US_READY
    self:onUserReady(viewId, bReady)
    
    if head_bg and viewId ~= 2 then
        local score_atlas = head_bg:child('score_atlas')
        --local game_gold = head_bg:child('game_gold')
        local name = head_bg:child('name')
        if score_atlas then
            local str = helper.str.makeFormatNum(userItem.nTableScore, 1)
            score_atlas:setString(str)
        end
        if name then
            name:setString(userItem.szNickName)
        end
    end

    local touxiang = head_bg:child('head_1')
    touxiang:hide()

    --头像
	local head = head_bg:child('sp_head')
	if not userItem then
		if head then
            --head:setVisible(false)
            head:opacity(0)
		end
		
	else
		--头像
        if not head then
            local x, y = touxiang:pos()
			head = PopupHead:create(self, userItem, 100, 0)
			head:pos(x + 50, y + 50)			--初始位置
			head:setName('sp_head')
			head:addTo( head_bg)
		else
			head:updateHead(userItem)
		end
        head:opacity(255)
    end

    --[[
    if nil == self.m_tabUserHead[viewId] then
        local roleItem = GameRoleItem:create(userItem, viewId)
        roleItem:setPosition(self.m_tabUserHeadPos[viewId])
        self.m_tabUserHead[viewId] = roleItem
        self.m_userinfoControl:addChild(roleItem)
    else
        self.m_tabUserHead[viewId].m_userItem = userItem
        self.m_tabUserHead[viewId]:updateStatus()
    end
    --]]

    if cmd.MY_VIEWID == viewId then
        self:reSetUserInfo()
    end
end

function GameViewLayer:onUserReady(viewId, bReady)
    --用户准备
    if bReady then
        local readySp = self.m_tabReadySp[viewId]
        if nil ~= readySp then
            readySp:setVisible(true)
        end
    else
        local readySp = self.m_tabReadySp[viewId]
        if nil ~= readySp then
            readySp:setVisible(false)
        end
    end
end

function GameViewLayer:onGetCellScore( score )
    score = score or 0
    local str = ""
    if score < 0 then
        str = "." .. score
    else
        str = "" .. score        
    end 
    if string.len(str) > 11 then
        str = string.sub(str, 1, 11)
        str = str .. "///"
    end  
    self.difen = score
    print('difen is ', self.difen)
    self.m_atlasDiFeng:setString(str) 
end

function GameViewLayer:onGetGameFree()
    if false == self:getParentNode():getFrame().bEnterAntiCheatRoom then
        self.m_btnReady:setEnabled(true)
        self.m_btnReady:setVisible(true)
    end
end

function GameViewLayer:onGameStart()
    self.m_nMaxCallScore = 0
    self.m_textGameCall:setString("1")
    for k,v in pairs(self.m_tabBankerCard) do
        v:setVisible(false)
        v:setCardValue(0)
    end
    --self.m_spInfoTip:setSpriteFrame("blank.png")
    for k,v in pairs(self.m_tabStateSp) do
        v:stopAllActions()
        v:hide()
        --v:setSpriteFrame("blank.png")
    end

    for k,v in pairs(self.m_tabCardCount) do
        v:setString("")
        v:hide()
    end
    for k, v in pairs(self.m_tabCardCountBg) do
        v:hide()
    end
    self.m_promptIdx = 0

    for i = 1, cmd.PLAYER_COUNT do
        local name = 'head_bg_' .. tostring(i)
        local head_bg = self.m_userinfoControl:child(name)
        local head = head_bg:child('head')
        if head then
            head:hide()
        end
    end
end

-- 获取到扑克数据
-- @param[viewId] 界面viewid
-- @param[cards] 扑克数据
-- @param[bReEnter] 是否断线重连
-- @param[pCallBack] 回调函数
function GameViewLayer:onGetGameCard(viewId, cards, bReEnter, pCallBack)
    if bReEnter then
        print(viewId)
            self.m_tabNodeCards[viewId]:updateCardsNode(cards, (viewId == cmd.MY_VIEWID), false)
    else
        if nil ~= pCallBack then
            pCallBack:retain()
        end
        local call = cc.CallFunc:create(function()
            -- 非自己扑克
            local empTyCard = GameLogic:emptyCardList(cmd.NORMAL_COUNT)
            self.m_tabNodeCards[cmd.LEFT_VIEWID]:updateCardsNode(empTyCard, false, true)
            empTyCard = GameLogic:emptyCardList(cmd.NORMAL_COUNT)
            self.m_tabNodeCards[cmd.RIGHT_VIEWID]:updateCardsNode(empTyCard, false, true)

            -- 自己扑克
            self.m_tabNodeCards[cmd.MY_VIEWID]:updateCardsNode(cards, true, true, pCallBack)

            -- 庄家扑克
            -- 50 525
            -- 50 720
            for k,v in pairs(self.m_tabBankerCard) do
                v:setVisible(true)
            end
        end)
        local call2 = cc.CallFunc:create(function()
            -- 音效
            if GlobalUserItem.bVoiceAble then
                AudioEngine.playEffect('sound/dispatch.wav')
            end
            --ExternalFun.playSoundEffect( "dispatch.wav" )
        end)
        local seq = cc.Sequence:create(call2, cc.DelayTime:create(0.3), call)
        self:stopAllActions()
        self:runAction(seq)
    end
end

-- 获取到玩家叫分
-- @param[callViewId]   当前叫分玩家
-- @param[lastViewId]   上个叫分玩家
-- @param[callScore]    当前叫分分数
-- @param[lastScore]    上个叫分分数
-- @param[bReEnter]     是否断线重连
function GameViewLayer:onGetCallScore( callViewId, lastViewId, callScore, lastScore, bReEnter, bIsEndSendCard )
    bReEnter = bReEnter or false

    if 255 == lastScore then
        print("不叫")
        -- 不叫
        --local frame = cc.SpriteFrameCache:getInstance():getSpriteFrame("game_tips_callscore0.png")
        --if nil ~= frame then
            self.m_tabStateSp[lastViewId]:texture('word/game_tips_callscore0.png')
            self.m_tabStateSp[lastViewId]:show()
        --end    
    elseif lastScore > 0 and lastScore < 4 then
        local param = AnimationMgr.getAnimationParam()
        param.m_fDelay = 0.1
        param.m_strName = 'call_point' .. lastScore

        -- 播放叫分动画
        if bReEnter then
            --local callscore = AnimationMgr.getAnimate(param)
            self.m_tabStateSp[lastViewId]:texture(param.m_strName .. '_3.png')
            self.m_tabStateSp[lastViewId]:show()
                --local frames = callscore:getAnimation():getFrames()
               -- if #frames > 0 then
                  --  self.m_tabStateSp[lastViewId]:setSpriteFrame(frames[#frames]:getSpriteFrame())
                  --  self.m_tabStateSp[lastViewId]:show()
               -- end
        else
            local callback = cc.RemoveSelf:create(true)
            local call_sprite = helper.app.createAnimation('room/call_point/call_point',5,0.5,false,callback, ccui.TextureResType.localType)
            --param.m_strName = Define.CALLSCORE_ANIMATION_KEY
            call_sprite:pos(self.m_tabStateSp[lastViewId]:pos())
            self.m_rootLayer:addChild(call_sprite)
           -- local animate = AnimationMgr.getAnimate(param)
            --param.m_strName = lastScore .. "_score_key"
            --param.m_strName = 'room/call_point/call_point' .. lastScore
            local animation_name = 'call_point' .. lastScore
            animation_name = 'room/'.. animation_name .. '/' .. animation_name
            --local callscore = AnimationMgr.getAnimate(param)
            local callscore_sprite = helper.app.createAnimation(animation_name,3, 0.3,false,cc.RemoveSelf:create(true), ccui.TextureResType.localType)
            callscore_sprite:pos(self.m_tabStateSp[lastViewId]:pos())
            self.m_rootLayer:addChild(callscore_sprite)
            --if callScore then
                local call = cc.CallFunc:create(function()
                    --local frames = callscore:getAnimation():getFrames()
                    --if #frames > 0 then
                        --self.m_tabStateSp[lastViewId]:setSpriteFrame(frames[#frames]:getSpriteFrame())
                        self.m_tabStateSp[lastViewId]:texture(animation_name .. '_2.png')
                        self.m_tabStateSp[lastViewId]:show()
                   -- end
                end)
                local seq = cc.Sequence:create(cc.DelayTime:create(0.5), call)
                self.m_tabStateSp[lastViewId]:stopAllActions()
                self.m_tabStateSp[lastViewId]:show()
                self.m_tabStateSp[lastViewId]:runAction(seq)
           -- end
            
        end
    end

    lastScore = (lastScore > 3) and 0 or lastScore
    if lastScore > self.m_nMaxCallScore then
        self.m_nMaxCallScore = lastScore
    end
    if cmd.MY_VIEWID ~= lastViewId and not bIsEndSendCard then
        --local headitem = self.m_tabUserHead[lastViewId]
        --if nil ~= headitem then
            -- 音效 
            local sound_path = ''
            if lastScore == 2 then
                sound_path = 'two_point'
            elseif lastScore == 3 then
                sound_path = 'three_point'
            elseif lastScore == 1 then
                sound_path = 'one_point'
            else
                sound_path = 'no'
            end
            print('lastViewId ', sound_path)
            helper.music.playPeiyin( self._scene.wPeiyin[lastViewId], sound_path)
            --ExternalFun.playSoundEffect( "cs" .. lastScore .. ".wav", headitem.m_userItem)
        --end
    end

    self.m_bMyCallBanker = (cmd.MY_VIEWID == callViewId)
    if cmd.MY_VIEWID == callViewId and not self:getParentNode().m_bRoundOver then
        -- 托管不叫
        if self.m_trusteeshipControl:isVisible() then
            self.m_callScoreControl:setVisible(false)
            self:getParentNode():sendCallScore(0)
        else
            --self.m_spInfoTip:setSpriteFrame("blank.png")
            -- 计算叫分
            local maxCall = self.m_nMaxCallScore + 1
            for i = 2, #self.m_tabCallScoreBtn do
                local btn = self.m_tabCallScoreBtn[i]
                btn:setEnabled(true)
                btn:setOpacity(255)
                if i <= maxCall  then
                    btn:setEnabled(false)
                    btn:setOpacity(125)
                end
            end
            self.m_callScoreControl:setVisible(true)
        end
    else
        if not bReEnter and not self:getParentNode().m_bRoundOver then
            -- 等待叫分
            self.m_spInfoTip:setPosition(display.width * 0.5, 375)
            --self.m_spInfoTip:setSpriteFrame("game_tips_01.png")
            self.m_spInfoTip:texture("word/game_tips_01.png")
        end        
    end
end

-- 获取到庄家信息
-- @param[bankerViewId]         庄家视图id
-- @param[cbBankerScore]        庄家分数
-- @param[bankerCards]          庄家牌
-- @param[bReEnter]             是否断线重连
function GameViewLayer:onGetBankerInfo(bankerViewId, cbBankerScore, bankerCards, bReEnter)
    bReEnter = bReEnter or false
    self.m_bMyCallBanker = false
    -- 更新庄家扑克
    if 3 == #bankerCards then
        for k,v in pairs(bankerCards) do
            self.m_tabBankerCard[k]:setVisible(true)
            self.m_tabBankerCard[k]:setCardValue(v)
        end
        if self._scene.isLaizi then
            self.m_tabBankerCard[4]:setVisible(true)
            self.m_tabBankerCard[4]:setCardValue(self._scene.cbLaiziCardData)
        end
    end
    -- 叫分
    self.m_textGameCall:setString(cbBankerScore) 
    self.cbBankerScore = cbBankerScore

    -- 庄家切换
    for k,v in pairs(self.m_tabUserHead) do
        --v:switeGameState(k == bankerViewId)

        self.m_tabStateSp[k]:stopAllActions()
        self.m_tabStateSp[k]:hide()
    end

    if false == bReEnter then
        -- 庄家增加牌
        local handCards = self.m_tabNodeCards[bankerViewId]:getHandCards()
        local count = #handCards
        if bankerViewId == cmd.MY_VIEWID then
            handCards[count + 1] = bankerCards[1]
            handCards[count + 2] = bankerCards[2]
            handCards[count + 3] = bankerCards[3]
            GameLogic:SortLaiziCardList(handCards)
            --handCards = GameLogic:SortCardList(handCards, cmd.MAX_COUNT, 0)
        else
            handCards[count + 1] = 0
            handCards[count + 2] = 0
            handCards[count + 3] = 0
        end
        self.m_tabNodeCards[bankerViewId]:addCards(bankerCards, handCards)
    end

    -- 提示
    --self.m_spInfoTip:setSpriteFrame("blank.png")
end

-- 用户出牌
-- @param[curViewId]        当前出牌视图id
-- @param[lastViewId]       上局出牌视图id
-- @param[lastOutCards]     上局出牌
-- @param[bRenter]          是否断线重连
function GameViewLayer:onGetOutCard(curViewId, lastViewId, lastOutCards, bReEnter)
    bReEnter = bReEnter or false

    self.m_bMyOutCards = (curViewId == cmd.MY_VIEWID)
    if nil ~= self.m_tabStateSp[curViewId] then
        self.m_tabStateSp[curViewId]:hide()
        --self.m_tabStateSp[curViewId]:setSpriteFrame("blank.png")
    end

    -- 自己出牌
    if curViewId == cmd.MY_VIEWID then
        self:ShowCardSel(false, nil)
        -- 托管
        if self.m_trusteeshipControl:isVisible() then
            self:onPromptOut(true)
        else
            -- 移除上轮出牌
            self.m_outCardsControl:removeChildByTag(curViewId)

            self.m_onGameControl:setVisible(true)

            self.m_btnOutCard:setEnabled(false)
            self.m_btnOutCard:setOpacity(125)

            local promptList = self:getParentNode().m_tabPromptList
            self.m_bCanOutCard = (#promptList > 0)

            -- 出牌控制
            if not self.m_bCanOutCard then
                --self.m_spInfoTip:setSpriteFrame("game_tips_00.png")
                --self.m_spInfoTip:texture("word/game_tips_00.png")
                --self.m_spInfoTip:setPosition(display.width * 0.5, 160)
            else
                local sel = self.m_tabNodeCards[cmd.MY_VIEWID]:getSelectCards()
                local selCount = #sel
                if selCount > 0 then
                    local selType = GameLogic:GetCardType(sel, selCount)
                    if GameLogic.CT_ERROR ~= selType then
                        local lastOutCount = #lastOutCards
                        if lastOutCount == 0 then
                            self.m_btnOutCard:setEnabled(true)
                            self.m_btnOutCard:setOpacity(255)
                        elseif lastOutCount > 0 and GameLogic:CompareCard(lastOutCards, lastOutCount, sel, selCount) then
                            self.m_btnOutCard:setEnabled(true)
                            self.m_btnOutCard:setOpacity(255)
                        end
                    end
                end
                --self.m_spInfoTip:setSpriteFrame("blank.png")
            end
        end
    end
    print('lastViewId，is ', lastViewId)
    -- 出牌消息
    if lastViewId ~= cmd.MY_VIEWID and #lastOutCards > 0 then
        print('show out card begin')
        local vec = self.m_tabNodeCards[lastViewId]:outCard(lastOutCards, bReEnter)
        self:outCardEffect(lastViewId, lastOutCards, vec)
    end
end

function GameViewLayer:setBankerShape( bankerView, curView )
    -- body
    print('bankerView and curView is 1111111111111111111111111111111111', bankerView, curView)
    for i = 1, cmd.PLAYER_COUNT do
        local isBanker = false
        if bankerView == i then
            isBanker = true
        end
        local png = "room/game_role12.png"
        if isBanker then
            png = "room/game_role11.png"
        end
       
        local name = 'head_bg_' .. tostring(i)
        local head_bg = self.m_userinfoControl:child(name)
        local sp_head = head_bg:child('sp_head')
        if sp_head then
            --sp_head:hide()
            sp_head:opacity(0)
        end
        local head = head_bg:child('head')
        print('png is ', png, head, name, head:getName())
        if head then
            head:show()
            head:setLocalZOrder(10)
            --head:loadTexture(png)
            head:texture(png)
        end
    end
    self.m_spInfoTip:hide()
end

-- 用户pass
-- @param[passViewId]       放弃视图id
function GameViewLayer:onGetPassCard( passViewId )
    if passViewId ~= cmd.MY_VIEWID then
        --local headitem = self.m_tabUserHead[passViewId]
        --if nil ~= headitem then
        -- 音效
        helper.music.playPeiyin( self._scene.wPeiyin[passViewId], "pass_" .. math.random(1, 2)  )
            --ExternalFun.playSoundEffect( "pass" .. math.random(0, 1) .. ".wav", headitem.m_userItem)
        --end        
    end
    self.m_outCardsControl:removeChildByTag(passViewId)

    -- 显示不出
    --local frame = cc.SpriteFrameCache:getInstance():getSpriteFrame("game_nooutcard.png")
    --if nil ~= frame then
        --self.m_tabStateSp[passViewId]:setSpriteFrame(frame)
        self.m_tabStateSp[passViewId]:texture('word/game_nooutcard.png')
        self.m_tabStateSp[passViewId]:show()
    --end
end

function GameViewLayer:runResultNum(scorelist)
    for i = 1, cmd.PLAYER_COUNT do
        local viewID = self._scene:SwitchViewChairID(i - 1)
        local name = 'head_bg_' .. tostring(viewID)
        local head_bg = self.m_userinfoControl:child(name)
        local result_num = head_bg:child('result_num')
        result_num:setLocalZOrder(10)
        result_num:show()
        local score = scorelist[i]
        local str = ''
        local png = 'room/addScoreNum.png'
        if score > 0 then
            str = "/" .. score
        else
            score =  - score
            str = "/" .. score
            png = 'room/subScoreNum.png'
        end
        result_num:setProperty(str, png, 22, 30, '/')
        local x, y = result_num:pos()
        local mv_ac = cc.MoveBy:create(1.5, cc.p(0, 100))
        local call_back = cc.CallFunc:create(
            function (  )
                result_num:hide()
                result_num:pos(x, y)
            end
        )
        result_num:runAction(cc.Sequence:create(mv_ac, call_back))
    end
end

-- 游戏结束
function GameViewLayer:onGetGameConclude( rs )
    -- 界面重置
    self:reSetGame()

    self:stopAllActions()

    -- 取消托管
    self.m_trusteeshipControl:setVisible(false)

    -- 显示准备
    self.m_btnReady:setEnabled(false)
    self.m_btnReady:setVisible(false)

    --self.m_spInfoTip:setSpriteFrame("blank.png")
    self.m_spInfoTip:setPosition(display.width * 0.5, 375)

    for k,v in pairs(self.m_tabBankerCard) do
        v:setVisible(false)
        v:setCardValue(0)
    end
    --self.m_spInfoTip:setSpriteFrame("blank.png")
    for k,v in pairs(self.m_tabStateSp) do
        v:stopAllActions()
        v:hide()
        --v:setSpriteFrame("blank.png")
    end

    for k,v in pairs(self.m_tabCardCount) do
        v:setString("")
        v:hide()
    end
    for k, v in pairs(self.m_tabCardCountBg) do
        v:hide()
    end

    -- 结算
    --[[
    if nil == self.m_resultLayer then
        self.m_resultLayer = GameResultLayer:create(self)
        self:addToRootLayer(self.m_resultLayer, TAG_ZORDER.RESULT_ZORDER)
    end
    if not GlobalUserItem.bPrivateRoom then
        self:runAction(cc.Sequence:create(cc.DelayTime:create(2), cc.CallFunc:create(function()
            --self.m_resultLayer:showGameResult(rs)
        end)))        
    else
        --self.m_resultLayer:showGameResult(rs)
    end    
    --]]

    self:runAction(cc.Sequence:create(cc.DelayTime:create(2), cc.CallFunc:create(function()
        local view = helper.app.getFromScene('subGameResultLayer')
        if view then
            view:show()
        end
    end)))    
    self.m_rootLayer:removeChildByName("__effect_ani_name__")
end

-- 初始化玩家
function GameViewLayer:initPlayers()
    -- 房间人数对应的座位显示
    local player_visibles = {true, true, true, true}
    local renshu = PassRoom:getInstance():getChairCount()
    print( '人数:', renshu)

end

------------------------------------------------------------------------------------------------------------
--更新
------------------------------------------------------------------------------------------------------------

return GameViewLayer
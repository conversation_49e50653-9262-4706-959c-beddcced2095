-------------------------------------------------------------------------------
--  创世版1.0
--  局结算(小结算)
--  @date 2017-06-19
--  @auth woodoo
-------------------------------------------------------------------------------
local ExternalFun = cs.app.client('external.ExternalFun')
local PopupHead = cs.app.client('system.PopupHead')
local cmd = cs.app.game('room.CMD_Game')
local GameLogic = cs.app.game('room.GameLogic')


local GameResultLayer = class("GameResultLayer", function(scene)
    return helper.app.loadCSB('GameResultLayer.csb')
end)


-------------------------------------------------------------------------------
-- 构造方法 scene:gameview
-------------------------------------------------------------------------------
function GameResultLayer:ctor(scene, game_layer, user_items, is_jiesan)
    self._scene = scene
    self._game_layer = game_layer
    self._user_items = user_items
    self._is_jiesan = is_jiesan
    self:setName('GameResultLayer')
    self:child('template'):hide()
    --self:child('Image_2'):hide()
    
    self:child('btn_share'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnShare) )
    self:child('btn_continue'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnContinue) )

  
    self:showResult()
    --helper.app.getFromScene('GameResultLayer')
end


-------------------------------------------------------------------------------
-- 分享按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnShare(sender)
    --helper.pop.shareImage()
     -- body
    if self._scene and self._scene._scene and self._scene._scene.onQueryExitGame then
        self._scene._scene:onQueryExitGame()
        if self._scene.btStart then
            self._scene.btStart:show()
        end
    end
    self:removeFromParent()
end


-------------------------------------------------------------------------------
-- 继续游戏按钮点击
-------------------------------------------------------------------------------
function GameResultLayer:onBtnContinue(sender)
    --self:doClose()
    if self._scene and self._scene.onButtonClickedEvent then
        self._scene:onButtonClickedEvent(4)
    else
        PassRoom:getInstance():onLoginRoom(GlobalUserItem.dwCurServerID)
    end
    
    self:removeFromParent()
end

-------------------------------------------------------------------------------
-- 显示列表
-------------------------------------------------------------------------------
function GameResultLayer:showResult()
    -- 先显示界面元素
    for i, child in ipairs(self:getChildren()) do
        child:show()
    end

    local width = 40
    print('gameResultLyaer player num is ', self._scene.player_num)

    
    local template = self:child('template')
    local panel_offset_y = 85    -- 两行的锚点直接距离
    local panel_start_y = 92 + (5 - #self._user_items) * panel_offset_y / 2 -- 不足4行，修正到中间去
    for i = 1, cmd.GAME_PLAYER do repeat
        if self._user_items[i] == nil then
            break
        end
        if i <= self._scene.player_num then
            local chair_id = i - 1
            local panel = template:clone():show():addTo(self)

            if self._scene._scene:GetMeChairID() == i - 1 then
                local bg_win = panel:child('bg_win')
                bg_win:texture('room/xiaojiesuandiban01.png')
            end
            

            
            panel:pos(display.width / 2 + 568, panel_start_y)
            panel:runAction( cc.Sequence:create(
                cc.DelayTime:create((i - 1) * 0.1),
                cc.EaseBackOut:create( cc.MoveTo:create(0.3, cc.p(display.width / 2, panel_start_y)) )
            ) )
            panel_start_y = panel_start_y + panel_offset_y

            -- 头像
            local panel_avator = panel:child('panel_avator')
            local head = PopupHead:create(self, self._user_items[i], 58, 58)
            head:pos(panel_avator:size().width/2, panel_avator:size().height/2):addTo(panel_avator)

            -- 房主
            --if chair_id ~= 0 then
                panel:child('img_fangzhu'):removeFromParent()
            --end

            -- 输赢积分
            local score = self._game_layer.lGameScore[i]
            local lbl_score = panel:child('label_score')
            lbl_score:setString( (score >= 0 and '+' or '') .. score )
            if score < 0 then
                lbl_score:setColor(cc.c3b(0xD7, 0x43, 0x29))
            end

            local score_total = 0
		    for j = 1, 3 do
		    	score_total = score_total + self._game_layer.sandaoScore[i][j]
		    end		   

            -- 牌分
            --[[
            local score = self._game_layer.lGameScore[i]
            local lbl_score = panel:child('label_pai_score')
            lbl_score:setString( (score_total >= 0 and '+' or '') .. score_total )
            if score < 0 then
                lbl_score:setColor(cc.c3b(0xD7, 0x43, 0x29))
            end
            
            -- 附加分
            score_total = self._game_layer.lGameScore[i] - score_total
            local lbl_score = panel:child('label_addition_score')
            lbl_score:setString( (score_total >= 0 and '+' or '') .. score_total )
            if score < 0 then
                lbl_score:setColor(cc.c3b(0xD7, 0x43, 0x29))
            end
--]]
            -- 昵称
            panel:child('label_name'):setString(self._user_items[i].szNickName or '')

            local card = panel:child('card')
            if self._game_layer.cbGiveup[i] == 1 then
                card:texture('card/card403.png')
            else
                self:setCardTexture(card, self._game_layer.cbCardData[i][1])
            end
            
            local x, y = card:pos()
            self:CreateDaoCards(i, 2, 3, 1, card, cc.p(x, y), lbl_score, panel)
            self:CreateDaoCards(i, 4, 6, 2, card, cc.p(x + 30 * 3.5, y), lbl_score, panel)
            self:CreateDaoCards(i, 7, 9, 3, card, cc.p(x + 30 * 7, y), lbl_score, panel)

        end
    until true
    end
    template:removeFromParent()
    self:hide()
end

function GameResultLayer:CreateDaoCards(user_id, start_index, end_index, card_dao, card, start_pos, lbl, parent)
    local x, y = start_pos.x, start_pos.y
    --self:addMaCardSign(card,value,color)
    for j = start_index, end_index do
        local clone = card:clone()
        clone:pos(x + 30 * (j - 1), y)
        if self._game_layer.cbGiveup[user_id] == 1 then
            clone:texture('card/card403.png')
        else
            self:setCardTexture(clone, self._game_layer.cbCardData[user_id][j])
        end
        
        parent:addChild(clone)
    end

    local final_x = x + 30 * end_index + 10

     

    local img_card_type = card:clone()
    img_card_type:ignoreContentAdaptWithSize(true)
    parent:addChild(img_card_type)
    if card_dao == 1 then
        img_card_type:pos(x + 30, y - 30)
        --final_x = final_x + 10
    elseif card_dao == 2 then
        img_card_type:pos(x + (math.floor(end_index / 2)  + 1) * 30, y - 30)
        --final_x = final_x + 10
    else
        img_card_type:pos(x + 10 * 30 - 91, y - 30)
        --final_x = final_x + 10
    end
    --
    
    local path 
    if self._game_layer.cbUseSpecialType[user_id] == 1 then
        local type = self._game_layer.cbSpeicalType[user_id] + GameLogic.OX_VALUEO
        path = self._scene:getSpeicalTypePngPath(type)
    else
        local card_type = self._game_layer.sandaoType[user_id][card_dao]
        path = self._scene.typeMapPng[card_type + 1]
    end
    print('path', path, 'type is ', self._game_layer.sandaoType[user_id][card_dao])
    if self._game_layer.cbGiveup[user_id] == 1 then
        img_card_type:texture('word/t_qipai.png')
    else
        img_card_type:texture(path)
    end
    
    if self._is_jiesan == 1 then
        img_card_type:hide()
    end

    local lbl_score = lbl:clone()
    lbl_score:setColor(display.COLOR_WHITE)
    lbl_score:setTextColor(display.COLOR_WHITE)
    lbl_score:addTo(parent)
    lbl_score:pos(final_x + 20, y)
    local score = self._game_layer.sandaoScore[user_id][card_dao]
    lbl_score:setString((score >= 0 and '+' or '') .. score)
    --if score < 0 then
        
    --end
end

function GameResultLayer:addMaCardSign( card, value, color )
	card:removeChildByName('ma_card_sign')
        
    print('self._game_layer.maCardValue ', self._game_layer.maCardValue, value)
	if self._game_layer.maCardValue ~= -1 and self._game_layer.maCardValue == value and 
	color == 3  then
    	local image = ccui.ImageView:create()
    	image:texture('room/ma_card_sign.png')
		image:setName('ma_card_sign')
        local size = card:size()
        --print('size.height is ', size.height)
		image:pos(20, size.height - 71)
    	card:addChild(image)
    end
end

function GameResultLayer:setCardTexture(card, data)
    local value = GameLogic:getCardValue(data)
    local color = GameLogic:getCardColor(data)
    local png = string.format('card/card%d%02d.png', color, value)
    if self._is_jiesan == 1 then
        png = 'card/card403.png'
    end
    --self:addMaCardSign(card, value, color)
	card:texture(png)
end


-------------------------------------------------------------------------------
-- 关闭界面
-------------------------------------------------------------------------------
function GameResultLayer:doClose()

    if self.m_parent and self.m_parent.onClickReady then
        self.m_parent:onClickReady()
    else
        PassRoom:getInstance():onLoginRoom(GlobalUserItem.dwCurServerID)
    end

    if yl.IS_REPLAY_MODEL then
       helper.app.getFromScene('game_room_layer'):onExitRoom()
    else
       local is_room_ended = PassRoom:getInstance().m_bRoomEnd
       if not is_room_ended then
            self._scene.btStart:setVisible(false)
            --self._scene:onButtonClickedEvent('btn_start')
            self._game_layer:onStartGame()
       else
            local room_result_layer = helper.app.getFromScene('subRoomResultLayer')
            if room_result_layer then
                room_result_layer:show()
            else
                GlobalUserItem.bWaitQuit = false
                local game_layer = self._scene._scene
                if game_layer then
                    game_layer:onExitRoom()
                end
            end
       end
    end
    self:removeFromParent()
end


return GameResultLayer
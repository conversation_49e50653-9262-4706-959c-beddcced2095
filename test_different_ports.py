#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import time
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def generate_sign(params, channel_key):
    """生成签名"""
    sorted_keys = sorted(params.keys())
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    sign_str = param_str + channel_key
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()

def test_different_ports():
    """测试不同端口的服务器"""
    print("=== 测试不同端口的服务器 ===")
    
    # 从包配置中发现的不同端口
    ports = [8603, 8606, 8607]
    
    # 基础参数
    params = {
        'uid': '123456',
        'phone': '13800138000',
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sign = generate_sign(params, channel_key)
    params['sign'] = sign
    
    for port in ports:
        print(f"\n测试端口 {port}:")
        
        # 测试HTTP
        http_url = f'http://lhmj.tuo3.com.cn:{port}/admin/api/v1/user/get_verify_code'
        print(f"  HTTP URL: {http_url}")
        
        try:
            response = requests.post(http_url, data=params, timeout=30)
            print(f"  状态码: {response.status_code}")
            print(f"  响应长度: {len(response.text)}")
            
            if response.status_code == 200:
                print(f"  响应内容: {response.text}")
                try:
                    json_data = response.json()
                    if json_data.get('code') == 0:
                        print(f"  ✅ 端口 {port} 成功!")
                        return port
                    else:
                        print(f"  ⚠️ 端口 {port} 返回错误: {json_data.get('msg')}")
                except:
                    print(f"  ⚠️ 端口 {port} 非JSON响应")
            elif response.status_code == 502:
                print(f"  ❌ 端口 {port} - 502 Bad Gateway")
            else:
                print(f"  ❌ 端口 {port} - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 端口 {port} 请求失败: {e}")
        
        # 测试HTTPS
        https_url = f'https://lhmj.tuo3.com.cn:{port}/admin/api/v1/user/get_verify_code'
        print(f"  HTTPS URL: {https_url}")
        
        try:
            response = requests.post(https_url, data=params, timeout=30, verify=False)
            print(f"  HTTPS 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  HTTPS 响应: {response.text}")
                try:
                    json_data = response.json()
                    if json_data.get('code') == 0:
                        print(f"  ✅ HTTPS 端口 {port} 成功!")
                        return port
                except:
                    pass
                    
        except Exception as e:
            print(f"  ❌ HTTPS 端口 {port} 失败: {e}")
    
    return None

def test_alternative_paths():
    """测试其他可能的路径"""
    print("\n=== 测试其他可能的路径 ===")
    
    base_urls = [
        'http://lhmj.tuo3.com.cn',
        'http://lhmj.tuo3.com.cn:8603',
        'http://lhmj.tuo3.com.cn:8606', 
        'http://lhmj.tuo3.com.cn:8607'
    ]
    
    paths = [
        '/admin/api/v1/user/get_verify_code',
        '/api/v1/user/get_verify_code',
        '/user/get_verify_code',
        '/get_verify_code',
        '/sms/send',
        '/sms/get_verify_code'
    ]
    
    # 基础参数
    params = {
        'uid': '123456',
        'phone': '13800138000',
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sign = generate_sign(params, channel_key)
    params['sign'] = sign
    
    for base_url in base_urls:
        for path in paths:
            url = base_url + path
            print(f"\n测试: {url}")
            
            try:
                response = requests.post(url, data=params, timeout=10)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"  响应: {response.text[:100]}...")
                    try:
                        json_data = response.json()
                        if json_data.get('code') == 0:
                            print(f"  ✅ 找到正确的端点: {url}")
                            return url
                        else:
                            print(f"  ⚠️ 错误: {json_data.get('msg')}")
                    except:
                        print("  ⚠️ 非JSON响应")
                elif response.status_code == 404:
                    print("  ❌ 404 Not Found")
                elif response.status_code == 502:
                    print("  ❌ 502 Bad Gateway")
                else:
                    print(f"  ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 请求失败: {str(e)[:50]}...")
    
    return None

if __name__ == '__main__':
    print("开始测试不同的服务器配置...")
    
    # 测试不同端口
    working_port = test_different_ports()
    
    # 测试不同路径
    working_url = test_alternative_paths()
    
    if working_port or working_url:
        print(f"\n✅ 找到可用的配置!")
        if working_port:
            print(f"可用端口: {working_port}")
        if working_url:
            print(f"可用URL: {working_url}")
    else:
        print("\n❌ 未找到可用的服务器配置")
        print("\n🔍 建议检查:")
        print("1. 服务器是否正在维护")
        print("2. 是否有新的服务器地址")
        print("3. 联系技术支持获取最新配置")

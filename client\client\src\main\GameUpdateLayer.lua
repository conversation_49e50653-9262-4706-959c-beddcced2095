-------------------------------------------------------------------------------
--  创世版1.0
--  游戏更新
--  @date 2017-12-04
--  @auth woodoo
-------------------------------------------------------------------------------
local ClientUpdate = cs.app.client('app.controllers.ClientUpdate')


local GameUpdateLayer = class("GameUpdateLayer", ccui.Layout)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function GameUpdateLayer:ctor(update_config, finish_callback)
    self:enableNodeEvents()
    self.m_update_config = update_config
    self.m_finish_callback = finish_callback

    -- 载入主UI
    local main_node = helper.app.loadCSB('GameUpdateLayer.csb')
    self.main_node = main_node
    self:addChild(main_node)

    self.m_panel_loading = main_node:child('panel_loading')
    main_node:child('sp_icon'):texture('common/icon_kind_' .. update_config._Kind .. '.png')

    main_node:child('sp_icon'):runAction( cc.RepeatForever:create( cc.Sequence:create(
        cc.ScaleTo:create(1, 0.95),
        cc.ScaleTo:create(1, 1)
    ) ) )
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function GameUpdateLayer:onEnter()
    print('GameUpdateLayer:onEnter...')
    self:goUpdate()
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function GameUpdateLayer:onExit()
    print('GameUpdateLayer:onExit...')
end


-------------------------------------------------------------------------------
-- 启动下载
-------------------------------------------------------------------------------
function GameUpdateLayer:goUpdate()
    self:updatePercent(0)
    local config = self.m_update_config
    ClientUpdate:create(config.newfileurl, config.dst, config.src, config.downurl):doUpdate(self)
    -- for test
    --[[
    local percent = 0
    self:perform(function()
        percent = percent + 1
        self:updateProgress(1, '', percent)
        if percent == 100 then
            self:updateResult(true, '')
        end
    end, 0.1, 100)
    --]]
end


-------------------------------------------------------------------------------
-- 下载进度（ClientUpdate回调）
-------------------------------------------------------------------------------
function GameUpdateLayer:updateProgress(sub, msg, mainpersent)
    if tolua.isnull(self) then return end
    self:updatePercent(mainpersent)
end


-------------------------------------------------------------------------------
-- 下载结果（ClientUpdate回调）
-------------------------------------------------------------------------------
function GameUpdateLayer:updateResult(result, msg)
    if tolua.isnull(self) then return end

    if result == true then
        local config = self.m_update_config
        appdf.app:getVersionManager():setResVersion(config._ServerResVersion, config._Module)
        for k, v in pairs(appdf.app._gameList) do
            if v._Module == config._Module then
                v._Active = true
            end
        end

        self:m_finish_callback(config._Module)
        self:removeFromParent()
    else
        -- 重试询问
        helper.pop.alert(LANG{'START_RETRY_MSG', msg=msg}, function()
            self:goUpdate()
        end, function()
            self:removeFromParent()
        end)
    end
end


-------------------------------------------------------------------------------
-- 更新进度条
-------------------------------------------------------------------------------
function GameUpdateLayer:updatePercent(percent)
    local bar = self.m_panel_loading:child('loading')
    bar:setPercent(percent)
    local size = bar:getVirtualRendererSize()
    local bar_x = bar:getPositionX()
    self.m_panel_loading:child('label_percent'):setString( string.format('%d%%', percent) )
end


return GameUpdateLayer
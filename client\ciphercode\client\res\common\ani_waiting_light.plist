<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>ani_waiting_light_1.png</key>
            <dict>
                <key>frame</key>
                <string>{{328,328},{160,160}}</string>
                <key>offset</key>
                <string>{-1,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,4},{160,160}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_2.png</key>
            <dict>
                <key>frame</key>
                <string>{{332,2},{164,162}}</string>
                <key>offset</key>
                <string>{-2,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,4},{164,162}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_3.png</key>
            <dict>
                <key>frame</key>
                <string>{{328,166},{160,160}}</string>
                <key>offset</key>
                <string>{-1,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,5},{160,160}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_4.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,168},{162,164}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{4,3},{162,164}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_5.png</key>
            <dict>
                <key>frame</key>
                <string>{{166,328},{160,160}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,5},{160,160}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_6.png</key>
            <dict>
                <key>frame</key>
                <string>{{166,2},{164,162}}</string>
                <key>offset</key>
                <string>{0,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{3,2},{164,162}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_7.png</key>
            <dict>
                <key>frame</key>
                <string>{{166,166},{160,160}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,4},{160,160}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
            <key>ani_waiting_light_8.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{162,164}}</string>
                <key>offset</key>
                <string>{-2,2}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{2,1},{162,164}}</string>
                <key>sourceSize</key>
                <string>{170,170}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>ani_waiting_light.png</string>
            <key>size</key>
            <string>{498,490}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:a5757e87ab6e523fb6b3bb3893b0d542:2ebb1b053672e92f6aa14bcfc0d770a2:a0401fd2bfde77de698ef1a6f6a49a88$</string>
            <key>textureFileName</key>
            <string>ani_waiting_light.png</string>
        </dict>
    </dict>
</plist>

-------------------------------------------------------------------------------
--  创世版1.0
--  大厅主界面 之 金币场大厅扩展
--  @date 2018-03-02
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd_common = cs.app.client('header.CMD_Common')


local MainScene = cs.app.client('main.MainScene')


-------------------------------------------------------------------------------
-- 初始化金币场大厅
-------------------------------------------------------------------------------
function MainScene:InitGoldUI()
    LiveFrame:getInstance():addListen(cmd_common.MDM_SOCKET_SERVICE, cmd_common.SUB_SERVER_ONLINE, self, self.onServerResp)

    local foot_node = self.main_node:child('foot_node')

    helper.app.addGoldLabelEvent(foot_node:child('label_gold'))

    local btn_quick_start = foot_node:child('btn_quick_start')
    if foot_node:child('btn_quick_start') then
        foot_node:child('btn_quick_start'):hide()   -- 要等到服务器信息返回才显示
        foot_node:child('btn_quick_start'):addTouchEventListener( handler(self, self.onBtnQuickStart) )
    end
end


-------------------------------------------------------------------------------
-- Socket重连hook
-------------------------------------------------------------------------------
function MainScene:onSetSockGold()
    self:refreshQuickStart()
end


-------------------------------------------------------------------------------
-- 刷新快速开始
--  获取合适的配置，请求服务器
-------------------------------------------------------------------------------
function MainScene:refreshQuickStart()
    if not cs.app.IS_GOLD_HALL then return end
    -- 找到合适的配置
    local quick_kind = cc.UserDefault:getInstance():getIntegerForKey("goldquickstartkind", 0)
    local kind_config = nil
    for i, v in ipairs(cs.app.plugins) do repeat
        if yl.is_reviewing and v.review then break end
        if v.kind and not kind_config then kind_config = v end
        if quick_kind > 0 and v.kind == quick_kind then kind_config = v end
    until true end
    if kind_config then
        self.m_quick_start_kind = kind_config
        self.main_node:child('foot_node/btn_quick_start/label_kind'):setString( kind_config.name )
        self:requestServerInfo(kind_config.kind, handler(self, self.setQuickStart))
    end
end


-------------------------------------------------------------------------------
-- 设置快速开始按钮
-------------------------------------------------------------------------------
function MainScene:setQuickStart(servers)
    for i, v in ipairs(servers) do
        if v.bIsDefault then
            for _, vv in ipairs(GlobalUserItem.roomlist) do
                local kind = vv[1]
                local rooms = vv[2]
                for k, vvv in ipairs(rooms) do
                    if v.wServerID == vvv.wServerID then
                        self.m_quick_start_server = vvv.wServerID
                        self.main_node:child('foot_node/btn_quick_start'):show()
                    end
                end
            end
        end
    end
end


-------------------------------------------------------------------------------
-- 更新快速开始玩法(金币场玩法界面调用)
-------------------------------------------------------------------------------
function MainScene:updateQuickKind(kind)
    cc.UserDefault:getInstance():setIntegerForKey("goldquickstartkind", kind)
end


-------------------------------------------------------------------------------
-- 获取服务器状态
-------------------------------------------------------------------------------
function MainScene:requestServerInfo(kind, callback)
    self.m_server_resp_callback = callback
	local cmd_data = ExternalFun.create_netdata( cmd_common.CMD_GR_ID, {dwID = kind} )
	cmd_data:setcmdinfo(cmd_common.MDM_SOCKET_SERVICE, cmd_common.SUB_SERVER_ONLINE)
    LiveFrame:getInstance():send(cmd_data)
end


-------------------------------------------------------------------------------
-- 服务器状态返回
-------------------------------------------------------------------------------
function MainScene:onServerResp(data)
    if not self.m_server_resp_callback then return end
    local ret = LiveFrame:getInstance():resp(data, cmd_common.ServerIDOnline, true)
    if not ret then return false end

    --dump(ret, 'servers state', 6)
    self.m_server_resp_callback(ret)
    self.m_server_resp_callback = nil
end


-------------------------------------------------------------------------------
-- 快速开始按钮点击
-------------------------------------------------------------------------------
function MainScene:onBtnQuickStart(sender, event)
    helper.app.commClickEffect(sender, event)
    if event ~= cc.EventCode.ENDED then return end
    if not self.m_quick_start_server then return end
    
    -- 开始创建后禁用创建按钮一段时间
    sender:setTouchEnabled(false)
    sender:perform(function()
        sender:setTouchEnabled(true)
    end, 3)

    local server = self.m_quick_start_server
    helper.app.checkGameUpdate(self.m_quick_start_kind.game, self.m_quick_start_kind.kind, function()
        PassRoom:getInstance():onLoginServer(server)
    end)
end


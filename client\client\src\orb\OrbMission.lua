-------------------------------------------------------------------------------
--  创世版1.0
--  拆红包 - 任务（途径）
--  @date 2019-01-24
--  @auth woodoo
-------------------------------------------------------------------------------
local OrbUtil = cs.app.client('orb.OrbUtil')
local OrbBase = cs.app.client('orb.OrbBase')


local OrbMission = class('OrbMission', OrbBase)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function OrbMission:ctor(panel)
    print('OrbMission:ctor...')
    self.super.ctor(self, panel)

    self.m_panel:child('mission_2/btn_share'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnShare) )
    self.m_panel:child('mission_3/btn_share'):addTouchEventListener( helper.app.tintClickHandler(self, self.onBtnShare) )
end


-------------------------------------------------------------------------------
-- 显示事件
-------------------------------------------------------------------------------
function OrbMission:onShow()
    print('OrbMission:onShow')
    local amount = self:getAmount()
    self.m_panel:child('label_amount'):setString(string.format('%.2f', amount))
end


return OrbMission
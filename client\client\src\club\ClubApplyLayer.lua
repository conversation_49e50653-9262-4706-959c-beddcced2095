-------------------------------------------------------------------------------
--  创世版3.0
--  申请列表
--  @date 2018-01-17
--  @auth woodoo
-------------------------------------------------------------------------------
local LiveFrame = cs.app.client('frame.LiveFrame')
local ExternalFun = cs.app.client('external.ExternalFun')
local cmd = cs.app.client('header.CMD_Common')
local ClubUtil = cs.app.client('club.ClubUtil')
local ClubPager = cs.app.client('club.ClubPager')


local ClubApplyLayer = class("ClubApplyLayer", cc.Layer)


-------------------------------------------------------------------------------
-- 构造方法
-------------------------------------------------------------------------------
function ClubApplyLayer:ctor(club)
    print('ClubApplyLayer:ctor...')
    self.m_club = club
    local main_node = ClubUtil.initUI(self, 'ClubApplyLayer.csb')
    main_node:child('row_template'):hide()
    self.m_pager = ClubPager.new(main_node:child('pager'), self.m_club.dwClubID, cmd.SUB_CLUB_APPLY_MEMBERS)
end


-------------------------------------------------------------------------------
-- onEnter
-------------------------------------------------------------------------------
function ClubApplyLayer:onEnter()
    print('ClubApplyLayer:onEnter...')
    ClubUtil.listen(cmd.SUB_CLUB_APPLY_MEMBERS, self, self.onApplyListResp)
    ClubUtil.listen(cmd.SUB_CLUB_APPLY_APPROVE, self, self.onApplyApproveResp)

    self.m_pager:stepPage(1)
end


-------------------------------------------------------------------------------
-- onExit
-------------------------------------------------------------------------------
function ClubApplyLayer:onExit()
    print('ClubApplyLayer:onExit...')
    LiveFrame:getInstance():removeListenByObj(self)
end


-------------------------------------------------------------------------------
-- 申请列表生成
-------------------------------------------------------------------------------
function ClubApplyLayer:onApplyListResp(data)
    local applies = LiveFrame:getInstance():resp(data, cmd.tagClubApplyMember, true)
    if not applies then return end

    self.m_applies = applies

    -- 更新翻页
    if #applies > 0 then
        self.m_pager:updateParams(applies[1].wTotal, applies[1].wIndex, applies[#applies].wIndex)
    else
        self.m_pager:updateParams(0, 0, 0)
    end

    local listview = self.main_node:child('listview')
    listview:removeAllItems()
    local template = self.main_node:child('row_template')
    helper.layout.pushEmpty(listview, {10, 10})
    for i, apply in ipairs(applies) do
        local item = template:clone():show()
        item.apply = apply

        ClubUtil.createPlayerHead(item:child('panel_head'), apply.dwUserID, apply.szHeadHttp)

        item:child('label_name'):setString(apply.szName)
        item:child('label_info'):setString(apply.szMsg)
        item:child('btn_refuse'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnRefuse) )
        item:child('btn_agree'):addTouchEventListener( helper.app.commClickHandler(self, self.onBtnAgree) )
        listview:pushBackCustomItem(item)
    end
    helper.layout.pushEmpty(listview, {10, 10})
end


-------------------------------------------------------------------------------
-- 发送拒绝或同意
-------------------------------------------------------------------------------
function ClubApplyLayer:sendApprove(user_id, value)
    local values = {dwID=self.m_club.dwClubID, nValue=user_id, nValue2=value}
    ClubUtil.send(cmd.SUB_CLUB_APPLY_APPROVE, cmd.CMD_GR_IDValue, values)
end


-------------------------------------------------------------------------------
-- 拒绝按钮点击
-------------------------------------------------------------------------------
function ClubApplyLayer:onBtnRefuse(sender)
    local apply = sender:getParent().apply
    helper.pop.alert( LANG.CLUB_APPLY_REFUSE, function()
        self:sendApprove(apply.dwUserID, 0)
    end, true)
end


-------------------------------------------------------------------------------
-- 同意按钮点击
-------------------------------------------------------------------------------
function ClubApplyLayer:onBtnAgree(sender)
    local apply = sender:getParent().apply
    helper.pop.alert( LANG.CLUB_APPLY_AGREE, function()
        self:sendApprove(apply.dwUserID, 1)
    end, true)
end


-------------------------------------------------------------------------------
-- 申请处理返回
-------------------------------------------------------------------------------
function ClubApplyLayer:onApplyApproveResp(data)
    ClubUtil.commonResp(data, cmd.CMD_GR_IDMsg, function(ret)
        helper.pop.message( LANG.CLUB_APPLY_A_SUCC )
        if tolua.isnull(self) then return end
        -- 重新请求该页
        self.m_pager:stepPage(#self.m_applies == 1 and -1 or 0)

        if not self.m_club.is_join or self.m_club.cbIsManager == 1 then
            ClubUtil.sendApplyCountCmd(self.m_club.dwClubID)
        end
    end)
end

return ClubApplyLayer

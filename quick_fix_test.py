#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import hashlib

def quick_test_sms():
    """快速测试短信接口是否恢复"""
    print("=== 快速测试短信接口 ===")
    
    # 简单的连通性测试
    test_urls = [
        'http://lhmj.tuo3.com.cn/',
        'http://lhmj.tuo3.com.cn/admin/',
    ]
    
    print("1. 测试基本连通性...")
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} - 连接正常")
            elif response.status_code == 502:
                print(f"❌ {url} - 仍然是 502 错误")
                return False
            else:
                print(f"⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 连接失败: {e}")
            return False
    
    print("\n2. 测试短信验证接口...")
    
    # 准备测试参数
    params = {
        'uid': '123456',
        'phone': '13800138000',
        'type': 'login',
        'uuid': 'TEST_DEVICE_ID',
        'timestamp': str(int(time.time() * 1000)),
        'channel': '50010001',
        'c_version': '10',
        'res_version': '1',
    }
    
    # 生成签名
    channel_key = '8ed42f39c27b572cf2a73a5f620f63ed'
    sorted_keys = sorted(params.keys())
    param_str = ''.join(str(params[key]) for key in sorted_keys)
    sign_str = param_str + channel_key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()
    params['sign'] = sign
    
    # 测试短信接口
    url = 'http://lhmj.tuo3.com.cn/admin/api/v1/user/get_verify_code'
    
    try:
        response = requests.post(url, data=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"响应: {response.text}")
            try:
                json_data = response.json()
                if json_data.get('code') == 0:
                    print("🎉 短信接口恢复正常！")
                    return True
                else:
                    print(f"⚠️ 接口错误: {json_data.get('msg')}")
                    print("💡 可能是短信服务商配置问题")
                    return True  # 至少服务器响应正常了
            except:
                print("⚠️ 非JSON响应，但服务器已响应")
                return True
        elif response.status_code == 502:
            print("❌ 仍然是 502 错误，Apache 还没修复好")
            return False
        else:
            print(f"⚠️ 其他状态码: {response.status_code}")
            return True  # 至少不是 502 了
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

if __name__ == '__main__':
    print("快速测试 Apache 修复结果...")
    print("请确保已经：")
    print("1. 停止了 FileZilla（减少干扰）")
    print("2. 解决了端口 443 冲突")
    print("3. 重启了 Apache")
    print()
    
    success = quick_test_sms()
    
    if success:
        print("\n✅ 修复成功！可以继续使用短信验证功能")
        print("💡 FileZilla 问题可以稍后处理")
    else:
        print("\n❌ 还需要继续修复 Apache")
        print("💡 建议检查：")
        print("   - 端口 443 是否还被占用")
        print("   - Apache 错误日志")
        print("   - XAMPP 控制面板中 Apache 状态")
